{"ast": null, "code": "import { CallType } from '../../models/message.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/call.service\";\nimport * as i2 from \"../../services/logger.service\";\nfunction IncomingCallComponent_div_0_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"div\", 30);\n    i0.ɵɵelement(2, \"i\", 31);\n    i0.ɵɵelementStart(3, \"span\", 32);\n    i0.ɵɵtext(4, \"Appel vid\\u00E9o avec cam\\u00E9ra\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction IncomingCallComponent_div_0_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33)(1, \"div\", 34);\n    i0.ɵɵelement(2, \"div\", 35);\n    i0.ɵɵelementStart(3, \"span\", 36);\n    i0.ɵɵtext(4, \"Connexion en cours...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction IncomingCallComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2)(2, \"div\", 3)(3, \"div\", 4);\n    i0.ɵɵelement(4, \"i\", 5);\n    i0.ɵɵelementStart(5, \"span\", 6);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(7, \"div\", 7);\n    i0.ɵɵelement(8, \"div\", 8)(9, \"div\", 9)(10, \"div\", 10);\n    i0.ɵɵelementStart(11, \"div\", 11);\n    i0.ɵɵelement(12, \"img\", 12);\n    i0.ɵɵelementStart(13, \"div\", 13);\n    i0.ɵɵelement(14, \"i\", 14);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(15, \"div\", 15)(16, \"h2\", 16);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"p\", 17);\n    i0.ɵɵtext(19, \"vous appelle...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(20, IncomingCallComponent_div_0_div_20_Template, 5, 0, \"div\", 18);\n    i0.ɵɵelementStart(21, \"div\", 19)(22, \"button\", 20);\n    i0.ɵɵlistener(\"click\", function IncomingCallComponent_div_0_Template_button_click_22_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.rejectCall());\n    });\n    i0.ɵɵelement(23, \"i\", 21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"button\", 22);\n    i0.ɵɵlistener(\"click\", function IncomingCallComponent_div_0_Template_button_click_24_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.acceptCall());\n    });\n    i0.ɵɵelement(25, \"i\", 23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(26, IncomingCallComponent_div_0_div_26_Template, 5, 0, \"div\", 24);\n    i0.ɵɵelementStart(27, \"div\", 25)(28, \"button\", 26);\n    i0.ɵɵelement(29, \"i\", 27);\n    i0.ɵɵtext(30, \" Message \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"button\", 28);\n    i0.ɵɵelement(32, \"i\", 29);\n    i0.ɵɵtext(33, \" Plus tard \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassMap(ctx_r0.getCallTypeIcon());\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r0.getCallTypeText(), \" entrant\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"src\", ctx_r0.getCallerAvatar(), i0.ɵɵsanitizeUrl)(\"alt\", ctx_r0.getCallerName());\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(ctx_r0.getCallTypeIcon());\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.getCallerName());\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isVideoCall());\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r0.isProcessing);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"animate-spin\", ctx_r0.isProcessing);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", ctx_r0.isProcessing);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"animate-spin\", ctx_r0.isProcessing);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isProcessing);\n  }\n}\nexport let IncomingCallComponent = /*#__PURE__*/(() => {\n  class IncomingCallComponent {\n    constructor(callService, logger) {\n      this.callService = callService;\n      this.logger = logger;\n      this.incomingCall = null;\n      this.isProcessing = false;\n      this.subscriptions = [];\n      // Exposer les énums au template\n      this.CallType = CallType;\n    }\n    ngOnInit() {\n      // S'abonner aux appels entrants\n      const incomingCallSub = this.callService.incomingCall$.subscribe(call => {\n        this.incomingCall = call;\n        this.isProcessing = false;\n        if (call) {\n          console.log('📞 [IncomingCall] Incoming call received:', {\n            callId: call.id,\n            caller: call.caller?.username,\n            type: call.type\n          });\n        }\n      });\n      this.subscriptions.push(incomingCallSub);\n    }\n    ngOnDestroy() {\n      this.subscriptions.forEach(sub => sub.unsubscribe());\n    }\n    /**\n     * Accepte l'appel entrant\n     */\n    acceptCall() {\n      if (!this.incomingCall || this.isProcessing) return;\n      console.log('✅ [IncomingCall] Accepting call:', this.incomingCall.id);\n      this.isProcessing = true;\n      this.callService.acceptCall(this.incomingCall).subscribe({\n        next: call => {\n          console.log('✅ [IncomingCall] Call accepted successfully:', call);\n          this.isProcessing = false;\n          // L'interface d'appel actif va automatiquement s'afficher\n        },\n\n        error: error => {\n          console.error('❌ [IncomingCall] Error accepting call:', error);\n          this.logger.error('Error accepting call:', error);\n          this.isProcessing = false;\n          // Optionnel : Afficher un message d'erreur à l'utilisateur\n          alert(\"Erreur lors de l'acceptation de l'appel. Veuillez réessayer.\");\n        }\n      });\n    }\n    /**\n     * Rejette l'appel entrant\n     */\n    rejectCall() {\n      if (!this.incomingCall || this.isProcessing) return;\n      console.log('❌ [IncomingCall] Rejecting call:', this.incomingCall.id);\n      this.isProcessing = true;\n      this.callService.rejectCall(this.incomingCall.id, 'User rejected').subscribe({\n        next: result => {\n          console.log('✅ [IncomingCall] Call rejected successfully:', result);\n          this.isProcessing = false;\n        },\n        error: error => {\n          console.error('❌ [IncomingCall] Error rejecting call:', error);\n          this.logger.error('Error rejecting call:', error);\n          this.isProcessing = false;\n        }\n      });\n    }\n    /**\n     * Obtient le nom de l'appelant\n     */\n    getCallerName() {\n      return this.incomingCall?.caller?.username || 'Utilisateur inconnu';\n    }\n    /**\n     * Obtient l'avatar de l'appelant\n     */\n    getCallerAvatar() {\n      return this.incomingCall?.caller?.image || '/assets/images/default-avatar.png';\n    }\n    /**\n     * Obtient le type d'appel (audio/vidéo)\n     */\n    getCallTypeText() {\n      if (!this.incomingCall) return '';\n      return this.incomingCall.type === CallType.VIDEO ? 'Appel vidéo' : 'Appel audio';\n    }\n    /**\n     * Obtient l'icône du type d'appel\n     */\n    getCallTypeIcon() {\n      if (!this.incomingCall) return 'fa-phone';\n      return this.incomingCall.type === CallType.VIDEO ? 'fa-video' : 'fa-phone';\n    }\n    /**\n     * Vérifie si c'est un appel vidéo\n     */\n    isVideoCall() {\n      return this.incomingCall?.type === CallType.VIDEO;\n    }\n    static {\n      this.ɵfac = function IncomingCallComponent_Factory(t) {\n        return new (t || IncomingCallComponent)(i0.ɵɵdirectiveInject(i1.CallService), i0.ɵɵdirectiveInject(i2.LoggerService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: IncomingCallComponent,\n        selectors: [[\"app-incoming-call\"]],\n        decls: 1,\n        vars: 1,\n        consts: [[\"class\", \"fixed inset-0 z-50 bg-black/80 backdrop-blur-sm flex items-center justify-center p-4\", 4, \"ngIf\"], [1, \"fixed\", \"inset-0\", \"z-50\", \"bg-black/80\", \"backdrop-blur-sm\", \"flex\", \"items-center\", \"justify-center\", \"p-4\"], [1, \"bg-gradient-to-br\", \"from-gray-900\", \"via-blue-900\", \"to-purple-900\", \"rounded-3xl\", \"p-8\", \"max-w-md\", \"w-full\", \"mx-auto\", \"shadow-2xl\", \"border\", \"border-blue-500/30\", \"animate-pulse-slow\"], [1, \"text-center\", \"mb-6\"], [1, \"inline-flex\", \"items-center\", \"space-x-2\", \"px-4\", \"py-2\", \"rounded-full\", \"bg-blue-500/20\", \"border\", \"border-blue-400/30\"], [1, \"fas\", \"text-blue-400\"], [1, \"text-blue-300\", \"font-medium\"], [1, \"relative\", \"mb-6\", \"flex\", \"justify-center\"], [1, \"absolute\", \"inset-0\", \"rounded-full\", \"border-4\", \"border-blue-400/30\", \"animate-ping\"], [1, \"absolute\", \"-inset-4\", \"rounded-full\", \"border-2\", \"border-blue-400/20\", \"animate-ping\", 2, \"animation-delay\", \"0.5s\"], [1, \"absolute\", \"-inset-8\", \"rounded-full\", \"border\", \"border-blue-400/10\", \"animate-ping\", 2, \"animation-delay\", \"1s\"], [1, \"relative\", \"w-32\", \"h-32\", \"rounded-full\", \"overflow-hidden\", \"border-4\", \"border-blue-500/50\", \"shadow-2xl\"], [1, \"w-full\", \"h-full\", \"object-cover\", 3, \"src\", \"alt\"], [1, \"absolute\", \"bottom-0\", \"right-0\", \"w-10\", \"h-10\", \"bg-blue-500\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\", \"border-2\", \"border-white\"], [1, \"fas\", \"text-white\", \"text-sm\"], [1, \"text-center\", \"mb-8\"], [1, \"text-2xl\", \"font-bold\", \"text-white\", \"mb-2\", \"drop-shadow-lg\"], [1, \"text-blue-300\", \"text-lg\"], [\"class\", \"text-center mb-6\", 4, \"ngIf\"], [1, \"flex\", \"justify-center\", \"space-x-8\"], [\"title\", \"Rejeter l'appel\", 1, \"w-16\", \"h-16\", \"rounded-full\", \"bg-red-500\", \"hover:bg-red-600\", \"disabled:bg-red-400\", \"flex\", \"items-center\", \"justify-center\", \"transition-all\", \"duration-300\", \"transform\", \"hover:scale-110\", \"active:scale-95\", \"shadow-lg\", \"shadow-red-500/50\", 3, \"disabled\", \"click\"], [1, \"fas\", \"fa-phone-slash\", \"text-white\", \"text-xl\"], [\"title\", \"Accepter l'appel\", 1, \"w-16\", \"h-16\", \"rounded-full\", \"bg-green-500\", \"hover:bg-green-600\", \"disabled:bg-green-400\", \"flex\", \"items-center\", \"justify-center\", \"transition-all\", \"duration-300\", \"transform\", \"hover:scale-110\", \"active:scale-95\", \"shadow-lg\", \"shadow-green-500/50\", \"animate-pulse\", 3, \"disabled\", \"click\"], [1, \"fas\", \"fa-phone\", \"text-white\", \"text-xl\"], [\"class\", \"text-center mt-6\", 4, \"ngIf\"], [1, \"flex\", \"justify-center\", \"space-x-4\", \"mt-6\"], [\"title\", \"Envoyer un message\", 1, \"px-4\", \"py-2\", \"rounded-full\", \"bg-gray-700/50\", \"hover:bg-gray-600/50\", \"text-gray-300\", \"hover:text-white\", \"text-sm\", \"transition-all\", \"duration-200\"], [1, \"fas\", \"fa-comment\", \"mr-2\"], [\"title\", \"Rappeler plus tard\", 1, \"px-4\", \"py-2\", \"rounded-full\", \"bg-gray-700/50\", \"hover:bg-gray-600/50\", \"text-gray-300\", \"hover:text-white\", \"text-sm\", \"transition-all\", \"duration-200\"], [1, \"fas\", \"fa-clock\", \"mr-2\"], [1, \"inline-flex\", \"items-center\", \"space-x-2\", \"px-4\", \"py-2\", \"rounded-full\", \"bg-purple-500/20\", \"border\", \"border-purple-400/30\"], [1, \"fas\", \"fa-video\", \"text-purple-400\"], [1, \"text-purple-300\", \"text-sm\"], [1, \"text-center\", \"mt-6\"], [1, \"inline-flex\", \"items-center\", \"space-x-2\", \"text-blue-300\"], [1, \"w-4\", \"h-4\", \"border-2\", \"border-blue-400\", \"border-t-transparent\", \"rounded-full\", \"animate-spin\"], [1, \"text-sm\"]],\n        template: function IncomingCallComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵtemplate(0, IncomingCallComponent_div_0_Template, 34, 16, \"div\", 0);\n          }\n          if (rf & 2) {\n            i0.ɵɵproperty(\"ngIf\", ctx.incomingCall);\n          }\n        },\n        styles: [\"@keyframes _ngcontent-%COMP%_pulse-slow{0%,to{transform:scale(1);opacity:1}50%{transform:scale(1.02);opacity:.9}}.animate-pulse-slow[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_pulse-slow 3s ease-in-out infinite}@keyframes _ngcontent-%COMP%_pulse-ring{0%{transform:scale(1);opacity:1}to{transform:scale(1.8);opacity:0}}.glow-green[_ngcontent-%COMP%]{box-shadow:0 0 20px #22c55e80}.glow-green[_ngcontent-%COMP%]:hover{box-shadow:0 0 30px #22c55eb3}.glow-red[_ngcontent-%COMP%]{box-shadow:0 0 20px #ef444480}.glow-red[_ngcontent-%COMP%]:hover{box-shadow:0 0 30px #ef4444b3}@keyframes _ngcontent-%COMP%_vibrate{0%,to{transform:translate(0)}25%{transform:translate(-2px)}75%{transform:translate(2px)}}.animate-vibrate[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_vibrate .5s ease-in-out infinite}.backdrop-blur-custom[_ngcontent-%COMP%]{backdrop-filter:blur(8px);-webkit-backdrop-filter:blur(8px)}button[_ngcontent-%COMP%]{transition:all .3s cubic-bezier(.4,0,.2,1)}button[_ngcontent-%COMP%]:active{transform:scale(.95)}button[_ngcontent-%COMP%]:disabled{cursor:not-allowed;opacity:.6}button[_ngcontent-%COMP%]:hover:not(:disabled){filter:brightness(1.1)}@keyframes _ngcontent-%COMP%_spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}.animate-spin[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_spin 1s linear infinite}@keyframes _ngcontent-%COMP%_pulse-accept{0%,to{box-shadow:0 0 20px #22c55e80}50%{box-shadow:0 0 40px #22c55ecc}}.pulse-accept[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_pulse-accept 2s ease-in-out infinite}.fas[_ngcontent-%COMP%]{filter:drop-shadow(0 2px 4px rgba(0,0,0,.3))}@keyframes _ngcontent-%COMP%_slide-in{0%{transform:translateY(100px);opacity:0}to{transform:translateY(0);opacity:1}}.slide-in[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_slide-in .5s ease-out}@keyframes _ngcontent-%COMP%_gradient-border{0%{border-color:#3b82f64d}50%{border-color:#9333ea80}to{border-color:#3b82f64d}}.animated-border[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_gradient-border 3s ease-in-out infinite}@media (max-width: 640px){.w-32[_ngcontent-%COMP%]{width:6rem}.h-32[_ngcontent-%COMP%]{height:6rem}.text-2xl[_ngcontent-%COMP%]{font-size:1.5rem}.w-16[_ngcontent-%COMP%]{width:3.5rem}.h-16[_ngcontent-%COMP%]{height:3.5rem}.space-x-8[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] + *[_ngcontent-%COMP%]{margin-left:1.5rem}}.backdrop-overlay[_ngcontent-%COMP%]{background:rgba(0,0,0,.8);backdrop-filter:blur(10px);-webkit-backdrop-filter:blur(10px)}@keyframes _ngcontent-%COMP%_heartbeat{0%,to{transform:scale(1)}50%{transform:scale(1.05)}}.animate-heartbeat[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_heartbeat 1.5s ease-in-out infinite}\", \"@keyframes _ngcontent-%COMP%_pulse-slow {\\n  0%, 100% {\\n    transform: scale(1);\\n    opacity: 1;\\n  }\\n  50% {\\n    transform: scale(1.02);\\n    opacity: 0.9;\\n  }\\n}\\n\\n.animate-pulse-slow[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_pulse-slow 3s ease-in-out infinite;\\n}\"]\n      });\n    }\n  }\n  return IncomingCallComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}