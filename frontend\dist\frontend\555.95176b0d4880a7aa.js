"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[555],{555:(Tr,et,g)=>{g.r(et),g.d(et,{ProjectsModule:()=>Ir});var p=g(177),c=g(4341),b=g(6647),E=g(5312),t=g(7705),R=g(1873),S=g(6535),f=g(4085),y=g(6860),I=g(5964),N=g(6697),it=g(6977),F=g(8203);class H{attach(r){return this._attachedHost=r,r.attach(this)}detach(){let r=this._attachedHost;null!=r&&(this._attachedHost=null,r.detach())}get isAttached(){return null!=this._attachedHost}setAttachedHost(r){this._attachedHost=r}}class X extends H{constructor(r,e,i,o,a){super(),this.component=r,this.viewContainerRef=e,this.injector=i,this.componentFactoryResolver=o,this.projectableNodes=a}}class rt extends H{constructor(r,e,i,o){super(),this.templateRef=r,this.viewContainerRef=e,this.context=i,this.injector=o}get origin(){return this.templateRef.elementRef}attach(r,e=this.context){return this.context=e,super.attach(r)}detach(){return this.context=void 0,super.detach()}}class Nt extends H{constructor(r){super(),this.element=r instanceof t.aKT?r.nativeElement:r}}class U{constructor(){this._isDisposed=!1,this.attachDomPortal=null}hasAttached(){return!!this._attachedPortal}attach(r){return r instanceof X?(this._attachedPortal=r,this.attachComponentPortal(r)):r instanceof rt?(this._attachedPortal=r,this.attachTemplatePortal(r)):this.attachDomPortal&&r instanceof Nt?(this._attachedPortal=r,this.attachDomPortal(r)):void 0}detach(){this._attachedPortal&&(this._attachedPortal.setAttachedHost(null),this._attachedPortal=null),this._invokeDisposeFn()}dispose(){this.hasAttached()&&this.detach(),this._invokeDisposeFn(),this._isDisposed=!0}setDisposeFn(r){this._disposeFn=r}_invokeDisposeFn(){this._disposeFn&&(this._disposeFn(),this._disposeFn=null)}}class Vt extends U{constructor(r,e,i,o,a){super(),this.outletElement=r,this._componentFactoryResolver=e,this._appRef=i,this._defaultInjector=o,this.attachDomPortal=s=>{const d=s.element,l=this._document.createComment("dom-portal");d.parentNode.insertBefore(l,d),this.outletElement.appendChild(d),this._attachedPortal=s,super.setDisposeFn(()=>{l.parentNode&&l.parentNode.replaceChild(d,l)})},this._document=a}attachComponentPortal(r){const i=(r.componentFactoryResolver||this._componentFactoryResolver).resolveComponentFactory(r.component);let o;return r.viewContainerRef?(o=r.viewContainerRef.createComponent(i,r.viewContainerRef.length,r.injector||r.viewContainerRef.injector,r.projectableNodes||void 0),this.setDisposeFn(()=>o.destroy())):(o=i.create(r.injector||this._defaultInjector||t.zZn.NULL),this._appRef.attachView(o.hostView),this.setDisposeFn(()=>{this._appRef.viewCount>0&&this._appRef.detachView(o.hostView),o.destroy()})),this.outletElement.appendChild(this._getComponentRootNode(o)),this._attachedPortal=r,o}attachTemplatePortal(r){let e=r.viewContainerRef,i=e.createEmbeddedView(r.templateRef,r.context,{injector:r.injector});return i.rootNodes.forEach(o=>this.outletElement.appendChild(o)),i.detectChanges(),this.setDisposeFn(()=>{let o=e.indexOf(i);-1!==o&&e.remove(o)}),this._attachedPortal=r,i}dispose(){super.dispose(),this.outletElement.remove()}_getComponentRootNode(r){return r.hostView.rootNodes[0]}}let Z=(()=>{class n extends U{constructor(e,i,o){super(),this._componentFactoryResolver=e,this._viewContainerRef=i,this._isInitialized=!1,this.attached=new t.bkB,this.attachDomPortal=a=>{const s=a.element,d=this._document.createComment("dom-portal");a.setAttachedHost(this),s.parentNode.insertBefore(d,s),this._getRootNode().appendChild(s),this._attachedPortal=a,super.setDisposeFn(()=>{d.parentNode&&d.parentNode.replaceChild(s,d)})},this._document=o}get portal(){return this._attachedPortal}set portal(e){this.hasAttached()&&!e&&!this._isInitialized||(this.hasAttached()&&super.detach(),e&&super.attach(e),this._attachedPortal=e||null)}get attachedRef(){return this._attachedRef}ngOnInit(){this._isInitialized=!0}ngOnDestroy(){super.dispose(),this._attachedRef=this._attachedPortal=null}attachComponentPortal(e){e.setAttachedHost(this);const i=null!=e.viewContainerRef?e.viewContainerRef:this._viewContainerRef,a=(e.componentFactoryResolver||this._componentFactoryResolver).resolveComponentFactory(e.component),s=i.createComponent(a,i.length,e.injector||i.injector,e.projectableNodes||void 0);return i!==this._viewContainerRef&&this._getRootNode().appendChild(s.hostView.rootNodes[0]),super.setDisposeFn(()=>s.destroy()),this._attachedPortal=e,this._attachedRef=s,this.attached.emit(s),s}attachTemplatePortal(e){e.setAttachedHost(this);const i=this._viewContainerRef.createEmbeddedView(e.templateRef,e.context,{injector:e.injector});return super.setDisposeFn(()=>this._viewContainerRef.clear()),this._attachedPortal=e,this._attachedRef=i,this.attached.emit(i),i}_getRootNode(){const e=this._viewContainerRef.element.nativeElement;return e.nodeType===e.ELEMENT_NODE?e:e.parentNode}static{this.\u0275fac=function(i){return new(i||n)(t.rXU(t.OM3),t.rXU(t.c1b),t.rXU(p.qQ))}}static{this.\u0275dir=t.FsC({type:n,selectors:[["","cdkPortalOutlet",""]],inputs:{portal:["cdkPortalOutlet","portal"]},outputs:{attached:"attached"},exportAs:["cdkPortalOutlet"],features:[t.Vt3]})}}return n})(),V=(()=>{class n{static{this.\u0275fac=function(i){return new(i||n)}}static{this.\u0275mod=t.$C({type:n})}static{this.\u0275inj=t.G2t({})}}return n})();var j=g(1413),ot=g(8359),nt=g(7786);const at=(0,y.CZ)();class zt{constructor(r,e){this._viewportRuler=r,this._previousHTMLStyles={top:"",left:""},this._isEnabled=!1,this._document=e}attach(){}enable(){if(this._canBeEnabled()){const r=this._document.documentElement;this._previousScrollPosition=this._viewportRuler.getViewportScrollPosition(),this._previousHTMLStyles.left=r.style.left||"",this._previousHTMLStyles.top=r.style.top||"",r.style.left=(0,f.a1)(-this._previousScrollPosition.left),r.style.top=(0,f.a1)(-this._previousScrollPosition.top),r.classList.add("cdk-global-scrollblock"),this._isEnabled=!0}}disable(){if(this._isEnabled){const r=this._document.documentElement,i=r.style,o=this._document.body.style,a=i.scrollBehavior||"",s=o.scrollBehavior||"";this._isEnabled=!1,i.left=this._previousHTMLStyles.left,i.top=this._previousHTMLStyles.top,r.classList.remove("cdk-global-scrollblock"),at&&(i.scrollBehavior=o.scrollBehavior="auto"),window.scroll(this._previousScrollPosition.left,this._previousScrollPosition.top),at&&(i.scrollBehavior=a,o.scrollBehavior=s)}}_canBeEnabled(){if(this._document.documentElement.classList.contains("cdk-global-scrollblock")||this._isEnabled)return!1;const e=this._document.body,i=this._viewportRuler.getViewportSize();return e.scrollHeight>i.height||e.scrollWidth>i.width}}class $t{constructor(r,e,i,o){this._scrollDispatcher=r,this._ngZone=e,this._viewportRuler=i,this._config=o,this._scrollSubscription=null,this._detach=()=>{this.disable(),this._overlayRef.hasAttached()&&this._ngZone.run(()=>this._overlayRef.detach())}}attach(r){this._overlayRef=r}enable(){if(this._scrollSubscription)return;const r=this._scrollDispatcher.scrolled(0).pipe((0,I.p)(e=>!e||!this._overlayRef.overlayElement.contains(e.getElementRef().nativeElement)));this._config&&this._config.threshold&&this._config.threshold>1?(this._initialScrollPosition=this._viewportRuler.getViewportScrollPosition().top,this._scrollSubscription=r.subscribe(()=>{const e=this._viewportRuler.getViewportScrollPosition().top;Math.abs(e-this._initialScrollPosition)>this._config.threshold?this._detach():this._overlayRef.updatePosition()})):this._scrollSubscription=r.subscribe(this._detach)}disable(){this._scrollSubscription&&(this._scrollSubscription.unsubscribe(),this._scrollSubscription=null)}detach(){this.disable(),this._overlayRef=null}}class st{enable(){}disable(){}attach(){}}function K(n,r){return r.some(e=>n.bottom<e.top||n.top>e.bottom||n.right<e.left||n.left>e.right)}function dt(n,r){return r.some(e=>n.top<e.top||n.bottom>e.bottom||n.left<e.left||n.right>e.right)}class Gt{constructor(r,e,i,o){this._scrollDispatcher=r,this._viewportRuler=e,this._ngZone=i,this._config=o,this._scrollSubscription=null}attach(r){this._overlayRef=r}enable(){this._scrollSubscription||(this._scrollSubscription=this._scrollDispatcher.scrolled(this._config?this._config.scrollThrottle:0).subscribe(()=>{if(this._overlayRef.updatePosition(),this._config&&this._config.autoClose){const e=this._overlayRef.overlayElement.getBoundingClientRect(),{width:i,height:o}=this._viewportRuler.getViewportSize();K(e,[{width:i,height:o,bottom:o,right:i,top:0,left:0}])&&(this.disable(),this._ngZone.run(()=>this._overlayRef.detach()))}}))}disable(){this._scrollSubscription&&(this._scrollSubscription.unsubscribe(),this._scrollSubscription=null)}detach(){this.disable(),this._overlayRef=null}}let Yt=(()=>{class n{constructor(e,i,o,a){this._scrollDispatcher=e,this._viewportRuler=i,this._ngZone=o,this.noop=()=>new st,this.close=s=>new $t(this._scrollDispatcher,this._ngZone,this._viewportRuler,s),this.block=()=>new zt(this._viewportRuler,this._document),this.reposition=s=>new Gt(this._scrollDispatcher,this._viewportRuler,this._ngZone,s),this._document=a}static{this.\u0275fac=function(i){return new(i||n)(t.KVO(S.R),t.KVO(S.Xj),t.KVO(t.SKi),t.KVO(p.qQ))}}static{this.\u0275prov=t.jDH({token:n,factory:n.\u0275fac,providedIn:"root"})}}return n})();class ct{constructor(r){if(this.scrollStrategy=new st,this.panelClass="",this.hasBackdrop=!1,this.backdropClass="cdk-overlay-dark-backdrop",this.disposeOnNavigation=!1,r){const e=Object.keys(r);for(const i of e)void 0!==r[i]&&(this[i]=r[i])}}}class qt{constructor(r,e){this.connectionPair=r,this.scrollableViewProperties=e}}let lt=(()=>{class n{constructor(e){this._attachedOverlays=[],this._document=e}ngOnDestroy(){this.detach()}add(e){this.remove(e),this._attachedOverlays.push(e)}remove(e){const i=this._attachedOverlays.indexOf(e);i>-1&&this._attachedOverlays.splice(i,1),0===this._attachedOverlays.length&&this.detach()}static{this.\u0275fac=function(i){return new(i||n)(t.KVO(p.qQ))}}static{this.\u0275prov=t.jDH({token:n,factory:n.\u0275fac,providedIn:"root"})}}return n})(),Ht=(()=>{class n extends lt{constructor(e,i){super(e),this._ngZone=i,this._keydownListener=o=>{const a=this._attachedOverlays;for(let s=a.length-1;s>-1;s--)if(a[s]._keydownEvents.observers.length>0){const d=a[s]._keydownEvents;this._ngZone?this._ngZone.run(()=>d.next(o)):d.next(o);break}}}add(e){super.add(e),this._isAttached||(this._ngZone?this._ngZone.runOutsideAngular(()=>this._document.body.addEventListener("keydown",this._keydownListener)):this._document.body.addEventListener("keydown",this._keydownListener),this._isAttached=!0)}detach(){this._isAttached&&(this._document.body.removeEventListener("keydown",this._keydownListener),this._isAttached=!1)}static{this.\u0275fac=function(i){return new(i||n)(t.KVO(p.qQ),t.KVO(t.SKi,8))}}static{this.\u0275prov=t.jDH({token:n,factory:n.\u0275fac,providedIn:"root"})}}return n})(),Xt=(()=>{class n extends lt{constructor(e,i,o){super(e),this._platform=i,this._ngZone=o,this._cursorStyleIsSet=!1,this._pointerDownListener=a=>{this._pointerDownEventTarget=(0,y.Fb)(a)},this._clickListener=a=>{const s=(0,y.Fb)(a),d="click"===a.type&&this._pointerDownEventTarget?this._pointerDownEventTarget:s;this._pointerDownEventTarget=null;const l=this._attachedOverlays.slice();for(let m=l.length-1;m>-1;m--){const u=l[m];if(u._outsidePointerEvents.observers.length<1||!u.hasAttached())continue;if(u.overlayElement.contains(s)||u.overlayElement.contains(d))break;const h=u._outsidePointerEvents;this._ngZone?this._ngZone.run(()=>h.next(a)):h.next(a)}}}add(e){if(super.add(e),!this._isAttached){const i=this._document.body;this._ngZone?this._ngZone.runOutsideAngular(()=>this._addEventListeners(i)):this._addEventListeners(i),this._platform.IOS&&!this._cursorStyleIsSet&&(this._cursorOriginalValue=i.style.cursor,i.style.cursor="pointer",this._cursorStyleIsSet=!0),this._isAttached=!0}}detach(){if(this._isAttached){const e=this._document.body;e.removeEventListener("pointerdown",this._pointerDownListener,!0),e.removeEventListener("click",this._clickListener,!0),e.removeEventListener("auxclick",this._clickListener,!0),e.removeEventListener("contextmenu",this._clickListener,!0),this._platform.IOS&&this._cursorStyleIsSet&&(e.style.cursor=this._cursorOriginalValue,this._cursorStyleIsSet=!1),this._isAttached=!1}}_addEventListeners(e){e.addEventListener("pointerdown",this._pointerDownListener,!0),e.addEventListener("click",this._clickListener,!0),e.addEventListener("auxclick",this._clickListener,!0),e.addEventListener("contextmenu",this._clickListener,!0)}static{this.\u0275fac=function(i){return new(i||n)(t.KVO(p.qQ),t.KVO(y.OD),t.KVO(t.SKi,8))}}static{this.\u0275prov=t.jDH({token:n,factory:n.\u0275fac,providedIn:"root"})}}return n})(),z=(()=>{class n{constructor(e,i){this._platform=i,this._document=e}ngOnDestroy(){this._containerElement?.remove()}getContainerElement(){return this._containerElement||this._createContainer(),this._containerElement}_createContainer(){const e="cdk-overlay-container";if(this._platform.isBrowser||(0,y.v8)()){const o=this._document.querySelectorAll(`.${e}[platform="server"], .${e}[platform="test"]`);for(let a=0;a<o.length;a++)o[a].remove()}const i=this._document.createElement("div");i.classList.add(e),(0,y.v8)()?i.setAttribute("platform","test"):this._platform.isBrowser||i.setAttribute("platform","server"),this._document.body.appendChild(i),this._containerElement=i}static{this.\u0275fac=function(i){return new(i||n)(t.KVO(p.qQ),t.KVO(y.OD))}}static{this.\u0275prov=t.jDH({token:n,factory:n.\u0275fac,providedIn:"root"})}}return n})();class T{constructor(r,e,i,o,a,s,d,l,m,u=!1){this._portalOutlet=r,this._host=e,this._pane=i,this._config=o,this._ngZone=a,this._keyboardDispatcher=s,this._document=d,this._location=l,this._outsideClickDispatcher=m,this._animationsDisabled=u,this._backdropElement=null,this._backdropClick=new j.B,this._attachments=new j.B,this._detachments=new j.B,this._locationChanges=ot.yU.EMPTY,this._backdropClickHandler=h=>this._backdropClick.next(h),this._backdropTransitionendHandler=h=>{this._disposeBackdrop(h.target)},this._keydownEvents=new j.B,this._outsidePointerEvents=new j.B,o.scrollStrategy&&(this._scrollStrategy=o.scrollStrategy,this._scrollStrategy.attach(this)),this._positionStrategy=o.positionStrategy}get overlayElement(){return this._pane}get backdropElement(){return this._backdropElement}get hostElement(){return this._host}attach(r){!this._host.parentElement&&this._previousHostParent&&this._previousHostParent.appendChild(this._host);const e=this._portalOutlet.attach(r);return this._positionStrategy&&this._positionStrategy.attach(this),this._updateStackingOrder(),this._updateElementSize(),this._updateElementDirection(),this._scrollStrategy&&this._scrollStrategy.enable(),this._ngZone.onStable.pipe((0,N.s)(1)).subscribe(()=>{this.hasAttached()&&this.updatePosition()}),this._togglePointerEvents(!0),this._config.hasBackdrop&&this._attachBackdrop(),this._config.panelClass&&this._toggleClasses(this._pane,this._config.panelClass,!0),this._attachments.next(),this._keyboardDispatcher.add(this),this._config.disposeOnNavigation&&(this._locationChanges=this._location.subscribe(()=>this.dispose())),this._outsideClickDispatcher.add(this),"function"==typeof e?.onDestroy&&e.onDestroy(()=>{this.hasAttached()&&this._ngZone.runOutsideAngular(()=>Promise.resolve().then(()=>this.detach()))}),e}detach(){if(!this.hasAttached())return;this.detachBackdrop(),this._togglePointerEvents(!1),this._positionStrategy&&this._positionStrategy.detach&&this._positionStrategy.detach(),this._scrollStrategy&&this._scrollStrategy.disable();const r=this._portalOutlet.detach();return this._detachments.next(),this._keyboardDispatcher.remove(this),this._detachContentWhenStable(),this._locationChanges.unsubscribe(),this._outsideClickDispatcher.remove(this),r}dispose(){const r=this.hasAttached();this._positionStrategy&&this._positionStrategy.dispose(),this._disposeScrollStrategy(),this._disposeBackdrop(this._backdropElement),this._locationChanges.unsubscribe(),this._keyboardDispatcher.remove(this),this._portalOutlet.dispose(),this._attachments.complete(),this._backdropClick.complete(),this._keydownEvents.complete(),this._outsidePointerEvents.complete(),this._outsideClickDispatcher.remove(this),this._host?.remove(),this._previousHostParent=this._pane=this._host=null,r&&this._detachments.next(),this._detachments.complete()}hasAttached(){return this._portalOutlet.hasAttached()}backdropClick(){return this._backdropClick}attachments(){return this._attachments}detachments(){return this._detachments}keydownEvents(){return this._keydownEvents}outsidePointerEvents(){return this._outsidePointerEvents}getConfig(){return this._config}updatePosition(){this._positionStrategy&&this._positionStrategy.apply()}updatePositionStrategy(r){r!==this._positionStrategy&&(this._positionStrategy&&this._positionStrategy.dispose(),this._positionStrategy=r,this.hasAttached()&&(r.attach(this),this.updatePosition()))}updateSize(r){this._config={...this._config,...r},this._updateElementSize()}setDirection(r){this._config={...this._config,direction:r},this._updateElementDirection()}addPanelClass(r){this._pane&&this._toggleClasses(this._pane,r,!0)}removePanelClass(r){this._pane&&this._toggleClasses(this._pane,r,!1)}getDirection(){const r=this._config.direction;return r?"string"==typeof r?r:r.value:"ltr"}updateScrollStrategy(r){r!==this._scrollStrategy&&(this._disposeScrollStrategy(),this._scrollStrategy=r,this.hasAttached()&&(r.attach(this),r.enable()))}_updateElementDirection(){this._host.setAttribute("dir",this.getDirection())}_updateElementSize(){if(!this._pane)return;const r=this._pane.style;r.width=(0,f.a1)(this._config.width),r.height=(0,f.a1)(this._config.height),r.minWidth=(0,f.a1)(this._config.minWidth),r.minHeight=(0,f.a1)(this._config.minHeight),r.maxWidth=(0,f.a1)(this._config.maxWidth),r.maxHeight=(0,f.a1)(this._config.maxHeight)}_togglePointerEvents(r){this._pane.style.pointerEvents=r?"":"none"}_attachBackdrop(){const r="cdk-overlay-backdrop-showing";this._backdropElement=this._document.createElement("div"),this._backdropElement.classList.add("cdk-overlay-backdrop"),this._animationsDisabled&&this._backdropElement.classList.add("cdk-overlay-backdrop-noop-animation"),this._config.backdropClass&&this._toggleClasses(this._backdropElement,this._config.backdropClass,!0),this._host.parentElement.insertBefore(this._backdropElement,this._host),this._backdropElement.addEventListener("click",this._backdropClickHandler),!this._animationsDisabled&&typeof requestAnimationFrame<"u"?this._ngZone.runOutsideAngular(()=>{requestAnimationFrame(()=>{this._backdropElement&&this._backdropElement.classList.add(r)})}):this._backdropElement.classList.add(r)}_updateStackingOrder(){this._host.nextSibling&&this._host.parentNode.appendChild(this._host)}detachBackdrop(){const r=this._backdropElement;if(r){if(this._animationsDisabled)return void this._disposeBackdrop(r);r.classList.remove("cdk-overlay-backdrop-showing"),this._ngZone.runOutsideAngular(()=>{r.addEventListener("transitionend",this._backdropTransitionendHandler)}),r.style.pointerEvents="none",this._backdropTimeout=this._ngZone.runOutsideAngular(()=>setTimeout(()=>{this._disposeBackdrop(r)},500))}}_toggleClasses(r,e,i){const o=(0,f.FG)(e||[]).filter(a=>!!a);o.length&&(i?r.classList.add(...o):r.classList.remove(...o))}_detachContentWhenStable(){this._ngZone.runOutsideAngular(()=>{const r=this._ngZone.onStable.pipe((0,it.Q)((0,nt.h)(this._attachments,this._detachments))).subscribe(()=>{(!this._pane||!this._host||0===this._pane.children.length)&&(this._pane&&this._config.panelClass&&this._toggleClasses(this._pane,this._config.panelClass,!1),this._host&&this._host.parentElement&&(this._previousHostParent=this._host.parentElement,this._host.remove()),r.unsubscribe())})})}_disposeScrollStrategy(){const r=this._scrollStrategy;r&&(r.disable(),r.detach&&r.detach())}_disposeBackdrop(r){r&&(r.removeEventListener("click",this._backdropClickHandler),r.removeEventListener("transitionend",this._backdropTransitionendHandler),r.remove(),this._backdropElement===r&&(this._backdropElement=null)),this._backdropTimeout&&(clearTimeout(this._backdropTimeout),this._backdropTimeout=void 0)}}const ut="cdk-overlay-connected-position-bounding-box",Ut=/([A-Za-z%]+)$/;class Zt{get positions(){return this._preferredPositions}constructor(r,e,i,o,a){this._viewportRuler=e,this._document=i,this._platform=o,this._overlayContainer=a,this._lastBoundingBoxSize={width:0,height:0},this._isPushed=!1,this._canPush=!0,this._growAfterOpen=!1,this._hasFlexibleDimensions=!0,this._positionLocked=!1,this._viewportMargin=0,this._scrollables=[],this._preferredPositions=[],this._positionChanges=new j.B,this._resizeSubscription=ot.yU.EMPTY,this._offsetX=0,this._offsetY=0,this._appliedPanelClasses=[],this.positionChanges=this._positionChanges,this.setOrigin(r)}attach(r){this._validatePositions(),r.hostElement.classList.add(ut),this._overlayRef=r,this._boundingBox=r.hostElement,this._pane=r.overlayElement,this._isDisposed=!1,this._isInitialRender=!0,this._lastPosition=null,this._resizeSubscription.unsubscribe(),this._resizeSubscription=this._viewportRuler.change().subscribe(()=>{this._isInitialRender=!0,this.apply()})}apply(){if(this._isDisposed||!this._platform.isBrowser)return;if(!this._isInitialRender&&this._positionLocked&&this._lastPosition)return void this.reapplyLastPosition();this._clearPanelClasses(),this._resetOverlayElementStyles(),this._resetBoundingBoxStyles(),this._viewportRect=this._getNarrowedViewportRect(),this._originRect=this._getOriginRect(),this._overlayRect=this._pane.getBoundingClientRect(),this._containerRect=this._overlayContainer.getContainerElement().getBoundingClientRect();const r=this._originRect,e=this._overlayRect,i=this._viewportRect,o=this._containerRect,a=[];let s;for(let d of this._preferredPositions){let l=this._getOriginPoint(r,o,d),m=this._getOverlayPoint(l,e,d),u=this._getOverlayFit(m,e,i,d);if(u.isCompletelyWithinViewport)return this._isPushed=!1,void this._applyPosition(d,l);this._canFitWithFlexibleDimensions(u,m,i)?a.push({position:d,origin:l,overlayRect:e,boundingBoxRect:this._calculateBoundingBoxRect(l,d)}):(!s||s.overlayFit.visibleArea<u.visibleArea)&&(s={overlayFit:u,overlayPoint:m,originPoint:l,position:d,overlayRect:e})}if(a.length){let d=null,l=-1;for(const m of a){const u=m.boundingBoxRect.width*m.boundingBoxRect.height*(m.position.weight||1);u>l&&(l=u,d=m)}return this._isPushed=!1,void this._applyPosition(d.position,d.origin)}if(this._canPush)return this._isPushed=!0,void this._applyPosition(s.position,s.originPoint);this._applyPosition(s.position,s.originPoint)}detach(){this._clearPanelClasses(),this._lastPosition=null,this._previousPushAmount=null,this._resizeSubscription.unsubscribe()}dispose(){this._isDisposed||(this._boundingBox&&M(this._boundingBox.style,{top:"",left:"",right:"",bottom:"",height:"",width:"",alignItems:"",justifyContent:""}),this._pane&&this._resetOverlayElementStyles(),this._overlayRef&&this._overlayRef.hostElement.classList.remove(ut),this.detach(),this._positionChanges.complete(),this._overlayRef=this._boundingBox=null,this._isDisposed=!0)}reapplyLastPosition(){if(this._isDisposed||!this._platform.isBrowser)return;const r=this._lastPosition;if(r){this._originRect=this._getOriginRect(),this._overlayRect=this._pane.getBoundingClientRect(),this._viewportRect=this._getNarrowedViewportRect(),this._containerRect=this._overlayContainer.getContainerElement().getBoundingClientRect();const e=this._getOriginPoint(this._originRect,this._containerRect,r);this._applyPosition(r,e)}else this.apply()}withScrollableContainers(r){return this._scrollables=r,this}withPositions(r){return this._preferredPositions=r,-1===r.indexOf(this._lastPosition)&&(this._lastPosition=null),this._validatePositions(),this}withViewportMargin(r){return this._viewportMargin=r,this}withFlexibleDimensions(r=!0){return this._hasFlexibleDimensions=r,this}withGrowAfterOpen(r=!0){return this._growAfterOpen=r,this}withPush(r=!0){return this._canPush=r,this}withLockedPosition(r=!0){return this._positionLocked=r,this}setOrigin(r){return this._origin=r,this}withDefaultOffsetX(r){return this._offsetX=r,this}withDefaultOffsetY(r){return this._offsetY=r,this}withTransformOriginOn(r){return this._transformOriginSelector=r,this}_getOriginPoint(r,e,i){let o,a;if("center"==i.originX)o=r.left+r.width/2;else{const s=this._isRtl()?r.right:r.left,d=this._isRtl()?r.left:r.right;o="start"==i.originX?s:d}return e.left<0&&(o-=e.left),a="center"==i.originY?r.top+r.height/2:"top"==i.originY?r.top:r.bottom,e.top<0&&(a-=e.top),{x:o,y:a}}_getOverlayPoint(r,e,i){let o,a;return o="center"==i.overlayX?-e.width/2:"start"===i.overlayX?this._isRtl()?-e.width:0:this._isRtl()?0:-e.width,a="center"==i.overlayY?-e.height/2:"top"==i.overlayY?0:-e.height,{x:r.x+o,y:r.y+a}}_getOverlayFit(r,e,i,o){const a=pt(e);let{x:s,y:d}=r,l=this._getOffset(o,"x"),m=this._getOffset(o,"y");l&&(s+=l),m&&(d+=m);let C=0-d,x=d+a.height-i.height,k=this._subtractOverflows(a.width,0-s,s+a.width-i.width),_=this._subtractOverflows(a.height,C,x),B=k*_;return{visibleArea:B,isCompletelyWithinViewport:a.width*a.height===B,fitsInViewportVertically:_===a.height,fitsInViewportHorizontally:k==a.width}}_canFitWithFlexibleDimensions(r,e,i){if(this._hasFlexibleDimensions){const o=i.bottom-e.y,a=i.right-e.x,s=mt(this._overlayRef.getConfig().minHeight),d=mt(this._overlayRef.getConfig().minWidth);return(r.fitsInViewportVertically||null!=s&&s<=o)&&(r.fitsInViewportHorizontally||null!=d&&d<=a)}return!1}_pushOverlayOnScreen(r,e,i){if(this._previousPushAmount&&this._positionLocked)return{x:r.x+this._previousPushAmount.x,y:r.y+this._previousPushAmount.y};const o=pt(e),a=this._viewportRect,s=Math.max(r.x+o.width-a.width,0),d=Math.max(r.y+o.height-a.height,0),l=Math.max(a.top-i.top-r.y,0),m=Math.max(a.left-i.left-r.x,0);let u=0,h=0;return u=o.width<=a.width?m||-s:r.x<this._viewportMargin?a.left-i.left-r.x:0,h=o.height<=a.height?l||-d:r.y<this._viewportMargin?a.top-i.top-r.y:0,this._previousPushAmount={x:u,y:h},{x:r.x+u,y:r.y+h}}_applyPosition(r,e){if(this._setTransformOrigin(r),this._setOverlayElementStyles(e,r),this._setBoundingBoxStyles(e,r),r.panelClass&&this._addPanelClasses(r.panelClass),this._lastPosition=r,this._positionChanges.observers.length){const i=this._getScrollVisibility(),o=new qt(r,i);this._positionChanges.next(o)}this._isInitialRender=!1}_setTransformOrigin(r){if(!this._transformOriginSelector)return;const e=this._boundingBox.querySelectorAll(this._transformOriginSelector);let i,o=r.overlayY;i="center"===r.overlayX?"center":this._isRtl()?"start"===r.overlayX?"right":"left":"start"===r.overlayX?"left":"right";for(let a=0;a<e.length;a++)e[a].style.transformOrigin=`${i} ${o}`}_calculateBoundingBoxRect(r,e){const i=this._viewportRect,o=this._isRtl();let a,s,d,u,h,C;if("top"===e.overlayY)s=r.y,a=i.height-s+this._viewportMargin;else if("bottom"===e.overlayY)d=i.height-r.y+2*this._viewportMargin,a=i.height-d+this._viewportMargin;else{const x=Math.min(i.bottom-r.y+i.top,r.y),k=this._lastBoundingBoxSize.height;a=2*x,s=r.y-x,a>k&&!this._isInitialRender&&!this._growAfterOpen&&(s=r.y-k/2)}if("end"===e.overlayX&&!o||"start"===e.overlayX&&o)C=i.width-r.x+this._viewportMargin,u=r.x-this._viewportMargin;else if("start"===e.overlayX&&!o||"end"===e.overlayX&&o)h=r.x,u=i.right-r.x;else{const x=Math.min(i.right-r.x+i.left,r.x),k=this._lastBoundingBoxSize.width;u=2*x,h=r.x-x,u>k&&!this._isInitialRender&&!this._growAfterOpen&&(h=r.x-k/2)}return{top:s,left:h,bottom:d,right:C,width:u,height:a}}_setBoundingBoxStyles(r,e){const i=this._calculateBoundingBoxRect(r,e);!this._isInitialRender&&!this._growAfterOpen&&(i.height=Math.min(i.height,this._lastBoundingBoxSize.height),i.width=Math.min(i.width,this._lastBoundingBoxSize.width));const o={};if(this._hasExactPosition())o.top=o.left="0",o.bottom=o.right=o.maxHeight=o.maxWidth="",o.width=o.height="100%";else{const a=this._overlayRef.getConfig().maxHeight,s=this._overlayRef.getConfig().maxWidth;o.height=(0,f.a1)(i.height),o.top=(0,f.a1)(i.top),o.bottom=(0,f.a1)(i.bottom),o.width=(0,f.a1)(i.width),o.left=(0,f.a1)(i.left),o.right=(0,f.a1)(i.right),o.alignItems="center"===e.overlayX?"center":"end"===e.overlayX?"flex-end":"flex-start",o.justifyContent="center"===e.overlayY?"center":"bottom"===e.overlayY?"flex-end":"flex-start",a&&(o.maxHeight=(0,f.a1)(a)),s&&(o.maxWidth=(0,f.a1)(s))}this._lastBoundingBoxSize=i,M(this._boundingBox.style,o)}_resetBoundingBoxStyles(){M(this._boundingBox.style,{top:"0",left:"0",right:"0",bottom:"0",height:"",width:"",alignItems:"",justifyContent:""})}_resetOverlayElementStyles(){M(this._pane.style,{top:"",left:"",bottom:"",right:"",position:"",transform:""})}_setOverlayElementStyles(r,e){const i={},o=this._hasExactPosition(),a=this._hasFlexibleDimensions,s=this._overlayRef.getConfig();if(o){const u=this._viewportRuler.getViewportScrollPosition();M(i,this._getExactOverlayY(e,r,u)),M(i,this._getExactOverlayX(e,r,u))}else i.position="static";let d="",l=this._getOffset(e,"x"),m=this._getOffset(e,"y");l&&(d+=`translateX(${l}px) `),m&&(d+=`translateY(${m}px)`),i.transform=d.trim(),s.maxHeight&&(o?i.maxHeight=(0,f.a1)(s.maxHeight):a&&(i.maxHeight="")),s.maxWidth&&(o?i.maxWidth=(0,f.a1)(s.maxWidth):a&&(i.maxWidth="")),M(this._pane.style,i)}_getExactOverlayY(r,e,i){let o={top:"",bottom:""},a=this._getOverlayPoint(e,this._overlayRect,r);return this._isPushed&&(a=this._pushOverlayOnScreen(a,this._overlayRect,i)),"bottom"===r.overlayY?o.bottom=this._document.documentElement.clientHeight-(a.y+this._overlayRect.height)+"px":o.top=(0,f.a1)(a.y),o}_getExactOverlayX(r,e,i){let s,o={left:"",right:""},a=this._getOverlayPoint(e,this._overlayRect,r);return this._isPushed&&(a=this._pushOverlayOnScreen(a,this._overlayRect,i)),s=this._isRtl()?"end"===r.overlayX?"left":"right":"end"===r.overlayX?"right":"left","right"===s?o.right=this._document.documentElement.clientWidth-(a.x+this._overlayRect.width)+"px":o.left=(0,f.a1)(a.x),o}_getScrollVisibility(){const r=this._getOriginRect(),e=this._pane.getBoundingClientRect(),i=this._scrollables.map(o=>o.getElementRef().nativeElement.getBoundingClientRect());return{isOriginClipped:dt(r,i),isOriginOutsideView:K(r,i),isOverlayClipped:dt(e,i),isOverlayOutsideView:K(e,i)}}_subtractOverflows(r,...e){return e.reduce((i,o)=>i-Math.max(o,0),r)}_getNarrowedViewportRect(){const r=this._document.documentElement.clientWidth,e=this._document.documentElement.clientHeight,i=this._viewportRuler.getViewportScrollPosition();return{top:i.top+this._viewportMargin,left:i.left+this._viewportMargin,right:i.left+r-this._viewportMargin,bottom:i.top+e-this._viewportMargin,width:r-2*this._viewportMargin,height:e-2*this._viewportMargin}}_isRtl(){return"rtl"===this._overlayRef.getDirection()}_hasExactPosition(){return!this._hasFlexibleDimensions||this._isPushed}_getOffset(r,e){return"x"===e?null==r.offsetX?this._offsetX:r.offsetX:null==r.offsetY?this._offsetY:r.offsetY}_validatePositions(){}_addPanelClasses(r){this._pane&&(0,f.FG)(r).forEach(e=>{""!==e&&-1===this._appliedPanelClasses.indexOf(e)&&(this._appliedPanelClasses.push(e),this._pane.classList.add(e))})}_clearPanelClasses(){this._pane&&(this._appliedPanelClasses.forEach(r=>{this._pane.classList.remove(r)}),this._appliedPanelClasses=[])}_getOriginRect(){const r=this._origin;if(r instanceof t.aKT)return r.nativeElement.getBoundingClientRect();if(r instanceof Element)return r.getBoundingClientRect();const e=r.width||0,i=r.height||0;return{top:r.y,bottom:r.y+i,left:r.x,right:r.x+e,height:i,width:e}}}function M(n,r){for(let e in r)r.hasOwnProperty(e)&&(n[e]=r[e]);return n}function mt(n){if("number"!=typeof n&&null!=n){const[r,e]=n.split(Ut);return e&&"px"!==e?null:parseFloat(r)}return n||null}function pt(n){return{top:Math.floor(n.top),right:Math.floor(n.right),bottom:Math.floor(n.bottom),left:Math.floor(n.left),width:Math.floor(n.width),height:Math.floor(n.height)}}const ht="cdk-global-overlay-wrapper";class Kt{constructor(){this._cssPosition="static",this._topOffset="",this._bottomOffset="",this._alignItems="",this._xPosition="",this._xOffset="",this._width="",this._height="",this._isDisposed=!1}attach(r){const e=r.getConfig();this._overlayRef=r,this._width&&!e.width&&r.updateSize({width:this._width}),this._height&&!e.height&&r.updateSize({height:this._height}),r.hostElement.classList.add(ht),this._isDisposed=!1}top(r=""){return this._bottomOffset="",this._topOffset=r,this._alignItems="flex-start",this}left(r=""){return this._xOffset=r,this._xPosition="left",this}bottom(r=""){return this._topOffset="",this._bottomOffset=r,this._alignItems="flex-end",this}right(r=""){return this._xOffset=r,this._xPosition="right",this}start(r=""){return this._xOffset=r,this._xPosition="start",this}end(r=""){return this._xOffset=r,this._xPosition="end",this}width(r=""){return this._overlayRef?this._overlayRef.updateSize({width:r}):this._width=r,this}height(r=""){return this._overlayRef?this._overlayRef.updateSize({height:r}):this._height=r,this}centerHorizontally(r=""){return this.left(r),this._xPosition="center",this}centerVertically(r=""){return this.top(r),this._alignItems="center",this}apply(){if(!this._overlayRef||!this._overlayRef.hasAttached())return;const r=this._overlayRef.overlayElement.style,e=this._overlayRef.hostElement.style,i=this._overlayRef.getConfig(),{width:o,height:a,maxWidth:s,maxHeight:d}=i,l=!("100%"!==o&&"100vw"!==o||s&&"100%"!==s&&"100vw"!==s),m=!("100%"!==a&&"100vh"!==a||d&&"100%"!==d&&"100vh"!==d),u=this._xPosition,h=this._xOffset,C="rtl"===this._overlayRef.getConfig().direction;let x="",k="",_="";l?_="flex-start":"center"===u?(_="center",C?k=h:x=h):C?"left"===u||"end"===u?(_="flex-end",x=h):("right"===u||"start"===u)&&(_="flex-start",k=h):"left"===u||"start"===u?(_="flex-start",x=h):("right"===u||"end"===u)&&(_="flex-end",k=h),r.position=this._cssPosition,r.marginLeft=l?"0":x,r.marginTop=m?"0":this._topOffset,r.marginBottom=this._bottomOffset,r.marginRight=l?"0":k,e.justifyContent=_,e.alignItems=m?"flex-start":this._alignItems}dispose(){if(this._isDisposed||!this._overlayRef)return;const r=this._overlayRef.overlayElement.style,e=this._overlayRef.hostElement,i=e.style;e.classList.remove(ht),i.justifyContent=i.alignItems=r.marginTop=r.marginBottom=r.marginLeft=r.marginRight=r.position="",this._overlayRef=null,this._isDisposed=!0}}let Wt=(()=>{class n{constructor(e,i,o,a){this._viewportRuler=e,this._document=i,this._platform=o,this._overlayContainer=a}global(){return new Kt}flexibleConnectedTo(e){return new Zt(e,this._viewportRuler,this._document,this._platform,this._overlayContainer)}static{this.\u0275fac=function(i){return new(i||n)(t.KVO(S.Xj),t.KVO(p.qQ),t.KVO(y.OD),t.KVO(z))}}static{this.\u0275prov=t.jDH({token:n,factory:n.\u0275fac,providedIn:"root"})}}return n})(),Jt=0,D=(()=>{class n{constructor(e,i,o,a,s,d,l,m,u,h,C,x){this.scrollStrategies=e,this._overlayContainer=i,this._componentFactoryResolver=o,this._positionBuilder=a,this._keyboardDispatcher=s,this._injector=d,this._ngZone=l,this._document=m,this._directionality=u,this._location=h,this._outsideClickDispatcher=C,this._animationsModuleType=x}create(e){const i=this._createHostElement(),o=this._createPaneElement(i),a=this._createPortalOutlet(o),s=new ct(e);return s.direction=s.direction||this._directionality.value,new T(a,i,o,s,this._ngZone,this._keyboardDispatcher,this._document,this._location,this._outsideClickDispatcher,"NoopAnimations"===this._animationsModuleType)}position(){return this._positionBuilder}_createPaneElement(e){const i=this._document.createElement("div");return i.id="cdk-overlay-"+Jt++,i.classList.add("cdk-overlay-pane"),e.appendChild(i),i}_createHostElement(){const e=this._document.createElement("div");return this._overlayContainer.getContainerElement().appendChild(e),e}_createPortalOutlet(e){return this._appRef||(this._appRef=this._injector.get(t.o8S)),new Vt(e,this._componentFactoryResolver,this._appRef,this._injector,this._document)}static{this.\u0275fac=function(i){return new(i||n)(t.KVO(Yt),t.KVO(z),t.KVO(t.OM3),t.KVO(Wt),t.KVO(Ht),t.KVO(t.zZn),t.KVO(t.SKi),t.KVO(p.qQ),t.KVO(F.dS),t.KVO(p.aZ),t.KVO(Xt),t.KVO(t.bc$,8))}}static{this.\u0275prov=t.jDH({token:n,factory:n.\u0275fac,providedIn:"root"})}}return n})();const ee={provide:new t.nKC("cdk-connected-overlay-scroll-strategy"),deps:[D],useFactory:function te(n){return()=>n.scrollStrategies.reposition()}};let gt=(()=>{class n{static{this.\u0275fac=function(i){return new(i||n)}}static{this.\u0275mod=t.$C({type:n})}static{this.\u0275inj=t.G2t({providers:[D,ee],imports:[F.jI,V,S.E9,S.E9]})}}return n})();var w=g(9037),$=g(7336),ft=g(9030),bt=g(7673),_t=g(9172);function ie(n,r){}class G{constructor(){this.role="dialog",this.panelClass="",this.hasBackdrop=!0,this.backdropClass="",this.disableClose=!1,this.width="",this.height="",this.data=null,this.ariaDescribedBy=null,this.ariaLabelledBy=null,this.ariaLabel=null,this.ariaModal=!0,this.autoFocus="first-tabbable",this.restoreFocus=!0,this.closeOnNavigation=!0,this.closeOnDestroy=!0,this.closeOnOverlayDetachments=!0}}let vt=(()=>{class n extends U{constructor(e,i,o,a,s,d,l,m){super(),this._elementRef=e,this._focusTrapFactory=i,this._config=a,this._interactivityChecker=s,this._ngZone=d,this._overlayRef=l,this._focusMonitor=m,this._elementFocusedBeforeDialogWasOpened=null,this._closeInteractionType=null,this._ariaLabelledByQueue=[],this.attachDomPortal=u=>{this._portalOutlet.hasAttached();const h=this._portalOutlet.attachDomPortal(u);return this._contentAttached(),h},this._document=o,this._config.ariaLabelledBy&&this._ariaLabelledByQueue.push(this._config.ariaLabelledBy)}_contentAttached(){this._initializeFocusTrap(),this._handleBackdropClicks(),this._captureInitialFocus()}_captureInitialFocus(){this._trapFocus()}ngOnDestroy(){this._restoreFocus()}attachComponentPortal(e){this._portalOutlet.hasAttached();const i=this._portalOutlet.attachComponentPortal(e);return this._contentAttached(),i}attachTemplatePortal(e){this._portalOutlet.hasAttached();const i=this._portalOutlet.attachTemplatePortal(e);return this._contentAttached(),i}_recaptureFocus(){this._containsFocus()||this._trapFocus()}_forceFocus(e,i){this._interactivityChecker.isFocusable(e)||(e.tabIndex=-1,this._ngZone.runOutsideAngular(()=>{const o=()=>{e.removeEventListener("blur",o),e.removeEventListener("mousedown",o),e.removeAttribute("tabindex")};e.addEventListener("blur",o),e.addEventListener("mousedown",o)})),e.focus(i)}_focusByCssSelector(e,i){let o=this._elementRef.nativeElement.querySelector(e);o&&this._forceFocus(o,i)}_trapFocus(){const e=this._elementRef.nativeElement;switch(this._config.autoFocus){case!1:case"dialog":this._containsFocus()||e.focus();break;case!0:case"first-tabbable":this._focusTrap.focusInitialElementWhenReady().then(i=>{i||this._focusDialogContainer()});break;case"first-heading":this._focusByCssSelector('h1, h2, h3, h4, h5, h6, [role="heading"]');break;default:this._focusByCssSelector(this._config.autoFocus)}}_restoreFocus(){const e=this._config.restoreFocus;let i=null;if("string"==typeof e?i=this._document.querySelector(e):"boolean"==typeof e?i=e?this._elementFocusedBeforeDialogWasOpened:null:e&&(i=e),this._config.restoreFocus&&i&&"function"==typeof i.focus){const o=(0,y.vc)(),a=this._elementRef.nativeElement;(!o||o===this._document.body||o===a||a.contains(o))&&(this._focusMonitor?(this._focusMonitor.focusVia(i,this._closeInteractionType),this._closeInteractionType=null):i.focus())}this._focusTrap&&this._focusTrap.destroy()}_focusDialogContainer(){this._elementRef.nativeElement.focus&&this._elementRef.nativeElement.focus()}_containsFocus(){const e=this._elementRef.nativeElement,i=(0,y.vc)();return e===i||e.contains(i)}_initializeFocusTrap(){this._focusTrap=this._focusTrapFactory.create(this._elementRef.nativeElement),this._document&&(this._elementFocusedBeforeDialogWasOpened=(0,y.vc)())}_handleBackdropClicks(){this._overlayRef.backdropClick().subscribe(()=>{this._config.disableClose&&this._recaptureFocus()})}static{this.\u0275fac=function(i){return new(i||n)(t.rXU(t.aKT),t.rXU(w.GX),t.rXU(p.qQ,8),t.rXU(G),t.rXU(w.Z7),t.rXU(t.SKi),t.rXU(T),t.rXU(w.FN))}}static{this.\u0275cmp=t.VBU({type:n,selectors:[["cdk-dialog-container"]],viewQuery:function(i,o){if(1&i&&t.GBs(Z,7),2&i){let a;t.mGM(a=t.lsd())&&(o._portalOutlet=a.first)}},hostAttrs:["tabindex","-1",1,"cdk-dialog-container"],hostVars:6,hostBindings:function(i,o){2&i&&t.BMQ("id",o._config.id||null)("role",o._config.role)("aria-modal",o._config.ariaModal)("aria-labelledby",o._config.ariaLabel?null:o._ariaLabelledByQueue[0])("aria-label",o._config.ariaLabel)("aria-describedby",o._config.ariaDescribedBy||null)},features:[t.Vt3],decls:1,vars:0,consts:[["cdkPortalOutlet",""]],template:function(i,o){1&i&&t.DNE(0,ie,0,0,"ng-template",0)},dependencies:[Z],styles:[".cdk-dialog-container{display:block;width:100%;height:100%;min-height:inherit;max-height:inherit}"],encapsulation:2})}}return n})();class W{constructor(r,e){this.overlayRef=r,this.config=e,this.closed=new j.B,this.disableClose=e.disableClose,this.backdropClick=r.backdropClick(),this.keydownEvents=r.keydownEvents(),this.outsidePointerEvents=r.outsidePointerEvents(),this.id=e.id,this.keydownEvents.subscribe(i=>{i.keyCode===$._f&&!this.disableClose&&!(0,$.rp)(i)&&(i.preventDefault(),this.close(void 0,{focusOrigin:"keyboard"}))}),this.backdropClick.subscribe(()=>{this.disableClose||this.close(void 0,{focusOrigin:"mouse"})}),this._detachSubscription=r.detachments().subscribe(()=>{!1!==e.closeOnOverlayDetachments&&this.close()})}close(r,e){if(this.containerInstance){const i=this.closed;this.containerInstance._closeInteractionType=e?.focusOrigin||"program",this._detachSubscription.unsubscribe(),this.overlayRef.dispose(),i.next(r),i.complete(),this.componentInstance=this.containerInstance=null}}updatePosition(){return this.overlayRef.updatePosition(),this}updateSize(r="",e=""){return this.overlayRef.updateSize({width:r,height:e}),this}addPanelClass(r){return this.overlayRef.addPanelClass(r),this}removePanelClass(r){return this.overlayRef.removePanelClass(r),this}}const xt=new t.nKC("DialogScrollStrategy"),re=new t.nKC("DialogData"),oe=new t.nKC("DefaultDialogConfig"),ae={provide:xt,deps:[D],useFactory:function ne(n){return()=>n.scrollStrategies.block()}};let se=0,kt=(()=>{class n{get openDialogs(){return this._parentDialog?this._parentDialog.openDialogs:this._openDialogsAtThisLevel}get afterOpened(){return this._parentDialog?this._parentDialog.afterOpened:this._afterOpenedAtThisLevel}constructor(e,i,o,a,s,d){this._overlay=e,this._injector=i,this._defaultOptions=o,this._parentDialog=a,this._overlayContainer=s,this._openDialogsAtThisLevel=[],this._afterAllClosedAtThisLevel=new j.B,this._afterOpenedAtThisLevel=new j.B,this._ariaHiddenElements=new Map,this.afterAllClosed=(0,ft.v)(()=>this.openDialogs.length?this._getAfterAllClosed():this._getAfterAllClosed().pipe((0,_t.Z)(void 0))),this._scrollStrategy=d}open(e,i){(i={...this._defaultOptions||new G,...i}).id=i.id||"cdk-dialog-"+se++,i.id&&this.getDialogById(i.id);const a=this._getOverlayConfig(i),s=this._overlay.create(a),d=new W(s,i),l=this._attachContainer(s,d,i);return d.containerInstance=l,this._attachDialogContent(e,d,l,i),this.openDialogs.length||this._hideNonDialogContentFromAssistiveTechnology(),this.openDialogs.push(d),d.closed.subscribe(()=>this._removeOpenDialog(d,!0)),this.afterOpened.next(d),d}closeAll(){J(this.openDialogs,e=>e.close())}getDialogById(e){return this.openDialogs.find(i=>i.id===e)}ngOnDestroy(){J(this._openDialogsAtThisLevel,e=>{!1===e.config.closeOnDestroy&&this._removeOpenDialog(e,!1)}),J(this._openDialogsAtThisLevel,e=>e.close()),this._afterAllClosedAtThisLevel.complete(),this._afterOpenedAtThisLevel.complete(),this._openDialogsAtThisLevel=[]}_getOverlayConfig(e){const i=new ct({positionStrategy:e.positionStrategy||this._overlay.position().global().centerHorizontally().centerVertically(),scrollStrategy:e.scrollStrategy||this._scrollStrategy(),panelClass:e.panelClass,hasBackdrop:e.hasBackdrop,direction:e.direction,minWidth:e.minWidth,minHeight:e.minHeight,maxWidth:e.maxWidth,maxHeight:e.maxHeight,width:e.width,height:e.height,disposeOnNavigation:e.closeOnNavigation});return e.backdropClass&&(i.backdropClass=e.backdropClass),i}_attachContainer(e,i,o){const a=o.injector||o.viewContainerRef?.injector,s=[{provide:G,useValue:o},{provide:W,useValue:i},{provide:T,useValue:e}];let d;o.container?"function"==typeof o.container?d=o.container:(d=o.container.type,s.push(...o.container.providers(o))):d=vt;const l=new X(d,o.viewContainerRef,t.zZn.create({parent:a||this._injector,providers:s}),o.componentFactoryResolver);return e.attach(l).instance}_attachDialogContent(e,i,o,a){if(e instanceof t.C4Q){const s=this._createInjector(a,i,o,void 0);let d={$implicit:a.data,dialogRef:i};a.templateContext&&(d={...d,..."function"==typeof a.templateContext?a.templateContext():a.templateContext}),o.attachTemplatePortal(new rt(e,null,d,s))}else{const s=this._createInjector(a,i,o,this._injector),d=o.attachComponentPortal(new X(e,a.viewContainerRef,s,a.componentFactoryResolver));i.componentRef=d,i.componentInstance=d.instance}}_createInjector(e,i,o,a){const s=e.injector||e.viewContainerRef?.injector,d=[{provide:re,useValue:e.data},{provide:W,useValue:i}];return e.providers&&("function"==typeof e.providers?d.push(...e.providers(i,e,o)):d.push(...e.providers)),e.direction&&(!s||!s.get(F.dS,null,{optional:!0}))&&d.push({provide:F.dS,useValue:{value:e.direction,change:(0,bt.of)()}}),t.zZn.create({parent:s||a,providers:d})}_removeOpenDialog(e,i){const o=this.openDialogs.indexOf(e);o>-1&&(this.openDialogs.splice(o,1),this.openDialogs.length||(this._ariaHiddenElements.forEach((a,s)=>{a?s.setAttribute("aria-hidden",a):s.removeAttribute("aria-hidden")}),this._ariaHiddenElements.clear(),i&&this._getAfterAllClosed().next()))}_hideNonDialogContentFromAssistiveTechnology(){const e=this._overlayContainer.getContainerElement();if(e.parentElement){const i=e.parentElement.children;for(let o=i.length-1;o>-1;o--){const a=i[o];a!==e&&"SCRIPT"!==a.nodeName&&"STYLE"!==a.nodeName&&!a.hasAttribute("aria-live")&&(this._ariaHiddenElements.set(a,a.getAttribute("aria-hidden")),a.setAttribute("aria-hidden","true"))}}}_getAfterAllClosed(){const e=this._parentDialog;return e?e._getAfterAllClosed():this._afterAllClosedAtThisLevel}static{this.\u0275fac=function(i){return new(i||n)(t.KVO(D),t.KVO(t.zZn),t.KVO(oe,8),t.KVO(n,12),t.KVO(z),t.KVO(xt))}}static{this.\u0275prov=t.jDH({token:n,factory:n.\u0275fac})}}return n})();function J(n,r){let e=n.length;for(;e--;)r(n[e])}let de=(()=>{class n{static{this.\u0275fac=function(i){return new(i||n)}}static{this.\u0275mod=t.$C({type:n})}static{this.\u0275inj=t.G2t({providers:[kt,ae],imports:[gt,V,w.Pd,V]})}}return n})();const le=new t.nKC("mat-sanity-checks",{providedIn:"root",factory:function ce(){return!0}});let O=(()=>{class n{constructor(e,i,o){this._sanityChecks=i,this._document=o,this._hasDoneGlobalChecks=!1,e._applyBodyHighContrastModeCssClasses(),this._hasDoneGlobalChecks||(this._hasDoneGlobalChecks=!0)}_checkIsEnabled(e){return!(0,y.v8)()&&("boolean"==typeof this._sanityChecks?this._sanityChecks:!!this._sanityChecks[e])}static{this.\u0275fac=function(i){return new(i||n)(t.KVO(w.Q_),t.KVO(le,8),t.KVO(p.qQ))}}static{this.\u0275mod=t.$C({type:n})}static{this.\u0275inj=t.G2t({imports:[F.jI,F.jI]})}}return n})();const wt=(0,y.BQ)({passive:!0,capture:!0});class pe{constructor(){this._events=new Map,this._delegateEventHandler=r=>{const e=(0,y.Fb)(r);e&&this._events.get(r.type)?.forEach((i,o)=>{(o===e||o.contains(e))&&i.forEach(a=>a.handleEvent(r))})}}addHandler(r,e,i,o){const a=this._events.get(e);if(a){const s=a.get(i);s?s.add(o):a.set(i,new Set([o]))}else this._events.set(e,new Map([[i,new Set([o])]])),r.runOutsideAngular(()=>{document.addEventListener(e,this._delegateEventHandler,wt)})}removeHandler(r,e,i){const o=this._events.get(r);if(!o)return;const a=o.get(e);a&&(a.delete(i),0===a.size&&o.delete(e),0===o.size&&(this._events.delete(r),document.removeEventListener(r,this._delegateEventHandler,wt)))}}class Q{static{this._eventManager=new pe}constructor(r,e,i,o){this._target=r,this._ngZone=e,this._platform=o,this._isPointerDown=!1,this._activeRipples=new Map,this._pointerUpEventsRegistered=!1,o.isBrowser&&(this._containerElement=(0,f.i8)(i))}fadeInRipple(r,e,i={}){const o=this._containerRect=this._containerRect||this._containerElement.getBoundingClientRect(),a={...Ct,...i.animation},s=i.radius||function ge(n,r,e){const i=Math.max(Math.abs(n-e.left),Math.abs(n-e.right)),o=Math.max(Math.abs(r-e.top),Math.abs(r-e.bottom));return Math.sqrt(i*i+o*o)}(r,e,o),d=r-o.left,l=e-o.top,m=a.enterDuration,u=document.createElement("div");u.classList.add("mat-ripple-element"),u.style.left=d-s+"px",u.style.top=l-s+"px",u.style.height=2*s+"px",u.style.width=2*s+"px",null!=i.color&&(u.style.backgroundColor=i.color),u.style.transitionDuration=`${m}ms`,this._containerElement.appendChild(u);const h=window.getComputedStyle(u),x=h.transitionDuration,k="none"===h.transitionProperty||"0s"===x||"0s, 0s"===x||0===o.width&&0===o.height,_=new me(this,u,i,k);u.style.transform="scale3d(1, 1, 1)",_.state=0,i.persistent||(this._mostRecentTransientRipple=_);return!k&&(m||a.exitDuration)&&this._ngZone.runOutsideAngular(()=>{const Lt=()=>this._finishRippleTransition(_),Bt=()=>this._destroyRipple(_);u.addEventListener("transitionend",Lt),u.addEventListener("transitioncancel",Bt)}),this._activeRipples.set(_,null),(k||!m)&&this._finishRippleTransition(_),_}fadeOutRipple(r){if(2===r.state||3===r.state)return;const e=r.element,i={...Ct,...r.config.animation};e.style.transitionDuration=`${i.exitDuration}ms`,e.style.opacity="0",r.state=2,(r._animationForciblyDisabledThroughCss||!i.exitDuration)&&this._finishRippleTransition(r)}fadeOutAll(){this._getActiveRipples().forEach(r=>r.fadeOut())}fadeOutAllNonPersistent(){this._getActiveRipples().forEach(r=>{r.config.persistent||r.fadeOut()})}setupTriggerEvents(r){const e=(0,f.i8)(r);!this._platform.isBrowser||!e||e===this._triggerElement||(this._removeTriggerEvents(),this._triggerElement=e,Mt.forEach(i=>{Q._eventManager.addHandler(this._ngZone,i,e,this)}))}handleEvent(r){"mousedown"===r.type?this._onMousedown(r):"touchstart"===r.type?this._onTouchStart(r):this._onPointerUp(),this._pointerUpEventsRegistered||(this._ngZone.runOutsideAngular(()=>{Ft.forEach(e=>{this._triggerElement.addEventListener(e,this,Et)})}),this._pointerUpEventsRegistered=!0)}_finishRippleTransition(r){0===r.state?this._startFadeOutTransition(r):2===r.state&&this._destroyRipple(r)}_startFadeOutTransition(r){const e=r===this._mostRecentTransientRipple,{persistent:i}=r.config;r.state=1,!i&&(!e||!this._isPointerDown)&&r.fadeOut()}_destroyRipple(r){const e=this._activeRipples.get(r)??null;this._activeRipples.delete(r),this._activeRipples.size||(this._containerRect=null),r===this._mostRecentTransientRipple&&(this._mostRecentTransientRipple=null),r.state=3,null!==e&&(r.element.removeEventListener("transitionend",e.onTransitionEnd),r.element.removeEventListener("transitioncancel",e.onTransitionCancel)),r.element.remove()}_onMousedown(r){const e=(0,w._G)(r),i=this._lastTouchStartEvent&&Date.now()<this._lastTouchStartEvent+800;!this._target.rippleDisabled&&!e&&!i&&(this._isPointerDown=!0,this.fadeInRipple(r.clientX,r.clientY,this._target.rippleConfig))}_onTouchStart(r){if(!this._target.rippleDisabled&&!(0,w.w6)(r)){this._lastTouchStartEvent=Date.now(),this._isPointerDown=!0;const e=r.changedTouches;if(e)for(let i=0;i<e.length;i++)this.fadeInRipple(e[i].clientX,e[i].clientY,this._target.rippleConfig)}}_onPointerUp(){this._isPointerDown&&(this._isPointerDown=!1,this._getActiveRipples().forEach(r=>{!r.config.persistent&&(1===r.state||r.config.terminateOnPointerUp&&0===r.state)&&r.fadeOut()}))}_getActiveRipples(){return Array.from(this._activeRipples.keys())}_removeTriggerEvents(){const r=this._triggerElement;r&&(Mt.forEach(e=>Q._eventManager.removeHandler(e,r,this)),this._pointerUpEventsRegistered&&Ft.forEach(e=>r.removeEventListener(e,this,Et)))}}let fe=(()=>{class n{static{this.\u0275fac=function(i){return new(i||n)}}static{this.\u0275mod=t.$C({type:n})}static{this.\u0275inj=t.G2t({imports:[O,O]})}}return n})();function be(n,r){}g(9969);class Y{constructor(){this.role="dialog",this.panelClass="",this.hasBackdrop=!0,this.backdropClass="",this.disableClose=!1,this.width="",this.height="",this.maxWidth="80vw",this.data=null,this.ariaDescribedBy=null,this.ariaLabelledBy=null,this.ariaLabel=null,this.ariaModal=!0,this.autoFocus="first-tabbable",this.restoreFocus=!0,this.delayFocusTrap=!0,this.closeOnNavigation=!0}}const tt="mdc-dialog--open",Dt="mdc-dialog--opening",Ot="mdc-dialog--closing";let xe=(()=>{class n extends vt{constructor(e,i,o,a,s,d,l,m){super(e,i,o,a,s,d,l,m),this._animationStateChanged=new t.bkB}_captureInitialFocus(){this._config.delayFocusTrap||this._trapFocus()}_openAnimationDone(e){this._config.delayFocusTrap&&this._trapFocus(),this._animationStateChanged.next({state:"opened",totalTime:e})}static{this.\u0275fac=function(i){return new(i||n)(t.rXU(t.aKT),t.rXU(w.GX),t.rXU(p.qQ,8),t.rXU(Y),t.rXU(w.Z7),t.rXU(t.SKi),t.rXU(T),t.rXU(w.FN))}}static{this.\u0275cmp=t.VBU({type:n,selectors:[["ng-component"]],features:[t.Vt3],decls:0,vars:0,template:function(i,o){},encapsulation:2})}}return n})();const Pt="--mat-dialog-transition-duration";function Rt(n){return null==n?null:"number"==typeof n?n:n.endsWith("ms")?(0,f.OE)(n.substring(0,n.length-2)):n.endsWith("s")?1e3*(0,f.OE)(n.substring(0,n.length-1)):"0"===n?0:null}let ke=(()=>{class n extends xe{constructor(e,i,o,a,s,d,l,m,u){super(e,i,o,a,s,d,l,u),this._animationMode=m,this._animationsEnabled="NoopAnimations"!==this._animationMode,this._hostElement=this._elementRef.nativeElement,this._enterAnimationDuration=this._animationsEnabled?Rt(this._config.enterAnimationDuration)??150:0,this._exitAnimationDuration=this._animationsEnabled?Rt(this._config.exitAnimationDuration)??75:0,this._animationTimer=null,this._finishDialogOpen=()=>{this._clearAnimationClasses(),this._openAnimationDone(this._enterAnimationDuration)},this._finishDialogClose=()=>{this._clearAnimationClasses(),this._animationStateChanged.emit({state:"closed",totalTime:this._exitAnimationDuration})}}_contentAttached(){super._contentAttached(),this._startOpenAnimation()}ngOnDestroy(){super.ngOnDestroy(),null!==this._animationTimer&&clearTimeout(this._animationTimer)}_startOpenAnimation(){this._animationStateChanged.emit({state:"opening",totalTime:this._enterAnimationDuration}),this._animationsEnabled?(this._hostElement.style.setProperty(Pt,`${this._enterAnimationDuration}ms`),this._requestAnimationFrame(()=>this._hostElement.classList.add(Dt,tt)),this._waitForAnimationToComplete(this._enterAnimationDuration,this._finishDialogOpen)):(this._hostElement.classList.add(tt),Promise.resolve().then(()=>this._finishDialogOpen()))}_startExitAnimation(){this._animationStateChanged.emit({state:"closing",totalTime:this._exitAnimationDuration}),this._hostElement.classList.remove(tt),this._animationsEnabled?(this._hostElement.style.setProperty(Pt,`${this._exitAnimationDuration}ms`),this._requestAnimationFrame(()=>this._hostElement.classList.add(Ot)),this._waitForAnimationToComplete(this._exitAnimationDuration,this._finishDialogClose)):Promise.resolve().then(()=>this._finishDialogClose())}_clearAnimationClasses(){this._hostElement.classList.remove(Dt,Ot)}_waitForAnimationToComplete(e,i){null!==this._animationTimer&&clearTimeout(this._animationTimer),this._animationTimer=setTimeout(i,e)}_requestAnimationFrame(e){this._ngZone.runOutsideAngular(()=>{"function"==typeof requestAnimationFrame?requestAnimationFrame(e):e()})}static{this.\u0275fac=function(i){return new(i||n)(t.rXU(t.aKT),t.rXU(w.GX),t.rXU(p.qQ,8),t.rXU(Y),t.rXU(w.Z7),t.rXU(t.SKi),t.rXU(T),t.rXU(t.bc$,8),t.rXU(w.FN))}}static{this.\u0275cmp=t.VBU({type:n,selectors:[["mat-dialog-container"]],hostAttrs:["tabindex","-1",1,"mat-mdc-dialog-container","mdc-dialog"],hostVars:8,hostBindings:function(i,o){2&i&&(t.Mr5("id",o._config.id),t.BMQ("aria-modal",o._config.ariaModal)("role",o._config.role)("aria-labelledby",o._config.ariaLabel?null:o._ariaLabelledByQueue[0])("aria-label",o._config.ariaLabel)("aria-describedby",o._config.ariaDescribedBy||null),t.AVh("_mat-animation-noopable",!o._animationsEnabled))},features:[t.Vt3],decls:3,vars:0,consts:[[1,"mdc-dialog__container"],[1,"mat-mdc-dialog-surface","mdc-dialog__surface"],["cdkPortalOutlet",""]],template:function(i,o){1&i&&(t.j41(0,"div",0)(1,"div",1),t.DNE(2,be,0,0,"ng-template",2),t.k0s()())},dependencies:[Z],styles:['.mdc-elevation-overlay{position:absolute;border-radius:inherit;pointer-events:none;opacity:var(--mdc-elevation-overlay-opacity, 0);transition:opacity 280ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-dialog,.mdc-dialog__scrim{position:fixed;top:0;left:0;align-items:center;justify-content:center;box-sizing:border-box;width:100%;height:100%}.mdc-dialog{display:none;z-index:var(--mdc-dialog-z-index, 7)}.mdc-dialog .mdc-dialog__content{padding:20px 24px 20px 24px}.mdc-dialog .mdc-dialog__surface{min-width:280px}@media(max-width: 592px){.mdc-dialog .mdc-dialog__surface{max-width:calc(100vw - 32px)}}@media(min-width: 592px){.mdc-dialog .mdc-dialog__surface{max-width:560px}}.mdc-dialog .mdc-dialog__surface{max-height:calc(100% - 32px)}.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{max-width:none}@media(max-width: 960px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{max-height:560px;width:560px}.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface .mdc-dialog__close{right:-12px}}@media(max-width: 720px)and (max-width: 672px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{width:calc(100vw - 112px)}}@media(max-width: 720px)and (min-width: 672px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{width:560px}}@media(max-width: 720px)and (max-height: 720px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{max-height:calc(100vh - 160px)}}@media(max-width: 720px)and (min-height: 720px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{max-height:560px}}@media(max-width: 720px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface .mdc-dialog__close{right:-12px}}@media(max-width: 720px)and (max-height: 400px),(max-width: 600px),(min-width: 720px)and (max-height: 400px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{height:100%;max-height:100vh;max-width:100vw;width:100vw;border-radius:0}.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface .mdc-dialog__close{order:-1;left:-12px}.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface .mdc-dialog__header{padding:0 16px 9px;justify-content:flex-start}.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface .mdc-dialog__title{margin-left:calc(16px - 2 * 12px)}}@media(min-width: 960px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{width:calc(100vw - 400px)}.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface .mdc-dialog__close{right:-12px}}.mdc-dialog.mdc-dialog__scrim--hidden .mdc-dialog__scrim{opacity:0}.mdc-dialog__scrim{opacity:0;z-index:-1}.mdc-dialog__container{display:flex;flex-direction:row;align-items:center;justify-content:space-around;box-sizing:border-box;height:100%;transform:scale(0.8);opacity:0;pointer-events:none}.mdc-dialog__surface{position:relative;display:flex;flex-direction:column;flex-grow:0;flex-shrink:0;box-sizing:border-box;max-width:100%;max-height:100%;pointer-events:auto;overflow-y:auto;outline:0}.mdc-dialog__surface .mdc-elevation-overlay{width:100%;height:100%;top:0;left:0}[dir=rtl] .mdc-dialog__surface,.mdc-dialog__surface[dir=rtl]{text-align:right}@media screen and (forced-colors: active),(-ms-high-contrast: active){.mdc-dialog__surface{outline:2px solid windowText}}.mdc-dialog__surface::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:2px solid rgba(0,0,0,0);border-radius:inherit;content:"";pointer-events:none}@media screen and (forced-colors: active){.mdc-dialog__surface::before{border-color:CanvasText}}@media screen and (-ms-high-contrast: active),screen and (-ms-high-contrast: none){.mdc-dialog__surface::before{content:none}}.mdc-dialog__title{display:block;margin-top:0;position:relative;flex-shrink:0;box-sizing:border-box;margin:0 0 1px;padding:0 24px 9px}.mdc-dialog__title::before{display:inline-block;width:0;height:40px;content:"";vertical-align:0}[dir=rtl] .mdc-dialog__title,.mdc-dialog__title[dir=rtl]{text-align:right}.mdc-dialog--scrollable .mdc-dialog__title{margin-bottom:1px;padding-bottom:15px}.mdc-dialog--fullscreen .mdc-dialog__header{align-items:baseline;border-bottom:1px solid rgba(0,0,0,0);display:inline-flex;justify-content:space-between;padding:0 24px 9px;z-index:1}@media screen and (forced-colors: active){.mdc-dialog--fullscreen .mdc-dialog__header{border-bottom-color:CanvasText}}.mdc-dialog--fullscreen .mdc-dialog__header .mdc-dialog__close{right:-12px}.mdc-dialog--fullscreen .mdc-dialog__title{margin-bottom:0;padding:0;border-bottom:0}.mdc-dialog--fullscreen.mdc-dialog--scrollable .mdc-dialog__title{border-bottom:0;margin-bottom:0}.mdc-dialog--fullscreen .mdc-dialog__close{top:5px}.mdc-dialog--fullscreen.mdc-dialog--scrollable .mdc-dialog__actions{border-top:1px solid rgba(0,0,0,0)}@media screen and (forced-colors: active){.mdc-dialog--fullscreen.mdc-dialog--scrollable .mdc-dialog__actions{border-top-color:CanvasText}}.mdc-dialog--fullscreen--titleless .mdc-dialog__close{margin-top:4px}.mdc-dialog--fullscreen--titleless.mdc-dialog--scrollable .mdc-dialog__close{margin-top:0}.mdc-dialog__content{flex-grow:1;box-sizing:border-box;margin:0;overflow:auto}.mdc-dialog__content>:first-child{margin-top:0}.mdc-dialog__content>:last-child{margin-bottom:0}.mdc-dialog__title+.mdc-dialog__content,.mdc-dialog__header+.mdc-dialog__content{padding-top:0}.mdc-dialog--scrollable .mdc-dialog__title+.mdc-dialog__content{padding-top:8px;padding-bottom:8px}.mdc-dialog__content .mdc-deprecated-list:first-child:last-child{padding:6px 0 0}.mdc-dialog--scrollable .mdc-dialog__content .mdc-deprecated-list:first-child:last-child{padding:0}.mdc-dialog__actions{display:flex;position:relative;flex-shrink:0;flex-wrap:wrap;align-items:center;justify-content:flex-end;box-sizing:border-box;min-height:52px;margin:0;padding:8px;border-top:1px solid rgba(0,0,0,0)}@media screen and (forced-colors: active){.mdc-dialog__actions{border-top-color:CanvasText}}.mdc-dialog--stacked .mdc-dialog__actions{flex-direction:column;align-items:flex-end}.mdc-dialog__button{margin-left:8px;margin-right:0;max-width:100%;text-align:right}[dir=rtl] .mdc-dialog__button,.mdc-dialog__button[dir=rtl]{margin-left:0;margin-right:8px}.mdc-dialog__button:first-child{margin-left:0;margin-right:0}[dir=rtl] .mdc-dialog__button:first-child,.mdc-dialog__button:first-child[dir=rtl]{margin-left:0;margin-right:0}[dir=rtl] .mdc-dialog__button,.mdc-dialog__button[dir=rtl]{text-align:left}.mdc-dialog--stacked .mdc-dialog__button:not(:first-child){margin-top:12px}.mdc-dialog--open,.mdc-dialog--opening,.mdc-dialog--closing{display:flex}.mdc-dialog--opening .mdc-dialog__scrim{transition:opacity 150ms linear}.mdc-dialog--opening .mdc-dialog__container{transition:opacity 75ms linear,transform 150ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mdc-dialog--closing .mdc-dialog__scrim,.mdc-dialog--closing .mdc-dialog__container{transition:opacity 75ms linear}.mdc-dialog--closing .mdc-dialog__container{transform:none}.mdc-dialog--open .mdc-dialog__scrim{opacity:1}.mdc-dialog--open .mdc-dialog__container{transform:none;opacity:1}.mdc-dialog--open.mdc-dialog__surface-scrim--shown .mdc-dialog__surface-scrim{opacity:1}.mdc-dialog--open.mdc-dialog__surface-scrim--hiding .mdc-dialog__surface-scrim{transition:opacity 75ms linear}.mdc-dialog--open.mdc-dialog__surface-scrim--showing .mdc-dialog__surface-scrim{transition:opacity 150ms linear}.mdc-dialog__surface-scrim{display:none;opacity:0;position:absolute;width:100%;height:100%;z-index:1}.mdc-dialog__surface-scrim--shown .mdc-dialog__surface-scrim,.mdc-dialog__surface-scrim--showing .mdc-dialog__surface-scrim,.mdc-dialog__surface-scrim--hiding .mdc-dialog__surface-scrim{display:block}.mdc-dialog-scroll-lock{overflow:hidden}.mdc-dialog--no-content-padding .mdc-dialog__content{padding:0}.mdc-dialog--sheet .mdc-dialog__container .mdc-dialog__close{right:12px;top:9px;position:absolute;z-index:1}.mdc-dialog__scrim--removed{pointer-events:none}.mdc-dialog__scrim--removed .mdc-dialog__scrim,.mdc-dialog__scrim--removed .mdc-dialog__surface-scrim{display:none}.mat-mdc-dialog-content{max-height:65vh}.mat-mdc-dialog-container{position:static;display:block}.mat-mdc-dialog-container,.mat-mdc-dialog-container .mdc-dialog__container,.mat-mdc-dialog-container .mdc-dialog__surface{max-height:inherit;min-height:inherit;min-width:inherit;max-width:inherit}.mat-mdc-dialog-container .mdc-dialog__surface{display:block;width:100%;height:100%}.mat-mdc-dialog-container{--mdc-dialog-container-elevation-shadow:0px 11px 15px -7px rgba(0, 0, 0, 0.2), 0px 24px 38px 3px rgba(0, 0, 0, 0.14), 0px 9px 46px 8px rgba(0, 0, 0, 0.12);--mdc-dialog-container-shadow-color:#000;--mdc-dialog-container-shape:4px;--mdc-dialog-container-elevation: var(--mdc-dialog-container-elevation-shadow);outline:0}.mat-mdc-dialog-container .mdc-dialog__surface{background-color:var(--mdc-dialog-container-color, white)}.mat-mdc-dialog-container .mdc-dialog__surface{box-shadow:var(--mdc-dialog-container-elevation, 0px 11px 15px -7px rgba(0, 0, 0, 0.2), 0px 24px 38px 3px rgba(0, 0, 0, 0.14), 0px 9px 46px 8px rgba(0, 0, 0, 0.12))}.mat-mdc-dialog-container .mdc-dialog__surface{border-radius:var(--mdc-dialog-container-shape, 4px)}.mat-mdc-dialog-container .mdc-dialog__title{font-family:var(--mdc-dialog-subhead-font, Roboto, sans-serif);line-height:var(--mdc-dialog-subhead-line-height, 1.5rem);font-size:var(--mdc-dialog-subhead-size, 1rem);font-weight:var(--mdc-dialog-subhead-weight, 400);letter-spacing:var(--mdc-dialog-subhead-tracking, 0.03125em)}.mat-mdc-dialog-container .mdc-dialog__title{color:var(--mdc-dialog-subhead-color, rgba(0, 0, 0, 0.87))}.mat-mdc-dialog-container .mdc-dialog__content{font-family:var(--mdc-dialog-supporting-text-font, Roboto, sans-serif);line-height:var(--mdc-dialog-supporting-text-line-height, 1.5rem);font-size:var(--mdc-dialog-supporting-text-size, 1rem);font-weight:var(--mdc-dialog-supporting-text-weight, 400);letter-spacing:var(--mdc-dialog-supporting-text-tracking, 0.03125em)}.mat-mdc-dialog-container .mdc-dialog__content{color:var(--mdc-dialog-supporting-text-color, rgba(0, 0, 0, 0.6))}.mat-mdc-dialog-container .mdc-dialog__container{transition-duration:var(--mat-dialog-transition-duration, 0ms)}.mat-mdc-dialog-container._mat-animation-noopable .mdc-dialog__container{transition:none}.mat-mdc-dialog-content{display:block}.mat-mdc-dialog-actions{justify-content:start}.mat-mdc-dialog-actions.mat-mdc-dialog-actions-align-center,.mat-mdc-dialog-actions[align=center]{justify-content:center}.mat-mdc-dialog-actions.mat-mdc-dialog-actions-align-end,.mat-mdc-dialog-actions[align=end]{justify-content:flex-end}.mat-mdc-dialog-actions .mat-button-base+.mat-button-base,.mat-mdc-dialog-actions .mat-mdc-button-base+.mat-mdc-button-base{margin-left:8px}[dir=rtl] .mat-mdc-dialog-actions .mat-button-base+.mat-button-base,[dir=rtl] .mat-mdc-dialog-actions .mat-mdc-button-base+.mat-mdc-button-base{margin-left:0;margin-right:8px}'],encapsulation:2})}}return n})();class ye{constructor(r,e,i){this._ref=r,this._containerInstance=i,this._afterOpened=new j.B,this._beforeClosed=new j.B,this._state=0,this.disableClose=e.disableClose,this.id=r.id,i._animationStateChanged.pipe((0,I.p)(o=>"opened"===o.state),(0,N.s)(1)).subscribe(()=>{this._afterOpened.next(),this._afterOpened.complete()}),i._animationStateChanged.pipe((0,I.p)(o=>"closed"===o.state),(0,N.s)(1)).subscribe(()=>{clearTimeout(this._closeFallbackTimeout),this._finishDialogClose()}),r.overlayRef.detachments().subscribe(()=>{this._beforeClosed.next(this._result),this._beforeClosed.complete(),this._finishDialogClose()}),(0,nt.h)(this.backdropClick(),this.keydownEvents().pipe((0,I.p)(o=>o.keyCode===$._f&&!this.disableClose&&!(0,$.rp)(o)))).subscribe(o=>{this.disableClose||(o.preventDefault(),function je(n,r,e){n._closeInteractionType=r,n.close(e)}(this,"keydown"===o.type?"keyboard":"mouse"))})}close(r){this._result=r,this._containerInstance._animationStateChanged.pipe((0,I.p)(e=>"closing"===e.state),(0,N.s)(1)).subscribe(e=>{this._beforeClosed.next(r),this._beforeClosed.complete(),this._ref.overlayRef.detachBackdrop(),this._closeFallbackTimeout=setTimeout(()=>this._finishDialogClose(),e.totalTime+100)}),this._state=1,this._containerInstance._startExitAnimation()}afterOpened(){return this._afterOpened}afterClosed(){return this._ref.closed}beforeClosed(){return this._beforeClosed}backdropClick(){return this._ref.backdropClick}keydownEvents(){return this._ref.keydownEvents}updatePosition(r){let e=this._ref.config.positionStrategy;return r&&(r.left||r.right)?r.left?e.left(r.left):e.right(r.right):e.centerHorizontally(),r&&(r.top||r.bottom)?r.top?e.top(r.top):e.bottom(r.bottom):e.centerVertically(),this._ref.updatePosition(),this}updateSize(r="",e=""){return this._ref.updateSize(r,e),this}addPanelClass(r){return this._ref.addPanelClass(r),this}removePanelClass(r){return this._ref.removePanelClass(r),this}getState(){return this._state}_finishDialogClose(){this._state=2,this._ref.close(this._result,{focusOrigin:this._closeInteractionType}),this.componentInstance=null}}const we=new t.nKC("MatMdcDialogData"),Ce=new t.nKC("mat-mdc-dialog-default-options"),St=new t.nKC("mat-mdc-dialog-scroll-strategy"),Me={provide:St,deps:[D],useFactory:function Ee(n){return()=>n.scrollStrategies.block()}};let Fe=0,De=(()=>{class n{get openDialogs(){return this._parentDialog?this._parentDialog.openDialogs:this._openDialogsAtThisLevel}get afterOpened(){return this._parentDialog?this._parentDialog.afterOpened:this._afterOpenedAtThisLevel}_getAfterAllClosed(){const e=this._parentDialog;return e?e._getAfterAllClosed():this._afterAllClosedAtThisLevel}constructor(e,i,o,a,s,d,l,m,u,h){this._overlay=e,this._defaultOptions=o,this._parentDialog=a,this._dialogRefConstructor=l,this._dialogContainerType=m,this._dialogDataToken=u,this._openDialogsAtThisLevel=[],this._afterAllClosedAtThisLevel=new j.B,this._afterOpenedAtThisLevel=new j.B,this._idPrefix="mat-dialog-",this.dialogConfigClass=Y,this.afterAllClosed=(0,ft.v)(()=>this.openDialogs.length?this._getAfterAllClosed():this._getAfterAllClosed().pipe((0,_t.Z)(void 0))),this._scrollStrategy=d,this._dialog=i.get(kt)}open(e,i){let o;(i={...this._defaultOptions||new Y,...i}).id=i.id||`${this._idPrefix}${Fe++}`,i.scrollStrategy=i.scrollStrategy||this._scrollStrategy();const a=this._dialog.open(e,{...i,positionStrategy:this._overlay.position().global().centerHorizontally().centerVertically(),disableClose:!0,closeOnDestroy:!1,closeOnOverlayDetachments:!1,container:{type:this._dialogContainerType,providers:()=>[{provide:this.dialogConfigClass,useValue:i},{provide:G,useValue:i}]},templateContext:()=>({dialogRef:o}),providers:(s,d,l)=>(o=new this._dialogRefConstructor(s,i,l),o.updatePosition(i?.position),[{provide:this._dialogContainerType,useValue:l},{provide:this._dialogDataToken,useValue:d.data},{provide:this._dialogRefConstructor,useValue:o}])});return o.componentRef=a.componentRef,o.componentInstance=a.componentInstance,this.openDialogs.push(o),this.afterOpened.next(o),o.afterClosed().subscribe(()=>{const s=this.openDialogs.indexOf(o);s>-1&&(this.openDialogs.splice(s,1),this.openDialogs.length||this._getAfterAllClosed().next())}),o}closeAll(){this._closeDialogs(this.openDialogs)}getDialogById(e){return this.openDialogs.find(i=>i.id===e)}ngOnDestroy(){this._closeDialogs(this._openDialogsAtThisLevel),this._afterAllClosedAtThisLevel.complete(),this._afterOpenedAtThisLevel.complete()}_closeDialogs(e){let i=e.length;for(;i--;)e[i].close()}static{this.\u0275fac=function(i){t.QTQ()}}static{this.\u0275prov=t.jDH({token:n,factory:n.\u0275fac})}}return n})(),It=(()=>{class n extends De{constructor(e,i,o,a,s,d,l,m){super(e,i,a,d,l,s,ye,ke,we,m),this._idPrefix="mat-mdc-dialog-"}static{this.\u0275fac=function(i){return new(i||n)(t.KVO(D),t.KVO(t.zZn),t.KVO(p.aZ,8),t.KVO(Ce,8),t.KVO(St),t.KVO(n,12),t.KVO(z),t.KVO(t.bc$,8))}}static{this.\u0275prov=t.jDH({token:n,factory:n.\u0275fac})}}return n})(),Oe=(()=>{class n{static{this.\u0275fac=function(i){return new(i||n)}}static{this.\u0275mod=t.$C({type:n})}static{this.\u0275inj=t.G2t({providers:[It,Me],imports:[de,gt,V,O,O]})}}return n})();var Pe=g(8490);const Re=["confirmDialog"];function Se(n,r){if(1&n&&(t.j41(0,"div",86)(1,"div",87)(2,"div",88),t.qSk(),t.j41(3,"svg",61),t.nrm(4,"path",89),t.k0s()(),t.joV(),t.j41(5,"span",90),t.EFF(6),t.k0s()(),t.j41(7,"a",91)(8,"div",60),t.qSk(),t.j41(9,"svg",92),t.nrm(10,"path",93),t.k0s(),t.joV(),t.j41(11,"span"),t.EFF(12,"T\xe9l\xe9charger"),t.k0s()()()()),2&n){const e=r.$implicit,i=t.XpG(4);t.R7$(6),t.SpI(" ",i.getFileName(e)," "),t.R7$(1),t.Y8G("href",i.getFileUrl(e),t.B4B)("download",i.getFileName(e))}}function Ie(n,r){if(1&n&&(t.j41(0,"div",72)(1,"div",73),t.qSk(),t.j41(2,"svg",61),t.nrm(3,"path",83),t.k0s(),t.joV(),t.j41(4,"h4",75),t.EFF(5),t.k0s()(),t.j41(6,"div",84),t.DNE(7,Se,13,3,"div",85),t.k0s()()),2&n){const e=t.XpG().$implicit;t.R7$(5),t.SpI(" Fichiers (",e.fichiers.length,") "),t.R7$(2),t.Y8G("ngForOf",e.fichiers)}}function Te(n,r){1&n&&(t.j41(0,"div",72)(1,"div",73),t.qSk(),t.j41(2,"svg",61),t.nrm(3,"path",83),t.k0s(),t.joV(),t.j41(4,"h4",75),t.EFF(5," Fichiers "),t.k0s()(),t.j41(6,"div",94)(7,"div",95),t.qSk(),t.j41(8,"svg",96),t.nrm(9,"path",83),t.k0s()(),t.joV(),t.j41(10,"p",64),t.EFF(11,"Aucun fichier joint"),t.k0s()()())}const Ae=function(n){return["/admin/projects/editProjet",n]},Le=function(n){return["/admin/projects/details",n]},Be=function(){return["/admin/projects/rendus"]},Ne=function(n){return{projetId:n}};function Ve(n,r){if(1&n){const e=t.RV6();t.j41(0,"div",54)(1,"div",55)(2,"div",56)(3,"div",57)(4,"h3",58),t.EFF(5),t.k0s(),t.j41(6,"div",59)(7,"div",60),t.qSk(),t.j41(8,"svg",61),t.nrm(9,"path",36),t.k0s(),t.joV(),t.j41(10,"span",62),t.EFF(11),t.k0s()(),t.j41(12,"div",60),t.qSk(),t.j41(13,"svg",63),t.nrm(14,"path",30),t.k0s(),t.joV(),t.j41(15,"span",64),t.EFF(16),t.nI1(17,"date"),t.k0s()()()(),t.j41(18,"div",65)(19,"a",66),t.qSk(),t.j41(20,"svg",67),t.nrm(21,"path",68),t.k0s()(),t.joV(),t.j41(22,"button",69),t.bIt("click",function(){const a=t.eBV(e).$implicit,s=t.XpG(2);return t.Njj(a._id&&s.openDeleteDialog(a._id))}),t.qSk(),t.j41(23,"svg",67),t.nrm(24,"path",70),t.k0s()()()()(),t.joV(),t.j41(25,"div",71)(26,"div",72)(27,"div",73),t.qSk(),t.j41(28,"svg",61),t.nrm(29,"path",74),t.k0s(),t.joV(),t.j41(30,"h4",75),t.EFF(31," Description "),t.k0s()(),t.j41(32,"p",76),t.EFF(33),t.k0s()(),t.DNE(34,Ie,8,2,"div",77),t.DNE(35,Te,12,0,"div",77),t.j41(36,"div",78)(37,"a",79)(38,"div",46),t.qSk(),t.j41(39,"svg",80),t.nrm(40,"path",74),t.k0s(),t.joV(),t.j41(41,"span"),t.EFF(42,"Voir d\xe9tails"),t.k0s()()(),t.j41(43,"a",81)(44,"div",46),t.qSk(),t.j41(45,"svg",80),t.nrm(46,"path",82),t.k0s(),t.joV(),t.j41(47,"span"),t.EFF(48,"Voir rendus"),t.k0s()()()()()()}if(2&n){const e=r.$implicit;t.R7$(5),t.SpI(" ",e.titre," "),t.R7$(6),t.SpI(" ",e.groupe||"Tous"," "),t.R7$(5),t.SpI(" ",t.i5U(17,10,e.dateLimite,"dd/MM/yyyy")," "),t.R7$(3),t.Y8G("routerLink",t.eq3(13,Ae,e._id)),t.R7$(14),t.SpI(" ",e.description||"Aucune description fournie"," "),t.R7$(1),t.Y8G("ngIf",e.fichiers&&e.fichiers.length>0),t.R7$(1),t.Y8G("ngIf",!e.fichiers||0===e.fichiers.length),t.R7$(2),t.Y8G("routerLink",t.eq3(15,Le,e._id)),t.R7$(6),t.Y8G("routerLink",t.lJ4(17,Be))("queryParams",t.eq3(18,Ne,e._id))}}function ze(n,r){if(1&n&&(t.j41(0,"div",52),t.DNE(1,Ve,49,20,"div",53),t.k0s()),2&n){const e=t.XpG();t.R7$(1),t.Y8G("ngForOf",e.projets)}}function $e(n,r){1&n&&(t.j41(0,"div",97)(1,"div",98)(2,"div",99),t.nrm(3,"div",100),t.j41(4,"div",101),t.nrm(5,"div",102),t.k0s()(),t.j41(6,"p",103),t.EFF(7,"Chargement des projets..."),t.k0s(),t.j41(8,"p",104),t.EFF(9,"Veuillez patienter"),t.k0s()()())}function Ge(n,r){1&n&&(t.j41(0,"div",97)(1,"div",98)(2,"div",105),t.qSk(),t.j41(3,"svg",106),t.nrm(4,"path",107),t.k0s()(),t.joV(),t.j41(5,"h3",108),t.EFF(6,"Aucun projet disponible"),t.k0s(),t.j41(7,"p",109),t.EFF(8,"Commencez par cr\xe9er votre premier projet pour organiser vos cours"),t.k0s(),t.j41(9,"a",110),t.qSk(),t.j41(10,"svg",111),t.nrm(11,"path",48),t.k0s(),t.EFF(12," Cr\xe9er un projet "),t.k0s()()())}function Ye(n,r){if(1&n){const e=t.RV6();t.j41(0,"div",112)(1,"div",113)(2,"div",114)(3,"div",115),t.qSk(),t.j41(4,"svg",116),t.nrm(5,"path",117),t.k0s()(),t.joV(),t.j41(6,"h2",108),t.EFF(7),t.k0s(),t.j41(8,"p",10),t.EFF(9),t.k0s()(),t.j41(10,"div",44)(11,"button",118),t.bIt("click",function(){t.eBV(e);const o=t.XpG();return t.Njj(o.onDeleteCancel())}),t.EFF(12," Annuler "),t.k0s(),t.j41(13,"button",119),t.bIt("click",function(){t.eBV(e);const o=t.XpG();return t.Njj(o.onDeleteConfirm())}),t.EFF(14," Supprimer "),t.k0s()()()()}if(2&n){const e=t.XpG();t.R7$(7),t.SpI(" ",e.dialogData.title," "),t.R7$(2),t.JRh(e.dialogData.message)}}let qe=(()=>{class n{constructor(e,i,o,a){this.projetService=e,this.router=i,this.dialog=o,this.authService=a,this.projets=[],this.isLoading=!0,this.isAdmin=!1,this.showDeleteDialog=!1,this.projectIdToDelete=null,this.dialogData={title:"Confirmer la suppression",message:"\xcates-vous s\xfbr de vouloir supprimer ce projet?"}}ngOnInit(){this.loadProjets(),this.checkAdminStatus()}loadProjets(){this.isLoading=!0,this.projetService.getProjets().subscribe({next:e=>{this.projets=e,this.isLoading=!1},error:e=>{this.isLoading=!1,alert("Erreur lors du chargement des projets: "+(e.error?.message||e.message||"Erreur inconnue"))}})}loadProjects(){this.loadProjets()}checkAdminStatus(){this.isAdmin=this.authService.isAdmin()}editProjet(e){e&&this.router.navigate(["/admin/projects/edit",e])}viewProjetDetails(e){e&&this.router.navigate(["/admin/projects/detail",e])}deleteProjet(e){e&&confirm("\xcates-vous s\xfbr de vouloir supprimer ce projet ?")&&this.projetService.deleteProjet(e).subscribe({next:()=>{alert("Projet supprim\xe9 avec succ\xe8s"),this.loadProjets()},error:i=>{alert("Erreur lors de la suppression du projet: "+(i.error?.message||i.message||"Erreur inconnue"))}})}openDeleteDialog(e){e&&(this.showDeleteDialog=!0,this.projectIdToDelete=e)}onDeleteConfirm(){this.projectIdToDelete&&this.projetService.deleteProjet(this.projectIdToDelete).subscribe({next:()=>{alert("Projet supprim\xe9 avec succ\xe8s"),this.loadProjets(),this.showDeleteDialog=!1},error:e=>{alert("Erreur lors de la suppression du projet: "+(e.error?.message||e.message||"Erreur inconnue")),this.showDeleteDialog=!1}})}onDeleteCancel(){this.showDeleteDialog=!1}getFileUrl(e){if(!e)return"";let i=e;if(e.includes("/")||e.includes("\\")){const o=e.split(/[\/\\]/);i=o[o.length-1]}return`${E.c.urlBackend}projets/telecharger/${i}`}getFileName(e){if(!e)return"Fichier";if(e.includes("/")||e.includes("\\")){const i=e.split(/[\/\\]/);return i[i.length-1]}return e}getActiveProjectsCount(){if(!this.projets)return 0;const e=new Date;return this.projets.filter(i=>!i.dateLimite||new Date(i.dateLimite)>=e).length}getExpiredProjectsCount(){if(!this.projets)return 0;const e=new Date;return this.projets.filter(i=>!!i.dateLimite&&new Date(i.dateLimite)<e).length}getUniqueGroupsCount(){return this.projets?new Set(this.projets.map(i=>i.groupe).filter(i=>i&&""!==i.trim())).size:0}getCompletionPercentage(){if(!this.projets||0===this.projets.length)return 0;const e=this.getExpiredProjectsCount();return Math.round(e/this.projets.length*100)}getProjectsByGroup(){if(!this.projets)return{};const e={};return this.projets.forEach(i=>{const o=i.groupe||"Non sp\xe9cifi\xe9";e[o]=(e[o]||0)+1}),e}getRecentProjects(){if(!this.projets)return[];const e=new Date,i=new Date;return i.setMonth(i.getMonth()+1),this.projets.filter(o=>{if(!o.dateLimite)return!1;const a=new Date(o.dateLimite);return a>=e&&a<=i})}getUpcomingDeadlines(){if(!this.projets)return[];const e=new Date,i=new Date;return i.setDate(i.getDate()+7),this.projets.filter(o=>{if(!o.dateLimite)return!1;const a=new Date(o.dateLimite);return a>=e&&a<=i}).sort((o,a)=>{const s=new Date(o.dateLimite),d=new Date(a.dateLimite);return s.getTime()-d.getTime()})}static{this.\u0275fac=function(i){return new(i||n)(t.rXU(R.e),t.rXU(b.Ix),t.rXU(It),t.rXU(Pe.u))}}static{this.\u0275cmp=t.VBU({type:n,selectors:[["app-list-project"]],viewQuery:function(i,o){if(1&i&&t.GBs(Re,5),2&i){let a;t.mGM(a=t.lsd())&&(o.confirmDialog=a.first)}},decls:87,vars:13,consts:[[1,"min-h-screen","bg-gradient-to-br","from-gray-50","via-blue-50","to-indigo-100","dark:from-dark-bg-primary","dark:via-dark-bg-secondary","dark:to-dark-bg-tertiary"],[1,"container","mx-auto","px-4","py-8"],[1,"bg-white/80","dark:bg-dark-bg-secondary/80","backdrop-blur-sm","rounded-2xl","p-8","mb-8","shadow-lg","border","border-gray-200/50","dark:border-dark-bg-tertiary/50"],[1,"flex","flex-col","lg:flex-row","lg:items-center","lg:justify-between"],[1,"mb-6","lg:mb-0"],[1,"flex","items-center","space-x-4","mb-4"],[1,"h-16","w-16","rounded-2xl","bg-gradient-to-br","from-primary","to-primary-dark","dark:from-dark-accent-primary","dark:to-dark-accent-secondary","flex","items-center","justify-center","shadow-lg"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-8","h-8","text-white"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M19 11H5m14-7H3a2 2 0 00-2 2v12a2 2 0 002 2h16a2 2 0 002-2V6a2 2 0 00-2-2z"],[1,"text-3xl","font-bold","text-text-dark","dark:text-dark-text-primary"],[1,"text-text","dark:text-dark-text-secondary"],[1,"grid","grid-cols-2","md:grid-cols-4","gap-4"],[1,"bg-gradient-to-br","from-primary/10","to-primary/5","dark:from-dark-accent-primary/20","dark:to-dark-accent-primary/10","rounded-xl","p-4","border","border-primary/20","dark:border-dark-accent-primary/30"],[1,"flex","items-center","justify-between"],[1,"text-xs","font-medium","text-primary","dark:text-dark-accent-primary","uppercase","tracking-wider"],[1,"text-2xl","font-bold","text-primary","dark:text-dark-accent-primary"],[1,"text-xs","text-text","dark:text-dark-text-secondary","mt-1"],[1,"bg-primary/20","dark:bg-dark-accent-primary/30","p-3","rounded-xl"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-6","h-6","text-primary","dark:text-dark-accent-primary"],[1,"bg-gradient-to-br","from-success/10","to-success/5","dark:from-dark-accent-secondary/20","dark:to-dark-accent-secondary/10","rounded-xl","p-4","border","border-success/20","dark:border-dark-accent-secondary/30"],[1,"text-xs","font-medium","text-success","dark:text-dark-accent-secondary","uppercase","tracking-wider"],[1,"text-2xl","font-bold","text-success","dark:text-dark-accent-secondary"],[1,"bg-success/20","dark:bg-dark-accent-secondary/30","p-3","rounded-xl"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-6","h-6","text-success","dark:text-dark-accent-secondary"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"],[1,"bg-gradient-to-br","from-warning/10","to-warning/5","dark:from-warning/20","dark:to-warning/10","rounded-xl","p-4","border","border-warning/20","dark:border-warning/30"],[1,"text-xs","font-medium","text-warning","dark:text-warning","uppercase","tracking-wider"],[1,"text-2xl","font-bold","text-warning","dark:text-warning"],[1,"bg-warning/20","dark:bg-warning/30","p-3","rounded-xl"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-6","h-6","text-warning","dark:text-warning"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"],[1,"bg-gradient-to-br","from-info/10","to-info/5","dark:from-dark-accent-primary/20","dark:to-dark-accent-primary/10","rounded-xl","p-4","border","border-info/20","dark:border-dark-accent-primary/30"],[1,"text-xs","font-medium","text-info","dark:text-dark-accent-primary","uppercase","tracking-wider"],[1,"text-2xl","font-bold","text-info","dark:text-dark-accent-primary"],[1,"bg-info/20","dark:bg-dark-accent-primary/30","p-3","rounded-xl"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-6","h-6","text-info","dark:text-dark-accent-primary"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"],[1,"mt-6","bg-gray-50","dark:bg-dark-bg-tertiary/50","rounded-xl","p-4"],[1,"flex","items-center","justify-between","mb-2"],[1,"text-sm","font-semibold","text-text-dark","dark:text-dark-text-primary"],[1,"text-xs","text-text","dark:text-dark-text-secondary"],[1,"w-full","bg-gray-200","dark:bg-dark-bg-tertiary","rounded-full","h-2"],[1,"bg-gradient-to-r","from-success","to-primary","dark:from-dark-accent-secondary","dark:to-dark-accent-primary","h-2","rounded-full","transition-all","duration-500"],[1,"flex","justify-between","text-xs","text-text","dark:text-dark-text-secondary","mt-2"],[1,"flex","flex-col","sm:flex-row","gap-3"],["routerLink","/admin/projects/new",1,"group","px-6","py-3","bg-gradient-to-r","from-primary","to-primary-dark","dark:from-dark-accent-primary","dark:to-dark-accent-secondary","text-white","rounded-xl","hover:shadow-lg","hover:scale-105","transition-all","duration-200","font-medium"],[1,"flex","items-center","justify-center","space-x-2"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-5","h-5","group-hover:scale-110","transition-transform"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M12 6v6m0 0v6m0-6h6m-6 0H6"],["class","grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8",4,"ngIf"],["class","text-center py-16",4,"ngIf"],["class","fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm",4,"ngIf"],[1,"grid","grid-cols-1","md:grid-cols-2","lg:grid-cols-3","gap-6","mb-8"],["class","bg-white/80 dark:bg-dark-bg-secondary/80 backdrop-blur-sm rounded-2xl shadow-lg border border-gray-200/50 dark:border-dark-bg-tertiary/50 hover:shadow-xl transition-all duration-300 group overflow-hidden",4,"ngFor","ngForOf"],[1,"bg-white/80","dark:bg-dark-bg-secondary/80","backdrop-blur-sm","rounded-2xl","shadow-lg","border","border-gray-200/50","dark:border-dark-bg-tertiary/50","hover:shadow-xl","transition-all","duration-300","group","overflow-hidden"],[1,"relative","p-6","bg-gradient-to-r","from-primary/5","to-secondary/5","dark:from-dark-accent-primary/10","dark:to-dark-accent-secondary/10"],[1,"flex","items-start","justify-between"],[1,"flex-1","pr-4"],[1,"text-xl","font-bold","text-text-dark","dark:text-dark-text-primary","mb-2","line-clamp-2"],[1,"flex","items-center","space-x-4"],[1,"flex","items-center","space-x-1"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-4","h-4","text-primary","dark:text-dark-accent-primary"],[1,"text-sm","font-medium","text-primary","dark:text-dark-accent-primary"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-4","h-4","text-warning","dark:text-warning"],[1,"text-sm","text-text","dark:text-dark-text-secondary"],[1,"flex","space-x-2","opacity-0","group-hover:opacity-100","transition-all","duration-200"],[1,"p-2","bg-white/80","dark:bg-dark-bg-tertiary/80","backdrop-blur-sm","rounded-lg","text-primary","dark:text-dark-accent-primary","hover:bg-primary","hover:text-white","dark:hover:bg-dark-accent-primary","transition-all","duration-200","shadow-lg",3,"routerLink"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-4","h-4"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"],[1,"p-2","bg-white/80","dark:bg-dark-bg-tertiary/80","backdrop-blur-sm","rounded-lg","text-danger","dark:text-danger-dark","hover:bg-danger","hover:text-white","dark:hover:bg-danger-dark","transition-all","duration-200","shadow-lg",3,"click"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"],[1,"p-6"],[1,"mb-6"],[1,"flex","items-center","space-x-2","mb-3"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"],[1,"text-sm","font-semibold","text-text-dark","dark:text-dark-text-primary","uppercase","tracking-wider"],[1,"text-text","dark:text-dark-text-secondary","text-sm","line-clamp-3","leading-relaxed"],["class","mb-6",4,"ngIf"],[1,"flex","flex-col","sm:flex-row","gap-3","pt-4","border-t","border-gray-100","dark:border-dark-bg-tertiary/50"],[1,"flex-1","group","px-4","py-3","bg-gray-50","dark:bg-dark-bg-tertiary/50","text-text-dark","dark:text-dark-text-primary","hover:bg-primary","hover:text-white","dark:hover:bg-dark-accent-primary","rounded-xl","transition-all","duration-200","font-medium",3,"routerLink"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-4","h-4","group-hover:scale-110","transition-transform"],[1,"flex-1","group","px-4","py-3","bg-gradient-to-r","from-primary","to-primary-dark","dark:from-dark-accent-primary","dark:to-dark-accent-secondary","text-white","rounded-xl","hover:shadow-lg","hover:scale-105","transition-all","duration-200","font-medium",3,"routerLink","queryParams"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"],[1,"space-y-2","bg-gray-50","dark:bg-dark-bg-tertiary/50","rounded-xl","p-4"],["class","flex items-center justify-between p-2 bg-white dark:bg-dark-bg-secondary rounded-lg shadow-sm",4,"ngFor","ngForOf"],[1,"flex","items-center","justify-between","p-2","bg-white","dark:bg-dark-bg-secondary","rounded-lg","shadow-sm"],[1,"flex","items-center","space-x-2","flex-1","min-w-0"],[1,"bg-primary/10","dark:bg-dark-accent-primary/20","p-1.5","rounded-lg"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"],[1,"text-sm","text-text-dark","dark:text-dark-text-primary","truncate"],[1,"ml-2","px-3","py-1.5","bg-primary/10","dark:bg-dark-accent-primary/20","text-primary","dark:text-dark-accent-primary","hover:bg-primary","hover:text-white","dark:hover:bg-dark-accent-primary","rounded-lg","transition-all","duration-200","text-xs","font-medium",3,"href","download"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-3","h-3"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"],[1,"bg-gray-50","dark:bg-dark-bg-tertiary/50","rounded-xl","p-4","text-center"],[1,"bg-gray-200","dark:bg-dark-bg-tertiary","p-3","rounded-lg","inline-flex","items-center","justify-center","mb-2"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-5","h-5","text-gray-400","dark:text-dark-text-secondary"],[1,"text-center","py-16"],[1,"bg-white/80","dark:bg-dark-bg-secondary/80","backdrop-blur-sm","rounded-2xl","p-12","shadow-lg","border","border-gray-200/50","dark:border-dark-bg-tertiary/50","max-w-md","mx-auto"],[1,"relative"],[1,"animate-spin","rounded-full","h-16","w-16","border-4","border-primary/20","border-t-primary","dark:border-dark-accent-primary/20","dark:border-t-dark-accent-primary","mx-auto"],[1,"absolute","inset-0","flex","items-center","justify-center"],[1,"w-8","h-8","bg-gradient-to-br","from-primary","to-primary-dark","dark:from-dark-accent-primary","dark:to-dark-accent-secondary","rounded-full","animate-pulse"],[1,"mt-6","text-text-dark","dark:text-dark-text-primary","font-medium"],[1,"mt-2","text-sm","text-text","dark:text-dark-text-secondary"],[1,"bg-gradient-to-br","from-primary/10","to-secondary/10","dark:from-dark-accent-primary/20","dark:to-dark-accent-secondary/20","rounded-2xl","p-6","mb-6"],["fill","none","viewBox","0 0 24 24","stroke","currentColor",1,"h-16","w-16","mx-auto","text-primary","dark:text-dark-accent-primary"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","1.5","d","M19 11H5m14-7H3a2 2 0 00-2 2v12a2 2 0 002 2h16a2 2 0 002-2V6a2 2 0 00-2-2z"],[1,"text-xl","font-bold","text-text-dark","dark:text-dark-text-primary","mb-2"],[1,"text-text","dark:text-dark-text-secondary","mb-6"],["routerLink","/admin/projects/new",1,"inline-flex","items-center","px-6","py-3","bg-gradient-to-r","from-primary","to-primary-dark","dark:from-dark-accent-primary","dark:to-dark-accent-secondary","text-white","rounded-xl","hover:shadow-lg","hover:scale-105","transition-all","duration-200","font-medium"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-5","h-5","mr-2"],[1,"fixed","inset-0","z-50","flex","items-center","justify-center","bg-black/50","backdrop-blur-sm"],[1,"bg-white/95","dark:bg-dark-bg-secondary/95","backdrop-blur-sm","rounded-2xl","shadow-2xl","w-full","max-w-md","p-8","border","border-gray-200/50","dark:border-dark-bg-tertiary/50","mx-4"],[1,"text-center","mb-6"],[1,"bg-danger/10","dark:bg-danger-dark/20","rounded-full","p-4","w-16","h-16","mx-auto","mb-4"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-8","h-8","text-danger","dark:text-danger-dark","mx-auto"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"],[1,"flex-1","px-6","py-3","bg-gray-100","dark:bg-dark-bg-tertiary","text-text-dark","dark:text-dark-text-primary","hover:bg-gray-200","dark:hover:bg-dark-bg-tertiary/80","rounded-xl","transition-all","duration-200","font-medium",3,"click"],[1,"flex-1","px-6","py-3","bg-gradient-to-r","from-danger","to-danger-dark","dark:from-danger-dark","dark:to-danger","text-white","rounded-xl","hover:shadow-lg","hover:scale-105","transition-all","duration-200","font-medium",3,"click"]],template:function(i,o){1&i&&(t.j41(0,"div",0)(1,"div",1)(2,"div",2)(3,"div",3)(4,"div",4)(5,"div",5)(6,"div",6),t.qSk(),t.j41(7,"svg",7),t.nrm(8,"path",8),t.k0s()(),t.joV(),t.j41(9,"div")(10,"h1",9),t.EFF(11," Gestion des Projets "),t.k0s(),t.j41(12,"p",10),t.EFF(13," Cr\xe9ez, g\xe9rez et suivez vos projets acad\xe9miques "),t.k0s()()(),t.j41(14,"div",11)(15,"div",12)(16,"div",13)(17,"div")(18,"p",14),t.EFF(19,"Total"),t.k0s(),t.j41(20,"p",15),t.EFF(21),t.k0s(),t.j41(22,"p",16),t.EFF(23,"Projets cr\xe9\xe9s"),t.k0s()(),t.j41(24,"div",17),t.qSk(),t.j41(25,"svg",18),t.nrm(26,"path",8),t.k0s()()()(),t.joV(),t.j41(27,"div",19)(28,"div",13)(29,"div")(30,"p",20),t.EFF(31,"Actifs"),t.k0s(),t.j41(32,"p",21),t.EFF(33),t.k0s(),t.j41(34,"p",16),t.EFF(35,"En cours"),t.k0s()(),t.j41(36,"div",22),t.qSk(),t.j41(37,"svg",23),t.nrm(38,"path",24),t.k0s()()()(),t.joV(),t.j41(39,"div",25)(40,"div",13)(41,"div")(42,"p",26),t.EFF(43,"Expir\xe9s"),t.k0s(),t.j41(44,"p",27),t.EFF(45),t.k0s(),t.j41(46,"p",16),t.EFF(47,"Date d\xe9pass\xe9e"),t.k0s()(),t.j41(48,"div",28),t.qSk(),t.j41(49,"svg",29),t.nrm(50,"path",30),t.k0s()()()(),t.joV(),t.j41(51,"div",31)(52,"div",13)(53,"div")(54,"p",32),t.EFF(55,"Groupes"),t.k0s(),t.j41(56,"p",33),t.EFF(57),t.k0s(),t.j41(58,"p",16),t.EFF(59,"Diff\xe9rents"),t.k0s()(),t.j41(60,"div",34),t.qSk(),t.j41(61,"svg",35),t.nrm(62,"path",36),t.k0s()()()()(),t.joV(),t.j41(63,"div",37)(64,"div",38)(65,"h4",39),t.EFF(66,"Progression des projets"),t.k0s(),t.j41(67,"span",40),t.EFF(68),t.k0s()(),t.j41(69,"div",41),t.nrm(70,"div",42),t.k0s(),t.j41(71,"div",43)(72,"span"),t.EFF(73),t.k0s(),t.j41(74,"span"),t.EFF(75),t.k0s()()()(),t.j41(76,"div",44)(77,"a",45)(78,"div",46),t.qSk(),t.j41(79,"svg",47),t.nrm(80,"path",48),t.k0s(),t.joV(),t.j41(81,"span"),t.EFF(82,"Nouveau projet"),t.k0s()()()()()(),t.DNE(83,ze,2,1,"div",49),t.DNE(84,$e,10,0,"div",50),t.DNE(85,Ge,13,0,"div",50),t.DNE(86,Ye,15,2,"div",51),t.k0s()()),2&i&&(t.R7$(21),t.JRh(o.projets.length||0),t.R7$(12),t.JRh(o.getActiveProjectsCount()),t.R7$(12),t.JRh(o.getExpiredProjectsCount()),t.R7$(12),t.JRh(o.getUniqueGroupsCount()),t.R7$(11),t.SpI("",o.getCompletionPercentage(),"% compl\xe9t\xe9s"),t.R7$(2),t.xc7("width",o.getCompletionPercentage(),"%"),t.R7$(3),t.SpI("",o.getActiveProjectsCount()," actifs"),t.R7$(2),t.SpI("",o.getExpiredProjectsCount()," expir\xe9s"),t.R7$(8),t.Y8G("ngIf",!o.isLoading&&o.projets&&o.projets.length>0),t.R7$(1),t.Y8G("ngIf",o.isLoading),t.R7$(1),t.Y8G("ngIf",!(o.isLoading||o.projets&&0!==o.projets.length)),t.R7$(1),t.Y8G("ngIf",o.showDeleteDialog))},dependencies:[p.Sq,p.bT,b.Wk,p.vh],styles:['@keyframes _ngcontent-%COMP%_fadeInUp{0%{opacity:0;transform:translateY(20px)}to{opacity:1;transform:translateY(0)}}@keyframes _ngcontent-%COMP%_slideInRight{0%{opacity:0;transform:translate(30px)}to{opacity:1;transform:translate(0)}}@keyframes _ngcontent-%COMP%_scaleIn{0%{opacity:0;transform:scale(.9)}to{opacity:1;transform:scale(1)}}.glass-card[_ngcontent-%COMP%]{backdrop-filter:blur(10px);-webkit-backdrop-filter:blur(10px);border:1px solid rgba(255,255,255,.2)}.dark[_ngcontent-%COMP%]   .glass-card[_ngcontent-%COMP%]{border:1px solid rgba(255,255,255,.1)}.project-card[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_fadeInUp .6s ease-out;transition:all .3s cubic-bezier(.4,0,.2,1)}.project-card[_ngcontent-%COMP%]:hover{transform:translateY(-8px);box-shadow:0 25px 50px #00000026}.dark[_ngcontent-%COMP%]   .project-card[_ngcontent-%COMP%]:hover{box-shadow:0 25px 50px #0006}.btn-modern[_ngcontent-%COMP%]{position:relative;overflow:hidden;transition:all .3s cubic-bezier(.4,0,.2,1)}.btn-modern[_ngcontent-%COMP%]:before{content:"";position:absolute;top:0;left:-100%;width:100%;height:100%;background:linear-gradient(90deg,transparent,rgba(255,255,255,.2),transparent);transition:left .5s}.btn-modern[_ngcontent-%COMP%]:hover:before{left:100%}.badge-gradient[_ngcontent-%COMP%]{background:linear-gradient(135deg,#4f5fad 0%,#7826b5 100%);transition:all .3s ease}.dark[_ngcontent-%COMP%]   .badge-gradient[_ngcontent-%COMP%]{background:linear-gradient(135deg,#00f7ff 0%,#9d4edd 100%)}.icon-hover[_ngcontent-%COMP%]{transition:transform .2s ease}.icon-hover[_ngcontent-%COMP%]:hover{transform:scale(1.1) rotate(5deg)}.tooltip[_ngcontent-%COMP%]{position:relative}.tooltip[_ngcontent-%COMP%]:after{content:attr(data-tooltip);position:absolute;bottom:100%;left:50%;transform:translate(-50%);background:rgba(0,0,0,.8);color:#fff;padding:.5rem;border-radius:.375rem;font-size:.75rem;white-space:nowrap;opacity:0;pointer-events:none;transition:opacity .3s;z-index:1000}.tooltip[_ngcontent-%COMP%]:hover:after{opacity:1}.loading-pulse[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_pulse 2s infinite}@keyframes _ngcontent-%COMP%_pulse{0%,to{opacity:1}50%{opacity:.5}}.empty-state[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_fadeInUp .8s ease-out}.focus-visible[_ngcontent-%COMP%]:focus{outline:2px solid #4f5fad;outline-offset:2px}.dark[_ngcontent-%COMP%]   .focus-visible[_ngcontent-%COMP%]:focus{outline:2px solid #00f7ff}@media (max-width: 768px){.project-card[_ngcontent-%COMP%]{margin-bottom:1rem}.btn-modern[_ngcontent-%COMP%]{width:100%;justify-content:center}}.floating-actions[_ngcontent-%COMP%]{transition:all .3s cubic-bezier(.4,0,.2,1)}.floating-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{transition:all .2s ease}.floating-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover{transform:scale(1.1)}.file-item[_ngcontent-%COMP%]{transition:all .2s ease}.file-item[_ngcontent-%COMP%]:hover{transform:translate(4px);background-color:#4f5fad0d}.dark[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]:hover{background-color:#00f7ff0d}.dialog-overlay[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_fadeIn .3s ease-out}.dialog-content[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_scaleIn .3s ease-out}@keyframes _ngcontent-%COMP%_fadeIn{0%{opacity:0}to{opacity:1}}.bg-gradient-modern[_ngcontent-%COMP%]{background:linear-gradient(135deg,#667eea 0%,#764ba2 100%)}.dark[_ngcontent-%COMP%]   .bg-gradient-modern[_ngcontent-%COMP%]{background:linear-gradient(135deg,#00f7ff 0%,#9d4edd 100%)}.card-hover-effect[_ngcontent-%COMP%]{transition:all .3s cubic-bezier(.4,0,.2,1)}.card-hover-effect[_ngcontent-%COMP%]:hover{transform:translateY(-4px) scale(1.02);box-shadow:0 20px 40px #0000001a}.dark[_ngcontent-%COMP%]   .card-hover-effect[_ngcontent-%COMP%]:hover{box-shadow:0 20px 40px #0000004d}']})}}return n})();var He=g(9271);function Xe(n,r){1&n&&(t.j41(0,"div",72),t.qSk(),t.j41(1,"svg",5),t.nrm(2,"path",73),t.k0s(),t.joV(),t.j41(3,"span"),t.EFF(4,"Le titre est requis"),t.k0s()())}function Ue(n,r){1&n&&(t.j41(0,"div",72),t.qSk(),t.j41(1,"svg",5),t.nrm(2,"path",73),t.k0s(),t.joV(),t.j41(3,"span"),t.EFF(4,"La description est requise"),t.k0s()())}function Ze(n,r){1&n&&(t.j41(0,"div",72),t.qSk(),t.j41(1,"svg",5),t.nrm(2,"path",73),t.k0s(),t.joV(),t.j41(3,"span"),t.EFF(4,"La date limite est requise"),t.k0s()())}function Ke(n,r){1&n&&(t.qSk(),t.joV(),t.j41(0,"div",72),t.qSk(),t.j41(1,"svg",5),t.nrm(2,"path",73),t.k0s(),t.joV(),t.j41(3,"span"),t.EFF(4,"Le groupe est requis"),t.k0s()())}let We=(()=>{class n{constructor(e,i,o,a){this.fb=e,this.projetService=i,this.router=o,this.authService=a,this.selectedFiles=[],this.isSubmitting=!1,this.projetForm=this.fb.group({titre:["",c.k0.required],description:[""],dateLimite:["",c.k0.required],fichiers:[null],groupe:["",c.k0.required]})}onFileChange(e){const i=e.target;i.files&&(this.selectedFiles=Array.from(i.files))}onSubmit(){if(this.projetForm.invalid)return;this.isSubmitting=!0,console.log("Soumission du formulaire de projet");const e=new FormData;e.append("titre",this.projetForm.value.titre),e.append("description",this.projetForm.value.description||""),e.append("dateLimite",this.projetForm.value.dateLimite),e.append("groupe",this.projetForm.value.groupe);const i=this.authService.getCurrentUserId(),o=this.authService.getCurrentUser(),a=localStorage.getItem("user");let s=i;!s&&o&&(s=o._id||o.id),!s&&a&&(s=JSON.parse(a).id),s?(e.append("professeur",s),this.selectedFiles.forEach(d=>{e.append("fichiers",d)}),console.log("Donn\xe9es du formulaire:",{titre:this.projetForm.value.titre,description:this.projetForm.value.description,dateLimite:this.projetForm.value.dateLimite,groupe:this.projetForm.value.groupe,fichiers:this.selectedFiles.map(d=>d.name)}),this.projetService.addProjet(e).subscribe({next:()=>{console.log("Projet ajout\xe9 avec succ\xe8s"),alert("Projet ajout\xe9 avec succ\xe8s"),this.router.navigate(["/admin/projects"])},error:d=>{console.error("Erreur lors de l'ajout du projet:",d),alert("Erreur lors de l'ajout du projet: "+(d.error?.message||d.message||"Erreur inconnue")),this.isSubmitting=!1},complete:()=>{this.isSubmitting=!1}})):alert("Erreur: Impossible de r\xe9cup\xe9rer l'ID utilisateur. Veuillez vous reconnecter.")}static{this.\u0275fac=function(i){return new(i||n)(t.rXU(c.ok),t.rXU(R.e),t.rXU(b.Ix),t.rXU(He.V))}}static{this.\u0275cmp=t.VBU({type:n,selectors:[["app-add-project"]],decls:135,vars:6,consts:[[1,"min-h-screen","bg-gradient-to-br","from-gray-50","via-blue-50","to-indigo-100","dark:from-dark-bg-primary","dark:via-dark-bg-secondary","dark:to-dark-bg-tertiary"],[1,"container","mx-auto","px-4","py-8"],[1,"mb-8"],[1,"flex","items-center","space-x-2","text-sm","text-text","dark:text-dark-text-secondary","mb-4"],["routerLink","/admin/projects/list-project",1,"hover:text-primary","dark:hover:text-dark-accent-primary","transition-colors"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-4","h-4"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M9 5l7 7-7 7"],[1,"text-primary","dark:text-dark-accent-primary","font-medium"],[1,"bg-white/80","dark:bg-dark-bg-secondary/80","backdrop-blur-sm","rounded-2xl","p-8","shadow-lg","border","border-gray-200/50","dark:border-dark-bg-tertiary/50"],[1,"flex","items-center","space-x-4"],[1,"h-16","w-16","rounded-2xl","bg-gradient-to-br","from-primary","to-primary-dark","dark:from-dark-accent-primary","dark:to-dark-accent-secondary","flex","items-center","justify-center","shadow-lg"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-8","h-8","text-white"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M12 6v6m0 0v6m0-6h6m-6 0H6"],[1,"text-3xl","font-bold","text-text-dark","dark:text-dark-text-primary"],[1,"text-text","dark:text-dark-text-secondary"],[1,"max-w-4xl","mx-auto"],[1,"bg-white/80","dark:bg-dark-bg-secondary/80","backdrop-blur-sm","rounded-2xl","shadow-lg","border","border-gray-200/50","dark:border-dark-bg-tertiary/50","overflow-hidden"],[1,"p-8"],["enctype","multipart/form-data",1,"space-y-8",3,"formGroup","ngSubmit"],[1,"space-y-6"],[1,"flex","items-center","space-x-3","mb-6"],[1,"bg-primary/10","dark:bg-dark-accent-primary/20","p-2","rounded-lg"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-5","h-5","text-primary","dark:text-dark-accent-primary"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"],[1,"text-lg","font-semibold","text-text-dark","dark:text-dark-text-primary"],[1,"space-y-2"],["for","titre",1,"flex","items-center","space-x-2","text-sm","font-semibold","text-text-dark","dark:text-dark-text-primary"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-4","h-4","text-primary","dark:text-dark-accent-primary"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a1.994 1.994 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"],[1,"text-danger","dark:text-danger-dark"],[1,"relative"],["type","text","id","titre","formControlName","titre","placeholder","Ex: D\xe9veloppement d'une application web",1,"w-full","px-4","py-3","bg-white","dark:bg-dark-bg-secondary","border-2","border-gray-200","dark:border-dark-bg-tertiary","rounded-xl","focus:outline-none","focus:border-primary","dark:focus:border-dark-accent-primary","focus:ring-4","focus:ring-primary/10","dark:focus:ring-dark-accent-primary/20","transition-all","duration-200","text-text-dark","dark:text-dark-text-primary","placeholder-gray-400","dark:placeholder-dark-text-secondary"],["class","flex items-center space-x-2 text-danger dark:text-danger-dark text-sm",4,"ngIf"],["for","description",1,"flex","items-center","space-x-2","text-sm","font-semibold","text-text-dark","dark:text-dark-text-primary"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M4 6h16M4 12h16M4 18h7"],["id","description","formControlName","description","rows","4","placeholder","D\xe9crivez les objectifs, les livrables attendus et les crit\xe8res d'\xe9valuation...",1,"w-full","px-4","py-3","bg-white","dark:bg-dark-bg-secondary","border-2","border-gray-200","dark:border-dark-bg-tertiary","rounded-xl","focus:outline-none","focus:border-primary","dark:focus:border-dark-accent-primary","focus:ring-4","focus:ring-primary/10","dark:focus:ring-dark-accent-primary/20","transition-all","duration-200","text-text-dark","dark:text-dark-text-primary","placeholder-gray-400","dark:placeholder-dark-text-secondary","resize-none"],[1,"grid","grid-cols-1","md:grid-cols-2","gap-6"],["for","dateLimite",1,"flex","items-center","space-x-2","text-sm","font-semibold","text-text-dark","dark:text-dark-text-primary"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"],["type","date","id","dateLimite","formControlName","dateLimite",1,"w-full","px-4","py-3","bg-white","dark:bg-dark-bg-secondary","border-2","border-gray-200","dark:border-dark-bg-tertiary","rounded-xl","focus:outline-none","focus:border-primary","dark:focus:border-dark-accent-primary","focus:ring-4","focus:ring-primary/10","dark:focus:ring-dark-accent-primary/20","transition-all","duration-200","text-text-dark","dark:text-dark-text-primary"],["for","groupe",1,"flex","items-center","space-x-2","text-sm","font-semibold","text-text-dark","dark:text-dark-text-primary"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"],["id","groupe","formControlName","groupe",1,"w-full","px-4","py-3","pr-10","bg-white","dark:bg-dark-bg-secondary","border-2","border-gray-200","dark:border-dark-bg-tertiary","rounded-xl","focus:outline-none","focus:border-primary","dark:focus:border-dark-accent-primary","focus:ring-4","focus:ring-primary/10","dark:focus:ring-dark-accent-primary/20","transition-all","duration-200","text-text-dark","dark:text-dark-text-primary","appearance-none"],["value",""],["value","1cinfo"],["value","2cinfo1"],["value","2cinfo2"],["value","2cinfo3"],["value","tous"],[1,"absolute","inset-y-0","right-0","pr-3","flex","items-center","pointer-events-none"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-5","h-5","text-gray-400","dark:text-dark-text-secondary"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M19 9l-7 7-7-7"],[1,"bg-secondary/10","dark:bg-dark-accent-secondary/20","p-2","rounded-lg"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-5","h-5","text-secondary","dark:text-dark-accent-secondary"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"],["for","fichiers",1,"flex","items-center","space-x-2","text-sm","font-semibold","text-text-dark","dark:text-dark-text-primary"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-4","h-4","text-secondary","dark:text-dark-accent-secondary"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"],[1,"text-xs","text-text","dark:text-dark-text-secondary"],["type","file","id","fichiers","multiple","",1,"absolute","inset-0","w-full","h-full","opacity-0","cursor-pointer","z-10",3,"change"],[1,"w-full","px-6","py-8","bg-gray-50","dark:bg-dark-bg-tertiary/50","border-2","border-dashed","border-gray-300","dark:border-dark-bg-tertiary","rounded-xl","hover:border-primary","dark:hover:border-dark-accent-primary","transition-all","duration-200","text-center"],[1,"space-y-3"],[1,"bg-primary/10","dark:bg-dark-accent-primary/20","p-3","rounded-xl","w-fit","mx-auto"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-8","h-8","text-primary","dark:text-dark-accent-primary"],[1,"text-text-dark","dark:text-dark-text-primary","font-medium"],[1,"text-sm","text-text","dark:text-dark-text-secondary"],[1,"flex","flex-col","sm:flex-row","gap-4","pt-6","border-t","border-gray-200","dark:border-dark-bg-tertiary/50"],["type","button","routerLink","/admin/projects/list-project",1,"flex-1","px-6","py-3","bg-gray-100","dark:bg-dark-bg-tertiary","text-text-dark","dark:text-dark-text-primary","hover:bg-gray-200","dark:hover:bg-dark-bg-tertiary/80","rounded-xl","transition-all","duration-200","font-medium"],[1,"flex","items-center","justify-center","space-x-2"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-5","h-5"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M6 18L18 6M6 6l12 12"],["type","submit",1,"flex-1","px-6","py-3","bg-gradient-to-r","from-primary","to-primary-dark","dark:from-dark-accent-primary","dark:to-dark-accent-secondary","text-white","rounded-xl","hover:shadow-lg","hover:scale-105","transition-all","duration-200","font-medium","disabled:opacity-50","disabled:cursor-not-allowed","disabled:hover:scale-100","disabled:hover:shadow-none",3,"disabled"],[1,"flex","items-center","space-x-2","text-danger","dark:text-danger-dark","text-sm"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"]],template:function(i,o){if(1&i&&(t.j41(0,"div",0)(1,"div",1)(2,"div",2)(3,"nav",3)(4,"a",4),t.EFF(5,"Projets"),t.k0s(),t.qSk(),t.j41(6,"svg",5),t.nrm(7,"path",6),t.k0s(),t.joV(),t.j41(8,"span",7),t.EFF(9,"Nouveau projet"),t.k0s()(),t.j41(10,"div",8)(11,"div",9)(12,"div",10),t.qSk(),t.j41(13,"svg",11),t.nrm(14,"path",12),t.k0s()(),t.joV(),t.j41(15,"div")(16,"h1",13),t.EFF(17," Cr\xe9er un nouveau projet "),t.k0s(),t.j41(18,"p",14),t.EFF(19," Ajoutez un projet pour organiser le travail de vos \xe9tudiants "),t.k0s()()()()(),t.j41(20,"div",15)(21,"div",16)(22,"div",17)(23,"form",18),t.bIt("ngSubmit",function(){return o.onSubmit()}),t.j41(24,"div",19)(25,"div",20)(26,"div",21),t.qSk(),t.j41(27,"svg",22),t.nrm(28,"path",23),t.k0s()(),t.joV(),t.j41(29,"h3",24),t.EFF(30,"Informations g\xe9n\xe9rales"),t.k0s()(),t.j41(31,"div",25)(32,"label",26),t.qSk(),t.j41(33,"svg",27),t.nrm(34,"path",28),t.k0s(),t.joV(),t.j41(35,"span"),t.EFF(36,"Titre du projet"),t.k0s(),t.j41(37,"span",29),t.EFF(38,"*"),t.k0s()(),t.j41(39,"div",30),t.nrm(40,"input",31),t.k0s(),t.DNE(41,Xe,5,0,"div",32),t.k0s(),t.j41(42,"div",25)(43,"label",33),t.qSk(),t.j41(44,"svg",27),t.nrm(45,"path",34),t.k0s(),t.joV(),t.j41(46,"span"),t.EFF(47,"Description"),t.k0s(),t.j41(48,"span",29),t.EFF(49,"*"),t.k0s()(),t.j41(50,"div",30),t.nrm(51,"textarea",35),t.k0s(),t.DNE(52,Ue,5,0,"div",32),t.k0s(),t.j41(53,"div",36)(54,"div",25)(55,"label",37),t.qSk(),t.j41(56,"svg",27),t.nrm(57,"path",38),t.k0s(),t.joV(),t.j41(58,"span"),t.EFF(59,"Date limite"),t.k0s(),t.j41(60,"span",29),t.EFF(61,"*"),t.k0s()(),t.j41(62,"div",30),t.nrm(63,"input",39),t.k0s(),t.DNE(64,Ze,5,0,"div",32),t.k0s(),t.j41(65,"div",25)(66,"label",40),t.qSk(),t.j41(67,"svg",27),t.nrm(68,"path",41),t.k0s(),t.joV(),t.j41(69,"span"),t.EFF(70,"Groupe cible"),t.k0s(),t.j41(71,"span",29),t.EFF(72,"*"),t.k0s()(),t.j41(73,"div",30)(74,"select",42)(75,"option",43),t.EFF(76,"S\xe9lectionner un groupe"),t.k0s(),t.j41(77,"option",44),t.EFF(78,"1cinfo"),t.k0s(),t.j41(79,"option",45),t.EFF(80,"2cinfo1"),t.k0s(),t.j41(81,"option",46),t.EFF(82,"2cinfo2"),t.k0s(),t.j41(83,"option",47),t.EFF(84,"2cinfo3"),t.k0s(),t.j41(85,"option",48),t.EFF(86,"Tous les groupes"),t.k0s()(),t.j41(87,"div",49),t.qSk(),t.j41(88,"svg",50),t.nrm(89,"path",51),t.k0s()()(),t.DNE(90,Ke,5,0,"div",32),t.k0s()()(),t.joV(),t.j41(91,"div",19)(92,"div",20)(93,"div",52),t.qSk(),t.j41(94,"svg",53),t.nrm(95,"path",54),t.k0s()(),t.joV(),t.j41(96,"h3",24),t.EFF(97,"Fichiers du projet"),t.k0s()(),t.j41(98,"div",25)(99,"label",55),t.qSk(),t.j41(100,"svg",56),t.nrm(101,"path",57),t.k0s(),t.joV(),t.j41(102,"span"),t.EFF(103,"Documents et ressources"),t.k0s(),t.j41(104,"span",58),t.EFF(105,"(optionnel)"),t.k0s()(),t.j41(106,"div",30)(107,"input",59),t.bIt("change",function(s){return o.onFileChange(s)}),t.k0s(),t.j41(108,"div",60)(109,"div",61)(110,"div",62),t.qSk(),t.j41(111,"svg",63),t.nrm(112,"path",57),t.k0s()(),t.joV(),t.j41(113,"div")(114,"p",64),t.EFF(115,"Glissez vos fichiers ici"),t.k0s(),t.j41(116,"p",65),t.EFF(117,"ou cliquez pour parcourir"),t.k0s()(),t.j41(118,"p",58),t.EFF(119,"PDF, DOC, DOCX, images jusqu'\xe0 10MB"),t.k0s()()()(),t.j41(120,"p",58),t.EFF(121,"Ajoutez des \xe9nonc\xe9s, des ressources ou des exemples pour aider vos \xe9tudiants"),t.k0s()()(),t.j41(122,"div",66)(123,"button",67)(124,"div",68),t.qSk(),t.j41(125,"svg",69),t.nrm(126,"path",70),t.k0s(),t.joV(),t.j41(127,"span"),t.EFF(128,"Annuler"),t.k0s()()(),t.j41(129,"button",71)(130,"div",68),t.qSk(),t.j41(131,"svg",69),t.nrm(132,"path",12),t.k0s(),t.joV(),t.j41(133,"span"),t.EFF(134,"Cr\xe9er le projet"),t.k0s()()()()()()()()()()),2&i){let a,s,d,l;t.R7$(23),t.Y8G("formGroup",o.projetForm),t.R7$(18),t.Y8G("ngIf",(null==(a=o.projetForm.get("titre"))?null:a.invalid)&&(null==(a=o.projetForm.get("titre"))?null:a.touched)),t.R7$(11),t.Y8G("ngIf",(null==(s=o.projetForm.get("description"))?null:s.invalid)&&(null==(s=o.projetForm.get("description"))?null:s.touched)),t.R7$(12),t.Y8G("ngIf",(null==(d=o.projetForm.get("dateLimite"))?null:d.invalid)&&(null==(d=o.projetForm.get("dateLimite"))?null:d.touched)),t.R7$(26),t.Y8G("ngIf",(null==(l=o.projetForm.get("groupe"))?null:l.invalid)&&(null==(l=o.projetForm.get("groupe"))?null:l.touched)),t.R7$(39),t.Y8G("disabled",o.projetForm.invalid)}},dependencies:[p.bT,b.Wk,c.qT,c.xH,c.y7,c.me,c.wz,c.BC,c.cb,c.j4,c.JD]})}}return n})();function Je(n,r){1&n&&(t.j41(0,"div",51),t.qSk(),t.j41(1,"svg",5),t.nrm(2,"path",52),t.k0s(),t.joV(),t.j41(3,"span"),t.EFF(4,"Le titre est requis"),t.k0s()())}function Qe(n,r){1&n&&(t.j41(0,"div",51),t.qSk(),t.j41(1,"svg",5),t.nrm(2,"path",52),t.k0s(),t.joV(),t.j41(3,"span"),t.EFF(4,"La description est requise"),t.k0s()())}function ti(n,r){1&n&&(t.j41(0,"div",51),t.qSk(),t.j41(1,"svg",5),t.nrm(2,"path",52),t.k0s(),t.joV(),t.j41(3,"span"),t.EFF(4,"La date limite est requise"),t.k0s()())}function ei(n,r){1&n&&(t.j41(0,"div",51),t.qSk(),t.j41(1,"svg",5),t.nrm(2,"path",52),t.k0s(),t.joV(),t.j41(3,"span"),t.EFF(4,"Le groupe est requis"),t.k0s()())}const At=function(n){return["/admin/projects/details",n]};let ii=(()=>{class n{constructor(e,i,o,a){this.route=e,this.fb=i,this.projetService=o,this.router=a}ngOnInit(){this.projectId=this.route.snapshot.paramMap.get("id")||"",this.projetId=this.projectId,this.updateForm=this.fb.group({titre:["",c.k0.required],description:["",c.k0.required],groupe:["",c.k0.required],dateLimite:["",c.k0.required]}),this.projetService.getProjetById(this.projectId).subscribe({next:e=>{let i="";e.dateLimite&&(i=new Date(e.dateLimite).toISOString().split("T")[0]),this.updateForm.patchValue({titre:e.titre,description:e.description,groupe:e.groupe,dateLimite:i})},error:e=>{console.error("Erreur lors du chargement du projet:",e),alert("Erreur lors du chargement du projet")}})}onSubmit(){this.updateForm.valid&&this.projetService.updateProjet(this.projectId,this.updateForm.value).subscribe({next:()=>{alert("Projet mis \xe0 jour avec succ\xe8s"),this.router.navigate(["/admin/projects/details",this.projectId])},error:e=>{console.error("Erreur lors de la mise \xe0 jour:",e),alert("Erreur lors de la mise \xe0 jour du projet")}})}static{this.\u0275fac=function(i){return new(i||n)(t.rXU(b.nX),t.rXU(c.ok),t.rXU(R.e),t.rXU(b.Ix))}}static{this.\u0275cmp=t.VBU({type:n,selectors:[["app-update-project"]],decls:93,vars:12,consts:[[1,"min-h-screen","bg-gradient-to-br","from-gray-50","via-blue-50","to-indigo-100","dark:from-dark-bg-primary","dark:via-dark-bg-secondary","dark:to-dark-bg-tertiary"],[1,"container","mx-auto","px-4","py-8"],[1,"mb-8"],[1,"flex","items-center","space-x-2","text-sm","text-text","dark:text-dark-text-secondary","mb-4"],["routerLink","/admin/projects/list-project",1,"hover:text-primary","dark:hover:text-dark-accent-primary","transition-colors"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-4","h-4"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M9 5l7 7-7 7"],[1,"hover:text-primary","dark:hover:text-dark-accent-primary","transition-colors",3,"routerLink"],[1,"text-primary","dark:text-dark-accent-primary","font-medium"],[1,"bg-white/80","dark:bg-dark-bg-secondary/80","backdrop-blur-sm","rounded-2xl","p-8","shadow-lg","border","border-gray-200/50","dark:border-dark-bg-tertiary/50"],[1,"flex","items-center","space-x-4"],[1,"h-16","w-16","rounded-2xl","bg-gradient-to-br","from-secondary","to-primary","dark:from-dark-accent-secondary","dark:to-dark-accent-primary","flex","items-center","justify-center","shadow-lg"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-8","h-8","text-white"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"],[1,"text-3xl","font-bold","text-text-dark","dark:text-dark-text-primary"],[1,"text-text","dark:text-dark-text-secondary"],[1,"max-w-4xl","mx-auto"],[1,"bg-white/80","dark:bg-dark-bg-secondary/80","backdrop-blur-sm","rounded-2xl","shadow-lg","border","border-gray-200/50","dark:border-dark-bg-tertiary/50","overflow-hidden"],[1,"p-8"],[1,"space-y-8",3,"formGroup","ngSubmit"],[1,"space-y-6"],[1,"flex","items-center","space-x-3","mb-6"],[1,"bg-primary/10","dark:bg-dark-accent-primary/20","p-2","rounded-lg"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-5","h-5","text-primary","dark:text-dark-accent-primary"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"],[1,"text-lg","font-semibold","text-text-dark","dark:text-dark-text-primary"],[1,"space-y-2"],["for","titre",1,"flex","items-center","space-x-2","text-sm","font-semibold","text-text-dark","dark:text-dark-text-primary"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-4","h-4","text-primary","dark:text-dark-accent-primary"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a1.994 1.994 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"],[1,"text-danger","dark:text-danger-dark"],[1,"relative"],["type","text","id","titre","formControlName","titre","placeholder","Ex: D\xe9veloppement d'une application web",1,"w-full","px-4","py-3","bg-white","dark:bg-dark-bg-secondary","border-2","border-gray-200","dark:border-dark-bg-tertiary","rounded-xl","focus:outline-none","focus:border-primary","dark:focus:border-dark-accent-primary","focus:ring-4","focus:ring-primary/10","dark:focus:ring-dark-accent-primary/20","transition-all","duration-200","text-text-dark","dark:text-dark-text-primary","placeholder-gray-400","dark:placeholder-dark-text-secondary"],["class","flex items-center space-x-2 text-danger dark:text-danger-dark text-sm",4,"ngIf"],["for","description",1,"flex","items-center","space-x-2","text-sm","font-semibold","text-text-dark","dark:text-dark-text-primary"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M4 6h16M4 12h16M4 18h7"],["id","description","formControlName","description","rows","4","placeholder","D\xe9crivez les objectifs, les livrables attendus et les crit\xe8res d'\xe9valuation...",1,"w-full","px-4","py-3","bg-white","dark:bg-dark-bg-secondary","border-2","border-gray-200","dark:border-dark-bg-tertiary","rounded-xl","focus:outline-none","focus:border-primary","dark:focus:border-dark-accent-primary","focus:ring-4","focus:ring-primary/10","dark:focus:ring-dark-accent-primary/20","transition-all","duration-200","text-text-dark","dark:text-dark-text-primary","placeholder-gray-400","dark:placeholder-dark-text-secondary","resize-none"],[1,"grid","grid-cols-1","md:grid-cols-2","gap-6"],["for","dateLimite",1,"flex","items-center","space-x-2","text-sm","font-semibold","text-text-dark","dark:text-dark-text-primary"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"],["type","date","id","dateLimite","formControlName","dateLimite",1,"w-full","px-4","py-3","bg-white","dark:bg-dark-bg-secondary","border-2","border-gray-200","dark:border-dark-bg-tertiary","rounded-xl","focus:outline-none","focus:border-primary","dark:focus:border-dark-accent-primary","focus:ring-4","focus:ring-primary/10","dark:focus:ring-dark-accent-primary/20","transition-all","duration-200","text-text-dark","dark:text-dark-text-primary"],["for","groupe",1,"flex","items-center","space-x-2","text-sm","font-semibold","text-text-dark","dark:text-dark-text-primary"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"],["type","text","id","groupe","formControlName","groupe","placeholder","Ex: 2cinfo1",1,"w-full","px-4","py-3","bg-white","dark:bg-dark-bg-secondary","border-2","border-gray-200","dark:border-dark-bg-tertiary","rounded-xl","focus:outline-none","focus:border-primary","dark:focus:border-dark-accent-primary","focus:ring-4","focus:ring-primary/10","dark:focus:ring-dark-accent-primary/20","transition-all","duration-200","text-text-dark","dark:text-dark-text-primary","placeholder-gray-400","dark:placeholder-dark-text-secondary"],[1,"flex","flex-col","sm:flex-row","gap-4","pt-6","border-t","border-gray-200","dark:border-dark-bg-tertiary/50"],["type","button",1,"flex-1","px-6","py-3","bg-gray-100","dark:bg-dark-bg-tertiary","text-text-dark","dark:text-dark-text-primary","hover:bg-gray-200","dark:hover:bg-dark-bg-tertiary/80","rounded-xl","transition-all","duration-200","font-medium",3,"routerLink"],[1,"flex","items-center","justify-center","space-x-2"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-5","h-5"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M6 18L18 6M6 6l12 12"],["type","submit",1,"flex-1","px-6","py-3","bg-gradient-to-r","from-secondary","to-primary","dark:from-dark-accent-secondary","dark:to-dark-accent-primary","text-white","rounded-xl","hover:shadow-lg","hover:scale-105","transition-all","duration-200","font-medium","disabled:opacity-50","disabled:cursor-not-allowed","disabled:hover:scale-100","disabled:hover:shadow-none",3,"disabled"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M5 13l4 4L19 7"],[1,"flex","items-center","space-x-2","text-danger","dark:text-danger-dark","text-sm"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"]],template:function(i,o){if(1&i&&(t.j41(0,"div",0)(1,"div",1)(2,"div",2)(3,"nav",3)(4,"a",4),t.EFF(5,"Projets"),t.k0s(),t.qSk(),t.j41(6,"svg",5),t.nrm(7,"path",6),t.k0s(),t.joV(),t.j41(8,"a",7),t.EFF(9,"D\xe9tails"),t.k0s(),t.qSk(),t.j41(10,"svg",5),t.nrm(11,"path",6),t.k0s(),t.joV(),t.j41(12,"span",8),t.EFF(13,"Modifier"),t.k0s()(),t.j41(14,"div",9)(15,"div",10)(16,"div",11),t.qSk(),t.j41(17,"svg",12),t.nrm(18,"path",13),t.k0s()(),t.joV(),t.j41(19,"div")(20,"h1",14),t.EFF(21," Modifier le projet "),t.k0s(),t.j41(22,"p",15),t.EFF(23," Mettez \xe0 jour les informations du projet "),t.k0s()()()()(),t.j41(24,"div",16)(25,"div",17)(26,"div",18)(27,"form",19),t.bIt("ngSubmit",function(){return o.onSubmit()}),t.j41(28,"div",20)(29,"div",21)(30,"div",22),t.qSk(),t.j41(31,"svg",23),t.nrm(32,"path",24),t.k0s()(),t.joV(),t.j41(33,"h3",25),t.EFF(34,"Informations du projet"),t.k0s()(),t.j41(35,"div",26)(36,"label",27),t.qSk(),t.j41(37,"svg",28),t.nrm(38,"path",29),t.k0s(),t.joV(),t.j41(39,"span"),t.EFF(40,"Titre du projet"),t.k0s(),t.j41(41,"span",30),t.EFF(42,"*"),t.k0s()(),t.j41(43,"div",31),t.nrm(44,"input",32),t.k0s(),t.DNE(45,Je,5,0,"div",33),t.k0s(),t.j41(46,"div",26)(47,"label",34),t.qSk(),t.j41(48,"svg",28),t.nrm(49,"path",35),t.k0s(),t.joV(),t.j41(50,"span"),t.EFF(51,"Description"),t.k0s(),t.j41(52,"span",30),t.EFF(53,"*"),t.k0s()(),t.j41(54,"div",31),t.nrm(55,"textarea",36),t.k0s(),t.DNE(56,Qe,5,0,"div",33),t.k0s(),t.j41(57,"div",37)(58,"div",26)(59,"label",38),t.qSk(),t.j41(60,"svg",28),t.nrm(61,"path",39),t.k0s(),t.joV(),t.j41(62,"span"),t.EFF(63,"Date limite"),t.k0s(),t.j41(64,"span",30),t.EFF(65,"*"),t.k0s()(),t.j41(66,"div",31),t.nrm(67,"input",40),t.k0s(),t.DNE(68,ti,5,0,"div",33),t.k0s(),t.j41(69,"div",26)(70,"label",41),t.qSk(),t.j41(71,"svg",28),t.nrm(72,"path",42),t.k0s(),t.joV(),t.j41(73,"span"),t.EFF(74,"Groupe cible"),t.k0s(),t.j41(75,"span",30),t.EFF(76,"*"),t.k0s()(),t.j41(77,"div",31),t.nrm(78,"input",43),t.k0s(),t.DNE(79,ei,5,0,"div",33),t.k0s()()(),t.j41(80,"div",44)(81,"button",45)(82,"div",46),t.qSk(),t.j41(83,"svg",47),t.nrm(84,"path",48),t.k0s(),t.joV(),t.j41(85,"span"),t.EFF(86,"Annuler"),t.k0s()()(),t.j41(87,"button",49)(88,"div",46),t.qSk(),t.j41(89,"svg",47),t.nrm(90,"path",50),t.k0s(),t.joV(),t.j41(91,"span"),t.EFF(92,"Mettre \xe0 jour le projet"),t.k0s()()()()()()()()()()),2&i){let a,s,d,l;t.R7$(8),t.Y8G("routerLink",t.eq3(8,At,o.projetId)),t.R7$(19),t.Y8G("formGroup",o.updateForm),t.R7$(18),t.Y8G("ngIf",(null==(a=o.updateForm.get("titre"))?null:a.invalid)&&(null==(a=o.updateForm.get("titre"))?null:a.touched)),t.R7$(11),t.Y8G("ngIf",(null==(s=o.updateForm.get("description"))?null:s.invalid)&&(null==(s=o.updateForm.get("description"))?null:s.touched)),t.R7$(12),t.Y8G("ngIf",(null==(d=o.updateForm.get("dateLimite"))?null:d.invalid)&&(null==(d=o.updateForm.get("dateLimite"))?null:d.touched)),t.R7$(11),t.Y8G("ngIf",(null==(l=o.updateForm.get("groupe"))?null:l.invalid)&&(null==(l=o.updateForm.get("groupe"))?null:l.touched)),t.R7$(2),t.Y8G("routerLink",t.eq3(10,At,o.projetId)),t.R7$(6),t.Y8G("disabled",!o.updateForm.valid)}},dependencies:[p.bT,b.Wk,c.qT,c.me,c.BC,c.cb,c.j4,c.JD]})}}return n})();var ri=g(4704),P=g(7169);function oi(n,r){if(1&n&&(t.j41(0,"div",64)(1,"div",65)(2,"div",66)(3,"div",67)(4,"div",29),t.qSk(),t.j41(5,"svg",30),t.nrm(6,"path",68),t.k0s()(),t.joV(),t.j41(7,"div",69)(8,"p",70),t.EFF(9),t.k0s(),t.j41(10,"p",71),t.EFF(11,"Document"),t.k0s()()(),t.j41(12,"a",72)(13,"div",16),t.qSk(),t.j41(14,"svg",73),t.nrm(15,"path",74),t.k0s(),t.joV(),t.j41(16,"span"),t.EFF(17,"T\xe9l\xe9charger"),t.k0s()()()()()()),2&n){const e=r.$implicit,i=t.XpG(2);t.R7$(9),t.SpI(" ",i.getFileName(e)," "),t.R7$(3),t.Y8G("href",i.getFileUrl(e),t.B4B)}}function ni(n,r){if(1&n&&(t.j41(0,"div",62),t.DNE(1,oi,18,2,"div",63),t.k0s()),2&n){const e=t.XpG();t.R7$(1),t.Y8G("ngForOf",e.projet.fichiers)}}function ai(n,r){1&n&&(t.j41(0,"div",75)(1,"div",76)(2,"div",77),t.qSk(),t.j41(3,"svg",78),t.nrm(4,"path",37),t.k0s()(),t.joV(),t.j41(5,"p",22),t.EFF(6,"Aucun fichier joint \xe0 ce projet"),t.k0s()()())}function si(n,r){1&n&&(t.j41(0,"div",59)(1,"div",79),t.nrm(2,"div",80)(3,"div",81)(4,"div",82),t.k0s()())}function di(n,r){if(1&n&&(t.j41(0,"div")(1,"div",83)(2,"div",23)(3,"div",84),t.qSk(),t.j41(4,"svg",85),t.nrm(5,"path",86),t.k0s()(),t.joV(),t.j41(6,"div")(7,"h3",87),t.EFF(8,"Statistiques"),t.k0s(),t.j41(9,"p",88),t.EFF(10,"Suivi des rendus"),t.k0s()()()(),t.j41(11,"div",89)(12,"div")(13,"div",90)(14,"span",91),t.EFF(15,"Progression globale"),t.k0s(),t.j41(16,"span",92),t.EFF(17),t.k0s()(),t.j41(18,"div",93),t.nrm(19,"div",94),t.k0s(),t.j41(20,"div",95)(21,"span"),t.EFF(22),t.k0s(),t.j41(23,"span"),t.EFF(24),t.k0s()()(),t.j41(25,"div",96)(26,"div",97)(27,"div",66)(28,"div")(29,"p",98),t.EFF(30,"Rendus"),t.k0s(),t.j41(31,"p",99),t.EFF(32),t.k0s()(),t.j41(33,"div",100),t.qSk(),t.j41(34,"svg",101),t.nrm(35,"path",102),t.k0s()()()(),t.joV(),t.j41(36,"div",103)(37,"div",66)(38,"div")(39,"p",104),t.EFF(40,"En attente"),t.k0s(),t.j41(41,"p",105),t.EFF(42),t.k0s()(),t.j41(43,"div",106),t.qSk(),t.j41(44,"svg",107),t.nrm(45,"path",21),t.k0s()()()(),t.joV(),t.j41(46,"div",108)(47,"div",66)(48,"div")(49,"p",109),t.EFF(50,"Taux"),t.k0s(),t.j41(51,"p",110),t.EFF(52),t.k0s()(),t.j41(53,"div",111),t.qSk(),t.j41(54,"svg",42),t.nrm(55,"path",112),t.k0s()()()()()()()),2&n){const e=t.XpG();t.R7$(17),t.Lme(" ",e.etudiantsRendus.length,"/",e.totalEtudiants," "),t.R7$(2),t.xc7("width",e.getProgressPercentage(),"%"),t.R7$(3),t.SpI("",e.getProgressPercentage(),"% compl\xe9t\xe9"),t.R7$(2),t.SpI("",e.getRemainingDays()," jours restants"),t.R7$(8),t.JRh(e.etudiantsRendus.length),t.R7$(10),t.JRh(e.totalEtudiants-e.etudiantsRendus.length),t.R7$(10),t.SpI("",e.getProgressPercentage(),"%")}}function ci(n,r){if(1&n&&(t.j41(0,"div",113)(1,"div",114),t.EFF(2),t.k0s(),t.j41(3,"div",115)(4,"div",19),t.EFF(5),t.k0s(),t.j41(6,"div",71),t.EFF(7),t.k0s()(),t.j41(8,"div",116),t.qSk(),t.j41(9,"svg",117),t.nrm(10,"path",102),t.k0s()()()),2&n){const e=r.$implicit,i=t.XpG();t.R7$(2),t.SpI(" ",i.getStudentInitials(e.etudiant)," "),t.R7$(3),t.SpI(" ",i.getStudentName(e.etudiant)," "),t.R7$(2),t.SpI(" ",i.formatDate(e.dateRendu)," ")}}function li(n,r){1&n&&(t.j41(0,"div",75)(1,"div",76)(2,"div",77),t.qSk(),t.j41(3,"svg",78),t.nrm(4,"path",51),t.k0s()(),t.joV(),t.j41(5,"p",22),t.EFF(6,"Aucun rendu pour le moment"),t.k0s()()())}const ui=function(n){return["/admin/projects/editProjet",n]},mi=function(){return["/admin/projects/rendus"]},pi=function(n){return{projetId:n}};let hi=(()=>{class n{constructor(e,i,o,a,s){this.route=e,this.router=i,this.projectService=o,this.fileService=a,this.rendusService=s,this.projet=null,this.rendus=[],this.totalEtudiants=0,this.etudiantsRendus=[],this.derniersRendus=[],this.isLoading=!0}ngOnInit(){const e=this.route.snapshot.paramMap.get("id");e&&this.loadProjectData(e)}loadProjectData(e){this.isLoading=!0,this.projectService.getProjetById(e).subscribe({next:i=>{this.projet=i,this.loadProjectStatistics(e)},error:i=>{console.error("Erreur lors du chargement du projet:",i),this.isLoading=!1}})}loadProjectStatistics(e){this.rendusService.getRendusByProjet(e).subscribe({next:i=>{this.rendus=i,this.etudiantsRendus=i.filter(a=>a.etudiant),this.derniersRendus=[...this.etudiantsRendus].sort((a,s)=>new Date(s.dateRendu).getTime()-new Date(a.dateRendu).getTime()).slice(0,5);const o=new Set(this.etudiantsRendus.map(a=>a.etudiant._id||a.etudiant));this.totalEtudiants=Math.max(o.size,this.estimateStudentCount()),this.isLoading=!1},error:i=>{console.error("Erreur lors du chargement des statistiques:",i),this.isLoading=!1}})}estimateStudentCount(){const e=this.projet?.groupe?.toLowerCase();return e?.includes("1c")?25:e?.includes("2c")?20:e?.includes("3c")?15:20}getFileUrl(e){return this.fileService.getDownloadUrl(e)}deleteProjet(e){e&&confirm("\xcates-vous s\xfbr de vouloir supprimer ce projet ?")&&this.projectService.deleteProjet(e).subscribe({next:()=>{alert("Projet supprim\xe9 avec succ\xe8s"),this.router.navigate(["/admin/projects"])},error:i=>{console.error("Erreur lors de la suppression du projet",i),alert("Erreur lors de la suppression du projet")}})}formatDate(e){const i=new Date(e);return`${i.getDate().toString().padStart(2,"0")}/${(i.getMonth()+1).toString().padStart(2,"0")}/${i.getFullYear()}`}getFileName(e){if(!e)return"Fichier";if(e.includes("/")||e.includes("\\")){const i=e.split(/[\/\\]/);return i[i.length-1]}return e}getProjectStatus(){if(!this.projet?.dateLimite)return"En cours";const e=new Date,i=new Date(this.projet.dateLimite);return i<e?"Expir\xe9":i.getTime()-e.getTime()<6048e5?"Urgent":"Actif"}getStatusClass(){switch(this.getProjectStatus()){case"Actif":return"bg-success/10 dark:bg-dark-accent-secondary/20 text-success dark:text-dark-accent-secondary border border-success/20 dark:border-dark-accent-secondary/30";case"Urgent":return"bg-warning/10 dark:bg-warning/20 text-warning dark:text-warning border border-warning/20 dark:border-warning/30";case"Expir\xe9":return"bg-danger/10 dark:bg-danger-dark/20 text-danger dark:text-danger-dark border border-danger/20 dark:border-danger-dark/30";default:return"bg-info/10 dark:bg-dark-accent-primary/20 text-info dark:text-dark-accent-primary border border-info/20 dark:border-dark-accent-primary/30"}}getProgressPercentage(){return 0===this.totalEtudiants?0:Math.round(this.etudiantsRendus.length/this.totalEtudiants*100)}getRemainingDays(){if(!this.projet?.dateLimite)return 0;const e=new Date,o=new Date(this.projet.dateLimite).getTime()-e.getTime(),a=Math.ceil(o/864e5);return Math.max(0,a)}getStudentInitials(e){if(!e)return"??";const i=e.firstName||e.prenom||"",o=e.lastName||e.nom||"";if(i&&o)return(i.charAt(0)+o.charAt(0)).toUpperCase();const a=e.fullName||e.name||"";if(a){const s=a.trim().split(" ");return s.length>=2?(s[0].charAt(0)+s[1].charAt(0)).toUpperCase():a.substring(0,2).toUpperCase()}return i?i.substring(0,2).toUpperCase():"??"}getStudentName(e){if(!e)return"Utilisateur inconnu";const i=e.firstName||e.prenom||"",o=e.lastName||e.nom||"";if(i&&o)return`${i} ${o}`.trim();const a=e.fullName||e.name||"";return a?a.trim():i?i.trim():e.email?e.email:"Utilisateur inconnu"}static{this.\u0275fac=function(i){return new(i||n)(t.rXU(b.nX),t.rXU(b.Ix),t.rXU(R.e),t.rXU(ri.E),t.rXU(P.R))}}static{this.\u0275cmp=t.VBU({type:n,selectors:[["app-detail-project"]],decls:98,vars:23,consts:[[1,"min-h-screen","bg-gradient-to-br","from-gray-50","via-blue-50","to-indigo-100","dark:from-dark-bg-primary","dark:via-dark-bg-secondary","dark:to-dark-bg-tertiary"],[1,"container","mx-auto","px-4","py-8"],[1,"mb-8"],[1,"flex","items-center","space-x-2","text-sm","text-text","dark:text-dark-text-secondary","mb-4"],["routerLink","/admin/projects/list-project",1,"hover:text-primary","dark:hover:text-dark-accent-primary","transition-colors"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-4","h-4"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M9 5l7 7-7 7"],[1,"text-primary","dark:text-dark-accent-primary","font-medium"],[1,"bg-white/80","dark:bg-dark-bg-secondary/80","backdrop-blur-sm","rounded-2xl","p-8","shadow-lg","border","border-gray-200/50","dark:border-dark-bg-tertiary/50"],[1,"flex","flex-col","lg:flex-row","lg:items-center","lg:justify-between"],[1,"flex","items-center","space-x-4","mb-6","lg:mb-0"],[1,"h-16","w-16","rounded-2xl","bg-gradient-to-br","from-primary","to-primary-dark","dark:from-dark-accent-primary","dark:to-dark-accent-secondary","flex","items-center","justify-center","shadow-lg"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-8","h-8","text-white"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M19 11H5m14-7H3a2 2 0 00-2 2v12a2 2 0 002 2h16a2 2 0 002-2V6a2 2 0 00-2-2z"],[1,"text-3xl","font-bold","text-text-dark","dark:text-dark-text-primary"],[1,"flex","items-center","space-x-4","mt-2"],[1,"flex","items-center","space-x-1"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-4","h-4","text-primary","dark:text-dark-accent-primary"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"],[1,"text-sm","font-medium","text-text-dark","dark:text-dark-text-primary"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-4","h-4","text-warning","dark:text-warning"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"],[1,"text-sm","text-text","dark:text-dark-text-secondary"],[1,"flex","items-center","space-x-3"],[1,"px-4","py-2","rounded-xl","text-sm","font-medium",3,"ngClass"],[1,"grid","grid-cols-1","lg:grid-cols-3","gap-8"],[1,"lg:col-span-2","space-y-6"],[1,"bg-white/80","dark:bg-dark-bg-secondary/80","backdrop-blur-sm","rounded-2xl","p-6","shadow-lg","border","border-gray-200/50","dark:border-dark-bg-tertiary/50"],[1,"flex","items-center","space-x-3","mb-4"],[1,"bg-primary/10","dark:bg-dark-accent-primary/20","p-2","rounded-lg"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-5","h-5","text-primary","dark:text-dark-accent-primary"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M4 6h16M4 12h16M4 18h7"],[1,"text-lg","font-semibold","text-text-dark","dark:text-dark-text-primary"],[1,"prose","prose-gray","dark:prose-invert","max-w-none"],[1,"text-text","dark:text-dark-text-secondary","leading-relaxed"],[1,"bg-secondary/10","dark:bg-dark-accent-secondary/20","p-2","rounded-lg"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-5","h-5","text-secondary","dark:text-dark-accent-secondary"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"],[1,"text-sm","font-normal","text-text","dark:text-dark-text-secondary","ml-2"],["class","grid grid-cols-1 sm:grid-cols-2 gap-4",4,"ngIf"],["class","text-center py-8",4,"ngIf"],[1,"bg-info/10","dark:bg-dark-accent-primary/20","p-2","rounded-lg"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-5","h-5","text-info","dark:text-dark-accent-primary"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M15 12a3 3 0 11-6 0 3 3 0 016 0z"],[1,"grid","grid-cols-1","sm:grid-cols-3","gap-4"],[1,"group","px-4","py-3","bg-gradient-to-r","from-secondary","to-primary","dark:from-dark-accent-secondary","dark:to-dark-accent-primary","text-white","rounded-xl","hover:shadow-lg","hover:scale-105","transition-all","duration-200","font-medium",3,"routerLink"],[1,"flex","items-center","justify-center","space-x-2"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-5","h-5","group-hover:scale-110","transition-transform"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"],[1,"group","px-4","py-3","bg-gradient-to-r","from-primary","to-primary-dark","dark:from-dark-accent-primary","dark:to-dark-accent-secondary","text-white","rounded-xl","hover:shadow-lg","hover:scale-105","transition-all","duration-200","font-medium",3,"routerLink","queryParams"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"],[1,"group","px-4","py-3","bg-gradient-to-r","from-danger","to-danger-dark","dark:from-danger-dark","dark:to-danger","text-white","rounded-xl","hover:shadow-lg","hover:scale-105","transition-all","duration-200","font-medium",3,"click"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"],[1,"space-y-6"],[1,"bg-white/80","dark:bg-dark-bg-secondary/80","backdrop-blur-sm","rounded-2xl","shadow-lg","border","border-gray-200/50","dark:border-dark-bg-tertiary/50","overflow-hidden"],["class","p-6",4,"ngIf"],[4,"ngIf"],[1,"bg-white/80","dark:bg-dark-bg-secondary/80","backdrop-blur-sm","rounded-2xl","shadow-lg","border","border-gray-200/50","dark:border-dark-bg-tertiary/50"],[1,"p-6"],[1,"space-y-3"],["class","flex items-center space-x-3 p-3 bg-gray-50 dark:bg-dark-bg-tertiary/50 rounded-xl",4,"ngFor","ngForOf"],[1,"grid","grid-cols-1","sm:grid-cols-2","gap-4"],["class","group",4,"ngFor","ngForOf"],[1,"group"],[1,"bg-gray-50","dark:bg-dark-bg-tertiary/50","rounded-xl","p-4","border","border-gray-200","dark:border-dark-bg-tertiary","hover:border-primary","dark:hover:border-dark-accent-primary","transition-all","duration-200"],[1,"flex","items-center","justify-between"],[1,"flex","items-center","space-x-3","flex-1","min-w-0"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"],[1,"flex-1","min-w-0"],[1,"text-sm","font-medium","text-text-dark","dark:text-dark-text-primary","truncate"],[1,"text-xs","text-text","dark:text-dark-text-secondary"],["download","",1,"ml-3","px-3","py-2","bg-primary/10","dark:bg-dark-accent-primary/20","text-primary","dark:text-dark-accent-primary","hover:bg-primary","hover:text-white","dark:hover:bg-dark-accent-primary","rounded-lg","transition-all","duration-200","text-xs","font-medium",3,"href"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-3","h-3"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"],[1,"text-center","py-8"],[1,"bg-gray-100","dark:bg-dark-bg-tertiary/50","rounded-xl","p-6"],[1,"bg-gray-200","dark:bg-dark-bg-tertiary","p-3","rounded-lg","inline-flex","items-center","justify-center","mb-3"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-6","h-6","text-gray-400","dark:text-dark-text-secondary"],[1,"animate-pulse","space-y-4"],[1,"h-4","bg-gray-200","dark:bg-dark-bg-tertiary","rounded","w-3/4"],[1,"h-4","bg-gray-200","dark:bg-dark-bg-tertiary","rounded","w-1/2"],[1,"h-8","bg-gray-200","dark:bg-dark-bg-tertiary","rounded"],[1,"bg-gradient-to-r","from-primary","to-primary-dark","dark:from-dark-accent-primary","dark:to-dark-accent-secondary","p-6","text-white"],[1,"bg-white/20","p-2","rounded-lg"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-6","h-6"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"],[1,"text-lg","font-semibold"],[1,"text-sm","text-white/80"],[1,"p-6","space-y-6"],[1,"flex","justify-between","items-center","mb-3"],[1,"text-sm","font-semibold","text-text-dark","dark:text-dark-text-primary"],[1,"text-sm","font-bold","text-primary","dark:text-dark-accent-primary"],[1,"w-full","bg-gray-200","dark:bg-dark-bg-tertiary","rounded-full","h-3"],[1,"bg-gradient-to-r","from-primary","to-primary-dark","dark:from-dark-accent-primary","dark:to-dark-accent-secondary","h-3","rounded-full","transition-all","duration-500"],[1,"flex","justify-between","text-xs","text-text","dark:text-dark-text-secondary","mt-2"],[1,"grid","grid-cols-1","gap-4"],[1,"bg-gradient-to-br","from-success/10","to-success/5","dark:from-dark-accent-secondary/20","dark:to-dark-accent-secondary/10","rounded-xl","p-4","border","border-success/20","dark:border-dark-accent-secondary/30"],[1,"text-xs","font-medium","text-success","dark:text-dark-accent-secondary","uppercase","tracking-wider"],[1,"text-2xl","font-bold","text-success","dark:text-dark-accent-secondary"],[1,"bg-success/20","dark:bg-dark-accent-secondary/30","p-2","rounded-lg"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-5","h-5","text-success","dark:text-dark-accent-secondary"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"],[1,"bg-gradient-to-br","from-warning/10","to-warning/5","dark:from-warning/20","dark:to-warning/10","rounded-xl","p-4","border","border-warning/20","dark:border-warning/30"],[1,"text-xs","font-medium","text-warning","dark:text-warning","uppercase","tracking-wider"],[1,"text-2xl","font-bold","text-warning","dark:text-warning"],[1,"bg-warning/20","dark:bg-warning/30","p-2","rounded-lg"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-5","h-5","text-warning","dark:text-warning"],[1,"bg-gradient-to-br","from-info/10","to-info/5","dark:from-dark-accent-primary/20","dark:to-dark-accent-primary/10","rounded-xl","p-4","border","border-info/20","dark:border-dark-accent-primary/30"],[1,"text-xs","font-medium","text-info","dark:text-dark-accent-primary","uppercase","tracking-wider"],[1,"text-2xl","font-bold","text-info","dark:text-dark-accent-primary"],[1,"bg-info/20","dark:bg-dark-accent-primary/30","p-2","rounded-lg"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"],[1,"flex","items-center","space-x-3","p-3","bg-gray-50","dark:bg-dark-bg-tertiary/50","rounded-xl"],[1,"h-10","w-10","rounded-xl","bg-gradient-to-br","from-primary","to-primary-dark","dark:from-dark-accent-primary","dark:to-dark-accent-secondary","flex","items-center","justify-center","text-white","text-sm","font-bold","shadow-lg"],[1,"flex-1"],[1,"bg-success/10","dark:bg-dark-accent-secondary/20","p-1.5","rounded-lg"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-4","h-4","text-success","dark:text-dark-accent-secondary"]],template:function(i,o){1&i&&(t.j41(0,"div",0)(1,"div",1)(2,"div",2)(3,"nav",3)(4,"a",4),t.EFF(5,"Projets"),t.k0s(),t.qSk(),t.j41(6,"svg",5),t.nrm(7,"path",6),t.k0s(),t.joV(),t.j41(8,"span",7),t.EFF(9),t.k0s()(),t.j41(10,"div",8)(11,"div",9)(12,"div",10)(13,"div",11),t.qSk(),t.j41(14,"svg",12),t.nrm(15,"path",13),t.k0s()(),t.joV(),t.j41(16,"div")(17,"h1",14),t.EFF(18),t.k0s(),t.j41(19,"div",15)(20,"div",16),t.qSk(),t.j41(21,"svg",17),t.nrm(22,"path",18),t.k0s(),t.joV(),t.j41(23,"span",19),t.EFF(24),t.k0s()(),t.j41(25,"div",16),t.qSk(),t.j41(26,"svg",20),t.nrm(27,"path",21),t.k0s(),t.joV(),t.j41(28,"span",22),t.EFF(29),t.k0s()()()()(),t.j41(30,"div",23)(31,"span",24),t.EFF(32),t.k0s()()()()(),t.j41(33,"div",25)(34,"div",26)(35,"div",27)(36,"div",28)(37,"div",29),t.qSk(),t.j41(38,"svg",30),t.nrm(39,"path",31),t.k0s()(),t.joV(),t.j41(40,"h3",32),t.EFF(41,"Description du projet"),t.k0s()(),t.j41(42,"div",33)(43,"p",34),t.EFF(44),t.k0s()()(),t.j41(45,"div",27)(46,"div",28)(47,"div",35),t.qSk(),t.j41(48,"svg",36),t.nrm(49,"path",37),t.k0s()(),t.joV(),t.j41(50,"h3",32),t.EFF(51," Fichiers joints "),t.j41(52,"span",38),t.EFF(53),t.k0s()()(),t.DNE(54,ni,2,1,"div",39),t.DNE(55,ai,7,0,"div",40),t.k0s(),t.j41(56,"div",27)(57,"div",28)(58,"div",41),t.qSk(),t.j41(59,"svg",42),t.nrm(60,"path",43)(61,"path",44),t.k0s()(),t.joV(),t.j41(62,"h3",32),t.EFF(63,"Actions disponibles"),t.k0s()(),t.j41(64,"div",45)(65,"a",46)(66,"div",47),t.qSk(),t.j41(67,"svg",48),t.nrm(68,"path",49),t.k0s(),t.joV(),t.j41(69,"span"),t.EFF(70,"Modifier"),t.k0s()()(),t.j41(71,"a",50)(72,"div",47),t.qSk(),t.j41(73,"svg",48),t.nrm(74,"path",51),t.k0s(),t.joV(),t.j41(75,"span"),t.EFF(76,"Voir rendus"),t.k0s()()(),t.j41(77,"button",52),t.bIt("click",function(){return o.deleteProjet(null==o.projet?null:o.projet._id)}),t.j41(78,"div",47),t.qSk(),t.j41(79,"svg",48),t.nrm(80,"path",53),t.k0s(),t.joV(),t.j41(81,"span"),t.EFF(82,"Supprimer"),t.k0s()()()()()(),t.j41(83,"div",54)(84,"div",55),t.DNE(85,si,5,0,"div",56),t.DNE(86,di,56,9,"div",57),t.j41(87,"div",58)(88,"div",59)(89,"div",28)(90,"div",35),t.qSk(),t.j41(91,"svg",36),t.nrm(92,"path",21),t.k0s()(),t.joV(),t.j41(93,"h3",32),t.EFF(94,"Derniers rendus"),t.k0s()(),t.j41(95,"div",60),t.DNE(96,ci,11,3,"div",61),t.DNE(97,li,7,0,"div",40),t.k0s()()()()()()()()),2&i&&(t.R7$(9),t.JRh((null==o.projet?null:o.projet.titre)||"D\xe9tails du projet"),t.R7$(9),t.SpI(" ",(null==o.projet?null:o.projet.titre)||"Chargement..."," "),t.R7$(6),t.JRh((null==o.projet?null:o.projet.groupe)||"Tous les groupes"),t.R7$(5),t.JRh(null!=o.projet&&o.projet.dateLimite?o.formatDate(null==o.projet?null:o.projet.dateLimite):"Pas de date limite"),t.R7$(2),t.Y8G("ngClass",o.getStatusClass()),t.R7$(1),t.SpI(" ",o.getProjectStatus()," "),t.R7$(12),t.SpI(" ",(null==o.projet?null:o.projet.description)||"Aucune description fournie pour ce projet."," "),t.R7$(9),t.Lme(" (",(null==o.projet||null==o.projet.fichiers?null:o.projet.fichiers.length)||0," fichier",((null==o.projet||null==o.projet.fichiers?null:o.projet.fichiers.length)||0)>1?"s":"",") "),t.R7$(1),t.Y8G("ngIf",(null==o.projet||null==o.projet.fichiers?null:o.projet.fichiers.length)>0),t.R7$(1),t.Y8G("ngIf",!(null!=o.projet&&o.projet.fichiers)||0===o.projet.fichiers.length),t.R7$(10),t.Y8G("routerLink",t.eq3(18,ui,null==o.projet?null:o.projet._id)),t.R7$(6),t.Y8G("routerLink",t.lJ4(20,mi))("queryParams",t.eq3(21,pi,null==o.projet?null:o.projet._id)),t.R7$(14),t.Y8G("ngIf",o.isLoading),t.R7$(1),t.Y8G("ngIf",!o.isLoading),t.R7$(10),t.Y8G("ngForOf",o.derniersRendus),t.R7$(1),t.Y8G("ngIf",!o.derniersRendus||0===o.derniersRendus.length))},dependencies:[p.YU,p.Sq,p.bT,b.Wk]})}}return n})();function gi(n,r){if(1&n&&(t.j41(0,"option",39),t.EFF(1),t.k0s()),2&n){const e=r.$implicit;t.Y8G("value",e),t.R7$(1),t.JRh(e)}}function fi(n,r){if(1&n&&(t.j41(0,"option",39),t.EFF(1),t.k0s()),2&n){const e=r.$implicit;t.Y8G("value",e._id),t.R7$(1),t.JRh(e.titre)}}function bi(n,r){1&n&&(t.j41(0,"div",40)(1,"div",41),t.nrm(2,"div",42)(3,"div",43),t.k0s(),t.j41(4,"p",44),t.EFF(5,"Chargement des rendus..."),t.k0s()())}function _i(n,r){if(1&n&&(t.j41(0,"div",45)(1,"div",46),t.qSk(),t.j41(2,"svg",47),t.nrm(3,"path",48),t.k0s(),t.joV(),t.j41(4,"p",49),t.EFF(5),t.k0s()()()),2&n){const e=t.XpG();t.R7$(5),t.JRh(e.error)}}function vi(n,r){1&n&&(t.qSk(),t.nrm(0,"path",31))}function xi(n,r){1&n&&(t.qSk(),t.nrm(0,"path",78))}function ki(n,r){if(1&n&&(t.j41(0,"div",24)(1,"div",79),t.qSk(),t.j41(2,"svg",80),t.nrm(3,"path",81),t.k0s()(),t.joV(),t.j41(4,"div")(5,"p",68),t.EFF(6,"Score"),t.k0s(),t.j41(7,"p",82),t.EFF(8),t.k0s()()()),2&n){const e=t.XpG().$implicit,i=t.XpG(2);t.R7$(7),t.Y8G("ngClass",i.getScoreColorClass(i.getScoreTotal(e))),t.R7$(1),t.SpI(" ",i.getScoreTotal(e),"/20 ")}}const yi=function(n){return["/admin/projects/evaluation-details",n]};function ji(n,r){if(1&n){const e=t.RV6();t.j41(0,"div",76)(1,"a",83)(2,"div",84),t.qSk(),t.j41(3,"svg",85),t.nrm(4,"path",86)(5,"path",87),t.k0s(),t.joV(),t.j41(6,"span"),t.EFF(7,"Voir l'\xe9valuation"),t.k0s()()(),t.j41(8,"button",88),t.bIt("click",function(){t.eBV(e);const o=t.XpG().$implicit,a=t.XpG(2);return t.Njj(a.navigateToEditEvaluation(o._id))}),t.j41(9,"div",84),t.qSk(),t.j41(10,"svg",85),t.nrm(11,"path",89),t.k0s(),t.joV(),t.j41(12,"span"),t.EFF(13,"Modifier"),t.k0s()()()()}if(2&n){const e=t.XpG().$implicit;t.R7$(1),t.Y8G("routerLink",t.eq3(1,yi,e._id))}}function wi(n,r){if(1&n){const e=t.RV6();t.j41(0,"div",76)(1,"button",90),t.bIt("click",function(){t.eBV(e);const o=t.XpG().$implicit,a=t.XpG(2);return t.Njj(a.evaluerRendu(o._id,"manual"))}),t.j41(2,"div",84),t.qSk(),t.j41(3,"svg",85),t.nrm(4,"path",57),t.k0s(),t.joV(),t.j41(5,"span"),t.EFF(6,"\xc9valuer manuellement"),t.k0s()()(),t.j41(7,"button",91),t.bIt("click",function(){t.eBV(e);const o=t.XpG().$implicit,a=t.XpG(2);return t.Njj(a.evaluerRendu(o._id,"ai"))}),t.j41(8,"div",84),t.qSk(),t.j41(9,"svg",85),t.nrm(10,"path",92),t.k0s(),t.joV(),t.j41(11,"span"),t.EFF(12,"\xc9valuer par IA"),t.k0s()()()()}}function Ci(n,r){if(1&n&&(t.j41(0,"div",52)(1,"div",53)(2,"div",4)(3,"div",41)(4,"div",54),t.EFF(5),t.k0s(),t.j41(6,"div",55),t.qSk(),t.j41(7,"svg",56),t.nrm(8,"path",57),t.k0s()()(),t.joV(),t.j41(9,"div",58)(10,"h3",59),t.EFF(11),t.k0s(),t.j41(12,"p",60),t.EFF(13),t.k0s(),t.j41(14,"div",61)(15,"div",62),t.qSk(),t.j41(16,"svg",25),t.nrm(17,"path",26),t.k0s(),t.joV(),t.j41(18,"span",63),t.EFF(19),t.k0s()(),t.j41(20,"div",62),t.qSk(),t.j41(21,"svg",25),t.nrm(22,"path",64),t.k0s(),t.joV(),t.j41(23,"span",63),t.EFF(24),t.k0s()()()()(),t.j41(25,"div",65)(26,"div",24)(27,"div",66),t.qSk(),t.j41(28,"svg",67),t.nrm(29,"path",64),t.k0s()(),t.joV(),t.j41(30,"div")(31,"p",68),t.EFF(32,"Soumis le"),t.k0s(),t.j41(33,"p",69),t.EFF(34),t.k0s()()(),t.j41(35,"div",24)(36,"div",70),t.qSk(),t.j41(37,"svg",71),t.DNE(38,vi,1,0,"path",72),t.DNE(39,xi,1,0,"path",73),t.k0s()(),t.joV(),t.j41(40,"div")(41,"p",68),t.EFF(42,"Statut"),t.k0s(),t.j41(43,"span",74),t.EFF(44),t.k0s()()(),t.DNE(45,ki,9,2,"div",75),t.k0s(),t.j41(46,"div",76),t.DNE(47,ji,14,3,"div",77),t.DNE(48,wi,13,0,"div",77),t.k0s()()()),2&n){const e=r.$implicit,i=t.XpG(2);t.R7$(5),t.SpI(" ",i.getInitials(e.etudiant)," "),t.R7$(6),t.SpI(" ",i.getStudentName(e.etudiant)," "),t.R7$(2),t.JRh((null==e.etudiant?null:e.etudiant.email)||"Email non disponible"),t.R7$(6),t.SpI(" ",i.getGroupName(e.etudiant)," "),t.R7$(5),t.JRh(null==e.projet?null:e.projet.titre),t.R7$(10),t.JRh(i.formatDate(e.dateSoumission)),t.R7$(2),t.Y8G("ngClass",i.getStatusIconClass(e)),t.R7$(2),t.Y8G("ngIf",e.evaluation),t.R7$(1),t.Y8G("ngIf",!e.evaluation),t.R7$(4),t.Y8G("ngClass",i.getStatusBadgeClass(e)),t.R7$(1),t.SpI(" ",i.getStatutEvaluation(e)," "),t.R7$(1),t.Y8G("ngIf",e.evaluation),t.R7$(2),t.Y8G("ngIf",e.evaluation),t.R7$(1),t.Y8G("ngIf",!e.evaluation)}}function Ei(n,r){if(1&n&&(t.j41(0,"div",50),t.DNE(1,Ci,49,14,"div",51),t.k0s()),2&n){const e=t.XpG();t.R7$(1),t.Y8G("ngForOf",e.filteredRendus)}}function Mi(n,r){if(1&n){const e=t.RV6();t.j41(0,"div",93)(1,"div",94)(2,"div",95),t.qSk(),t.j41(3,"svg",96),t.nrm(4,"path",97),t.k0s()(),t.joV(),t.j41(5,"h3",98),t.EFF(6,"Aucun rendu trouv\xe9"),t.k0s(),t.j41(7,"p",99),t.EFF(8,"Aucun rendu ne correspond \xe0 vos crit\xe8res de filtrage actuels."),t.k0s(),t.j41(9,"button",100),t.bIt("click",function(){t.eBV(e);const o=t.XpG();return t.Njj(o.resetFilters())}),t.j41(10,"div",84),t.qSk(),t.j41(11,"svg",71),t.nrm(12,"path",101),t.k0s(),t.joV(),t.j41(13,"span"),t.EFF(14,"R\xe9initialiser les filtres"),t.k0s()()()()()}}let Fi=(()=>{class n{constructor(e,i,o,a){this.rendusService=e,this.projetService=i,this.router=o,this.datePipe=a,this.rendus=[],this.filteredRendus=[],this.isLoading=!0,this.error="",this.searchTerm="",this.filterStatus="all",this.filtreGroupe="",this.filtreProjet="",this.groupes=[],this.projets=[]}ngOnInit(){this.loadRendus(),this.loadProjets(),this.extractGroupes()}loadRendus(){this.isLoading=!0,this.rendusService.getAllRendus().subscribe({next:e=>{this.rendus=e,this.extractGroupes(),this.applyFilters(),this.isLoading=!1},error:e=>{console.error("Erreur lors du chargement des rendus",e),this.error="Impossible de charger les rendus. Veuillez r\xe9essayer plus tard.",this.isLoading=!1}})}loadProjets(){this.projetService.getProjets().subscribe({next:e=>{this.projets=e},error:e=>{console.error("Erreur lors du chargement des projets",e)}})}extractGroupes(){if(this.rendus&&this.rendus.length>0){const e=new Set;this.rendus.forEach(i=>{if(i.etudiant){const o=this.getGroupName(i.etudiant);o&&"Non sp\xe9cifi\xe9"!==o&&e.add(o)}}),this.groupes=Array.from(e)}}applyFilters(){let e=this.rendus;if("evaluated"===this.filterStatus?e=e.filter(i=>i.evaluation&&i.evaluation.scores):"pending"===this.filterStatus&&(e=e.filter(i=>!i.evaluation||!i.evaluation.scores)),""!==this.searchTerm.trim()){const i=this.searchTerm.toLowerCase().trim();e=e.filter(o=>{const a=o.etudiant;if(!a)return!1;const s=(a.firstName||a.prenom||"").toLowerCase(),d=(a.lastName||a.nom||"").toLowerCase(),l=(a.fullName||a.name||a.username||"").toLowerCase(),m=(a.email||"").toLowerCase(),u=(o.projet?.titre||"").toLowerCase();return s.includes(i)||d.includes(i)||l.includes(i)||m.includes(i)||u.includes(i)})}this.filtreGroupe&&(e=e.filter(i=>this.getGroupName(i.etudiant)===this.filtreGroupe)),this.filtreProjet&&(e=e.filter(i=>i.projet?._id===this.filtreProjet)),this.filteredRendus=e}filtrerRendus(){return this.filteredRendus}onSearchChange(){this.applyFilters()}setFilterStatus(e){this.filterStatus=e,this.applyFilters()}evaluateRendu(e){this.router.navigate(["/admin/projects/evaluate",e])}evaluerRendu(e,i){this.router.navigate(["/admin/projects/evaluate",e],{queryParams:{mode:i}})}viewEvaluationDetails(e){this.router.navigate(["/admin/projects/evaluation-details",e])}getStatusClass(e){return e.evaluation&&e.evaluation.scores?"bg-green-100 text-green-800":"bg-yellow-100 text-yellow-800"}getClasseStatut(e){return this.getStatusClass(e)}getStatusText(e){return e.evaluation&&e.evaluation._id||"\xe9valu\xe9"===e.statut?"\xc9valu\xe9":"En attente"}getStatutEvaluation(e){return this.getStatusText(e)}getScoreTotal(e){if(!e.evaluation||!e.evaluation.scores)return 0;const i=e.evaluation.scores;return i.structure+i.pratiques+i.fonctionnalite+i.originalite}getScoreClass(e){return e>=16?"text-green-600":e>=12?"text-blue-600":e>=8?"text-yellow-600":"text-red-600"}formatDate(e){return e&&this.datePipe.transform(e,"dd/MM/yyyy")||""}navigateToEditEvaluation(e){this.router.navigate(["/admin/projects/edit-evaluation",e])}getFileUrl(e){if(!e)return"";let i=e;if(e.includes("/")||e.includes("\\")){const o=e.split(/[\/\\]/);i=o[o.length-1]}return`${E.c.urlBackend}projets/telecharger/${i}`}getFileName(e){if(!e)return"Fichier";if(e.includes("/")||e.includes("\\")){const i=e.split(/[\/\\]/);return i[i.length-1]}return e}getInitials(e){if(!e)return"??";const i=e.firstName||"",o=e.lastName||"";if(i&&o&&o.trim())return(i.charAt(0)+o.charAt(0)).toUpperCase();const a=e.fullName||e.name||e.username||"";if(a&&a.trim()){const s=a.trim().split(" ");return s.length>=2?(s[0].charAt(0)+s[1].charAt(0)).toUpperCase():a.substring(0,2).toUpperCase()}return i&&i.trim()?i.substring(0,2).toUpperCase():"??"}getGroupName(e){return e?e.group&&"object"==typeof e.group&&e.group.name?e.group.name:e.group&&"string"==typeof e.group&&e.group.trim()?e.group.trim():e.groupe&&"string"==typeof e.groupe&&e.groupe.trim()?e.groupe.trim():e.groupName&&"string"==typeof e.groupName&&e.groupName.trim()?e.groupName.trim():e.department&&"string"==typeof e.department&&e.department.trim()?e.department.trim():"Non sp\xe9cifi\xe9":"Non sp\xe9cifi\xe9"}getStudentName(e){if(!e)return"Utilisateur inconnu";const i=e.firstName||"",o=e.lastName||"";if(i&&o&&o.trim())return`${i} ${o}`.trim();const a=e.fullName||e.name||e.username||"";return a&&a.trim()?a.trim():i&&i.trim()?i.trim():e.email?e.email:"Utilisateur inconnu"}getEvaluatedCount(){return this.rendus.filter(e=>e.evaluation&&e.evaluation._id).length}getStatusIconClass(e){return e.evaluation&&e.evaluation._id?"bg-success/10 dark:bg-dark-accent-secondary/10 text-success dark:text-dark-accent-secondary":"bg-warning/10 dark:bg-warning/20 text-warning dark:text-warning"}getStatusBadgeClass(e){return e.evaluation&&e.evaluation._id?"bg-gradient-to-r from-success/20 to-success-dark/20 dark:from-dark-accent-secondary/30 dark:to-dark-accent-secondary/20 text-success-dark dark:text-dark-accent-secondary border border-success/30 dark:border-dark-accent-secondary/40":"bg-gradient-to-r from-warning/20 to-warning/30 dark:from-warning/30 dark:to-warning/20 text-warning-dark dark:text-warning border border-warning/40 dark:border-warning/50"}getScoreColorClass(e){return e>=16?"text-success dark:text-dark-accent-secondary":e>=12?"text-info dark:text-dark-accent-primary":e>=8?"text-warning dark:text-warning":"text-danger dark:text-danger-dark"}resetFilters(){this.filtreGroupe="",this.filtreProjet="",this.filterStatus="all",this.searchTerm="",this.applyFilters()}static{this.\u0275fac=function(i){return new(i||n)(t.rXU(P.R),t.rXU(R.e),t.rXU(b.Ix),t.rXU(p.vh))}}static{this.\u0275cmp=t.VBU({type:n,selectors:[["app-list-rendus"]],features:[t.Jv_([p.vh])],decls:73,vars:11,consts:[[1,"min-h-screen","bg-[#edf1f4]","dark:bg-dark-bg-primary","transition-colors","duration-300"],[1,"container","mx-auto","px-4","py-8"],[1,"bg-gradient-to-r","from-primary","to-primary-dark","dark:from-dark-accent-primary","dark:to-dark-accent-secondary","rounded-2xl","p-8","mb-8","shadow-xl"],[1,"flex","items-center","justify-between"],[1,"flex","items-center","space-x-4"],[1,"bg-white/20","dark:bg-black/20","p-3","rounded-xl","backdrop-blur-sm"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-8","h-8","text-white"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"],[1,"text-3xl","font-bold","text-white","mb-2"],[1,"text-white/80"],[1,"hidden","md:flex","items-center","space-x-4","text-white/80"],[1,"text-center"],[1,"text-2xl","font-bold"],[1,"text-sm"],[1,"w-px","h-12","bg-white/20"],[1,"bg-white/80","dark:bg-dark-bg-secondary/80","backdrop-blur-sm","rounded-2xl","p-6","mb-8","shadow-lg","border","border-gray-200/50","dark:border-dark-bg-tertiary/50"],[1,"flex","items-center","space-x-3","mb-6"],[1,"bg-gradient-to-r","from-primary","to-primary-dark","dark:from-dark-accent-primary","dark:to-dark-accent-secondary","p-2","rounded-lg"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-5","h-5","text-white"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"],[1,"text-xl","font-bold","text-text-dark","dark:text-dark-text-primary"],[1,"grid","grid-cols-1","md:grid-cols-3","gap-6"],[1,"space-y-2"],[1,"block","text-sm","font-semibold","text-text-dark","dark:text-dark-text-primary"],[1,"flex","items-center","space-x-2"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-4","h-4","text-primary","dark:text-dark-accent-primary"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"],[1,"w-full","px-4","py-3","bg-white","dark:bg-dark-bg-secondary","border-2","border-gray-200","dark:border-dark-bg-tertiary","rounded-xl","focus:outline-none","focus:border-primary","dark:focus:border-dark-accent-primary","focus:ring-4","focus:ring-primary/10","dark:focus:ring-dark-accent-primary/20","transition-all","duration-200","text-text-dark","dark:text-dark-text-primary",3,"ngModel","ngModelChange"],["value",""],[3,"value",4,"ngFor","ngForOf"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M19 11H5m14-7H3a2 2 0 00-2 2v12a2 2 0 002 2h16a2 2 0 002-2V6a2 2 0 00-2-2z"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"],["value","all"],["value","evaluated"],["value","pending"],["class","flex flex-col items-center justify-center py-16",4,"ngIf"],["class","bg-danger/10 dark:bg-danger-dark/20 border border-danger/30 dark:border-danger-dark/40 text-danger dark:text-danger-dark rounded-xl p-4 mb-6 backdrop-blur-sm",4,"ngIf"],["class","space-y-6",4,"ngIf"],["class","text-center py-16",4,"ngIf"],[3,"value"],[1,"flex","flex-col","items-center","justify-center","py-16"],[1,"relative"],[1,"animate-spin","rounded-full","h-16","w-16","border-4","border-primary/30","dark:border-dark-accent-primary/30"],[1,"animate-spin","rounded-full","h-16","w-16","border-4","border-transparent","border-t-primary","dark:border-t-dark-accent-primary","absolute","top-0","left-0"],[1,"mt-4","text-text","dark:text-dark-text-secondary","animate-pulse"],[1,"bg-danger/10","dark:bg-danger-dark/20","border","border-danger/30","dark:border-danger-dark/40","text-danger","dark:text-danger-dark","rounded-xl","p-4","mb-6","backdrop-blur-sm"],[1,"flex","items-center","space-x-3"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-5","h-5","text-danger","dark:text-danger-dark","flex-shrink-0"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"],[1,"font-medium"],[1,"space-y-6"],["class","bg-white/80 dark:bg-dark-bg-secondary/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-gray-200/50 dark:border-dark-bg-tertiary/50 hover:shadow-xl transition-all duration-300 group",4,"ngFor","ngForOf"],[1,"bg-white/80","dark:bg-dark-bg-secondary/80","backdrop-blur-sm","rounded-2xl","p-6","shadow-lg","border","border-gray-200/50","dark:border-dark-bg-tertiary/50","hover:shadow-xl","transition-all","duration-300","group"],[1,"flex","flex-col","lg:flex-row","lg:items-center","lg:justify-between","space-y-4","lg:space-y-0"],[1,"h-16","w-16","rounded-2xl","bg-gradient-to-br","from-primary","to-primary-dark","dark:from-dark-accent-primary","dark:to-dark-accent-secondary","flex","items-center","justify-center","text-white","text-lg","font-bold","shadow-lg"],[1,"absolute","-bottom-1","-right-1","w-6","h-6","bg-gradient-to-r","from-success","to-success-dark","dark:from-dark-accent-secondary","dark:to-dark-accent-primary","rounded-full","flex","items-center","justify-center"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-3","h-3","text-white"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"],[1,"flex-1"],[1,"text-lg","font-bold","text-text-dark","dark:text-dark-text-primary"],[1,"text-sm","text-text","dark:text-dark-text-secondary"],[1,"flex","items-center","space-x-4","mt-2"],[1,"flex","items-center","space-x-1"],[1,"text-sm","font-medium","text-text-dark","dark:text-dark-text-primary"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 0h6m-6 0l-2 13a2 2 0 002 2h6a2 2 0 002-2L16 7"],[1,"flex","flex-col","sm:flex-row","sm:items-center","space-y-3","sm:space-y-0","sm:space-x-6"],[1,"bg-info/10","dark:bg-dark-accent-primary/10","p-2","rounded-lg"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-4","h-4","text-info","dark:text-dark-accent-primary"],[1,"text-xs","text-text","dark:text-dark-text-secondary"],[1,"text-sm","font-semibold","text-text-dark","dark:text-dark-text-primary"],[1,"p-2","rounded-lg",3,"ngClass"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-4","h-4"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z",4,"ngIf"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z",4,"ngIf"],[1,"inline-flex","items-center","px-3","py-1","rounded-full","text-xs","font-semibold",3,"ngClass"],["class","flex items-center space-x-2",4,"ngIf"],[1,"flex","flex-col","sm:flex-row","gap-2"],["class","flex flex-col sm:flex-row gap-2",4,"ngIf"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"],[1,"bg-success/10","dark:bg-dark-accent-secondary/10","p-2","rounded-lg"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-4","h-4","text-success","dark:text-dark-accent-secondary"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"],[1,"text-sm","font-bold",3,"ngClass"],[1,"group/btn","px-4","py-2","bg-gradient-to-r","from-info","to-primary","dark:from-dark-accent-primary","dark:to-dark-accent-secondary","text-white","rounded-xl","hover:shadow-lg","hover:scale-105","transition-all","duration-200","font-medium","text-center",3,"routerLink"],[1,"flex","items-center","justify-center","space-x-2"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-4","h-4","group-hover/btn:scale-110","transition-transform"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M15 12a3 3 0 11-6 0 3 3 0 016 0z"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"],[1,"group/btn","px-4","py-2","bg-gradient-to-r","from-secondary","to-primary-dark","dark:from-dark-accent-secondary","dark:to-dark-accent-primary","text-white","rounded-xl","hover:shadow-lg","hover:scale-105","transition-all","duration-200","font-medium",3,"click"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"],[1,"group/btn","px-4","py-2","bg-gradient-to-r","from-success","to-success-dark","dark:from-dark-accent-primary","dark:to-dark-accent-secondary","text-white","rounded-xl","hover:shadow-lg","hover:scale-105","transition-all","duration-200","font-medium",3,"click"],[1,"group/btn","px-4","py-2","bg-gradient-to-r","from-secondary","to-primary","dark:from-dark-accent-secondary","dark:to-dark-accent-primary","text-white","rounded-xl","hover:shadow-lg","hover:scale-105","transition-all","duration-200","font-medium",3,"click"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"],[1,"text-center","py-16"],[1,"bg-white/80","dark:bg-dark-bg-secondary/80","backdrop-blur-sm","rounded-2xl","p-12","shadow-lg","border","border-gray-200/50","dark:border-dark-bg-tertiary/50","max-w-md","mx-auto"],[1,"bg-gradient-to-br","from-primary/10","to-secondary/10","dark:from-dark-accent-primary/20","dark:to-dark-accent-secondary/20","rounded-2xl","p-6","mb-6"],["fill","none","viewBox","0 0 24 24","stroke","currentColor",1,"h-16","w-16","mx-auto","text-primary","dark:text-dark-accent-primary"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","1.5","d","M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"],[1,"text-xl","font-bold","text-text-dark","dark:text-dark-text-primary","mb-2"],[1,"text-text","dark:text-dark-text-secondary","mb-4"],[1,"px-6","py-2","bg-gradient-to-r","from-primary","to-primary-dark","dark:from-dark-accent-primary","dark:to-dark-accent-secondary","text-white","rounded-xl","hover:shadow-lg","hover:scale-105","transition-all","duration-200","font-medium",3,"click"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"]],template:function(i,o){1&i&&(t.j41(0,"div",0)(1,"div",1)(2,"div",2)(3,"div",3)(4,"div",4)(5,"div",5),t.qSk(),t.j41(6,"svg",6),t.nrm(7,"path",7),t.k0s()(),t.joV(),t.j41(8,"div")(9,"h1",8),t.EFF(10,"Liste des Rendus"),t.k0s(),t.j41(11,"p",9),t.EFF(12,"Gestion et \xe9valuation des projets \xe9tudiants"),t.k0s()()(),t.j41(13,"div",10)(14,"div",11)(15,"div",12),t.EFF(16),t.k0s(),t.j41(17,"div",13),t.EFF(18,"Total"),t.k0s()(),t.nrm(19,"div",14),t.j41(20,"div",11)(21,"div",12),t.EFF(22),t.k0s(),t.j41(23,"div",13),t.EFF(24,"\xc9valu\xe9s"),t.k0s()()()()(),t.j41(25,"div",15)(26,"div",16)(27,"div",17),t.qSk(),t.j41(28,"svg",18),t.nrm(29,"path",19),t.k0s()(),t.joV(),t.j41(30,"h2",20),t.EFF(31,"Filtres et recherche"),t.k0s()(),t.j41(32,"div",21)(33,"div",22)(34,"label",23)(35,"div",24),t.qSk(),t.j41(36,"svg",25),t.nrm(37,"path",26),t.k0s(),t.joV(),t.j41(38,"span"),t.EFF(39,"Groupe"),t.k0s()()(),t.j41(40,"select",27),t.bIt("ngModelChange",function(s){return o.filtreGroupe=s})("ngModelChange",function(){return o.applyFilters()}),t.j41(41,"option",28),t.EFF(42,"Tous les groupes"),t.k0s(),t.DNE(43,gi,2,2,"option",29),t.k0s()(),t.j41(44,"div",22)(45,"label",23)(46,"div",24),t.qSk(),t.j41(47,"svg",25),t.nrm(48,"path",30),t.k0s(),t.joV(),t.j41(49,"span"),t.EFF(50,"Projet"),t.k0s()()(),t.j41(51,"select",27),t.bIt("ngModelChange",function(s){return o.filtreProjet=s})("ngModelChange",function(){return o.applyFilters()}),t.j41(52,"option",28),t.EFF(53,"Tous les projets"),t.k0s(),t.DNE(54,fi,2,2,"option",29),t.k0s()(),t.j41(55,"div",22)(56,"label",23)(57,"div",24),t.qSk(),t.j41(58,"svg",25),t.nrm(59,"path",31),t.k0s(),t.joV(),t.j41(60,"span"),t.EFF(61,"Statut"),t.k0s()()(),t.j41(62,"select",27),t.bIt("ngModelChange",function(s){return o.filterStatus=s})("ngModelChange",function(){return o.applyFilters()}),t.j41(63,"option",32),t.EFF(64,"Tous les statuts"),t.k0s(),t.j41(65,"option",33),t.EFF(66,"\xc9valu\xe9s"),t.k0s(),t.j41(67,"option",34),t.EFF(68,"En attente"),t.k0s()()()()(),t.DNE(69,bi,6,0,"div",35),t.DNE(70,_i,6,1,"div",36),t.DNE(71,Ei,2,1,"div",37),t.DNE(72,Mi,15,0,"div",38),t.k0s()()),2&i&&(t.R7$(16),t.JRh(o.rendus.length),t.R7$(6),t.JRh(o.getEvaluatedCount()),t.R7$(18),t.Y8G("ngModel",o.filtreGroupe),t.R7$(3),t.Y8G("ngForOf",o.groupes),t.R7$(8),t.Y8G("ngModel",o.filtreProjet),t.R7$(3),t.Y8G("ngForOf",o.projets),t.R7$(8),t.Y8G("ngModel",o.filterStatus),t.R7$(7),t.Y8G("ngIf",o.isLoading),t.R7$(1),t.Y8G("ngIf",o.error),t.R7$(1),t.Y8G("ngIf",!o.isLoading&&o.filteredRendus.length>0),t.R7$(1),t.Y8G("ngIf",!o.isLoading&&0===o.filteredRendus.length))},dependencies:[p.YU,p.Sq,p.bT,b.Wk,c.xH,c.y7,c.wz,c.BC,c.vS],styles:['.loading-spinner[_ngcontent-%COMP%]{display:flex;justify-content:center;margin:2rem 0}.error-message[_ngcontent-%COMP%]{color:#dc3545;margin-top:.25rem}@keyframes _ngcontent-%COMP%_fadeInUp{0%{opacity:0;transform:translateY(20px)}to{opacity:1;transform:translateY(0)}}@keyframes _ngcontent-%COMP%_slideInRight{0%{opacity:0;transform:translate(30px)}to{opacity:1;transform:translate(0)}}@keyframes _ngcontent-%COMP%_scaleIn{0%{opacity:0;transform:scale(.9)}to{opacity:1;transform:scale(1)}}.glass-card[_ngcontent-%COMP%]{backdrop-filter:blur(10px);-webkit-backdrop-filter:blur(10px);border:1px solid rgba(255,255,255,.2)}.dark[_ngcontent-%COMP%]   .glass-card[_ngcontent-%COMP%]{border:1px solid rgba(255,255,255,.1)}.rendu-card[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_fadeInUp .6s ease-out;transition:all .3s cubic-bezier(.4,0,.2,1)}.rendu-card[_ngcontent-%COMP%]:hover{transform:translateY(-4px);box-shadow:0 20px 40px #0000001a}.dark[_ngcontent-%COMP%]   .rendu-card[_ngcontent-%COMP%]:hover{box-shadow:0 20px 40px #0000004d}.btn-modern[_ngcontent-%COMP%]{position:relative;overflow:hidden;transition:all .3s cubic-bezier(.4,0,.2,1)}.btn-modern[_ngcontent-%COMP%]:before{content:"";position:absolute;top:0;left:-100%;width:100%;height:100%;background:linear-gradient(90deg,transparent,rgba(255,255,255,.2),transparent);transition:left .5s}.btn-modern[_ngcontent-%COMP%]:hover:before{left:100%}.avatar-gradient[_ngcontent-%COMP%]{background:linear-gradient(135deg,#4f5fad 0%,#7826b5 100%);transition:all .3s ease}.dark[_ngcontent-%COMP%]   .avatar-gradient[_ngcontent-%COMP%]{background:linear-gradient(135deg,#00f7ff 0%,#9d4edd 100%)}.avatar-gradient[_ngcontent-%COMP%]:hover{transform:scale(1.05);box-shadow:0 8px 25px #4f5fad4d}.dark[_ngcontent-%COMP%]   .avatar-gradient[_ngcontent-%COMP%]:hover{box-shadow:0 8px 25px #00f7ff4d}.status-badge[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_scaleIn .4s ease-out;transition:all .2s ease}.status-badge[_ngcontent-%COMP%]:hover{transform:scale(1.05)}.filter-select[_ngcontent-%COMP%]{transition:all .3s cubic-bezier(.4,0,.2,1)}.filter-select[_ngcontent-%COMP%]:focus{transform:translateY(-2px);box-shadow:0 8px 25px #4f5fad26}.dark[_ngcontent-%COMP%]   .filter-select[_ngcontent-%COMP%]:focus{box-shadow:0 8px 25px #00f7ff26}.header-gradient[_ngcontent-%COMP%]{background:linear-gradient(135deg,#4f5fad 0%,#7826b5 100%);animation:_ngcontent-%COMP%_slideInRight .8s ease-out}.dark[_ngcontent-%COMP%]   .header-gradient[_ngcontent-%COMP%]{background:linear-gradient(135deg,#00f7ff 0%,#9d4edd 100%)}@media (max-width: 768px){.rendu-card[_ngcontent-%COMP%]{margin-bottom:1rem}.btn-modern[_ngcontent-%COMP%]{width:100%;justify-content:center}.filter-grid[_ngcontent-%COMP%]{grid-template-columns:1fr;gap:1rem}}.icon-hover[_ngcontent-%COMP%]{transition:transform .2s ease}.icon-hover[_ngcontent-%COMP%]:hover{transform:scale(1.1) rotate(5deg)}.tooltip[_ngcontent-%COMP%]{position:relative}.tooltip[_ngcontent-%COMP%]:after{content:attr(data-tooltip);position:absolute;bottom:100%;left:50%;transform:translate(-50%);background:rgba(0,0,0,.8);color:#fff;padding:.5rem;border-radius:.375rem;font-size:.75rem;white-space:nowrap;opacity:0;pointer-events:none;transition:opacity .3s;z-index:1000}.tooltip[_ngcontent-%COMP%]:hover:after{opacity:1}.loading-pulse[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_pulse 2s infinite}@keyframes _ngcontent-%COMP%_pulse{0%,to{opacity:1}50%{opacity:.5}}.empty-state[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_fadeInUp .8s ease-out}.focus-visible[_ngcontent-%COMP%]:focus{outline:2px solid #4f5fad;outline-offset:2px}.dark[_ngcontent-%COMP%]   .focus-visible[_ngcontent-%COMP%]:focus{outline:2px solid #00f7ff}']})}}return n})();function Di(n,r){1&n&&(t.j41(0,"div",13)(1,"div",14),t.nrm(2,"div",15)(3,"div",16),t.k0s(),t.j41(4,"p",17),t.EFF(5,"Chargement des donn\xe9es..."),t.k0s()())}function Oi(n,r){if(1&n&&(t.j41(0,"div",18)(1,"div",19),t.qSk(),t.j41(2,"svg",20),t.nrm(3,"path",21),t.k0s(),t.joV(),t.j41(4,"p",22),t.EFF(5),t.k0s()()()),2&n){const e=t.XpG();t.R7$(5),t.JRh(e.error)}}function Pi(n,r){if(1&n&&(t.j41(0,"a",56),t.qSk(),t.j41(1,"svg",57),t.nrm(2,"path",58),t.k0s(),t.joV(),t.j41(3,"span",59),t.EFF(4),t.k0s()()),2&n){const e=t.XpG().$implicit;t.Y8G("href","http://localhost:3000/"+e,t.B4B),t.R7$(4),t.JRh((null==e?null:e.split("/").pop())||"Fichier")}}function Ri(n,r){if(1&n&&(t.qex(0),t.DNE(1,Pi,5,2,"a",55),t.bVm()),2&n){const e=r.$implicit;t.R7$(1),t.Y8G("ngIf",e)}}function Si(n,r){if(1&n&&(t.j41(0,"div",49)(1,"div",50),t.qSk(),t.j41(2,"svg",51),t.nrm(3,"path",52),t.k0s(),t.joV(),t.j41(4,"h3",38),t.EFF(5),t.k0s()(),t.j41(6,"div",53),t.DNE(7,Ri,2,1,"ng-container",54),t.k0s()()),2&n){const e=t.XpG(2);t.R7$(5),t.SpI("Fichiers joints (",e.rendu.fichiers.length,")"),t.R7$(2),t.Y8G("ngForOf",e.rendu.fichiers)}}function Ii(n,r){1&n&&(t.qSk(),t.j41(0,"svg",107),t.nrm(1,"path",7),t.k0s())}function Ti(n,r){1&n&&(t.qSk(),t.j41(0,"svg",108),t.nrm(1,"path",109),t.k0s())}function Ai(n,r){1&n&&(t.j41(0,"span"),t.EFF(1,"Soumettre l'\xe9valuation"),t.k0s())}function Li(n,r){1&n&&(t.j41(0,"span"),t.EFF(1,"Soumission en cours..."),t.k0s())}function Bi(n,r){if(1&n){const e=t.RV6();t.qSk(),t.joV(),t.j41(0,"form",60),t.bIt("ngSubmit",function(){t.eBV(e);const o=t.XpG(2);return t.Njj(o.onSubmit())}),t.j41(1,"div",61)(2,"div",25)(3,"div",62),t.qSk(),t.j41(4,"svg",27),t.nrm(5,"path",63),t.k0s()(),t.joV(),t.j41(6,"h3",64),t.EFF(7,"Crit\xe8res d'\xe9valuation"),t.k0s()(),t.j41(8,"div",65)(9,"div",66)(10,"label",67)(11,"div",68),t.qSk(),t.j41(12,"svg",69),t.nrm(13,"path",70),t.k0s(),t.joV(),t.j41(14,"span"),t.EFF(15,"Structure du code"),t.k0s()()(),t.j41(16,"div",14),t.nrm(17,"input",71),t.j41(18,"div",72)(19,"span",73),t.EFF(20,"/5"),t.k0s()()()(),t.j41(21,"div",66)(22,"label",67)(23,"div",68),t.qSk(),t.j41(24,"svg",69),t.nrm(25,"path",7),t.k0s(),t.joV(),t.j41(26,"span"),t.EFF(27,"Bonnes pratiques"),t.k0s()()(),t.j41(28,"div",14),t.nrm(29,"input",74),t.j41(30,"div",72)(31,"span",73),t.EFF(32,"/5"),t.k0s()()()(),t.j41(33,"div",66)(34,"label",67)(35,"div",68),t.qSk(),t.j41(36,"svg",69),t.nrm(37,"path",75)(38,"path",76),t.k0s(),t.joV(),t.j41(39,"span"),t.EFF(40,"Fonctionnalit\xe9"),t.k0s()()(),t.j41(41,"div",14),t.nrm(42,"input",77),t.j41(43,"div",72)(44,"span",73),t.EFF(45,"/5"),t.k0s()()()(),t.j41(46,"div",66)(47,"label",67)(48,"div",68),t.qSk(),t.j41(49,"svg",69),t.nrm(50,"path",41),t.k0s(),t.joV(),t.j41(51,"span"),t.EFF(52,"Originalit\xe9"),t.k0s()()(),t.j41(53,"div",14),t.nrm(54,"input",78),t.j41(55,"div",72)(56,"span",73),t.EFF(57,"/5"),t.k0s()()()()(),t.j41(58,"div",79)(59,"div",80)(60,"div",19)(61,"div",26),t.qSk(),t.j41(62,"svg",27),t.nrm(63,"path",63),t.k0s()(),t.joV(),t.j41(64,"span",81),t.EFF(65,"Score total"),t.k0s()(),t.j41(66,"div",82)(67,"div",83),t.EFF(68),t.k0s(),t.j41(69,"div",84),t.EFF(70),t.k0s()()(),t.j41(71,"div",85),t.nrm(72,"div",86),t.k0s(),t.j41(73,"div",87)(74,"span"),t.EFF(75,"0"),t.k0s(),t.j41(76,"span"),t.EFF(77),t.k0s()()()(),t.j41(78,"div",61)(79,"div",25)(80,"div",88),t.qSk(),t.j41(81,"svg",27),t.nrm(82,"path",89),t.k0s()(),t.joV(),t.j41(83,"h3",64),t.EFF(84,"Commentaires d\xe9taill\xe9s"),t.k0s()(),t.j41(85,"div",14),t.nrm(86,"textarea",90),t.j41(87,"div",91),t.EFF(88," Minimum 50 caract\xe8res "),t.k0s()(),t.j41(89,"div",92),t.qSk(),t.j41(90,"svg",93),t.nrm(91,"path",94),t.k0s(),t.joV(),t.j41(92,"div",95)(93,"p",96),t.EFF(94,"Conseils pour une \xe9valuation de qualit\xe9 :"),t.k0s(),t.j41(95,"ul",97)(96,"li"),t.EFF(97,"\u2022 Mentionnez les aspects techniques r\xe9ussis"),t.k0s(),t.j41(98,"li"),t.EFF(99,"\u2022 Identifiez les points d'am\xe9lioration"),t.k0s(),t.j41(100,"li"),t.EFF(101,"\u2022 Proposez des suggestions constructives"),t.k0s(),t.j41(102,"li"),t.EFF(103,"\u2022 \xc9valuez la cr\xe9ativit\xe9 et l'originalit\xe9"),t.k0s()()()()(),t.j41(104,"div",98)(105,"button",99),t.bIt("click",function(){t.eBV(e);const o=t.XpG(2);return t.Njj(o.annuler())}),t.j41(106,"div",100),t.qSk(),t.j41(107,"svg",101),t.nrm(108,"path",102),t.k0s(),t.joV(),t.j41(109,"span"),t.EFF(110,"Annuler"),t.k0s()()(),t.j41(111,"button",103)(112,"div",100),t.DNE(113,Ii,2,0,"svg",104),t.DNE(114,Ti,2,0,"svg",105),t.DNE(115,Ai,2,0,"span",106),t.DNE(116,Li,2,0,"span",106),t.k0s()()()()}if(2&n){const e=t.XpG(2);t.Y8G("formGroup",e.evaluationForm),t.R7$(68),t.JRh(e.getScoreTotal()),t.R7$(2),t.SpI("sur ",e.getScoreMaximum(),""),t.R7$(2),t.xc7("width",e.getScoreTotal()/e.getScoreMaximum()*100,"%"),t.R7$(5),t.JRh(e.getScoreMaximum()),t.R7$(36),t.Y8G("ngIf",!e.isSubmitting),t.R7$(1),t.Y8G("ngIf",e.isSubmitting),t.R7$(1),t.Y8G("ngIf",!e.isSubmitting),t.R7$(1),t.Y8G("ngIf",e.isSubmitting)}}function Ni(n,r){1&n&&(t.qSk(),t.j41(0,"svg",107),t.nrm(1,"path",125),t.k0s())}function Vi(n,r){1&n&&(t.qSk(),t.j41(0,"svg",108),t.nrm(1,"path",109),t.k0s())}function zi(n,r){1&n&&(t.j41(0,"span"),t.EFF(1,"Lancer l'\xe9valuation IA"),t.k0s())}function $i(n,r){1&n&&(t.j41(0,"span"),t.EFF(1,"Lancement en cours..."),t.k0s())}function Gi(n,r){if(1&n){const e=t.RV6();t.j41(0,"div")(1,"div",25)(2,"div",112),t.qSk(),t.j41(3,"svg",113),t.nrm(4,"path",41),t.k0s()(),t.joV(),t.j41(5,"div")(6,"h3",29),t.EFF(7,"\xc9valuation automatique par IA"),t.k0s(),t.j41(8,"p",114),t.EFF(9,"Syst\xe8me d'intelligence artificielle Mistral 7B"),t.k0s()()(),t.j41(10,"div",115)(11,"div",116)(12,"div",117),t.qSk(),t.j41(13,"svg",27),t.nrm(14,"path",94),t.k0s()(),t.joV(),t.j41(15,"div")(16,"h4",118),t.EFF(17,"Comment fonctionne l'\xe9valuation IA ?"),t.k0s(),t.j41(18,"p",119),t.EFF(19,"Notre syst\xe8me d'IA analysera automatiquement le code soumis selon les crit\xe8res suivants :"),t.k0s(),t.j41(20,"div",120)(21,"div",68),t.nrm(22,"div",121),t.j41(23,"span",122),t.EFF(24,"Structure et organisation"),t.k0s()(),t.j41(25,"div",68),t.nrm(26,"div",121),t.j41(27,"span",122),t.EFF(28,"Bonnes pratiques"),t.k0s()(),t.j41(29,"div",68),t.nrm(30,"div",121),t.j41(31,"span",122),t.EFF(32,"Fonctionnalit\xe9s"),t.k0s()(),t.j41(33,"div",68),t.nrm(34,"div",121),t.j41(35,"span",122),t.EFF(36,"Originalit\xe9"),t.k0s()()()()()(),t.j41(37,"div",123)(38,"button",99),t.bIt("click",function(){t.eBV(e);const o=t.XpG(3);return t.Njj(o.annuler())}),t.j41(39,"div",100),t.qSk(),t.j41(40,"svg",101),t.nrm(41,"path",102),t.k0s(),t.joV(),t.j41(42,"span"),t.EFF(43,"Annuler"),t.k0s()()(),t.j41(44,"button",124),t.bIt("click",function(){t.eBV(e);const o=t.XpG(3);return t.Njj(o.onSubmit())}),t.j41(45,"div",100),t.DNE(46,Ni,2,0,"svg",104),t.DNE(47,Vi,2,0,"svg",105),t.DNE(48,zi,2,0,"span",106),t.DNE(49,$i,2,0,"span",106),t.k0s()()()()}if(2&n){const e=t.XpG(3);t.R7$(44),t.Y8G("disabled",e.isSubmitting),t.R7$(2),t.Y8G("ngIf",!e.isSubmitting),t.R7$(1),t.Y8G("ngIf",e.isSubmitting),t.R7$(1),t.Y8G("ngIf",!e.isSubmitting),t.R7$(1),t.Y8G("ngIf",e.isSubmitting)}}function Yi(n,r){1&n&&(t.j41(0,"div",126)(1,"div",127),t.nrm(2,"div",128)(3,"div",129),t.j41(4,"div",130),t.qSk(),t.j41(5,"svg",131),t.nrm(6,"path",41),t.k0s()()(),t.joV(),t.j41(7,"h3",132),t.EFF(8,"L'IA analyse le projet..."),t.k0s(),t.j41(9,"p",133),t.EFF(10,"Notre syst\xe8me examine le code selon les crit\xe8res d'\xe9valuation"),t.k0s(),t.j41(11,"div",134)(12,"div",135),t.nrm(13,"div",136)(14,"div",137)(15,"div",138),t.k0s(),t.j41(16,"p",84),t.EFF(17,"Cela peut prendre quelques instants"),t.k0s()()())}function qi(n,r){if(1&n&&(t.qSk(),t.joV(),t.j41(0,"div",110),t.DNE(1,Gi,50,5,"div",106),t.DNE(2,Yi,18,0,"div",111),t.k0s()),2&n){const e=t.XpG(2);t.R7$(1),t.Y8G("ngIf",!e.aiProcessing),t.R7$(1),t.Y8G("ngIf",e.aiProcessing)}}function Hi(n,r){if(1&n){const e=t.RV6();t.j41(0,"div",23)(1,"div",24)(2,"div",25)(3,"div",26),t.qSk(),t.j41(4,"svg",27),t.nrm(5,"path",28),t.k0s()(),t.joV(),t.j41(6,"h2",29),t.EFF(7,"Informations sur le rendu"),t.k0s()(),t.j41(8,"div",30)(9,"div",31)(10,"div",32)(11,"p",33),t.EFF(12,"Projet"),t.k0s(),t.j41(13,"p",34),t.EFF(14),t.k0s()(),t.j41(15,"div",35)(16,"p",33),t.EFF(17,"\xc9tudiant"),t.k0s(),t.j41(18,"p",34),t.EFF(19),t.k0s()()(),t.j41(20,"div",31)(21,"div",36)(22,"p",33),t.EFF(23,"Date de soumission"),t.k0s(),t.j41(24,"p",34),t.EFF(25),t.nI1(26,"date"),t.k0s()(),t.j41(27,"div",37)(28,"p",33),t.EFF(29,"Description"),t.k0s(),t.j41(30,"p",38),t.EFF(31),t.k0s()()()(),t.DNE(32,Si,8,2,"div",39),t.k0s(),t.j41(33,"div",24)(34,"div",40)(35,"div",19)(36,"div",26),t.qSk(),t.j41(37,"svg",27),t.nrm(38,"path",41),t.k0s()(),t.joV(),t.j41(39,"h2",29),t.EFF(40,"Mode d'\xe9valuation"),t.k0s()(),t.j41(41,"div",4)(42,"div",42)(43,"button",43),t.bIt("click",function(){t.eBV(e);const o=t.XpG();return t.Njj(o.evaluationMode="manual")}),t.EFF(44," Manuel "),t.k0s(),t.j41(45,"button",43),t.bIt("click",function(){t.eBV(e);const o=t.XpG();return t.Njj(o.evaluationMode="ai")}),t.EFF(46," IA "),t.k0s()(),t.j41(47,"button",44),t.bIt("click",function(){t.eBV(e);const o=t.XpG();return t.Njj(o.toggleEvaluationMode())}),t.qSk(),t.j41(48,"svg",45),t.nrm(49,"path",46),t.k0s(),t.EFF(50," Basculer "),t.k0s()()(),t.DNE(51,Bi,117,10,"form",47),t.DNE(52,qi,3,2,"div",48),t.k0s()()}if(2&n){const e=t.XpG();t.R7$(14),t.JRh(e.rendu.projet.titre),t.R7$(5),t.Lme("",e.rendu.etudiant.nom," ",e.rendu.etudiant.prenom,""),t.R7$(6),t.JRh(t.i5U(26,12,e.rendu.dateSoumission,"dd/MM/yyyy HH:mm")),t.R7$(6),t.JRh(e.rendu.description||"Aucune description"),t.R7$(1),t.Y8G("ngIf",e.rendu.fichiers&&e.rendu.fichiers.length>0),t.R7$(11),t.HbH("manual"===e.evaluationMode?"bg-white dark:bg-dark-bg-secondary text-primary dark:text-dark-accent-primary shadow-md":"text-text dark:text-dark-text-secondary"),t.R7$(2),t.HbH("ai"===e.evaluationMode?"bg-white dark:bg-dark-bg-secondary text-primary dark:text-dark-accent-primary shadow-md":"text-text dark:text-dark-text-secondary"),t.R7$(6),t.Y8G("ngIf","manual"===e.evaluationMode),t.R7$(1),t.Y8G("ngIf","ai"===e.evaluationMode)}}let Xi=(()=>{class n{constructor(e,i,o,a){this.fb=e,this.route=i,this.router=o,this.rendusService=a,this.renduId="",this.rendu=null,this.isLoading=!0,this.isSubmitting=!1,this.error="",this.evaluationMode="manual",this.aiProcessing=!1,this.evaluationForm=this.fb.group({scores:this.fb.group({structure:[0,[c.k0.required,c.k0.min(0),c.k0.max(5)]],pratiques:[0,[c.k0.required,c.k0.min(0),c.k0.max(5)]],fonctionnalite:[0,[c.k0.required,c.k0.min(0),c.k0.max(5)]],originalite:[0,[c.k0.required,c.k0.min(0),c.k0.max(5)]]}),commentaires:["",c.k0.required],utiliserIA:[!1]})}ngOnInit(){this.renduId=this.route.snapshot.paramMap.get("renduId")||"";const e=this.route.snapshot.queryParamMap.get("mode");if("ai"===e||"manual"===e)this.evaluationMode=e,this.evaluationForm.patchValue({utiliserIA:"ai"===e}),localStorage.setItem("evaluationMode",e);else{const i=localStorage.getItem("evaluationMode");("ai"===i||"manual"===i)&&(this.evaluationMode=i,this.evaluationForm.patchValue({utiliserIA:"ai"===i}))}this.renduId?this.loadRendu():(this.error="ID de rendu manquant",this.isLoading=!1)}loadRendu(){this.isLoading=!0,this.rendusService.getRenduById(this.renduId).subscribe({next:e=>{this.rendu=e,this.rendu.fichiers&&(this.rendu.fichiers=this.rendu.fichiers.filter(i=>null!=i&&""!==i)),this.isLoading=!1},error:e=>{this.error="Erreur lors du chargement du rendu",this.isLoading=!1,console.error(e)}})}toggleEvaluationMode(){this.evaluationMode="manual"===this.evaluationMode?"ai":"manual",this.evaluationForm.patchValue({utiliserIA:"ai"===this.evaluationMode}),localStorage.setItem("evaluationMode",this.evaluationMode)}onSubmit(){console.log("Submit clicked, form valid:",this.evaluationForm.valid),console.log("Form values:",this.evaluationForm.value),this.isSubmitting=!0,this.error="","ai"===this.evaluationMode&&(this.evaluationForm.patchValue({utiliserIA:!0}),this.aiProcessing=!0);const e=this.evaluationForm.value;console.log("Sending evaluation data:",e),this.rendusService.evaluateRendu(this.renduId,e).subscribe({next:i=>{if("ai"===this.evaluationMode&&i.evaluation){const o=i.evaluation.scores;this.evaluationForm.patchValue({scores:{structure:o.structure||0,pratiques:o.pratiques||0,fonctionnalite:o.fonctionnalite||0,originalite:o.originalite||0},commentaires:i.evaluation.commentaires||"\xc9valuation g\xe9n\xe9r\xe9e par IA"}),this.aiProcessing=!1,this.isSubmitting=!1,this.error="",alert("\xc9valuation par IA r\xe9ussie! Vous pouvez modifier les r\xe9sultats avant de confirmer.")}else this.isSubmitting=!1,alert("\xc9valuation soumise avec succ\xe8s!"),this.router.navigate(["/admin/projects/rendus"])},error:i=>{this.error="Erreur lors de l'\xe9valuation du rendu: "+(i.error?.message||i.message||"Erreur inconnue"),this.isSubmitting=!1,this.aiProcessing=!1,console.error(i)}})}getScoreTotal(){const e=this.evaluationForm.get("scores")?.value;return e?e.structure+e.pratiques+e.fonctionnalite+e.originalite:0}getScoreMaximum(){return 20}annuler(){const e=this.evaluationForm.value;(e.scores?.structure||e.scores?.pratiques||e.scores?.fonctionnalite||e.scores?.originalite||e.commentaires)&&!confirm("\xcates-vous s\xfbr de vouloir annuler ? Toutes les donn\xe9es saisies seront perdues.")||(console.log("Navigation vers la liste des rendus..."),this.router.navigate(["/admin/projects/rendus"]))}static{this.\u0275fac=function(i){return new(i||n)(t.rXU(c.ok),t.rXU(b.nX),t.rXU(b.Ix),t.rXU(P.R))}}static{this.\u0275cmp=t.VBU({type:n,selectors:[["app-project-evaluation"]],decls:16,vars:3,consts:[[1,"min-h-screen","bg-[#edf1f4]","dark:bg-dark-bg-primary","transition-colors","duration-300"],[1,"container","mx-auto","px-4","py-8"],[1,"max-w-5xl","mx-auto"],[1,"bg-gradient-to-r","from-primary","to-primary-dark","dark:from-dark-accent-primary","dark:to-dark-accent-secondary","rounded-2xl","p-8","mb-8","shadow-xl"],[1,"flex","items-center","space-x-4"],[1,"bg-white/20","dark:bg-black/20","p-3","rounded-xl","backdrop-blur-sm"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-8","h-8","text-white"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"],[1,"text-3xl","font-bold","text-white","mb-2"],[1,"text-white/80"],["class","flex flex-col items-center justify-center py-16",4,"ngIf"],["class","bg-danger/10 dark:bg-danger-dark/20 border border-danger/30 dark:border-danger-dark/40 text-danger dark:text-danger-dark rounded-xl p-4 mb-6 backdrop-blur-sm",4,"ngIf"],["class","space-y-8",4,"ngIf"],[1,"flex","flex-col","items-center","justify-center","py-16"],[1,"relative"],[1,"animate-spin","rounded-full","h-16","w-16","border-4","border-primary/30","dark:border-dark-accent-primary/30"],[1,"animate-spin","rounded-full","h-16","w-16","border-4","border-transparent","border-t-primary","dark:border-t-dark-accent-primary","absolute","top-0","left-0"],[1,"mt-4","text-text","dark:text-dark-text-secondary","animate-pulse"],[1,"bg-danger/10","dark:bg-danger-dark/20","border","border-danger/30","dark:border-danger-dark/40","text-danger","dark:text-danger-dark","rounded-xl","p-4","mb-6","backdrop-blur-sm"],[1,"flex","items-center","space-x-3"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-5","h-5","text-danger","dark:text-danger-dark","flex-shrink-0"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"],[1,"font-medium"],[1,"space-y-8"],[1,"bg-white/80","dark:bg-dark-bg-secondary/80","backdrop-blur-sm","rounded-2xl","p-6","shadow-lg","border","border-gray-200/50","dark:border-dark-bg-tertiary/50"],[1,"flex","items-center","space-x-3","mb-6"],[1,"bg-gradient-to-r","from-primary","to-primary-dark","dark:from-dark-accent-primary","dark:to-dark-accent-secondary","p-2","rounded-lg"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-5","h-5","text-white"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"],[1,"text-2xl","font-bold","text-text-dark","dark:text-dark-text-primary"],[1,"grid","grid-cols-1","lg:grid-cols-2","gap-6"],[1,"space-y-4"],[1,"bg-gradient-to-r","from-primary/5","to-primary-dark/5","dark:from-dark-accent-primary/10","dark:to-dark-accent-secondary/10","rounded-xl","p-4"],[1,"text-sm","text-text","dark:text-dark-text-secondary","mb-1"],[1,"font-semibold","text-text-dark","dark:text-dark-text-primary","text-lg"],[1,"bg-gradient-to-r","from-secondary/5","to-secondary-dark/5","dark:from-dark-accent-secondary/10","dark:to-dark-accent-primary/10","rounded-xl","p-4"],[1,"bg-gradient-to-r","from-info/5","to-info/10","dark:from-dark-accent-primary/5","dark:to-dark-accent-primary/10","rounded-xl","p-4"],[1,"bg-gradient-to-r","from-success/5","to-success/10","dark:from-success/10","dark:to-success/5","rounded-xl","p-4"],[1,"font-semibold","text-text-dark","dark:text-dark-text-primary"],["class","mt-6 bg-gradient-to-r from-gray-50 to-gray-100 dark:from-dark-bg-tertiary/30 dark:to-dark-bg-tertiary/50 rounded-xl p-4",4,"ngIf"],[1,"flex","flex-col","sm:flex-row","sm:items-center","sm:justify-between","gap-4","mb-6"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"],[1,"flex","items-center","bg-gray-100","dark:bg-dark-bg-tertiary","rounded-xl","p-1","shadow-inner"],[1,"px-4","py-2","rounded-lg","font-medium","transition-all","duration-200","text-sm",3,"click"],[1,"px-4","py-2","bg-gradient-to-r","from-primary","to-primary-dark","dark:from-dark-accent-primary","dark:to-dark-accent-secondary","text-white","rounded-xl","hover:shadow-lg","hover:scale-105","transition-all","duration-200","font-medium",3,"click"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-4","h-4","inline","mr-2"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"],["class","space-y-8",3,"formGroup","ngSubmit",4,"ngIf"],["class","bg-gradient-to-br from-gray-50/50 to-white/50 dark:from-dark-bg-tertiary/30 dark:to-dark-bg-secondary/30 rounded-2xl p-8 border border-gray-200/50 dark:border-dark-bg-tertiary/50",4,"ngIf"],[1,"mt-6","bg-gradient-to-r","from-gray-50","to-gray-100","dark:from-dark-bg-tertiary/30","dark:to-dark-bg-tertiary/50","rounded-xl","p-4"],[1,"flex","items-center","space-x-2","mb-3"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-5","h-5","text-primary","dark:text-dark-accent-primary"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"],[1,"grid","grid-cols-1","sm:grid-cols-2","lg:grid-cols-3","gap-3"],[4,"ngFor","ngForOf"],["target","_blank","class","flex items-center space-x-2 p-3 bg-white dark:bg-dark-bg-secondary rounded-lg hover:bg-primary/5 dark:hover:bg-dark-accent-primary/10 transition-all duration-200 border border-gray-200 dark:border-dark-bg-tertiary group",3,"href",4,"ngIf"],["target","_blank",1,"flex","items-center","space-x-2","p-3","bg-white","dark:bg-dark-bg-secondary","rounded-lg","hover:bg-primary/5","dark:hover:bg-dark-accent-primary/10","transition-all","duration-200","border","border-gray-200","dark:border-dark-bg-tertiary","group",3,"href"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-4","h-4","text-primary","dark:text-dark-accent-primary","group-hover:scale-110","transition-transform"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"],[1,"text-sm","font-medium","text-text-dark","dark:text-dark-text-primary","truncate"],[1,"space-y-8",3,"formGroup","ngSubmit"],[1,"bg-gradient-to-br","from-gray-50/50","to-white/50","dark:from-dark-bg-tertiary/30","dark:to-dark-bg-secondary/30","rounded-2xl","p-6","border","border-gray-200/50","dark:border-dark-bg-tertiary/50"],[1,"bg-gradient-to-r","from-success","to-success-dark","dark:from-dark-accent-primary","dark:to-dark-accent-secondary","p-2","rounded-lg"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"],[1,"text-xl","font-bold","text-text-dark","dark:text-dark-text-primary"],["formGroupName","scores",1,"grid","grid-cols-1","lg:grid-cols-2","gap-6"],[1,"group"],[1,"block","text-sm","font-semibold","text-text-dark","dark:text-dark-text-primary","mb-3"],[1,"flex","items-center","space-x-2"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-4","h-4","text-primary","dark:text-dark-accent-primary"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M19 11H5m14-7H3a2 2 0 00-2 2v12a2 2 0 002 2h16a2 2 0 002-2V6a2 2 0 00-2-2z"],["type","number","formControlName","structure","min","0","max","5","placeholder","0-5",1,"w-full","px-4","py-3","bg-white","dark:bg-dark-bg-secondary","border-2","border-gray-200","dark:border-dark-bg-tertiary","rounded-xl","focus:outline-none","focus:border-primary","dark:focus:border-dark-accent-primary","focus:ring-4","focus:ring-primary/10","dark:focus:ring-dark-accent-primary/20","transition-all","duration-200","text-text-dark","dark:text-dark-text-primary","placeholder-text","dark:placeholder-dark-text-secondary"],[1,"absolute","inset-y-0","right-0","flex","items-center","pr-4"],[1,"text-sm","font-medium","text-text","dark:text-dark-text-secondary"],["type","number","formControlName","pratiques","min","0","max","5","placeholder","0-5",1,"w-full","px-4","py-3","bg-white","dark:bg-dark-bg-secondary","border-2","border-gray-200","dark:border-dark-bg-tertiary","rounded-xl","focus:outline-none","focus:border-primary","dark:focus:border-dark-accent-primary","focus:ring-4","focus:ring-primary/10","dark:focus:ring-dark-accent-primary/20","transition-all","duration-200","text-text-dark","dark:text-dark-text-primary","placeholder-text","dark:placeholder-dark-text-secondary"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M15 12a3 3 0 11-6 0 3 3 0 016 0z"],["type","number","formControlName","fonctionnalite","min","0","max","5","placeholder","0-5",1,"w-full","px-4","py-3","bg-white","dark:bg-dark-bg-secondary","border-2","border-gray-200","dark:border-dark-bg-tertiary","rounded-xl","focus:outline-none","focus:border-primary","dark:focus:border-dark-accent-primary","focus:ring-4","focus:ring-primary/10","dark:focus:ring-dark-accent-primary/20","transition-all","duration-200","text-text-dark","dark:text-dark-text-primary","placeholder-text","dark:placeholder-dark-text-secondary"],["type","number","formControlName","originalite","min","0","max","5","placeholder","0-5",1,"w-full","px-4","py-3","bg-white","dark:bg-dark-bg-secondary","border-2","border-gray-200","dark:border-dark-bg-tertiary","rounded-xl","focus:outline-none","focus:border-primary","dark:focus:border-dark-accent-primary","focus:ring-4","focus:ring-primary/10","dark:focus:ring-dark-accent-primary/20","transition-all","duration-200","text-text-dark","dark:text-dark-text-primary","placeholder-text","dark:placeholder-dark-text-secondary"],[1,"mt-8","bg-gradient-to-r","from-primary/5","to-primary-dark/5","dark:from-dark-accent-primary/10","dark:to-dark-accent-secondary/10","rounded-2xl","p-6","border","border-primary/20","dark:border-dark-accent-primary/30"],[1,"flex","items-center","justify-between","mb-4"],[1,"text-lg","font-bold","text-text-dark","dark:text-dark-text-primary"],[1,"text-right"],[1,"text-3xl","font-bold","text-primary","dark:text-dark-accent-primary"],[1,"text-sm","text-text","dark:text-dark-text-secondary"],[1,"w-full","bg-gray-200","dark:bg-dark-bg-tertiary","rounded-full","h-3","overflow-hidden"],[1,"h-full","bg-gradient-to-r","from-primary","to-primary-dark","dark:from-dark-accent-primary","dark:to-dark-accent-secondary","rounded-full","transition-all","duration-500","ease-out"],[1,"flex","justify-between","text-xs","text-text","dark:text-dark-text-secondary","mt-2"],[1,"bg-gradient-to-r","from-info","to-primary","dark:from-dark-accent-secondary","dark:to-dark-accent-primary","p-2","rounded-lg"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z"],["formControlName","commentaires","rows","6","placeholder","D\xe9crivez les points forts et les axes d'am\xe9lioration du projet. Soyez pr\xe9cis et constructif dans vos commentaires...",1,"w-full","px-4","py-4","bg-white","dark:bg-dark-bg-secondary","border-2","border-gray-200","dark:border-dark-bg-tertiary","rounded-xl","focus:outline-none","focus:border-primary","dark:focus:border-dark-accent-primary","focus:ring-4","focus:ring-primary/10","dark:focus:ring-dark-accent-primary/20","transition-all","duration-200","text-text-dark","dark:text-dark-text-primary","placeholder-text","dark:placeholder-dark-text-secondary","resize-none"],[1,"absolute","bottom-3","right-3","text-xs","text-text","dark:text-dark-text-secondary","bg-white/80","dark:bg-dark-bg-secondary/80","px-2","py-1","rounded-lg","backdrop-blur-sm"],[1,"mt-4","flex","items-start","space-x-3","p-4","bg-primary/5","dark:bg-dark-accent-primary/10","rounded-xl","border","border-primary/20","dark:border-dark-accent-primary/30"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-5","h-5","text-primary","dark:text-dark-accent-primary","mt-0.5","flex-shrink-0"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"],[1,"text-sm","text-text-dark","dark:text-dark-text-primary"],[1,"font-medium","mb-1"],[1,"space-y-1","text-text","dark:text-dark-text-secondary"],[1,"flex","flex-col","sm:flex-row","gap-4","justify-between","items-center","pt-6"],["type","button",1,"w-full","sm:w-auto","group","px-6","py-3","bg-gray-100","dark:bg-dark-bg-tertiary","text-text-dark","dark:text-dark-text-primary","rounded-xl","hover:bg-gray-200","dark:hover:bg-dark-bg-tertiary/80","transition-all","duration-200","font-medium","border-2","border-gray-200","dark:border-dark-bg-tertiary","hover:border-gray-300","dark:hover:border-dark-bg-tertiary/60",3,"click"],[1,"flex","items-center","justify-center","space-x-2"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-4","h-4","group-hover:scale-110","transition-transform"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M6 18L18 6M6 6l12 12"],["type","submit",1,"w-full","sm:w-auto","group","px-8","py-3","bg-gradient-to-r","from-success","to-success-dark","dark:from-dark-accent-primary","dark:to-dark-accent-secondary","text-white","rounded-xl","hover:shadow-xl","hover:scale-105","transition-all","duration-200","font-semibold","border-2","border-transparent","hover:border-success/30","dark:hover:border-dark-accent-primary/30"],["class","w-5 h-5 group-hover:scale-110 transition-transform","fill","none","stroke","currentColor","viewBox","0 0 24 24",4,"ngIf"],["class","w-5 h-5 animate-spin","fill","none","stroke","currentColor","viewBox","0 0 24 24",4,"ngIf"],[4,"ngIf"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-5","h-5","group-hover:scale-110","transition-transform"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-5","h-5","animate-spin"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"],[1,"bg-gradient-to-br","from-gray-50/50","to-white/50","dark:from-dark-bg-tertiary/30","dark:to-dark-bg-secondary/30","rounded-2xl","p-8","border","border-gray-200/50","dark:border-dark-bg-tertiary/50"],["class","text-center py-12",4,"ngIf"],[1,"bg-gradient-to-r","from-secondary","to-primary","dark:from-dark-accent-secondary","dark:to-dark-accent-primary","p-3","rounded-xl"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-6","h-6","text-white"],[1,"text-text","dark:text-dark-text-secondary"],[1,"bg-gradient-to-r","from-primary/5","to-secondary/5","dark:from-dark-accent-primary/10","dark:to-dark-accent-secondary/10","rounded-2xl","p-6","mb-8","border","border-primary/20","dark:border-dark-accent-primary/30"],[1,"flex","items-start","space-x-4"],[1,"bg-gradient-to-r","from-primary","to-secondary","dark:from-dark-accent-primary","dark:to-dark-accent-secondary","p-2","rounded-lg","flex-shrink-0"],[1,"font-bold","text-text-dark","dark:text-dark-text-primary","mb-3"],[1,"text-text","dark:text-dark-text-secondary","mb-4"],[1,"grid","grid-cols-1","sm:grid-cols-2","gap-3"],[1,"w-2","h-2","bg-gradient-to-r","from-primary","to-secondary","dark:from-dark-accent-primary","dark:to-dark-accent-secondary","rounded-full"],[1,"text-sm","text-text-dark","dark:text-dark-text-primary","font-medium"],[1,"flex","flex-col","sm:flex-row","gap-4","justify-between","items-center"],[1,"w-full","sm:w-auto","group","px-8","py-3","bg-gradient-to-r","from-secondary","to-primary","dark:from-dark-accent-secondary","dark:to-dark-accent-primary","text-white","rounded-xl","hover:shadow-xl","hover:scale-105","transition-all","duration-200","font-semibold","border-2","border-transparent","hover:border-secondary/30","dark:hover:border-dark-accent-secondary/30","disabled:opacity-50","disabled:cursor-not-allowed","disabled:hover:scale-100",3,"disabled","click"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M13 10V3L4 14h7v7l9-11h-7z"],[1,"text-center","py-12"],[1,"relative","mb-8"],[1,"animate-spin","rounded-full","h-20","w-20","border-4","border-primary/30","dark:border-dark-accent-primary/30","mx-auto"],[1,"animate-spin","rounded-full","h-20","w-20","border-4","border-transparent","border-t-primary","dark:border-t-dark-accent-primary","absolute","top-0","left-1/2","transform","-translate-x-1/2"],[1,"absolute","inset-0","flex","items-center","justify-center"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-8","h-8","text-primary","dark:text-dark-accent-primary","animate-pulse"],[1,"text-xl","font-bold","text-text-dark","dark:text-dark-text-primary","mb-2"],[1,"text-text","dark:text-dark-text-secondary","mb-6"],[1,"bg-gradient-to-r","from-primary/5","to-secondary/5","dark:from-dark-accent-primary/10","dark:to-dark-accent-secondary/10","rounded-2xl","p-4","max-w-md","mx-auto","border","border-primary/20","dark:border-dark-accent-primary/30"],[1,"flex","items-center","justify-center","space-x-2","mb-2"],[1,"w-2","h-2","bg-gradient-to-r","from-primary","to-secondary","dark:from-dark-accent-primary","dark:to-dark-accent-secondary","rounded-full","animate-bounce"],[1,"w-2","h-2","bg-gradient-to-r","from-primary","to-secondary","dark:from-dark-accent-primary","dark:to-dark-accent-secondary","rounded-full","animate-bounce",2,"animation-delay","0.1s"],[1,"w-2","h-2","bg-gradient-to-r","from-primary","to-secondary","dark:from-dark-accent-primary","dark:to-dark-accent-secondary","rounded-full","animate-bounce",2,"animation-delay","0.2s"]],template:function(i,o){1&i&&(t.j41(0,"div",0)(1,"div",1)(2,"div",2)(3,"div",3)(4,"div",4)(5,"div",5),t.qSk(),t.j41(6,"svg",6),t.nrm(7,"path",7),t.k0s()(),t.joV(),t.j41(8,"div")(9,"h1",8),t.EFF(10,"\xc9valuation du projet"),t.k0s(),t.j41(11,"p",9),t.EFF(12,"Syst\xe8me d'\xe9valuation intelligent avec IA int\xe9gr\xe9e"),t.k0s()()()(),t.DNE(13,Di,6,0,"div",10),t.DNE(14,Oi,6,1,"div",11),t.DNE(15,Hi,53,15,"div",12),t.k0s()()()),2&i&&(t.R7$(13),t.Y8G("ngIf",o.isLoading),t.R7$(1),t.Y8G("ngIf",o.error),t.R7$(1),t.Y8G("ngIf",o.rendu&&!o.isLoading))},dependencies:[p.Sq,p.bT,c.qT,c.me,c.Q0,c.BC,c.cb,c.VZ,c.zX,c.j4,c.JD,c.$R,p.vh],styles:['.container[_ngcontent-%COMP%]{max-width:1200px;margin:0 auto}.form-group[_ngcontent-%COMP%]{margin-bottom:1rem}.error-message[_ngcontent-%COMP%]{color:#dc3545;margin-top:.25rem}.loading-spinner[_ngcontent-%COMP%]{display:flex;justify-content:center;margin:2rem 0}@keyframes _ngcontent-%COMP%_fadeInUp{0%{opacity:0;transform:translateY(20px)}to{opacity:1;transform:translateY(0)}}@keyframes _ngcontent-%COMP%_slideInRight{0%{opacity:0;transform:translate(30px)}to{opacity:1;transform:translate(0)}}@keyframes _ngcontent-%COMP%_glow{0%,to{box-shadow:0 0 5px #4f5fad4d}50%{box-shadow:0 0 20px #4f5fad99,0 0 30px #4f5fad66}}@keyframes _ngcontent-%COMP%_glowDark{0%,to{box-shadow:0 0 5px #00f7ff4d}50%{box-shadow:0 0 20px #00f7ff99,0 0 30px #00f7ff66}}.form-input-enhanced[_ngcontent-%COMP%]{transition:all .3s cubic-bezier(.4,0,.2,1)}.form-input-enhanced[_ngcontent-%COMP%]:focus{transform:translateY(-2px)}.btn-modern[_ngcontent-%COMP%]{position:relative;overflow:hidden;transition:all .3s cubic-bezier(.4,0,.2,1)}.btn-modern[_ngcontent-%COMP%]:before{content:"";position:absolute;top:0;left:-100%;width:100%;height:100%;background:linear-gradient(90deg,transparent,rgba(255,255,255,.2),transparent);transition:left .5s}.btn-modern[_ngcontent-%COMP%]:hover:before{left:100%}.glass-card[_ngcontent-%COMP%]{backdrop-filter:blur(10px);-webkit-backdrop-filter:blur(10px);border:1px solid rgba(255,255,255,.2)}.dark[_ngcontent-%COMP%]   .glass-card[_ngcontent-%COMP%]{border:1px solid rgba(255,255,255,.1)}.animate-fade-in[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_fadeInUp .6s ease-out}.animate-slide-in[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_slideInRight .6s ease-out}.progress-bar-animated[_ngcontent-%COMP%]{background:linear-gradient(90deg,#4f5fad,#7826b5);background-size:200% 100%;animation:_ngcontent-%COMP%_shimmer 2s infinite}.dark[_ngcontent-%COMP%]   .progress-bar-animated[_ngcontent-%COMP%]{background:linear-gradient(90deg,#00f7ff,#9d4edd);background-size:200% 100%}@keyframes _ngcontent-%COMP%_shimmer{0%{background-position:-200% 0}to{background-position:200% 0}}.tooltip[_ngcontent-%COMP%]{position:relative}.tooltip[_ngcontent-%COMP%]:after{content:attr(data-tooltip);position:absolute;bottom:100%;left:50%;transform:translate(-50%);background:rgba(0,0,0,.8);color:#fff;padding:.5rem;border-radius:.375rem;font-size:.75rem;white-space:nowrap;opacity:0;pointer-events:none;transition:opacity .3s;z-index:1000}.tooltip[_ngcontent-%COMP%]:hover:after{opacity:1}@media (max-width: 768px){.container[_ngcontent-%COMP%]{padding:1rem}.grid-responsive[_ngcontent-%COMP%]{grid-template-columns:1fr}.btn-modern[_ngcontent-%COMP%]{width:100%;justify-content:center}}.focus-visible[_ngcontent-%COMP%]:focus{outline:2px solid #4f5fad;outline-offset:2px}.dark[_ngcontent-%COMP%]   .focus-visible[_ngcontent-%COMP%]:focus{outline:2px solid #00f7ff}.icon-hover[_ngcontent-%COMP%]{transition:transform .2s ease}.icon-hover[_ngcontent-%COMP%]:hover{transform:scale(1.1) rotate(5deg)}.alert-modern[_ngcontent-%COMP%]{border-left:4px solid;background:rgba(255,255,255,.9);-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px)}.dark[_ngcontent-%COMP%]   .alert-modern[_ngcontent-%COMP%]{background:rgba(0,0,0,.3)}']})}}return n})();function Ui(n,r){1&n&&(t.j41(0,"div",10),t.nrm(1,"div",11),t.k0s())}function Zi(n,r){if(1&n&&(t.j41(0,"div",12),t.EFF(1),t.k0s()),2&n){const e=t.XpG();t.R7$(1),t.SpI(" ",e.error," ")}}function Ki(n,r){if(1&n&&(t.j41(0,"div",40)(1,"div",33)(2,"span",41),t.EFF(3,"Structure du code"),t.k0s(),t.j41(4,"span",42),t.EFF(5),t.k0s()(),t.j41(6,"div",43),t.nrm(7,"div",44),t.k0s()()),2&n){const e=t.XpG(2);t.R7$(5),t.SpI("",e.rendu.evaluation.scores.structure,"/5"),t.R7$(2),t.xc7("width",e.rendu.evaluation.scores.structure/5*100,"%")}}function Wi(n,r){if(1&n&&(t.j41(0,"div",40)(1,"div",33)(2,"span",41),t.EFF(3,"Bonnes pratiques"),t.k0s(),t.j41(4,"span",42),t.EFF(5),t.k0s()(),t.j41(6,"div",43),t.nrm(7,"div",45),t.k0s()()),2&n){const e=t.XpG(2);t.R7$(5),t.SpI("",e.rendu.evaluation.scores.pratiques,"/5"),t.R7$(2),t.xc7("width",e.rendu.evaluation.scores.pratiques/5*100,"%")}}function Ji(n,r){if(1&n&&(t.j41(0,"div",40)(1,"div",33)(2,"span",41),t.EFF(3,"Fonctionnalit\xe9s"),t.k0s(),t.j41(4,"span",42),t.EFF(5),t.k0s()(),t.j41(6,"div",43),t.nrm(7,"div",46),t.k0s()()),2&n){const e=t.XpG(2);t.R7$(5),t.SpI("",e.rendu.evaluation.scores.fonctionnalite,"/5"),t.R7$(2),t.xc7("width",e.rendu.evaluation.scores.fonctionnalite/5*100,"%")}}function Qi(n,r){if(1&n&&(t.j41(0,"div",40)(1,"div",33)(2,"span",41),t.EFF(3,"Originalit\xe9"),t.k0s(),t.j41(4,"span",42),t.EFF(5),t.k0s()(),t.j41(6,"div",43),t.nrm(7,"div",47),t.k0s()()),2&n){const e=t.XpG(2);t.R7$(5),t.SpI("",e.rendu.evaluation.scores.originalite,"/5"),t.R7$(2),t.xc7("width",e.rendu.evaluation.scores.originalite/5*100,"%")}}function tr(n,r){if(1&n&&(t.j41(0,"div",29)(1,"h3",48),t.EFF(2,"Commentaires"),t.k0s(),t.j41(3,"div",40)(4,"p",49),t.EFF(5),t.k0s()()()),2&n){const e=t.XpG(2);t.R7$(5),t.JRh(e.rendu.evaluation.commentaires)}}function er(n,r){if(1&n&&(t.j41(0,"a",52),t.qSk(),t.j41(1,"svg",53),t.nrm(2,"path",54),t.k0s(),t.joV(),t.j41(3,"span",55),t.EFF(4),t.k0s()()),2&n){const e=r.$implicit,i=t.XpG(3);t.Y8G("href",i.getFileUrl(e),t.B4B)("download",i.getFileName(e)),t.R7$(4),t.JRh(i.getFileName(e))}}function ir(n,r){if(1&n&&(t.j41(0,"div",29)(1,"h3",48),t.EFF(2,"Fichiers soumis"),t.k0s(),t.j41(3,"div",50),t.DNE(4,er,5,3,"a",51),t.k0s()()),2&n){const e=t.XpG(2);t.R7$(4),t.Y8G("ngForOf",e.rendu.fichiers)}}function rr(n,r){if(1&n&&(t.j41(0,"div",56),t.EFF(1),t.nI1(2,"date"),t.k0s()),2&n){const e=t.XpG(2);t.R7$(1),t.SpI(" \xc9valu\xe9 le ",t.i5U(2,1,e.rendu.evaluation.dateEvaluation,"dd/MM/yyyy \xe0 HH:mm")," ")}}const or=function(n,r,e,i){return{"bg-green-600":n,"bg-blue-600":r,"bg-yellow-600":e,"bg-red-600":i}};function nr(n,r){if(1&n&&(t.j41(0,"div",13)(1,"div",14)(2,"div",15)(3,"div")(4,"h2",16),t.EFF(5),t.k0s(),t.j41(6,"p",17),t.EFF(7),t.nI1(8,"date"),t.k0s()(),t.j41(9,"div",18)(10,"span",19),t.EFF(11),t.k0s()()(),t.j41(12,"div",20)(13,"div",21),t.EFF(14),t.k0s(),t.j41(15,"div",22)(16,"p",23),t.EFF(17),t.k0s(),t.j41(18,"p",24),t.EFF(19),t.k0s()(),t.j41(20,"div",25)(21,"span",26),t.EFF(22),t.k0s()()()(),t.j41(23,"div",27)(24,"h3",28),t.EFF(25,"D\xe9tails des scores"),t.k0s(),t.j41(26,"div",29)(27,"div",30),t.DNE(28,Ki,8,3,"div",31),t.DNE(29,Wi,8,3,"div",31),t.DNE(30,Ji,8,3,"div",31),t.DNE(31,Qi,8,3,"div",31),t.k0s(),t.j41(32,"div",32)(33,"div",33)(34,"span",34),t.EFF(35,"Score total"),t.k0s(),t.j41(36,"span",35),t.EFF(37),t.k0s()(),t.j41(38,"div",36),t.nrm(39,"div",37),t.k0s()()(),t.DNE(40,tr,6,1,"div",38),t.DNE(41,ir,5,1,"div",38),t.DNE(42,rr,3,4,"div",39),t.k0s()()),2&n){const e=t.XpG();t.R7$(5),t.JRh(null==e.rendu.projet?null:e.rendu.projet.titre),t.R7$(2),t.SpI("Soumis le ",t.i5U(8,24,e.rendu.dateSoumission,"dd/MM/yyyy \xe0 HH:mm"),""),t.R7$(3),t.Y8G("ngClass",e.getScoreClass()),t.R7$(1),t.Lme(" ",e.getScoreTotal(),"/",e.getScoreMaximum()," "),t.R7$(3),t.Lme(" ",null==e.rendu.etudiant||null==e.rendu.etudiant.nom?null:e.rendu.etudiant.nom.charAt(0),"",null==e.rendu.etudiant||null==e.rendu.etudiant.prenom?null:e.rendu.etudiant.prenom.charAt(0)," "),t.R7$(3),t.Lme("",null==e.rendu.etudiant?null:e.rendu.etudiant.nom," ",null==e.rendu.etudiant?null:e.rendu.etudiant.prenom,""),t.R7$(2),t.JRh(null==e.rendu.etudiant?null:e.rendu.etudiant.email),t.R7$(3),t.SpI(" ",(null==e.rendu.etudiant?null:e.rendu.etudiant.groupe)||"Groupe non sp\xe9cifi\xe9"," "),t.R7$(6),t.Y8G("ngIf",null==e.rendu.evaluation?null:e.rendu.evaluation.scores),t.R7$(1),t.Y8G("ngIf",null==e.rendu.evaluation?null:e.rendu.evaluation.scores),t.R7$(1),t.Y8G("ngIf",null==e.rendu.evaluation?null:e.rendu.evaluation.scores),t.R7$(1),t.Y8G("ngIf",null==e.rendu.evaluation?null:e.rendu.evaluation.scores),t.R7$(5),t.Y8G("ngClass",e.getScoreClass()),t.R7$(1),t.Lme("",e.getScoreTotal(),"/",e.getScoreMaximum(),""),t.R7$(2),t.xc7("width",e.getScorePercentage(),"%"),t.Y8G("ngClass",t.ziG(27,or,e.getScorePercentage()>=80,e.getScorePercentage()>=60&&e.getScorePercentage()<80,e.getScorePercentage()>=40&&e.getScorePercentage()<60,e.getScorePercentage()<40)),t.R7$(1),t.Y8G("ngIf",null==e.rendu.evaluation?null:e.rendu.evaluation.commentaires),t.R7$(1),t.Y8G("ngIf",e.rendu.fichiers&&e.rendu.fichiers.length>0),t.R7$(1),t.Y8G("ngIf",null==e.rendu.evaluation?null:e.rendu.evaluation.dateEvaluation)}}let ar=(()=>{class n{constructor(e,i,o){this.route=e,this.router=i,this.rendusService=o,this.renduId="",this.rendu=null,this.isLoading=!0,this.error=""}ngOnInit(){this.renduId=this.route.snapshot.paramMap.get("renduId")||"",this.renduId?this.loadRendu():(this.error="ID de rendu manquant",this.isLoading=!1)}loadRendu(){this.isLoading=!0,this.rendusService.getRenduById(this.renduId).subscribe({next:e=>{this.rendu=e,this.isLoading=!1},error:e=>{this.error="Erreur lors du chargement du rendu",this.isLoading=!1,console.error(e)}})}getScoreTotal(){if(!this.rendu?.evaluation?.scores)return 0;const e=this.rendu.evaluation.scores;return e.structure+e.pratiques+e.fonctionnalite+e.originalite}getScoreMaximum(){return 20}getScorePercentage(){return this.getScoreTotal()/this.getScoreMaximum()*100}getScoreClass(){const e=this.getScorePercentage();return e>=80?"text-green-600":e>=60?"text-blue-600":e>=40?"text-yellow-600":"text-red-600"}retourListe(){this.router.navigate(["/admin/projects/rendus"])}getFileUrl(e){if(!e)return"";let i=e;if(e.includes("/")||e.includes("\\")){const o=e.split(/[\/\\]/);i=o[o.length-1]}return`${E.c.urlBackend}projets/telecharger/${i}`}getFileName(e){if(!e)return"Fichier";if(e.includes("/")||e.includes("\\")){const i=e.split(/[\/\\]/);return i[i.length-1]}return e}static{this.\u0275fac=function(i){return new(i||n)(t.rXU(b.nX),t.rXU(b.Ix),t.rXU(P.R))}}static{this.\u0275cmp=t.VBU({type:n,selectors:[["app-evaluation-details"]],decls:11,vars:3,consts:[[1,"min-h-screen","bg-[#edf1f4]","p-4","md:p-6"],[1,"max-w-4xl","mx-auto"],[1,"flex","items-center","mb-6"],[1,"mr-4","p-2","rounded-full","hover:bg-gray-200","transition-colors",3,"click"],["xmlns","http://www.w3.org/2000/svg","fill","none","viewBox","0 0 24 24","stroke","currentColor",1,"h-6","w-6","text-gray-600"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M10 19l-7-7m0 0l7-7m-7 7h18"],[1,"text-2xl","md:text-3xl","font-bold","text-[#4f5fad]"],["class","flex justify-center py-12",4,"ngIf"],["class","bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4",4,"ngIf"],["class","bg-white rounded-xl shadow-md overflow-hidden",4,"ngIf"],[1,"flex","justify-center","py-12"],[1,"animate-spin","rounded-full","h-12","w-12","border-t-2","border-b-2","border-[#4f5fad]"],[1,"bg-red-100","border","border-red-400","text-red-700","px-4","py-3","rounded","mb-4"],[1,"bg-white","rounded-xl","shadow-md","overflow-hidden"],[1,"p-6","border-b","border-gray-200"],[1,"flex","flex-col","md:flex-row","justify-between","items-start","md:items-center","mb-4"],[1,"text-xl","font-bold","text-gray-800"],[1,"text-sm","text-gray-500"],[1,"mt-2","md:mt-0"],[1,"text-2xl","font-bold",3,"ngClass"],[1,"flex","items-center","mb-4"],[1,"h-10","w-10","rounded-full","bg-[#6C63FF]","flex","items-center","justify-center","text-white","font-bold"],[1,"ml-4"],[1,"text-sm","font-medium","text-gray-900"],[1,"text-xs","text-gray-500"],[1,"ml-auto"],[1,"bg-[#f0f4f8]","px-3","py-1","rounded-full","text-xs","font-medium","text-[#4f5fad]"],[1,"p-6"],[1,"text-lg","font-semibold","mb-4"],[1,"mb-6"],[1,"grid","grid-cols-1","md:grid-cols-2","gap-4"],["class","bg-gray-50 p-4 rounded-lg",4,"ngIf"],[1,"mt-6","bg-gray-50","p-4","rounded-lg"],[1,"flex","justify-between","items-center","mb-2"],[1,"text-base","font-medium","text-gray-700"],[1,"text-base","font-bold",3,"ngClass"],[1,"w-full","bg-gray-200","rounded-full","h-3"],[1,"h-3","rounded-full",3,"ngClass"],["class","mb-6",4,"ngIf"],["class","text-sm text-gray-500 text-right",4,"ngIf"],[1,"bg-gray-50","p-4","rounded-lg"],[1,"text-sm","font-medium","text-gray-700"],[1,"text-sm","font-bold"],[1,"w-full","bg-gray-200","rounded-full","h-2.5"],[1,"bg-blue-600","h-2.5","rounded-full"],[1,"bg-green-600","h-2.5","rounded-full"],[1,"bg-purple-600","h-2.5","rounded-full"],[1,"bg-yellow-600","h-2.5","rounded-full"],[1,"text-lg","font-semibold","mb-2"],[1,"text-gray-700","whitespace-pre-line"],[1,"grid","grid-cols-1","md:grid-cols-2","gap-3"],["target","_blank","class","flex items-center p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors",3,"href","download",4,"ngFor","ngForOf"],["target","_blank",1,"flex","items-center","p-3","bg-gray-50","rounded-lg","hover:bg-gray-100","transition-colors",3,"href","download"],["xmlns","http://www.w3.org/2000/svg","fill","none","viewBox","0 0 24 24","stroke","currentColor",1,"h-5","w-5","text-gray-500","mr-2"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"],[1,"text-sm","text-gray-700","truncate"],[1,"text-sm","text-gray-500","text-right"]],template:function(i,o){1&i&&(t.j41(0,"div",0)(1,"div",1)(2,"div",2)(3,"button",3),t.bIt("click",function(){return o.retourListe()}),t.qSk(),t.j41(4,"svg",4),t.nrm(5,"path",5),t.k0s()(),t.joV(),t.j41(6,"h1",6),t.EFF(7,"D\xe9tails de l'\xe9valuation"),t.k0s()(),t.DNE(8,Ui,2,0,"div",7),t.DNE(9,Zi,2,1,"div",8),t.DNE(10,nr,43,32,"div",9),t.k0s()()),2&i&&(t.R7$(8),t.Y8G("ngIf",o.isLoading),t.R7$(1),t.Y8G("ngIf",o.error),t.R7$(1),t.Y8G("ngIf",!o.isLoading&&!o.error&&o.rendu))},dependencies:[p.YU,p.Sq,p.bT,p.vh],styles:[".loading-spinner[_ngcontent-%COMP%]{display:flex;justify-content:center;margin:2rem 0}.error-message[_ngcontent-%COMP%]{color:#dc3545;margin-top:.25rem}"]})}}return n})();var L=g(9437),sr=g(980),q=g(8810),dr=g(1626);let cr=(()=>{class n{constructor(e){this.http=e}getAllEvaluations(){return this.http.get(`${E.c.urlBackend}evaluations/getev`).pipe((0,L.W)(i=>(console.error("Erreur HTTP lors de la r\xe9cup\xe9ration des \xe9valuations:",i),(0,q.$)(()=>new Error("Erreur lors de la r\xe9cup\xe9ration des \xe9valuations")))))}getEvaluationById(e){return this.http.get(`${E.c.urlBackend}evaluations/${e}`).pipe((0,L.W)(o=>(console.error("Erreur HTTP lors de la r\xe9cup\xe9ration de l'\xe9valuation:",o),(0,q.$)(()=>new Error("Erreur lors de la r\xe9cup\xe9ration de l'\xe9valuation")))))}updateMissingGroups(){return this.http.post(`${E.c.urlBackend}evaluations/update-missing-groups`,{}).pipe((0,L.W)(i=>(console.error("Erreur HTTP lors de la mise \xe0 jour des groupes:",i),(0,q.$)(()=>new Error("Erreur lors de la mise \xe0 jour des groupes")))))}deleteEvaluation(e){return this.http.delete(`${E.c.urlBackend}evaluations/${e}`).pipe((0,L.W)(o=>(console.error("Erreur HTTP lors de la suppression de l'\xe9valuation:",o),(0,q.$)(()=>new Error("Erreur lors de la suppression de l'\xe9valuation")))))}static{this.\u0275fac=function(i){return new(i||n)(t.KVO(dr.Qq))}}static{this.\u0275prov=t.jDH({token:n,factory:n.\u0275fac,providedIn:"root"})}}return n})();function lr(n,r){if(1&n){const e=t.RV6();t.j41(0,"div",29)(1,"button",30),t.bIt("click",function(){t.eBV(e);const o=t.XpG(2);return t.Njj(o.clearSearch())}),t.qSk(),t.j41(2,"svg",31),t.nrm(3,"path",32),t.k0s()()()}}function ur(n,r){if(1&n&&(t.j41(0,"div",33),t.EFF(1),t.k0s()),2&n){const e=t.XpG(2);t.R7$(1),t.Lme(" ",e.filteredEvaluations.length,' r\xe9sultat(s) trouv\xe9(s) pour "',e.searchTerm,'" ')}}function mr(n,r){if(1&n){const e=t.RV6();t.j41(0,"div",21)(1,"div",22)(2,"div",23),t.qSk(),t.j41(3,"svg",24),t.nrm(4,"path",25),t.k0s()(),t.joV(),t.j41(5,"input",26),t.bIt("ngModelChange",function(o){t.eBV(e);const a=t.XpG();return t.Njj(a.searchTerm=o)})("input",function(){t.eBV(e);const o=t.XpG();return t.Njj(o.onSearchChange())}),t.k0s(),t.DNE(6,lr,4,0,"div",27),t.k0s(),t.DNE(7,ur,2,2,"div",28),t.k0s()}if(2&n){const e=t.XpG();t.R7$(5),t.Y8G("ngModel",e.searchTerm),t.R7$(1),t.Y8G("ngIf",e.searchTerm),t.R7$(1),t.Y8G("ngIf",e.searchTerm)}}function pr(n,r){1&n&&(t.j41(0,"div",34)(1,"div",22),t.nrm(2,"div",35)(3,"div",36),t.k0s(),t.j41(4,"p",37),t.EFF(5,"Chargement des \xe9valuations..."),t.k0s()())}function hr(n,r){if(1&n){const e=t.RV6();t.j41(0,"div",38)(1,"div",3)(2,"div",39),t.qSk(),t.j41(3,"svg",40),t.nrm(4,"path",41),t.k0s(),t.joV(),t.j41(5,"p",42),t.EFF(6),t.k0s()(),t.j41(7,"button",43),t.bIt("click",function(){t.eBV(e);const o=t.XpG();return t.Njj(o.loadEvaluations())}),t.EFF(8," R\xe9essayer "),t.k0s()()()}if(2&n){const e=t.XpG();t.R7$(6),t.JRh(e.error)}}function gr(n,r){if(1&n&&(t.j41(0,"option",67),t.EFF(1),t.k0s()),2&n){const e=r.$implicit;t.Y8G("value",e),t.R7$(1),t.JRh(e)}}function fr(n,r){if(1&n&&(t.j41(0,"option",67),t.EFF(1),t.k0s()),2&n){const e=r.$implicit;t.Y8G("value",e._id),t.R7$(1),t.JRh(e.titre)}}function br(n,r){if(1&n){const e=t.RV6();t.j41(0,"div",44)(1,"div",45)(2,"div",46),t.qSk(),t.j41(3,"svg",47),t.nrm(4,"path",48),t.k0s()(),t.joV(),t.j41(5,"h2",49),t.EFF(6,"Filtres et recherche"),t.k0s()(),t.j41(7,"div",50)(8,"div",51)(9,"label",52)(10,"div",53),t.qSk(),t.j41(11,"svg",54),t.nrm(12,"path",55),t.k0s(),t.joV(),t.j41(13,"span"),t.EFF(14,"Filtrer par groupe"),t.k0s()()(),t.j41(15,"select",56),t.bIt("ngModelChange",function(o){t.eBV(e);const a=t.XpG();return t.Njj(a.filterGroupe=o)})("change",function(){t.eBV(e);const o=t.XpG();return t.Njj(o.applyFilters())}),t.j41(16,"option",57),t.EFF(17),t.k0s(),t.DNE(18,gr,2,2,"option",58),t.k0s()(),t.j41(19,"div",51)(20,"label",52)(21,"div",53),t.qSk(),t.j41(22,"svg",54),t.nrm(23,"path",59),t.k0s(),t.joV(),t.j41(24,"span"),t.EFF(25,"Filtrer par projet"),t.k0s()()(),t.j41(26,"select",56),t.bIt("ngModelChange",function(o){t.eBV(e);const a=t.XpG();return t.Njj(a.filterProjet=o)})("change",function(){t.eBV(e);const o=t.XpG();return t.Njj(o.applyFilters())}),t.j41(27,"option",57),t.EFF(28),t.k0s(),t.DNE(29,fr,2,2,"option",58),t.k0s()(),t.j41(30,"div",51)(31,"label",52)(32,"div",53),t.qSk(),t.j41(33,"svg",54),t.nrm(34,"path",60)(35,"path",61),t.k0s(),t.joV(),t.j41(36,"span"),t.EFF(37,"Actions rapides"),t.k0s()()(),t.j41(38,"div",62)(39,"button",63),t.bIt("click",function(){t.eBV(e);const o=t.XpG();return t.Njj(o.refreshList())}),t.j41(40,"div",64),t.qSk(),t.j41(41,"svg",65),t.nrm(42,"path",66),t.k0s(),t.joV(),t.j41(43,"span"),t.EFF(44,"Actualiser"),t.k0s()()()()()()()}if(2&n){const e=t.XpG();t.R7$(15),t.Y8G("ngModel",e.filterGroupe),t.R7$(2),t.SpI("Tous les groupes (",e.groupes.length,")"),t.R7$(1),t.Y8G("ngForOf",e.groupes),t.R7$(8),t.Y8G("ngModel",e.filterProjet),t.R7$(2),t.SpI("Tous les projets (",e.projets.length,")"),t.R7$(1),t.Y8G("ngForOf",e.projets)}}function _r(n,r){if(1&n){const e=t.RV6();t.j41(0,"div",70)(1,"div",71)(2,"div",4)(3,"div",22)(4,"div",72),t.EFF(5),t.k0s(),t.j41(6,"div",73),t.qSk(),t.j41(7,"svg",74),t.nrm(8,"path",75),t.k0s()()(),t.joV(),t.j41(9,"div",76)(10,"h3",77),t.EFF(11),t.k0s(),t.j41(12,"p",78),t.EFF(13),t.k0s(),t.j41(14,"div",79)(15,"div",80),t.qSk(),t.j41(16,"svg",54),t.nrm(17,"path",55),t.k0s(),t.joV(),t.j41(18,"span",81),t.EFF(19),t.k0s()(),t.j41(20,"div",80),t.qSk(),t.j41(21,"svg",54),t.nrm(22,"path",59),t.k0s(),t.joV(),t.j41(23,"span",81),t.EFF(24),t.k0s()()()()(),t.j41(25,"div",82)(26,"div",53)(27,"div",83),t.qSk(),t.j41(28,"svg",84),t.nrm(29,"path",85),t.k0s()(),t.joV(),t.j41(30,"div")(31,"p",86),t.EFF(32,"\xc9valu\xe9e le"),t.k0s(),t.j41(33,"p",87),t.EFF(34),t.k0s()()(),t.j41(35,"div",53)(36,"div",88),t.qSk(),t.j41(37,"svg",65),t.nrm(38,"path",89),t.k0s()(),t.joV(),t.j41(39,"div")(40,"p",86),t.EFF(41,"Score total"),t.k0s(),t.j41(42,"span",90),t.EFF(43),t.k0s()()(),t.j41(44,"div",91)(45,"p",92),t.EFF(46,"D\xe9tail des scores"),t.k0s(),t.j41(47,"div",93)(48,"div",94)(49,"span"),t.EFF(50,"Structure:"),t.k0s(),t.j41(51,"span",42),t.EFF(52),t.k0s()(),t.j41(53,"div",94)(54,"span"),t.EFF(55,"Pratiques:"),t.k0s(),t.j41(56,"span",42),t.EFF(57),t.k0s()(),t.j41(58,"div",94)(59,"span"),t.EFF(60,"Fonctionnalit\xe9:"),t.k0s(),t.j41(61,"span",42),t.EFF(62),t.k0s()(),t.j41(63,"div",94)(64,"span"),t.EFF(65,"Originalit\xe9:"),t.k0s(),t.j41(66,"span",42),t.EFF(67),t.k0s()()()()(),t.j41(68,"div",95)(69,"button",96),t.bIt("click",function(){const a=t.eBV(e).$implicit,s=t.XpG(2);return t.Njj(s.viewEvaluationDetails(a.rendu))}),t.j41(70,"div",64),t.qSk(),t.j41(71,"svg",97),t.nrm(72,"path",61)(73,"path",98),t.k0s(),t.joV(),t.j41(74,"span"),t.EFF(75,"Voir d\xe9tails"),t.k0s()()(),t.j41(76,"button",99),t.bIt("click",function(){const a=t.eBV(e).$implicit,s=t.XpG(2);return t.Njj(s.editEvaluation(a.rendu))}),t.j41(77,"div",64),t.qSk(),t.j41(78,"svg",97),t.nrm(79,"path",100),t.k0s(),t.joV(),t.j41(80,"span"),t.EFF(81,"Modifier"),t.k0s()()(),t.j41(82,"button",101),t.bIt("click",function(){const a=t.eBV(e).$implicit,s=t.XpG(2);return t.Njj(s.deleteEvaluation(a._id))}),t.j41(83,"div",64),t.qSk(),t.j41(84,"svg",97),t.nrm(85,"path",102),t.k0s(),t.joV(),t.j41(86,"span"),t.EFF(87,"Supprimer"),t.k0s()()()()()()}if(2&n){const e=r.$implicit,i=t.XpG(2);t.R7$(5),t.SpI(" ",i.getStudentInitials(e.etudiant)," "),t.R7$(6),t.SpI(" ",i.getStudentName(e.etudiant)," "),t.R7$(2),t.JRh((null==e.etudiant?null:e.etudiant.email)||"Email non disponible"),t.R7$(6),t.SpI(" ",i.getStudentGroup(e.etudiant)," "),t.R7$(5),t.SpI(" ",i.getProjectTitle(e)," "),t.R7$(10),t.JRh(i.formatDate(e.dateEvaluation)),t.R7$(2),t.Y8G("ngClass",i.getScoreIconClass(i.getScoreTotal(e))),t.R7$(6),t.Y8G("ngClass",i.getScoreColorClass(i.getScoreTotal(e))),t.R7$(1),t.SpI(" ",i.getScoreTotal(e),"/20 "),t.R7$(9),t.JRh(e.scores.structure||0),t.R7$(5),t.JRh(e.scores.pratiques||0),t.R7$(5),t.JRh(e.scores.fonctionnalite||0),t.R7$(5),t.JRh(e.scores.originalite||0)}}function vr(n,r){if(1&n&&(t.j41(0,"div",68),t.DNE(1,_r,88,13,"div",69),t.k0s()),2&n){const e=t.XpG();t.R7$(1),t.Y8G("ngForOf",e.filteredEvaluations)}}function xr(n,r){if(1&n){const e=t.RV6();t.j41(0,"div",103)(1,"div",104)(2,"div",105),t.qSk(),t.j41(3,"svg",106),t.nrm(4,"path",107),t.k0s()(),t.joV(),t.j41(5,"h3",108),t.EFF(6,"Aucune \xe9valuation trouv\xe9e"),t.k0s(),t.j41(7,"p",109),t.EFF(8,"Aucune \xe9valuation ne correspond \xe0 vos crit\xe8res de filtrage actuels."),t.k0s(),t.j41(9,"button",110),t.bIt("click",function(){t.eBV(e);const o=t.XpG();return t.Njj(o.resetFilters())}),t.j41(10,"div",64),t.qSk(),t.j41(11,"svg",65),t.nrm(12,"path",66),t.k0s(),t.joV(),t.j41(13,"span"),t.EFF(14,"R\xe9initialiser les filtres"),t.k0s()()()()()}}function yr(n,r){1&n&&(t.j41(0,"div",11),t.nrm(1,"div",12),t.j41(2,"p"),t.EFF(3,"Chargement des donn\xe9es..."),t.k0s()())}function jr(n,r){if(1&n&&(t.j41(0,"div",13),t.nrm(1,"i",14),t.EFF(2),t.k0s()),2&n){const e=t.XpG();t.R7$(2),t.SpI(" ",e.error," ")}}function wr(n,r){if(1&n&&(t.j41(0,"div",21)(1,"label"),t.EFF(2,"Fichier soumis:"),t.k0s(),t.j41(3,"a",61),t.nrm(4,"i",62),t.EFF(5),t.k0s()()),2&n){const e=t.XpG(2);t.R7$(3),t.Y8G("href",e.getFileUrl(e.rendu.fichierRendu),t.B4B),t.R7$(2),t.SpI(" ",e.getFileName(e.rendu.fichierRendu)," ")}}function Cr(n,r){1&n&&(t.j41(0,"div",63)(1,"small",64),t.EFF(2,"Les commentaires sont obligatoires"),t.k0s()())}function Er(n,r){1&n&&t.nrm(0,"i",65)}function Mr(n,r){1&n&&t.nrm(0,"i",66)}function Fr(n,r){if(1&n){const e=t.RV6();t.j41(0,"div",15)(1,"div",16)(2,"h2",17),t.nrm(3,"i",18),t.EFF(4," Informations sur le rendu "),t.k0s(),t.j41(5,"div",19)(6,"div",20)(7,"label"),t.EFF(8,"Projet:"),t.k0s(),t.j41(9,"span"),t.EFF(10),t.k0s()(),t.j41(11,"div",20)(12,"label"),t.EFF(13,"\xc9tudiant:"),t.k0s(),t.j41(14,"span"),t.EFF(15),t.k0s()(),t.j41(16,"div",20)(17,"label"),t.EFF(18,"Groupe:"),t.k0s(),t.j41(19,"span"),t.EFF(20),t.k0s()(),t.j41(21,"div",20)(22,"label"),t.EFF(23,"Date de soumission:"),t.k0s(),t.j41(24,"span"),t.EFF(25),t.nI1(26,"date"),t.k0s()(),t.j41(27,"div",21)(28,"label"),t.EFF(29,"Description:"),t.k0s(),t.j41(30,"span"),t.EFF(31),t.k0s()(),t.DNE(32,wr,6,2,"div",22),t.k0s()(),t.j41(33,"form",23),t.bIt("ngSubmit",function(){t.eBV(e);const o=t.XpG();return t.Njj(o.onSubmit())}),t.j41(34,"div",24)(35,"h3",17),t.nrm(36,"i",25),t.EFF(37," Crit\xe8res d'\xe9valuation "),t.k0s(),t.j41(38,"div",26)(39,"div",27)(40,"div",28)(41,"label",29),t.nrm(42,"i",30),t.EFF(43," Structure et organisation du code "),t.k0s(),t.j41(44,"div",31),t.nrm(45,"input",32),t.j41(46,"span",33),t.EFF(47,"/ 5"),t.k0s()(),t.j41(48,"small",34),t.EFF(49,"Qualit\xe9 de l'organisation et de la structure du code"),t.k0s()(),t.j41(50,"div",28)(51,"label",35),t.nrm(52,"i",36),t.EFF(53," Bonnes pratiques "),t.k0s(),t.j41(54,"div",31),t.nrm(55,"input",37),t.j41(56,"span",33),t.EFF(57,"/ 5"),t.k0s()(),t.j41(58,"small",34),t.EFF(59,"Respect des conventions et bonnes pratiques"),t.k0s()(),t.j41(60,"div",28)(61,"label",38),t.nrm(62,"i",39),t.EFF(63," Fonctionnalit\xe9 "),t.k0s(),t.j41(64,"div",31),t.nrm(65,"input",40),t.j41(66,"span",33),t.EFF(67,"/ 5"),t.k0s()(),t.j41(68,"small",34),t.EFF(69,"Fonctionnement correct et complet"),t.k0s()(),t.j41(70,"div",28)(71,"label",41),t.nrm(72,"i",42),t.EFF(73," Originalit\xe9 et cr\xe9ativit\xe9 "),t.k0s(),t.j41(74,"div",31),t.nrm(75,"input",43),t.j41(76,"span",33),t.EFF(77,"/ 5"),t.k0s()(),t.j41(78,"small",34),t.EFF(79,"Innovation et cr\xe9ativit\xe9 dans la solution"),t.k0s()()(),t.j41(80,"div",44)(81,"div",45)(82,"span",46),t.EFF(83,"Score total:"),t.k0s(),t.j41(84,"span",47),t.EFF(85),t.k0s(),t.j41(86,"span",48),t.EFF(87),t.k0s()(),t.j41(88,"div",49),t.EFF(89),t.k0s()()(),t.j41(90,"div",50)(91,"label",51),t.nrm(92,"i",52),t.EFF(93," Commentaires et observations "),t.k0s(),t.nrm(94,"textarea",53),t.DNE(95,Cr,3,0,"div",54),t.k0s(),t.j41(96,"div",55)(97,"button",56),t.bIt("click",function(){t.eBV(e);const o=t.XpG();return t.Njj(o.annuler())}),t.nrm(98,"i",57),t.EFF(99," Annuler "),t.k0s(),t.j41(100,"button",58),t.DNE(101,Er,1,0,"i",59),t.DNE(102,Mr,1,0,"i",60),t.EFF(103),t.k0s()()()()()}if(2&n){const e=t.XpG();let i;t.R7$(10),t.JRh((null==e.rendu.projet?null:e.rendu.projet.titre)||"Non sp\xe9cifi\xe9"),t.R7$(5),t.Lme("",null==e.rendu.etudiant?null:e.rendu.etudiant.nom," ",null==e.rendu.etudiant?null:e.rendu.etudiant.prenom,""),t.R7$(5),t.JRh((null==e.rendu.etudiant?null:e.rendu.etudiant.groupe)||"Non sp\xe9cifi\xe9"),t.R7$(5),t.JRh(t.i5U(26,17,e.rendu.dateSoumission,"dd/MM/yyyy \xe0 HH:mm")),t.R7$(6),t.JRh(e.rendu.description||"Aucune description"),t.R7$(1),t.Y8G("ngIf",e.rendu.fichierRendu),t.R7$(1),t.Y8G("formGroup",e.evaluationForm),t.R7$(52),t.JRh(e.getScoreTotal()),t.R7$(2),t.SpI("/ ",e.getScoreMaximum(),""),t.R7$(2),t.SpI(" ",(e.getScoreTotal()/e.getScoreMaximum()*100).toFixed(1),"% "),t.R7$(6),t.Y8G("ngIf",(null==(i=e.evaluationForm.get("commentaires"))?null:i.invalid)&&(null==(i=e.evaluationForm.get("commentaires"))?null:i.touched)),t.R7$(2),t.Y8G("disabled",e.isSubmitting),t.R7$(3),t.Y8G("disabled",e.evaluationForm.invalid||e.isSubmitting),t.R7$(1),t.Y8G("ngIf",e.isSubmitting),t.R7$(1),t.Y8G("ngIf",!e.isSubmitting),t.R7$(1),t.SpI(" ",e.isSubmitting?"Enregistrement...":"Enregistrer l'\xe9valuation"," ")}}const Dr=[{path:"",component:qe},{path:"new",component:We},{path:"editProjet/:id",component:ii},{path:"details/:id",component:hi},{path:"rendus",component:Fi},{path:"evaluate/:renduId",component:Xi},{path:"evaluation-details/:renduId",component:ar},{path:"evaluations",component:(()=>{class n{constructor(e,i,o){this.rendusService=e,this.evaluationService=i,this.router=o,this.evaluations=[],this.filteredEvaluations=[],this.isLoading=!0,this.error="",this.searchTerm="",this.filterGroupe="",this.filterProjet="",this.groupes=[],this.projets=[],this.destroy$=new j.B}ngOnInit(){this.loadEvaluations()}ngOnDestroy(){this.destroy$.next(),this.destroy$.complete()}loadEvaluations(){this.isLoading=!0,this.error="",console.log("D\xe9but du chargement des \xe9valuations..."),this.evaluationService.getAllEvaluations().pipe((0,it.Q)(this.destroy$),(0,L.W)(e=>(this.error="Impossible de charger les \xe9valuations. Veuillez r\xe9essayer plus tard.",this.isLoading=!1,(0,bt.of)([]))),(0,sr.j)(()=>{this.isLoading=!1})).subscribe({next:e=>{console.log("\xc9valuations re\xe7ues:",e),Array.isArray(e)?(this.evaluations=e.map(i=>{const o=i;return(!o.projetDetails||!o.projetDetails.titre)&&(console.warn("D\xe9tails du projet manquants pour l'\xe9valuation:",o._id),o.renduDetails&&o.renduDetails.projet&&(o.projetDetails=o.renduDetails.projet)),o}),this.extractGroupesAndProjets(),this.applyFilters()):this.error="Format de donn\xe9es incorrect. Veuillez r\xe9essayer plus tard."}})}extractGroupesAndProjets(){const e=new Set;this.evaluations.forEach(o=>{if(o.etudiant){const a=this.getStudentGroup(o.etudiant);a&&"Non sp\xe9cifi\xe9"!==a&&e.add(a)}}),this.groupes=Array.from(e).sort();const i=new Map;this.evaluations.forEach(o=>{o.projetDetails&&o.projetDetails._id&&i.set(o.projetDetails._id,o.projetDetails)}),this.projets=Array.from(i.values())}applyFilters(){let e=this.evaluations;if(""!==this.searchTerm.trim()){const i=this.searchTerm.toLowerCase().trim();e=e.filter(o=>{if(!o.etudiant)return!1;const a=this.getStudentName(o.etudiant).toLowerCase(),s=(o.etudiant.email||"").toLowerCase(),d=this.getProjectTitle(o).toLowerCase(),l=this.getStudentGroup(o.etudiant).toLowerCase();return a.includes(i)||s.includes(i)||d.includes(i)||l.includes(i)})}this.filterGroupe&&(e=e.filter(i=>this.getStudentGroup(i.etudiant)===this.filterGroupe)),this.filterProjet&&(e=e.filter(i=>i.projetDetails?._id===this.filterProjet)),this.filteredEvaluations=e}onSearchChange(){this.applyFilters()}clearSearch(){this.searchTerm="",this.applyFilters()}refreshList(){console.log("Actualisation de la liste des \xe9valuations..."),this.searchTerm="",this.filterGroupe="",this.filterProjet="",this.loadEvaluations(),console.log("Liste actualis\xe9e et filtres r\xe9initialis\xe9s")}resetFilters(){console.log("R\xe9initialisation des filtres..."),this.searchTerm="",this.filterGroupe="",this.filterProjet="",this.applyFilters(),console.log("Filtres r\xe9initialis\xe9s")}editEvaluation(e){this.router.navigate(["/admin/projects/edit-evaluation",e])}viewEvaluationDetails(e){this.router.navigate(["/admin/projects/evaluation-details",e])}getScoreTotal(e){if(!e.scores)return 0;const i=e.scores;return i.structure+i.pratiques+i.fonctionnalite+i.originalite}getScoreClass(e){return e>=16?"text-green-600 bg-green-100":e>=12?"text-blue-600 bg-blue-100":e>=8?"text-yellow-600 bg-yellow-100":"text-red-600 bg-red-100"}formatDate(e){return e?new Date(e).toLocaleDateString():"Non disponible"}getStudentInitials(e){if(!e)return"??";const i=e.firstName||"",o=e.lastName||"";if(i&&o&&o.trim())return(i.charAt(0)+o.charAt(0)).toUpperCase();const a=e.fullName||e.name||e.username||"";if(a&&a.trim()){const s=a.trim().split(" ");return s.length>=2?(s[0].charAt(0)+s[1].charAt(0)).toUpperCase():a.substring(0,2).toUpperCase()}return i&&i.trim()?i.substring(0,2).toUpperCase():"??"}getStudentName(e){if(!e)return"Utilisateur inconnu";const i=e.firstName||"",o=e.lastName||"";if(i&&o&&o.trim())return`${i} ${o}`.trim();const a=e.fullName||e.name||e.username||"";return a&&a.trim()?a.trim():i&&i.trim()?i.trim():e.email?e.email:"Utilisateur inconnu"}getStudentGroup(e){return e?(console.log("Donn\xe9es \xe9tudiant pour groupe:",{email:e.email,group:e.group,groupe:e.groupe,groupName:e.groupName,department:e.department,allData:e}),e.group&&"object"==typeof e.group&&e.group.name?(console.log(`Groupe objet trouv\xe9 pour ${e.email}: ${e.group.name}`),e.group.name):e.group&&"string"==typeof e.group&&e.group.trim()?(console.log(`Groupe string trouv\xe9 pour ${e.email}: ${e.group}`),e.group.trim()):e.groupe&&"string"==typeof e.groupe&&e.groupe.trim()?(console.log(`Groupe (ancien champ) trouv\xe9 pour ${e.email}: ${e.groupe}`),e.groupe.trim()):e.groupName&&"string"==typeof e.groupName&&e.groupName.trim()?(console.log(`GroupName trouv\xe9 pour ${e.email}: ${e.groupName}`),e.groupName.trim()):e.department&&"string"==typeof e.department&&e.department.trim()?(console.log(`Department trouv\xe9 pour ${e.email}: ${e.department}`),e.department.trim()):(console.log(`Aucun groupe trouv\xe9 pour ${e.email}`),"Non sp\xe9cifi\xe9")):"Non sp\xe9cifi\xe9"}getProjectTitle(e){return e.projetDetails?.titre||e.renduDetails?.projet?.titre||"Projet inconnu"}getAverageScore(){return 0===this.evaluations.length?"0":(this.evaluations.reduce((o,a)=>o+this.getScoreTotal(a),0)/this.evaluations.length).toFixed(1)}getScoreIconClass(e){return e>=16?"bg-success/10 dark:bg-dark-accent-secondary/10 text-success dark:text-dark-accent-secondary":e>=12?"bg-info/10 dark:bg-dark-accent-primary/10 text-info dark:text-dark-accent-primary":e>=8?"bg-warning/10 dark:bg-warning/20 text-warning dark:text-warning":"bg-danger/10 dark:bg-danger-dark/20 text-danger dark:text-danger-dark"}getScoreColorClass(e){return e>=16?"text-success dark:text-dark-accent-secondary":e>=12?"text-info dark:text-dark-accent-primary":e>=8?"text-warning dark:text-warning":"text-danger dark:text-danger-dark"}deleteEvaluation(e){confirm("\xcates-vous s\xfbr de vouloir supprimer cette \xe9valuation ? Cette action est irr\xe9versible.")&&this.evaluationService.deleteEvaluation(e).subscribe({next:()=>{alert("\xc9valuation supprim\xe9e avec succ\xe8s !"),this.loadEvaluations()},error:i=>{console.error("Erreur lors de la suppression:",i),alert("Erreur lors de la suppression de l'\xe9valuation.")}})}static{this.\u0275fac=function(i){return new(i||n)(t.rXU(P.R),t.rXU(cr),t.rXU(b.Ix))}}static{this.\u0275cmp=t.VBU({type:n,selectors:[["app-evaluations-list"]],decls:31,vars:8,consts:[[1,"min-h-screen","bg-[#edf1f4]","dark:bg-dark-bg-primary","transition-colors","duration-300"],[1,"container","mx-auto","px-4","py-8"],[1,"bg-gradient-to-r","from-primary","to-primary-dark","dark:from-dark-accent-primary","dark:to-dark-accent-secondary","rounded-2xl","p-8","mb-8","shadow-xl"],[1,"flex","items-center","justify-between"],[1,"flex","items-center","space-x-4"],[1,"bg-white/20","dark:bg-black/20","p-3","rounded-xl","backdrop-blur-sm"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-8","h-8","text-white"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 01-2 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"],[1,"text-3xl","font-bold","text-white","mb-2"],[1,"text-white/80"],[1,"hidden","md:flex","items-center","space-x-4","text-white/80"],[1,"text-center"],[1,"text-2xl","font-bold"],[1,"text-sm"],[1,"w-px","h-12","bg-white/20"],["class","bg-white/80 dark:bg-dark-bg-secondary/80 backdrop-blur-sm rounded-2xl p-4 mb-6 shadow-lg border border-gray-200/50 dark:border-dark-bg-tertiary/50",4,"ngIf"],["class","flex flex-col items-center justify-center py-16",4,"ngIf"],["class","bg-danger/10 dark:bg-danger-dark/20 border border-danger/30 dark:border-danger-dark/40 text-danger dark:text-danger-dark rounded-xl p-6 mb-6 backdrop-blur-sm",4,"ngIf"],["class","bg-white/80 dark:bg-dark-bg-secondary/80 backdrop-blur-sm rounded-2xl p-6 mb-8 shadow-lg border border-gray-200/50 dark:border-dark-bg-tertiary/50",4,"ngIf"],["class","space-y-6",4,"ngIf"],["class","text-center py-16",4,"ngIf"],[1,"bg-white/80","dark:bg-dark-bg-secondary/80","backdrop-blur-sm","rounded-2xl","p-4","mb-6","shadow-lg","border","border-gray-200/50","dark:border-dark-bg-tertiary/50"],[1,"relative"],[1,"absolute","inset-y-0","left-0","pl-3","flex","items-center","pointer-events-none"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-5","h-5","text-gray-400","dark:text-dark-text-secondary"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"],["type","text","placeholder","Rechercher par nom, email, projet ou groupe...",1,"block","w-full","pl-10","pr-3","py-3","border-2","border-gray-200","dark:border-dark-bg-tertiary","rounded-xl","leading-5","bg-white","dark:bg-dark-bg-secondary","text-text-dark","dark:text-dark-text-primary","placeholder-gray-500","dark:placeholder-dark-text-secondary","focus:outline-none","focus:border-primary","dark:focus:border-dark-accent-primary","focus:ring-4","focus:ring-primary/10","dark:focus:ring-dark-accent-primary/20","transition-all","duration-200",3,"ngModel","ngModelChange","input"],["class","absolute inset-y-0 right-0 pr-3 flex items-center",4,"ngIf"],["class","mt-2 text-sm text-text dark:text-dark-text-secondary",4,"ngIf"],[1,"absolute","inset-y-0","right-0","pr-3","flex","items-center"],[1,"text-gray-400","hover:text-gray-600","dark:text-dark-text-secondary","dark:hover:text-dark-text-primary","transition-colors",3,"click"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-5","h-5"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M6 18L18 6M6 6l12 12"],[1,"mt-2","text-sm","text-text","dark:text-dark-text-secondary"],[1,"flex","flex-col","items-center","justify-center","py-16"],[1,"animate-spin","rounded-full","h-16","w-16","border-4","border-primary/30","dark:border-dark-accent-primary/30"],[1,"animate-spin","rounded-full","h-16","w-16","border-4","border-transparent","border-t-primary","dark:border-t-dark-accent-primary","absolute","top-0","left-0"],[1,"mt-4","text-text","dark:text-dark-text-secondary","animate-pulse"],[1,"bg-danger/10","dark:bg-danger-dark/20","border","border-danger/30","dark:border-danger-dark/40","text-danger","dark:text-danger-dark","rounded-xl","p-6","mb-6","backdrop-blur-sm"],[1,"flex","items-center","space-x-3"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-5","h-5","text-danger","dark:text-danger-dark","flex-shrink-0"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"],[1,"font-medium"],[1,"px-4","py-2","bg-danger/20","dark:bg-danger-dark/20","text-danger","dark:text-danger-dark","rounded-lg","hover:bg-danger/30","dark:hover:bg-danger-dark/30","transition-colors","font-medium",3,"click"],[1,"bg-white/80","dark:bg-dark-bg-secondary/80","backdrop-blur-sm","rounded-2xl","p-6","mb-8","shadow-lg","border","border-gray-200/50","dark:border-dark-bg-tertiary/50"],[1,"flex","items-center","space-x-3","mb-6"],[1,"bg-gradient-to-r","from-primary","to-primary-dark","dark:from-dark-accent-primary","dark:to-dark-accent-secondary","p-2","rounded-lg"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-5","h-5","text-white"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"],[1,"text-xl","font-bold","text-text-dark","dark:text-dark-text-primary"],[1,"grid","grid-cols-1","md:grid-cols-3","gap-6"],[1,"space-y-2"],[1,"block","text-sm","font-semibold","text-text-dark","dark:text-dark-text-primary"],[1,"flex","items-center","space-x-2"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-4","h-4","text-primary","dark:text-dark-accent-primary"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"],[1,"w-full","px-4","py-3","bg-white","dark:bg-dark-bg-secondary","border-2","border-gray-200","dark:border-dark-bg-tertiary","rounded-xl","focus:outline-none","focus:border-primary","dark:focus:border-dark-accent-primary","focus:ring-4","focus:ring-primary/10","dark:focus:ring-dark-accent-primary/20","transition-all","duration-200","text-text-dark","dark:text-dark-text-primary",3,"ngModel","ngModelChange","change"],["value",""],[3,"value",4,"ngFor","ngForOf"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M19 11H5m14-7H3a2 2 0 00-2 2v12a2 2 0 002 2h16a2 2 0 002-2V6a2 2 0 00-2-2z"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M15 12a3 3 0 11-6 0 3 3 0 016 0z"],[1,"flex","flex-col","space-y-2"],[1,"px-4","py-3","bg-gradient-to-r","from-secondary","to-primary","dark:from-dark-accent-secondary","dark:to-dark-accent-primary","text-white","rounded-xl","hover:shadow-lg","hover:scale-105","transition-all","duration-200","font-medium",3,"click"],[1,"flex","items-center","justify-center","space-x-2"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-4","h-4"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"],[3,"value"],[1,"space-y-6"],["class","bg-white/80 dark:bg-dark-bg-secondary/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-gray-200/50 dark:border-dark-bg-tertiary/50 hover:shadow-xl transition-all duration-300 group",4,"ngFor","ngForOf"],[1,"bg-white/80","dark:bg-dark-bg-secondary/80","backdrop-blur-sm","rounded-2xl","p-6","shadow-lg","border","border-gray-200/50","dark:border-dark-bg-tertiary/50","hover:shadow-xl","transition-all","duration-300","group"],[1,"flex","flex-col","lg:flex-row","lg:items-center","lg:justify-between","space-y-4","lg:space-y-0"],[1,"h-16","w-16","rounded-2xl","bg-gradient-to-br","from-primary","to-primary-dark","dark:from-dark-accent-primary","dark:to-dark-accent-secondary","flex","items-center","justify-center","text-white","text-lg","font-bold","shadow-lg"],[1,"absolute","-bottom-1","-right-1","w-6","h-6","bg-gradient-to-r","from-success","to-success-dark","dark:from-dark-accent-secondary","dark:to-dark-accent-primary","rounded-full","flex","items-center","justify-center"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-3","h-3","text-white"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"],[1,"flex-1"],[1,"text-lg","font-bold","text-text-dark","dark:text-dark-text-primary"],[1,"text-sm","text-text","dark:text-dark-text-secondary"],[1,"flex","items-center","space-x-4","mt-2"],[1,"flex","items-center","space-x-1"],[1,"text-sm","font-medium","text-text-dark","dark:text-dark-text-primary"],[1,"flex","flex-col","sm:flex-row","sm:items-center","space-y-3","sm:space-y-0","sm:space-x-6"],[1,"bg-info/10","dark:bg-dark-accent-primary/10","p-2","rounded-lg"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-4","h-4","text-info","dark:text-dark-accent-primary"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 0h6m-6 0l-2 13a2 2 0 002 2h6a2 2 0 002-2L16 7"],[1,"text-xs","text-text","dark:text-dark-text-secondary"],[1,"text-sm","font-semibold","text-text-dark","dark:text-dark-text-primary"],[1,"p-2","rounded-lg",3,"ngClass"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"],[1,"text-lg","font-bold",3,"ngClass"],[1,"bg-gray-50","dark:bg-dark-bg-tertiary/50","rounded-lg","p-3"],[1,"text-xs","text-text","dark:text-dark-text-secondary","mb-1"],[1,"grid","grid-cols-2","gap-1","text-xs"],[1,"flex","justify-between"],[1,"flex","flex-col","sm:flex-row","gap-2"],[1,"group/btn","px-4","py-2","bg-gradient-to-r","from-info","to-primary","dark:from-dark-accent-primary","dark:to-dark-accent-secondary","text-white","rounded-xl","hover:shadow-lg","hover:scale-105","transition-all","duration-200","font-medium",3,"click"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-4","h-4","group-hover/btn:scale-110","transition-transform"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"],[1,"group/btn","px-4","py-2","bg-gradient-to-r","from-secondary","to-primary-dark","dark:from-dark-accent-secondary","dark:to-dark-accent-primary","text-white","rounded-xl","hover:shadow-lg","hover:scale-105","transition-all","duration-200","font-medium",3,"click"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"],[1,"group/btn","px-4","py-2","bg-gradient-to-r","from-danger","to-danger-dark","dark:from-danger-dark","dark:to-danger","text-white","rounded-xl","hover:shadow-lg","hover:scale-105","transition-all","duration-200","font-medium",3,"click"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"],[1,"text-center","py-16"],[1,"bg-white/80","dark:bg-dark-bg-secondary/80","backdrop-blur-sm","rounded-2xl","p-12","shadow-lg","border","border-gray-200/50","dark:border-dark-bg-tertiary/50","max-w-md","mx-auto"],[1,"bg-gradient-to-br","from-primary/10","to-secondary/10","dark:from-dark-accent-primary/20","dark:to-dark-accent-secondary/20","rounded-2xl","p-6","mb-6"],["fill","none","viewBox","0 0 24 24","stroke","currentColor",1,"h-16","w-16","mx-auto","text-primary","dark:text-dark-accent-primary"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","1.5","d","M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 01-2 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"],[1,"text-xl","font-bold","text-text-dark","dark:text-dark-text-primary","mb-2"],[1,"text-text","dark:text-dark-text-secondary","mb-4"],[1,"px-6","py-2","bg-gradient-to-r","from-primary","to-primary-dark","dark:from-dark-accent-primary","dark:to-dark-accent-secondary","text-white","rounded-xl","hover:shadow-lg","hover:scale-105","transition-all","duration-200","font-medium",3,"click"]],template:function(i,o){1&i&&(t.j41(0,"div",0)(1,"div",1)(2,"div",2)(3,"div",3)(4,"div",4)(5,"div",5),t.qSk(),t.j41(6,"svg",6),t.nrm(7,"path",7),t.k0s()(),t.joV(),t.j41(8,"div")(9,"h1",8),t.EFF(10,"Liste des \xc9valuations"),t.k0s(),t.j41(11,"p",9),t.EFF(12,"Gestion et suivi des \xe9valuations de projets"),t.k0s()()(),t.j41(13,"div",10)(14,"div",11)(15,"div",12),t.EFF(16),t.k0s(),t.j41(17,"div",13),t.EFF(18,"Total"),t.k0s()(),t.nrm(19,"div",14),t.j41(20,"div",11)(21,"div",12),t.EFF(22),t.k0s(),t.j41(23,"div",13),t.EFF(24,"Moyenne"),t.k0s()()()()(),t.DNE(25,mr,8,3,"div",15),t.DNE(26,pr,6,0,"div",16),t.DNE(27,hr,9,1,"div",17),t.DNE(28,br,45,6,"div",18),t.DNE(29,vr,2,1,"div",19),t.DNE(30,xr,15,0,"div",20),t.k0s()()),2&i&&(t.R7$(16),t.JRh(o.evaluations.length),t.R7$(6),t.JRh(o.getAverageScore()),t.R7$(3),t.Y8G("ngIf",!o.isLoading&&!o.error),t.R7$(1),t.Y8G("ngIf",o.isLoading),t.R7$(1),t.Y8G("ngIf",o.error),t.R7$(1),t.Y8G("ngIf",!o.isLoading&&!o.error),t.R7$(1),t.Y8G("ngIf",!o.isLoading&&!o.error&&o.filteredEvaluations.length>0),t.R7$(1),t.Y8G("ngIf",!o.isLoading&&!o.error&&0===o.filteredEvaluations.length))},dependencies:[p.YU,p.Sq,p.bT,c.xH,c.y7,c.me,c.wz,c.BC,c.vS],styles:['.loading-spinner[_ngcontent-%COMP%]{display:flex;justify-content:center;margin:2rem 0}.error-message[_ngcontent-%COMP%]{color:#dc3545;margin-top:.25rem}@keyframes _ngcontent-%COMP%_fadeInUp{0%{opacity:0;transform:translateY(20px)}to{opacity:1;transform:translateY(0)}}@keyframes _ngcontent-%COMP%_slideInRight{0%{opacity:0;transform:translate(30px)}to{opacity:1;transform:translate(0)}}@keyframes _ngcontent-%COMP%_scaleIn{0%{opacity:0;transform:scale(.9)}to{opacity:1;transform:scale(1)}}.glass-card[_ngcontent-%COMP%]{backdrop-filter:blur(10px);-webkit-backdrop-filter:blur(10px);border:1px solid rgba(255,255,255,.2)}.dark[_ngcontent-%COMP%]   .glass-card[_ngcontent-%COMP%]{border:1px solid rgba(255,255,255,.1)}.evaluation-card[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_fadeInUp .6s ease-out;transition:all .3s cubic-bezier(.4,0,.2,1)}.evaluation-card[_ngcontent-%COMP%]:hover{transform:translateY(-4px);box-shadow:0 20px 40px #0000001a}.dark[_ngcontent-%COMP%]   .evaluation-card[_ngcontent-%COMP%]:hover{box-shadow:0 20px 40px #0000004d}.btn-modern[_ngcontent-%COMP%]{position:relative;overflow:hidden;transition:all .3s cubic-bezier(.4,0,.2,1)}.btn-modern[_ngcontent-%COMP%]:before{content:"";position:absolute;top:0;left:-100%;width:100%;height:100%;background:linear-gradient(90deg,transparent,rgba(255,255,255,.2),transparent);transition:left .5s}.btn-modern[_ngcontent-%COMP%]:hover:before{left:100%}.avatar-gradient[_ngcontent-%COMP%]{background:linear-gradient(135deg,#4f5fad 0%,#7826b5 100%);transition:all .3s ease}.dark[_ngcontent-%COMP%]   .avatar-gradient[_ngcontent-%COMP%]{background:linear-gradient(135deg,#00f7ff 0%,#9d4edd 100%)}.avatar-gradient[_ngcontent-%COMP%]:hover{transform:scale(1.05);box-shadow:0 8px 25px #4f5fad4d}.dark[_ngcontent-%COMP%]   .avatar-gradient[_ngcontent-%COMP%]:hover{box-shadow:0 8px 25px #00f7ff4d}.score-badge[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_scaleIn .4s ease-out;transition:all .2s ease}.score-badge[_ngcontent-%COMP%]:hover{transform:scale(1.05)}.filter-select[_ngcontent-%COMP%]{transition:all .3s cubic-bezier(.4,0,.2,1)}.filter-select[_ngcontent-%COMP%]:focus{transform:translateY(-2px);box-shadow:0 8px 25px #4f5fad26}.dark[_ngcontent-%COMP%]   .filter-select[_ngcontent-%COMP%]:focus{box-shadow:0 8px 25px #00f7ff26}.header-gradient[_ngcontent-%COMP%]{background:linear-gradient(135deg,#4f5fad 0%,#7826b5 100%);animation:_ngcontent-%COMP%_slideInRight .8s ease-out}.dark[_ngcontent-%COMP%]   .header-gradient[_ngcontent-%COMP%]{background:linear-gradient(135deg,#00f7ff 0%,#9d4edd 100%)}@media (max-width: 768px){.evaluation-card[_ngcontent-%COMP%]{margin-bottom:1rem}.btn-modern[_ngcontent-%COMP%]{width:100%;justify-content:center}.filter-grid[_ngcontent-%COMP%]{grid-template-columns:1fr;gap:1rem}}.icon-hover[_ngcontent-%COMP%]{transition:transform .2s ease}.icon-hover[_ngcontent-%COMP%]:hover{transform:scale(1.1) rotate(5deg)}.tooltip[_ngcontent-%COMP%]{position:relative}.tooltip[_ngcontent-%COMP%]:after{content:attr(data-tooltip);position:absolute;bottom:100%;left:50%;transform:translate(-50%);background:rgba(0,0,0,.8);color:#fff;padding:.5rem;border-radius:.375rem;font-size:.75rem;white-space:nowrap;opacity:0;pointer-events:none;transition:opacity .3s;z-index:1000}.tooltip[_ngcontent-%COMP%]:hover:after{opacity:1}.loading-pulse[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_pulse 2s infinite}@keyframes _ngcontent-%COMP%_pulse{0%,to{opacity:1}50%{opacity:.5}}.empty-state[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_fadeInUp .8s ease-out}.focus-visible[_ngcontent-%COMP%]:focus{outline:2px solid #4f5fad;outline-offset:2px}.dark[_ngcontent-%COMP%]   .focus-visible[_ngcontent-%COMP%]:focus{outline:2px solid #00f7ff}.delete-btn[_ngcontent-%COMP%]{background:linear-gradient(135deg,#dc2626 0%,#b91c1c 100%);transition:all .3s ease}.delete-btn[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,#b91c1c 0%,#991b1b 100%);transform:scale(1.05);box-shadow:0 8px 25px #dc26264d}.dark[_ngcontent-%COMP%]   .delete-btn[_ngcontent-%COMP%]{background:linear-gradient(135deg,#ef4444 0%,#dc2626 100%)}.dark[_ngcontent-%COMP%]   .delete-btn[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,#dc2626 0%,#b91c1c 100%);box-shadow:0 8px 25px #ef44444d}']})}}return n})()},{path:"edit-evaluation/:renduId",component:(()=>{class n{constructor(e,i,o,a){this.fb=e,this.route=i,this.router=o,this.rendusService=a,this.renduId="",this.rendu=null,this.isLoading=!0,this.isSubmitting=!1,this.error="",this.evaluationForm=this.fb.group({scores:this.fb.group({structure:[0,[c.k0.required,c.k0.min(0),c.k0.max(5)]],pratiques:[0,[c.k0.required,c.k0.min(0),c.k0.max(5)]],fonctionnalite:[0,[c.k0.required,c.k0.min(0),c.k0.max(5)]],originalite:[0,[c.k0.required,c.k0.min(0),c.k0.max(5)]]}),commentaires:["",c.k0.required]})}ngOnInit(){this.renduId=this.route.snapshot.paramMap.get("renduId")||"",this.renduId?this.loadRendu():(this.error="ID de rendu manquant",this.isLoading=!1)}loadRendu(){this.isLoading=!0,this.rendusService.getRenduById(this.renduId).subscribe({next:e=>{this.rendu=e,this.rendu.evaluation&&this.rendu.evaluation.scores&&this.evaluationForm.patchValue({scores:{structure:this.rendu.evaluation.scores.structure||0,pratiques:this.rendu.evaluation.scores.pratiques||0,fonctionnalite:this.rendu.evaluation.scores.fonctionnalite||0,originalite:this.rendu.evaluation.scores.originalite||0},commentaires:this.rendu.evaluation.commentaires||""}),this.isLoading=!1},error:e=>{this.error="Erreur lors du chargement du rendu",this.isLoading=!1,console.error(e)}})}onSubmit(){if(this.evaluationForm.invalid)return;this.isSubmitting=!0;const e=this.evaluationForm.value;if(!this.renduId)return this.error="ID du rendu manquant",void(this.isSubmitting=!1);this.rendusService.updateEvaluation(this.renduId,e).subscribe({next:i=>{this.isSubmitting=!1,this.router.navigate(["/admin/projects/list-rendus"])},error:i=>{this.error=`Erreur lors de la mise \xe0 jour de l'\xe9valuation: ${i.message||"Erreur inconnue"}`,this.isSubmitting=!1,console.error(i)}})}getScoreTotal(){const e=this.evaluationForm.get("scores")?.value;return e?e.structure+e.pratiques+e.fonctionnalite+e.originalite:0}getScoreMaximum(){return 20}annuler(){this.router.navigate(["/admin/projects/list-rendus"])}getFileUrl(e){if(!e)return"";let i=e;if(e.includes("/")||e.includes("\\")){const o=e.split(/[\/\\]/);i=o[o.length-1]}return`${E.c.urlBackend}projets/telecharger/${i}`}getFileName(e){if(!e)return"Fichier";if(e.includes("/")||e.includes("\\")){const i=e.split(/[\/\\]/);return i[i.length-1]}return e}static{this.\u0275fac=function(i){return new(i||n)(t.rXU(c.ok),t.rXU(b.nX),t.rXU(b.Ix),t.rXU(P.R))}}static{this.\u0275cmp=t.VBU({type:n,selectors:[["app-edit-evaluation"]],decls:18,vars:3,consts:[[1,"evaluation-container"],[1,"header-section"],[1,"page-title"],[1,"breadcrumb"],["routerLink","/admin/projects",1,"breadcrumb-link"],[1,"breadcrumb-separator"],["routerLink","/admin/projects/list-rendus",1,"breadcrumb-link"],[1,"breadcrumb-current"],["class","loading-spinner",4,"ngIf"],["class","error-message",4,"ngIf"],["class","main-content",4,"ngIf"],[1,"loading-spinner"],[1,"spinner"],[1,"error-message"],[1,"fas","fa-exclamation-triangle"],[1,"main-content"],[1,"rendu-info-card"],[1,"card-title"],[1,"fas","fa-file-alt"],[1,"info-grid"],[1,"info-item"],[1,"info-item","full-width"],["class","info-item full-width",4,"ngIf"],[1,"evaluation-form",3,"formGroup","ngSubmit"],[1,"form-card"],[1,"fas","fa-star"],["formGroupName","scores",1,"scores-section"],[1,"score-grid"],[1,"score-item"],["for","structure",1,"score-label"],[1,"fas","fa-code"],[1,"score-input-container"],["id","structure","type","number","formControlName","structure","min","0","max","5","step","0.5","placeholder","0",1,"score-input"],[1,"score-max"],[1,"score-description"],["for","pratiques",1,"score-label"],[1,"fas","fa-check-circle"],["id","pratiques","type","number","formControlName","pratiques","min","0","max","5","step","0.5","placeholder","0",1,"score-input"],["for","fonctionnalite",1,"score-label"],[1,"fas","fa-cogs"],["id","fonctionnalite","type","number","formControlName","fonctionnalite","min","0","max","5","step","0.5","placeholder","0",1,"score-input"],["for","originalite",1,"score-label"],[1,"fas","fa-lightbulb"],["id","originalite","type","number","formControlName","originalite","min","0","max","5","step","0.5","placeholder","0",1,"score-input"],[1,"score-total"],[1,"total-display"],[1,"total-label"],[1,"total-value"],[1,"total-max"],[1,"total-percentage"],[1,"comments-section"],["for","commentaires",1,"comments-label"],[1,"fas","fa-comment-alt"],["id","commentaires","formControlName","commentaires","rows","6","placeholder","Ajoutez vos commentaires d\xe9taill\xe9s sur le travail de l'\xe9tudiant...",1,"comments-textarea"],["class","form-validation",4,"ngIf"],[1,"actions"],["type","button",1,"btn","btn-secondary",3,"disabled","click"],[1,"fas","fa-times"],["type","submit",1,"btn","btn-primary",3,"disabled"],["class","fas fa-spinner fa-spin",4,"ngIf"],["class","fas fa-save",4,"ngIf"],["target","_blank",1,"file-link",3,"href"],[1,"fas","fa-download"],[1,"form-validation"],[1,"error-text"],[1,"fas","fa-spinner","fa-spin"],[1,"fas","fa-save"]],template:function(i,o){1&i&&(t.j41(0,"div",0)(1,"div",1)(2,"h1",2),t.EFF(3,"Modifier l'\xe9valuation"),t.k0s(),t.j41(4,"nav",3)(5,"a",4),t.EFF(6,"Projets"),t.k0s(),t.j41(7,"span",5),t.EFF(8,">"),t.k0s(),t.j41(9,"a",6),t.EFF(10,"Rendus"),t.k0s(),t.j41(11,"span",5),t.EFF(12,">"),t.k0s(),t.j41(13,"span",7),t.EFF(14,"Modifier \xe9valuation"),t.k0s()()(),t.DNE(15,yr,4,0,"div",8),t.DNE(16,jr,3,1,"div",9),t.DNE(17,Fr,104,20,"div",10),t.k0s()),2&i&&(t.R7$(15),t.Y8G("ngIf",o.isLoading),t.R7$(1),t.Y8G("ngIf",o.error),t.R7$(1),t.Y8G("ngIf",o.rendu&&!o.isLoading))},dependencies:[p.bT,b.Wk,c.qT,c.me,c.Q0,c.BC,c.cb,c.VZ,c.zX,c.j4,c.JD,c.$R,p.vh],styles:['.evaluation-container[_ngcontent-%COMP%]{max-width:1200px;margin:0 auto;padding:20px;background-color:#f8fafc;min-height:100vh}.header-section[_ngcontent-%COMP%]{margin-bottom:30px}.page-title[_ngcontent-%COMP%]{font-size:2rem;font-weight:700;color:#1e293b;margin-bottom:10px;display:flex;align-items:center;gap:10px}.page-title[_ngcontent-%COMP%]:before{content:"\\1f4dd";font-size:1.5rem}.breadcrumb[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;font-size:.875rem;color:#64748b}.breadcrumb-link[_ngcontent-%COMP%]{color:#3b82f6;text-decoration:none;transition:color .2s}.breadcrumb-link[_ngcontent-%COMP%]:hover{color:#1d4ed8;text-decoration:underline}.breadcrumb-separator[_ngcontent-%COMP%]{color:#94a3b8}.breadcrumb-current[_ngcontent-%COMP%]{color:#1e293b;font-weight:500}.loading-spinner[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:60px 20px;text-align:center}.spinner[_ngcontent-%COMP%]{width:50px;height:50px;border:4px solid #e2e8f0;border-top:4px solid #3b82f6;border-radius:50%;animation:_ngcontent-%COMP%_spin 1s linear infinite;margin-bottom:15px}@keyframes _ngcontent-%COMP%_spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}.loading-spinner[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#64748b;font-size:1rem;margin:0}.error-message[_ngcontent-%COMP%]{background-color:#fef2f2;border:1px solid #fecaca;color:#dc2626;padding:16px;border-radius:8px;margin-bottom:20px;display:flex;align-items:center;gap:10px;font-weight:500}.error-message[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1.2rem}.main-content[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:25px}.rendu-info-card[_ngcontent-%COMP%]{background:white;border-radius:12px;padding:25px;box-shadow:0 1px 3px #0000001a;border:1px solid #e2e8f0}.card-title[_ngcontent-%COMP%]{font-size:1.25rem;font-weight:600;color:#1e293b;margin-bottom:20px;display:flex;align-items:center;gap:10px;padding-bottom:10px;border-bottom:2px solid #f1f5f9}.card-title[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#3b82f6;font-size:1.1rem}.info-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(300px,1fr));gap:20px}.info-item[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:5px}.info-item.full-width[_ngcontent-%COMP%]{grid-column:1 / -1}.info-item[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{font-weight:600;color:#374151;font-size:.875rem;text-transform:uppercase;letter-spacing:.05em}.info-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{color:#1e293b;font-size:1rem;padding:8px 12px;background-color:#f8fafc;border-radius:6px;border:1px solid #e2e8f0}.file-link[_ngcontent-%COMP%]{display:inline-flex;align-items:center;gap:8px;color:#3b82f6;text-decoration:none;padding:8px 12px;background-color:#eff6ff;border:1px solid #bfdbfe;border-radius:6px;transition:all .2s;font-weight:500}.file-link[_ngcontent-%COMP%]:hover{background-color:#dbeafe;border-color:#93c5fd;transform:translateY(-1px)}.file-link[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:.875rem}.evaluation-form[_ngcontent-%COMP%]{background:white;border-radius:12px;box-shadow:0 1px 3px #0000001a;border:1px solid #e2e8f0;overflow:hidden}.form-card[_ngcontent-%COMP%]{padding:25px}.scores-section[_ngcontent-%COMP%]{margin-bottom:30px}.score-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(280px,1fr));gap:25px;margin-bottom:25px}.score-item[_ngcontent-%COMP%]{background-color:#f8fafc;padding:20px;border-radius:10px;border:1px solid #e2e8f0;transition:all .2s}.score-item[_ngcontent-%COMP%]:hover{border-color:#cbd5e1;box-shadow:0 2px 4px #0000000d}.score-label[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;font-weight:600;color:#374151;margin-bottom:10px;font-size:.95rem}.score-label[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#3b82f6;font-size:1rem}.score-input-container[_ngcontent-%COMP%]{display:flex;align-items:center;gap:10px;margin-bottom:8px}.score-input[_ngcontent-%COMP%]{flex:1;padding:12px 15px;border:2px solid #e2e8f0;border-radius:8px;font-size:1.1rem;font-weight:600;text-align:center;transition:all .2s;background-color:#fff}.score-input[_ngcontent-%COMP%]:focus{outline:none;border-color:#3b82f6;box-shadow:0 0 0 3px #3b82f61a}.score-input[_ngcontent-%COMP%]:invalid{border-color:#ef4444}.score-max[_ngcontent-%COMP%]{font-weight:600;color:#64748b;font-size:1rem}.score-description[_ngcontent-%COMP%]{color:#64748b;font-size:.8rem;line-height:1.4;font-style:italic}.score-total[_ngcontent-%COMP%]{background:linear-gradient(135deg,#667eea 0%,#764ba2 100%);color:#fff;padding:20px;border-radius:12px;text-align:center;margin-top:20px;box-shadow:0 4px 6px #0000001a}.total-display[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;gap:10px;margin-bottom:8px}.total-label[_ngcontent-%COMP%]{font-size:1.1rem;font-weight:500}.total-value[_ngcontent-%COMP%]{font-size:2rem;font-weight:700}.total-max[_ngcontent-%COMP%]{font-size:1.1rem;opacity:.9}.total-percentage[_ngcontent-%COMP%]{font-size:1rem;opacity:.9;font-weight:500}.comments-section[_ngcontent-%COMP%]{margin-bottom:30px}.comments-label[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;font-weight:600;color:#374151;margin-bottom:12px;font-size:1rem}.comments-label[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#3b82f6;font-size:1rem}.comments-textarea[_ngcontent-%COMP%]{width:100%;padding:15px;border:2px solid #e2e8f0;border-radius:8px;font-size:.95rem;line-height:1.6;resize:vertical;min-height:120px;transition:all .2s;font-family:inherit}.comments-textarea[_ngcontent-%COMP%]:focus{outline:none;border-color:#3b82f6;box-shadow:0 0 0 3px #3b82f61a}.comments-textarea[_ngcontent-%COMP%]::placeholder{color:#9ca3af;font-style:italic}.form-validation[_ngcontent-%COMP%]{margin-top:5px}.error-text[_ngcontent-%COMP%]{color:#ef4444;font-size:.8rem;font-weight:500}.actions[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;gap:15px;padding-top:25px;border-top:1px solid #e2e8f0}.btn[_ngcontent-%COMP%]{display:inline-flex;align-items:center;gap:8px;padding:12px 24px;border:none;border-radius:8px;font-size:.95rem;font-weight:600;text-decoration:none;cursor:pointer;transition:all .2s;min-width:140px;justify-content:center}.btn[_ngcontent-%COMP%]:disabled{opacity:.6;cursor:not-allowed;transform:none!important}.btn-primary[_ngcontent-%COMP%]{background:linear-gradient(135deg,#3b82f6 0%,#1d4ed8 100%);color:#fff;box-shadow:0 2px 4px #3b82f64d}.btn-primary[_ngcontent-%COMP%]:hover:not(:disabled){transform:translateY(-1px);box-shadow:0 4px 8px #3b82f666}.btn-primary[_ngcontent-%COMP%]:active:not(:disabled){transform:translateY(0)}.btn-secondary[_ngcontent-%COMP%]{background-color:#f8fafc;color:#64748b;border:2px solid #e2e8f0}.btn-secondary[_ngcontent-%COMP%]:hover:not(:disabled){background-color:#f1f5f9;border-color:#cbd5e1;color:#475569;transform:translateY(-1px)}.btn-secondary[_ngcontent-%COMP%]:active:not(:disabled){transform:translateY(0)}.btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:.9rem}@media (max-width: 768px){.evaluation-container[_ngcontent-%COMP%]{padding:15px}.page-title[_ngcontent-%COMP%]{font-size:1.5rem}.score-grid[_ngcontent-%COMP%]{grid-template-columns:1fr;gap:20px}.info-grid[_ngcontent-%COMP%]{grid-template-columns:1fr}.actions[_ngcontent-%COMP%]{flex-direction:column-reverse;gap:10px}.btn[_ngcontent-%COMP%]{width:100%}.total-display[_ngcontent-%COMP%]{flex-direction:column;gap:5px}.total-value[_ngcontent-%COMP%]{font-size:1.8rem}}@media (max-width: 480px){.evaluation-container[_ngcontent-%COMP%]{padding:10px}.rendu-info-card[_ngcontent-%COMP%], .form-card[_ngcontent-%COMP%]{padding:20px}.page-title[_ngcontent-%COMP%]{font-size:1.3rem}.score-item[_ngcontent-%COMP%]{padding:15px}}@keyframes _ngcontent-%COMP%_fadeIn{0%{opacity:0;transform:translateY(10px)}to{opacity:1;transform:translateY(0)}}.main-content[_ngcontent-%COMP%], .score-item[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_fadeIn .3s ease-out}.score-item[_ngcontent-%COMP%]:nth-child(1){animation-delay:.1s}.score-item[_ngcontent-%COMP%]:nth-child(2){animation-delay:.2s}.score-item[_ngcontent-%COMP%]:nth-child(3){animation-delay:.3s}.score-item[_ngcontent-%COMP%]:nth-child(4){animation-delay:.4s}.score-input[_ngcontent-%COMP%]:focus, .comments-textarea[_ngcontent-%COMP%]:focus{transform:translateY(-1px)}.btn[_ngcontent-%COMP%]:focus, .score-input[_ngcontent-%COMP%]:focus, .comments-textarea[_ngcontent-%COMP%]:focus{outline:2px solid #3b82f6;outline-offset:2px}.score-input[value="5"][_ngcontent-%COMP%]{background-color:#dcfce7;border-color:#16a34a;color:#15803d}.score-input[value="4"][_ngcontent-%COMP%]{background-color:#fef3c7;border-color:#d97706;color:#92400e}.score-input[value="3"][_ngcontent-%COMP%]{background-color:#fef3c7;border-color:#f59e0b;color:#d97706}.score-input[value="2"][_ngcontent-%COMP%]{background-color:#fed7aa;border-color:#ea580c;color:#c2410c}.score-input[value="1"][_ngcontent-%COMP%]{background-color:#fecaca;border-color:#dc2626;color:#b91c1c}.score-input[value="0"][_ngcontent-%COMP%]{background-color:#f3f4f6;border-color:#9ca3af;color:#6b7280}']})}}return n})()}];let Or=(()=>{class n{static{this.\u0275fac=function(i){return new(i||n)}}static{this.\u0275mod=t.$C({type:n})}static{this.\u0275inj=t.G2t({imports:[b.iI.forChild(Dr),b.iI]})}}return n})(),Sr=(()=>{class n{static{this.\u0275fac=function(i){return new(i||n)}}static{this.\u0275mod=t.$C({type:n})}static{this.\u0275inj=t.G2t({imports:[O,fe,O]})}}return n})(),Ir=(()=>{class n{static{this.\u0275fac=function(i){return new(i||n)}}static{this.\u0275mod=t.$C({type:n})}static{this.\u0275inj=t.G2t({imports:[p.MD,Or,c.YN,c.X1,b.iI,Oe,Sr]})}}return n})()}}]);