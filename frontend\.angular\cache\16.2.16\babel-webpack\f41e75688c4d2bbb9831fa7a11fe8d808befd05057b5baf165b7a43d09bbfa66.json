{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/OneDrive/Bureau/Project PI/devBridge/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { BehaviorSubject, throwError, from } from 'rxjs';\nimport { map, catchError, switchMap } from 'rxjs/operators';\nimport { CallType, CallStatus } from '../models/message.model';\nimport { INITIATE_CALL_MUTATION, ACCEPT_CALL_MUTATION, REJECT_CALL_MUTATION, END_CALL_MUTATION, TOGGLE_CALL_MEDIA_MUTATION, INCOMING_CALL_SUBSCRIPTION, CALL_STATUS_CHANGED_SUBSCRIPTION, CALL_SIGNAL_SUBSCRIPTION } from '../graphql/message.graphql';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"apollo-angular\";\nimport * as i2 from \"./logger.service\";\nexport class CallService {\n  constructor(apollo, logger) {\n    this.apollo = apollo;\n    this.logger = logger;\n    // État des appels\n    this.activeCall = new BehaviorSubject(null);\n    this.incomingCall = new BehaviorSubject(null);\n    // Observables publics\n    this.activeCall$ = this.activeCall.asObservable();\n    this.incomingCall$ = this.incomingCall.asObservable();\n    // Propriétés pour la gestion des sons\n    this.sounds = {};\n    this.isPlaying = {};\n    this.muted = false;\n    // États simples pour les médias\n    this.isVideoEnabled = true;\n    this.isAudioEnabled = true;\n    // WebRTC\n    this.peerConnection = null;\n    this.localStream = null;\n    this.remoteStream = null;\n    this.localVideoElement = null;\n    this.remoteVideoElement = null;\n    this.currentCallId = null;\n    this.preloadSounds();\n    this.initializeSubscriptions();\n    this.initializeWebRTC();\n  }\n  /**\n   * Initialise les subscriptions avec un délai pour s'assurer que l'authentification est prête\n   */\n  initializeSubscriptions() {\n    // Attendre un peu pour s'assurer que l'authentification est prête\n    setTimeout(() => {\n      this.subscribeToIncomingCalls();\n      this.subscribeToCallStatusChanges();\n      this.subscribeToCallSignals();\n    }, 1000);\n    // Réessayer UNE SEULE FOIS après 10 secondes si nécessaire\n    setTimeout(() => {\n      if (!this.incomingCall.value) {\n        // console.log('🔄 [CallService] Retrying subscription initialization...');\n        this.subscribeToIncomingCalls();\n        this.subscribeToCallStatusChanges();\n        this.subscribeToCallSignals();\n      }\n    }, 10000);\n  }\n  /**\n   * Initialise WebRTC\n   */\n  initializeWebRTC() {\n    console.log('🔧 [CallService] Initializing WebRTC...');\n    // Configuration des serveurs STUN/TURN\n    const configuration = {\n      iceServers: [{\n        urls: 'stun:stun.l.google.com:19302'\n      }, {\n        urls: 'stun:stun1.l.google.com:19302'\n      }]\n    };\n    try {\n      this.peerConnection = new RTCPeerConnection(configuration);\n      this.setupPeerConnectionEvents();\n      console.log('✅ [CallService] WebRTC initialized successfully');\n    } catch (error) {\n      console.error('❌ [CallService] Failed to initialize WebRTC:', error);\n    }\n  }\n  /**\n   * Configure les événements de la PeerConnection\n   */\n  setupPeerConnectionEvents() {\n    if (!this.peerConnection) return;\n    // Quand on reçoit un stream distant\n    this.peerConnection.ontrack = event => {\n      console.log('📺 [CallService] Remote stream received');\n      console.log('🔍 [CallService] Stream details:', {\n        streamId: event.streams[0]?.id,\n        tracks: event.streams[0]?.getTracks().map(t => ({\n          kind: t.kind,\n          enabled: t.enabled,\n          readyState: t.readyState\n        }))\n      });\n      this.remoteStream = event.streams[0];\n      if (this.remoteVideoElement) {\n        console.log('🎥 [CallService] Setting remote stream to video element');\n        this.remoteVideoElement.srcObject = this.remoteStream;\n        // Configurer les événements de l'élément vidéo\n        this.remoteVideoElement.onloadedmetadata = () => {\n          console.log('🎵 [CallService] Remote video metadata loaded');\n        };\n        this.remoteVideoElement.oncanplay = () => {\n          console.log('🎵 [CallService] Remote video can play');\n        };\n        this.remoteVideoElement.onplay = () => {\n          console.log('🎵 [CallService] Remote video started playing');\n        };\n        // Forcer la lecture audio immédiatement\n        try {\n          this.ensureAudioPlayback(this.remoteVideoElement);\n        } catch (error) {\n          console.warn('⚠️ [CallService] ensureAudioPlayback error in ontrack:', error);\n          // Fallback simple : forcer la lecture directement\n          console.log('🔄 [CallService] Trying direct play as fallback');\n          this.remoteVideoElement.play().catch(e => {\n            console.warn('⚠️ [CallService] Direct play failed:', e);\n            // Dernier recours : attendre une interaction utilisateur\n            console.log('⏳ [CallService] Waiting for user interaction to enable audio');\n          });\n        }\n      } else {\n        console.warn('⚠️ [CallService] No remote video element available');\n      }\n    };\n    // Gestion des candidats ICE\n    this.peerConnection.onicecandidate = event => {\n      if (event.candidate) {\n        console.log('🧊 [CallService] ICE candidate generated');\n        this.sendSignal('ice-candidate', JSON.stringify(event.candidate));\n      }\n    };\n    // État de la connexion\n    this.peerConnection.onconnectionstatechange = () => {\n      console.log('🔗 [CallService] Connection state:', this.peerConnection?.connectionState);\n    };\n  }\n  /**\n   * Précharge les sons utilisés dans l'application\n   */\n  preloadSounds() {\n    // Créer des sons synthétiques de haute qualité\n    this.createSyntheticSounds();\n    // Charger le son de notification qui existe encore\n    this.loadSound('notification', 'assets/sounds/notification.mp3');\n    console.log('🎵 [CallService] Beautiful synthetic melodies created for calls');\n  }\n  /**\n   * Crée des sons synthétiques comme fallback\n   */\n  createSyntheticSounds() {\n    try {\n      // Créer un contexte audio pour les sons synthétiques\n      const audioContext = new (window.AudioContext || window.webkitAudioContext)();\n      // Son de sonnerie (mélodie agréable)\n      this.sounds['ringtone-synthetic'] = this.createRingtoneSound(audioContext);\n      // Son de connexion (accord agréable)\n      this.sounds['call-connected-synthetic'] = this.createConnectedSound(audioContext);\n      // Son de fin d'appel (ton descendant)\n      this.sounds['call-end-synthetic'] = this.createEndSound(audioContext);\n      // Son de notification (bip agréable)\n      this.sounds['notification-synthetic'] = this.createNotificationSound(audioContext);\n      console.log('🔊 [CallService] Synthetic sounds created as fallback');\n    } catch (error) {\n      console.warn('⚠️ [CallService] Could not create synthetic sounds:', error);\n    }\n  }\n  /**\n   * Crée une sonnerie agréable (mélodie)\n   */\n  createRingtoneSound(audioContext) {\n    const audio = new Audio();\n    audio.volume = 0.5;\n    let isPlaying = false;\n    let timeoutIds = [];\n    audio.playSynthetic = () => {\n      if (isPlaying) return Promise.resolve();\n      isPlaying = true;\n      const playMelody = () => {\n        if (!isPlaying) return;\n        // Mélodie inspirée de Nokia mais plus moderne : Mi-Ré-Fa#-Sol-Do#-Si-Ré-Do\n        const melody = [{\n          freq: 659.25,\n          duration: 0.125\n        }, {\n          freq: 587.33,\n          duration: 0.125\n        }, {\n          freq: 739.99,\n          duration: 0.25\n        }, {\n          freq: 783.99,\n          duration: 0.25\n        }, {\n          freq: 554.37,\n          duration: 0.125\n        }, {\n          freq: 493.88,\n          duration: 0.125\n        }, {\n          freq: 587.33,\n          duration: 0.25\n        }, {\n          freq: 523.25,\n          duration: 0.25\n        } // Do\n        ];\n\n        let currentTime = audioContext.currentTime;\n        melody.forEach((note, index) => {\n          if (!isPlaying) return;\n          const oscillator = audioContext.createOscillator();\n          const gainNode = audioContext.createGain();\n          oscillator.connect(gainNode);\n          gainNode.connect(audioContext.destination);\n          oscillator.frequency.value = note.freq;\n          oscillator.type = 'square'; // Son plus moderne\n          // Enveloppe ADSR pour un son plus naturel\n          gainNode.gain.setValueAtTime(0, currentTime);\n          gainNode.gain.linearRampToValueAtTime(0.3, currentTime + 0.02);\n          gainNode.gain.linearRampToValueAtTime(0.2, currentTime + note.duration * 0.7);\n          gainNode.gain.exponentialRampToValueAtTime(0.01, currentTime + note.duration);\n          oscillator.start(currentTime);\n          oscillator.stop(currentTime + note.duration);\n          currentTime += note.duration + 0.05; // Petite pause entre les notes\n        });\n        // Répéter la mélodie après une pause\n        const timeoutId = setTimeout(() => {\n          if (isPlaying) {\n            playMelody();\n          }\n        }, (currentTime - audioContext.currentTime + 0.8) * 1000);\n        timeoutIds.push(timeoutId);\n      };\n      playMelody();\n      return Promise.resolve();\n    };\n    audio.stopSynthetic = () => {\n      isPlaying = false;\n      timeoutIds.forEach(id => clearTimeout(id));\n      timeoutIds = [];\n    };\n    return audio;\n  }\n  /**\n   * Crée un son de connexion agréable (mélodie ascendante)\n   */\n  createConnectedSound(audioContext) {\n    const audio = new Audio();\n    audio.volume = 0.5;\n    audio.playSynthetic = () => {\n      // Mélodie ascendante positive : Do-Mi-Sol-Do (octave supérieure)\n      const melody = [{\n        freq: 523.25,\n        duration: 0.15\n      }, {\n        freq: 659.25,\n        duration: 0.15\n      }, {\n        freq: 783.99,\n        duration: 0.15\n      }, {\n        freq: 1046.5,\n        duration: 0.4\n      } // Do (octave supérieure)\n      ];\n\n      let currentTime = audioContext.currentTime;\n      melody.forEach((note, index) => {\n        const oscillator = audioContext.createOscillator();\n        const gainNode = audioContext.createGain();\n        oscillator.connect(gainNode);\n        gainNode.connect(audioContext.destination);\n        oscillator.frequency.value = note.freq;\n        oscillator.type = 'triangle'; // Son plus doux\n        // Enveloppe pour un son naturel\n        gainNode.gain.setValueAtTime(0, currentTime);\n        gainNode.gain.linearRampToValueAtTime(0.25, currentTime + 0.02);\n        gainNode.gain.linearRampToValueAtTime(0.15, currentTime + note.duration * 0.8);\n        gainNode.gain.exponentialRampToValueAtTime(0.01, currentTime + note.duration);\n        oscillator.start(currentTime);\n        oscillator.stop(currentTime + note.duration);\n        currentTime += note.duration * 0.8; // Chevauchement léger des notes\n      });\n\n      return Promise.resolve();\n    };\n    return audio;\n  }\n  /**\n   * Crée un son de fin d'appel (mélodie descendante douce)\n   */\n  createEndSound(audioContext) {\n    const audio = new Audio();\n    audio.volume = 0.4;\n    audio.playSynthetic = () => {\n      // Mélodie descendante douce : Sol-Mi-Do-Sol(grave)\n      const melody = [{\n        freq: 783.99,\n        duration: 0.2\n      }, {\n        freq: 659.25,\n        duration: 0.2\n      }, {\n        freq: 523.25,\n        duration: 0.2\n      }, {\n        freq: 392.0,\n        duration: 0.4\n      } // Sol (octave inférieure)\n      ];\n\n      let currentTime = audioContext.currentTime;\n      melody.forEach((note, index) => {\n        const oscillator = audioContext.createOscillator();\n        const gainNode = audioContext.createGain();\n        oscillator.connect(gainNode);\n        gainNode.connect(audioContext.destination);\n        oscillator.frequency.value = note.freq;\n        oscillator.type = 'sine'; // Son doux pour la fin\n        // Enveloppe douce pour un son apaisant\n        gainNode.gain.setValueAtTime(0, currentTime);\n        gainNode.gain.linearRampToValueAtTime(0.2, currentTime + 0.05);\n        gainNode.gain.linearRampToValueAtTime(0.1, currentTime + note.duration * 0.7);\n        gainNode.gain.exponentialRampToValueAtTime(0.01, currentTime + note.duration);\n        oscillator.start(currentTime);\n        oscillator.stop(currentTime + note.duration);\n        currentTime += note.duration * 0.9; // Léger chevauchement\n      });\n\n      return Promise.resolve();\n    };\n    return audio;\n  }\n  /**\n   * Crée un son de notification agréable (double bip)\n   */\n  createNotificationSound(audioContext) {\n    const audio = new Audio();\n    audio.volume = 0.6;\n    audio.playSynthetic = () => {\n      // Double bip agréable : Do-Sol\n      const notes = [{\n        freq: 523.25,\n        duration: 0.15,\n        delay: 0\n      }, {\n        freq: 783.99,\n        duration: 0.25,\n        delay: 0.2\n      } // Sol\n      ];\n\n      notes.forEach(note => {\n        setTimeout(() => {\n          const oscillator = audioContext.createOscillator();\n          const gainNode = audioContext.createGain();\n          oscillator.connect(gainNode);\n          gainNode.connect(audioContext.destination);\n          oscillator.frequency.value = note.freq;\n          oscillator.type = 'triangle'; // Son doux et agréable\n          const startTime = audioContext.currentTime;\n          // Enveloppe pour un son naturel\n          gainNode.gain.setValueAtTime(0, startTime);\n          gainNode.gain.linearRampToValueAtTime(0.4, startTime + 0.02);\n          gainNode.gain.linearRampToValueAtTime(0.2, startTime + note.duration * 0.7);\n          gainNode.gain.exponentialRampToValueAtTime(0.01, startTime + note.duration);\n          oscillator.start(startTime);\n          oscillator.stop(startTime + note.duration);\n        }, note.delay * 1000);\n      });\n      return Promise.resolve();\n    };\n    return audio;\n  }\n  /**\n   * Charge un fichier audio\n   */\n  loadSound(name, path) {\n    try {\n      const audio = new Audio();\n      // Configuration de l'audio\n      audio.preload = 'auto';\n      audio.volume = 0.7;\n      // Gérer les événements de chargement\n      audio.addEventListener('canplaythrough', () => {\n        console.log(`✅ [CallService] Sound ${name} loaded successfully from ${path}`);\n      });\n      audio.addEventListener('error', e => {\n        console.error(`❌ [CallService] Error loading sound ${name} from ${path}:`, e);\n        console.log(`🔄 [CallService] Trying to load ${name} with different approach...`);\n        // Essayer de charger avec un chemin relatif\n        const altPath = path.startsWith('/') ? path.substring(1) : path;\n        if (altPath !== path) {\n          setTimeout(() => {\n            audio.src = altPath;\n            audio.load();\n          }, 100);\n        }\n      });\n      audio.addEventListener('ended', () => {\n        this.isPlaying[name] = false;\n        console.log(`🔇 [CallService] Sound ${name} ended`);\n      });\n      // Définir la source et charger\n      audio.src = path;\n      audio.load();\n      this.sounds[name] = audio;\n      this.isPlaying[name] = false;\n      console.log(`🔊 [CallService] Loading sound ${name} from ${path}`);\n    } catch (error) {\n      console.error(`❌ [CallService] Error creating audio element for ${name}:`, error);\n    }\n  }\n  /**\n   * Joue un son\n   */\n  play(name, loop = false) {\n    if (this.muted) {\n      console.log(`🔇 [CallService] Sound ${name} muted`);\n      return;\n    }\n    try {\n      // Pour les sons d'appel, utiliser directement les versions synthétiques\n      let sound;\n      if (name === 'ringtone' || name === 'call-connected' || name === 'call-end') {\n        const syntheticName = `${name}-synthetic`;\n        sound = this.sounds[syntheticName];\n        if (sound) {\n          console.log(`🎵 [CallService] Using beautiful synthetic melody for ${name}`);\n        }\n      } else {\n        // Pour les autres sons (comme notification), essayer d'abord le fichier\n        sound = this.sounds[name];\n        if (!sound || sound.error) {\n          const syntheticName = `${name}-synthetic`;\n          sound = this.sounds[syntheticName];\n          if (sound) {\n            console.log(`🔊 [CallService] Using synthetic sound for ${name}`);\n          }\n        }\n      }\n      if (!sound) {\n        console.warn(`🔊 [CallService] Sound ${name} not found (neither original nor synthetic)`);\n        // Créer un bip simple comme dernier recours\n        this.playSimpleBeep(name === 'ringtone' ? 800 : name === 'call-connected' ? 1000 : 400);\n        return;\n      }\n      sound.loop = loop;\n      sound.volume = 0.7;\n      if (!this.isPlaying[name]) {\n        console.log(`🔊 [CallService] Playing sound: ${name} (loop: ${loop})`);\n        // Vérifier si c'est un son synthétique\n        if (sound.playSynthetic) {\n          sound.playSynthetic().then(() => {\n            console.log(`✅ [CallService] Synthetic sound ${name} started successfully`);\n            this.isPlaying[name] = true;\n            // Pour la sonnerie synthétique, elle gère sa propre boucle\n            if (name === 'ringtone' && !loop) {\n              // Si ce n'est pas en boucle, arrêter après un certain temps\n              setTimeout(() => {\n                this.isPlaying[name] = false;\n              }, 3000);\n            } else if (name !== 'ringtone') {\n              // Pour les autres sons, arrêter après leur durée\n              setTimeout(() => {\n                this.isPlaying[name] = false;\n              }, name === 'call-connected' ? 1200 : 1000);\n            }\n          }).catch(error => {\n            console.error(`❌ [CallService] Error playing synthetic sound ${name}:`, error);\n          });\n        } else {\n          // Son normal\n          sound.currentTime = 0;\n          sound.play().then(() => {\n            console.log(`✅ [CallService] Sound ${name} started successfully`);\n            this.isPlaying[name] = true;\n          }).catch(error => {\n            console.error(`❌ [CallService] Error playing sound ${name}:`, error);\n            // Essayer le son synthétique en cas d'échec\n            const syntheticName = `${name}-synthetic`;\n            const syntheticSound = this.sounds[syntheticName];\n            if (syntheticSound && syntheticSound.playSynthetic) {\n              console.log(`🔄 [CallService] Falling back to synthetic sound for ${name}`);\n              this.play(name, loop);\n            } else {\n              // Dernier recours : bip simple\n              this.playSimpleBeep(name === 'ringtone' ? 800 : name === 'call-connected' ? 1000 : 400);\n            }\n          });\n        }\n      } else {\n        console.log(`🔊 [CallService] Sound ${name} already playing`);\n      }\n    } catch (error) {\n      console.error(`❌ [CallService] Error in play method for ${name}:`, error);\n      // Dernier recours\n      this.playSimpleBeep(name === 'ringtone' ? 800 : name === 'call-connected' ? 1000 : 400);\n    }\n  }\n  /**\n   * Joue un bip simple comme dernier recours\n   */\n  playSimpleBeep(frequency) {\n    try {\n      const audioContext = new (window.AudioContext || window.webkitAudioContext)();\n      const oscillator = audioContext.createOscillator();\n      const gainNode = audioContext.createGain();\n      oscillator.connect(gainNode);\n      gainNode.connect(audioContext.destination);\n      oscillator.frequency.value = frequency;\n      oscillator.type = 'sine';\n      gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);\n      gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);\n      oscillator.start(audioContext.currentTime);\n      oscillator.stop(audioContext.currentTime + 0.3);\n      console.log(`🔊 [CallService] Playing simple beep at ${frequency}Hz`);\n    } catch (error) {\n      console.error('❌ [CallService] Could not play simple beep:', error);\n    }\n  }\n  /**\n   * Arrête un son\n   */\n  stop(name) {\n    try {\n      let sound = this.sounds[name];\n      // Essayer aussi la version synthétique\n      if (!sound) {\n        sound = this.sounds[`${name}-synthetic`];\n      }\n      if (!sound) {\n        console.warn(`🔊 [CallService] Sound ${name} not found for stopping`);\n        return;\n      }\n      if (this.isPlaying[name]) {\n        console.log(`🔇 [CallService] Stopping sound: ${name}`);\n        // Arrêter le son synthétique si c'est le cas\n        if (sound.stopSynthetic) {\n          sound.stopSynthetic();\n        } else {\n          sound.pause();\n          sound.currentTime = 0;\n        }\n        this.isPlaying[name] = false;\n      } else {\n        console.log(`🔇 [CallService] Sound ${name} was not playing`);\n      }\n    } catch (error) {\n      console.error(`❌ [CallService] Error stopping sound ${name}:`, error);\n    }\n  }\n  /**\n   * S'abonne aux appels entrants\n   */\n  subscribeToIncomingCalls() {\n    // console.log('🔔 [CallService] Setting up incoming call subscription...');\n    try {\n      this.apollo.subscribe({\n        query: INCOMING_CALL_SUBSCRIPTION,\n        errorPolicy: 'all' // Continuer même en cas d'erreur\n      }).subscribe({\n        next: ({\n          data,\n          errors\n        }) => {\n          if (errors) {\n            console.warn('⚠️ [CallService] GraphQL errors in subscription:', errors);\n          }\n          if (data?.incomingCall) {\n            // console.log('📞 [CallService] Incoming call received:', data.incomingCall.id);\n            this.handleIncomingCall(data.incomingCall);\n          }\n        },\n        error: error => {\n          console.error('❌ [CallService] Error in incoming call subscription:', error);\n          // Réessayer UNE SEULE FOIS après 10 secondes\n          setTimeout(() => {\n            this.subscribeToIncomingCalls();\n          }, 10000);\n        },\n        complete: () => {\n          // console.log('🔚 [CallService] Incoming call subscription completed');\n          // Réessayer si la subscription se ferme de manière inattendue\n          setTimeout(() => {\n            this.subscribeToIncomingCalls();\n          }, 5000);\n        }\n      });\n    } catch (error) {\n      console.error('❌ [CallService] Failed to create subscription:', error);\n      // Réessayer après 10 secondes\n      setTimeout(() => {\n        this.subscribeToIncomingCalls();\n      }, 10000);\n    }\n  }\n  /**\n   * Méthode publique pour forcer la réinitialisation de la subscription\n   */\n  reinitializeSubscription() {\n    // console.log('🔄 [CallService] Manually reinitializing subscription...');\n    this.subscribeToIncomingCalls();\n    this.subscribeToCallStatusChanges();\n    this.subscribeToCallSignals();\n  }\n  /**\n   * S'abonne aux signaux WebRTC (offres, réponses, candidats ICE)\n   */\n  subscribeToCallSignals() {\n    console.log('📡 [CallService] Setting up call signal subscription...');\n    try {\n      this.apollo.subscribe({\n        query: CALL_SIGNAL_SUBSCRIPTION,\n        variables: {\n          callId: this.currentCallId || null\n        },\n        errorPolicy: 'all'\n      }).subscribe({\n        next: ({\n          data,\n          errors\n        }) => {\n          if (errors) {\n            console.warn('⚠️ [CallService] Call signal subscription errors:', errors);\n          }\n          if (data?.callSignal) {\n            console.log('📡 [CallService] Received call signal:', data.callSignal);\n            this.handleCallSignal(data.callSignal);\n          }\n        },\n        error: error => {\n          console.error('❌ [CallService] Error in call signal subscription:', error);\n          // Réessayer après 5 secondes\n          setTimeout(() => {\n            this.subscribeToCallSignals();\n          }, 5000);\n        },\n        complete: () => {\n          console.log('🔚 [CallService] Call signal subscription completed');\n          // Réessayer si la subscription se ferme\n          setTimeout(() => {\n            this.subscribeToCallSignals();\n          }, 2000);\n        }\n      });\n    } catch (error) {\n      console.error('❌ [CallService] Failed to create call signal subscription:', error);\n      setTimeout(() => {\n        this.subscribeToCallSignals();\n      }, 3000);\n    }\n  }\n  /**\n   * Gère les signaux WebRTC reçus\n   */\n  handleCallSignal(signal) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      console.log('🔄 [CallService] Handling call signal:', signal.type);\n      try {\n        switch (signal.type) {\n          case 'offer':\n            yield _this.handleRemoteOffer(signal);\n            break;\n          case 'answer':\n            yield _this.handleRemoteAnswer(signal);\n            break;\n          case 'ice-candidate':\n            yield _this.handleRemoteIceCandidate(signal);\n            break;\n          default:\n            console.warn('⚠️ [CallService] Unknown signal type:', signal.type);\n        }\n      } catch (error) {\n        console.error('❌ [CallService] Error handling call signal:', error);\n      }\n    })();\n  }\n  /**\n   * Gère une offre WebRTC distante\n   */\n  handleRemoteOffer(signal) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      console.log('📥 [CallService] Handling remote offer');\n      if (!_this2.peerConnection) {\n        console.log('🔧 [CallService] Initializing WebRTC for remote offer');\n        _this2.initializeWebRTC();\n      }\n      const offer = JSON.parse(signal.data);\n      yield _this2.peerConnection.setRemoteDescription(offer);\n      console.log('✅ [CallService] Remote offer set successfully');\n    })();\n  }\n  /**\n   * Gère une réponse WebRTC distante\n   */\n  handleRemoteAnswer(signal) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      console.log('📥 [CallService] Handling remote answer');\n      if (!_this3.peerConnection) {\n        console.error('❌ [CallService] No peer connection for remote answer');\n        return;\n      }\n      const answer = JSON.parse(signal.data);\n      yield _this3.peerConnection.setRemoteDescription(answer);\n      console.log('✅ [CallService] Remote answer set successfully');\n    })();\n  }\n  /**\n   * Gère un candidat ICE distant\n   */\n  handleRemoteIceCandidate(signal) {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      console.log('🧊 [CallService] Handling remote ICE candidate');\n      if (!_this4.peerConnection) {\n        console.error('❌ [CallService] No peer connection for ICE candidate');\n        return;\n      }\n      const candidate = JSON.parse(signal.data);\n      yield _this4.peerConnection.addIceCandidate(candidate);\n      console.log('✅ [CallService] Remote ICE candidate added successfully');\n    })();\n  }\n  /**\n   * Envoie un signal WebRTC via GraphQL\n   */\n  sendSignal(type, data) {\n    if (!this.currentCallId) {\n      console.warn('⚠️ [CallService] No current call ID for sending signal');\n      return;\n    }\n    console.log(`📤 [CallService] Sending signal: ${type}`);\n    // TODO: Implémenter l'envoi via mutation GraphQL\n    // Pour l'instant, simulons l'échange direct\n    this.simulateSignalExchange(type, data);\n  }\n  /**\n   * Simule l'échange de signaux WebRTC (temporaire)\n   */\n  simulateSignalExchange(type, data) {\n    console.log(`🔄 [CallService] Simulating signal exchange: ${type}`);\n    // Simuler la réception du signal par l'autre peer après un délai\n    setTimeout(() => {\n      const signal = {\n        callId: this.currentCallId,\n        senderId: 'other-user',\n        type: type,\n        data: data\n      };\n      console.log('📡 [CallService] Simulated received signal:', signal);\n      this.handleCallSignal(signal);\n    }, 100);\n  }\n  /**\n   * Méthode de test pour vérifier les sons\n   */\n  testSounds() {\n    console.log('🧪 [CallService] Testing sounds...');\n    // Test de la sonnerie\n    console.log('🔊 Testing ringtone...');\n    this.play('ringtone', true);\n    setTimeout(() => {\n      this.stop('ringtone');\n      console.log('🔊 Testing call-connected...');\n      this.play('call-connected');\n    }, 3000);\n    setTimeout(() => {\n      console.log('🔊 Testing call-end...');\n      this.play('call-end');\n    }, 5000);\n  }\n  /**\n   * Test spécifique pour la sonnerie\n   */\n  testRingtone() {\n    console.log('🧪 [CallService] Testing ringtone specifically...');\n    console.log('🔊 [CallService] Available sounds:', Object.keys(this.sounds));\n    // Vérifier si le son est chargé\n    const ringtone = this.sounds['ringtone'];\n    const ringtoneSynthetic = this.sounds['ringtone-synthetic'];\n    if (ringtone) {\n      console.log('✅ [CallService] Ringtone MP3 found:', {\n        src: ringtone.src,\n        readyState: ringtone.readyState,\n        error: ringtone.error,\n        duration: ringtone.duration\n      });\n    } else {\n      console.log('❌ [CallService] Ringtone MP3 not found');\n    }\n    if (ringtoneSynthetic) {\n      console.log('✅ [CallService] Ringtone synthetic found');\n    }\n    // Jouer la sonnerie (elle va automatiquement utiliser le fallback si nécessaire)\n    console.log('🎵 [CallService] Playing beautiful ringtone melody...');\n    this.play('ringtone', true);\n    // Arrêter après 8 secondes pour entendre plusieurs cycles de la mélodie\n    setTimeout(() => {\n      this.stop('ringtone');\n      console.log('🔇 [CallService] Ringtone test stopped');\n    }, 8000);\n  }\n  /**\n   * Test tous les nouveaux sons améliorés\n   */\n  testBeautifulSounds() {\n    console.log('🧪 [CallService] Testing all beautiful sounds...');\n    // Test de la sonnerie (mélodie)\n    // console.log('🎵 Testing beautiful ringtone melody...');\n    this.play('ringtone', true);\n    setTimeout(() => {\n      this.stop('ringtone');\n      // console.log('🎵 Testing beautiful connection sound...');\n      this.play('call-connected');\n    }, 4000);\n    setTimeout(() => {\n      // console.log('🎵 Testing beautiful end sound...');\n      this.play('call-end');\n    }, 6000);\n    setTimeout(() => {\n      // console.log('🎵 Testing beautiful notification sound...');\n      this.play('notification');\n    }, 8000);\n    setTimeout(() => {\n      console.log('🎵 All sound tests completed!');\n    }, 10000);\n  }\n  /**\n   * Joue le son de notification (méthode publique)\n   */\n  playNotification() {\n    console.log('🔔 [CallService] Playing notification sound...');\n    this.play('notification');\n  }\n  /**\n   * S'abonne aux changements de statut d'appel\n   */\n  subscribeToCallStatusChanges() {\n    console.log('📞 [CallService] Setting up call status change subscription...');\n    try {\n      this.apollo.subscribe({\n        query: CALL_STATUS_CHANGED_SUBSCRIPTION,\n        errorPolicy: 'all'\n      }).subscribe({\n        next: ({\n          data,\n          errors\n        }) => {\n          if (errors) {\n            console.warn('⚠️ [CallService] GraphQL errors in call status subscription:', errors);\n          }\n          if (data?.callStatusChanged) {\n            console.log('📞 [CallService] Call status changed:', data.callStatusChanged);\n            this.handleCallStatusChange(data.callStatusChanged);\n          }\n        },\n        error: error => {\n          console.error('❌ [CallService] Error in call status subscription:', error);\n          // Réessayer après 5 secondes\n          setTimeout(() => {\n            this.subscribeToCallStatusChanges();\n          }, 5000);\n        },\n        complete: () => {\n          console.log('🔚 [CallService] Call status subscription completed');\n          // Réessayer si la subscription se ferme\n          setTimeout(() => {\n            this.subscribeToCallStatusChanges();\n          }, 2000);\n        }\n      });\n    } catch (error) {\n      console.error('❌ [CallService] Failed to create call status subscription:', error);\n      setTimeout(() => {\n        this.subscribeToCallStatusChanges();\n      }, 3000);\n    }\n  }\n  /**\n   * Gère un appel entrant\n   */\n  handleIncomingCall(call) {\n    console.log('🔔 [CallService] Handling incoming call:', {\n      callId: call.id,\n      callType: call.type,\n      caller: call.caller?.username,\n      conversationId: call.conversationId\n    });\n    this.incomingCall.next(call);\n    this.play('ringtone', true);\n    console.log('🔊 [CallService] Ringtone started, call notification sent to UI');\n  }\n  /**\n   * Gère les changements de statut d'appel\n   */\n  handleCallStatusChange(call) {\n    console.log('📞 [CallService] Call status changed:', call.status);\n    switch (call.status) {\n      case CallStatus.REJECTED:\n        console.log('❌ [CallService] Call was rejected');\n        this.stop('ringtone');\n        this.play('call-end');\n        this.activeCall.next(null);\n        this.incomingCall.next(null);\n        break;\n      case CallStatus.ENDED:\n        console.log('📞 [CallService] Call ended');\n        this.stopAllSounds();\n        this.play('call-end');\n        this.currentCallId = null; // Réinitialiser l'ID\n        this.activeCall.next(null);\n        this.incomingCall.next(null);\n        break;\n      case CallStatus.CONNECTED:\n        console.log('✅ [CallService] Call connected');\n        this.stop('ringtone');\n        this.play('call-connected');\n        this.currentCallId = call.id; // Stocker l'ID pour la subscription\n        this.activeCall.next(call);\n        this.incomingCall.next(null);\n        break;\n      case CallStatus.RINGING:\n        console.log('📞 [CallService] Call is ringing');\n        this.play('ringtone', true);\n        break;\n      default:\n        console.log('📞 [CallService] Unknown call status:', call.status);\n        break;\n    }\n  }\n  /**\n   * Obtient les médias utilisateur (caméra/micro)\n   */\n  getUserMedia(callType) {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      console.log('🎥 [CallService] Getting user media for:', callType);\n      const constraints = {\n        audio: true,\n        video: callType === CallType.VIDEO\n      };\n      try {\n        const stream = yield navigator.mediaDevices.getUserMedia(constraints);\n        console.log('✅ [CallService] User media obtained');\n        console.log('🔍 [CallService] Stream tracks:', stream.getTracks().map(t => ({\n          kind: t.kind,\n          enabled: t.enabled,\n          readyState: t.readyState\n        })));\n        _this5.localStream = stream;\n        // Afficher le stream local si on a un élément vidéo\n        if (_this5.localVideoElement && callType === CallType.VIDEO) {\n          _this5.localVideoElement.srcObject = stream;\n        }\n        return stream;\n      } catch (error) {\n        console.error('❌ [CallService] Failed to get user media:', error);\n        throw new Error(\"Impossible d'accéder à la caméra/microphone\");\n      }\n    })();\n  }\n  /**\n   * Ajoute le stream local à la PeerConnection\n   */\n  addLocalStreamToPeerConnection(stream) {\n    if (!this.peerConnection) {\n      console.error('❌ [CallService] No peer connection available');\n      return;\n    }\n    console.log('📤 [CallService] Adding local stream to peer connection');\n    stream.getTracks().forEach(track => {\n      this.peerConnection.addTrack(track, stream);\n    });\n  }\n  /**\n   * Crée une offre WebRTC\n   */\n  createOffer() {\n    var _this6 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this6.peerConnection) {\n        throw new Error('No peer connection available');\n      }\n      console.log('📝 [CallService] Creating WebRTC offer');\n      const offer = yield _this6.peerConnection.createOffer();\n      yield _this6.peerConnection.setLocalDescription(offer);\n      return offer;\n    })();\n  }\n  /**\n   * Crée une réponse WebRTC\n   */\n  createAnswer(offer) {\n    var _this7 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this7.peerConnection) {\n        throw new Error('No peer connection available');\n      }\n      console.log('📝 [CallService] Creating WebRTC answer');\n      yield _this7.peerConnection.setRemoteDescription(offer);\n      const answer = yield _this7.peerConnection.createAnswer();\n      yield _this7.peerConnection.setLocalDescription(answer);\n      return answer;\n    })();\n  }\n  /**\n   * Configure les éléments vidéo\n   */\n  setVideoElements(localVideo, remoteVideo) {\n    console.log('📺 [CallService] Setting video elements');\n    this.localVideoElement = localVideo;\n    this.remoteVideoElement = remoteVideo;\n    // Configurer les éléments pour la lecture audio\n    try {\n      this.setupAudioPlayback(localVideo, remoteVideo);\n    } catch (error) {\n      console.warn('⚠️ [CallService] setupAudioPlayback error:', error);\n    }\n    // Si on a déjà des streams, les connecter IMMÉDIATEMENT\n    if (this.localStream && localVideo) {\n      console.log('🔗 [CallService] Attaching local stream to video element');\n      localVideo.srcObject = this.localStream;\n      localVideo.muted = true; // Local toujours muet pour éviter l'écho\n      localVideo.volume = 0;\n      localVideo.autoplay = true;\n      try {\n        localVideo.play();\n        console.log('✅ [CallService] Local video playing');\n      } catch (error) {\n        console.warn('⚠️ [CallService] Local video play error:', error);\n      }\n    } else {\n      console.warn('⚠️ [CallService] No local stream or video element available');\n    }\n    if (this.remoteStream && remoteVideo) {\n      console.log('🔗 [CallService] Attaching remote stream to video element');\n      remoteVideo.srcObject = this.remoteStream;\n      remoteVideo.muted = false; // Remote avec audio\n      remoteVideo.volume = 1;\n      remoteVideo.autoplay = true;\n      try {\n        remoteVideo.play();\n        console.log('✅ [CallService] Remote video playing');\n      } catch (error) {\n        console.warn('⚠️ [CallService] Remote video play error:', error);\n      }\n    } else {\n      console.warn('⚠️ [CallService] No remote stream or video element available');\n    }\n    // Forcer l'attachement après un délai pour s'assurer que les streams sont prêts\n    setTimeout(() => {\n      this.forceStreamAttachment();\n    }, 1000);\n  }\n  /**\n   * Force l'attachement des streams aux éléments vidéo\n   */\n  forceStreamAttachment() {\n    console.log('🔄 [CallService] Forcing stream attachment...');\n    // Forcer l'attachement du stream local\n    if (this.localStream && this.localVideoElement) {\n      if (!this.localVideoElement.srcObject) {\n        console.log('🔗 [CallService] Force attaching local stream');\n        this.localVideoElement.srcObject = this.localStream;\n        this.localVideoElement.muted = true;\n        this.localVideoElement.autoplay = true;\n        this.localVideoElement.play().catch(e => console.warn('⚠️ [CallService] Force local play failed:', e));\n      }\n    }\n    // Forcer l'attachement du stream distant\n    if (this.remoteStream && this.remoteVideoElement) {\n      if (!this.remoteVideoElement.srcObject) {\n        console.log('🔗 [CallService] Force attaching remote stream');\n        this.remoteVideoElement.srcObject = this.remoteStream;\n        this.remoteVideoElement.muted = false;\n        this.remoteVideoElement.volume = 1;\n        this.remoteVideoElement.autoplay = true;\n        this.remoteVideoElement.play().catch(e => console.warn('⚠️ [CallService] Force remote play failed:', e));\n      }\n    }\n    // Vérifier l'état après forçage\n    console.log('📊 [CallService] Stream attachment status:', {\n      localAttached: !!this.localVideoElement?.srcObject,\n      remoteAttached: !!this.remoteVideoElement?.srcObject,\n      localStreamExists: !!this.localStream,\n      remoteStreamExists: !!this.remoteStream\n    });\n  }\n  /**\n   * Configure la lecture audio pour les éléments vidéo\n   */\n  setupAudioPlayback(localVideo, remoteVideo) {\n    console.log('🔊 [CallService] Setting up audio playback');\n    // Configurer les propriétés audio\n    localVideo.volume = 0; // Local toujours muet pour éviter l'écho\n    remoteVideo.volume = 1; // Remote avec volume maximum\n    // Forcer l'autoplay\n    localVideo.autoplay = true;\n    remoteVideo.autoplay = true;\n    // Événements pour débugger\n    remoteVideo.addEventListener('loadedmetadata', () => {\n      console.log('🎵 [CallService] Remote video metadata loaded');\n    });\n    remoteVideo.addEventListener('canplay', () => {\n      console.log('🎵 [CallService] Remote video can play');\n      this.ensureAudioPlayback(remoteVideo);\n    });\n    remoteVideo.addEventListener('play', () => {\n      console.log('🎵 [CallService] Remote video started playing');\n    });\n    remoteVideo.addEventListener('pause', () => {\n      console.log('⏸️ [CallService] Remote video paused');\n    });\n  }\n  /**\n   * Force la lecture audio\n   */\n  ensureAudioPlayback(videoElement) {\n    console.log('🔊 [CallService] Ensuring audio playback for element:', videoElement === this.localVideoElement ? 'local' : 'remote');\n    // Forcer la lecture\n    videoElement.play().then(() => {\n      console.log('✅ [CallService] Video/audio playback started successfully');\n    }).catch(error => {\n      console.warn('⚠️ [CallService] Autoplay prevented, user interaction required:', error);\n      // Essayer de jouer après interaction utilisateur\n      const playOnInteraction = () => {\n        videoElement.play().then(() => {\n          console.log('✅ [CallService] Video/audio playback started after user interaction');\n          document.removeEventListener('click', playOnInteraction);\n          document.removeEventListener('keydown', playOnInteraction);\n        }).catch(err => {\n          console.error('❌ [CallService] Failed to play after interaction:', err);\n        });\n      };\n      document.addEventListener('click', playOnInteraction);\n      document.addEventListener('keydown', playOnInteraction);\n    });\n  }\n  /**\n   * Initie un appel WebRTC\n   */\n  initiateCall(recipientId, callType, conversationId) {\n    console.log('🔄 [CallService] Initiating call:', {\n      recipientId,\n      callType,\n      conversationId\n    });\n    if (!recipientId) {\n      const error = new Error('Recipient ID is required');\n      console.error('❌ [CallService] initiateCall error:', error);\n      return throwError(() => error);\n    }\n    // Générer un ID unique pour l'appel\n    const callId = `call_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n    // Créer une vraie offre WebRTC\n    return from(this.createWebRTCOffer(callType)).pipe(switchMap(offer => {\n      const variables = {\n        recipientId,\n        callType: callType,\n        callId,\n        offer: JSON.stringify(offer),\n        conversationId\n      };\n      console.log('📤 [CallService] Sending initiate call mutation:', variables);\n      return this.apollo.mutate({\n        mutation: INITIATE_CALL_MUTATION,\n        variables\n      }).pipe(map(result => {\n        console.log('✅ [CallService] Call initiated successfully:', result);\n        if (!result.data?.initiateCall) {\n          throw new Error('No call data received from server');\n        }\n        const call = result.data.initiateCall;\n        console.log('📞 [CallService] Call details:', {\n          id: call.id,\n          type: call.type,\n          status: call.status,\n          caller: call.caller?.username,\n          recipient: call.recipient?.username\n        });\n        // Mettre à jour l'état local\n        this.activeCall.next(call);\n        return call;\n      }), catchError(error => {\n        console.error('❌ [CallService] initiateCall error:', error);\n        this.logger.error('Error initiating call:', error);\n        let errorMessage = \"Erreur lors de l'initiation de l'appel\";\n        if (error.networkError) {\n          errorMessage = 'Erreur de connexion réseau';\n        } else if (error.graphQLErrors?.length > 0) {\n          errorMessage = error.graphQLErrors[0].message || errorMessage;\n        }\n        return throwError(() => new Error(errorMessage));\n      }));\n    }), catchError(error => {\n      console.error('❌ [CallService] WebRTC error:', error);\n      return throwError(() => new Error('Erreur WebRTC: ' + error.message));\n    }));\n  }\n  /**\n   * Crée une offre WebRTC complète avec médias\n   */\n  createWebRTCOffer(callType) {\n    var _this8 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        // Réinitialiser la PeerConnection si nécessaire\n        if (!_this8.peerConnection) {\n          _this8.initializeWebRTC();\n        }\n        // Obtenir les médias utilisateur\n        const stream = yield _this8.getUserMedia(callType);\n        // Ajouter le stream à la PeerConnection\n        _this8.addLocalStreamToPeerConnection(stream);\n        // Créer l'offre\n        const offer = yield _this8.createOffer();\n        console.log('✅ [CallService] WebRTC offer created successfully');\n        return offer;\n      } catch (error) {\n        console.error('❌ [CallService] Failed to create WebRTC offer:', error);\n        throw error;\n      }\n    })();\n  }\n  /**\n   * Accepte un appel entrant\n   */\n  acceptCall(incomingCall) {\n    console.log('🔄 [CallService] Accepting call:', incomingCall.id);\n    // Créer une vraie réponse WebRTC\n    return from(this.createWebRTCAnswer(incomingCall)).pipe(switchMap(answer => {\n      return this.apollo.mutate({\n        mutation: ACCEPT_CALL_MUTATION,\n        variables: {\n          callId: incomingCall.id,\n          answer: JSON.stringify(answer)\n        }\n      }).pipe(switchMap(result => {\n        console.log('✅ [CallService] Call accepted successfully:', result);\n        if (!result.data?.acceptCall) {\n          throw new Error('No call data received from server');\n        }\n        const call = result.data.acceptCall;\n        // Arrêter la sonnerie\n        this.stop('ringtone');\n        this.play('call-connected');\n        // Démarrer les médias pour l'appel de manière asynchrone\n        return from(this.startMediaForCall(incomingCall, call));\n      }), catchError(error => {\n        console.error('❌ [CallService] acceptCall error:', error);\n        this.logger.error('Error accepting call:', error);\n        return throwError(() => new Error(\"Erreur lors de l'acceptation de l'appel\"));\n      }));\n    }), catchError(error => {\n      console.error('❌ [CallService] WebRTC answer error:', error);\n      return throwError(() => new Error('Erreur WebRTC: ' + error.message));\n    }));\n  }\n  /**\n   * Crée une réponse WebRTC complète avec médias\n   */\n  createWebRTCAnswer(incomingCall) {\n    var _this9 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        console.log('🔄 [CallService] Creating WebRTC answer for incoming call:', {\n          callId: incomingCall.id,\n          callType: incomingCall.type,\n          hasOffer: !!incomingCall.offer,\n          offerLength: incomingCall.offer?.length || 0\n        });\n        // Réinitialiser la PeerConnection si nécessaire\n        if (!_this9.peerConnection) {\n          console.log('🔧 [CallService] Initializing new PeerConnection for answer');\n          _this9.initializeWebRTC();\n        }\n        // Obtenir les médias utilisateur\n        console.log('🎥 [CallService] Getting user media for answer...');\n        const stream = yield _this9.getUserMedia(incomingCall.type);\n        // Ajouter le stream à la PeerConnection\n        console.log('📤 [CallService] Adding local stream to PeerConnection for answer');\n        _this9.addLocalStreamToPeerConnection(stream);\n        // Récupérer l'offre depuis l'appel entrant\n        if (!incomingCall.offer) {\n          throw new Error('No offer received in incoming call');\n        }\n        const offer = JSON.parse(incomingCall.offer);\n        if (!offer || !offer.type || !offer.sdp) {\n          throw new Error('Invalid offer format received');\n        }\n        // Créer la réponse\n        const answer = yield _this9.createAnswer(offer);\n        console.log('✅ [CallService] WebRTC answer created successfully');\n        return answer;\n      } catch (error) {\n        console.error('❌ [CallService] Failed to create WebRTC answer:', error);\n        throw error;\n      }\n    })();\n  }\n  /**\n   * Rejette un appel entrant\n   */\n  rejectCall(callId, reason) {\n    console.log('🔄 [CallService] Rejecting call:', callId, reason);\n    return this.apollo.mutate({\n      mutation: REJECT_CALL_MUTATION,\n      variables: {\n        callId,\n        reason: reason || 'User rejected'\n      }\n    }).pipe(map(result => {\n      console.log('✅ [CallService] Call rejected successfully:', result);\n      if (!result.data?.rejectCall) {\n        throw new Error('No response received from server');\n      }\n      // Mettre à jour l'état local\n      this.incomingCall.next(null);\n      this.activeCall.next(null);\n      // Arrêter la sonnerie\n      this.stop('ringtone');\n      return result.data.rejectCall;\n    }), catchError(error => {\n      console.error('❌ [CallService] rejectCall error:', error);\n      this.logger.error('Error rejecting call:', error);\n      return throwError(() => new Error(\"Erreur lors du rejet de l'appel\"));\n    }));\n  }\n  /**\n   * Termine un appel en cours\n   */\n  endCall(callId) {\n    console.log('🔄 [CallService] Ending call:', callId);\n    return this.apollo.mutate({\n      mutation: END_CALL_MUTATION,\n      variables: {\n        callId,\n        feedback: null // Pas de feedback pour l'instant\n      }\n    }).pipe(map(result => {\n      console.log('✅ [CallService] Call ended successfully:', result);\n      if (!result.data?.endCall) {\n        throw new Error('No response received from server');\n      }\n      // Mettre à jour l'état local\n      this.activeCall.next(null);\n      this.incomingCall.next(null);\n      // Arrêter tous les sons\n      this.stopAllSounds();\n      this.play('call-end');\n      // Nettoyer les ressources WebRTC\n      this.cleanupWebRTC();\n      return result.data.endCall;\n    }), catchError(error => {\n      console.error('❌ [CallService] endCall error:', error);\n      this.logger.error('Error ending call:', error);\n      return throwError(() => new Error(\"Erreur lors de la fin de l'appel\"));\n    }));\n  }\n  /**\n   * Bascule l'état des médias (audio/vidéo) pendant un appel\n   */\n  toggleMedia(callId, enableVideo, enableAudio) {\n    console.log('🔄 [CallService] Toggling media:', {\n      callId,\n      enableVideo,\n      enableAudio\n    });\n    return this.apollo.mutate({\n      mutation: TOGGLE_CALL_MEDIA_MUTATION,\n      variables: {\n        callId,\n        video: enableVideo,\n        audio: enableAudio\n      }\n    }).pipe(map(result => {\n      console.log('✅ [CallService] Media toggled successfully:', result);\n      if (!result.data?.toggleCallMedia) {\n        throw new Error('No response received from server');\n      }\n      return result.data.toggleCallMedia;\n    }), catchError(error => {\n      console.error('❌ [CallService] toggleMedia error:', error);\n      this.logger.error('Error toggling media:', error);\n      return throwError(() => new Error('Erreur lors du changement des médias'));\n    }));\n  }\n  /**\n   * Démarre les médias pour un appel accepté\n   */\n  startMediaForCall(incomingCall, call) {\n    var _this0 = this;\n    return _asyncToGenerator(function* () {\n      console.log('🎥 [CallService] Call connected - playing connection sound');\n      // Jouer le son de connexion\n      _this0.play('call-connected');\n      // Mettre à jour l'état local\n      _this0.activeCall.next(call);\n      _this0.incomingCall.next(null); // Supprimer l'appel entrant\n      return call;\n    })();\n  }\n  /**\n   * Active/désactive la vidéo\n   */\n  toggleVideo() {\n    this.isVideoEnabled = !this.isVideoEnabled;\n    console.log('📹 [CallService] Video toggled:', this.isVideoEnabled);\n    return this.isVideoEnabled;\n  }\n  /**\n   * Active/désactive l'audio\n   */\n  toggleAudio() {\n    this.isAudioEnabled = !this.isAudioEnabled;\n    console.log('🎤 [CallService] Audio toggled:', this.isAudioEnabled);\n    return this.isAudioEnabled;\n  }\n  /**\n   * Obtient l'état de la vidéo\n   */\n  getVideoEnabled() {\n    return this.isVideoEnabled;\n  }\n  /**\n   * Obtient l'état de l'audio\n   */\n  getAudioEnabled() {\n    return this.isAudioEnabled;\n  }\n  /**\n   * Arrête tous les sons\n   */\n  stopAllSounds() {\n    console.log('🔇 [CallService] Stopping all sounds');\n    Object.keys(this.sounds).forEach(name => {\n      this.stop(name);\n    });\n  }\n  /**\n   * Active les sons après interaction utilisateur (pour contourner les restrictions du navigateur)\n   */\n  enableSounds() {\n    console.log('🔊 [CallService] Enabling sounds after user interaction');\n    Object.values(this.sounds).forEach(sound => {\n      if (sound) {\n        sound.muted = false;\n        sound.volume = 0.7;\n        // Jouer et arrêter immédiatement pour \"débloquer\" l'audio\n        sound.play().then(() => {\n          sound.pause();\n          sound.currentTime = 0;\n        }).catch(() => {\n          // Ignorer les erreurs ici\n        });\n      }\n    });\n  }\n  /**\n   * Nettoie les ressources WebRTC\n   */\n  cleanupWebRTC() {\n    console.log('🧹 [CallService] Cleaning up WebRTC resources');\n    // Arrêter les tracks du stream local\n    if (this.localStream) {\n      this.localStream.getTracks().forEach(track => {\n        track.stop();\n      });\n      this.localStream = null;\n    }\n    // Nettoyer les éléments vidéo\n    if (this.localVideoElement) {\n      this.localVideoElement.srcObject = null;\n    }\n    if (this.remoteVideoElement) {\n      this.remoteVideoElement.srcObject = null;\n    }\n    // Fermer la PeerConnection\n    if (this.peerConnection) {\n      this.peerConnection.close();\n      this.peerConnection = null;\n    }\n    this.remoteStream = null;\n  }\n  ngOnDestroy() {\n    this.stopAllSounds();\n    this.cleanupWebRTC();\n  }\n  static {\n    this.ɵfac = function CallService_Factory(t) {\n      return new (t || CallService)(i0.ɵɵinject(i1.Apollo), i0.ɵɵinject(i2.LoggerService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: CallService,\n      factory: CallService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["BehaviorSubject", "throwError", "from", "map", "catchError", "switchMap", "CallType", "CallStatus", "INITIATE_CALL_MUTATION", "ACCEPT_CALL_MUTATION", "REJECT_CALL_MUTATION", "END_CALL_MUTATION", "TOGGLE_CALL_MEDIA_MUTATION", "INCOMING_CALL_SUBSCRIPTION", "CALL_STATUS_CHANGED_SUBSCRIPTION", "CALL_SIGNAL_SUBSCRIPTION", "CallService", "constructor", "apollo", "logger", "activeCall", "incomingCall", "activeCall$", "asObservable", "incomingCall$", "sounds", "isPlaying", "muted", "isVideoEnabled", "isAudioEnabled", "peerConnection", "localStream", "remoteStream", "localVideoElement", "remoteVideoElement", "currentCallId", "preloadSounds", "initializeSubscriptions", "initializeWebRTC", "setTimeout", "subscribeToIncomingCalls", "subscribeToCallStatusChanges", "subscribeToCallSignals", "value", "console", "log", "configuration", "iceServers", "urls", "RTCPeerConnection", "setupPeerConnectionEvents", "error", "ontrack", "event", "streamId", "streams", "id", "tracks", "getTracks", "t", "kind", "enabled", "readyState", "srcObject", "onloadedmetadata", "oncanplay", "onplay", "ensureAudioPlayback", "warn", "play", "catch", "e", "onicecandidate", "candidate", "sendSignal", "JSON", "stringify", "onconnectionstatechange", "connectionState", "createSyntheticSounds", "loadSound", "audioContext", "window", "AudioContext", "webkitAudioContext", "createRingtoneSound", "createConnectedSound", "createEndSound", "createNotificationSound", "audio", "Audio", "volume", "timeoutIds", "playSynthetic", "Promise", "resolve", "playMelody", "melody", "freq", "duration", "currentTime", "for<PERSON>ach", "note", "index", "oscillator", "createOscillator", "gainNode", "createGain", "connect", "destination", "frequency", "type", "gain", "setValueAtTime", "linearRampToValueAtTime", "exponentialRampToValueAtTime", "start", "stop", "timeoutId", "push", "stopSynthetic", "clearTimeout", "notes", "delay", "startTime", "name", "path", "preload", "addEventListener", "altPath", "startsWith", "substring", "src", "load", "loop", "sound", "syntheticName", "playSimpleBeep", "then", "syntheticSound", "pause", "subscribe", "query", "errorPolicy", "next", "data", "errors", "handleIncomingCall", "complete", "reinitializeSubscription", "variables", "callId", "callSignal", "handleCallSignal", "signal", "_this", "_asyncToGenerator", "handleRemoteOffer", "handleRemoteAnswer", "handleRemoteIceCandidate", "_this2", "offer", "parse", "setRemoteDescription", "_this3", "answer", "_this4", "addIceCandidate", "simulateSignalExchange", "senderId", "testSounds", "testRingtone", "Object", "keys", "ringtone", "ringtoneSynthetic", "testBeautifulSounds", "playNotification", "callStatusChanged", "handleCallStatusChange", "call", "callType", "caller", "username", "conversationId", "status", "REJECTED", "ENDED", "stopAllSounds", "CONNECTED", "RINGING", "getUserMedia", "_this5", "constraints", "video", "VIDEO", "stream", "navigator", "mediaDevices", "Error", "addLocalStreamToPeerConnection", "track", "addTrack", "createOffer", "_this6", "setLocalDescription", "createAnswer", "_this7", "setVideoElements", "localVideo", "remoteVideo", "setupAudioPlayback", "autoplay", "forceStreamAttachment", "localAttached", "remoteAttached", "localStreamExists", "remoteStreamExists", "videoElement", "playOnInteraction", "document", "removeEventListener", "err", "initiateCall", "recipientId", "Date", "now", "Math", "random", "toString", "substr", "createWebRTCOffer", "pipe", "mutate", "mutation", "result", "recipient", "errorMessage", "networkError", "graphQLErrors", "length", "message", "_this8", "acceptCall", "createWebRTCAnswer", "startMediaForCall", "_this9", "<PERSON><PERSON><PERSON>", "offerLength", "sdp", "rejectCall", "reason", "endCall", "feedback", "cleanupWebRTC", "toggleMedia", "enableVideo", "enableAudio", "toggleCallMedia", "_this0", "toggleVideo", "toggleAudio", "getVideoEnabled", "getAudioEnabled", "enableSounds", "values", "close", "ngOnDestroy", "i0", "ɵɵinject", "i1", "Apollo", "i2", "LoggerService", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\services\\call.service.ts"], "sourcesContent": ["import { Injectable, <PERSON><PERSON><PERSON>roy } from '@angular/core';\nimport { Apollo } from 'apollo-angular';\nimport { BehaviorSubject, Observable, throwError, of, from } from 'rxjs';\nimport { map, catchError, switchMap } from 'rxjs/operators';\nimport {\n  Call,\n  CallType,\n  CallStatus,\n  IncomingCall,\n  CallSuccess,\n} from '../models/message.model';\nimport {\n  INITIATE_CALL_MUTATION,\n  ACCEPT_CALL_MUTATION,\n  REJECT_CALL_MUTATION,\n  END_CALL_MUTATION,\n  TOGGLE_CALL_MEDIA_MUTATION,\n  INCOMING_CALL_SUBSCRIPTION,\n  CALL_STATUS_CHANGED_SUBSCRIPTION,\n  CALL_SIGNAL_SUBSCRIPTION,\n} from '../graphql/message.graphql';\nimport { LoggerService } from './logger.service';\n\n@Injectable({\n  providedIn: 'root',\n})\nexport class CallService implements OnDestroy {\n  // État des appels\n  private activeCall = new BehaviorSubject<Call | null>(null);\n  private incomingCall = new BehaviorSubject<IncomingCall | null>(null);\n\n  // Observables publics\n  public activeCall$ = this.activeCall.asObservable();\n  public incomingCall$ = this.incomingCall.asObservable();\n\n  // Propriétés pour la gestion des sons\n  private sounds: { [key: string]: HTMLAudioElement } = {};\n  private isPlaying: { [key: string]: boolean } = {};\n  private muted = false;\n\n  // États simples pour les médias\n  private isVideoEnabled = true;\n  private isAudioEnabled = true;\n\n  // WebRTC\n  private peerConnection: RTCPeerConnection | null = null;\n  private localStream: MediaStream | null = null;\n  private remoteStream: MediaStream | null = null;\n  private localVideoElement: HTMLVideoElement | null = null;\n  private remoteVideoElement: HTMLVideoElement | null = null;\n  private currentCallId: string | null = null;\n\n  constructor(private apollo: Apollo, private logger: LoggerService) {\n    this.preloadSounds();\n    this.initializeSubscriptions();\n    this.initializeWebRTC();\n  }\n\n  /**\n   * Initialise les subscriptions avec un délai pour s'assurer que l'authentification est prête\n   */\n  private initializeSubscriptions(): void {\n    // Attendre un peu pour s'assurer que l'authentification est prête\n    setTimeout(() => {\n      this.subscribeToIncomingCalls();\n      this.subscribeToCallStatusChanges();\n      this.subscribeToCallSignals();\n    }, 1000);\n\n    // Réessayer UNE SEULE FOIS après 10 secondes si nécessaire\n    setTimeout(() => {\n      if (!this.incomingCall.value) {\n        // console.log('🔄 [CallService] Retrying subscription initialization...');\n        this.subscribeToIncomingCalls();\n        this.subscribeToCallStatusChanges();\n        this.subscribeToCallSignals();\n      }\n    }, 10000);\n  }\n\n  /**\n   * Initialise WebRTC\n   */\n  private initializeWebRTC(): void {\n    console.log('🔧 [CallService] Initializing WebRTC...');\n\n    // Configuration des serveurs STUN/TURN\n    const configuration: RTCConfiguration = {\n      iceServers: [\n        { urls: 'stun:stun.l.google.com:19302' },\n        { urls: 'stun:stun1.l.google.com:19302' },\n      ],\n    };\n\n    try {\n      this.peerConnection = new RTCPeerConnection(configuration);\n      this.setupPeerConnectionEvents();\n      console.log('✅ [CallService] WebRTC initialized successfully');\n    } catch (error) {\n      console.error('❌ [CallService] Failed to initialize WebRTC:', error);\n    }\n  }\n\n  /**\n   * Configure les événements de la PeerConnection\n   */\n  private setupPeerConnectionEvents(): void {\n    if (!this.peerConnection) return;\n\n    // Quand on reçoit un stream distant\n    this.peerConnection.ontrack = (event) => {\n      console.log('📺 [CallService] Remote stream received');\n      console.log('🔍 [CallService] Stream details:', {\n        streamId: event.streams[0]?.id,\n        tracks: event.streams[0]?.getTracks().map((t) => ({\n          kind: t.kind,\n          enabled: t.enabled,\n          readyState: t.readyState,\n        })),\n      });\n\n      this.remoteStream = event.streams[0];\n\n      if (this.remoteVideoElement) {\n        console.log('🎥 [CallService] Setting remote stream to video element');\n        this.remoteVideoElement.srcObject = this.remoteStream;\n\n        // Configurer les événements de l'élément vidéo\n        this.remoteVideoElement.onloadedmetadata = () => {\n          console.log('🎵 [CallService] Remote video metadata loaded');\n        };\n\n        this.remoteVideoElement.oncanplay = () => {\n          console.log('🎵 [CallService] Remote video can play');\n        };\n\n        this.remoteVideoElement.onplay = () => {\n          console.log('🎵 [CallService] Remote video started playing');\n        };\n\n        // Forcer la lecture audio immédiatement\n        try {\n          this.ensureAudioPlayback(this.remoteVideoElement);\n        } catch (error) {\n          console.warn(\n            '⚠️ [CallService] ensureAudioPlayback error in ontrack:',\n            error\n          );\n          // Fallback simple : forcer la lecture directement\n          console.log('🔄 [CallService] Trying direct play as fallback');\n          this.remoteVideoElement.play().catch((e) => {\n            console.warn('⚠️ [CallService] Direct play failed:', e);\n            // Dernier recours : attendre une interaction utilisateur\n            console.log(\n              '⏳ [CallService] Waiting for user interaction to enable audio'\n            );\n          });\n        }\n      } else {\n        console.warn('⚠️ [CallService] No remote video element available');\n      }\n    };\n\n    // Gestion des candidats ICE\n    this.peerConnection.onicecandidate = (event) => {\n      if (event.candidate) {\n        console.log('🧊 [CallService] ICE candidate generated');\n        this.sendSignal('ice-candidate', JSON.stringify(event.candidate));\n      }\n    };\n\n    // État de la connexion\n    this.peerConnection.onconnectionstatechange = () => {\n      console.log(\n        '🔗 [CallService] Connection state:',\n        this.peerConnection?.connectionState\n      );\n    };\n  }\n\n  /**\n   * Précharge les sons utilisés dans l'application\n   */\n  private preloadSounds(): void {\n    // Créer des sons synthétiques de haute qualité\n    this.createSyntheticSounds();\n\n    // Charger le son de notification qui existe encore\n    this.loadSound('notification', 'assets/sounds/notification.mp3');\n\n    console.log(\n      '🎵 [CallService] Beautiful synthetic melodies created for calls'\n    );\n  }\n\n  /**\n   * Crée des sons synthétiques comme fallback\n   */\n  private createSyntheticSounds(): void {\n    try {\n      // Créer un contexte audio pour les sons synthétiques\n      const audioContext = new (window.AudioContext ||\n        (window as any).webkitAudioContext)();\n\n      // Son de sonnerie (mélodie agréable)\n      this.sounds['ringtone-synthetic'] =\n        this.createRingtoneSound(audioContext);\n\n      // Son de connexion (accord agréable)\n      this.sounds['call-connected-synthetic'] =\n        this.createConnectedSound(audioContext);\n\n      // Son de fin d'appel (ton descendant)\n      this.sounds['call-end-synthetic'] = this.createEndSound(audioContext);\n\n      // Son de notification (bip agréable)\n      this.sounds['notification-synthetic'] =\n        this.createNotificationSound(audioContext);\n\n      console.log('🔊 [CallService] Synthetic sounds created as fallback');\n    } catch (error) {\n      console.warn(\n        '⚠️ [CallService] Could not create synthetic sounds:',\n        error\n      );\n    }\n  }\n\n  /**\n   * Crée une sonnerie agréable (mélodie)\n   */\n  private createRingtoneSound(audioContext: AudioContext): HTMLAudioElement {\n    const audio = new Audio();\n    audio.volume = 0.5;\n\n    let isPlaying = false;\n    let timeoutIds: any[] = [];\n\n    (audio as any).playSynthetic = () => {\n      if (isPlaying) return Promise.resolve();\n\n      isPlaying = true;\n\n      const playMelody = () => {\n        if (!isPlaying) return;\n\n        // Mélodie inspirée de Nokia mais plus moderne : Mi-Ré-Fa#-Sol-Do#-Si-Ré-Do\n        const melody = [\n          { freq: 659.25, duration: 0.125 }, // Mi\n          { freq: 587.33, duration: 0.125 }, // Ré\n          { freq: 739.99, duration: 0.25 }, // Fa#\n          { freq: 783.99, duration: 0.25 }, // Sol\n          { freq: 554.37, duration: 0.125 }, // Do#\n          { freq: 493.88, duration: 0.125 }, // Si\n          { freq: 587.33, duration: 0.25 }, // Ré\n          { freq: 523.25, duration: 0.25 }, // Do\n        ];\n\n        let currentTime = audioContext.currentTime;\n\n        melody.forEach((note, index) => {\n          if (!isPlaying) return;\n\n          const oscillator = audioContext.createOscillator();\n          const gainNode = audioContext.createGain();\n\n          oscillator.connect(gainNode);\n          gainNode.connect(audioContext.destination);\n\n          oscillator.frequency.value = note.freq;\n          oscillator.type = 'square'; // Son plus moderne\n\n          // Enveloppe ADSR pour un son plus naturel\n          gainNode.gain.setValueAtTime(0, currentTime);\n          gainNode.gain.linearRampToValueAtTime(0.3, currentTime + 0.02);\n          gainNode.gain.linearRampToValueAtTime(\n            0.2,\n            currentTime + note.duration * 0.7\n          );\n          gainNode.gain.exponentialRampToValueAtTime(\n            0.01,\n            currentTime + note.duration\n          );\n\n          oscillator.start(currentTime);\n          oscillator.stop(currentTime + note.duration);\n\n          currentTime += note.duration + 0.05; // Petite pause entre les notes\n        });\n\n        // Répéter la mélodie après une pause\n        const timeoutId = setTimeout(() => {\n          if (isPlaying) {\n            playMelody();\n          }\n        }, (currentTime - audioContext.currentTime + 0.8) * 1000);\n\n        timeoutIds.push(timeoutId);\n      };\n\n      playMelody();\n      return Promise.resolve();\n    };\n\n    (audio as any).stopSynthetic = () => {\n      isPlaying = false;\n      timeoutIds.forEach((id) => clearTimeout(id));\n      timeoutIds = [];\n    };\n\n    return audio;\n  }\n\n  /**\n   * Crée un son de connexion agréable (mélodie ascendante)\n   */\n  private createConnectedSound(audioContext: AudioContext): HTMLAudioElement {\n    const audio = new Audio();\n    audio.volume = 0.5;\n\n    (audio as any).playSynthetic = () => {\n      // Mélodie ascendante positive : Do-Mi-Sol-Do (octave supérieure)\n      const melody = [\n        { freq: 523.25, duration: 0.15 }, // Do\n        { freq: 659.25, duration: 0.15 }, // Mi\n        { freq: 783.99, duration: 0.15 }, // Sol\n        { freq: 1046.5, duration: 0.4 }, // Do (octave supérieure)\n      ];\n\n      let currentTime = audioContext.currentTime;\n\n      melody.forEach((note, index) => {\n        const oscillator = audioContext.createOscillator();\n        const gainNode = audioContext.createGain();\n\n        oscillator.connect(gainNode);\n        gainNode.connect(audioContext.destination);\n\n        oscillator.frequency.value = note.freq;\n        oscillator.type = 'triangle'; // Son plus doux\n\n        // Enveloppe pour un son naturel\n        gainNode.gain.setValueAtTime(0, currentTime);\n        gainNode.gain.linearRampToValueAtTime(0.25, currentTime + 0.02);\n        gainNode.gain.linearRampToValueAtTime(\n          0.15,\n          currentTime + note.duration * 0.8\n        );\n        gainNode.gain.exponentialRampToValueAtTime(\n          0.01,\n          currentTime + note.duration\n        );\n\n        oscillator.start(currentTime);\n        oscillator.stop(currentTime + note.duration);\n\n        currentTime += note.duration * 0.8; // Chevauchement léger des notes\n      });\n\n      return Promise.resolve();\n    };\n\n    return audio;\n  }\n\n  /**\n   * Crée un son de fin d'appel (mélodie descendante douce)\n   */\n  private createEndSound(audioContext: AudioContext): HTMLAudioElement {\n    const audio = new Audio();\n    audio.volume = 0.4;\n\n    (audio as any).playSynthetic = () => {\n      // Mélodie descendante douce : Sol-Mi-Do-Sol(grave)\n      const melody = [\n        { freq: 783.99, duration: 0.2 }, // Sol\n        { freq: 659.25, duration: 0.2 }, // Mi\n        { freq: 523.25, duration: 0.2 }, // Do\n        { freq: 392.0, duration: 0.4 }, // Sol (octave inférieure)\n      ];\n\n      let currentTime = audioContext.currentTime;\n\n      melody.forEach((note, index) => {\n        const oscillator = audioContext.createOscillator();\n        const gainNode = audioContext.createGain();\n\n        oscillator.connect(gainNode);\n        gainNode.connect(audioContext.destination);\n\n        oscillator.frequency.value = note.freq;\n        oscillator.type = 'sine'; // Son doux pour la fin\n\n        // Enveloppe douce pour un son apaisant\n        gainNode.gain.setValueAtTime(0, currentTime);\n        gainNode.gain.linearRampToValueAtTime(0.2, currentTime + 0.05);\n        gainNode.gain.linearRampToValueAtTime(\n          0.1,\n          currentTime + note.duration * 0.7\n        );\n        gainNode.gain.exponentialRampToValueAtTime(\n          0.01,\n          currentTime + note.duration\n        );\n\n        oscillator.start(currentTime);\n        oscillator.stop(currentTime + note.duration);\n\n        currentTime += note.duration * 0.9; // Léger chevauchement\n      });\n\n      return Promise.resolve();\n    };\n\n    return audio;\n  }\n\n  /**\n   * Crée un son de notification agréable (double bip)\n   */\n  private createNotificationSound(\n    audioContext: AudioContext\n  ): HTMLAudioElement {\n    const audio = new Audio();\n    audio.volume = 0.6;\n\n    (audio as any).playSynthetic = () => {\n      // Double bip agréable : Do-Sol\n      const notes = [\n        { freq: 523.25, duration: 0.15, delay: 0 }, // Do\n        { freq: 783.99, duration: 0.25, delay: 0.2 }, // Sol\n      ];\n\n      notes.forEach((note) => {\n        setTimeout(() => {\n          const oscillator = audioContext.createOscillator();\n          const gainNode = audioContext.createGain();\n\n          oscillator.connect(gainNode);\n          gainNode.connect(audioContext.destination);\n\n          oscillator.frequency.value = note.freq;\n          oscillator.type = 'triangle'; // Son doux et agréable\n\n          const startTime = audioContext.currentTime;\n\n          // Enveloppe pour un son naturel\n          gainNode.gain.setValueAtTime(0, startTime);\n          gainNode.gain.linearRampToValueAtTime(0.4, startTime + 0.02);\n          gainNode.gain.linearRampToValueAtTime(\n            0.2,\n            startTime + note.duration * 0.7\n          );\n          gainNode.gain.exponentialRampToValueAtTime(\n            0.01,\n            startTime + note.duration\n          );\n\n          oscillator.start(startTime);\n          oscillator.stop(startTime + note.duration);\n        }, note.delay * 1000);\n      });\n\n      return Promise.resolve();\n    };\n\n    return audio;\n  }\n\n  /**\n   * Charge un fichier audio\n   */\n  private loadSound(name: string, path: string): void {\n    try {\n      const audio = new Audio();\n\n      // Configuration de l'audio\n      audio.preload = 'auto';\n      audio.volume = 0.7;\n\n      // Gérer les événements de chargement\n      audio.addEventListener('canplaythrough', () => {\n        console.log(\n          `✅ [CallService] Sound ${name} loaded successfully from ${path}`\n        );\n      });\n\n      audio.addEventListener('error', (e) => {\n        console.error(\n          `❌ [CallService] Error loading sound ${name} from ${path}:`,\n          e\n        );\n        console.log(\n          `🔄 [CallService] Trying to load ${name} with different approach...`\n        );\n\n        // Essayer de charger avec un chemin relatif\n        const altPath = path.startsWith('/') ? path.substring(1) : path;\n        if (altPath !== path) {\n          setTimeout(() => {\n            audio.src = altPath;\n            audio.load();\n          }, 100);\n        }\n      });\n\n      audio.addEventListener('ended', () => {\n        this.isPlaying[name] = false;\n        console.log(`🔇 [CallService] Sound ${name} ended`);\n      });\n\n      // Définir la source et charger\n      audio.src = path;\n      audio.load();\n\n      this.sounds[name] = audio;\n      this.isPlaying[name] = false;\n\n      console.log(`🔊 [CallService] Loading sound ${name} from ${path}`);\n    } catch (error) {\n      console.error(\n        `❌ [CallService] Error creating audio element for ${name}:`,\n        error\n      );\n    }\n  }\n\n  /**\n   * Joue un son\n   */\n  private play(name: string, loop: boolean = false): void {\n    if (this.muted) {\n      console.log(`🔇 [CallService] Sound ${name} muted`);\n      return;\n    }\n\n    try {\n      // Pour les sons d'appel, utiliser directement les versions synthétiques\n      let sound;\n      if (\n        name === 'ringtone' ||\n        name === 'call-connected' ||\n        name === 'call-end'\n      ) {\n        const syntheticName = `${name}-synthetic`;\n        sound = this.sounds[syntheticName];\n        if (sound) {\n          console.log(\n            `🎵 [CallService] Using beautiful synthetic melody for ${name}`\n          );\n        }\n      } else {\n        // Pour les autres sons (comme notification), essayer d'abord le fichier\n        sound = this.sounds[name];\n        if (!sound || sound.error) {\n          const syntheticName = `${name}-synthetic`;\n          sound = this.sounds[syntheticName];\n          if (sound) {\n            console.log(`🔊 [CallService] Using synthetic sound for ${name}`);\n          }\n        }\n      }\n\n      if (!sound) {\n        console.warn(\n          `🔊 [CallService] Sound ${name} not found (neither original nor synthetic)`\n        );\n        // Créer un bip simple comme dernier recours\n        this.playSimpleBeep(\n          name === 'ringtone' ? 800 : name === 'call-connected' ? 1000 : 400\n        );\n        return;\n      }\n\n      sound.loop = loop;\n      sound.volume = 0.7;\n\n      if (!this.isPlaying[name]) {\n        console.log(`🔊 [CallService] Playing sound: ${name} (loop: ${loop})`);\n\n        // Vérifier si c'est un son synthétique\n        if ((sound as any).playSynthetic) {\n          (sound as any)\n            .playSynthetic()\n            .then(() => {\n              console.log(\n                `✅ [CallService] Synthetic sound ${name} started successfully`\n              );\n              this.isPlaying[name] = true;\n\n              // Pour la sonnerie synthétique, elle gère sa propre boucle\n              if (name === 'ringtone' && !loop) {\n                // Si ce n'est pas en boucle, arrêter après un certain temps\n                setTimeout(() => {\n                  this.isPlaying[name] = false;\n                }, 3000);\n              } else if (name !== 'ringtone') {\n                // Pour les autres sons, arrêter après leur durée\n                setTimeout(\n                  () => {\n                    this.isPlaying[name] = false;\n                  },\n                  name === 'call-connected' ? 1200 : 1000\n                );\n              }\n            })\n            .catch((error: any) => {\n              console.error(\n                `❌ [CallService] Error playing synthetic sound ${name}:`,\n                error\n              );\n            });\n        } else {\n          // Son normal\n          sound.currentTime = 0;\n          sound\n            .play()\n            .then(() => {\n              console.log(\n                `✅ [CallService] Sound ${name} started successfully`\n              );\n              this.isPlaying[name] = true;\n            })\n            .catch((error) => {\n              console.error(\n                `❌ [CallService] Error playing sound ${name}:`,\n                error\n              );\n\n              // Essayer le son synthétique en cas d'échec\n              const syntheticName = `${name}-synthetic`;\n              const syntheticSound = this.sounds[syntheticName];\n              if (syntheticSound && (syntheticSound as any).playSynthetic) {\n                console.log(\n                  `🔄 [CallService] Falling back to synthetic sound for ${name}`\n                );\n                this.play(name, loop);\n              } else {\n                // Dernier recours : bip simple\n                this.playSimpleBeep(\n                  name === 'ringtone'\n                    ? 800\n                    : name === 'call-connected'\n                    ? 1000\n                    : 400\n                );\n              }\n            });\n        }\n      } else {\n        console.log(`🔊 [CallService] Sound ${name} already playing`);\n      }\n    } catch (error) {\n      console.error(\n        `❌ [CallService] Error in play method for ${name}:`,\n        error\n      );\n      // Dernier recours\n      this.playSimpleBeep(\n        name === 'ringtone' ? 800 : name === 'call-connected' ? 1000 : 400\n      );\n    }\n  }\n\n  /**\n   * Joue un bip simple comme dernier recours\n   */\n  private playSimpleBeep(frequency: number): void {\n    try {\n      const audioContext = new (window.AudioContext ||\n        (window as any).webkitAudioContext)();\n      const oscillator = audioContext.createOscillator();\n      const gainNode = audioContext.createGain();\n\n      oscillator.connect(gainNode);\n      gainNode.connect(audioContext.destination);\n\n      oscillator.frequency.value = frequency;\n      oscillator.type = 'sine';\n\n      gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);\n      gainNode.gain.exponentialRampToValueAtTime(\n        0.01,\n        audioContext.currentTime + 0.3\n      );\n\n      oscillator.start(audioContext.currentTime);\n      oscillator.stop(audioContext.currentTime + 0.3);\n\n      console.log(`🔊 [CallService] Playing simple beep at ${frequency}Hz`);\n    } catch (error) {\n      console.error('❌ [CallService] Could not play simple beep:', error);\n    }\n  }\n\n  /**\n   * Arrête un son\n   */\n  private stop(name: string): void {\n    try {\n      let sound = this.sounds[name];\n\n      // Essayer aussi la version synthétique\n      if (!sound) {\n        sound = this.sounds[`${name}-synthetic`];\n      }\n\n      if (!sound) {\n        console.warn(`🔊 [CallService] Sound ${name} not found for stopping`);\n        return;\n      }\n\n      if (this.isPlaying[name]) {\n        console.log(`🔇 [CallService] Stopping sound: ${name}`);\n\n        // Arrêter le son synthétique si c'est le cas\n        if ((sound as any).stopSynthetic) {\n          (sound as any).stopSynthetic();\n        } else {\n          sound.pause();\n          sound.currentTime = 0;\n        }\n\n        this.isPlaying[name] = false;\n      } else {\n        console.log(`🔇 [CallService] Sound ${name} was not playing`);\n      }\n    } catch (error) {\n      console.error(`❌ [CallService] Error stopping sound ${name}:`, error);\n    }\n  }\n\n  /**\n   * S'abonne aux appels entrants\n   */\n  private subscribeToIncomingCalls(): void {\n    // console.log('🔔 [CallService] Setting up incoming call subscription...');\n\n    try {\n      this.apollo\n        .subscribe<{ incomingCall: IncomingCall }>({\n          query: INCOMING_CALL_SUBSCRIPTION,\n          errorPolicy: 'all', // Continuer même en cas d'erreur\n        })\n        .subscribe({\n          next: ({ data, errors }) => {\n            if (errors) {\n              console.warn(\n                '⚠️ [CallService] GraphQL errors in subscription:',\n                errors\n              );\n            }\n\n            if (data?.incomingCall) {\n              // console.log('📞 [CallService] Incoming call received:', data.incomingCall.id);\n              this.handleIncomingCall(data.incomingCall);\n            }\n          },\n          error: (error) => {\n            console.error(\n              '❌ [CallService] Error in incoming call subscription:',\n              error\n            );\n\n            // Réessayer UNE SEULE FOIS après 10 secondes\n            setTimeout(() => {\n              this.subscribeToIncomingCalls();\n            }, 10000);\n          },\n          complete: () => {\n            // console.log('🔚 [CallService] Incoming call subscription completed');\n            // Réessayer si la subscription se ferme de manière inattendue\n            setTimeout(() => {\n              this.subscribeToIncomingCalls();\n            }, 5000);\n          },\n        });\n    } catch (error) {\n      console.error('❌ [CallService] Failed to create subscription:', error);\n      // Réessayer après 10 secondes\n      setTimeout(() => {\n        this.subscribeToIncomingCalls();\n      }, 10000);\n    }\n  }\n\n  /**\n   * Méthode publique pour forcer la réinitialisation de la subscription\n   */\n  public reinitializeSubscription(): void {\n    // console.log('🔄 [CallService] Manually reinitializing subscription...');\n    this.subscribeToIncomingCalls();\n    this.subscribeToCallStatusChanges();\n    this.subscribeToCallSignals();\n  }\n\n  /**\n   * S'abonne aux signaux WebRTC (offres, réponses, candidats ICE)\n   */\n  private subscribeToCallSignals(): void {\n    console.log('📡 [CallService] Setting up call signal subscription...');\n\n    try {\n      this.apollo\n        .subscribe<{ callSignal: any }>({\n          query: CALL_SIGNAL_SUBSCRIPTION,\n          variables: {\n            callId: this.currentCallId || null,\n          },\n          errorPolicy: 'all',\n        })\n        .subscribe({\n          next: ({ data, errors }) => {\n            if (errors) {\n              console.warn(\n                '⚠️ [CallService] Call signal subscription errors:',\n                errors\n              );\n            }\n\n            if (data?.callSignal) {\n              console.log(\n                '📡 [CallService] Received call signal:',\n                data.callSignal\n              );\n              this.handleCallSignal(data.callSignal);\n            }\n          },\n          error: (error) => {\n            console.error(\n              '❌ [CallService] Error in call signal subscription:',\n              error\n            );\n            // Réessayer après 5 secondes\n            setTimeout(() => {\n              this.subscribeToCallSignals();\n            }, 5000);\n          },\n          complete: () => {\n            console.log('🔚 [CallService] Call signal subscription completed');\n            // Réessayer si la subscription se ferme\n            setTimeout(() => {\n              this.subscribeToCallSignals();\n            }, 2000);\n          },\n        });\n    } catch (error) {\n      console.error(\n        '❌ [CallService] Failed to create call signal subscription:',\n        error\n      );\n      setTimeout(() => {\n        this.subscribeToCallSignals();\n      }, 3000);\n    }\n  }\n\n  /**\n   * Gère les signaux WebRTC reçus\n   */\n  private async handleCallSignal(signal: any): Promise<void> {\n    console.log('🔄 [CallService] Handling call signal:', signal.type);\n\n    try {\n      switch (signal.type) {\n        case 'offer':\n          await this.handleRemoteOffer(signal);\n          break;\n        case 'answer':\n          await this.handleRemoteAnswer(signal);\n          break;\n        case 'ice-candidate':\n          await this.handleRemoteIceCandidate(signal);\n          break;\n        default:\n          console.warn('⚠️ [CallService] Unknown signal type:', signal.type);\n      }\n    } catch (error) {\n      console.error('❌ [CallService] Error handling call signal:', error);\n    }\n  }\n\n  /**\n   * Gère une offre WebRTC distante\n   */\n  private async handleRemoteOffer(signal: any): Promise<void> {\n    console.log('📥 [CallService] Handling remote offer');\n\n    if (!this.peerConnection) {\n      console.log('🔧 [CallService] Initializing WebRTC for remote offer');\n      this.initializeWebRTC();\n    }\n\n    const offer = JSON.parse(signal.data);\n    await this.peerConnection!.setRemoteDescription(offer);\n    console.log('✅ [CallService] Remote offer set successfully');\n  }\n\n  /**\n   * Gère une réponse WebRTC distante\n   */\n  private async handleRemoteAnswer(signal: any): Promise<void> {\n    console.log('📥 [CallService] Handling remote answer');\n\n    if (!this.peerConnection) {\n      console.error('❌ [CallService] No peer connection for remote answer');\n      return;\n    }\n\n    const answer = JSON.parse(signal.data);\n    await this.peerConnection.setRemoteDescription(answer);\n    console.log('✅ [CallService] Remote answer set successfully');\n  }\n\n  /**\n   * Gère un candidat ICE distant\n   */\n  private async handleRemoteIceCandidate(signal: any): Promise<void> {\n    console.log('🧊 [CallService] Handling remote ICE candidate');\n\n    if (!this.peerConnection) {\n      console.error('❌ [CallService] No peer connection for ICE candidate');\n      return;\n    }\n\n    const candidate = JSON.parse(signal.data);\n    await this.peerConnection.addIceCandidate(candidate);\n    console.log('✅ [CallService] Remote ICE candidate added successfully');\n  }\n\n  /**\n   * Envoie un signal WebRTC via GraphQL\n   */\n  private sendSignal(type: string, data: string): void {\n    if (!this.currentCallId) {\n      console.warn('⚠️ [CallService] No current call ID for sending signal');\n      return;\n    }\n\n    console.log(`📤 [CallService] Sending signal: ${type}`);\n\n    // TODO: Implémenter l'envoi via mutation GraphQL\n    // Pour l'instant, simulons l'échange direct\n    this.simulateSignalExchange(type, data);\n  }\n\n  /**\n   * Simule l'échange de signaux WebRTC (temporaire)\n   */\n  private simulateSignalExchange(type: string, data: string): void {\n    console.log(`🔄 [CallService] Simulating signal exchange: ${type}`);\n\n    // Simuler la réception du signal par l'autre peer après un délai\n    setTimeout(() => {\n      const signal = {\n        callId: this.currentCallId,\n        senderId: 'other-user',\n        type: type,\n        data: data,\n      };\n\n      console.log('📡 [CallService] Simulated received signal:', signal);\n      this.handleCallSignal(signal);\n    }, 100);\n  }\n\n  /**\n   * Méthode de test pour vérifier les sons\n   */\n  public testSounds(): void {\n    console.log('🧪 [CallService] Testing sounds...');\n\n    // Test de la sonnerie\n    console.log('🔊 Testing ringtone...');\n    this.play('ringtone', true);\n\n    setTimeout(() => {\n      this.stop('ringtone');\n      console.log('🔊 Testing call-connected...');\n      this.play('call-connected');\n    }, 3000);\n\n    setTimeout(() => {\n      console.log('🔊 Testing call-end...');\n      this.play('call-end');\n    }, 5000);\n  }\n\n  /**\n   * Test spécifique pour la sonnerie\n   */\n  public testRingtone(): void {\n    console.log('🧪 [CallService] Testing ringtone specifically...');\n    console.log('🔊 [CallService] Available sounds:', Object.keys(this.sounds));\n\n    // Vérifier si le son est chargé\n    const ringtone = this.sounds['ringtone'];\n    const ringtoneSynthetic = this.sounds['ringtone-synthetic'];\n\n    if (ringtone) {\n      console.log('✅ [CallService] Ringtone MP3 found:', {\n        src: ringtone.src,\n        readyState: ringtone.readyState,\n        error: ringtone.error,\n        duration: ringtone.duration,\n      });\n    } else {\n      console.log('❌ [CallService] Ringtone MP3 not found');\n    }\n\n    if (ringtoneSynthetic) {\n      console.log('✅ [CallService] Ringtone synthetic found');\n    }\n\n    // Jouer la sonnerie (elle va automatiquement utiliser le fallback si nécessaire)\n    console.log('🎵 [CallService] Playing beautiful ringtone melody...');\n    this.play('ringtone', true);\n\n    // Arrêter après 8 secondes pour entendre plusieurs cycles de la mélodie\n    setTimeout(() => {\n      this.stop('ringtone');\n      console.log('🔇 [CallService] Ringtone test stopped');\n    }, 8000);\n  }\n\n  /**\n   * Test tous les nouveaux sons améliorés\n   */\n  public testBeautifulSounds(): void {\n    console.log('🧪 [CallService] Testing all beautiful sounds...');\n\n    // Test de la sonnerie (mélodie)\n    // console.log('🎵 Testing beautiful ringtone melody...');\n    this.play('ringtone', true);\n\n    setTimeout(() => {\n      this.stop('ringtone');\n      // console.log('🎵 Testing beautiful connection sound...');\n      this.play('call-connected');\n    }, 4000);\n\n    setTimeout(() => {\n      // console.log('🎵 Testing beautiful end sound...');\n      this.play('call-end');\n    }, 6000);\n\n    setTimeout(() => {\n      // console.log('🎵 Testing beautiful notification sound...');\n      this.play('notification');\n    }, 8000);\n\n    setTimeout(() => {\n      console.log('🎵 All sound tests completed!');\n    }, 10000);\n  }\n\n  /**\n   * Joue le son de notification (méthode publique)\n   */\n  public playNotification(): void {\n    console.log('🔔 [CallService] Playing notification sound...');\n    this.play('notification');\n  }\n\n  /**\n   * S'abonne aux changements de statut d'appel\n   */\n  private subscribeToCallStatusChanges(): void {\n    console.log(\n      '📞 [CallService] Setting up call status change subscription...'\n    );\n\n    try {\n      this.apollo\n        .subscribe<{ callStatusChanged: Call }>({\n          query: CALL_STATUS_CHANGED_SUBSCRIPTION,\n          errorPolicy: 'all',\n        })\n        .subscribe({\n          next: ({ data, errors }) => {\n            if (errors) {\n              console.warn(\n                '⚠️ [CallService] GraphQL errors in call status subscription:',\n                errors\n              );\n            }\n\n            if (data?.callStatusChanged) {\n              console.log(\n                '📞 [CallService] Call status changed:',\n                data.callStatusChanged\n              );\n              this.handleCallStatusChange(data.callStatusChanged);\n            }\n          },\n          error: (error) => {\n            console.error(\n              '❌ [CallService] Error in call status subscription:',\n              error\n            );\n            // Réessayer après 5 secondes\n            setTimeout(() => {\n              this.subscribeToCallStatusChanges();\n            }, 5000);\n          },\n          complete: () => {\n            console.log('🔚 [CallService] Call status subscription completed');\n            // Réessayer si la subscription se ferme\n            setTimeout(() => {\n              this.subscribeToCallStatusChanges();\n            }, 2000);\n          },\n        });\n    } catch (error) {\n      console.error(\n        '❌ [CallService] Failed to create call status subscription:',\n        error\n      );\n      setTimeout(() => {\n        this.subscribeToCallStatusChanges();\n      }, 3000);\n    }\n  }\n\n  /**\n   * Gère un appel entrant\n   */\n  private handleIncomingCall(call: IncomingCall): void {\n    console.log('🔔 [CallService] Handling incoming call:', {\n      callId: call.id,\n      callType: call.type,\n      caller: call.caller?.username,\n      conversationId: call.conversationId,\n    });\n\n    this.incomingCall.next(call);\n    this.play('ringtone', true);\n\n    console.log(\n      '🔊 [CallService] Ringtone started, call notification sent to UI'\n    );\n  }\n\n  /**\n   * Gère les changements de statut d'appel\n   */\n  private handleCallStatusChange(call: Call): void {\n    console.log('📞 [CallService] Call status changed:', call.status);\n\n    switch (call.status) {\n      case CallStatus.REJECTED:\n        console.log('❌ [CallService] Call was rejected');\n        this.stop('ringtone');\n        this.play('call-end');\n        this.activeCall.next(null);\n        this.incomingCall.next(null);\n        break;\n\n      case CallStatus.ENDED:\n        console.log('📞 [CallService] Call ended');\n        this.stopAllSounds();\n        this.play('call-end');\n        this.currentCallId = null; // Réinitialiser l'ID\n        this.activeCall.next(null);\n        this.incomingCall.next(null);\n        break;\n\n      case CallStatus.CONNECTED:\n        console.log('✅ [CallService] Call connected');\n        this.stop('ringtone');\n        this.play('call-connected');\n        this.currentCallId = call.id; // Stocker l'ID pour la subscription\n        this.activeCall.next(call);\n        this.incomingCall.next(null);\n        break;\n\n      case CallStatus.RINGING:\n        console.log('📞 [CallService] Call is ringing');\n        this.play('ringtone', true);\n        break;\n\n      default:\n        console.log('📞 [CallService] Unknown call status:', call.status);\n        break;\n    }\n  }\n\n  /**\n   * Obtient les médias utilisateur (caméra/micro)\n   */\n  private async getUserMedia(callType: CallType): Promise<MediaStream> {\n    console.log('🎥 [CallService] Getting user media for:', callType);\n\n    const constraints: MediaStreamConstraints = {\n      audio: true,\n      video: callType === CallType.VIDEO,\n    };\n\n    try {\n      const stream = await navigator.mediaDevices.getUserMedia(constraints);\n      console.log('✅ [CallService] User media obtained');\n      console.log(\n        '🔍 [CallService] Stream tracks:',\n        stream.getTracks().map((t) => ({\n          kind: t.kind,\n          enabled: t.enabled,\n          readyState: t.readyState,\n        }))\n      );\n      this.localStream = stream;\n\n      // Afficher le stream local si on a un élément vidéo\n      if (this.localVideoElement && callType === CallType.VIDEO) {\n        this.localVideoElement.srcObject = stream;\n      }\n\n      return stream;\n    } catch (error) {\n      console.error('❌ [CallService] Failed to get user media:', error);\n      throw new Error(\"Impossible d'accéder à la caméra/microphone\");\n    }\n  }\n\n  /**\n   * Ajoute le stream local à la PeerConnection\n   */\n  private addLocalStreamToPeerConnection(stream: MediaStream): void {\n    if (!this.peerConnection) {\n      console.error('❌ [CallService] No peer connection available');\n      return;\n    }\n\n    console.log('📤 [CallService] Adding local stream to peer connection');\n    stream.getTracks().forEach((track) => {\n      this.peerConnection!.addTrack(track, stream);\n    });\n  }\n\n  /**\n   * Crée une offre WebRTC\n   */\n  private async createOffer(): Promise<RTCSessionDescriptionInit> {\n    if (!this.peerConnection) {\n      throw new Error('No peer connection available');\n    }\n\n    console.log('📝 [CallService] Creating WebRTC offer');\n    const offer = await this.peerConnection.createOffer();\n    await this.peerConnection.setLocalDescription(offer);\n    return offer;\n  }\n\n  /**\n   * Crée une réponse WebRTC\n   */\n  private async createAnswer(\n    offer: RTCSessionDescriptionInit\n  ): Promise<RTCSessionDescriptionInit> {\n    if (!this.peerConnection) {\n      throw new Error('No peer connection available');\n    }\n\n    console.log('📝 [CallService] Creating WebRTC answer');\n    await this.peerConnection.setRemoteDescription(offer);\n    const answer = await this.peerConnection.createAnswer();\n    await this.peerConnection.setLocalDescription(answer);\n    return answer;\n  }\n\n  /**\n   * Configure les éléments vidéo\n   */\n  public setVideoElements(\n    localVideo: HTMLVideoElement,\n    remoteVideo: HTMLVideoElement\n  ): void {\n    console.log('📺 [CallService] Setting video elements');\n    this.localVideoElement = localVideo;\n    this.remoteVideoElement = remoteVideo;\n\n    // Configurer les éléments pour la lecture audio\n    try {\n      this.setupAudioPlayback(localVideo, remoteVideo);\n    } catch (error) {\n      console.warn('⚠️ [CallService] setupAudioPlayback error:', error);\n    }\n\n    // Si on a déjà des streams, les connecter IMMÉDIATEMENT\n    if (this.localStream && localVideo) {\n      console.log('🔗 [CallService] Attaching local stream to video element');\n      localVideo.srcObject = this.localStream;\n      localVideo.muted = true; // Local toujours muet pour éviter l'écho\n      localVideo.volume = 0;\n      localVideo.autoplay = true;\n      try {\n        localVideo.play();\n        console.log('✅ [CallService] Local video playing');\n      } catch (error) {\n        console.warn('⚠️ [CallService] Local video play error:', error);\n      }\n    } else {\n      console.warn(\n        '⚠️ [CallService] No local stream or video element available'\n      );\n    }\n\n    if (this.remoteStream && remoteVideo) {\n      console.log('🔗 [CallService] Attaching remote stream to video element');\n      remoteVideo.srcObject = this.remoteStream;\n      remoteVideo.muted = false; // Remote avec audio\n      remoteVideo.volume = 1;\n      remoteVideo.autoplay = true;\n      try {\n        remoteVideo.play();\n        console.log('✅ [CallService] Remote video playing');\n      } catch (error) {\n        console.warn('⚠️ [CallService] Remote video play error:', error);\n      }\n    } else {\n      console.warn(\n        '⚠️ [CallService] No remote stream or video element available'\n      );\n    }\n\n    // Forcer l'attachement après un délai pour s'assurer que les streams sont prêts\n    setTimeout(() => {\n      this.forceStreamAttachment();\n    }, 1000);\n  }\n\n  /**\n   * Force l'attachement des streams aux éléments vidéo\n   */\n  private forceStreamAttachment(): void {\n    console.log('🔄 [CallService] Forcing stream attachment...');\n\n    // Forcer l'attachement du stream local\n    if (this.localStream && this.localVideoElement) {\n      if (!this.localVideoElement.srcObject) {\n        console.log('🔗 [CallService] Force attaching local stream');\n        this.localVideoElement.srcObject = this.localStream;\n        this.localVideoElement.muted = true;\n        this.localVideoElement.autoplay = true;\n        this.localVideoElement\n          .play()\n          .catch((e) =>\n            console.warn('⚠️ [CallService] Force local play failed:', e)\n          );\n      }\n    }\n\n    // Forcer l'attachement du stream distant\n    if (this.remoteStream && this.remoteVideoElement) {\n      if (!this.remoteVideoElement.srcObject) {\n        console.log('🔗 [CallService] Force attaching remote stream');\n        this.remoteVideoElement.srcObject = this.remoteStream;\n        this.remoteVideoElement.muted = false;\n        this.remoteVideoElement.volume = 1;\n        this.remoteVideoElement.autoplay = true;\n        this.remoteVideoElement\n          .play()\n          .catch((e) =>\n            console.warn('⚠️ [CallService] Force remote play failed:', e)\n          );\n      }\n    }\n\n    // Vérifier l'état après forçage\n    console.log('📊 [CallService] Stream attachment status:', {\n      localAttached: !!this.localVideoElement?.srcObject,\n      remoteAttached: !!this.remoteVideoElement?.srcObject,\n      localStreamExists: !!this.localStream,\n      remoteStreamExists: !!this.remoteStream,\n    });\n  }\n\n  /**\n   * Configure la lecture audio pour les éléments vidéo\n   */\n  private setupAudioPlayback(\n    localVideo: HTMLVideoElement,\n    remoteVideo: HTMLVideoElement\n  ): void {\n    console.log('🔊 [CallService] Setting up audio playback');\n\n    // Configurer les propriétés audio\n    localVideo.volume = 0; // Local toujours muet pour éviter l'écho\n    remoteVideo.volume = 1; // Remote avec volume maximum\n\n    // Forcer l'autoplay\n    localVideo.autoplay = true;\n    remoteVideo.autoplay = true;\n\n    // Événements pour débugger\n    remoteVideo.addEventListener('loadedmetadata', () => {\n      console.log('🎵 [CallService] Remote video metadata loaded');\n    });\n\n    remoteVideo.addEventListener('canplay', () => {\n      console.log('🎵 [CallService] Remote video can play');\n      this.ensureAudioPlayback(remoteVideo);\n    });\n\n    remoteVideo.addEventListener('play', () => {\n      console.log('🎵 [CallService] Remote video started playing');\n    });\n\n    remoteVideo.addEventListener('pause', () => {\n      console.log('⏸️ [CallService] Remote video paused');\n    });\n  }\n\n  /**\n   * Force la lecture audio\n   */\n  private ensureAudioPlayback(videoElement: HTMLVideoElement): void {\n    console.log(\n      '🔊 [CallService] Ensuring audio playback for element:',\n      videoElement === this.localVideoElement ? 'local' : 'remote'\n    );\n\n    // Forcer la lecture\n    videoElement\n      .play()\n      .then(() => {\n        console.log(\n          '✅ [CallService] Video/audio playback started successfully'\n        );\n      })\n      .catch((error) => {\n        console.warn(\n          '⚠️ [CallService] Autoplay prevented, user interaction required:',\n          error\n        );\n\n        // Essayer de jouer après interaction utilisateur\n        const playOnInteraction = () => {\n          videoElement\n            .play()\n            .then(() => {\n              console.log(\n                '✅ [CallService] Video/audio playback started after user interaction'\n              );\n              document.removeEventListener('click', playOnInteraction);\n              document.removeEventListener('keydown', playOnInteraction);\n            })\n            .catch((err) => {\n              console.error(\n                '❌ [CallService] Failed to play after interaction:',\n                err\n              );\n            });\n        };\n\n        document.addEventListener('click', playOnInteraction);\n        document.addEventListener('keydown', playOnInteraction);\n      });\n  }\n\n  /**\n   * Initie un appel WebRTC\n   */\n  initiateCall(\n    recipientId: string,\n    callType: CallType,\n    conversationId?: string\n  ): Observable<Call> {\n    console.log('🔄 [CallService] Initiating call:', {\n      recipientId,\n      callType,\n      conversationId,\n    });\n\n    if (!recipientId) {\n      const error = new Error('Recipient ID is required');\n      console.error('❌ [CallService] initiateCall error:', error);\n      return throwError(() => error);\n    }\n\n    // Générer un ID unique pour l'appel\n    const callId = `call_${Date.now()}_${Math.random()\n      .toString(36)\n      .substr(2, 9)}`;\n\n    // Créer une vraie offre WebRTC\n    return from(this.createWebRTCOffer(callType)).pipe(\n      switchMap((offer) => {\n        const variables = {\n          recipientId,\n          callType: callType,\n          callId,\n          offer: JSON.stringify(offer),\n          conversationId,\n        };\n\n        console.log(\n          '📤 [CallService] Sending initiate call mutation:',\n          variables\n        );\n\n        return this.apollo\n          .mutate<{ initiateCall: Call }>({\n            mutation: INITIATE_CALL_MUTATION,\n            variables,\n          })\n          .pipe(\n            map((result) => {\n              console.log(\n                '✅ [CallService] Call initiated successfully:',\n                result\n              );\n\n              if (!result.data?.initiateCall) {\n                throw new Error('No call data received from server');\n              }\n\n              const call = result.data.initiateCall;\n              console.log('📞 [CallService] Call details:', {\n                id: call.id,\n                type: call.type,\n                status: call.status,\n                caller: call.caller?.username,\n                recipient: call.recipient?.username,\n              });\n\n              // Mettre à jour l'état local\n              this.activeCall.next(call);\n\n              return call;\n            }),\n            catchError((error) => {\n              console.error('❌ [CallService] initiateCall error:', error);\n              this.logger.error('Error initiating call:', error);\n\n              let errorMessage = \"Erreur lors de l'initiation de l'appel\";\n              if (error.networkError) {\n                errorMessage = 'Erreur de connexion réseau';\n              } else if (error.graphQLErrors?.length > 0) {\n                errorMessage = error.graphQLErrors[0].message || errorMessage;\n              }\n\n              return throwError(() => new Error(errorMessage));\n            })\n          );\n      }),\n      catchError((error) => {\n        console.error('❌ [CallService] WebRTC error:', error);\n        return throwError(() => new Error('Erreur WebRTC: ' + error.message));\n      })\n    );\n  }\n\n  /**\n   * Crée une offre WebRTC complète avec médias\n   */\n  private async createWebRTCOffer(\n    callType: CallType\n  ): Promise<RTCSessionDescriptionInit> {\n    try {\n      // Réinitialiser la PeerConnection si nécessaire\n      if (!this.peerConnection) {\n        this.initializeWebRTC();\n      }\n\n      // Obtenir les médias utilisateur\n      const stream = await this.getUserMedia(callType);\n\n      // Ajouter le stream à la PeerConnection\n      this.addLocalStreamToPeerConnection(stream);\n\n      // Créer l'offre\n      const offer = await this.createOffer();\n\n      console.log('✅ [CallService] WebRTC offer created successfully');\n      return offer;\n    } catch (error) {\n      console.error('❌ [CallService] Failed to create WebRTC offer:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Accepte un appel entrant\n   */\n  acceptCall(incomingCall: IncomingCall): Observable<Call> {\n    console.log('🔄 [CallService] Accepting call:', incomingCall.id);\n\n    // Créer une vraie réponse WebRTC\n    return from(this.createWebRTCAnswer(incomingCall)).pipe(\n      switchMap((answer) => {\n        return this.apollo\n          .mutate<{ acceptCall: Call }>({\n            mutation: ACCEPT_CALL_MUTATION,\n            variables: {\n              callId: incomingCall.id,\n              answer: JSON.stringify(answer),\n            },\n          })\n          .pipe(\n            switchMap((result) => {\n              console.log(\n                '✅ [CallService] Call accepted successfully:',\n                result\n              );\n\n              if (!result.data?.acceptCall) {\n                throw new Error('No call data received from server');\n              }\n\n              const call = result.data.acceptCall;\n\n              // Arrêter la sonnerie\n              this.stop('ringtone');\n              this.play('call-connected');\n\n              // Démarrer les médias pour l'appel de manière asynchrone\n              return from(this.startMediaForCall(incomingCall, call));\n            }),\n            catchError((error) => {\n              console.error('❌ [CallService] acceptCall error:', error);\n              this.logger.error('Error accepting call:', error);\n              return throwError(\n                () => new Error(\"Erreur lors de l'acceptation de l'appel\")\n              );\n            })\n          );\n      }),\n      catchError((error) => {\n        console.error('❌ [CallService] WebRTC answer error:', error);\n        return throwError(() => new Error('Erreur WebRTC: ' + error.message));\n      })\n    );\n  }\n\n  /**\n   * Crée une réponse WebRTC complète avec médias\n   */\n  private async createWebRTCAnswer(\n    incomingCall: IncomingCall\n  ): Promise<RTCSessionDescriptionInit> {\n    try {\n      console.log(\n        '🔄 [CallService] Creating WebRTC answer for incoming call:',\n        {\n          callId: incomingCall.id,\n          callType: incomingCall.type,\n          hasOffer: !!incomingCall.offer,\n          offerLength: incomingCall.offer?.length || 0,\n        }\n      );\n\n      // Réinitialiser la PeerConnection si nécessaire\n      if (!this.peerConnection) {\n        console.log(\n          '🔧 [CallService] Initializing new PeerConnection for answer'\n        );\n        this.initializeWebRTC();\n      }\n\n      // Obtenir les médias utilisateur\n      console.log('🎥 [CallService] Getting user media for answer...');\n      const stream = await this.getUserMedia(incomingCall.type);\n\n      // Ajouter le stream à la PeerConnection\n      console.log(\n        '📤 [CallService] Adding local stream to PeerConnection for answer'\n      );\n      this.addLocalStreamToPeerConnection(stream);\n\n      // Récupérer l'offre depuis l'appel entrant\n      if (!incomingCall.offer) {\n        throw new Error('No offer received in incoming call');\n      }\n\n      const offer = JSON.parse(incomingCall.offer);\n\n      if (!offer || !offer.type || !offer.sdp) {\n        throw new Error('Invalid offer format received');\n      }\n\n      // Créer la réponse\n      const answer = await this.createAnswer(offer);\n\n      console.log('✅ [CallService] WebRTC answer created successfully');\n      return answer;\n    } catch (error) {\n      console.error('❌ [CallService] Failed to create WebRTC answer:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Rejette un appel entrant\n   */\n  rejectCall(callId: string, reason?: string): Observable<CallSuccess> {\n    console.log('🔄 [CallService] Rejecting call:', callId, reason);\n\n    return this.apollo\n      .mutate<{ rejectCall: CallSuccess }>({\n        mutation: REJECT_CALL_MUTATION,\n        variables: {\n          callId,\n          reason: reason || 'User rejected',\n        },\n      })\n      .pipe(\n        map((result) => {\n          console.log('✅ [CallService] Call rejected successfully:', result);\n\n          if (!result.data?.rejectCall) {\n            throw new Error('No response received from server');\n          }\n\n          // Mettre à jour l'état local\n          this.incomingCall.next(null);\n          this.activeCall.next(null);\n\n          // Arrêter la sonnerie\n          this.stop('ringtone');\n\n          return result.data.rejectCall;\n        }),\n        catchError((error) => {\n          console.error('❌ [CallService] rejectCall error:', error);\n          this.logger.error('Error rejecting call:', error);\n          return throwError(() => new Error(\"Erreur lors du rejet de l'appel\"));\n        })\n      );\n  }\n\n  /**\n   * Termine un appel en cours\n   */\n  endCall(callId: string): Observable<CallSuccess> {\n    console.log('🔄 [CallService] Ending call:', callId);\n\n    return this.apollo\n      .mutate<{ endCall: CallSuccess }>({\n        mutation: END_CALL_MUTATION,\n        variables: {\n          callId,\n          feedback: null, // Pas de feedback pour l'instant\n        },\n      })\n      .pipe(\n        map((result) => {\n          console.log('✅ [CallService] Call ended successfully:', result);\n\n          if (!result.data?.endCall) {\n            throw new Error('No response received from server');\n          }\n\n          // Mettre à jour l'état local\n          this.activeCall.next(null);\n          this.incomingCall.next(null);\n\n          // Arrêter tous les sons\n          this.stopAllSounds();\n          this.play('call-end');\n\n          // Nettoyer les ressources WebRTC\n          this.cleanupWebRTC();\n\n          return result.data.endCall;\n        }),\n        catchError((error) => {\n          console.error('❌ [CallService] endCall error:', error);\n          this.logger.error('Error ending call:', error);\n          return throwError(\n            () => new Error(\"Erreur lors de la fin de l'appel\")\n          );\n        })\n      );\n  }\n\n  /**\n   * Bascule l'état des médias (audio/vidéo) pendant un appel\n   */\n  toggleMedia(\n    callId: string,\n    enableVideo?: boolean,\n    enableAudio?: boolean\n  ): Observable<CallSuccess> {\n    console.log('🔄 [CallService] Toggling media:', {\n      callId,\n      enableVideo,\n      enableAudio,\n    });\n\n    return this.apollo\n      .mutate<{ toggleCallMedia: CallSuccess }>({\n        mutation: TOGGLE_CALL_MEDIA_MUTATION,\n        variables: {\n          callId,\n          video: enableVideo,\n          audio: enableAudio,\n        },\n      })\n      .pipe(\n        map((result) => {\n          console.log('✅ [CallService] Media toggled successfully:', result);\n\n          if (!result.data?.toggleCallMedia) {\n            throw new Error('No response received from server');\n          }\n\n          return result.data.toggleCallMedia;\n        }),\n        catchError((error) => {\n          console.error('❌ [CallService] toggleMedia error:', error);\n          this.logger.error('Error toggling media:', error);\n          return throwError(\n            () => new Error('Erreur lors du changement des médias')\n          );\n        })\n      );\n  }\n\n  /**\n   * Démarre les médias pour un appel accepté\n   */\n  private async startMediaForCall(\n    incomingCall: IncomingCall,\n    call: Call\n  ): Promise<Call> {\n    console.log('🎥 [CallService] Call connected - playing connection sound');\n\n    // Jouer le son de connexion\n    this.play('call-connected');\n\n    // Mettre à jour l'état local\n    this.activeCall.next(call);\n    this.incomingCall.next(null); // Supprimer l'appel entrant\n\n    return call;\n  }\n\n  /**\n   * Active/désactive la vidéo\n   */\n  toggleVideo(): boolean {\n    this.isVideoEnabled = !this.isVideoEnabled;\n    console.log('📹 [CallService] Video toggled:', this.isVideoEnabled);\n    return this.isVideoEnabled;\n  }\n\n  /**\n   * Active/désactive l'audio\n   */\n  toggleAudio(): boolean {\n    this.isAudioEnabled = !this.isAudioEnabled;\n    console.log('🎤 [CallService] Audio toggled:', this.isAudioEnabled);\n    return this.isAudioEnabled;\n  }\n\n  /**\n   * Obtient l'état de la vidéo\n   */\n  getVideoEnabled(): boolean {\n    return this.isVideoEnabled;\n  }\n\n  /**\n   * Obtient l'état de l'audio\n   */\n  getAudioEnabled(): boolean {\n    return this.isAudioEnabled;\n  }\n\n  /**\n   * Arrête tous les sons\n   */\n  private stopAllSounds(): void {\n    console.log('🔇 [CallService] Stopping all sounds');\n    Object.keys(this.sounds).forEach((name) => {\n      this.stop(name);\n    });\n  }\n\n  /**\n   * Active les sons après interaction utilisateur (pour contourner les restrictions du navigateur)\n   */\n  public enableSounds(): void {\n    console.log('🔊 [CallService] Enabling sounds after user interaction');\n    Object.values(this.sounds).forEach((sound) => {\n      if (sound) {\n        sound.muted = false;\n        sound.volume = 0.7;\n        // Jouer et arrêter immédiatement pour \"débloquer\" l'audio\n        sound\n          .play()\n          .then(() => {\n            sound.pause();\n            sound.currentTime = 0;\n          })\n          .catch(() => {\n            // Ignorer les erreurs ici\n          });\n      }\n    });\n  }\n\n  /**\n   * Nettoie les ressources WebRTC\n   */\n  private cleanupWebRTC(): void {\n    console.log('🧹 [CallService] Cleaning up WebRTC resources');\n\n    // Arrêter les tracks du stream local\n    if (this.localStream) {\n      this.localStream.getTracks().forEach((track) => {\n        track.stop();\n      });\n      this.localStream = null;\n    }\n\n    // Nettoyer les éléments vidéo\n    if (this.localVideoElement) {\n      this.localVideoElement.srcObject = null;\n    }\n    if (this.remoteVideoElement) {\n      this.remoteVideoElement.srcObject = null;\n    }\n\n    // Fermer la PeerConnection\n    if (this.peerConnection) {\n      this.peerConnection.close();\n      this.peerConnection = null;\n    }\n\n    this.remoteStream = null;\n  }\n\n  ngOnDestroy(): void {\n    this.stopAllSounds();\n    this.cleanupWebRTC();\n  }\n}\n"], "mappings": ";AAEA,SAASA,eAAe,EAAcC,UAAU,EAAMC,IAAI,QAAQ,MAAM;AACxE,SAASC,GAAG,EAAEC,UAAU,EAAEC,SAAS,QAAQ,gBAAgB;AAC3D,SAEEC,QAAQ,EACRC,UAAU,QAGL,yBAAyB;AAChC,SACEC,sBAAsB,EACtBC,oBAAoB,EACpBC,oBAAoB,EACpBC,iBAAiB,EACjBC,0BAA0B,EAC1BC,0BAA0B,EAC1BC,gCAAgC,EAChCC,wBAAwB,QACnB,4BAA4B;;;;AAMnC,OAAM,MAAOC,WAAW;EA0BtBC,YAAoBC,MAAc,EAAUC,MAAqB;IAA7C,KAAAD,MAAM,GAANA,MAAM;IAAkB,KAAAC,MAAM,GAANA,MAAM;IAzBlD;IACQ,KAAAC,UAAU,GAAG,IAAIpB,eAAe,CAAc,IAAI,CAAC;IACnD,KAAAqB,YAAY,GAAG,IAAIrB,eAAe,CAAsB,IAAI,CAAC;IAErE;IACO,KAAAsB,WAAW,GAAG,IAAI,CAACF,UAAU,CAACG,YAAY,EAAE;IAC5C,KAAAC,aAAa,GAAG,IAAI,CAACH,YAAY,CAACE,YAAY,EAAE;IAEvD;IACQ,KAAAE,MAAM,GAAwC,EAAE;IAChD,KAAAC,SAAS,GAA+B,EAAE;IAC1C,KAAAC,KAAK,GAAG,KAAK;IAErB;IACQ,KAAAC,cAAc,GAAG,IAAI;IACrB,KAAAC,cAAc,GAAG,IAAI;IAE7B;IACQ,KAAAC,cAAc,GAA6B,IAAI;IAC/C,KAAAC,WAAW,GAAuB,IAAI;IACtC,KAAAC,YAAY,GAAuB,IAAI;IACvC,KAAAC,iBAAiB,GAA4B,IAAI;IACjD,KAAAC,kBAAkB,GAA4B,IAAI;IAClD,KAAAC,aAAa,GAAkB,IAAI;IAGzC,IAAI,CAACC,aAAa,EAAE;IACpB,IAAI,CAACC,uBAAuB,EAAE;IAC9B,IAAI,CAACC,gBAAgB,EAAE;EACzB;EAEA;;;EAGQD,uBAAuBA,CAAA;IAC7B;IACAE,UAAU,CAAC,MAAK;MACd,IAAI,CAACC,wBAAwB,EAAE;MAC/B,IAAI,CAACC,4BAA4B,EAAE;MACnC,IAAI,CAACC,sBAAsB,EAAE;IAC/B,CAAC,EAAE,IAAI,CAAC;IAER;IACAH,UAAU,CAAC,MAAK;MACd,IAAI,CAAC,IAAI,CAAClB,YAAY,CAACsB,KAAK,EAAE;QAC5B;QACA,IAAI,CAACH,wBAAwB,EAAE;QAC/B,IAAI,CAACC,4BAA4B,EAAE;QACnC,IAAI,CAACC,sBAAsB,EAAE;;IAEjC,CAAC,EAAE,KAAK,CAAC;EACX;EAEA;;;EAGQJ,gBAAgBA,CAAA;IACtBM,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;IAEtD;IACA,MAAMC,aAAa,GAAqB;MACtCC,UAAU,EAAE,CACV;QAAEC,IAAI,EAAE;MAA8B,CAAE,EACxC;QAAEA,IAAI,EAAE;MAA+B,CAAE;KAE5C;IAED,IAAI;MACF,IAAI,CAAClB,cAAc,GAAG,IAAImB,iBAAiB,CAACH,aAAa,CAAC;MAC1D,IAAI,CAACI,yBAAyB,EAAE;MAChCN,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;KAC/D,CAAC,OAAOM,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CAAC,8CAA8C,EAAEA,KAAK,CAAC;;EAExE;EAEA;;;EAGQD,yBAAyBA,CAAA;IAC/B,IAAI,CAAC,IAAI,CAACpB,cAAc,EAAE;IAE1B;IACA,IAAI,CAACA,cAAc,CAACsB,OAAO,GAAIC,KAAK,IAAI;MACtCT,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;MACtDD,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE;QAC9CS,QAAQ,EAAED,KAAK,CAACE,OAAO,CAAC,CAAC,CAAC,EAAEC,EAAE;QAC9BC,MAAM,EAAEJ,KAAK,CAACE,OAAO,CAAC,CAAC,CAAC,EAAEG,SAAS,EAAE,CAACvD,GAAG,CAAEwD,CAAC,KAAM;UAChDC,IAAI,EAAED,CAAC,CAACC,IAAI;UACZC,OAAO,EAAEF,CAAC,CAACE,OAAO;UAClBC,UAAU,EAAEH,CAAC,CAACG;SACf,CAAC;OACH,CAAC;MAEF,IAAI,CAAC9B,YAAY,GAAGqB,KAAK,CAACE,OAAO,CAAC,CAAC,CAAC;MAEpC,IAAI,IAAI,CAACrB,kBAAkB,EAAE;QAC3BU,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC;QACtE,IAAI,CAACX,kBAAkB,CAAC6B,SAAS,GAAG,IAAI,CAAC/B,YAAY;QAErD;QACA,IAAI,CAACE,kBAAkB,CAAC8B,gBAAgB,GAAG,MAAK;UAC9CpB,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;QAC9D,CAAC;QAED,IAAI,CAACX,kBAAkB,CAAC+B,SAAS,GAAG,MAAK;UACvCrB,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;QACvD,CAAC;QAED,IAAI,CAACX,kBAAkB,CAACgC,MAAM,GAAG,MAAK;UACpCtB,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;QAC9D,CAAC;QAED;QACA,IAAI;UACF,IAAI,CAACsB,mBAAmB,CAAC,IAAI,CAACjC,kBAAkB,CAAC;SAClD,CAAC,OAAOiB,KAAK,EAAE;UACdP,OAAO,CAACwB,IAAI,CACV,wDAAwD,EACxDjB,KAAK,CACN;UACD;UACAP,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;UAC9D,IAAI,CAACX,kBAAkB,CAACmC,IAAI,EAAE,CAACC,KAAK,CAAEC,CAAC,IAAI;YACzC3B,OAAO,CAACwB,IAAI,CAAC,sCAAsC,EAAEG,CAAC,CAAC;YACvD;YACA3B,OAAO,CAACC,GAAG,CACT,8DAA8D,CAC/D;UACH,CAAC,CAAC;;OAEL,MAAM;QACLD,OAAO,CAACwB,IAAI,CAAC,oDAAoD,CAAC;;IAEtE,CAAC;IAED;IACA,IAAI,CAACtC,cAAc,CAAC0C,cAAc,GAAInB,KAAK,IAAI;MAC7C,IAAIA,KAAK,CAACoB,SAAS,EAAE;QACnB7B,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;QACvD,IAAI,CAAC6B,UAAU,CAAC,eAAe,EAAEC,IAAI,CAACC,SAAS,CAACvB,KAAK,CAACoB,SAAS,CAAC,CAAC;;IAErE,CAAC;IAED;IACA,IAAI,CAAC3C,cAAc,CAAC+C,uBAAuB,GAAG,MAAK;MACjDjC,OAAO,CAACC,GAAG,CACT,oCAAoC,EACpC,IAAI,CAACf,cAAc,EAAEgD,eAAe,CACrC;IACH,CAAC;EACH;EAEA;;;EAGQ1C,aAAaA,CAAA;IACnB;IACA,IAAI,CAAC2C,qBAAqB,EAAE;IAE5B;IACA,IAAI,CAACC,SAAS,CAAC,cAAc,EAAE,gCAAgC,CAAC;IAEhEpC,OAAO,CAACC,GAAG,CACT,iEAAiE,CAClE;EACH;EAEA;;;EAGQkC,qBAAqBA,CAAA;IAC3B,IAAI;MACF;MACA,MAAME,YAAY,GAAG,KAAKC,MAAM,CAACC,YAAY,IAC1CD,MAAc,CAACE,kBAAkB,EAAC,CAAE;MAEvC;MACA,IAAI,CAAC3D,MAAM,CAAC,oBAAoB,CAAC,GAC/B,IAAI,CAAC4D,mBAAmB,CAACJ,YAAY,CAAC;MAExC;MACA,IAAI,CAACxD,MAAM,CAAC,0BAA0B,CAAC,GACrC,IAAI,CAAC6D,oBAAoB,CAACL,YAAY,CAAC;MAEzC;MACA,IAAI,CAACxD,MAAM,CAAC,oBAAoB,CAAC,GAAG,IAAI,CAAC8D,cAAc,CAACN,YAAY,CAAC;MAErE;MACA,IAAI,CAACxD,MAAM,CAAC,wBAAwB,CAAC,GACnC,IAAI,CAAC+D,uBAAuB,CAACP,YAAY,CAAC;MAE5CrC,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;KACrE,CAAC,OAAOM,KAAK,EAAE;MACdP,OAAO,CAACwB,IAAI,CACV,qDAAqD,EACrDjB,KAAK,CACN;;EAEL;EAEA;;;EAGQkC,mBAAmBA,CAACJ,YAA0B;IACpD,MAAMQ,KAAK,GAAG,IAAIC,KAAK,EAAE;IACzBD,KAAK,CAACE,MAAM,GAAG,GAAG;IAElB,IAAIjE,SAAS,GAAG,KAAK;IACrB,IAAIkE,UAAU,GAAU,EAAE;IAEzBH,KAAa,CAACI,aAAa,GAAG,MAAK;MAClC,IAAInE,SAAS,EAAE,OAAOoE,OAAO,CAACC,OAAO,EAAE;MAEvCrE,SAAS,GAAG,IAAI;MAEhB,MAAMsE,UAAU,GAAGA,CAAA,KAAK;QACtB,IAAI,CAACtE,SAAS,EAAE;QAEhB;QACA,MAAMuE,MAAM,GAAG,CACb;UAAEC,IAAI,EAAE,MAAM;UAAEC,QAAQ,EAAE;QAAK,CAAE,EACjC;UAAED,IAAI,EAAE,MAAM;UAAEC,QAAQ,EAAE;QAAK,CAAE,EACjC;UAAED,IAAI,EAAE,MAAM;UAAEC,QAAQ,EAAE;QAAI,CAAE,EAChC;UAAED,IAAI,EAAE,MAAM;UAAEC,QAAQ,EAAE;QAAI,CAAE,EAChC;UAAED,IAAI,EAAE,MAAM;UAAEC,QAAQ,EAAE;QAAK,CAAE,EACjC;UAAED,IAAI,EAAE,MAAM;UAAEC,QAAQ,EAAE;QAAK,CAAE,EACjC;UAAED,IAAI,EAAE,MAAM;UAAEC,QAAQ,EAAE;QAAI,CAAE,EAChC;UAAED,IAAI,EAAE,MAAM;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAE;QAAA,CACnC;;QAED,IAAIC,WAAW,GAAGnB,YAAY,CAACmB,WAAW;QAE1CH,MAAM,CAACI,OAAO,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAI;UAC7B,IAAI,CAAC7E,SAAS,EAAE;UAEhB,MAAM8E,UAAU,GAAGvB,YAAY,CAACwB,gBAAgB,EAAE;UAClD,MAAMC,QAAQ,GAAGzB,YAAY,CAAC0B,UAAU,EAAE;UAE1CH,UAAU,CAACI,OAAO,CAACF,QAAQ,CAAC;UAC5BA,QAAQ,CAACE,OAAO,CAAC3B,YAAY,CAAC4B,WAAW,CAAC;UAE1CL,UAAU,CAACM,SAAS,CAACnE,KAAK,GAAG2D,IAAI,CAACJ,IAAI;UACtCM,UAAU,CAACO,IAAI,GAAG,QAAQ,CAAC,CAAC;UAE5B;UACAL,QAAQ,CAACM,IAAI,CAACC,cAAc,CAAC,CAAC,EAAEb,WAAW,CAAC;UAC5CM,QAAQ,CAACM,IAAI,CAACE,uBAAuB,CAAC,GAAG,EAAEd,WAAW,GAAG,IAAI,CAAC;UAC9DM,QAAQ,CAACM,IAAI,CAACE,uBAAuB,CACnC,GAAG,EACHd,WAAW,GAAGE,IAAI,CAACH,QAAQ,GAAG,GAAG,CAClC;UACDO,QAAQ,CAACM,IAAI,CAACG,4BAA4B,CACxC,IAAI,EACJf,WAAW,GAAGE,IAAI,CAACH,QAAQ,CAC5B;UAEDK,UAAU,CAACY,KAAK,CAAChB,WAAW,CAAC;UAC7BI,UAAU,CAACa,IAAI,CAACjB,WAAW,GAAGE,IAAI,CAACH,QAAQ,CAAC;UAE5CC,WAAW,IAAIE,IAAI,CAACH,QAAQ,GAAG,IAAI,CAAC,CAAC;QACvC,CAAC,CAAC;QAEF;QACA,MAAMmB,SAAS,GAAG/E,UAAU,CAAC,MAAK;UAChC,IAAIb,SAAS,EAAE;YACbsE,UAAU,EAAE;;QAEhB,CAAC,EAAE,CAACI,WAAW,GAAGnB,YAAY,CAACmB,WAAW,GAAG,GAAG,IAAI,IAAI,CAAC;QAEzDR,UAAU,CAAC2B,IAAI,CAACD,SAAS,CAAC;MAC5B,CAAC;MAEDtB,UAAU,EAAE;MACZ,OAAOF,OAAO,CAACC,OAAO,EAAE;IAC1B,CAAC;IAEAN,KAAa,CAAC+B,aAAa,GAAG,MAAK;MAClC9F,SAAS,GAAG,KAAK;MACjBkE,UAAU,CAACS,OAAO,CAAE7C,EAAE,IAAKiE,YAAY,CAACjE,EAAE,CAAC,CAAC;MAC5CoC,UAAU,GAAG,EAAE;IACjB,CAAC;IAED,OAAOH,KAAK;EACd;EAEA;;;EAGQH,oBAAoBA,CAACL,YAA0B;IACrD,MAAMQ,KAAK,GAAG,IAAIC,KAAK,EAAE;IACzBD,KAAK,CAACE,MAAM,GAAG,GAAG;IAEjBF,KAAa,CAACI,aAAa,GAAG,MAAK;MAClC;MACA,MAAMI,MAAM,GAAG,CACb;QAAEC,IAAI,EAAE,MAAM;QAAEC,QAAQ,EAAE;MAAI,CAAE,EAChC;QAAED,IAAI,EAAE,MAAM;QAAEC,QAAQ,EAAE;MAAI,CAAE,EAChC;QAAED,IAAI,EAAE,MAAM;QAAEC,QAAQ,EAAE;MAAI,CAAE,EAChC;QAAED,IAAI,EAAE,MAAM;QAAEC,QAAQ,EAAE;MAAG,CAAE,CAAE;MAAA,CAClC;;MAED,IAAIC,WAAW,GAAGnB,YAAY,CAACmB,WAAW;MAE1CH,MAAM,CAACI,OAAO,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAI;QAC7B,MAAMC,UAAU,GAAGvB,YAAY,CAACwB,gBAAgB,EAAE;QAClD,MAAMC,QAAQ,GAAGzB,YAAY,CAAC0B,UAAU,EAAE;QAE1CH,UAAU,CAACI,OAAO,CAACF,QAAQ,CAAC;QAC5BA,QAAQ,CAACE,OAAO,CAAC3B,YAAY,CAAC4B,WAAW,CAAC;QAE1CL,UAAU,CAACM,SAAS,CAACnE,KAAK,GAAG2D,IAAI,CAACJ,IAAI;QACtCM,UAAU,CAACO,IAAI,GAAG,UAAU,CAAC,CAAC;QAE9B;QACAL,QAAQ,CAACM,IAAI,CAACC,cAAc,CAAC,CAAC,EAAEb,WAAW,CAAC;QAC5CM,QAAQ,CAACM,IAAI,CAACE,uBAAuB,CAAC,IAAI,EAAEd,WAAW,GAAG,IAAI,CAAC;QAC/DM,QAAQ,CAACM,IAAI,CAACE,uBAAuB,CACnC,IAAI,EACJd,WAAW,GAAGE,IAAI,CAACH,QAAQ,GAAG,GAAG,CAClC;QACDO,QAAQ,CAACM,IAAI,CAACG,4BAA4B,CACxC,IAAI,EACJf,WAAW,GAAGE,IAAI,CAACH,QAAQ,CAC5B;QAEDK,UAAU,CAACY,KAAK,CAAChB,WAAW,CAAC;QAC7BI,UAAU,CAACa,IAAI,CAACjB,WAAW,GAAGE,IAAI,CAACH,QAAQ,CAAC;QAE5CC,WAAW,IAAIE,IAAI,CAACH,QAAQ,GAAG,GAAG,CAAC,CAAC;MACtC,CAAC,CAAC;;MAEF,OAAOL,OAAO,CAACC,OAAO,EAAE;IAC1B,CAAC;IAED,OAAON,KAAK;EACd;EAEA;;;EAGQF,cAAcA,CAACN,YAA0B;IAC/C,MAAMQ,KAAK,GAAG,IAAIC,KAAK,EAAE;IACzBD,KAAK,CAACE,MAAM,GAAG,GAAG;IAEjBF,KAAa,CAACI,aAAa,GAAG,MAAK;MAClC;MACA,MAAMI,MAAM,GAAG,CACb;QAAEC,IAAI,EAAE,MAAM;QAAEC,QAAQ,EAAE;MAAG,CAAE,EAC/B;QAAED,IAAI,EAAE,MAAM;QAAEC,QAAQ,EAAE;MAAG,CAAE,EAC/B;QAAED,IAAI,EAAE,MAAM;QAAEC,QAAQ,EAAE;MAAG,CAAE,EAC/B;QAAED,IAAI,EAAE,KAAK;QAAEC,QAAQ,EAAE;MAAG,CAAE,CAAE;MAAA,CACjC;;MAED,IAAIC,WAAW,GAAGnB,YAAY,CAACmB,WAAW;MAE1CH,MAAM,CAACI,OAAO,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAI;QAC7B,MAAMC,UAAU,GAAGvB,YAAY,CAACwB,gBAAgB,EAAE;QAClD,MAAMC,QAAQ,GAAGzB,YAAY,CAAC0B,UAAU,EAAE;QAE1CH,UAAU,CAACI,OAAO,CAACF,QAAQ,CAAC;QAC5BA,QAAQ,CAACE,OAAO,CAAC3B,YAAY,CAAC4B,WAAW,CAAC;QAE1CL,UAAU,CAACM,SAAS,CAACnE,KAAK,GAAG2D,IAAI,CAACJ,IAAI;QACtCM,UAAU,CAACO,IAAI,GAAG,MAAM,CAAC,CAAC;QAE1B;QACAL,QAAQ,CAACM,IAAI,CAACC,cAAc,CAAC,CAAC,EAAEb,WAAW,CAAC;QAC5CM,QAAQ,CAACM,IAAI,CAACE,uBAAuB,CAAC,GAAG,EAAEd,WAAW,GAAG,IAAI,CAAC;QAC9DM,QAAQ,CAACM,IAAI,CAACE,uBAAuB,CACnC,GAAG,EACHd,WAAW,GAAGE,IAAI,CAACH,QAAQ,GAAG,GAAG,CAClC;QACDO,QAAQ,CAACM,IAAI,CAACG,4BAA4B,CACxC,IAAI,EACJf,WAAW,GAAGE,IAAI,CAACH,QAAQ,CAC5B;QAEDK,UAAU,CAACY,KAAK,CAAChB,WAAW,CAAC;QAC7BI,UAAU,CAACa,IAAI,CAACjB,WAAW,GAAGE,IAAI,CAACH,QAAQ,CAAC;QAE5CC,WAAW,IAAIE,IAAI,CAACH,QAAQ,GAAG,GAAG,CAAC,CAAC;MACtC,CAAC,CAAC;;MAEF,OAAOL,OAAO,CAACC,OAAO,EAAE;IAC1B,CAAC;IAED,OAAON,KAAK;EACd;EAEA;;;EAGQD,uBAAuBA,CAC7BP,YAA0B;IAE1B,MAAMQ,KAAK,GAAG,IAAIC,KAAK,EAAE;IACzBD,KAAK,CAACE,MAAM,GAAG,GAAG;IAEjBF,KAAa,CAACI,aAAa,GAAG,MAAK;MAClC;MACA,MAAM6B,KAAK,GAAG,CACZ;QAAExB,IAAI,EAAE,MAAM;QAAEC,QAAQ,EAAE,IAAI;QAAEwB,KAAK,EAAE;MAAC,CAAE,EAC1C;QAAEzB,IAAI,EAAE,MAAM;QAAEC,QAAQ,EAAE,IAAI;QAAEwB,KAAK,EAAE;MAAG,CAAE,CAAE;MAAA,CAC/C;;MAEDD,KAAK,CAACrB,OAAO,CAAEC,IAAI,IAAI;QACrB/D,UAAU,CAAC,MAAK;UACd,MAAMiE,UAAU,GAAGvB,YAAY,CAACwB,gBAAgB,EAAE;UAClD,MAAMC,QAAQ,GAAGzB,YAAY,CAAC0B,UAAU,EAAE;UAE1CH,UAAU,CAACI,OAAO,CAACF,QAAQ,CAAC;UAC5BA,QAAQ,CAACE,OAAO,CAAC3B,YAAY,CAAC4B,WAAW,CAAC;UAE1CL,UAAU,CAACM,SAAS,CAACnE,KAAK,GAAG2D,IAAI,CAACJ,IAAI;UACtCM,UAAU,CAACO,IAAI,GAAG,UAAU,CAAC,CAAC;UAE9B,MAAMa,SAAS,GAAG3C,YAAY,CAACmB,WAAW;UAE1C;UACAM,QAAQ,CAACM,IAAI,CAACC,cAAc,CAAC,CAAC,EAAEW,SAAS,CAAC;UAC1ClB,QAAQ,CAACM,IAAI,CAACE,uBAAuB,CAAC,GAAG,EAAEU,SAAS,GAAG,IAAI,CAAC;UAC5DlB,QAAQ,CAACM,IAAI,CAACE,uBAAuB,CACnC,GAAG,EACHU,SAAS,GAAGtB,IAAI,CAACH,QAAQ,GAAG,GAAG,CAChC;UACDO,QAAQ,CAACM,IAAI,CAACG,4BAA4B,CACxC,IAAI,EACJS,SAAS,GAAGtB,IAAI,CAACH,QAAQ,CAC1B;UAEDK,UAAU,CAACY,KAAK,CAACQ,SAAS,CAAC;UAC3BpB,UAAU,CAACa,IAAI,CAACO,SAAS,GAAGtB,IAAI,CAACH,QAAQ,CAAC;QAC5C,CAAC,EAAEG,IAAI,CAACqB,KAAK,GAAG,IAAI,CAAC;MACvB,CAAC,CAAC;MAEF,OAAO7B,OAAO,CAACC,OAAO,EAAE;IAC1B,CAAC;IAED,OAAON,KAAK;EACd;EAEA;;;EAGQT,SAASA,CAAC6C,IAAY,EAAEC,IAAY;IAC1C,IAAI;MACF,MAAMrC,KAAK,GAAG,IAAIC,KAAK,EAAE;MAEzB;MACAD,KAAK,CAACsC,OAAO,GAAG,MAAM;MACtBtC,KAAK,CAACE,MAAM,GAAG,GAAG;MAElB;MACAF,KAAK,CAACuC,gBAAgB,CAAC,gBAAgB,EAAE,MAAK;QAC5CpF,OAAO,CAACC,GAAG,CACT,yBAAyBgF,IAAI,6BAA6BC,IAAI,EAAE,CACjE;MACH,CAAC,CAAC;MAEFrC,KAAK,CAACuC,gBAAgB,CAAC,OAAO,EAAGzD,CAAC,IAAI;QACpC3B,OAAO,CAACO,KAAK,CACX,uCAAuC0E,IAAI,SAASC,IAAI,GAAG,EAC3DvD,CAAC,CACF;QACD3B,OAAO,CAACC,GAAG,CACT,mCAAmCgF,IAAI,6BAA6B,CACrE;QAED;QACA,MAAMI,OAAO,GAAGH,IAAI,CAACI,UAAU,CAAC,GAAG,CAAC,GAAGJ,IAAI,CAACK,SAAS,CAAC,CAAC,CAAC,GAAGL,IAAI;QAC/D,IAAIG,OAAO,KAAKH,IAAI,EAAE;UACpBvF,UAAU,CAAC,MAAK;YACdkD,KAAK,CAAC2C,GAAG,GAAGH,OAAO;YACnBxC,KAAK,CAAC4C,IAAI,EAAE;UACd,CAAC,EAAE,GAAG,CAAC;;MAEX,CAAC,CAAC;MAEF5C,KAAK,CAACuC,gBAAgB,CAAC,OAAO,EAAE,MAAK;QACnC,IAAI,CAACtG,SAAS,CAACmG,IAAI,CAAC,GAAG,KAAK;QAC5BjF,OAAO,CAACC,GAAG,CAAC,0BAA0BgF,IAAI,QAAQ,CAAC;MACrD,CAAC,CAAC;MAEF;MACApC,KAAK,CAAC2C,GAAG,GAAGN,IAAI;MAChBrC,KAAK,CAAC4C,IAAI,EAAE;MAEZ,IAAI,CAAC5G,MAAM,CAACoG,IAAI,CAAC,GAAGpC,KAAK;MACzB,IAAI,CAAC/D,SAAS,CAACmG,IAAI,CAAC,GAAG,KAAK;MAE5BjF,OAAO,CAACC,GAAG,CAAC,kCAAkCgF,IAAI,SAASC,IAAI,EAAE,CAAC;KACnE,CAAC,OAAO3E,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CACX,oDAAoD0E,IAAI,GAAG,EAC3D1E,KAAK,CACN;;EAEL;EAEA;;;EAGQkB,IAAIA,CAACwD,IAAY,EAAES,IAAA,GAAgB,KAAK;IAC9C,IAAI,IAAI,CAAC3G,KAAK,EAAE;MACdiB,OAAO,CAACC,GAAG,CAAC,0BAA0BgF,IAAI,QAAQ,CAAC;MACnD;;IAGF,IAAI;MACF;MACA,IAAIU,KAAK;MACT,IACEV,IAAI,KAAK,UAAU,IACnBA,IAAI,KAAK,gBAAgB,IACzBA,IAAI,KAAK,UAAU,EACnB;QACA,MAAMW,aAAa,GAAG,GAAGX,IAAI,YAAY;QACzCU,KAAK,GAAG,IAAI,CAAC9G,MAAM,CAAC+G,aAAa,CAAC;QAClC,IAAID,KAAK,EAAE;UACT3F,OAAO,CAACC,GAAG,CACT,yDAAyDgF,IAAI,EAAE,CAChE;;OAEJ,MAAM;QACL;QACAU,KAAK,GAAG,IAAI,CAAC9G,MAAM,CAACoG,IAAI,CAAC;QACzB,IAAI,CAACU,KAAK,IAAIA,KAAK,CAACpF,KAAK,EAAE;UACzB,MAAMqF,aAAa,GAAG,GAAGX,IAAI,YAAY;UACzCU,KAAK,GAAG,IAAI,CAAC9G,MAAM,CAAC+G,aAAa,CAAC;UAClC,IAAID,KAAK,EAAE;YACT3F,OAAO,CAACC,GAAG,CAAC,8CAA8CgF,IAAI,EAAE,CAAC;;;;MAKvE,IAAI,CAACU,KAAK,EAAE;QACV3F,OAAO,CAACwB,IAAI,CACV,0BAA0ByD,IAAI,6CAA6C,CAC5E;QACD;QACA,IAAI,CAACY,cAAc,CACjBZ,IAAI,KAAK,UAAU,GAAG,GAAG,GAAGA,IAAI,KAAK,gBAAgB,GAAG,IAAI,GAAG,GAAG,CACnE;QACD;;MAGFU,KAAK,CAACD,IAAI,GAAGA,IAAI;MACjBC,KAAK,CAAC5C,MAAM,GAAG,GAAG;MAElB,IAAI,CAAC,IAAI,CAACjE,SAAS,CAACmG,IAAI,CAAC,EAAE;QACzBjF,OAAO,CAACC,GAAG,CAAC,mCAAmCgF,IAAI,WAAWS,IAAI,GAAG,CAAC;QAEtE;QACA,IAAKC,KAAa,CAAC1C,aAAa,EAAE;UAC/B0C,KAAa,CACX1C,aAAa,EAAE,CACf6C,IAAI,CAAC,MAAK;YACT9F,OAAO,CAACC,GAAG,CACT,mCAAmCgF,IAAI,uBAAuB,CAC/D;YACD,IAAI,CAACnG,SAAS,CAACmG,IAAI,CAAC,GAAG,IAAI;YAE3B;YACA,IAAIA,IAAI,KAAK,UAAU,IAAI,CAACS,IAAI,EAAE;cAChC;cACA/F,UAAU,CAAC,MAAK;gBACd,IAAI,CAACb,SAAS,CAACmG,IAAI,CAAC,GAAG,KAAK;cAC9B,CAAC,EAAE,IAAI,CAAC;aACT,MAAM,IAAIA,IAAI,KAAK,UAAU,EAAE;cAC9B;cACAtF,UAAU,CACR,MAAK;gBACH,IAAI,CAACb,SAAS,CAACmG,IAAI,CAAC,GAAG,KAAK;cAC9B,CAAC,EACDA,IAAI,KAAK,gBAAgB,GAAG,IAAI,GAAG,IAAI,CACxC;;UAEL,CAAC,CAAC,CACDvD,KAAK,CAAEnB,KAAU,IAAI;YACpBP,OAAO,CAACO,KAAK,CACX,iDAAiD0E,IAAI,GAAG,EACxD1E,KAAK,CACN;UACH,CAAC,CAAC;SACL,MAAM;UACL;UACAoF,KAAK,CAACnC,WAAW,GAAG,CAAC;UACrBmC,KAAK,CACFlE,IAAI,EAAE,CACNqE,IAAI,CAAC,MAAK;YACT9F,OAAO,CAACC,GAAG,CACT,yBAAyBgF,IAAI,uBAAuB,CACrD;YACD,IAAI,CAACnG,SAAS,CAACmG,IAAI,CAAC,GAAG,IAAI;UAC7B,CAAC,CAAC,CACDvD,KAAK,CAAEnB,KAAK,IAAI;YACfP,OAAO,CAACO,KAAK,CACX,uCAAuC0E,IAAI,GAAG,EAC9C1E,KAAK,CACN;YAED;YACA,MAAMqF,aAAa,GAAG,GAAGX,IAAI,YAAY;YACzC,MAAMc,cAAc,GAAG,IAAI,CAAClH,MAAM,CAAC+G,aAAa,CAAC;YACjD,IAAIG,cAAc,IAAKA,cAAsB,CAAC9C,aAAa,EAAE;cAC3DjD,OAAO,CAACC,GAAG,CACT,wDAAwDgF,IAAI,EAAE,CAC/D;cACD,IAAI,CAACxD,IAAI,CAACwD,IAAI,EAAES,IAAI,CAAC;aACtB,MAAM;cACL;cACA,IAAI,CAACG,cAAc,CACjBZ,IAAI,KAAK,UAAU,GACf,GAAG,GACHA,IAAI,KAAK,gBAAgB,GACzB,IAAI,GACJ,GAAG,CACR;;UAEL,CAAC,CAAC;;OAEP,MAAM;QACLjF,OAAO,CAACC,GAAG,CAAC,0BAA0BgF,IAAI,kBAAkB,CAAC;;KAEhE,CAAC,OAAO1E,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CACX,4CAA4C0E,IAAI,GAAG,EACnD1E,KAAK,CACN;MACD;MACA,IAAI,CAACsF,cAAc,CACjBZ,IAAI,KAAK,UAAU,GAAG,GAAG,GAAGA,IAAI,KAAK,gBAAgB,GAAG,IAAI,GAAG,GAAG,CACnE;;EAEL;EAEA;;;EAGQY,cAAcA,CAAC3B,SAAiB;IACtC,IAAI;MACF,MAAM7B,YAAY,GAAG,KAAKC,MAAM,CAACC,YAAY,IAC1CD,MAAc,CAACE,kBAAkB,EAAC,CAAE;MACvC,MAAMoB,UAAU,GAAGvB,YAAY,CAACwB,gBAAgB,EAAE;MAClD,MAAMC,QAAQ,GAAGzB,YAAY,CAAC0B,UAAU,EAAE;MAE1CH,UAAU,CAACI,OAAO,CAACF,QAAQ,CAAC;MAC5BA,QAAQ,CAACE,OAAO,CAAC3B,YAAY,CAAC4B,WAAW,CAAC;MAE1CL,UAAU,CAACM,SAAS,CAACnE,KAAK,GAAGmE,SAAS;MACtCN,UAAU,CAACO,IAAI,GAAG,MAAM;MAExBL,QAAQ,CAACM,IAAI,CAACC,cAAc,CAAC,GAAG,EAAEhC,YAAY,CAACmB,WAAW,CAAC;MAC3DM,QAAQ,CAACM,IAAI,CAACG,4BAA4B,CACxC,IAAI,EACJlC,YAAY,CAACmB,WAAW,GAAG,GAAG,CAC/B;MAEDI,UAAU,CAACY,KAAK,CAACnC,YAAY,CAACmB,WAAW,CAAC;MAC1CI,UAAU,CAACa,IAAI,CAACpC,YAAY,CAACmB,WAAW,GAAG,GAAG,CAAC;MAE/CxD,OAAO,CAACC,GAAG,CAAC,2CAA2CiE,SAAS,IAAI,CAAC;KACtE,CAAC,OAAO3D,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CAAC,6CAA6C,EAAEA,KAAK,CAAC;;EAEvE;EAEA;;;EAGQkE,IAAIA,CAACQ,IAAY;IACvB,IAAI;MACF,IAAIU,KAAK,GAAG,IAAI,CAAC9G,MAAM,CAACoG,IAAI,CAAC;MAE7B;MACA,IAAI,CAACU,KAAK,EAAE;QACVA,KAAK,GAAG,IAAI,CAAC9G,MAAM,CAAC,GAAGoG,IAAI,YAAY,CAAC;;MAG1C,IAAI,CAACU,KAAK,EAAE;QACV3F,OAAO,CAACwB,IAAI,CAAC,0BAA0ByD,IAAI,yBAAyB,CAAC;QACrE;;MAGF,IAAI,IAAI,CAACnG,SAAS,CAACmG,IAAI,CAAC,EAAE;QACxBjF,OAAO,CAACC,GAAG,CAAC,oCAAoCgF,IAAI,EAAE,CAAC;QAEvD;QACA,IAAKU,KAAa,CAACf,aAAa,EAAE;UAC/Be,KAAa,CAACf,aAAa,EAAE;SAC/B,MAAM;UACLe,KAAK,CAACK,KAAK,EAAE;UACbL,KAAK,CAACnC,WAAW,GAAG,CAAC;;QAGvB,IAAI,CAAC1E,SAAS,CAACmG,IAAI,CAAC,GAAG,KAAK;OAC7B,MAAM;QACLjF,OAAO,CAACC,GAAG,CAAC,0BAA0BgF,IAAI,kBAAkB,CAAC;;KAEhE,CAAC,OAAO1E,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CAAC,wCAAwC0E,IAAI,GAAG,EAAE1E,KAAK,CAAC;;EAEzE;EAEA;;;EAGQX,wBAAwBA,CAAA;IAC9B;IAEA,IAAI;MACF,IAAI,CAACtB,MAAM,CACR2H,SAAS,CAAiC;QACzCC,KAAK,EAAEjI,0BAA0B;QACjCkI,WAAW,EAAE,KAAK,CAAE;OACrB,CAAC,CACDF,SAAS,CAAC;QACTG,IAAI,EAAEA,CAAC;UAAEC,IAAI;UAAEC;QAAM,CAAE,KAAI;UACzB,IAAIA,MAAM,EAAE;YACVtG,OAAO,CAACwB,IAAI,CACV,kDAAkD,EAClD8E,MAAM,CACP;;UAGH,IAAID,IAAI,EAAE5H,YAAY,EAAE;YACtB;YACA,IAAI,CAAC8H,kBAAkB,CAACF,IAAI,CAAC5H,YAAY,CAAC;;QAE9C,CAAC;QACD8B,KAAK,EAAGA,KAAK,IAAI;UACfP,OAAO,CAACO,KAAK,CACX,sDAAsD,EACtDA,KAAK,CACN;UAED;UACAZ,UAAU,CAAC,MAAK;YACd,IAAI,CAACC,wBAAwB,EAAE;UACjC,CAAC,EAAE,KAAK,CAAC;QACX,CAAC;QACD4G,QAAQ,EAAEA,CAAA,KAAK;UACb;UACA;UACA7G,UAAU,CAAC,MAAK;YACd,IAAI,CAACC,wBAAwB,EAAE;UACjC,CAAC,EAAE,IAAI,CAAC;QACV;OACD,CAAC;KACL,CAAC,OAAOW,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CAAC,gDAAgD,EAAEA,KAAK,CAAC;MACtE;MACAZ,UAAU,CAAC,MAAK;QACd,IAAI,CAACC,wBAAwB,EAAE;MACjC,CAAC,EAAE,KAAK,CAAC;;EAEb;EAEA;;;EAGO6G,wBAAwBA,CAAA;IAC7B;IACA,IAAI,CAAC7G,wBAAwB,EAAE;IAC/B,IAAI,CAACC,4BAA4B,EAAE;IACnC,IAAI,CAACC,sBAAsB,EAAE;EAC/B;EAEA;;;EAGQA,sBAAsBA,CAAA;IAC5BE,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC;IAEtE,IAAI;MACF,IAAI,CAAC3B,MAAM,CACR2H,SAAS,CAAsB;QAC9BC,KAAK,EAAE/H,wBAAwB;QAC/BuI,SAAS,EAAE;UACTC,MAAM,EAAE,IAAI,CAACpH,aAAa,IAAI;SAC/B;QACD4G,WAAW,EAAE;OACd,CAAC,CACDF,SAAS,CAAC;QACTG,IAAI,EAAEA,CAAC;UAAEC,IAAI;UAAEC;QAAM,CAAE,KAAI;UACzB,IAAIA,MAAM,EAAE;YACVtG,OAAO,CAACwB,IAAI,CACV,mDAAmD,EACnD8E,MAAM,CACP;;UAGH,IAAID,IAAI,EAAEO,UAAU,EAAE;YACpB5G,OAAO,CAACC,GAAG,CACT,wCAAwC,EACxCoG,IAAI,CAACO,UAAU,CAChB;YACD,IAAI,CAACC,gBAAgB,CAACR,IAAI,CAACO,UAAU,CAAC;;QAE1C,CAAC;QACDrG,KAAK,EAAGA,KAAK,IAAI;UACfP,OAAO,CAACO,KAAK,CACX,oDAAoD,EACpDA,KAAK,CACN;UACD;UACAZ,UAAU,CAAC,MAAK;YACd,IAAI,CAACG,sBAAsB,EAAE;UAC/B,CAAC,EAAE,IAAI,CAAC;QACV,CAAC;QACD0G,QAAQ,EAAEA,CAAA,KAAK;UACbxG,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;UAClE;UACAN,UAAU,CAAC,MAAK;YACd,IAAI,CAACG,sBAAsB,EAAE;UAC/B,CAAC,EAAE,IAAI,CAAC;QACV;OACD,CAAC;KACL,CAAC,OAAOS,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CACX,4DAA4D,EAC5DA,KAAK,CACN;MACDZ,UAAU,CAAC,MAAK;QACd,IAAI,CAACG,sBAAsB,EAAE;MAC/B,CAAC,EAAE,IAAI,CAAC;;EAEZ;EAEA;;;EAGc+G,gBAAgBA,CAACC,MAAW;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACxChH,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAE6G,MAAM,CAAC3C,IAAI,CAAC;MAElE,IAAI;QACF,QAAQ2C,MAAM,CAAC3C,IAAI;UACjB,KAAK,OAAO;YACV,MAAM4C,KAAI,CAACE,iBAAiB,CAACH,MAAM,CAAC;YACpC;UACF,KAAK,QAAQ;YACX,MAAMC,KAAI,CAACG,kBAAkB,CAACJ,MAAM,CAAC;YACrC;UACF,KAAK,eAAe;YAClB,MAAMC,KAAI,CAACI,wBAAwB,CAACL,MAAM,CAAC;YAC3C;UACF;YACE9G,OAAO,CAACwB,IAAI,CAAC,uCAAuC,EAAEsF,MAAM,CAAC3C,IAAI,CAAC;;OAEvE,CAAC,OAAO5D,KAAK,EAAE;QACdP,OAAO,CAACO,KAAK,CAAC,6CAA6C,EAAEA,KAAK,CAAC;;IACpE;EACH;EAEA;;;EAGc0G,iBAAiBA,CAACH,MAAW;IAAA,IAAAM,MAAA;IAAA,OAAAJ,iBAAA;MACzChH,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;MAErD,IAAI,CAACmH,MAAI,CAAClI,cAAc,EAAE;QACxBc,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;QACpEmH,MAAI,CAAC1H,gBAAgB,EAAE;;MAGzB,MAAM2H,KAAK,GAAGtF,IAAI,CAACuF,KAAK,CAACR,MAAM,CAACT,IAAI,CAAC;MACrC,MAAMe,MAAI,CAAClI,cAAe,CAACqI,oBAAoB,CAACF,KAAK,CAAC;MACtDrH,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;IAAC;EAC/D;EAEA;;;EAGciH,kBAAkBA,CAACJ,MAAW;IAAA,IAAAU,MAAA;IAAA,OAAAR,iBAAA;MAC1ChH,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;MAEtD,IAAI,CAACuH,MAAI,CAACtI,cAAc,EAAE;QACxBc,OAAO,CAACO,KAAK,CAAC,sDAAsD,CAAC;QACrE;;MAGF,MAAMkH,MAAM,GAAG1F,IAAI,CAACuF,KAAK,CAACR,MAAM,CAACT,IAAI,CAAC;MACtC,MAAMmB,MAAI,CAACtI,cAAc,CAACqI,oBAAoB,CAACE,MAAM,CAAC;MACtDzH,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;IAAC;EAChE;EAEA;;;EAGckH,wBAAwBA,CAACL,MAAW;IAAA,IAAAY,MAAA;IAAA,OAAAV,iBAAA;MAChDhH,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;MAE7D,IAAI,CAACyH,MAAI,CAACxI,cAAc,EAAE;QACxBc,OAAO,CAACO,KAAK,CAAC,sDAAsD,CAAC;QACrE;;MAGF,MAAMsB,SAAS,GAAGE,IAAI,CAACuF,KAAK,CAACR,MAAM,CAACT,IAAI,CAAC;MACzC,MAAMqB,MAAI,CAACxI,cAAc,CAACyI,eAAe,CAAC9F,SAAS,CAAC;MACpD7B,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC;IAAC;EACzE;EAEA;;;EAGQ6B,UAAUA,CAACqC,IAAY,EAAEkC,IAAY;IAC3C,IAAI,CAAC,IAAI,CAAC9G,aAAa,EAAE;MACvBS,OAAO,CAACwB,IAAI,CAAC,wDAAwD,CAAC;MACtE;;IAGFxB,OAAO,CAACC,GAAG,CAAC,oCAAoCkE,IAAI,EAAE,CAAC;IAEvD;IACA;IACA,IAAI,CAACyD,sBAAsB,CAACzD,IAAI,EAAEkC,IAAI,CAAC;EACzC;EAEA;;;EAGQuB,sBAAsBA,CAACzD,IAAY,EAAEkC,IAAY;IACvDrG,OAAO,CAACC,GAAG,CAAC,gDAAgDkE,IAAI,EAAE,CAAC;IAEnE;IACAxE,UAAU,CAAC,MAAK;MACd,MAAMmH,MAAM,GAAG;QACbH,MAAM,EAAE,IAAI,CAACpH,aAAa;QAC1BsI,QAAQ,EAAE,YAAY;QACtB1D,IAAI,EAAEA,IAAI;QACVkC,IAAI,EAAEA;OACP;MAEDrG,OAAO,CAACC,GAAG,CAAC,6CAA6C,EAAE6G,MAAM,CAAC;MAClE,IAAI,CAACD,gBAAgB,CAACC,MAAM,CAAC;IAC/B,CAAC,EAAE,GAAG,CAAC;EACT;EAEA;;;EAGOgB,UAAUA,CAAA;IACf9H,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;IAEjD;IACAD,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;IACrC,IAAI,CAACwB,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC;IAE3B9B,UAAU,CAAC,MAAK;MACd,IAAI,CAAC8E,IAAI,CAAC,UAAU,CAAC;MACrBzE,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;MAC3C,IAAI,CAACwB,IAAI,CAAC,gBAAgB,CAAC;IAC7B,CAAC,EAAE,IAAI,CAAC;IAER9B,UAAU,CAAC,MAAK;MACdK,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;MACrC,IAAI,CAACwB,IAAI,CAAC,UAAU,CAAC;IACvB,CAAC,EAAE,IAAI,CAAC;EACV;EAEA;;;EAGOsG,YAAYA,CAAA;IACjB/H,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC;IAChED,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAE+H,MAAM,CAACC,IAAI,CAAC,IAAI,CAACpJ,MAAM,CAAC,CAAC;IAE3E;IACA,MAAMqJ,QAAQ,GAAG,IAAI,CAACrJ,MAAM,CAAC,UAAU,CAAC;IACxC,MAAMsJ,iBAAiB,GAAG,IAAI,CAACtJ,MAAM,CAAC,oBAAoB,CAAC;IAE3D,IAAIqJ,QAAQ,EAAE;MACZlI,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAE;QACjDuF,GAAG,EAAE0C,QAAQ,CAAC1C,GAAG;QACjBtE,UAAU,EAAEgH,QAAQ,CAAChH,UAAU;QAC/BX,KAAK,EAAE2H,QAAQ,CAAC3H,KAAK;QACrBgD,QAAQ,EAAE2E,QAAQ,CAAC3E;OACpB,CAAC;KACH,MAAM;MACLvD,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;;IAGvD,IAAIkI,iBAAiB,EAAE;MACrBnI,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;;IAGzD;IACAD,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;IACpE,IAAI,CAACwB,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC;IAE3B;IACA9B,UAAU,CAAC,MAAK;MACd,IAAI,CAAC8E,IAAI,CAAC,UAAU,CAAC;MACrBzE,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;IACvD,CAAC,EAAE,IAAI,CAAC;EACV;EAEA;;;EAGOmI,mBAAmBA,CAAA;IACxBpI,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;IAE/D;IACA;IACA,IAAI,CAACwB,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC;IAE3B9B,UAAU,CAAC,MAAK;MACd,IAAI,CAAC8E,IAAI,CAAC,UAAU,CAAC;MACrB;MACA,IAAI,CAAChD,IAAI,CAAC,gBAAgB,CAAC;IAC7B,CAAC,EAAE,IAAI,CAAC;IAER9B,UAAU,CAAC,MAAK;MACd;MACA,IAAI,CAAC8B,IAAI,CAAC,UAAU,CAAC;IACvB,CAAC,EAAE,IAAI,CAAC;IAER9B,UAAU,CAAC,MAAK;MACd;MACA,IAAI,CAAC8B,IAAI,CAAC,cAAc,CAAC;IAC3B,CAAC,EAAE,IAAI,CAAC;IAER9B,UAAU,CAAC,MAAK;MACdK,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;IAC9C,CAAC,EAAE,KAAK,CAAC;EACX;EAEA;;;EAGOoI,gBAAgBA,CAAA;IACrBrI,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;IAC7D,IAAI,CAACwB,IAAI,CAAC,cAAc,CAAC;EAC3B;EAEA;;;EAGQ5B,4BAA4BA,CAAA;IAClCG,OAAO,CAACC,GAAG,CACT,gEAAgE,CACjE;IAED,IAAI;MACF,IAAI,CAAC3B,MAAM,CACR2H,SAAS,CAA8B;QACtCC,KAAK,EAAEhI,gCAAgC;QACvCiI,WAAW,EAAE;OACd,CAAC,CACDF,SAAS,CAAC;QACTG,IAAI,EAAEA,CAAC;UAAEC,IAAI;UAAEC;QAAM,CAAE,KAAI;UACzB,IAAIA,MAAM,EAAE;YACVtG,OAAO,CAACwB,IAAI,CACV,8DAA8D,EAC9D8E,MAAM,CACP;;UAGH,IAAID,IAAI,EAAEiC,iBAAiB,EAAE;YAC3BtI,OAAO,CAACC,GAAG,CACT,uCAAuC,EACvCoG,IAAI,CAACiC,iBAAiB,CACvB;YACD,IAAI,CAACC,sBAAsB,CAAClC,IAAI,CAACiC,iBAAiB,CAAC;;QAEvD,CAAC;QACD/H,KAAK,EAAGA,KAAK,IAAI;UACfP,OAAO,CAACO,KAAK,CACX,oDAAoD,EACpDA,KAAK,CACN;UACD;UACAZ,UAAU,CAAC,MAAK;YACd,IAAI,CAACE,4BAA4B,EAAE;UACrC,CAAC,EAAE,IAAI,CAAC;QACV,CAAC;QACD2G,QAAQ,EAAEA,CAAA,KAAK;UACbxG,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;UAClE;UACAN,UAAU,CAAC,MAAK;YACd,IAAI,CAACE,4BAA4B,EAAE;UACrC,CAAC,EAAE,IAAI,CAAC;QACV;OACD,CAAC;KACL,CAAC,OAAOU,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CACX,4DAA4D,EAC5DA,KAAK,CACN;MACDZ,UAAU,CAAC,MAAK;QACd,IAAI,CAACE,4BAA4B,EAAE;MACrC,CAAC,EAAE,IAAI,CAAC;;EAEZ;EAEA;;;EAGQ0G,kBAAkBA,CAACiC,IAAkB;IAC3CxI,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAE;MACtD0G,MAAM,EAAE6B,IAAI,CAAC5H,EAAE;MACf6H,QAAQ,EAAED,IAAI,CAACrE,IAAI;MACnBuE,MAAM,EAAEF,IAAI,CAACE,MAAM,EAAEC,QAAQ;MAC7BC,cAAc,EAAEJ,IAAI,CAACI;KACtB,CAAC;IAEF,IAAI,CAACnK,YAAY,CAAC2H,IAAI,CAACoC,IAAI,CAAC;IAC5B,IAAI,CAAC/G,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC;IAE3BzB,OAAO,CAACC,GAAG,CACT,iEAAiE,CAClE;EACH;EAEA;;;EAGQsI,sBAAsBA,CAACC,IAAU;IACvCxI,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEuI,IAAI,CAACK,MAAM,CAAC;IAEjE,QAAQL,IAAI,CAACK,MAAM;MACjB,KAAKlL,UAAU,CAACmL,QAAQ;QACtB9I,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;QAChD,IAAI,CAACwE,IAAI,CAAC,UAAU,CAAC;QACrB,IAAI,CAAChD,IAAI,CAAC,UAAU,CAAC;QACrB,IAAI,CAACjD,UAAU,CAAC4H,IAAI,CAAC,IAAI,CAAC;QAC1B,IAAI,CAAC3H,YAAY,CAAC2H,IAAI,CAAC,IAAI,CAAC;QAC5B;MAEF,KAAKzI,UAAU,CAACoL,KAAK;QACnB/I,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;QAC1C,IAAI,CAAC+I,aAAa,EAAE;QACpB,IAAI,CAACvH,IAAI,CAAC,UAAU,CAAC;QACrB,IAAI,CAAClC,aAAa,GAAG,IAAI,CAAC,CAAC;QAC3B,IAAI,CAACf,UAAU,CAAC4H,IAAI,CAAC,IAAI,CAAC;QAC1B,IAAI,CAAC3H,YAAY,CAAC2H,IAAI,CAAC,IAAI,CAAC;QAC5B;MAEF,KAAKzI,UAAU,CAACsL,SAAS;QACvBjJ,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;QAC7C,IAAI,CAACwE,IAAI,CAAC,UAAU,CAAC;QACrB,IAAI,CAAChD,IAAI,CAAC,gBAAgB,CAAC;QAC3B,IAAI,CAAClC,aAAa,GAAGiJ,IAAI,CAAC5H,EAAE,CAAC,CAAC;QAC9B,IAAI,CAACpC,UAAU,CAAC4H,IAAI,CAACoC,IAAI,CAAC;QAC1B,IAAI,CAAC/J,YAAY,CAAC2H,IAAI,CAAC,IAAI,CAAC;QAC5B;MAEF,KAAKzI,UAAU,CAACuL,OAAO;QACrBlJ,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;QAC/C,IAAI,CAACwB,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC;QAC3B;MAEF;QACEzB,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEuI,IAAI,CAACK,MAAM,CAAC;QACjE;;EAEN;EAEA;;;EAGcM,YAAYA,CAACV,QAAkB;IAAA,IAAAW,MAAA;IAAA,OAAApC,iBAAA;MAC3ChH,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAEwI,QAAQ,CAAC;MAEjE,MAAMY,WAAW,GAA2B;QAC1CxG,KAAK,EAAE,IAAI;QACXyG,KAAK,EAAEb,QAAQ,KAAK/K,QAAQ,CAAC6L;OAC9B;MAED,IAAI;QACF,MAAMC,MAAM,SAASC,SAAS,CAACC,YAAY,CAACP,YAAY,CAACE,WAAW,CAAC;QACrErJ,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;QAClDD,OAAO,CAACC,GAAG,CACT,iCAAiC,EACjCuJ,MAAM,CAAC1I,SAAS,EAAE,CAACvD,GAAG,CAAEwD,CAAC,KAAM;UAC7BC,IAAI,EAAED,CAAC,CAACC,IAAI;UACZC,OAAO,EAAEF,CAAC,CAACE,OAAO;UAClBC,UAAU,EAAEH,CAAC,CAACG;SACf,CAAC,CAAC,CACJ;QACDkI,MAAI,CAACjK,WAAW,GAAGqK,MAAM;QAEzB;QACA,IAAIJ,MAAI,CAAC/J,iBAAiB,IAAIoJ,QAAQ,KAAK/K,QAAQ,CAAC6L,KAAK,EAAE;UACzDH,MAAI,CAAC/J,iBAAiB,CAAC8B,SAAS,GAAGqI,MAAM;;QAG3C,OAAOA,MAAM;OACd,CAAC,OAAOjJ,KAAK,EAAE;QACdP,OAAO,CAACO,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;QACjE,MAAM,IAAIoJ,KAAK,CAAC,6CAA6C,CAAC;;IAC/D;EACH;EAEA;;;EAGQC,8BAA8BA,CAACJ,MAAmB;IACxD,IAAI,CAAC,IAAI,CAACtK,cAAc,EAAE;MACxBc,OAAO,CAACO,KAAK,CAAC,8CAA8C,CAAC;MAC7D;;IAGFP,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC;IACtEuJ,MAAM,CAAC1I,SAAS,EAAE,CAAC2C,OAAO,CAAEoG,KAAK,IAAI;MACnC,IAAI,CAAC3K,cAAe,CAAC4K,QAAQ,CAACD,KAAK,EAAEL,MAAM,CAAC;IAC9C,CAAC,CAAC;EACJ;EAEA;;;EAGcO,WAAWA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAhD,iBAAA;MACvB,IAAI,CAACgD,MAAI,CAAC9K,cAAc,EAAE;QACxB,MAAM,IAAIyK,KAAK,CAAC,8BAA8B,CAAC;;MAGjD3J,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;MACrD,MAAMoH,KAAK,SAAS2C,MAAI,CAAC9K,cAAc,CAAC6K,WAAW,EAAE;MACrD,MAAMC,MAAI,CAAC9K,cAAc,CAAC+K,mBAAmB,CAAC5C,KAAK,CAAC;MACpD,OAAOA,KAAK;IAAC;EACf;EAEA;;;EAGc6C,YAAYA,CACxB7C,KAAgC;IAAA,IAAA8C,MAAA;IAAA,OAAAnD,iBAAA;MAEhC,IAAI,CAACmD,MAAI,CAACjL,cAAc,EAAE;QACxB,MAAM,IAAIyK,KAAK,CAAC,8BAA8B,CAAC;;MAGjD3J,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;MACtD,MAAMkK,MAAI,CAACjL,cAAc,CAACqI,oBAAoB,CAACF,KAAK,CAAC;MACrD,MAAMI,MAAM,SAAS0C,MAAI,CAACjL,cAAc,CAACgL,YAAY,EAAE;MACvD,MAAMC,MAAI,CAACjL,cAAc,CAAC+K,mBAAmB,CAACxC,MAAM,CAAC;MACrD,OAAOA,MAAM;IAAC;EAChB;EAEA;;;EAGO2C,gBAAgBA,CACrBC,UAA4B,EAC5BC,WAA6B;IAE7BtK,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;IACtD,IAAI,CAACZ,iBAAiB,GAAGgL,UAAU;IACnC,IAAI,CAAC/K,kBAAkB,GAAGgL,WAAW;IAErC;IACA,IAAI;MACF,IAAI,CAACC,kBAAkB,CAACF,UAAU,EAAEC,WAAW,CAAC;KACjD,CAAC,OAAO/J,KAAK,EAAE;MACdP,OAAO,CAACwB,IAAI,CAAC,4CAA4C,EAAEjB,KAAK,CAAC;;IAGnE;IACA,IAAI,IAAI,CAACpB,WAAW,IAAIkL,UAAU,EAAE;MAClCrK,OAAO,CAACC,GAAG,CAAC,0DAA0D,CAAC;MACvEoK,UAAU,CAAClJ,SAAS,GAAG,IAAI,CAAChC,WAAW;MACvCkL,UAAU,CAACtL,KAAK,GAAG,IAAI,CAAC,CAAC;MACzBsL,UAAU,CAACtH,MAAM,GAAG,CAAC;MACrBsH,UAAU,CAACG,QAAQ,GAAG,IAAI;MAC1B,IAAI;QACFH,UAAU,CAAC5I,IAAI,EAAE;QACjBzB,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;OACnD,CAAC,OAAOM,KAAK,EAAE;QACdP,OAAO,CAACwB,IAAI,CAAC,0CAA0C,EAAEjB,KAAK,CAAC;;KAElE,MAAM;MACLP,OAAO,CAACwB,IAAI,CACV,6DAA6D,CAC9D;;IAGH,IAAI,IAAI,CAACpC,YAAY,IAAIkL,WAAW,EAAE;MACpCtK,OAAO,CAACC,GAAG,CAAC,2DAA2D,CAAC;MACxEqK,WAAW,CAACnJ,SAAS,GAAG,IAAI,CAAC/B,YAAY;MACzCkL,WAAW,CAACvL,KAAK,GAAG,KAAK,CAAC,CAAC;MAC3BuL,WAAW,CAACvH,MAAM,GAAG,CAAC;MACtBuH,WAAW,CAACE,QAAQ,GAAG,IAAI;MAC3B,IAAI;QACFF,WAAW,CAAC7I,IAAI,EAAE;QAClBzB,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;OACpD,CAAC,OAAOM,KAAK,EAAE;QACdP,OAAO,CAACwB,IAAI,CAAC,2CAA2C,EAAEjB,KAAK,CAAC;;KAEnE,MAAM;MACLP,OAAO,CAACwB,IAAI,CACV,8DAA8D,CAC/D;;IAGH;IACA7B,UAAU,CAAC,MAAK;MACd,IAAI,CAAC8K,qBAAqB,EAAE;IAC9B,CAAC,EAAE,IAAI,CAAC;EACV;EAEA;;;EAGQA,qBAAqBA,CAAA;IAC3BzK,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;IAE5D;IACA,IAAI,IAAI,CAACd,WAAW,IAAI,IAAI,CAACE,iBAAiB,EAAE;MAC9C,IAAI,CAAC,IAAI,CAACA,iBAAiB,CAAC8B,SAAS,EAAE;QACrCnB,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;QAC5D,IAAI,CAACZ,iBAAiB,CAAC8B,SAAS,GAAG,IAAI,CAAChC,WAAW;QACnD,IAAI,CAACE,iBAAiB,CAACN,KAAK,GAAG,IAAI;QACnC,IAAI,CAACM,iBAAiB,CAACmL,QAAQ,GAAG,IAAI;QACtC,IAAI,CAACnL,iBAAiB,CACnBoC,IAAI,EAAE,CACNC,KAAK,CAAEC,CAAC,IACP3B,OAAO,CAACwB,IAAI,CAAC,2CAA2C,EAAEG,CAAC,CAAC,CAC7D;;;IAIP;IACA,IAAI,IAAI,CAACvC,YAAY,IAAI,IAAI,CAACE,kBAAkB,EAAE;MAChD,IAAI,CAAC,IAAI,CAACA,kBAAkB,CAAC6B,SAAS,EAAE;QACtCnB,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;QAC7D,IAAI,CAACX,kBAAkB,CAAC6B,SAAS,GAAG,IAAI,CAAC/B,YAAY;QACrD,IAAI,CAACE,kBAAkB,CAACP,KAAK,GAAG,KAAK;QACrC,IAAI,CAACO,kBAAkB,CAACyD,MAAM,GAAG,CAAC;QAClC,IAAI,CAACzD,kBAAkB,CAACkL,QAAQ,GAAG,IAAI;QACvC,IAAI,CAAClL,kBAAkB,CACpBmC,IAAI,EAAE,CACNC,KAAK,CAAEC,CAAC,IACP3B,OAAO,CAACwB,IAAI,CAAC,4CAA4C,EAAEG,CAAC,CAAC,CAC9D;;;IAIP;IACA3B,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAE;MACxDyK,aAAa,EAAE,CAAC,CAAC,IAAI,CAACrL,iBAAiB,EAAE8B,SAAS;MAClDwJ,cAAc,EAAE,CAAC,CAAC,IAAI,CAACrL,kBAAkB,EAAE6B,SAAS;MACpDyJ,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAACzL,WAAW;MACrC0L,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAACzL;KAC5B,CAAC;EACJ;EAEA;;;EAGQmL,kBAAkBA,CACxBF,UAA4B,EAC5BC,WAA6B;IAE7BtK,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;IAEzD;IACAoK,UAAU,CAACtH,MAAM,GAAG,CAAC,CAAC,CAAC;IACvBuH,WAAW,CAACvH,MAAM,GAAG,CAAC,CAAC,CAAC;IAExB;IACAsH,UAAU,CAACG,QAAQ,GAAG,IAAI;IAC1BF,WAAW,CAACE,QAAQ,GAAG,IAAI;IAE3B;IACAF,WAAW,CAAClF,gBAAgB,CAAC,gBAAgB,EAAE,MAAK;MAClDpF,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;IAC9D,CAAC,CAAC;IAEFqK,WAAW,CAAClF,gBAAgB,CAAC,SAAS,EAAE,MAAK;MAC3CpF,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;MACrD,IAAI,CAACsB,mBAAmB,CAAC+I,WAAW,CAAC;IACvC,CAAC,CAAC;IAEFA,WAAW,CAAClF,gBAAgB,CAAC,MAAM,EAAE,MAAK;MACxCpF,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;IAC9D,CAAC,CAAC;IAEFqK,WAAW,CAAClF,gBAAgB,CAAC,OAAO,EAAE,MAAK;MACzCpF,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;IACrD,CAAC,CAAC;EACJ;EAEA;;;EAGQsB,mBAAmBA,CAACuJ,YAA8B;IACxD9K,OAAO,CAACC,GAAG,CACT,uDAAuD,EACvD6K,YAAY,KAAK,IAAI,CAACzL,iBAAiB,GAAG,OAAO,GAAG,QAAQ,CAC7D;IAED;IACAyL,YAAY,CACTrJ,IAAI,EAAE,CACNqE,IAAI,CAAC,MAAK;MACT9F,OAAO,CAACC,GAAG,CACT,2DAA2D,CAC5D;IACH,CAAC,CAAC,CACDyB,KAAK,CAAEnB,KAAK,IAAI;MACfP,OAAO,CAACwB,IAAI,CACV,iEAAiE,EACjEjB,KAAK,CACN;MAED;MACA,MAAMwK,iBAAiB,GAAGA,CAAA,KAAK;QAC7BD,YAAY,CACTrJ,IAAI,EAAE,CACNqE,IAAI,CAAC,MAAK;UACT9F,OAAO,CAACC,GAAG,CACT,qEAAqE,CACtE;UACD+K,QAAQ,CAACC,mBAAmB,CAAC,OAAO,EAAEF,iBAAiB,CAAC;UACxDC,QAAQ,CAACC,mBAAmB,CAAC,SAAS,EAAEF,iBAAiB,CAAC;QAC5D,CAAC,CAAC,CACDrJ,KAAK,CAAEwJ,GAAG,IAAI;UACblL,OAAO,CAACO,KAAK,CACX,mDAAmD,EACnD2K,GAAG,CACJ;QACH,CAAC,CAAC;MACN,CAAC;MAEDF,QAAQ,CAAC5F,gBAAgB,CAAC,OAAO,EAAE2F,iBAAiB,CAAC;MACrDC,QAAQ,CAAC5F,gBAAgB,CAAC,SAAS,EAAE2F,iBAAiB,CAAC;IACzD,CAAC,CAAC;EACN;EAEA;;;EAGAI,YAAYA,CACVC,WAAmB,EACnB3C,QAAkB,EAClBG,cAAuB;IAEvB5I,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAE;MAC/CmL,WAAW;MACX3C,QAAQ;MACRG;KACD,CAAC;IAEF,IAAI,CAACwC,WAAW,EAAE;MAChB,MAAM7K,KAAK,GAAG,IAAIoJ,KAAK,CAAC,0BAA0B,CAAC;MACnD3J,OAAO,CAACO,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3D,OAAOlD,UAAU,CAAC,MAAMkD,KAAK,CAAC;;IAGhC;IACA,MAAMoG,MAAM,GAAG,QAAQ0E,IAAI,CAACC,GAAG,EAAE,IAAIC,IAAI,CAACC,MAAM,EAAE,CAC/CC,QAAQ,CAAC,EAAE,CAAC,CACZC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;IAEjB;IACA,OAAOpO,IAAI,CAAC,IAAI,CAACqO,iBAAiB,CAAClD,QAAQ,CAAC,CAAC,CAACmD,IAAI,CAChDnO,SAAS,CAAE4J,KAAK,IAAI;MAClB,MAAMX,SAAS,GAAG;QAChB0E,WAAW;QACX3C,QAAQ,EAAEA,QAAQ;QAClB9B,MAAM;QACNU,KAAK,EAAEtF,IAAI,CAACC,SAAS,CAACqF,KAAK,CAAC;QAC5BuB;OACD;MAED5I,OAAO,CAACC,GAAG,CACT,kDAAkD,EAClDyG,SAAS,CACV;MAED,OAAO,IAAI,CAACpI,MAAM,CACfuN,MAAM,CAAyB;QAC9BC,QAAQ,EAAElO,sBAAsB;QAChC8I;OACD,CAAC,CACDkF,IAAI,CACHrO,GAAG,CAAEwO,MAAM,IAAI;QACb/L,OAAO,CAACC,GAAG,CACT,8CAA8C,EAC9C8L,MAAM,CACP;QAED,IAAI,CAACA,MAAM,CAAC1F,IAAI,EAAE8E,YAAY,EAAE;UAC9B,MAAM,IAAIxB,KAAK,CAAC,mCAAmC,CAAC;;QAGtD,MAAMnB,IAAI,GAAGuD,MAAM,CAAC1F,IAAI,CAAC8E,YAAY;QACrCnL,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE;UAC5CW,EAAE,EAAE4H,IAAI,CAAC5H,EAAE;UACXuD,IAAI,EAAEqE,IAAI,CAACrE,IAAI;UACf0E,MAAM,EAAEL,IAAI,CAACK,MAAM;UACnBH,MAAM,EAAEF,IAAI,CAACE,MAAM,EAAEC,QAAQ;UAC7BqD,SAAS,EAAExD,IAAI,CAACwD,SAAS,EAAErD;SAC5B,CAAC;QAEF;QACA,IAAI,CAACnK,UAAU,CAAC4H,IAAI,CAACoC,IAAI,CAAC;QAE1B,OAAOA,IAAI;MACb,CAAC,CAAC,EACFhL,UAAU,CAAE+C,KAAK,IAAI;QACnBP,OAAO,CAACO,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;QAC3D,IAAI,CAAChC,MAAM,CAACgC,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAElD,IAAI0L,YAAY,GAAG,wCAAwC;QAC3D,IAAI1L,KAAK,CAAC2L,YAAY,EAAE;UACtBD,YAAY,GAAG,4BAA4B;SAC5C,MAAM,IAAI1L,KAAK,CAAC4L,aAAa,EAAEC,MAAM,GAAG,CAAC,EAAE;UAC1CH,YAAY,GAAG1L,KAAK,CAAC4L,aAAa,CAAC,CAAC,CAAC,CAACE,OAAO,IAAIJ,YAAY;;QAG/D,OAAO5O,UAAU,CAAC,MAAM,IAAIsM,KAAK,CAACsC,YAAY,CAAC,CAAC;MAClD,CAAC,CAAC,CACH;IACL,CAAC,CAAC,EACFzO,UAAU,CAAE+C,KAAK,IAAI;MACnBP,OAAO,CAACO,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD,OAAOlD,UAAU,CAAC,MAAM,IAAIsM,KAAK,CAAC,iBAAiB,GAAGpJ,KAAK,CAAC8L,OAAO,CAAC,CAAC;IACvE,CAAC,CAAC,CACH;EACH;EAEA;;;EAGcV,iBAAiBA,CAC7BlD,QAAkB;IAAA,IAAA6D,MAAA;IAAA,OAAAtF,iBAAA;MAElB,IAAI;QACF;QACA,IAAI,CAACsF,MAAI,CAACpN,cAAc,EAAE;UACxBoN,MAAI,CAAC5M,gBAAgB,EAAE;;QAGzB;QACA,MAAM8J,MAAM,SAAS8C,MAAI,CAACnD,YAAY,CAACV,QAAQ,CAAC;QAEhD;QACA6D,MAAI,CAAC1C,8BAA8B,CAACJ,MAAM,CAAC;QAE3C;QACA,MAAMnC,KAAK,SAASiF,MAAI,CAACvC,WAAW,EAAE;QAEtC/J,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC;QAChE,OAAOoH,KAAK;OACb,CAAC,OAAO9G,KAAK,EAAE;QACdP,OAAO,CAACO,KAAK,CAAC,gDAAgD,EAAEA,KAAK,CAAC;QACtE,MAAMA,KAAK;;IACZ;EACH;EAEA;;;EAGAgM,UAAUA,CAAC9N,YAA0B;IACnCuB,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAExB,YAAY,CAACmC,EAAE,CAAC;IAEhE;IACA,OAAOtD,IAAI,CAAC,IAAI,CAACkP,kBAAkB,CAAC/N,YAAY,CAAC,CAAC,CAACmN,IAAI,CACrDnO,SAAS,CAAEgK,MAAM,IAAI;MACnB,OAAO,IAAI,CAACnJ,MAAM,CACfuN,MAAM,CAAuB;QAC5BC,QAAQ,EAAEjO,oBAAoB;QAC9B6I,SAAS,EAAE;UACTC,MAAM,EAAElI,YAAY,CAACmC,EAAE;UACvB6G,MAAM,EAAE1F,IAAI,CAACC,SAAS,CAACyF,MAAM;;OAEhC,CAAC,CACDmE,IAAI,CACHnO,SAAS,CAAEsO,MAAM,IAAI;QACnB/L,OAAO,CAACC,GAAG,CACT,6CAA6C,EAC7C8L,MAAM,CACP;QAED,IAAI,CAACA,MAAM,CAAC1F,IAAI,EAAEkG,UAAU,EAAE;UAC5B,MAAM,IAAI5C,KAAK,CAAC,mCAAmC,CAAC;;QAGtD,MAAMnB,IAAI,GAAGuD,MAAM,CAAC1F,IAAI,CAACkG,UAAU;QAEnC;QACA,IAAI,CAAC9H,IAAI,CAAC,UAAU,CAAC;QACrB,IAAI,CAAChD,IAAI,CAAC,gBAAgB,CAAC;QAE3B;QACA,OAAOnE,IAAI,CAAC,IAAI,CAACmP,iBAAiB,CAAChO,YAAY,EAAE+J,IAAI,CAAC,CAAC;MACzD,CAAC,CAAC,EACFhL,UAAU,CAAE+C,KAAK,IAAI;QACnBP,OAAO,CAACO,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;QACzD,IAAI,CAAChC,MAAM,CAACgC,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;QACjD,OAAOlD,UAAU,CACf,MAAM,IAAIsM,KAAK,CAAC,yCAAyC,CAAC,CAC3D;MACH,CAAC,CAAC,CACH;IACL,CAAC,CAAC,EACFnM,UAAU,CAAE+C,KAAK,IAAI;MACnBP,OAAO,CAACO,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5D,OAAOlD,UAAU,CAAC,MAAM,IAAIsM,KAAK,CAAC,iBAAiB,GAAGpJ,KAAK,CAAC8L,OAAO,CAAC,CAAC;IACvE,CAAC,CAAC,CACH;EACH;EAEA;;;EAGcG,kBAAkBA,CAC9B/N,YAA0B;IAAA,IAAAiO,MAAA;IAAA,OAAA1F,iBAAA;MAE1B,IAAI;QACFhH,OAAO,CAACC,GAAG,CACT,4DAA4D,EAC5D;UACE0G,MAAM,EAAElI,YAAY,CAACmC,EAAE;UACvB6H,QAAQ,EAAEhK,YAAY,CAAC0F,IAAI;UAC3BwI,QAAQ,EAAE,CAAC,CAAClO,YAAY,CAAC4I,KAAK;UAC9BuF,WAAW,EAAEnO,YAAY,CAAC4I,KAAK,EAAE+E,MAAM,IAAI;SAC5C,CACF;QAED;QACA,IAAI,CAACM,MAAI,CAACxN,cAAc,EAAE;UACxBc,OAAO,CAACC,GAAG,CACT,6DAA6D,CAC9D;UACDyM,MAAI,CAAChN,gBAAgB,EAAE;;QAGzB;QACAM,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC;QAChE,MAAMuJ,MAAM,SAASkD,MAAI,CAACvD,YAAY,CAAC1K,YAAY,CAAC0F,IAAI,CAAC;QAEzD;QACAnE,OAAO,CAACC,GAAG,CACT,mEAAmE,CACpE;QACDyM,MAAI,CAAC9C,8BAA8B,CAACJ,MAAM,CAAC;QAE3C;QACA,IAAI,CAAC/K,YAAY,CAAC4I,KAAK,EAAE;UACvB,MAAM,IAAIsC,KAAK,CAAC,oCAAoC,CAAC;;QAGvD,MAAMtC,KAAK,GAAGtF,IAAI,CAACuF,KAAK,CAAC7I,YAAY,CAAC4I,KAAK,CAAC;QAE5C,IAAI,CAACA,KAAK,IAAI,CAACA,KAAK,CAAClD,IAAI,IAAI,CAACkD,KAAK,CAACwF,GAAG,EAAE;UACvC,MAAM,IAAIlD,KAAK,CAAC,+BAA+B,CAAC;;QAGlD;QACA,MAAMlC,MAAM,SAASiF,MAAI,CAACxC,YAAY,CAAC7C,KAAK,CAAC;QAE7CrH,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;QACjE,OAAOwH,MAAM;OACd,CAAC,OAAOlH,KAAK,EAAE;QACdP,OAAO,CAACO,KAAK,CAAC,iDAAiD,EAAEA,KAAK,CAAC;QACvE,MAAMA,KAAK;;IACZ;EACH;EAEA;;;EAGAuM,UAAUA,CAACnG,MAAc,EAAEoG,MAAe;IACxC/M,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE0G,MAAM,EAAEoG,MAAM,CAAC;IAE/D,OAAO,IAAI,CAACzO,MAAM,CACfuN,MAAM,CAA8B;MACnCC,QAAQ,EAAEhO,oBAAoB;MAC9B4I,SAAS,EAAE;QACTC,MAAM;QACNoG,MAAM,EAAEA,MAAM,IAAI;;KAErB,CAAC,CACDnB,IAAI,CACHrO,GAAG,CAAEwO,MAAM,IAAI;MACb/L,OAAO,CAACC,GAAG,CAAC,6CAA6C,EAAE8L,MAAM,CAAC;MAElE,IAAI,CAACA,MAAM,CAAC1F,IAAI,EAAEyG,UAAU,EAAE;QAC5B,MAAM,IAAInD,KAAK,CAAC,kCAAkC,CAAC;;MAGrD;MACA,IAAI,CAAClL,YAAY,CAAC2H,IAAI,CAAC,IAAI,CAAC;MAC5B,IAAI,CAAC5H,UAAU,CAAC4H,IAAI,CAAC,IAAI,CAAC;MAE1B;MACA,IAAI,CAAC3B,IAAI,CAAC,UAAU,CAAC;MAErB,OAAOsH,MAAM,CAAC1F,IAAI,CAACyG,UAAU;IAC/B,CAAC,CAAC,EACFtP,UAAU,CAAE+C,KAAK,IAAI;MACnBP,OAAO,CAACO,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzD,IAAI,CAAChC,MAAM,CAACgC,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MACjD,OAAOlD,UAAU,CAAC,MAAM,IAAIsM,KAAK,CAAC,iCAAiC,CAAC,CAAC;IACvE,CAAC,CAAC,CACH;EACL;EAEA;;;EAGAqD,OAAOA,CAACrG,MAAc;IACpB3G,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE0G,MAAM,CAAC;IAEpD,OAAO,IAAI,CAACrI,MAAM,CACfuN,MAAM,CAA2B;MAChCC,QAAQ,EAAE/N,iBAAiB;MAC3B2I,SAAS,EAAE;QACTC,MAAM;QACNsG,QAAQ,EAAE,IAAI,CAAE;;KAEnB,CAAC,CACDrB,IAAI,CACHrO,GAAG,CAAEwO,MAAM,IAAI;MACb/L,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAE8L,MAAM,CAAC;MAE/D,IAAI,CAACA,MAAM,CAAC1F,IAAI,EAAE2G,OAAO,EAAE;QACzB,MAAM,IAAIrD,KAAK,CAAC,kCAAkC,CAAC;;MAGrD;MACA,IAAI,CAACnL,UAAU,CAAC4H,IAAI,CAAC,IAAI,CAAC;MAC1B,IAAI,CAAC3H,YAAY,CAAC2H,IAAI,CAAC,IAAI,CAAC;MAE5B;MACA,IAAI,CAAC4C,aAAa,EAAE;MACpB,IAAI,CAACvH,IAAI,CAAC,UAAU,CAAC;MAErB;MACA,IAAI,CAACyL,aAAa,EAAE;MAEpB,OAAOnB,MAAM,CAAC1F,IAAI,CAAC2G,OAAO;IAC5B,CAAC,CAAC,EACFxP,UAAU,CAAE+C,KAAK,IAAI;MACnBP,OAAO,CAACO,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD,IAAI,CAAChC,MAAM,CAACgC,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC9C,OAAOlD,UAAU,CACf,MAAM,IAAIsM,KAAK,CAAC,kCAAkC,CAAC,CACpD;IACH,CAAC,CAAC,CACH;EACL;EAEA;;;EAGAwD,WAAWA,CACTxG,MAAc,EACdyG,WAAqB,EACrBC,WAAqB;IAErBrN,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE;MAC9C0G,MAAM;MACNyG,WAAW;MACXC;KACD,CAAC;IAEF,OAAO,IAAI,CAAC/O,MAAM,CACfuN,MAAM,CAAmC;MACxCC,QAAQ,EAAE9N,0BAA0B;MACpC0I,SAAS,EAAE;QACTC,MAAM;QACN2C,KAAK,EAAE8D,WAAW;QAClBvK,KAAK,EAAEwK;;KAEV,CAAC,CACDzB,IAAI,CACHrO,GAAG,CAAEwO,MAAM,IAAI;MACb/L,OAAO,CAACC,GAAG,CAAC,6CAA6C,EAAE8L,MAAM,CAAC;MAElE,IAAI,CAACA,MAAM,CAAC1F,IAAI,EAAEiH,eAAe,EAAE;QACjC,MAAM,IAAI3D,KAAK,CAAC,kCAAkC,CAAC;;MAGrD,OAAOoC,MAAM,CAAC1F,IAAI,CAACiH,eAAe;IACpC,CAAC,CAAC,EACF9P,UAAU,CAAE+C,KAAK,IAAI;MACnBP,OAAO,CAACO,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;MAC1D,IAAI,CAAChC,MAAM,CAACgC,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MACjD,OAAOlD,UAAU,CACf,MAAM,IAAIsM,KAAK,CAAC,sCAAsC,CAAC,CACxD;IACH,CAAC,CAAC,CACH;EACL;EAEA;;;EAGc8C,iBAAiBA,CAC7BhO,YAA0B,EAC1B+J,IAAU;IAAA,IAAA+E,MAAA;IAAA,OAAAvG,iBAAA;MAEVhH,OAAO,CAACC,GAAG,CAAC,4DAA4D,CAAC;MAEzE;MACAsN,MAAI,CAAC9L,IAAI,CAAC,gBAAgB,CAAC;MAE3B;MACA8L,MAAI,CAAC/O,UAAU,CAAC4H,IAAI,CAACoC,IAAI,CAAC;MAC1B+E,MAAI,CAAC9O,YAAY,CAAC2H,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;MAE9B,OAAOoC,IAAI;IAAC;EACd;EAEA;;;EAGAgF,WAAWA,CAAA;IACT,IAAI,CAACxO,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;IAC1CgB,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE,IAAI,CAACjB,cAAc,CAAC;IACnE,OAAO,IAAI,CAACA,cAAc;EAC5B;EAEA;;;EAGAyO,WAAWA,CAAA;IACT,IAAI,CAACxO,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;IAC1Ce,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE,IAAI,CAAChB,cAAc,CAAC;IACnE,OAAO,IAAI,CAACA,cAAc;EAC5B;EAEA;;;EAGAyO,eAAeA,CAAA;IACb,OAAO,IAAI,CAAC1O,cAAc;EAC5B;EAEA;;;EAGA2O,eAAeA,CAAA;IACb,OAAO,IAAI,CAAC1O,cAAc;EAC5B;EAEA;;;EAGQ+J,aAAaA,CAAA;IACnBhJ,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;IACnD+H,MAAM,CAACC,IAAI,CAAC,IAAI,CAACpJ,MAAM,CAAC,CAAC4E,OAAO,CAAEwB,IAAI,IAAI;MACxC,IAAI,CAACR,IAAI,CAACQ,IAAI,CAAC;IACjB,CAAC,CAAC;EACJ;EAEA;;;EAGO2I,YAAYA,CAAA;IACjB5N,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC;IACtE+H,MAAM,CAAC6F,MAAM,CAAC,IAAI,CAAChP,MAAM,CAAC,CAAC4E,OAAO,CAAEkC,KAAK,IAAI;MAC3C,IAAIA,KAAK,EAAE;QACTA,KAAK,CAAC5G,KAAK,GAAG,KAAK;QACnB4G,KAAK,CAAC5C,MAAM,GAAG,GAAG;QAClB;QACA4C,KAAK,CACFlE,IAAI,EAAE,CACNqE,IAAI,CAAC,MAAK;UACTH,KAAK,CAACK,KAAK,EAAE;UACbL,KAAK,CAACnC,WAAW,GAAG,CAAC;QACvB,CAAC,CAAC,CACD9B,KAAK,CAAC,MAAK;UACV;QAAA,CACD,CAAC;;IAER,CAAC,CAAC;EACJ;EAEA;;;EAGQwL,aAAaA,CAAA;IACnBlN,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;IAE5D;IACA,IAAI,IAAI,CAACd,WAAW,EAAE;MACpB,IAAI,CAACA,WAAW,CAAC2B,SAAS,EAAE,CAAC2C,OAAO,CAAEoG,KAAK,IAAI;QAC7CA,KAAK,CAACpF,IAAI,EAAE;MACd,CAAC,CAAC;MACF,IAAI,CAACtF,WAAW,GAAG,IAAI;;IAGzB;IACA,IAAI,IAAI,CAACE,iBAAiB,EAAE;MAC1B,IAAI,CAACA,iBAAiB,CAAC8B,SAAS,GAAG,IAAI;;IAEzC,IAAI,IAAI,CAAC7B,kBAAkB,EAAE;MAC3B,IAAI,CAACA,kBAAkB,CAAC6B,SAAS,GAAG,IAAI;;IAG1C;IACA,IAAI,IAAI,CAACjC,cAAc,EAAE;MACvB,IAAI,CAACA,cAAc,CAAC4O,KAAK,EAAE;MAC3B,IAAI,CAAC5O,cAAc,GAAG,IAAI;;IAG5B,IAAI,CAACE,YAAY,GAAG,IAAI;EAC1B;EAEA2O,WAAWA,CAAA;IACT,IAAI,CAAC/E,aAAa,EAAE;IACpB,IAAI,CAACkE,aAAa,EAAE;EACtB;;;uBAt3DW9O,WAAW,EAAA4P,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,aAAA;IAAA;EAAA;;;aAAXjQ,WAAW;MAAAkQ,OAAA,EAAXlQ,WAAW,CAAAmQ,IAAA;MAAAC,UAAA,EAFV;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}