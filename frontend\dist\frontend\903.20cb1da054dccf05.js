"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[903],{8397:(j,p,f)=>{f.d(p,{f:()=>t});var u=f(4412),h=f(7705);let t=(()=>{class b{constructor(){this.toastsSubject=new u.t([]),this.toasts$=this.toastsSubject.asObservable(),this.currentId=0}generateId(){return Math.random().toString(36).substr(2,9)}addToast(i){const o={...i,id:this.generateId(),duration:i.duration||5e3};this.toastsSubject.next([...this.toastsSubject.value,o]),o.duration&&o.duration>0&&setTimeout(()=>{this.removeToast(o.id)},o.duration)}show(i,o="info",s=5e3){const m=this.generateId();this.toastsSubject.next([...this.toastsSubject.value,{id:m,type:o,title:"",message:i,duration:s}]),s>0&&setTimeout(()=>this.dismiss(m),s)}showSuccess(i,o=3e3){this.show(i,"success",o)}showError(i,o=5e3){this.show(i,"error",o)}showWarning(i,o=4e3){this.show(i,"warning",o)}showInfo(i,o=3e3){this.show(i,"info",o)}dismiss(i){const o=this.toastsSubject.value.filter(s=>s.id!==i);this.toastsSubject.next(o)}success(i,o,s){this.addToast({type:"success",title:i,message:o,duration:s,icon:"check-circle"})}error(i,o,s,m){this.addToast({type:"error",title:i,message:o,duration:s||8e3,icon:"x-circle",action:m})}warning(i,o,s){this.addToast({type:"warning",title:i,message:o,duration:s,icon:"exclamation-triangle"})}accessDenied(i="effectuer cette action",o){this.error("Acc\xe8s refus\xe9",`Vous n'avez pas les permissions n\xe9cessaires pour ${i}${o?` (Code: ${o})`:""}`,8e3,{label:"Comprendre les r\xf4les",handler:()=>{console.log("Redirection vers l'aide sur les r\xf4les")}})}ownershipRequired(i="cette ressource"){this.error("Propri\xe9taire requis",`Seul le propri\xe9taire ou un administrateur peut modifier ${i}`,8e3)}removeToast(i){this.toastsSubject.next(this.toastsSubject.value.filter(s=>s.id!==i))}clear(){this.toastsSubject.next([])}static{this.\u0275fac=function(o){return new(o||b)}}static{this.\u0275prov=h.jDH({token:b,factory:b.\u0275fac,providedIn:"root"})}}return b})()},1903:(j,p,f)=>{f.r(p),f.d(p,{DashboardModule:()=>D});var u=f(177),h=f(6647),t=f(7705),b=f(4796),P=f(8397);function i(r,l){if(1&r){const e=t.RV6();t.j41(0,"button",23),t.bIt("click",function(){t.eBV(e);const a=t.XpG();return t.Njj(a.clearSearch())}),t.nrm(1,"i",24),t.k0s()}}function o(r,l){if(1&r&&(t.j41(0,"div",25)(1,"div",26)(2,"div",27),t.nrm(3,"i",28)(4,"div",29),t.k0s(),t.j41(5,"div",30)(6,"h3",31),t.EFF(7," Success "),t.k0s(),t.j41(8,"p",32),t.EFF(9),t.k0s()()()()),2&r){const e=t.XpG();t.R7$(9),t.SpI(" ",e.message," ")}}function s(r,l){if(1&r&&(t.j41(0,"div",33)(1,"div",26)(2,"div",34),t.nrm(3,"i",35)(4,"div",36),t.k0s(),t.j41(5,"div",30)(6,"h3",37),t.EFF(7," Error "),t.k0s(),t.j41(8,"p",38),t.EFF(9),t.k0s()()()()),2&r){const e=t.XpG();t.R7$(9),t.SpI(" ",e.error," ")}}function m(r,l){1&r&&(t.j41(0,"div",39)(1,"div",40),t.nrm(2,"div",41)(3,"div",42),t.k0s()())}function k(r,l){if(1&r&&(t.j41(0,"div",43)(1,"div",44),t.nrm(2,"div",45)(3,"div",46),t.j41(4,"div",47)(5,"div",48)(6,"div",30)(7,"div",49),t.EFF(8," Total Users "),t.k0s(),t.j41(9,"div",50),t.EFF(10),t.k0s()(),t.j41(11,"div",51),t.nrm(12,"div",52),t.j41(13,"div",53),t.nrm(14,"i",54),t.k0s()()(),t.j41(15,"div",55),t.nrm(16,"div",56),t.j41(17,"div",57),t.nrm(18,"div",58),t.k0s()()()(),t.j41(19,"div",44),t.nrm(20,"div",59)(21,"div",60),t.j41(22,"div",47)(23,"div",48)(24,"div",30)(25,"div",49),t.EFF(26," User Status "),t.k0s(),t.j41(27,"div",61)(28,"div",48)(29,"div",62),t.nrm(30,"div",63),t.k0s(),t.j41(31,"span",64),t.EFF(32),t.k0s()(),t.j41(33,"div",48),t.nrm(34,"div",65),t.j41(35,"span",66),t.EFF(36),t.k0s()()()(),t.j41(37,"div",51),t.nrm(38,"div",67),t.j41(39,"div",53),t.nrm(40,"i",68),t.k0s()()(),t.j41(41,"div",55),t.nrm(42,"div",69),t.j41(43,"div",70)(44,"div",71),t.nrm(45,"div",72),t.k0s(),t.j41(46,"div",73),t.nrm(47,"div",74),t.k0s()()()()(),t.j41(48,"div",44),t.nrm(49,"div",75)(50,"div",76),t.j41(51,"div",47)(52,"div",48)(53,"div",30)(54,"div",49),t.EFF(55," User Roles "),t.k0s(),t.j41(56,"div",77)(57,"div",48),t.nrm(58,"div",78),t.j41(59,"span",79),t.EFF(60),t.k0s()(),t.j41(61,"div",48),t.nrm(62,"div",80),t.j41(63,"span",81),t.EFF(64),t.k0s()(),t.j41(65,"div",48),t.nrm(66,"div",82),t.j41(67,"span",83),t.EFF(68),t.k0s()()()(),t.j41(69,"div",51),t.nrm(70,"div",84),t.j41(71,"div",53),t.nrm(72,"i",85),t.k0s()()(),t.j41(73,"div",55),t.nrm(74,"div",86),t.j41(75,"div",70)(76,"div",87),t.nrm(77,"div",88),t.k0s(),t.j41(78,"div",89),t.nrm(79,"div",90),t.k0s(),t.j41(80,"div",91),t.nrm(81,"div",92),t.k0s()()(),t.j41(82,"div",93)(83,"span"),t.EFF(84,"Students"),t.k0s(),t.j41(85,"span"),t.EFF(86,"Teachers"),t.k0s(),t.j41(87,"span"),t.EFF(88,"Admins"),t.k0s()()()()()),2&r){const e=t.XpG();t.R7$(10),t.SpI(" ",e.users.length," "),t.R7$(22),t.SpI("",e.getActiveCount()," Active"),t.R7$(4),t.SpI("",e.getInactiveCount()," Inactive"),t.R7$(8),t.xc7("width",e.users.length?e.getActiveCount()/e.users.length*100:0,"%"),t.R7$(2),t.xc7("width",e.users.length?e.getInactiveCount()/e.users.length*100:0,"%"),t.R7$(14),t.SpI("",e.getStudentCount()," Students"),t.R7$(4),t.SpI("",e.getTeacherCount()," Teachers"),t.R7$(4),t.SpI("",e.getAdminCount()," Admins"),t.R7$(8),t.xc7("width",e.users.length?e.getStudentCount()/e.users.length*100:0,"%"),t.R7$(2),t.xc7("width",e.users.length?e.getTeacherCount()/e.users.length*100:0,"%"),t.R7$(2),t.xc7("width",e.users.length?e.getAdminCount()/e.users.length*100:0,"%")}}function _(r,l){if(1&r&&(t.j41(0,"option",132),t.EFF(1),t.nI1(2,"titlecase"),t.k0s()),2&r){const e=l.$implicit;t.Y8G("value",e),t.R7$(1),t.SpI(" ",t.bMT(2,2,e)," ")}}function F(r,l){if(1&r){const e=t.RV6();t.j41(0,"tr",109)(1,"td",110)(2,"div",48)(3,"div",111),t.nrm(4,"div",112),t.j41(5,"span",7),t.EFF(6),t.k0s()(),t.j41(7,"div",113)(8,"div",114),t.EFF(9),t.k0s()()()(),t.j41(10,"td",115)(11,"div",116),t.EFF(12),t.k0s()(),t.j41(13,"td",117)(14,"span",118),t.nrm(15,"i"),t.EFF(16),t.k0s()(),t.j41(17,"td",117)(18,"span",119),t.nrm(19,"i"),t.EFF(20),t.k0s()(),t.j41(21,"td",117)(22,"div",120)(23,"select",121),t.bIt("change",function(a){const d=t.eBV(e).$implicit,g=t.XpG(2);return t.Njj(g.onRoleChange(d._id,a.target.value))}),t.DNE(24,_,3,4,"option",122),t.k0s(),t.j41(25,"div",123),t.nrm(26,"i",124),t.k0s()()(),t.j41(27,"td",117)(28,"button",125),t.bIt("click",function(){const c=t.eBV(e).$implicit,d=t.XpG(2);return t.Njj(d.toggleUserActivation(c._id,!1!==c.isActive))}),t.nrm(29,"div",126)(30,"i",127),t.j41(31,"span",7),t.EFF(32),t.k0s()()(),t.j41(33,"td",117)(34,"button",128),t.bIt("click",function(){const c=t.eBV(e).$implicit,d=t.XpG(2);return t.Njj(d.onDeleteUser(c._id))}),t.nrm(35,"i",129),t.EFF(36," Delete "),t.k0s()(),t.j41(37,"td",117)(38,"button",130),t.bIt("click",function(){const c=t.eBV(e).$implicit,d=t.XpG(2);return t.Njj(d.showUserDetails(c._id))}),t.nrm(39,"i",131),t.EFF(40," Details "),t.k0s()()()}if(2&r){const e=l.$implicit,n=t.XpG(2);t.R7$(6),t.JRh(e.fullName.charAt(0)),t.R7$(3),t.SpI(" ",e.fullName," "),t.R7$(3),t.SpI(" ",e.email," "),t.R7$(2),t.Y8G("ngClass",e.verified?"bg-[#afcf75]/10 dark:bg-[#afcf75]/5 text-[#2a5a03] dark:text-[#afcf75]":"bg-[#ff6b69]/10 dark:bg-[#ff6b69]/5 text-[#ff6b69] dark:text-[#ff8785]"),t.R7$(1),t.HbH(e.verified?"fas fa-check-circle mr-1":"fas fa-times-circle mr-1"),t.R7$(1),t.SpI(" ",e.verified?"Yes":"No"," "),t.R7$(2),t.Y8G("ngClass",!1!==e.isActive?"bg-[#afcf75]/15 dark:bg-[#afcf75]/10 text-[#2a5a03] dark:text-[#afcf75] border border-[#afcf75]/30 shadow-sm":"bg-[#ff6b69]/15 dark:bg-[#ff6b69]/10 text-[#ff6b69] dark:text-[#ff8785] border border-[#ff6b69]/30 shadow-sm"),t.R7$(1),t.HbH(!1!==e.isActive?"fas fa-check-circle mr-1.5 text-[10px]":"fas fa-times-circle mr-1.5 text-[10px]"),t.R7$(1),t.SpI(" ",!1!==e.isActive?"Active":"Deactivated"," "),t.R7$(3),t.Y8G("value",e.role),t.R7$(1),t.Y8G("ngForOf",n.roles),t.R7$(4),t.Y8G("ngClass",!1!==e.isActive?"bg-[#ff6b69]/10 dark:bg-[#ff6b69]/5 text-[#ff6b69] dark:text-[#ff8785] hover:bg-[#ff6b69]/20 dark:hover:bg-[#ff6b69]/10 border-[#ff6b69]/30 hover:border-[#ff6b69]/50":"bg-[#afcf75]/10 dark:bg-[#afcf75]/5 text-[#2a5a03] dark:text-[#afcf75] hover:bg-[#afcf75]/20 dark:hover:bg-[#afcf75]/10 border-[#afcf75]/30 hover:border-[#afcf75]/50")("title",!1!==e.isActive?"Click to deactivate this user account":"Click to activate this user account"),t.R7$(1),t.Y8G("ngClass",!1!==e.isActive?"bg-gradient-to-r from-[#ff6b69]/5 to-[#ff6b69]/10":"bg-gradient-to-r from-[#afcf75]/5 to-[#afcf75]/10"),t.R7$(1),t.HbH(!1!==e.isActive?"fas fa-user-slash":"fas fa-user-check"),t.R7$(2),t.SpI(" ",!1!==e.isActive?"Deactivate":"Activate"," ")}}function E(r,l){if(1&r&&(t.j41(0,"div",94),t.nrm(1,"div",95),t.j41(2,"div",96)(3,"h6",97),t.nrm(4,"i",98),t.EFF(5," User Management "),t.k0s()(),t.j41(6,"div",99)(7,"table",100)(8,"thead",101)(9,"tr")(10,"th",102),t.EFF(11," Name "),t.k0s(),t.j41(12,"th",103),t.EFF(13," Email "),t.k0s(),t.j41(14,"th",104),t.EFF(15," Verified "),t.k0s(),t.j41(16,"th",104),t.EFF(17," Status "),t.k0s(),t.j41(18,"th",105),t.EFF(19," Role "),t.k0s(),t.j41(20,"th",106),t.EFF(21," Activate "),t.k0s(),t.j41(22,"th",106),t.EFF(23," Delete "),t.k0s(),t.j41(24,"th",106),t.EFF(25," Details "),t.k0s()()(),t.j41(26,"tbody",107),t.DNE(27,F,41,19,"tr",108),t.k0s()()()()),2&r){const e=t.XpG();t.R7$(27),t.Y8G("ngForOf",e.filteredUsers)}}function S(r,l){if(1&r){const e=t.RV6();t.j41(0,"div",143)(1,"button",144),t.bIt("click",function(){t.eBV(e);const a=t.XpG(2);return t.Njj(a.clearSearch())}),t.nrm(2,"div",145),t.j41(3,"div",146),t.nrm(4,"i",147),t.EFF(5," Clear search "),t.k0s()()()}}function I(r,l){if(1&r&&(t.j41(0,"div",94),t.nrm(1,"div",95),t.j41(2,"div",133)(3,"div",134),t.nrm(4,"div",135),t.j41(5,"div",136),t.nrm(6,"i",137),t.k0s(),t.nrm(7,"div",138)(8,"div",139),t.k0s(),t.j41(9,"h3",140),t.EFF(10," No users found "),t.k0s(),t.j41(11,"p",141),t.EFF(12),t.k0s(),t.DNE(13,S,6,0,"div",142),t.k0s()()),2&r){const e=t.XpG();t.R7$(12),t.SpI(" ",e.searchTerm?"No users match your search criteria.":"There are no users in the system yet."," "),t.R7$(1),t.Y8G("ngIf",e.searchTerm)}}const T=[{path:"",component:(()=>{class r{constructor(e,n,a){this.authService=e,this.router=n,this.toastService=a,this.users=[],this.error="",this.message="",this.roles=["student","teacher","admin"],this.loading=!0,this.currentUser=null,this.searchTerm="",this.filteredUsers=[]}ngOnInit(){this.loadUserData()}loadUserData(){this.loading=!0;const e=localStorage.getItem("token"),n=localStorage.getItem("user");e&&n?(this.currentUser=JSON.parse(n),"admin"===this.currentUser.role?this.authService.getAllUsers(e).subscribe({next:a=>{this.users=a,this.filteredUsers=[...this.users],this.loading=!1},error:a=>{this.error=a.error?.message||"Failed to fetch users",this.loading=!1}}):this.router.navigate(["/"])):this.router.navigate(["/admin/login"])}searchUsers(){if(!this.searchTerm.trim())return void(this.filteredUsers=[...this.users]);const e=this.searchTerm.toLowerCase().trim();this.filteredUsers=this.users.filter(n=>n.fullName.toLowerCase().includes(e)||n.email.toLowerCase().includes(e)||n.role.toLowerCase().includes(e))}clearSearch(){this.searchTerm="",this.filteredUsers=[...this.users]}applyFilters(){this.searchUsers()}onRoleChange(e,n){const a=localStorage.getItem("token");this.authService.updateUserRole(e,n,a).subscribe({next:c=>{this.message=c.message,this.error="";const d=this.users.findIndex(v=>v._id===e);-1!==d&&(this.users[d].role=n);const g=this.filteredUsers.findIndex(v=>v._id===e);-1!==g&&(this.filteredUsers[g].role=n),setTimeout(()=>{this.message=""},3e3)},error:c=>{this.error=c.error?.message||"Failed to update role",this.message="",setTimeout(()=>{this.error=""},3e3)}})}onDeleteUser(e){if(!confirm("Are you sure you want to delete this user?"))return;const a=localStorage.getItem("token");this.authService.deleteUser(e,a).subscribe({next:c=>{this.message=c.message,this.error="",this.users=this.users.filter(d=>d._id!==e),this.filteredUsers=this.filteredUsers.filter(d=>d._id!==e),setTimeout(()=>{this.message=""},3e3)},error:c=>{this.error=c.error?.message||"Failed to delete user",this.message="",setTimeout(()=>{this.error=""},3e3)}})}toggleUserActivation(e,n){const a=!n,c=a?"activate":"deactivate",d=this.users.find(x=>x._id===e),g=d?.fullName||d?.firstName||"User";if(!confirm(`Are you sure you want to ${c} ${g}?`))return;const R=localStorage.getItem("token");this.authService.toggleUserActivation(e,a,R).subscribe({next:x=>{this.toastService.showSuccess(`${g} has been ${a?"activated":"deactivated"} successfully`),this.message="",this.error="";const y=this.users.findIndex(w=>w._id===e);-1!==y&&(this.users[y].isActive=a);const O=this.filteredUsers.findIndex(w=>w._id===e);-1!==O&&(this.filteredUsers[O].isActive=a),this.applyFilters()},error:x=>{const M=a?"activate":"deactivate";this.toastService.showError(x.error?.message||`Failed to ${M} ${g}`),this.message="",this.error=""}})}getStudentCount(){return this.users.filter(e=>"student"===e.role).length}getTeacherCount(){return this.users.filter(e=>"teacher"===e.role).length}getAdminCount(){return this.users.filter(e=>"admin"===e.role).length}getActiveCount(){return this.users.filter(e=>!1!==e.isActive).length}getInactiveCount(){return this.users.filter(e=>!1===e.isActive).length}logout(){this.authService.logout(),this.router.navigate(["/admin/login"])}showUserDetails(e){this.router.navigate(["/admin/userdetails",e])}static{this.\u0275fac=function(n){return new(n||r)(t.rXU(b.u),t.rXU(h.Ix),t.rXU(P.f))}}static{this.\u0275cmp=t.VBU({type:r,selectors:[["app-dashboard"]],decls:37,vars:8,consts:[[1,"container-fluid","p-4","md:p-6","bg-[#edf1f4]","dark:bg-[#121212]","min-h-screen","relative"],[1,"absolute","inset-0","overflow-hidden","pointer-events-none"],[1,"absolute","top-[15%]","left-[10%]","w-64","h-64","rounded-full","bg-gradient-to-br","from-[#4f5fad]/5","to-transparent","dark:from-[#6d78c9]/3","dark:to-transparent","blur-3xl"],[1,"absolute","bottom-[20%]","right-[10%]","w-80","h-80","rounded-full","bg-gradient-to-tl","from-[#4f5fad]/5","to-transparent","dark:from-[#6d78c9]/3","dark:to-transparent","blur-3xl"],[1,"absolute","inset-0","opacity-5","dark:opacity-[0.03]"],[1,"h-full","grid","grid-cols-12"],[1,"border-r","border-[#4f5fad]","dark:border-[#6d78c9]"],[1,"relative","z-10"],[1,"flex","flex-col","md:flex-row","md:items-center","md:justify-between","mb-8"],[1,"text-2xl","font-bold","bg-gradient-to-r","from-[#3d4a85]","to-[#4f5fad]","dark:from-[#6d78c9]","dark:to-[#4f5fad]","bg-clip-text","text-transparent","mb-2"],[1,"text-sm","text-[#6d6870]","dark:text-[#a0a0a0]"],[1,"relative","w-full","md:w-72","mt-4","md:mt-0","group"],[1,"absolute","inset-y-0","left-0","pl-3","flex","items-center","pointer-events-none"],[1,"fas","fa-search","text-[#bdc6cc]","dark:text-[#6d6870]","group-focus-within:text-[#4f5fad]","dark:group-focus-within:text-[#6d78c9]","transition-colors"],["type","text","placeholder","Search users...",1,"w-full","pl-10","pr-10","py-2.5","text-sm","rounded-lg","border","border-[#bdc6cc]","dark:border-[#2a2a2a]","bg-white","dark:bg-[#1e1e1e]","text-[#6d6870]","dark:text-[#e0e0e0]","focus:outline-none","focus:border-[#4f5fad]","dark:focus:border-[#6d78c9]","focus:ring-2","focus:ring-[#4f5fad]/20","dark:focus:ring-[#6d78c9]/20","transition-all",3,"value","input"],[1,"absolute","inset-y-0","left-0","pl-3","flex","items-center","pointer-events-none","opacity-0","group-focus-within:opacity-100","transition-opacity"],[1,"w-0.5","h-4","bg-gradient-to-b","from-[#3d4a85]","to-[#4f5fad]","dark:from-[#6d78c9]","dark:to-[#4f5fad]","rounded-full"],["class","absolute inset-y-0 right-0 pr-3 flex items-center text-[#bdc6cc] dark:text-[#6d6870] hover:text-[#ff6b69] dark:hover:text-[#ff8785] transition-colors",3,"click",4,"ngIf"],["class","bg-[#afcf75]/10 dark:bg-[#afcf75]/5 border border-[#afcf75] dark:border-[#afcf75]/30 rounded-lg p-4 mx-auto max-w-3xl mb-6 backdrop-blur-sm",4,"ngIf"],["class","bg-[#ff6b69]/10 dark:bg-[#ff6b69]/5 border border-[#ff6b69] dark:border-[#ff6b69]/30 rounded-lg p-4 mx-auto max-w-3xl mb-6 backdrop-blur-sm",4,"ngIf"],["class","flex justify-center items-center py-20",4,"ngIf"],["class","grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",4,"ngIf"],["class","bg-white dark:bg-[#1e1e1e] rounded-xl shadow-md dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)] overflow-hidden mb-8 backdrop-blur-sm border border-[#edf1f4]/50 dark:border-[#2a2a2a] relative",4,"ngIf"],[1,"absolute","inset-y-0","right-0","pr-3","flex","items-center","text-[#bdc6cc]","dark:text-[#6d6870]","hover:text-[#ff6b69]","dark:hover:text-[#ff8785]","transition-colors",3,"click"],[1,"fas","fa-times-circle"],[1,"bg-[#afcf75]/10","dark:bg-[#afcf75]/5","border","border-[#afcf75]","dark:border-[#afcf75]/30","rounded-lg","p-4","mx-auto","max-w-3xl","mb-6","backdrop-blur-sm"],[1,"flex","items-start"],[1,"text-[#2a5a03]","dark:text-[#afcf75]","mr-3","text-xl","relative"],[1,"fas","fa-check-circle"],[1,"absolute","inset-0","bg-[#afcf75]/20","dark:bg-[#afcf75]/20","blur-xl","rounded-full","transform","scale-150","-z-10"],[1,"flex-1"],[1,"font-medium","text-[#2a5a03]","dark:text-[#afcf75]","mb-1"],[1,"text-sm","text-[#2a5a03]/80","dark:text-[#afcf75]/80"],[1,"bg-[#ff6b69]/10","dark:bg-[#ff6b69]/5","border","border-[#ff6b69]","dark:border-[#ff6b69]/30","rounded-lg","p-4","mx-auto","max-w-3xl","mb-6","backdrop-blur-sm"],[1,"text-[#ff6b69]","dark:text-[#ff8785]","mr-3","text-xl","relative"],[1,"fas","fa-exclamation-triangle"],[1,"absolute","inset-0","bg-[#ff6b69]/20","dark:bg-[#ff8785]/20","blur-xl","rounded-full","transform","scale-150","-z-10"],[1,"font-medium","text-[#ff6b69]","dark:text-[#ff8785]","mb-1"],[1,"text-sm","text-[#ff6b69]/80","dark:text-[#ff8785]/80"],[1,"flex","justify-center","items-center","py-20"],[1,"relative"],[1,"w-14","h-14","border-4","border-[#4f5fad]/20","dark:border-[#6d78c9]/20","border-t-[#4f5fad]","dark:border-t-[#6d78c9]","rounded-full","animate-spin"],[1,"absolute","inset-0","bg-[#4f5fad]/20","dark:bg-[#6d78c9]/20","blur-xl","rounded-full","transform","scale-150","-z-10"],[1,"grid","grid-cols-1","md:grid-cols-3","gap-6","mb-8"],[1,"bg-white","dark:bg-[#1e1e1e]","rounded-xl","shadow-md","dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)]","overflow-hidden","backdrop-blur-sm","border","border-[#edf1f4]/50","dark:border-[#2a2a2a]","relative","group","hover:shadow-lg","dark:hover:shadow-[0_8px_30px_rgba(0,0,0,0.3)]","transition-all","duration-300","hover:-translate-y-1"],[1,"absolute","top-0","left-0","right-0","h-1","bg-gradient-to-r","from-[#3d4a85]","to-[#4f5fad]","dark:from-[#6d78c9]","dark:to-[#4f5fad]"],[1,"absolute","top-0","left-0","right-0","h-1","bg-gradient-to-r","from-[#3d4a85]","to-[#4f5fad]","dark:from-[#6d78c9]","dark:to-[#4f5fad]","opacity-0","group-hover:opacity-100","blur-md","transition-opacity","duration-300"],[1,"p-6"],[1,"flex","items-center"],[1,"text-sm","font-medium","text-[#6d6870]","dark:text-[#a0a0a0]","uppercase","tracking-wider"],[1,"mt-1","text-2xl","font-semibold","bg-gradient-to-r","from-[#3d4a85]","to-[#4f5fad]","dark:from-[#6d78c9]","dark:to-[#4f5fad]","bg-clip-text","text-transparent"],[1,"ml-4","relative"],[1,"absolute","inset-0","bg-[#4f5fad]/10","dark:bg-[#6d78c9]/10","rounded-full","blur-xl","opacity-0","group-hover:opacity-100","transition-opacity","duration-300","scale-150"],[1,"relative","z-10","bg-[#edf1f4]/80","dark:bg-[#2a2a2a]/80","rounded-full","p-2.5","backdrop-blur-sm","group-hover:scale-110","transition-transform","duration-300"],[1,"fas","fa-users","text-[#4f5fad]","dark:text-[#6d78c9]","text-xl"],[1,"mt-4","w-full","bg-[#edf1f4]","dark:bg-[#2a2a2a]","rounded-full","h-2.5","overflow-hidden","relative"],[1,"absolute","inset-0","bg-gradient-to-r","from-[#3d4a85]/10","to-[#4f5fad]/10","dark:from-[#6d78c9]/10","dark:to-[#4f5fad]/10","opacity-0","group-hover:opacity-100","transition-opacity","duration-300","blur-md"],[1,"bg-gradient-to-r","from-[#3d4a85]","to-[#4f5fad]","dark:from-[#6d78c9]","dark:to-[#4f5fad]","h-2.5","rounded-full","relative","z-10",2,"width","100%"],[1,"absolute","inset-0","bg-[#00f7ff]/20","rounded-full","animate-pulse"],[1,"absolute","top-0","left-0","right-0","h-1","bg-gradient-to-r","from-[#2a5a03]","to-[#afcf75]","dark:from-[#2a5a03]","dark:to-[#afcf75]"],[1,"absolute","top-0","left-0","right-0","h-1","bg-gradient-to-r","from-[#2a5a03]","to-[#afcf75]","dark:from-[#2a5a03]","dark:to-[#afcf75]","opacity-0","group-hover:opacity-100","blur-md","transition-opacity","duration-300"],[1,"mt-1","flex","space-x-4","text-sm"],[1,"w-2","h-2","rounded-full","bg-[#afcf75]","dark:bg-[#afcf75]","mr-1.5","relative"],[1,"absolute","inset-0","bg-[#afcf75]","rounded-full","animate-ping","opacity-75"],[1,"text-[#2a5a03]","dark:text-[#afcf75]","font-medium"],[1,"w-2","h-2","rounded-full","bg-[#ff6b69]","dark:bg-[#ff8785]","mr-1.5"],[1,"text-[#ff6b69]","dark:text-[#ff8785]","font-medium"],[1,"absolute","inset-0","bg-[#afcf75]/10","dark:bg-[#afcf75]/10","rounded-full","blur-xl","opacity-0","group-hover:opacity-100","transition-opacity","duration-300","scale-150"],[1,"fas","fa-check-circle","text-[#afcf75]","dark:text-[#afcf75]","text-xl"],[1,"absolute","inset-0","bg-gradient-to-r","from-[#2a5a03]/10","to-[#afcf75]/10","dark:from-[#2a5a03]/10","dark:to-[#afcf75]/10","opacity-0","group-hover:opacity-100","transition-opacity","duration-300","blur-md"],[1,"flex","h-full","relative","z-10"],[1,"bg-gradient-to-r","from-[#2a5a03]","to-[#afcf75]","dark:from-[#2a5a03]","dark:to-[#afcf75]","h-2.5","relative"],[1,"absolute","inset-0","bg-[#afcf75]/20","rounded-full","animate-pulse"],[1,"bg-gradient-to-r","from-[#ff6b69]","to-[#ff8785]","dark:from-[#ff6b69]","dark:to-[#ff8785]","h-2.5","relative"],[1,"absolute","inset-0","bg-[#ff6b69]/20","rounded-full","animate-pulse"],[1,"absolute","top-0","left-0","right-0","h-1","bg-gradient-to-r","from-[#4a89ce]","to-[#7826b5]","dark:from-[#4a89ce]","dark:to-[#7826b5]"],[1,"absolute","top-0","left-0","right-0","h-1","bg-gradient-to-r","from-[#4a89ce]","to-[#7826b5]","dark:from-[#4a89ce]","dark:to-[#7826b5]","opacity-0","group-hover:opacity-100","blur-md","transition-opacity","duration-300"],[1,"mt-1","flex","flex-wrap","gap-4","text-sm"],[1,"w-2","h-2","rounded-full","bg-[#4a89ce]","dark:bg-[#4a89ce]","mr-1.5"],[1,"text-[#4a89ce]","dark:text-[#4a89ce]","font-medium"],[1,"w-2","h-2","rounded-full","bg-[#7826b5]","dark:bg-[#7826b5]","mr-1.5"],[1,"text-[#7826b5]","dark:text-[#7826b5]","font-medium"],[1,"w-2","h-2","rounded-full","bg-[#4f5fad]","dark:bg-[#6d78c9]","mr-1.5"],[1,"text-[#4f5fad]","dark:text-[#6d78c9]","font-medium"],[1,"absolute","inset-0","bg-[#4a89ce]/10","dark:bg-[#4a89ce]/10","rounded-full","blur-xl","opacity-0","group-hover:opacity-100","transition-opacity","duration-300","scale-150"],[1,"fas","fa-user-tag","text-[#4a89ce]","dark:text-[#4a89ce]","text-xl"],[1,"absolute","inset-0","bg-gradient-to-r","from-[#4a89ce]/10","to-[#7826b5]/10","dark:from-[#4a89ce]/10","dark:to-[#7826b5]/10","opacity-0","group-hover:opacity-100","transition-opacity","duration-300","blur-md"],[1,"bg-gradient-to-r","from-[#4a89ce]","to-[#4a89ce]","dark:from-[#4a89ce]","dark:to-[#4a89ce]","h-2.5","relative"],[1,"absolute","inset-0","bg-[#4a89ce]/20","rounded-full","animate-pulse"],[1,"bg-gradient-to-r","from-[#7826b5]","to-[#7826b5]","dark:from-[#7826b5]","dark:to-[#7826b5]","h-2.5","relative"],[1,"absolute","inset-0","bg-[#7826b5]/20","rounded-full","animate-pulse"],[1,"bg-gradient-to-r","from-[#4f5fad]","to-[#4f5fad]","dark:from-[#6d78c9]","dark:to-[#6d78c9]","h-2.5","relative"],[1,"absolute","inset-0","bg-[#4f5fad]/20","dark:bg-[#6d78c9]/20","rounded-full","animate-pulse"],[1,"flex","justify-between","mt-2","text-xs","text-[#6d6870]","dark:text-[#a0a0a0]"],[1,"bg-white","dark:bg-[#1e1e1e]","rounded-xl","shadow-md","dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)]","overflow-hidden","mb-8","backdrop-blur-sm","border","border-[#edf1f4]/50","dark:border-[#2a2a2a]","relative"],[1,"absolute","top-0","left-0","right-0","h-0.5","bg-gradient-to-r","from-[#3d4a85]","to-[#4f5fad]","dark:from-[#6d78c9]","dark:to-[#4f5fad]"],[1,"p-5","border-b","border-[#edf1f4]/50","dark:border-[#2a2a2a]","flex","items-center","justify-between"],[1,"font-bold","bg-gradient-to-r","from-[#3d4a85]","to-[#4f5fad]","dark:from-[#6d78c9]","dark:to-[#4f5fad]","bg-clip-text","text-transparent","flex","items-center"],[1,"fas","fa-users-cog","mr-2"],[1,"overflow-x-auto"],[1,"w-full","divide-y","divide-[#edf1f4]","dark:divide-[#2a2a2a]","table-fixed"],[1,"bg-[#f8fafc]","dark:bg-[#1a1a1a]"],["scope","col",1,"px-3","py-3","text-left","text-xs","font-medium","text-[#4f5fad]","dark:text-[#6d78c9]","uppercase","tracking-wider","w-[15%]"],["scope","col",1,"px-3","py-3","text-left","text-xs","font-medium","text-[#4f5fad]","dark:text-[#6d78c9]","uppercase","tracking-wider","w-[20%]"],["scope","col",1,"px-2","py-3","text-center","text-xs","font-medium","text-[#4f5fad]","dark:text-[#6d78c9]","uppercase","tracking-wider","w-[8%]"],["scope","col",1,"px-2","py-3","text-center","text-xs","font-medium","text-[#4f5fad]","dark:text-[#6d78c9]","uppercase","tracking-wider","w-[10%]"],["scope","col",1,"px-2","py-3","text-center","text-xs","font-medium","text-[#4f5fad]","dark:text-[#6d78c9]","uppercase","tracking-wider","w-[13%]"],[1,"bg-white","dark:bg-[#1e1e1e]","divide-y","divide-[#edf1f4]","dark:divide-[#2a2a2a]"],["class","hover:bg-[#f8fafc] dark:hover:bg-[#1a1a1a] transition-colors",4,"ngFor","ngForOf"],[1,"hover:bg-[#f8fafc]","dark:hover:bg-[#1a1a1a]","transition-colors"],[1,"px-3","py-3","whitespace-nowrap","truncate"],[1,"flex-shrink-0","h-8","w-8","rounded-full","bg-gradient-to-r","from-[#3d4a85]","to-[#4f5fad]","dark:from-[#6d78c9]","dark:to-[#4f5fad]","text-white","flex","items-center","justify-center","text-xs","shadow-md","relative","group"],[1,"absolute","inset-0","bg-[#4f5fad]/20","dark:bg-[#6d78c9]/20","rounded-full","blur-md","opacity-0","group-hover:opacity-100","transition-opacity"],[1,"ml-3"],[1,"text-sm","font-medium","text-[#4f5fad]","dark:text-[#6d78c9]","truncate","max-w-[120px]"],[1,"px-3","py-3","whitespace-nowrap"],[1,"text-sm","text-[#6d6870]","dark:text-[#a0a0a0]","truncate","max-w-[150px]"],[1,"px-2","py-3","whitespace-nowrap","text-center"],[1,"px-2","py-1","text-xs","rounded-lg","inline-flex","items-center","justify-center",3,"ngClass"],[1,"px-3","py-1.5","text-xs","rounded-full","inline-flex","items-center","justify-center","font-semibold","transition-all","duration-300",3,"ngClass"],[1,"relative","group"],[1,"w-full","px-2","py-1.5","text-xs","rounded-lg","border","border-[#bdc6cc]","dark:border-[#2a2a2a]","bg-white","dark:bg-[#1a1a1a]","text-[#6d6870]","dark:text-[#e0e0e0]","focus:outline-none","focus:border-[#4f5fad]","dark:focus:border-[#6d78c9]","focus:ring-1","focus:ring-[#4f5fad]/20","dark:focus:ring-[#6d78c9]/20","transition-all","appearance-none","pr-7",3,"value","change"],[3,"value",4,"ngFor","ngForOf"],[1,"absolute","inset-y-0","right-0","flex","items-center","pr-2","pointer-events-none","text-[#6d6870]","dark:text-[#a0a0a0]"],[1,"fas","fa-chevron-down","text-xs"],[1,"px-3","py-2","text-xs","rounded-lg","font-semibold","flex","items-center","justify-center","mx-auto","transition-all","duration-300","w-full","relative","overflow-hidden","group","border","shadow-sm","hover:shadow-md","transform","hover:scale-105",3,"ngClass","title","click"],[1,"absolute","inset-0","opacity-0","group-hover:opacity-100","transition-opacity","duration-300",3,"ngClass"],[1,"relative","z-10","mr-2","transition-transform","duration-300","group-hover:scale-110"],["title","Supprimer d\xe9finitivement cet utilisateur",1,"px-2","py-1.5","text-xs","rounded-lg","bg-[#ff6b69]/10","dark:bg-[#ff6b69]/5","text-[#ff6b69]","dark:text-[#ff8785]","hover:bg-[#ff6b69]/20","dark:hover:bg-[#ff6b69]/10","font-medium","flex","items-center","justify-center","mx-auto","transition-all","w-full",3,"click"],[1,"fas","fa-trash-alt","mr-1.5"],[1,"px-2","py-1.5","text-xs","rounded-lg","bg-[#4f5fad]/10","dark:bg-[#6d78c9]/5","text-[#4f5fad]","dark:text-[#6d78c9]","hover:bg-[#4f5fad]/20","dark:hover:bg-[#6d78c9]/10","font-medium","flex","items-center","justify-center","mx-auto","transition-all","w-full",3,"click"],[1,"fas","fa-eye","mr-1.5"],[3,"value"],[1,"p-10","text-center"],[1,"relative","mx-auto","w-20","h-20","mb-6"],[1,"absolute","inset-0","bg-[#4f5fad]/20","dark:bg-[#6d78c9]/20","rounded-full","blur-xl"],[1,"relative","z-10","w-20","h-20","rounded-full","bg-gradient-to-br","from-[#edf1f4]","to-white","dark:from-[#1a1a1a]","dark:to-[#2a2a2a]","flex","items-center","justify-center","shadow-lg"],[1,"fas","fa-users","text-3xl","bg-gradient-to-r","from-[#3d4a85]","to-[#4f5fad]","dark:from-[#6d78c9]","dark:to-[#4f5fad]","bg-clip-text","text-transparent"],[1,"absolute","inset-0","border-2","border-[#4f5fad]/20","dark:border-[#6d78c9]/20","rounded-full","animate-ping","opacity-75"],[1,"absolute","inset-0","border","border-[#4f5fad]/40","dark:border-[#6d78c9]/40","rounded-full","animate-pulse"],[1,"text-xl","font-bold","bg-gradient-to-r","from-[#3d4a85]","to-[#4f5fad]","dark:from-[#6d78c9]","dark:to-[#4f5fad]","bg-clip-text","text-transparent","mb-3"],[1,"text-sm","text-[#6d6870]","dark:text-[#a0a0a0]","max-w-md","mx-auto"],["class","mt-8",4,"ngIf"],[1,"mt-8"],[1,"inline-flex","items-center","px-4","py-2.5","text-sm","relative","overflow-hidden","group",3,"click"],[1,"absolute","inset-0","bg-gradient-to-r","from-[#3d4a85]","to-[#4f5fad]","dark:from-[#3d4a85]","dark:to-[#6d78c9]","rounded-lg","opacity-10","dark:opacity-20","group-hover:opacity-20","dark:group-hover:opacity-30","transition-opacity"],[1,"relative","z-10","flex","items-center","text-[#4f5fad]","dark:text-[#6d78c9]","font-medium"],[1,"fas","fa-times-circle","mr-2"]],template:function(n,a){1&n&&(t.j41(0,"div",0)(1,"div",1),t.nrm(2,"div",2)(3,"div",3),t.j41(4,"div",4)(5,"div",5),t.nrm(6,"div",6)(7,"div",6)(8,"div",6)(9,"div",6)(10,"div",6)(11,"div",6)(12,"div",6)(13,"div",6)(14,"div",6)(15,"div",6)(16,"div",6),t.k0s()()(),t.j41(17,"div",7)(18,"div",8)(19,"div")(20,"h1",9),t.EFF(21," Admin Dashboard "),t.k0s(),t.j41(22,"p",10),t.EFF(23," Manage users and system settings "),t.k0s()(),t.j41(24,"div",11)(25,"div",12),t.nrm(26,"i",13),t.k0s(),t.j41(27,"input",14),t.bIt("input",function(d){return a.searchTerm=d.target.value,a.searchUsers()}),t.k0s(),t.j41(28,"div",15),t.nrm(29,"div",16),t.k0s(),t.DNE(30,i,2,0,"button",17),t.k0s()(),t.DNE(31,o,10,1,"div",18),t.DNE(32,s,10,1,"div",19),t.DNE(33,m,4,0,"div",20),t.DNE(34,k,89,16,"div",21),t.DNE(35,E,28,1,"div",22),t.DNE(36,I,14,2,"div",22),t.k0s()()),2&n&&(t.R7$(27),t.Y8G("value",a.searchTerm),t.R7$(3),t.Y8G("ngIf",a.searchTerm),t.R7$(1),t.Y8G("ngIf",a.message),t.R7$(1),t.Y8G("ngIf",a.error),t.R7$(1),t.Y8G("ngIf",a.loading),t.R7$(1),t.Y8G("ngIf",!a.loading&&a.filteredUsers.length>0),t.R7$(1),t.Y8G("ngIf",!a.loading&&a.filteredUsers.length>0),t.R7$(1),t.Y8G("ngIf",!a.loading&&0===a.filteredUsers.length))},dependencies:[u.YU,u.Sq,u.bT,u.PV],styles:["[_nghost-%COMP%]{display:block}[_nghost-%COMP%]   .min-h-screen[_ngcontent-%COMP%]{background:linear-gradient(135deg,var(--dark-bg),var(--medium-bg));background-image:linear-gradient(rgba(0,247,255,.03) 1px,transparent 1px),linear-gradient(90deg,rgba(0,247,255,.03) 1px,transparent 1px);background-size:20px 20px}.grid[_ngcontent-%COMP%]   .bg-white[_ngcontent-%COMP%]{background:rgba(31,41,55,.7);-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);border:1px solid rgba(0,247,255,.1);box-shadow:0 4px 12px #0003,inset 0 0 0 1px #ffffff0d;transition:all .3s ease}.grid[_ngcontent-%COMP%]   .bg-white[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 6px 16px #0000004d,inset 0 0 0 1px #ffffff1a,0 0 15px #00f7ff33;border-color:#00f7ff4d}.grid[_ngcontent-%COMP%]   .bg-white[_ngcontent-%COMP%]   .h-1\\.5[_ngcontent-%COMP%]{height:4px;background:rgba(0,0,0,.3);overflow:hidden;border-radius:4px}.grid[_ngcontent-%COMP%]   .bg-white[_ngcontent-%COMP%]   .h-1\\.5 div[_ngcontent-%COMP%]{position:relative;overflow:hidden}.grid[_ngcontent-%COMP%]   .bg-white[_ngcontent-%COMP%]   .h-1\\.5 div[_ngcontent-%COMP%]:after{content:\"\";position:absolute;inset:0;background:linear-gradient(90deg,transparent,rgba(255,255,255,.2),transparent);animation:_ngcontent-%COMP%_shimmer 2s infinite}.grid[_ngcontent-%COMP%]   .bg-white[_ngcontent-%COMP%]   .h-1\\.5[_ngcontent-%COMP%]   .bg-primary-light[_ngcontent-%COMP%], .grid[_ngcontent-%COMP%]   .bg-white[_ngcontent-%COMP%]   .h-1\\.5[_ngcontent-%COMP%]   .bg-primary-dark[_ngcontent-%COMP%]{background:linear-gradient(90deg,var(--accent-color-dark),var(--accent-color))}.grid[_ngcontent-%COMP%]   .bg-white[_ngcontent-%COMP%]   .h-1\\.5[_ngcontent-%COMP%]   .bg-green-500[_ngcontent-%COMP%], .grid[_ngcontent-%COMP%]   .bg-white[_ngcontent-%COMP%]   .h-1\\.5[_ngcontent-%COMP%]   .bg-green-600[_ngcontent-%COMP%]{background:linear-gradient(90deg,#059669,#10B981)}.grid[_ngcontent-%COMP%]   .bg-white[_ngcontent-%COMP%]   .h-1\\.5[_ngcontent-%COMP%]   .bg-red-500[_ngcontent-%COMP%], .grid[_ngcontent-%COMP%]   .bg-white[_ngcontent-%COMP%]   .h-1\\.5[_ngcontent-%COMP%]   .bg-red-600[_ngcontent-%COMP%]{background:linear-gradient(90deg,#DC2626,#EF4444)}.grid[_ngcontent-%COMP%]   .bg-white[_ngcontent-%COMP%]   .h-1\\.5[_ngcontent-%COMP%]   .bg-blue-500[_ngcontent-%COMP%], .grid[_ngcontent-%COMP%]   .bg-white[_ngcontent-%COMP%]   .h-1\\.5[_ngcontent-%COMP%]   .bg-blue-600[_ngcontent-%COMP%]{background:linear-gradient(90deg,#3B82F6,#60A5FA)}.grid[_ngcontent-%COMP%]   .bg-white[_ngcontent-%COMP%]   .h-1\\.5[_ngcontent-%COMP%]   .bg-purple-500[_ngcontent-%COMP%], .grid[_ngcontent-%COMP%]   .bg-white[_ngcontent-%COMP%]   .h-1\\.5[_ngcontent-%COMP%]   .bg-purple-600[_ngcontent-%COMP%]{background:linear-gradient(90deg,#8B5CF6,#A78BFA)}.grid[_ngcontent-%COMP%]   .bg-white[_ngcontent-%COMP%]   .h-1\\.5[_ngcontent-%COMP%]   .bg-yellow-500[_ngcontent-%COMP%], .grid[_ngcontent-%COMP%]   .bg-white[_ngcontent-%COMP%]   .h-1\\.5[_ngcontent-%COMP%]   .bg-yellow-600[_ngcontent-%COMP%]{background:linear-gradient(90deg,#F59E0B,#FBBF24)}table[_ngcontent-%COMP%]{width:100%;border-collapse:separate;border-spacing:0}table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]{background:rgba(17,24,39,.7);-webkit-backdrop-filter:blur(4px);backdrop-filter:blur(4px);color:var(--accent-color);font-weight:500;letter-spacing:1px;text-transform:uppercase;padding:12px 16px;position:relative}table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]:first-child{border-top-left-radius:var(--border-radius-md)}table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]:last-child{border-top-right-radius:var(--border-radius-md)}table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]:after{content:\"\";position:absolute;left:0;bottom:0;width:100%;height:1px;background:linear-gradient(90deg,transparent,rgba(0,247,255,.5),transparent)}table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]{transition:all .2s ease}table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover{background:rgba(0,247,255,.05)!important}table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover   td[_ngcontent-%COMP%]{color:#fff}table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]{padding:12px 16px;transition:all .2s ease}table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .flex-shrink-0[_ngcontent-%COMP%]{background:linear-gradient(135deg,var(--accent-color),var(--secondary-color));box-shadow:var(--glow-effect)}.badge[_ngcontent-%COMP%]{display:inline-flex;align-items:center;padding:.25rem .5rem;border-radius:9999px;font-size:.75rem;font-weight:500}.badge.verified[_ngcontent-%COMP%]{background-color:#0596691a;color:#10b981;border:1px solid rgba(16,185,129,.2)}.badge.not-verified[_ngcontent-%COMP%]{background-color:#ef44441a;color:#ef4444;border:1px solid rgba(239,68,68,.2)}.badge.active[_ngcontent-%COMP%]{background-color:#0596691a;color:#10b981;border:1px solid rgba(16,185,129,.2)}.badge.inactive[_ngcontent-%COMP%]{background-color:#9ca3af1a;color:#9ca3af;border:1px solid rgba(156,163,175,.2)}button[_ngcontent-%COMP%]{transition:all .2s ease}button[_ngcontent-%COMP%]:hover{transform:translateY(-1px);box-shadow:var(--glow-effect)}button[_ngcontent-%COMP%]:active{transform:translateY(0)}input[type=text][_ngcontent-%COMP%]{background:rgba(31,41,55,.7);border:1px solid rgba(0,247,255,.1);color:#fff;transition:all .3s ease}input[type=text][_ngcontent-%COMP%]:focus{border-color:var(--accent-color);box-shadow:0 0 0 1px #00f7ff33,var(--glow-effect);outline:none}input[type=text][_ngcontent-%COMP%]::placeholder{color:#9ca3afb3}select[_ngcontent-%COMP%]{background:rgba(31,41,55,.7);border:1px solid rgba(0,247,255,.1);color:#fff;transition:all .3s ease;appearance:none;background-image:url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%2300f7ff'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M19 9l-7 7-7-7'%3E%3C/path%3E%3C/svg%3E\");background-repeat:no-repeat;background-position:right .5rem center;background-size:1rem;padding-right:2rem}select[_ngcontent-%COMP%]:focus{border-color:var(--accent-color);box-shadow:0 0 0 1px #00f7ff33,var(--glow-effect);outline:none}select[_ngcontent-%COMP%]   option[_ngcontent-%COMP%]{background-color:#1f2937}@keyframes _ngcontent-%COMP%_shimmer{0%{transform:translate(-100%)}to{transform:translate(100%)}}.animate-spin[_ngcontent-%COMP%]{border-color:var(--accent-color);box-shadow:0 0 15px #00f7ff80}"]})}}return r})()}];let $=(()=>{class r{static{this.\u0275fac=function(n){return new(n||r)}}static{this.\u0275mod=t.$C({type:r})}static{this.\u0275inj=t.G2t({imports:[h.iI.forChild(T),h.iI]})}}return r})(),D=(()=>{class r{static{this.\u0275fac=function(n){return new(n||r)}}static{this.\u0275mod=t.$C({type:r})}static{this.\u0275inj=t.G2t({imports:[u.MD,$]})}}return r})()}}]);