import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { Subscription } from 'rxjs';
import { CallService } from '../../services/call.service';
import { LoggerService } from '../../services/logger.service';
import { IncomingCall, CallType } from '../../models/message.model';

@Component({
  selector: 'app-incoming-call',
  templateUrl: './incoming-call.component.html',
  styleUrls: ['./incoming-call.component.css'],
})
export class IncomingCallComponent implements OnInit, OnDestroy {
  incomingCall: IncomingCall | null = null;
  isProcessing: boolean = false;

  private subscriptions: Subscription[] = [];

  // Exposer les énums au template
  CallType = CallType;

  constructor(
    private callService: CallService,
    private logger: LoggerService
  ) {}

  ngOnInit(): void {
    this.setupIncomingCallSubscription();
  }

  // === MÉTHODES PRIVÉES DE CONFIGURATION ===

  /**
   * Configure la subscription aux appels entrants
   */
  private setupIncomingCallSubscription(): void {
    const subscription = this.callService.incomingCall$.subscribe({
      next: (call: any) => {
        this.handleIncomingCallUpdate(call);
      },
      error: (error: any) => {
        this.handleSubscriptionError('incoming call subscription', error);
      },
    });

    this.subscriptions.push(subscription);
  }

  /**
   * Gère la mise à jour d'un appel entrant
   */
  private handleIncomingCallUpdate(call: any): void {
    this.incomingCall = call;
    this.isProcessing = false;

    if (call) {
      this.logCallInfo('Incoming call received', {
        callId: call.id,
        caller: call.caller?.username,
        type: call.type,
      });
    }
  }

  /**
   * Gère les erreurs de subscription
   */
  private handleSubscriptionError(source: string, error: any): void {
    console.error(`❌ [IncomingCall] Error in ${source}:`, error);
    this.logger.error(`Error in ${source}:`, error);
  }

  /**
   * Log standardisé pour les informations d'appel
   */
  private logCallInfo(action: string, data: any): void {
    console.log(`📞 [IncomingCall] ${action}:`, data);
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((sub) => sub.unsubscribe());
  }

  // === ACTIONS D'APPEL ===

  /**
   * Accepte l'appel entrant
   */
  acceptCall(): void {
    if (!this.canExecuteAction() || !this.incomingCall) return;

    this.logCallInfo('Accepting call', this.incomingCall.id);
    this.startProcessing();

    this.callService.acceptCall(this.incomingCall).subscribe({
      next: (call: any) => {
        this.handleCallActionSuccess('Call accepted successfully', call);
        // L'interface d'appel actif va automatiquement s'afficher
      },
      error: (error: any) => {
        this.handleCallActionError('accepting call', error);
      },
    });
  }

  /**
   * Rejette l'appel entrant
   */
  rejectCall(): void {
    if (!this.canExecuteAction() || !this.incomingCall) return;

    this.logCallInfo('Rejecting call', this.incomingCall.id);
    this.startProcessing();

    this.callService
      .rejectCall(this.incomingCall.id, 'User rejected')
      .subscribe({
        next: (result: any) => {
          this.handleCallActionSuccess('Call rejected successfully', result);
        },
        error: (error: any) => {
          this.handleCallActionError('rejecting call', error);
        },
      });
  }

  // === MÉTHODES UTILITAIRES D'ACTION ===

  /**
   * Vérifie si une action peut être exécutée
   */
  private canExecuteAction(): boolean {
    return !this.isProcessing;
  }

  /**
   * Démarre le traitement d'une action
   */
  private startProcessing(): void {
    this.isProcessing = true;
  }

  /**
   * Gère le succès d'une action d'appel
   */
  private handleCallActionSuccess(message: string, data: any): void {
    this.logCallInfo(message, data);
    this.isProcessing = false;
  }

  /**
   * Gère l'erreur d'une action d'appel
   */
  private handleCallActionError(action: string, error: any): void {
    console.error(`❌ [IncomingCall] Error ${action}:`, error);
    this.logger.error(`Error ${action}:`, error);
    this.isProcessing = false;

    // Message d'erreur à l'utilisateur
    alert(`Erreur lors de ${action}. Veuillez réessayer.`);
  }

  // === MÉTHODES UTILITAIRES CONSOLIDÉES ===

  /**
   * Obtient le nom de l'appelant
   */
  getCallerName(): string {
    return this.getParticipantName(this.incomingCall?.caller);
  }

  /**
   * Obtient l'avatar de l'appelant
   */
  getCallerAvatar(): string {
    return this.getParticipantAvatar(this.incomingCall?.caller);
  }

  /**
   * Obtient le type d'appel (audio/vidéo)
   */
  getCallTypeText(): string {
    if (!this.incomingCall) return '';
    return this.getTypeText(this.incomingCall.type);
  }

  /**
   * Obtient l'icône du type d'appel
   */
  getCallTypeIcon(): string {
    if (!this.incomingCall) return 'fa-phone';
    return this.getTypeIcon(this.incomingCall.type);
  }

  /**
   * Vérifie si c'est un appel vidéo
   */
  isVideoCall(): boolean {
    return this.incomingCall?.type === CallType.VIDEO;
  }

  // === MÉTHODES UTILITAIRES PRIVÉES (RÉUTILISABLES) ===

  /**
   * Obtient le nom d'un participant avec fallback
   */
  private getParticipantName(participant: any): string {
    return participant?.username || 'Utilisateur inconnu';
  }

  /**
   * Obtient l'avatar d'un participant avec fallback
   */
  private getParticipantAvatar(participant: any): string {
    return participant?.image || '/assets/images/default-avatar.png';
  }

  /**
   * Obtient le texte du type d'appel
   */
  private getTypeText(type: CallType): string {
    return type === CallType.VIDEO ? 'Appel vidéo' : 'Appel audio';
  }

  /**
   * Obtient l'icône du type d'appel
   */
  private getTypeIcon(type: CallType): string {
    return type === CallType.VIDEO ? 'fa-video' : 'fa-phone';
  }
}
