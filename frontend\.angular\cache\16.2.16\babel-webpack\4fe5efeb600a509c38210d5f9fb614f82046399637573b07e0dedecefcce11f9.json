{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/OneDrive/Bureau/Project PI/devBridge/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { BehaviorSubject, Observable, throwError } from 'rxjs';\nimport { map, catchError } from 'rxjs/operators';\nimport { CallStatus } from '../models/message.model';\nimport { REJECT_CALL_MUTATION, END_CALL_MUTATION, TOGGLE_CALL_MEDIA_MUTATION, INCOMING_CALL_SUBSCRIPTION, CALL_STATUS_CHANGED_SUBSCRIPTION, CALL_SIGNAL_SUBSCRIPTION } from '../graphql/message.graphql';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"apollo-angular\";\nimport * as i2 from \"./logger.service\";\nexport let CallService = /*#__PURE__*/(() => {\n  class CallService {\n    constructor(apollo, logger) {\n      this.apollo = apollo;\n      this.logger = logger;\n      // État des appels\n      this.activeCall = new BehaviorSubject(null);\n      this.incomingCall = new BehaviorSubject(null);\n      // Observables publics\n      this.activeCall$ = this.activeCall.asObservable();\n      this.incomingCall$ = this.incomingCall.asObservable();\n      // Propriétés pour la gestion des sons\n      this.sounds = {};\n      this.isPlaying = {};\n      // États simples pour les médias\n      this.isVideoEnabled = true;\n      this.isAudioEnabled = true;\n      // WebRTC\n      this.peerConnection = null;\n      this.localStream = null;\n      this.remoteStream = null;\n      this.localVideoElement = null;\n      this.remoteVideoElement = null;\n      this.currentCallId = null;\n      this.webrtcExchangeStarted = false;\n      this.initializeSounds();\n      this.initializeSubscriptions();\n      this.initializeWebRTC();\n    }\n    /**\n     * Initialise les sons\n     */\n    initializeSounds() {\n      this.createSyntheticSounds();\n    }\n    /**\n     * Crée des sons synthétiques\n     */\n    createSyntheticSounds() {\n      try {\n        const audioContext = new (window.AudioContext || window.webkitAudioContext)();\n        this.sounds['ringtone'] = this.createRingtoneSound(audioContext);\n        this.sounds['call-connected'] = this.createConnectedSound(audioContext);\n        this.sounds['call-end'] = this.createEndSound(audioContext);\n        this.sounds['notification'] = this.createNotificationSound(audioContext);\n      } catch (error) {\n        console.warn('Could not create synthetic sounds:', error);\n      }\n    }\n    /**\n     * Crée une sonnerie\n     */\n    createRingtoneSound(audioContext) {\n      const audio = new Audio();\n      audio.volume = 0.5;\n      let isPlaying = false;\n      let timeoutIds = [];\n      audio.playSynthetic = () => {\n        if (isPlaying) return Promise.resolve();\n        isPlaying = true;\n        const playMelody = () => {\n          if (!isPlaying) return;\n          const melody = [{\n            freq: 659.25,\n            duration: 0.125\n          }, {\n            freq: 587.33,\n            duration: 0.125\n          }, {\n            freq: 739.99,\n            duration: 0.25\n          }, {\n            freq: 783.99,\n            duration: 0.25\n          }];\n          let currentTime = audioContext.currentTime;\n          melody.forEach(note => {\n            if (!isPlaying) return;\n            const oscillator = audioContext.createOscillator();\n            const gainNode = audioContext.createGain();\n            oscillator.connect(gainNode);\n            gainNode.connect(audioContext.destination);\n            oscillator.frequency.value = note.freq;\n            oscillator.type = 'square';\n            gainNode.gain.setValueAtTime(0, currentTime);\n            gainNode.gain.linearRampToValueAtTime(0.3, currentTime + 0.02);\n            gainNode.gain.exponentialRampToValueAtTime(0.01, currentTime + note.duration);\n            oscillator.start(currentTime);\n            oscillator.stop(currentTime + note.duration);\n            currentTime += note.duration + 0.05;\n          });\n          const timeoutId = setTimeout(() => {\n            if (isPlaying) playMelody();\n          }, (currentTime - audioContext.currentTime + 0.8) * 1000);\n          timeoutIds.push(timeoutId);\n        };\n        playMelody();\n        return Promise.resolve();\n      };\n      audio.stopSynthetic = () => {\n        isPlaying = false;\n        timeoutIds.forEach(id => clearTimeout(id));\n        timeoutIds = [];\n      };\n      return audio;\n    }\n    /**\n     * Crée un son de connexion\n     */\n    createConnectedSound(audioContext) {\n      const audio = new Audio();\n      audio.volume = 0.5;\n      audio.playSynthetic = () => {\n        const melody = [{\n          freq: 523.25,\n          duration: 0.15\n        }, {\n          freq: 659.25,\n          duration: 0.15\n        }, {\n          freq: 783.99,\n          duration: 0.15\n        }, {\n          freq: 1046.5,\n          duration: 0.4\n        }];\n        let currentTime = audioContext.currentTime;\n        melody.forEach(note => {\n          const oscillator = audioContext.createOscillator();\n          const gainNode = audioContext.createGain();\n          oscillator.connect(gainNode);\n          gainNode.connect(audioContext.destination);\n          oscillator.frequency.value = note.freq;\n          oscillator.type = 'triangle';\n          gainNode.gain.setValueAtTime(0, currentTime);\n          gainNode.gain.linearRampToValueAtTime(0.25, currentTime + 0.02);\n          gainNode.gain.exponentialRampToValueAtTime(0.01, currentTime + note.duration);\n          oscillator.start(currentTime);\n          oscillator.stop(currentTime + note.duration);\n          currentTime += note.duration * 0.8;\n        });\n        return Promise.resolve();\n      };\n      return audio;\n    }\n    /**\n     * Crée un son de fin d'appel\n     */\n    createEndSound(audioContext) {\n      const audio = new Audio();\n      audio.volume = 0.4;\n      audio.playSynthetic = () => {\n        const melody = [{\n          freq: 783.99,\n          duration: 0.2\n        }, {\n          freq: 659.25,\n          duration: 0.2\n        }, {\n          freq: 523.25,\n          duration: 0.2\n        }, {\n          freq: 392.0,\n          duration: 0.4\n        }];\n        let currentTime = audioContext.currentTime;\n        melody.forEach(note => {\n          const oscillator = audioContext.createOscillator();\n          const gainNode = audioContext.createGain();\n          oscillator.connect(gainNode);\n          gainNode.connect(audioContext.destination);\n          oscillator.frequency.value = note.freq;\n          oscillator.type = 'sine';\n          gainNode.gain.setValueAtTime(0, currentTime);\n          gainNode.gain.linearRampToValueAtTime(0.2, currentTime + 0.05);\n          gainNode.gain.exponentialRampToValueAtTime(0.01, currentTime + note.duration);\n          oscillator.start(currentTime);\n          oscillator.stop(currentTime + note.duration);\n          currentTime += note.duration * 0.9;\n        });\n        return Promise.resolve();\n      };\n      return audio;\n    }\n    /**\n     * Crée un son de notification\n     */\n    createNotificationSound(audioContext) {\n      const audio = new Audio();\n      audio.volume = 0.6;\n      audio.playSynthetic = () => {\n        const notes = [{\n          freq: 523.25,\n          duration: 0.15,\n          delay: 0\n        }, {\n          freq: 783.99,\n          duration: 0.25,\n          delay: 0.2\n        }];\n        notes.forEach(note => {\n          setTimeout(() => {\n            const oscillator = audioContext.createOscillator();\n            const gainNode = audioContext.createGain();\n            oscillator.connect(gainNode);\n            gainNode.connect(audioContext.destination);\n            oscillator.frequency.value = note.freq;\n            oscillator.type = 'triangle';\n            const startTime = audioContext.currentTime;\n            gainNode.gain.setValueAtTime(0, startTime);\n            gainNode.gain.linearRampToValueAtTime(0.4, startTime + 0.02);\n            gainNode.gain.exponentialRampToValueAtTime(0.01, startTime + note.duration);\n            oscillator.start(startTime);\n            oscillator.stop(startTime + note.duration);\n          }, note.delay * 1000);\n        });\n        return Promise.resolve();\n      };\n      return audio;\n    }\n    /**\n     * Joue un son\n     */\n    play(soundName, loop = false) {\n      const sound = this.sounds[soundName];\n      if (!sound) return;\n      this.isPlaying[soundName] = true;\n      if (sound.playSynthetic) {\n        sound.playSynthetic();\n        if (loop) {\n          const interval = setInterval(() => {\n            if (this.isPlaying[soundName]) {\n              sound.playSynthetic();\n            } else {\n              clearInterval(interval);\n            }\n          }, 3000);\n        }\n      }\n    }\n    /**\n     * Arrête un son\n     */\n    stop(soundName) {\n      this.isPlaying[soundName] = false;\n      const sound = this.sounds[soundName];\n      if (sound && sound.stopSynthetic) {\n        sound.stopSynthetic();\n      }\n    }\n    /**\n     * Arrête tous les sons\n     */\n    stopAllSounds() {\n      Object.keys(this.sounds).forEach(name => {\n        this.stop(name);\n      });\n    }\n    /**\n     * Initialise les subscriptions\n     */\n    initializeSubscriptions() {\n      setTimeout(() => {\n        this.subscribeToIncomingCalls();\n        this.subscribeToCallStatusChanges();\n        this.subscribeToCallSignals();\n      }, 1000);\n    }\n    /**\n     * S'abonne aux appels entrants\n     */\n    subscribeToIncomingCalls() {\n      this.apollo.subscribe({\n        query: INCOMING_CALL_SUBSCRIPTION,\n        errorPolicy: 'all'\n      }).subscribe({\n        next: ({\n          data,\n          errors\n        }) => {\n          if (data?.incomingCall) {\n            this.handleIncomingCall(data.incomingCall);\n          }\n        },\n        error: error => {\n          console.error('Error in incoming call subscription:', error);\n          setTimeout(() => this.subscribeToIncomingCalls(), 5000);\n        }\n      });\n    }\n    /**\n     * S'abonne aux changements de statut d'appel\n     */\n    subscribeToCallStatusChanges() {\n      this.apollo.subscribe({\n        query: CALL_STATUS_CHANGED_SUBSCRIPTION,\n        errorPolicy: 'all'\n      }).subscribe({\n        next: ({\n          data,\n          errors\n        }) => {\n          if (data?.callStatusChanged) {\n            this.handleCallStatusChange(data.callStatusChanged);\n          }\n        },\n        error: error => {\n          console.error('Error in call status subscription:', error);\n          setTimeout(() => this.subscribeToCallStatusChanges(), 5000);\n        }\n      });\n    }\n    /**\n     * S'abonne aux signaux d'appel\n     */\n    subscribeToCallSignals() {\n      this.apollo.subscribe({\n        query: CALL_SIGNAL_SUBSCRIPTION,\n        errorPolicy: 'all'\n      }).subscribe({\n        next: ({\n          data,\n          errors\n        }) => {\n          if (data?.callSignal) {\n            this.handleCallSignal(data.callSignal);\n          }\n        },\n        error: error => {\n          console.error('Error in call signal subscription:', error);\n          setTimeout(() => this.subscribeToCallSignals(), 5000);\n        }\n      });\n    }\n    /**\n     * Gère un appel entrant\n     */\n    handleIncomingCall(call) {\n      this.incomingCall.next(call);\n      this.play('ringtone', true);\n    }\n    /**\n     * Gère les changements de statut d'appel\n     */\n    handleCallStatusChange(call) {\n      switch (call.status) {\n        case CallStatus.CONNECTED:\n          this.stop('ringtone');\n          this.play('call-connected');\n          this.activeCall.next(call);\n          this.incomingCall.next(null);\n          break;\n        case CallStatus.ENDED:\n          this.play('call-end');\n          this.activeCall.next(null);\n          this.incomingCall.next(null);\n          this.cleanupWebRTC();\n          break;\n        case CallStatus.REJECTED:\n          this.stop('ringtone');\n          this.play('call-end');\n          this.activeCall.next(null);\n          this.incomingCall.next(null);\n          break;\n      }\n    }\n    /**\n     * Gère les signaux d'appel\n     */\n    handleCallSignal(signal) {\n      if (!this.peerConnection) return;\n      switch (signal.type) {\n        case 'offer':\n          this.handleRemoteOffer(signal);\n          break;\n        case 'answer':\n          this.handleRemoteAnswer(signal);\n          break;\n        case 'ice-candidate':\n          this.handleRemoteICECandidate(signal);\n          break;\n      }\n    }\n    /**\n     * Initialise WebRTC\n     */\n    initializeWebRTC() {\n      const configuration = {\n        iceServers: [{\n          urls: 'stun:stun.l.google.com:19302'\n        }, {\n          urls: 'stun:stun1.l.google.com:19302'\n        }]\n      };\n      try {\n        this.peerConnection = new RTCPeerConnection(configuration);\n        this.setupPeerConnectionEvents();\n      } catch (error) {\n        console.error('Failed to initialize WebRTC:', error);\n      }\n    }\n    /**\n     * Configure les événements de la PeerConnection\n     */\n    setupPeerConnectionEvents() {\n      if (!this.peerConnection) return;\n      this.peerConnection.ontrack = event => {\n        this.remoteStream = event.streams[0];\n        this.attachRemoteStream();\n      };\n      this.peerConnection.onicecandidate = event => {\n        if (event.candidate) {\n          this.sendSignal('ice-candidate', JSON.stringify(event.candidate));\n        }\n      };\n    }\n    /**\n     * Attache le stream distant\n     */\n    attachRemoteStream() {\n      if (this.remoteStream && this.remoteVideoElement) {\n        this.remoteVideoElement.srcObject = this.remoteStream;\n        this.remoteVideoElement.muted = false;\n        this.remoteVideoElement.volume = 1;\n        this.remoteVideoElement.play();\n      }\n      // Attacher à tous les éléments vidéo disponibles\n      const videos = document.querySelectorAll('video');\n      for (let i = 1; i < videos.length; i++) {\n        if (this.remoteStream && !videos[i].srcObject) {\n          videos[i].srcObject = this.remoteStream;\n          videos[i].muted = false;\n          videos[i].volume = 1;\n          videos[i].play();\n          break;\n        }\n      }\n    }\n    /**\n     * Initie un appel - VERSION TEMPORAIRE SANS BACKEND\n     */\n    initiateCall(recipientId, callType, conversationId) {\n      console.log('🔄 [CallService] Initiating call (temporary version):', {\n        recipientId,\n        callType,\n        conversationId\n      });\n      // Créer un appel factice pour les tests\n      const fakeCall = {\n        id: 'call_' + Date.now(),\n        caller: {\n          id: 'current-user',\n          username: 'You',\n          image: '/assets/images/default-avatar.png'\n        },\n        recipient: {\n          id: recipientId,\n          username: 'Other User',\n          image: '/assets/images/default-avatar.png'\n        },\n        type: callType,\n        status: CallStatus.RINGING,\n        startTime: new Date().toISOString(),\n        conversationId: conversationId || ''\n      };\n      // Simuler un délai réseau\n      return new Observable(observer => {\n        setTimeout(() => {\n          console.log('✅ [CallService] Fake call created:', fakeCall);\n          this.activeCall.next(fakeCall);\n          this.currentCallId = fakeCall.id;\n          // Jouer la sonnerie\n          this.play('ringtone', true);\n          // Simuler un appel entrant après 3 secondes pour tester l'acceptation\n          setTimeout(() => {\n            this.simulateIncomingCall(fakeCall);\n          }, 3000);\n          observer.next(fakeCall);\n          observer.complete();\n        }, 500);\n      });\n      // VERSION AVEC BACKEND (à réactiver quand le backend sera prêt)\n      /*\n      const callId = 'call_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);\n           return this.apollo\n        .mutate<{ initiateCall: Call }>({\n          mutation: INITIATE_CALL_MUTATION,\n          variables: {\n            recipientId,\n            callType,\n            callId,\n            offer: '',\n            conversationId,\n          },\n        })\n        .pipe(\n          map((result) => {\n            if (!result.data?.initiateCall) {\n              throw new Error('No call data received from server');\n            }\n            const call = result.data.initiateCall;\n            this.activeCall.next(call);\n            this.currentCallId = call.id;\n            return call;\n          }),\n          catchError((error) => {\n            console.error('Error initiating call:', error);\n            return throwError(() => new Error(\"Erreur lors de l'initiation de l'appel: \" + error.message));\n          })\n        );\n      */\n    }\n    /**\n     * Accepte un appel - VERSION TEMPORAIRE\n     */\n    acceptCall(incomingCall) {\n      console.log('🔄 [CallService] Accepting call (temporary version):', incomingCall);\n      // Créer un appel accepté factice\n      const acceptedCall = {\n        id: incomingCall.id,\n        caller: incomingCall.caller,\n        recipient: {\n          id: 'current-user',\n          username: 'You',\n          image: '/assets/images/default-avatar.png'\n        },\n        type: incomingCall.type,\n        status: CallStatus.CONNECTED,\n        startTime: new Date().toISOString(),\n        conversationId: incomingCall.conversationId || ''\n      };\n      return new Observable(observer => {\n        setTimeout(() => {\n          console.log('✅ [CallService] Call accepted (fake):', acceptedCall);\n          this.stop('ringtone');\n          this.play('call-connected');\n          this.activeCall.next(acceptedCall);\n          this.incomingCall.next(null);\n          observer.next(acceptedCall);\n          observer.complete();\n        }, 300);\n      });\n    }\n    /**\n     * Rejette un appel\n     */\n    rejectCall(callId, reason) {\n      return this.apollo.mutate({\n        mutation: REJECT_CALL_MUTATION,\n        variables: {\n          callId,\n          reason: reason || 'User rejected'\n        }\n      }).pipe(map(result => {\n        this.stop('ringtone');\n        this.incomingCall.next(null);\n        this.activeCall.next(null);\n        return result.data.rejectCall;\n      }), catchError(error => {\n        console.error('Error rejecting call:', error);\n        return throwError(() => new Error(\"Erreur lors du rejet de l'appel\"));\n      }));\n    }\n    /**\n     * Termine un appel\n     */\n    endCall(callId) {\n      return this.apollo.mutate({\n        mutation: END_CALL_MUTATION,\n        variables: {\n          callId,\n          feedback: null\n        }\n      }).pipe(map(result => {\n        this.play('call-end');\n        this.activeCall.next(null);\n        this.incomingCall.next(null);\n        this.cleanupWebRTC();\n        return result.data.endCall;\n      }), catchError(error => {\n        console.error('Error ending call:', error);\n        return throwError(() => new Error(\"Erreur lors de la fin de l'appel\"));\n      }));\n    }\n    /**\n     * Configure les éléments vidéo\n     */\n    setVideoElements(localVideo, remoteVideo) {\n      this.localVideoElement = localVideo;\n      this.remoteVideoElement = remoteVideo;\n    }\n    /**\n     * Envoie un signal\n     */\n    sendSignal(type, data) {\n      // Simulation pour les tests\n      setTimeout(() => {\n        this.handleCallSignal({\n          callId: this.currentCallId,\n          senderId: 'other-user',\n          type,\n          data\n        });\n      }, 100);\n    }\n    /**\n     * Gère une offre distante\n     */\n    handleRemoteOffer(signal) {\n      return _asyncToGenerator(function* () {})();\n    } // Implémentation simplifiée\n    /**\n     * Gère une réponse distante\n     */\n    handleRemoteAnswer(signal) {\n      return _asyncToGenerator(function* () {})();\n    } // Implémentation simplifiée\n    /**\n     * Gère un candidat ICE distant\n     */\n    handleRemoteICECandidate(signal) {\n      return _asyncToGenerator(function* () {})();\n    } // Implémentation simplifiée\n    /**\n     * Nettoie les ressources WebRTC\n     */\n    cleanupWebRTC() {\n      if (this.localStream) {\n        this.localStream.getTracks().forEach(track => track.stop());\n        this.localStream = null;\n      }\n      if (this.peerConnection) {\n        this.peerConnection.close();\n        this.peerConnection = null;\n      }\n      this.remoteStream = null;\n    }\n    /**\n     * Active les sons après interaction utilisateur\n     */\n    enableSounds() {\n      console.log('Sounds enabled after user interaction');\n    }\n    /**\n     * Bascule l'audio\n     */\n    toggleAudio() {\n      this.isAudioEnabled = !this.isAudioEnabled;\n      return this.isAudioEnabled;\n    }\n    /**\n     * Bascule la vidéo\n     */\n    toggleVideo() {\n      this.isVideoEnabled = !this.isVideoEnabled;\n      return this.isVideoEnabled;\n    }\n    /**\n     * Bascule les médias\n     */\n    toggleMedia(callId, enableVideo, enableAudio) {\n      return this.apollo.mutate({\n        mutation: TOGGLE_CALL_MEDIA_MUTATION,\n        variables: {\n          callId,\n          video: enableVideo,\n          audio: enableAudio\n        }\n      }).pipe(map(result => {\n        return result.data.toggleCallMedia;\n      }), catchError(error => {\n        console.error('Error toggling media:', error);\n        return throwError(() => new Error('Erreur lors du changement des médias'));\n      }));\n    }\n    /**\n     * Simule un appel entrant (pour les tests)\n     */\n    simulateIncomingCall(originalCall) {\n      const incomingCall = {\n        id: originalCall.id,\n        caller: originalCall.recipient,\n        type: originalCall.type,\n        conversationId: originalCall.conversationId,\n        offer: '{\"type\":\"offer\",\"sdp\":\"fake-offer\"}',\n        timestamp: new Date().toISOString()\n      };\n      console.log('📞 [CallService] Simulating incoming call:', incomingCall);\n      this.stop('ringtone'); // Arrêter la sonnerie sortante\n      this.incomingCall.next(incomingCall);\n      this.activeCall.next(null); // Nettoyer l'appel sortant\n      // Jouer la sonnerie d'appel entrant\n      this.play('ringtone', true);\n    }\n    ngOnDestroy() {\n      this.stopAllSounds();\n      this.cleanupWebRTC();\n    }\n    static {\n      this.ɵfac = function CallService_Factory(t) {\n        return new (t || CallService)(i0.ɵɵinject(i1.Apollo), i0.ɵɵinject(i2.LoggerService));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: CallService,\n        factory: CallService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return CallService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}