"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[644],{8448:(Ee,oe,D)=>{D.d(oe,{I:()=>j});var a=D(7705),l=D(345);let j=(()=>{class N{constructor(T){this.sanitizer=T}transform(T){if(!T)return this.sanitizer.bypassSecurityTrustHtml("");const x=T.replace(/\(presence obligatoire\)/gi,'<span class="text-red-600 font-semibold">(presence obligatoire)</span>');return this.sanitizer.bypassSecurityTrustHtml(x)}static{this.\u0275fac=function(x){return new(x||N)(a.rXU(l.up,16))}}static{this.\u0275pipe=a.EJ8({name:"highlightPresence",type:N,pure:!0})}}return N})()},1683:(Ee,oe,D)=>{D.d(oe,{Y:()=>j});var a=D(177),l=D(7705);let j=(()=>{class N{static{this.\u0275fac=function(x){return new(x||N)}}static{this.\u0275mod=l.$C({type:N})}static{this.\u0275inj=l.G2t({imports:[a.MD]})}}return N})()},6543:(Ee,oe,D)=>{D.d(oe,{z:()=>k});var a=D(1626),l=D(8810),j=D(8141),N=D(9437),ee=D(5312),T=D(7705),x=D(7552);let k=(()=>{class K{constructor(H,G){this.http=H,this.jwtHelper=G}getUserHeaders(){const H=localStorage.getItem("token");if(!H||this.jwtHelper.isTokenExpired(H))throw new Error("Token invalide ou expir\xe9");return new a.Lr({Authorization:`Bearer ${H||""}`,"Content-Type":"application/json"})}getAdminHeaders(){const H=localStorage.getItem("token");if(!H||this.jwtHelper.isTokenExpired(H))throw new Error("Token invalide ou expir\xe9");return new a.Lr({Authorization:`Bearer ${H}`,role:"admin","Content-Type":"application/json"})}getAllPlannings(){return console.log("Service - R\xe9cup\xe9ration de tous les plannings"),this.http.get(`${ee.c.urlBackend}plannings/getall`,{headers:this.getUserHeaders()}).pipe((0,j.M)(H=>{console.log("Service - Plannings r\xe9cup\xe9r\xe9s:",H)}),(0,N.W)(H=>(console.error("Service - Erreur lors de la r\xe9cup\xe9ration des plannings:",H),(0,l.$)(()=>H))))}getPlanningById(H){return this.http.get(`${ee.c.urlBackend}plannings/getone/${H}`)}createPlanning(H){return this.http.post(`${ee.c.urlBackend}plannings/add`,H,{headers:this.getUserHeaders()})}updatePlanning(H,G){console.log("Service - Mise \xe0 jour du planning:",H),console.log("Service - Donn\xe9es envoy\xe9es:",G),console.log("Service - URL:",`${ee.c.urlBackend}plannings/update/${H}`);try{const le=this.getUserHeaders();return console.log("Service - Headers:",le),this.http.put(`${ee.c.urlBackend}plannings/update/${H}`,G,{headers:le}).pipe((0,j.M)(de=>{console.log("Service - R\xe9ponse du serveur:",de)}),(0,N.W)(de=>(console.error("Service - Erreur lors de la mise \xe0 jour:",de),(0,l.$)(()=>de))))}catch(le){return console.error("Service - Erreur lors de la pr\xe9paration de la requ\xeate:",le),(0,l.$)(()=>new Error("Erreur d'authentification: "+(le instanceof Error?le.message:String(le))))}}deletePlanning(H){return this.http.delete(`${ee.c.urlBackend}plannings/delete/${H}`,{headers:this.getUserHeaders()})}getPlanningsByUser(H){return this.http.get(`${ee.c.urlBackend}plannings/user/${H}`,{headers:this.getUserHeaders()}).pipe((0,j.M)(G=>{console.log("Service - Plannings par utilisateur r\xe9cup\xe9r\xe9s:",G)}),(0,N.W)(G=>(console.error("Service - Erreur lors de la r\xe9cup\xe9ration des plannings par utilisateur:",G),(0,l.$)(()=>G))))}getPlanningWithReunions(H){return this.http.get(`${ee.c.urlBackend}plannings/with-reunions/${H}`,{headers:this.getUserHeaders()})}getAllPlanningsAdmin(){return this.http.get(`${ee.c.urlBackend}plannings/admin/all`,{headers:this.getAdminHeaders()}).pipe((0,j.M)(H=>{console.log("Service - Tous les plannings (admin) r\xe9cup\xe9r\xe9s:",H)}),(0,N.W)(H=>(console.error("Service - Erreur lors de la r\xe9cup\xe9ration des plannings admin:",H),(0,l.$)(()=>H))))}static{this.\u0275fac=function(G){return new(G||K)(T.KVO(a.Qq),T.KVO(x.X7))}}static{this.\u0275prov=T.jDH({token:K,factory:K.\u0275fac,providedIn:"root"})}}return K})()},78:(Ee,oe,D)=>{D.d(oe,{C:()=>ee});var a=D(1626),l=D(5312),j=D(7705),N=D(7552);let ee=(()=>{class T{constructor(k,K){this.http=k,this.jwtHelper=K}getUserHeaders(){const k=localStorage.getItem("token");if(!k||this.jwtHelper.isTokenExpired(k))throw new Error("Token invalide ou expir\xe9");return new a.Lr({Authorization:`Bearer ${k||""}`,"Content-Type":"application/json"})}getAllReunions(){return this.http.get(`${l.c.urlBackend}reunions/getall`)}getReunionById(k){return this.http.get(`${l.c.urlBackend}reunions/getone/${k}`)}createReunion(k){return this.http.post(`${l.c.urlBackend}reunions/add`,k,{headers:this.getUserHeaders()})}updateReunion(k,K){return this.http.put(`${l.c.urlBackend}reunions/update/${k}`,K,{headers:this.getUserHeaders()})}deleteReunion(k){return this.http.delete(`${l.c.urlBackend}reunions/delete/${k}`,{headers:this.getUserHeaders()})}checkLienVisioUniqueness(k,K){return this.http.post(`${l.c.urlBackend}reunions/check-lien-visio`,{lienVisio:k,excludeReunionId:K},{headers:this.getUserHeaders()})}getReunionsByPlanning(k){return this.http.get(`${l.c.urlBackend}reunions/planning/${k}`)}getProchainesReunions(k){return this.http.get(`${l.c.urlBackend}reunions/user/${k}`)}getAllReunionsAdmin(){return this.http.get(`${l.c.urlBackend}reunions/admin/all`,{headers:this.getUserHeaders()})}forceDeleteReunion(k){return this.http.delete(`${l.c.urlBackend}reunions/admin/force-delete/${k}`,{headers:this.getUserHeaders()})}static{this.\u0275fac=function(K){return new(K||T)(j.KVO(a.Qq),j.KVO(N.X7))}}static{this.\u0275prov=j.jDH({token:T,factory:T.\u0275fac,providedIn:"root"})}}return T})()},8397:(Ee,oe,D)=>{D.d(oe,{f:()=>j});var a=D(4412),l=D(7705);let j=(()=>{class N{constructor(){this.toastsSubject=new a.t([]),this.toasts$=this.toastsSubject.asObservable(),this.currentId=0}generateId(){return Math.random().toString(36).substr(2,9)}addToast(T){const x={...T,id:this.generateId(),duration:T.duration||5e3};this.toastsSubject.next([...this.toastsSubject.value,x]),x.duration&&x.duration>0&&setTimeout(()=>{this.removeToast(x.id)},x.duration)}show(T,x="info",k=5e3){const K=this.generateId();this.toastsSubject.next([...this.toastsSubject.value,{id:K,type:x,title:"",message:T,duration:k}]),k>0&&setTimeout(()=>this.dismiss(K),k)}showSuccess(T,x=3e3){this.show(T,"success",x)}showError(T,x=5e3){this.show(T,"error",x)}showWarning(T,x=4e3){this.show(T,"warning",x)}showInfo(T,x=3e3){this.show(T,"info",x)}dismiss(T){const x=this.toastsSubject.value.filter(k=>k.id!==T);this.toastsSubject.next(x)}success(T,x,k){this.addToast({type:"success",title:T,message:x,duration:k,icon:"check-circle"})}error(T,x,k,K){this.addToast({type:"error",title:T,message:x,duration:k||8e3,icon:"x-circle",action:K})}warning(T,x,k){this.addToast({type:"warning",title:T,message:x,duration:k,icon:"exclamation-triangle"})}accessDenied(T="effectuer cette action",x){this.error("Acc\xe8s refus\xe9",`Vous n'avez pas les permissions n\xe9cessaires pour ${T}${x?` (Code: ${x})`:""}`,8e3,{label:"Comprendre les r\xf4les",handler:()=>{console.log("Redirection vers l'aide sur les r\xf4les")}})}ownershipRequired(T="cette ressource"){this.error("Propri\xe9taire requis",`Seul le propri\xe9taire ou un administrateur peut modifier ${T}`,8e3)}removeToast(T){this.toastsSubject.next(this.toastsSubject.value.filter(k=>k.id!==T))}clear(){this.toastsSubject.next([])}static{this.\u0275fac=function(x){return new(x||N)}}static{this.\u0275prov=l.jDH({token:N,factory:N.\u0275fac,providedIn:"root"})}}return N})()},652:(Ee,oe,D)=>{D.d(oe,{v:()=>xe});var a=D(4341),l=D(7705),j=D(6543),N=D(8490),ee=D(6647),T=D(8397),x=D(177);function k(U,se){if(1&U&&(l.j41(0,"div",49),l.EFF(1),l.k0s()),2&U){const I=l.XpG();l.R7$(1),l.SpI(" ",I.error," ")}}function K(U,se){1&U&&(l.j41(0,"span"),l.EFF(1,"Le titre est obligatoire"),l.k0s())}function pe(U,se){1&U&&(l.j41(0,"span"),l.EFF(1,"Au moins 3 caract\xe8res requis"),l.k0s())}function H(U,se){if(1&U&&(l.j41(0,"div",50),l.nrm(1,"i",51),l.DNE(2,K,2,0,"span",52),l.DNE(3,pe,2,0,"span",52),l.k0s()),2&U){const I=l.XpG();let P,$;l.R7$(2),l.Y8G("ngIf",null==(P=I.planningForm.get("titre"))||null==P.errors?null:P.errors.required),l.R7$(1),l.Y8G("ngIf",null==($=I.planningForm.get("titre"))||null==$.errors?null:$.errors.minlength)}}function G(U,se){if(1&U&&(l.j41(0,"option",53),l.EFF(1),l.k0s()),2&U){const I=se.$implicit;l.Y8G("value",I._id),l.R7$(1),l.SpI(" ",I.username," ")}}function le(U,se){1&U&&(l.j41(0,"div",50),l.nrm(1,"i",51),l.EFF(2," Veuillez s\xe9lectionner au moins un participant "),l.k0s())}function de(U,se){1&U&&l.nrm(0,"i",54)}function Ge(U,se){1&U&&l.nrm(0,"i",55)}let xe=(()=>{class U{constructor(I,P,$,re,Q,ne){this.fb=I,this.planningService=P,this.userService=$,this.route=re,this.router=Q,this.toastService=ne,this.users$=this.userService.getAllUsers(),this.error="",this.isLoading=!1}ngOnInit(){this.planningId=this.route.snapshot.paramMap.get("id"),this.initForm(),this.loadPlanning()}initForm(){this.planningForm=this.fb.group({titre:["",[a.k0.required,a.k0.minLength(3)]],description:[""],dateDebut:["",a.k0.required],dateFin:["",a.k0.required],lieu:[""],participants:[[],a.k0.required]})}loadPlanning(){this.planningService.getPlanningById(this.planningId).subscribe({next:I=>{const P=I.planning;this.planningForm.patchValue({titre:P.titre,description:P.description,dateDebut:P.dateDebut,dateFin:P.dateFin,lieu:P.lieu});const $=this.planningForm.get("participants");$.clear(),P.participants.forEach(re=>{$.push(this.fb.control(re._id))})},error:I=>{console.error("Erreur lors du chargement du planning:",I),this.toastService.showError(403===I.status?"Acc\xe8s refus\xe9 : vous n'avez pas les droits pour acc\xe9der \xe0 ce planning":404===I.status?"Le planning demand\xe9 n'existe pas ou a \xe9t\xe9 supprim\xe9":I.error?.message||"Erreur lors du chargement du planning")}})}onSubmit(){if(this.planningForm.invalid)return console.log("Formulaire invalide, soumission annul\xe9e"),this.markFormGroupTouched(),void this.toastService.showWarning("Veuillez corriger les erreurs avant de soumettre le formulaire");this.isLoading=!0;const I=this.planningForm.value;console.log("Donn\xe9es du formulaire \xe0 soumettre:",I);let P=I.dateDebut,$=I.dateFin;"string"==typeof P&&(P=new Date(P)),"string"==typeof $&&($=new Date($));const re={titre:I.titre,description:I.description||"",lieu:I.lieu||"",dateDebut:P,dateFin:$,participants:I.participants||[]};console.log("Mise \xe0 jour du planning avec ID:",this.planningId),console.log("Donn\xe9es format\xe9es:",re);try{this.planningService.updatePlanning(this.planningId,re).subscribe({next:Q=>{console.log("Planning mis \xe0 jour avec succ\xe8s:",Q),this.isLoading=!1,this.toastService.showSuccess("Le planning a \xe9t\xe9 modifi\xe9 avec succ\xe8s"),console.log("Redirection vers la page de d\xe9tail du planning:",this.planningId),setTimeout(()=>{this.router.navigate(["/plannings",this.planningId]).then(ne=>console.log("Redirection r\xe9ussie:",ne),ne=>console.error("Erreur de redirection:",ne))},100)},error:Q=>{this.isLoading=!1,console.error("Erreur lors de la mise \xe0 jour du planning:",Q),403===Q.status?this.toastService.showError("Acc\xe8s refus\xe9 : vous n'avez pas les droits pour modifier ce planning"):401===Q.status?this.toastService.showError("Vous devez \xeatre connect\xe9 pour effectuer cette action"):this.toastService.showError(Q.error?.message||"Erreur lors de la mise \xe0 jour du planning",8e3),Q.error&&console.error("D\xe9tails de l'erreur:",Q.error)}})}catch(Q){this.isLoading=!1;const ne=Q instanceof Error?Q.message:String(Q);this.toastService.showError(`Exception lors de la mise \xe0 jour: ${ne}`),console.error("Exception lors de la mise \xe0 jour:",Q)}}markFormGroupTouched(){Object.keys(this.planningForm.controls).forEach(I=>{const P=this.planningForm.get(I);P&&P.markAsTouched()})}static{this.\u0275fac=function(P){return new(P||U)(l.rXU(a.ok),l.rXU(j.z),l.rXU(N.u),l.rXU(ee.nX),l.rXU(ee.Ix),l.rXU(T.f))}}static{this.\u0275cmp=l.VBU({type:U,selectors:[["app-planning-edit"]],decls:71,vars:13,consts:[[1,"container","mx-auto","px-4","py-6","max-w-3xl"],[1,"bg-gradient-to-r","from-purple-600","to-indigo-600","rounded-t-lg","p-6","text-white","mb-0"],[1,"text-2xl","font-bold","flex","items-center"],[1,"fas","fa-edit","mr-3","text-purple-200"],[1,"text-purple-100","mt-2"],["novalidate","",1,"bg-white","rounded-b-lg","shadow-lg","p-6","border-t-0",3,"formGroup","ngSubmit"],["class","mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded",4,"ngIf"],[1,"grid","grid-cols-1","gap-6"],[1,"bg-gradient-to-r","from-purple-50","to-pink-50","p-4","rounded-lg","border","border-purple-200"],[1,"text-lg","font-semibold","text-purple-800","mb-4","flex","items-center"],[1,"fas","fa-info-circle","mr-2","text-purple-600"],[1,"grid","grid-cols-1","md:grid-cols-2","gap-6"],[1,"block","text-sm","font-medium","text-purple-700","mb-2"],[1,"fas","fa-tag","mr-2","text-purple-500"],["type","text","formControlName","titre","placeholder","Nom de votre planning...",1,"mt-1","block","w-full","px-4","py-3","border-2","border-purple-200","rounded-lg","shadow-sm","focus:ring-purple-500","focus:border-purple-500","focus:ring-2","transition-all","duration-200"],["class","text-red-500 text-sm mt-2 flex items-center",4,"ngIf"],[1,"block","text-sm","font-medium","text-orange-700","mb-2"],[1,"fas","fa-map-marker-alt","mr-2","text-orange-500"],["type","text","formControlName","lieu","placeholder","Salle, bureau, lieu de l'\xe9v\xe9nement...",1,"mt-1","block","w-full","px-4","py-3","border-2","border-orange-200","rounded-lg","shadow-sm","focus:ring-orange-500","focus:border-orange-500","focus:ring-2","transition-all","duration-200"],[1,"bg-gradient-to-r","from-blue-50","to-cyan-50","p-4","rounded-lg","border","border-blue-200"],[1,"text-lg","font-semibold","text-blue-800","mb-4","flex","items-center"],[1,"fas","fa-calendar-week","mr-2","text-blue-600"],[1,"block","text-sm","font-medium","text-green-700","mb-2"],[1,"fas","fa-calendar-day","mr-2","text-green-500"],["type","date","formControlName","dateDebut",1,"mt-1","block","w-full","px-4","py-3","border-2","border-green-200","rounded-lg","shadow-sm","focus:ring-green-500","focus:border-green-500","focus:ring-2","transition-all","duration-200"],[1,"block","text-sm","font-medium","text-red-700","mb-2"],[1,"fas","fa-calendar-check","mr-2","text-red-500"],["type","date","formControlName","dateFin",1,"mt-1","block","w-full","px-4","py-3","border-2","border-red-200","rounded-lg","shadow-sm","focus:ring-red-500","focus:border-red-500","focus:ring-2","transition-all","duration-200"],[1,"bg-gradient-to-r","from-indigo-50","to-purple-50","p-4","rounded-lg","border","border-indigo-200"],[1,"text-lg","font-semibold","text-indigo-800","mb-4","flex","items-center"],[1,"fas","fa-align-left","mr-2","text-indigo-600"],[1,"block","text-sm","font-medium","text-indigo-700","mb-2"],[1,"fas","fa-edit","mr-2","text-indigo-500"],["formControlName","description","rows","4","placeholder","D\xe9crivez les objectifs, le contexte ou les d\xe9tails de ce planning...",1,"mt-1","block","w-full","px-4","py-3","border-2","border-indigo-200","rounded-lg","shadow-sm","focus:ring-indigo-500","focus:border-indigo-500","focus:ring-2","transition-all","duration-200"],[1,"bg-gradient-to-r","from-emerald-50","to-teal-50","p-4","rounded-lg","border","border-emerald-200"],[1,"text-lg","font-semibold","text-emerald-800","mb-4","flex","items-center"],[1,"fas","fa-users","mr-2","text-emerald-600"],[1,"block","text-sm","font-medium","text-emerald-700","mb-2"],[1,"fas","fa-user-friends","mr-2","text-emerald-500"],["formControlName","participants","multiple","",1,"mt-1","block","w-full","px-4","py-3","border-2","border-emerald-200","rounded-lg","shadow-sm","focus:ring-emerald-500","focus:border-emerald-500","focus:ring-2","transition-all","duration-200","text-sm","min-h-[120px]"],["class","py-2",3,"value",4,"ngFor","ngForOf"],[1,"text-xs","text-emerald-600","mt-2"],[1,"fas","fa-info-circle","mr-1"],[1,"mt-8","flex","justify-end","space-x-4","bg-gray-50","p-4","rounded-lg","border-t","border-gray-200"],["type","button","routerLink","/plannings",1,"px-6","py-3","border-2","border-gray-300","rounded-lg","text-sm","font-medium","text-gray-700","hover:bg-gray-100","hover:border-gray-400","transition-all","duration-200","flex","items-center"],[1,"fas","fa-times","mr-2"],["type","button",1,"px-6","py-3","rounded-lg","text-sm","font-medium","text-white","bg-gradient-to-r","from-purple-600","to-indigo-600","hover:from-purple-700","hover:to-indigo-700","disabled:opacity-50","disabled:cursor-not-allowed","transition-all","duration-200","flex","items-center","shadow-lg",3,"disabled","click"],["class","fas fa-save mr-2",4,"ngIf"],["class","fas fa-spinner fa-spin mr-2",4,"ngIf"],[1,"mb-4","bg-red-100","border","border-red-400","text-red-700","px-4","py-3","rounded"],[1,"text-red-500","text-sm","mt-2","flex","items-center"],[1,"fas","fa-exclamation-circle","mr-1"],[4,"ngIf"],[1,"py-2",3,"value"],[1,"fas","fa-save","mr-2"],[1,"fas","fa-spinner","fa-spin","mr-2"]],template:function(P,$){if(1&P&&(l.j41(0,"div",0)(1,"div",1)(2,"h1",2),l.nrm(3,"i",3),l.EFF(4," Modifier le Planning "),l.k0s(),l.j41(5,"p",4),l.EFF(6,"Modifiez les d\xe9tails de votre planning"),l.k0s()(),l.j41(7,"form",5),l.bIt("ngSubmit",function(){return $.onSubmit()}),l.DNE(8,k,2,1,"div",6),l.j41(9,"div",7)(10,"div",8)(11,"h3",9),l.nrm(12,"i",10),l.EFF(13," Informations g\xe9n\xe9rales "),l.k0s(),l.j41(14,"div",11)(15,"div")(16,"label",12),l.nrm(17,"i",13),l.EFF(18," Titre * "),l.k0s(),l.nrm(19,"input",14),l.DNE(20,H,4,2,"div",15),l.k0s(),l.j41(21,"div")(22,"label",16),l.nrm(23,"i",17),l.EFF(24," Lieu / Salle "),l.k0s(),l.nrm(25,"input",18),l.k0s()()(),l.j41(26,"div",19)(27,"h3",20),l.nrm(28,"i",21),l.EFF(29," P\xe9riode du planning "),l.k0s(),l.j41(30,"div",11)(31,"div")(32,"label",22),l.nrm(33,"i",23),l.EFF(34," Date de d\xe9but * "),l.k0s(),l.nrm(35,"input",24),l.k0s(),l.j41(36,"div")(37,"label",25),l.nrm(38,"i",26),l.EFF(39," Date de fin * "),l.k0s(),l.nrm(40,"input",27),l.k0s()()(),l.j41(41,"div",28)(42,"h3",29),l.nrm(43,"i",30),l.EFF(44," Description "),l.k0s(),l.j41(45,"label",31),l.nrm(46,"i",32),l.EFF(47," D\xe9crivez votre planning "),l.k0s(),l.nrm(48,"textarea",33),l.k0s(),l.j41(49,"div",34)(50,"h3",35),l.nrm(51,"i",36),l.EFF(52," Participants "),l.k0s(),l.j41(53,"label",37),l.nrm(54,"i",38),l.EFF(55," S\xe9lectionnez les participants * "),l.k0s(),l.j41(56,"select",39),l.DNE(57,G,2,2,"option",40),l.nI1(58,"async"),l.k0s(),l.DNE(59,le,3,0,"div",15),l.j41(60,"p",41),l.nrm(61,"i",42),l.EFF(62," Maintenez Ctrl (ou Cmd) pour s\xe9lectionner plusieurs participants "),l.k0s()()(),l.j41(63,"div",43)(64,"button",44),l.nrm(65,"i",45),l.EFF(66," Annuler "),l.k0s(),l.j41(67,"button",46),l.bIt("click",function(){return $.onSubmit()}),l.DNE(68,de,1,0,"i",47),l.DNE(69,Ge,1,0,"i",48),l.EFF(70),l.k0s()()()()),2&P){let re,Q,ne;l.R7$(7),l.Y8G("formGroup",$.planningForm),l.R7$(1),l.Y8G("ngIf",$.error),l.R7$(11),l.AVh("border-red-300",(null==(re=$.planningForm.get("titre"))?null:re.invalid)&&(null==(re=$.planningForm.get("titre"))?null:re.touched)),l.R7$(1),l.Y8G("ngIf",(null==(Q=$.planningForm.get("titre"))?null:Q.invalid)&&(null==(Q=$.planningForm.get("titre"))?null:Q.touched)),l.R7$(37),l.Y8G("ngForOf",l.bMT(58,11,$.users$)),l.R7$(2),l.Y8G("ngIf",(null==(ne=$.planningForm.get("participants"))?null:ne.invalid)&&(null==(ne=$.planningForm.get("participants"))?null:ne.touched)),l.R7$(8),l.Y8G("disabled",$.isLoading||$.planningForm.invalid),l.R7$(1),l.Y8G("ngIf",!$.isLoading),l.R7$(1),l.Y8G("ngIf",$.isLoading),l.R7$(1),l.SpI(" ",$.isLoading?"Enregistrement...":"Enregistrer les modifications"," ")}},dependencies:[x.Sq,x.bT,ee.Wk,a.qT,a.xH,a.y7,a.me,a.W0,a.BC,a.cb,a.j4,a.JD,x.Jj]})}}return U})()},1163:(Ee,oe,D)=>{D.d(oe,{a:()=>Ft});var a=D(1635);Math.pow(10,8);const T=6048e5,x=864e5,k=6e4,K=36e5,ye=Symbol.for("constructDateFrom");function ie(u,c){return"function"==typeof u?u(c):u&&"object"==typeof u&&ye in u?u[ye](c):u instanceof Date?new u.constructor(c):new Date(c)}function L(u,c){return ie(c||u,u)}function Ae(u,c,h){const f=L(u,h?.in);return isNaN(c)?ie(h?.in||u,NaN):(c&&f.setDate(f.getDate()+c),f)}function Ke(u,c,h){return ie(h?.in||u,+L(u)+c)}function ht(u,c,h){return Ke(u,c*K,h)}function Me(u,c,h){const f=L(u,h?.in);return f.setTime(f.getTime()+c*k),f}function we(u,c,h){return Ke(u,1e3*c,h)}function Oe(u,...c){const h=ie.bind(null,u||c.find(f=>"object"==typeof f));return c.map(h)}function Ze(u){const c=L(u),h=new Date(Date.UTC(c.getFullYear(),c.getMonth(),c.getDate(),c.getHours(),c.getMinutes(),c.getSeconds(),c.getMilliseconds()));return h.setUTCFullYear(c.getFullYear()),+u-+h}function he(u,c){const h=L(u,c?.in);return h.setHours(0,0,0,0),h}function Qe(u,c,h){const[f,z]=Oe(h?.in,u,c),Y=qe(f,z),q=Math.abs(function Je(u,c,h){const[f,z]=Oe(h?.in,u,c),Y=he(f),q=he(z),ge=+Y-Ze(Y),Fe=+q-Ze(q);return Math.round((ge-Fe)/x)}(f,z));f.setDate(f.getDate()-Y*q);const Fe=Y*(q-+(qe(f,z)===-Y));return 0===Fe?0:Fe}function qe(u,c){const h=u.getFullYear()-c.getFullYear()||u.getMonth()-c.getMonth()||u.getDate()-c.getDate()||u.getHours()-c.getHours()||u.getMinutes()-c.getMinutes()||u.getSeconds()-c.getSeconds()||u.getMilliseconds()-c.getMilliseconds();return h<0?-1:h>0?1:h}function _e(u){return c=>{const f=(u?Math[u]:Math.trunc)(c);return 0===f?0:f}}function ke(u,c){return+L(u)-+L(c)}function vt(u,c,h){const f=ke(u,c)/k;return _e(h?.roundingMethod)(f)}function gt(u,c,h){const f=ke(u,c)/1e3;return _e(h?.roundingMethod)(f)}function yt(u,c){const h=L(u,c?.in);return h.setHours(23,59,59,999),h}function _t(u,c){const h=L(u,c?.in),f=h.getMonth();return h.setFullYear(h.getFullYear(),f+1,0),h.setHours(23,59,59,999),h}let Re={};function ze(){return Re}function et(u,c){const h=ze(),f=c?.weekStartsOn??c?.locale?.options?.weekStartsOn??h.weekStartsOn??h.locale?.options?.weekStartsOn??0,z=L(u,c?.in),Y=z.getDay(),q=6+(Y<f?-7:0)-(Y-f);return z.setDate(z.getDate()+q),z.setHours(23,59,59,999),z}function Ct(u,c){return L(u,c?.in).getDay()}function Et(u,c){return L(u,c?.in).getMonth()}function Tt(u,c,h){const[f,z]=Oe(h?.in,u,c);return+he(f)==+he(z)}function it(u,c,h){const[f,z]=Oe(h?.in,u,c);return f.getFullYear()===z.getFullYear()&&f.getMonth()===z.getMonth()}function He(u,c){const h=L(u,c?.in);return h.setMilliseconds(0),h}function ve(u,c){return+He(u)==+He(c)}function Le(u,c){let h,f=c?.in;return u.forEach(z=>{!f&&"object"==typeof z&&(f=ie.bind(null,z));const Y=L(z,f);(!h||h<Y||isNaN(+Y))&&(h=Y)}),ie(f,h||NaN)}function Se(u,c,h){const f=L(u,h?.in);return f.setHours(c),f}function De(u,c,h){const f=L(u,h?.in);return f.setMinutes(c),f}function kt(u,c){const h=L(u,c?.in);return h.setSeconds(0,0),h}function bt(u,c){const h=L(u,c?.in);return h.setDate(1),h.setHours(0,0,0,0),h}function be(u,c){const h=ze(),f=c?.weekStartsOn??c?.locale?.options?.weekStartsOn??h.weekStartsOn??h.locale?.options?.weekStartsOn??0,z=L(u,c?.in),Y=z.getDay(),q=(Y<f?7:0)+Y-f;return z.setDate(z.getDate()-q),z.setHours(0,0,0,0),z}function ot(u,c){return L(u,c?.in).getHours()}function st(u,c){return L(u,c?.in).getMinutes()}function Ve(u){return new Date(u).getTimezoneOffset()}function fe(u,c,h){return Ae(u,7*c,h)}function Ye(u,c,h){const f=L(u,h?.in);if(isNaN(c))return ie(h?.in||u,NaN);if(!c)return f;const z=f.getDate(),Y=ie(h?.in||u,f.getTime());return Y.setMonth(f.getMonth()+c+1,0),z>=Y.getDate()?Y:(f.setFullYear(Y.getFullYear(),Y.getMonth(),z),f)}function lt(u,c,h){return Ae(u,-c,h)}function un(u,c,h){return fe(u,-c,h)}function mn(u,c,h){return Ye(u,-c,h)}function je(u,c){return be(u,{...c,weekStartsOn:1})}function vn(u,c){const h=L(u,c?.in),f=+je(h)-+function hn(u,c){const h=function pn(u,c){const h=L(u,c?.in),f=h.getFullYear(),z=ie(h,0);z.setFullYear(f+1,0,4),z.setHours(0,0,0,0);const Y=je(z),q=ie(h,0);q.setFullYear(f,0,4),q.setHours(0,0,0,0);const ge=je(q);return h.getTime()>=Y.getTime()?f+1:h.getTime()>=ge.getTime()?f:f-1}(u,c),f=ie(c?.in||u,0);return f.setFullYear(h,0,4),f.setHours(0,0,0,0),je(f)}(h);return Math.round(f/T)+1}function zt(u,c,h){const f=L(u,h?.in);return f.setDate(c),f}function Wt(u,c,h){const f=L(u,h?.in),z=f.getFullYear(),Y=f.getDate(),q=ie(h?.in||u,0);q.setFullYear(z,c,15),q.setHours(0,0,0,0);const ge=function Ht(u,c){const h=L(u,c?.in),f=h.getFullYear(),z=h.getMonth(),Y=ie(h,0);return Y.setFullYear(f,z+1,0),Y.setHours(0,0,0,0),Y.getDate()}(q);return f.setMonth(c,Math.min(Y,ge)),f}function Ce(u,c,h){const f=L(u,h?.in);return isNaN(+f)?ie(h?.in||u,NaN):(f.setFullYear(c),f)}function Vt(u,c){return L(u,c?.in).getDate()}function It(u,c){return L(u,c?.in).getFullYear()}function Ft(){return(0,a.Cl)((0,a.Cl)({},function ce(){return{addDays:Ae,addHours:ht,addMinutes:Me,addSeconds:we,differenceInDays:Qe,differenceInMinutes:vt,differenceInSeconds:gt,endOfDay:yt,endOfMonth:_t,endOfWeek:et,getDay:Ct,getMonth:Et,isSameDay:Tt,isSameMonth:it,isSameSecond:ve,max:Le,setHours:Se,setMinutes:De,startOfDay:he,startOfMinute:kt,startOfMonth:bt,startOfWeek:be,getHours:ot,getMinutes:st,getTimezoneOffset:Ve}}()),{addWeeks:fe,addMonths:Ye,subDays:lt,subWeeks:un,subMonths:mn,getISOWeek:vn,setDate:zt,setMonth:Wt,setYear:Ce,getDate:Vt,getYear:It})}},4533:(Ee,oe,D)=>{D.d(oe,{rO:()=>ji,jD:()=>Yi,St:()=>Sn,MJ:()=>ut});var a=D(7705),l=D(177),j=D(1413),N=D(1985),ee=D(7673),T=D(1584),x=D(6977),k=function(){function t(){}return t.prototype.getAllStyles=function(i){return window.getComputedStyle(i)},t.prototype.getStyle=function(i,e){return this.getAllStyles(i)[e]},t.prototype.isStaticPositioned=function(i){return"static"===(this.getStyle(i,"position")||"static")},t.prototype.offsetParent=function(i){for(var e=i.offsetParent||document.documentElement;e&&e!==document.documentElement&&this.isStaticPositioned(e);)e=e.offsetParent;return e||document.documentElement},t.prototype.position=function(i,e){void 0===e&&(e=!0);var n,r={width:0,height:0,top:0,bottom:0,left:0,right:0};if("fixed"===this.getStyle(i,"position"))n={top:(n=i.getBoundingClientRect()).top,bottom:n.bottom,left:n.left,right:n.right,height:n.height,width:n.width};else{var o=this.offsetParent(i);n=this.offset(i,!1),o!==document.documentElement&&(r=this.offset(o,!1)),r.top+=o.clientTop,r.left+=o.clientLeft}return n.top-=r.top,n.bottom-=r.top,n.left-=r.left,n.right-=r.left,e&&(n.top=Math.round(n.top),n.bottom=Math.round(n.bottom),n.left=Math.round(n.left),n.right=Math.round(n.right)),n},t.prototype.offset=function(i,e){void 0===e&&(e=!0);var n=i.getBoundingClientRect(),r_top=window.pageYOffset-document.documentElement.clientTop,r_left=window.pageXOffset-document.documentElement.clientLeft,o={height:n.height||i.offsetHeight,width:n.width||i.offsetWidth,top:n.top+r_top,bottom:n.bottom+r_top,left:n.left+r_left,right:n.right+r_left};return e&&(o.height=Math.round(o.height),o.width=Math.round(o.width),o.top=Math.round(o.top),o.bottom=Math.round(o.bottom),o.left=Math.round(o.left),o.right=Math.round(o.right)),o},t.prototype.positionElements=function(i,e,n,r){var o=n.split("-"),s=o[0],d=void 0===s?"top":s,m=o[1],p=void 0===m?"center":m,v=r?this.offset(i,!1):this.position(i,!1),y=this.getAllStyles(e),S=parseFloat(y.marginTop),g=parseFloat(y.marginBottom),C=parseFloat(y.marginLeft),M=parseFloat(y.marginRight),E=0,O=0;switch(d){case"top":E=v.top-(e.offsetHeight+S+g);break;case"bottom":E=v.top+v.height;break;case"left":O=v.left-(e.offsetWidth+C+M);break;case"right":O=v.left+v.width}switch(p){case"top":E=v.top;break;case"bottom":E=v.top+v.height-e.offsetHeight;break;case"left":O=v.left;break;case"right":O=v.left+v.width-e.offsetWidth;break;case"center":"top"===d||"bottom"===d?O=v.left+v.width/2-e.offsetWidth/2:E=v.top+v.height/2-e.offsetHeight/2}e.style.transform="translate("+Math.round(O)+"px, "+Math.round(E)+"px)";var R=e.getBoundingClientRect(),b=document.documentElement,V=window.innerHeight||b.clientHeight,A=window.innerWidth||b.clientWidth;return R.left>=0&&R.top>=0&&R.right<=A&&R.bottom<=V},t}(),K=/\s+/,pe=new k,G=function(){return G=Object.assign||function(t){for(var i,e=1,n=arguments.length;e<n;e++)for(var r in i=arguments[e])Object.prototype.hasOwnProperty.call(i,r)&&(t[r]=i[r]);return t},G.apply(this,arguments)},le=function(t,i,e){if(e||2===arguments.length)for(var o,n=0,r=i.length;n<r;n++)(o||!(n in i))&&(o||(o=Array.prototype.slice.call(i,0,n)),o[n]=i[n]);return t.concat(o||Array.prototype.slice.call(i))},de=function(t){return t[t.SUNDAY=0]="SUNDAY",t[t.MONDAY=1]="MONDAY",t[t.TUESDAY=2]="TUESDAY",t[t.WEDNESDAY=3]="WEDNESDAY",t[t.THURSDAY=4]="THURSDAY",t[t.FRIDAY=5]="FRIDAY",t[t.SATURDAY=6]="SATURDAY",t}(de||{}),Ge=[de.SUNDAY,de.SATURDAY],xe=7,I=86400;function P(t,i){var e=i.startDate,r=i.excluded,o=i.precision;if(r.length<1)return 0;for(var d=t.getDay,m=t.addDays,p=(0,t.addSeconds)(e,i.seconds-1),v=d(e),y=d(p),S=0,g=e,C=function(){var M=d(g);r.some(function(E){return E===M})&&(S+=function $(t,i){var n=i.day,o=i.dayEnd,s=i.startDate,d=i.endDate,m=t.differenceInSeconds,v=t.startOfDay;if("minutes"===i.precision){if(n===i.dayStart)return m((0,t.endOfDay)(s),s)+1;if(n===o)return m(d,v(d))+1}return I}(t,{dayStart:v,dayEnd:y,day:M,precision:o,startDate:e,endDate:p})),g=m(g,1)};g<p;)C();return S}function ye(t,i){var n=i.periodStart,r=i.periodEnd;return i.events.filter(function(o){return function ne(t,i){var e=i.event,n=i.periodStart,r=i.periodEnd,o=t.isSameSecond,s=e.start,d=e.end||e.start;return!!(s>n&&s<r||d>n&&d<r||s<n&&d>r||o(s,n)||o(s,r)||o(d,n)||o(d,r))}(t,{event:o,periodStart:n,periodEnd:r})})}function ie(t,i){var e=i.date,n=i.weekendDays,r=void 0===n?Ge:n,s=t.isSameDay,d=t.getDay,m=(0,t.startOfDay)(new Date),p=d(e);return{date:e,day:p,isPast:e<m,isToday:s(e,m),isFuture:e>m,isWeekend:r.indexOf(p)>-1}}function Xe(t,i){for(var r=i.excluded,o=void 0===r?[]:r,s=i.weekendDays,d=i.viewStart,m=void 0===d?t.startOfWeek(i.viewDate,{weekStartsOn:i.weekStartsOn}):d,p=i.viewEnd,v=void 0===p?t.addDays(m,xe):p,y=t.addDays,S=t.getDay,g=[],C=m;C<v;)o.some(function(M){return S(C)===M})||g.push(ie(t,{date:C,weekendDays:s})),C=y(C,1);return g}function Xt(t,i){var e=i.events,n=void 0===e?[]:e,r=i.excluded,o=void 0===r?[]:r,s=i.precision,d=void 0===s?"days":s,m=i.absolutePositionedEvents,p=void 0!==m&&m,v=i.viewStart,y=i.viewEnd;v=t.startOfDay(v),y=t.endOfDay(y);var S=t.differenceInSeconds,g=t.differenceInDays,C=function L(t,i){for(var n=i.date2,r=i.excluded,o=i.date1,s=0;o<n;)-1===r.indexOf(t.getDay(o))&&s++,o=t.addDays(o,1);return s}(t,{date1:v,date2:y,excluded:o}),M=g(y,v)+1,E=n.filter(function(b){return b.allDay}).map(function(b){var V=function Q(t,i){var e=i.event,n=i.startOfWeek,r=i.excluded,o=i.precision,s=t.differenceInDays,d=t.startOfDay,m=t.differenceInSeconds;if(e.start<n)return 0;var p=0;switch(o){case"days":p=s(d(e.start),n)*I;break;case"minutes":p=m(e.start,n)}return p-=P(t,{startDate:n,seconds:p,excluded:r,precision:o}),Math.abs(p/I)}(t,{event:b,startOfWeek:v,excluded:o,precision:d}),A=function re(t,i){var e=i.event,n=i.offset,o=i.excluded,s=i.precision,d=i.totalDaysInView,p=t.differenceInSeconds,v=t.addDays,y=t.endOfDay,S=t.differenceInDays,g=I,C=(0,t.max)([e.start,i.startOfWeekDate]);e.end&&(g="minutes"===s?p(e.end,C):S(v(y(e.end),1),C)*I);var M=n*I,O=d*I;return M+g>O&&(g=O-M),(g-=P(t,{startDate:C,seconds:g,excluded:o,precision:s}))/I}(t,{event:b,offset:V,startOfWeekDate:v,excluded:o,precision:d,totalDaysInView:M});return{event:b,offset:V,span:A}}).filter(function(b){return b.offset<C}).filter(function(b){return b.span>0}).map(function(b){return{event:b.event,offset:b.offset,span:b.span,startsBeforeWeek:b.event.start<v,endsAfterWeek:(b.event.end||b.event.start)>y}}).sort(function(b,V){var A=S(b.event.start,V.event.start);return 0===A?S(V.event.end||V.event.start,b.event.end||b.event.start):A}),O=[],R=[];return E.forEach(function(b,V){if(-1===R.indexOf(b)){R.push(b);var A=b.span+b.offset,Z=E.slice(V+1).filter(function(W){if(W.offset>=A&&A+W.span<=M&&-1===R.indexOf(W)){var F=W.offset-A;return p||(W.offset=F),A+=W.span+F,R.push(W),!0}}),te=le([b],Z,!0),B=te.filter(function(W){return W.event.id}).map(function(W){return W.event.id}).join("-");O.push(G({row:te},B?{id:B}:{}))}}),O}function Ae(t,i){var e=i.events,n=i.viewDate,r=i.hourSegments,o=i.hourDuration,s=i.dayStart,d=i.dayEnd,m=i.weekStartsOn,p=i.excluded,v=i.weekendDays,y=i.segmentHeight,S=i.viewStart,g=i.viewEnd,C=i.minimumEventHeight,M=function Zt(t,i){var e=i.viewDate,n=i.hourSegments,r=i.hourDuration,o=i.dayStart,s=i.dayEnd,d=t.setMinutes,m=t.setHours,p=t.startOfDay,v=t.startOfMinute,y=t.endOfDay,S=t.addMinutes,g=t.addDays,C=[],M=d(m(p(e),$e(o.hour)),Me(o.minute)),E=d(m(v(y(e)),$e(s.hour)),Me(s.minute)),O=(r||60)/n,R=p(e),b=y(e),V=function(F){return F};t.getTimezoneOffset(R)!==t.getTimezoneOffset(b)&&(R=g(R,1),M=g(M,1),E=g(E,1),V=function(F){return g(F,-1)});for(var A=r?1440/r:60,Z=0;Z<A;Z++){for(var te=[],B=0;B<n;B++){var W=S(S(M,Z*(r||60)),B*O);W>=M&&W<E&&te.push({date:V(W),displayDate:W,isStart:0===B})}te.length>0&&C.push({segments:te})}return C}(t,{viewDate:n,hourSegments:r,hourDuration:o,dayStart:s,dayEnd:d}),E=Xe(t,{viewDate:n,weekStartsOn:m,excluded:p,weekendDays:v,viewStart:S,viewEnd:g}),O=t.setHours,R=t.setMinutes,b=t.getHours,V=t.getMinutes;return E.map(function(A){var Z=function ht(t,i){var e=i.events,n=i.viewDate,r=i.hourSegments,o=i.dayStart,s=i.dayEnd,d=i.eventWidth,m=i.segmentHeight,p=i.hourDuration,v=i.minimumEventHeight,y=t.setMinutes,S=t.setHours,g=t.startOfDay,C=t.startOfMinute,M=t.endOfDay,E=t.differenceInMinutes,O=y(S(g(n),$e(o.hour)),Me(o.minute)),R=y(S(C(M(n)),$e(s.hour)),Me(s.minute));R.setSeconds(59,999);var b=[],V=ye(t,{events:e.filter(function(B){return!B.allDay}),periodStart:O,periodEnd:R}),A=V.sort(function(B,W){return B.start.valueOf()-W.start.valueOf()}).map(function(B){var W=B.start,F=B.end||W,_=W<O,w=F>R,J=r*m/(p||60),X=0;if(W>O){var Yt=t.getTimezoneOffset(W),pt=t.getTimezoneOffset(O)-Yt;X+=E(W,O)+pt}X*=J,X=Math.floor(X);var Nt=_?O:W,ae=w?R:F,Be=t.getTimezoneOffset(Nt)-t.getTimezoneOffset(ae),ue=E(ae,Nt)+Be;B.end?ue*=J:ue=m,v&&ue<v&&(ue=v),ue=Math.floor(ue);for(var Ni=Pe(b,X,X+ue),Gt=0;Ni.some(function(Ui){return Ui.left===Gt});)Gt+=d;var Hn={event:B,height:ue,width:d,top:X,left:Gt,startsBeforeDay:_,endsAfterDay:w};return b.push(Hn),Hn}),Z=Math.max.apply(Math,A.map(function(B){return B.left+B.width})),te=ye(t,{events:e.filter(function(B){return B.allDay}),periodStart:g(O),periodEnd:M(R)});return{events:A,width:Z,allDayEvents:te,period:{events:V,start:O,end:R}}}(t,{events:e,viewDate:A.date,hourSegments:r,dayStart:s,dayEnd:d,segmentHeight:y,eventWidth:1,hourDuration:o,minimumEventHeight:C}),te=M.map(function(F){var _=F.segments.map(function(w){var J=R(O(A.date,b(w.date)),V(w.date));return G(G({},w),{date:J})});return G(G({},F),{segments:_})});function B(F,_){var w=Math.max.apply(Math,_.map(function(X){return X.left+1})),J=F.filter(function(X){return X.left>=w}).filter(function(X){return Pe(_,X.top,X.top+X.height).length>0});return J.length>0?B(F,J):w}var W=Z.events.map(function(F){var w=100/B(Z.events,Pe(Z.events,F.top,F.top+F.height));return G(G({},F),{left:F.left*w,width:w})});return{hours:te,date:A.date,events:W.map(function(F){var _=Pe(W.filter(function(w){return w.left>F.left}),F.top,F.top+F.height);return _.length>0?G(G({},F),{width:Math.min.apply(Math,_.map(function(w){return w.left}))-F.left}):F})}})}function Pe(t,i,e){return t.filter(function(n){var r=n.top,o=n.top+n.height;return i<o&&o<e||i<r&&r<e||r<=i&&e<=o})}function $e(t){return Math.max(Math.min(23,t),0)}function Me(t){return Math.max(Math.min(59,t),0)}var we=function(t){return t.NotArray="Events must be an array",t.StartPropertyMissing="Event is missing the `start` property",t.StartPropertyNotDate="Event `start` property should be a javascript date object. Do `new Date(event.start)` to fix it.",t.EndPropertyNotDate="Event `end` property should be a javascript date object. Do `new Date(event.end)` to fix it.",t.EndsBeforeStart="Event `start` property occurs after the `end`",t}(we||{}),Oe=D(6129);class Ze extends j.B{constructor(i=1/0,e=1/0,n=Oe.U){super(),this._bufferSize=i,this._windowTime=e,this._timestampProvider=n,this._buffer=[],this._infiniteTimeWindow=!0,this._infiniteTimeWindow=e===1/0,this._bufferSize=Math.max(1,i),this._windowTime=Math.max(1,e)}next(i){const{isStopped:e,_buffer:n,_infiniteTimeWindow:r,_timestampProvider:o,_windowTime:s}=this;e||(n.push(i),!r&&n.push(o.now()+s)),this._trimBuffer(),super.next(i)}_subscribe(i){this._throwIfClosed(),this._trimBuffer();const e=this._innerSubscribe(i),{_infiniteTimeWindow:n,_buffer:r}=this,o=r.slice();for(let s=0;s<o.length&&!i.closed;s+=n?1:2)i.next(o[s]);return this._checkFinalizedStatuses(i),e}_trimBuffer(){const{_bufferSize:i,_timestampProvider:e,_buffer:n,_infiniteTimeWindow:r}=this,o=(r?1:2)*i;if(i<1/0&&o<n.length&&n.splice(0,n.length-o),!r){const s=e.now();let d=0;for(let m=1;m<n.length&&n[m]<=s;m+=2)d=m;d&&n.splice(0,d+1)}}}var he=D(7786),Qt=D(4572),Je=D(3726),Te=D(5964),Qe=D(1397),qe=D(9172),me=D(6354),_e=D(7647),ke=D(6697),qt=D(5109),vt=D(6649),ft=D(9974);var yt=D(4360);function wt(){return(0,ft.N)((t,i)=>{let e,n=!1;t.subscribe((0,yt._)(i,r=>{const o=e;e=r,n&&i.next([o,r]),n=!0}))})}var _t=D(3294);function Re(t,i){return t=function tn(t,i){return typeof t>"u"?typeof i>"u"?t:i:t}(t,i),"function"==typeof t?function(){for(var n=arguments,r=arguments.length,o=Array(r),s=0;s<r;s++)o[s]=n[s];return!!t.apply(this,o)}:t?function(){return!0}:function(){return!1}}var ze="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol?"symbol":typeof t},Dt=function(t){return null!=t&&"object"===(typeof t>"u"?"undefined":ze(t))&&1===t.nodeType&&"object"===ze(t.style)&&"object"===ze(t.ownerDocument)};function et(t,i){if(i=nt(i,!0),!Dt(i))return-1;for(var e=0;e<t.length;e++)if(t[e]===i)return e;return-1}function tt(t,i){return-1!==et(t,i)}function nn(t){for(var i=arguments,e=[],n=arguments.length-1;n-- >0;)e[n]=i[n+1];return function Ct(t,i){for(var e=0;e<i.length;e++)tt(t,i[e])||t.push(i[e]);return i}(t,e=e.map(nt))}function Et(t){for(var i=arguments,e=[],n=arguments.length-1;n-- >0;)e[n]=i[n+1];return e.map(nt).reduce(function(r,o){var s=et(t,o);return-1!==s?r.concat(t.splice(s,1)):r},[])}function nt(t,i){if("string"==typeof t)try{return document.querySelector(t)}catch(e){throw e}if(!Dt(t)&&!i)throw new TypeError(t+" is not a DOM element.");return t}function it(t){if(t===window)return function rn(){var t={top:{value:0,enumerable:!0},left:{value:0,enumerable:!0},right:{value:window.innerWidth,enumerable:!0},bottom:{value:window.innerHeight,enumerable:!0},width:{value:window.innerWidth,enumerable:!0},height:{value:window.innerHeight,enumerable:!0},x:{value:0,enumerable:!0},y:{value:0,enumerable:!0}};if(Object.create)return Object.create({},t);var i={};return Object.defineProperties(i,t),i}();try{var i=t.getBoundingClientRect();return void 0===i.x&&(i.x=i.left,i.y=i.top),i}catch{throw new TypeError("Can't call getBoundingClientRect on "+t)}}var i,He=void 0;"function"!=typeof Object.create?(i=function(){},He=function(e,n){if(e!==Object(e)&&null!==e)throw TypeError("Argument must be an object, or null");i.prototype=e||{};var r=new i;return i.prototype=null,void 0!==n&&Object.defineProperties(r,n),null===e&&(r.__proto__=null),r}):He=Object.create;var on=He,ve=["altKey","button","buttons","clientX","clientY","ctrlKey","metaKey","movementX","movementY","offsetX","offsetY","pageX","pageY","region","relatedTarget","screenX","screenY","shiftKey","which","x","y"];function Le(t,i){i=i||{};for(var e=on(t),n=0;n<ve.length;n++)void 0!==i[ve[n]]&&(e[ve[n]]=i[ve[n]]);return e}function rt(t,i){console.log("data ",i),t.data=i||{},t.dispatched="mousemove"}var Se=["webkit","moz","ms","o"],We=function(){if(typeof window>"u")return function(){};for(var t=0,i=Se.length;t<i&&!window.requestAnimationFrame;++t)window.requestAnimationFrame=window[Se[t]+"RequestAnimationFrame"];if(!window.requestAnimationFrame){var e=0;window.requestAnimationFrame=function(n){var r=(new Date).getTime(),o=Math.max(0,16-r-e),s=window.setTimeout(function(){return n(r+o)},o);return e=r+o,s}}return window.requestAnimationFrame.bind(window)}(),De=function(){if(typeof window>"u")return function(){};for(var t=0,i=Se.length;t<i&&!window.cancelAnimationFrame;++t)window.cancelAnimationFrame=window[Se[t]+"CancelAnimationFrame"]||window[Se[t]+"CancelRequestAnimationFrame"];return window.cancelAnimationFrame||(window.cancelAnimationFrame=function(e){window.clearTimeout(e)}),window.cancelAnimationFrame.bind(window)}();function ln(t,i){void 0===i&&(i={});var e=this,n=4,r=!1;if("object"!=typeof i.margin){var o=i.margin||-1;this.margin={left:o,right:o,top:o,bottom:o}}else this.margin=i.margin;this.scrollWhenOutside=i.scrollWhenOutside||!1;var s={},d=function Tt(t,i){var e=Re((i=i||{}).allowUpdate,!0);return function(r){if(r=r||window.event,t.target=r.target||r.srcElement||r.originalTarget,t.element=this,t.type=r.type,e(r)){if(r.targetTouches)t.x=r.targetTouches[0].clientX,t.y=r.targetTouches[0].clientY,t.pageX=r.targetTouches[0].pageX,t.pageY=r.targetTouches[0].pageY,t.screenX=r.targetTouches[0].screenX,t.screenY=r.targetTouches[0].screenY;else{if(null===r.pageX&&null!==r.clientX){var o=r.target&&r.target.ownerDocument||document,s=o.documentElement,d=o.body;t.pageX=r.clientX+(s&&s.scrollLeft||d&&d.scrollLeft||0)-(s&&s.clientLeft||d&&d.clientLeft||0),t.pageY=r.clientY+(s&&s.scrollTop||d&&d.scrollTop||0)-(s&&s.clientTop||d&&d.clientTop||0)}else t.pageX=r.pageX,t.pageY=r.pageY;t.x=r.clientX,t.y=r.clientY,t.screenX=r.screenX,t.screenY=r.screenY}t.clientX=t.x,t.clientY=t.y}}}(s),m=function sn(t){var i={screenX:0,screenY:0,clientX:0,clientY:0,ctrlKey:!1,shiftKey:!1,altKey:!1,metaKey:!1,button:0,buttons:1,relatedTarget:null,region:null};function e(o){for(var s=0;s<ve.length;s++)i[ve[s]]=o[ve[s]]}return void 0!==t&&t.addEventListener("mousemove",e),{destroy:function r(){t&&t.removeEventListener("mousemove",e,!1),i=null},dispatch:MouseEvent?function(s,d,m){var p=new MouseEvent("mousemove",Le(i,d));return rt(p,m),s.dispatchEvent(p)}:"function"==typeof document.createEvent?function(s,d,m){var p=Le(i,d),v=document.createEvent("MouseEvents");return v.initMouseEvent("mousemove",!0,!0,window,0,p.screenX,p.screenY,p.clientX,p.clientY,p.ctrlKey,p.altKey,p.shiftKey,p.metaKey,p.button,p.relatedTarget),rt(v,m),s.dispatchEvent(v)}:"function"==typeof document.createEventObject?function(s,d,m){var p=document.createEventObject(),v=Le(i,d);for(var y in v)p[y]=v[y];return rt(p,m),s.dispatchEvent(p)}:void 0}}(),p=!1;window.addEventListener("mousemove",d,!1),window.addEventListener("touchmove",d,!1),isNaN(i.maxSpeed)||(n=i.maxSpeed),"object"!=typeof n&&(n={left:n,right:n,top:n,bottom:n}),this.autoScroll=Re(i.autoScroll),this.syncMove=Re(i.syncMove,!1),this.destroy=function(_){window.removeEventListener("mousemove",d,!1),window.removeEventListener("touchmove",d,!1),window.removeEventListener("mousedown",M,!1),window.removeEventListener("touchstart",M,!1),window.removeEventListener("mouseup",E,!1),window.removeEventListener("touchend",E,!1),window.removeEventListener("pointerup",E,!1),window.removeEventListener("mouseleave",R,!1),window.removeEventListener("mousemove",A,!1),window.removeEventListener("touchmove",A,!1),window.removeEventListener("scroll",C,!0),t=[],_&&O()},this.add=function(){for(var _=[],w=arguments.length;w--;)_[w]=arguments[w];return nn.apply(void 0,[t].concat(_)),this},this.remove=function(){for(var _=[],w=arguments.length;w--;)_[w]=arguments[w];return Et.apply(void 0,[t].concat(_))};var y,_,v=null;"[object Array]"!==Object.prototype.toString.call(t)&&(t=[t]),_=t,t=[],_.forEach(function(w){w===window?v=window:e.add(w)}),Object.defineProperties(this,{down:{get:function(){return p}},maxSpeed:{get:function(){return n}},point:{get:function(){return s}},scrolling:{get:function(){return r}}});var g,S=null;function C(_){for(var w=0;w<t.length;w++)if(t[w]===_.target){r=!0;break}r&&We(function(){return r=!1})}function M(){p=!0}function E(){p=!1,O()}function O(){De(g),De(y)}function R(){p=!1}function V(){for(var _=null,w=0;w<t.length;w++)St(s,t[w])&&(_=t[w]);return _}function A(_){if(e.autoScroll()&&!_.dispatched){var w=_.target,J=document.body;S&&!St(s,S)&&(e.scrollWhenOutside||(S=null)),w&&w.parentNode===J?w=V():(w=function b(_){if(!_)return null;if(S===_||tt(t,_))return _;for(;_=_.parentNode;)if(tt(t,_))return _;return null}(w),w||(w=V())),w&&w!==S&&(S=w),v&&(De(y),y=We(Z)),S&&(De(g),g=We(te))}}function Z(){B(v),De(y),y=We(Z)}function te(){S&&(B(S),De(g),g=We(te))}function B(_){var J,X,w=it(_);J=s.x<w.left+e.margin.left?Math.floor(Math.max(-1,(s.x-w.left)/e.margin.left-1)*e.maxSpeed.left):s.x>w.right-e.margin.right?Math.ceil(Math.min(1,(s.x-w.right)/e.margin.right+1)*e.maxSpeed.right):0,X=s.y<w.top+e.margin.top?Math.floor(Math.max(-1,(s.y-w.top)/e.margin.top-1)*e.maxSpeed.top):s.y>w.bottom-e.margin.bottom?Math.ceil(Math.min(1,(s.y-w.bottom)/e.margin.bottom+1)*e.maxSpeed.bottom):0,e.syncMove()&&m.dispatch(_,{pageX:s.pageX+J,pageY:s.pageY+X,clientX:s.x+J,clientY:s.y+X}),setTimeout(function(){X&&function W(_,w){_===window?window.scrollTo(_.pageXOffset,_.pageYOffset+w):_.scrollTop+=w}(_,X),J&&function F(_,w){_===window?window.scrollTo(_.pageXOffset+w,_.pageYOffset):_.scrollLeft+=w}(_,J)})}window.addEventListener("mousedown",M,!1),window.addEventListener("touchstart",M,!1),window.addEventListener("mouseup",E,!1),window.addEventListener("touchend",E,!1),window.addEventListener("pointerup",E,!1),window.addEventListener("mousemove",A,!1),window.addEventListener("touchmove",A,!1),window.addEventListener("mouseleave",R,!1),window.addEventListener("scroll",C,!0)}function St(t,i,e){return e?t.y>e.top&&t.y<e.bottom&&t.x>e.left&&t.x<e.right:function an(t,i){var e=it(i);return t.y>e.top&&t.y<e.bottom&&t.x>e.left&&t.x<e.right}(t,i)}function at(t,i,e){e&&e.split(" ").forEach(n=>t.addClass(i.nativeElement,n))}function be(t,i,e){e&&e.split(" ").forEach(n=>t.removeClass(i.nativeElement,n))}let xt=(()=>{class t{constructor(){this.currentDrag=new j.B}}return t.\u0275fac=function(e){return new(e||t)},t.\u0275prov=a.jDH({token:t,factory:t.\u0275fac,providedIn:"root"}),t})(),ot=(()=>{class t{constructor(e){this.elementRef=e}}return t.\u0275fac=function(e){return new(e||t)(a.rXU(a.aKT))},t.\u0275dir=a.FsC({type:t,selectors:[["","mwlDraggableScrollContainer",""]]}),t})(),Mt=(()=>{class t{constructor(e,n,r,o,s,d,m){this.element=e,this.renderer=n,this.draggableHelper=r,this.zone=o,this.vcr=s,this.scrollContainer=d,this.document=m,this.dragAxis={x:!0,y:!0},this.dragSnapGrid={},this.ghostDragEnabled=!0,this.showOriginalElementWhileDragging=!1,this.dragCursor="",this.autoScroll={margin:20},this.dragPointerDown=new a.bkB,this.dragStart=new a.bkB,this.ghostElementCreated=new a.bkB,this.dragging=new a.bkB,this.dragEnd=new a.bkB,this.pointerDown$=new j.B,this.pointerMove$=new j.B,this.pointerUp$=new j.B,this.eventListenerSubscriptions={},this.destroy$=new j.B,this.timeLongPress={timerBegin:0,timerEnd:0}}ngOnInit(){this.checkEventListeners();const e=this.pointerDown$.pipe((0,Te.p)(()=>this.canDrag()),(0,Qe.Z)(n=>{n.event.stopPropagation&&!this.scrollContainer&&n.event.stopPropagation();const r=this.renderer.createElement("style");this.renderer.setAttribute(r,"type","text/css"),this.renderer.appendChild(r,this.renderer.createText("\n          body * {\n           -moz-user-select: none;\n           -ms-user-select: none;\n           -webkit-user-select: none;\n           user-select: none;\n          }\n        ")),requestAnimationFrame(()=>{this.document.head.appendChild(r)});const o=this.getScrollPosition(),s=new N.c(g=>this.renderer.listen(this.scrollContainer?this.scrollContainer.elementRef.nativeElement:"window","scroll",M=>g.next(M))).pipe((0,qe.Z)(o),(0,me.T)(()=>this.getScrollPosition())),d=new j.B,m=new Ze;this.dragPointerDown.observers.length>0&&this.zone.run(()=>{this.dragPointerDown.next({x:0,y:0})});const p=(0,he.h)(this.pointerUp$,this.pointerDown$,m,this.destroy$).pipe((0,_e.u)()),v=(0,Qt.z)([this.pointerMove$,s]).pipe((0,me.T)(([g,C])=>({currentDrag$:d,transformX:g.clientX-n.clientX,transformY:g.clientY-n.clientY,clientX:g.clientX,clientY:g.clientY,scrollLeft:C.left,scrollTop:C.top,target:g.event.target})),(0,me.T)(g=>(this.dragSnapGrid.x&&(g.transformX=Math.round(g.transformX/this.dragSnapGrid.x)*this.dragSnapGrid.x),this.dragSnapGrid.y&&(g.transformY=Math.round(g.transformY/this.dragSnapGrid.y)*this.dragSnapGrid.y),g)),(0,me.T)(g=>(this.dragAxis.x||(g.transformX=0),this.dragAxis.y||(g.transformY=0),g)),(0,me.T)(g=>{const C=g.scrollLeft-o.left,M=g.scrollTop-o.top;return{...g,x:g.transformX+C,y:g.transformY+M}}),(0,Te.p)(({x:g,y:C,transformX:M,transformY:E})=>!this.validateDrag||this.validateDrag({x:g,y:C,transform:{x:M,y:E}})),(0,x.Q)(p),(0,_e.u)()),y=v.pipe((0,ke.s)(1),(0,_e.u)()),S=v.pipe((0,qt.o)(1),(0,_e.u)());return y.subscribe(({clientX:g,clientY:C,x:M,y:E})=>{if(this.dragStart.observers.length>0&&this.zone.run(()=>{this.dragStart.next({cancelDrag$:m})}),this.scroller=function kt(t,i){return new ln(t,i)}([this.scrollContainer?this.scrollContainer.elementRef.nativeElement:this.document.defaultView],{...this.autoScroll,autoScroll:()=>!0}),at(this.renderer,this.element,this.dragActiveClass),this.ghostDragEnabled){const O=this.element.nativeElement.getBoundingClientRect(),R=this.element.nativeElement.cloneNode(!0);if(this.showOriginalElementWhileDragging||this.renderer.setStyle(this.element.nativeElement,"visibility","hidden"),this.ghostElementAppendTo?this.ghostElementAppendTo.appendChild(R):this.element.nativeElement.parentNode.insertBefore(R,this.element.nativeElement.nextSibling),this.ghostElement=R,this.document.body.style.cursor=this.dragCursor,this.setElementStyles(R,{position:"fixed",top:`${O.top}px`,left:`${O.left}px`,width:`${O.width}px`,height:`${O.height}px`,cursor:this.dragCursor,margin:"0",willChange:"transform",pointerEvents:"none"}),this.ghostElementTemplate){const b=this.vcr.createEmbeddedView(this.ghostElementTemplate);R.innerHTML="",b.rootNodes.filter(V=>V instanceof Node).forEach(V=>{R.appendChild(V)}),S.subscribe(()=>{this.vcr.remove(this.vcr.indexOf(b))})}this.ghostElementCreated.observers.length>0&&this.zone.run(()=>{this.ghostElementCreated.emit({clientX:g-M,clientY:C-E,element:R})}),S.subscribe(()=>{R.parentElement.removeChild(R),this.ghostElement=null,this.renderer.setStyle(this.element.nativeElement,"visibility","")})}this.draggableHelper.currentDrag.next(d)}),S.pipe((0,Qe.Z)(g=>{const C=m.pipe(function en(t){return function gt(t,i){return(0,ft.N)((0,vt.S)(t,i,arguments.length>=2,!1,!0))}((i,e,n)=>!t||t(e,n)?i+1:i,0)}(),(0,ke.s)(1),(0,me.T)(M=>({...g,dragCancelled:M>0})));return m.complete(),C})).subscribe(({x:g,y:C,dragCancelled:M})=>{this.scroller.destroy(),this.dragEnd.observers.length>0&&this.zone.run(()=>{this.dragEnd.next({x:g,y:C,dragCancelled:M})}),be(this.renderer,this.element,this.dragActiveClass),d.complete()}),(0,he.h)(p,S).pipe((0,ke.s)(1)).subscribe(()=>{requestAnimationFrame(()=>{this.document.head.removeChild(r)})}),v}),(0,_e.u)());(0,he.h)(e.pipe((0,ke.s)(1),(0,me.T)(n=>[,n])),e.pipe(wt())).pipe((0,Te.p)(([n,r])=>!n||n.x!==r.x||n.y!==r.y),(0,me.T)(([n,r])=>r)).subscribe(({x:n,y:r,currentDrag$:o,clientX:s,clientY:d,transformX:m,transformY:p,target:v})=>{this.dragging.observers.length>0&&this.zone.run(()=>{this.dragging.next({x:n,y:r})}),requestAnimationFrame(()=>{if(this.ghostElement){const y=`translate3d(${m}px, ${p}px, 0px)`;this.setElementStyles(this.ghostElement,{transform:y,"-webkit-transform":y,"-ms-transform":y,"-moz-transform":y,"-o-transform":y})}}),o.next({clientX:s,clientY:d,dropData:this.dropData,target:v})})}ngOnChanges(e){e.dragAxis&&this.checkEventListeners()}ngOnDestroy(){this.unsubscribeEventListeners(),this.pointerDown$.complete(),this.pointerMove$.complete(),this.pointerUp$.complete(),this.destroy$.next()}checkEventListeners(){const e=this.canDrag(),n=Object.keys(this.eventListenerSubscriptions).length>0;e&&!n?this.zone.runOutsideAngular(()=>{this.eventListenerSubscriptions.mousedown=this.renderer.listen(this.element.nativeElement,"mousedown",r=>{this.onMouseDown(r)}),this.eventListenerSubscriptions.mouseup=this.renderer.listen("document","mouseup",r=>{this.onMouseUp(r)}),this.eventListenerSubscriptions.touchstart=this.renderer.listen(this.element.nativeElement,"touchstart",r=>{this.onTouchStart(r)}),this.eventListenerSubscriptions.touchend=this.renderer.listen("document","touchend",r=>{this.onTouchEnd(r)}),this.eventListenerSubscriptions.touchcancel=this.renderer.listen("document","touchcancel",r=>{this.onTouchEnd(r)}),this.eventListenerSubscriptions.mouseenter=this.renderer.listen(this.element.nativeElement,"mouseenter",()=>{this.onMouseEnter()}),this.eventListenerSubscriptions.mouseleave=this.renderer.listen(this.element.nativeElement,"mouseleave",()=>{this.onMouseLeave()})}):!e&&n&&this.unsubscribeEventListeners()}onMouseDown(e){0===e.button&&(this.eventListenerSubscriptions.mousemove||(this.eventListenerSubscriptions.mousemove=this.renderer.listen("document","mousemove",n=>{this.pointerMove$.next({event:n,clientX:n.clientX,clientY:n.clientY})})),this.pointerDown$.next({event:e,clientX:e.clientX,clientY:e.clientY}))}onMouseUp(e){0===e.button&&(this.eventListenerSubscriptions.mousemove&&(this.eventListenerSubscriptions.mousemove(),delete this.eventListenerSubscriptions.mousemove),this.pointerUp$.next({event:e,clientX:e.clientX,clientY:e.clientY}))}onTouchStart(e){let n,r,o;if(this.touchStartLongPress&&(this.timeLongPress.timerBegin=Date.now(),r=!1,o=this.hasScrollbar(),n=this.getScrollPosition()),!this.eventListenerSubscriptions.touchmove){const s=(0,Je.R)(this.document,"contextmenu").subscribe(m=>{m.preventDefault()}),d=(0,Je.R)(this.document,"touchmove",{passive:!1}).subscribe(m=>{this.touchStartLongPress&&!r&&o&&(r=this.shouldBeginDrag(e,m,n)),(!this.touchStartLongPress||!o||r)&&(m.preventDefault(),this.pointerMove$.next({event:m,clientX:m.targetTouches[0].clientX,clientY:m.targetTouches[0].clientY}))});this.eventListenerSubscriptions.touchmove=()=>{s.unsubscribe(),d.unsubscribe()}}this.pointerDown$.next({event:e,clientX:e.touches[0].clientX,clientY:e.touches[0].clientY})}onTouchEnd(e){this.eventListenerSubscriptions.touchmove&&(this.eventListenerSubscriptions.touchmove(),delete this.eventListenerSubscriptions.touchmove,this.touchStartLongPress&&this.enableScroll()),this.pointerUp$.next({event:e,clientX:e.changedTouches[0].clientX,clientY:e.changedTouches[0].clientY})}onMouseEnter(){this.setCursor(this.dragCursor)}onMouseLeave(){this.setCursor("")}canDrag(){return this.dragAxis.x||this.dragAxis.y}setCursor(e){this.eventListenerSubscriptions.mousemove||this.renderer.setStyle(this.element.nativeElement,"cursor",e)}unsubscribeEventListeners(){Object.keys(this.eventListenerSubscriptions).forEach(e=>{this.eventListenerSubscriptions[e](),delete this.eventListenerSubscriptions[e]})}setElementStyles(e,n){Object.keys(n).forEach(r=>{this.renderer.setStyle(e,r,n[r])})}getScrollElement(){return this.scrollContainer?this.scrollContainer.elementRef.nativeElement:this.document.body}getScrollPosition(){return this.scrollContainer?{top:this.scrollContainer.elementRef.nativeElement.scrollTop,left:this.scrollContainer.elementRef.nativeElement.scrollLeft}:{top:window.pageYOffset||this.document.documentElement.scrollTop,left:window.pageXOffset||this.document.documentElement.scrollLeft}}shouldBeginDrag(e,n,r){const o=this.getScrollPosition(),s_top=Math.abs(o.top-r.top),s_left=Math.abs(o.left-r.left),d=Math.abs(n.targetTouches[0].clientX-e.touches[0].clientX)-s_left,m=Math.abs(n.targetTouches[0].clientY-e.touches[0].clientY)-s_top,v=this.touchStartLongPress;return(d+m>v.delta||s_top>0||s_left>0)&&(this.timeLongPress.timerBegin=Date.now()),this.timeLongPress.timerEnd=Date.now(),this.timeLongPress.timerEnd-this.timeLongPress.timerBegin>=v.delay&&(this.disableScroll(),!0)}enableScroll(){this.scrollContainer&&this.renderer.setStyle(this.scrollContainer.elementRef.nativeElement,"overflow",""),this.renderer.setStyle(this.document.body,"overflow","")}disableScroll(){this.scrollContainer&&this.renderer.setStyle(this.scrollContainer.elementRef.nativeElement,"overflow","hidden"),this.renderer.setStyle(this.document.body,"overflow","hidden")}hasScrollbar(){const e=this.getScrollElement();return e.scrollWidth>e.clientWidth||e.scrollHeight>e.clientHeight}}return t.\u0275fac=function(e){return new(e||t)(a.rXU(a.aKT),a.rXU(a.sFG),a.rXU(xt),a.rXU(a.SKi),a.rXU(a.c1b),a.rXU(ot,8),a.rXU(l.qQ))},t.\u0275dir=a.FsC({type:t,selectors:[["","mwlDraggable",""]],inputs:{dropData:"dropData",dragAxis:"dragAxis",dragSnapGrid:"dragSnapGrid",ghostDragEnabled:"ghostDragEnabled",showOriginalElementWhileDragging:"showOriginalElementWhileDragging",validateDrag:"validateDrag",dragCursor:"dragCursor",dragActiveClass:"dragActiveClass",ghostElementAppendTo:"ghostElementAppendTo",ghostElementTemplate:"ghostElementTemplate",touchStartLongPress:"touchStartLongPress",autoScroll:"autoScroll"},outputs:{dragPointerDown:"dragPointerDown",dragStart:"dragStart",ghostElementCreated:"ghostElementCreated",dragging:"dragging",dragEnd:"dragEnd"},features:[a.OA$]}),t})();function st(t,i,e){return t>=e.left&&t<=e.right&&i>=e.top&&i<=e.bottom}let dn=(()=>{class t{constructor(e,n,r,o,s){this.element=e,this.draggableHelper=n,this.zone=r,this.renderer=o,this.scrollContainer=s,this.dragEnter=new a.bkB,this.dragLeave=new a.bkB,this.dragOver=new a.bkB,this.drop=new a.bkB}ngOnInit(){this.currentDragSubscription=this.draggableHelper.currentDrag.subscribe(e=>{at(this.renderer,this.element,this.dragActiveClass);const n={updateCache:!0},r=this.renderer.listen(this.scrollContainer?this.scrollContainer.elementRef.nativeElement:"window","scroll",()=>{n.updateCache=!0});let o;const s=e.pipe((0,me.T)(({clientX:p,clientY:v,dropData:y,target:S})=>{o={clientX:p,clientY:v,dropData:y,target:S},n.updateCache&&(n.rect=this.element.nativeElement.getBoundingClientRect(),this.scrollContainer&&(n.scrollContainerRect=this.scrollContainer.elementRef.nativeElement.getBoundingClientRect()),n.updateCache=!1);const g=st(p,v,n.rect),C=!this.validateDrop||this.validateDrop({clientX:p,clientY:v,target:S,dropData:y});return n.scrollContainerRect?g&&C&&st(p,v,n.scrollContainerRect):g&&C})),d=s.pipe((0,_t.F)());let m;d.pipe((0,Te.p)(p=>p)).subscribe(()=>{m=!0,at(this.renderer,this.element,this.dragOverClass),this.dragEnter.observers.length>0&&this.zone.run(()=>{this.dragEnter.next(o)})}),s.pipe((0,Te.p)(p=>p)).subscribe(()=>{this.dragOver.observers.length>0&&this.zone.run(()=>{this.dragOver.next(o)})}),d.pipe(wt(),(0,Te.p)(([p,v])=>p&&!v)).subscribe(()=>{m=!1,be(this.renderer,this.element,this.dragOverClass),this.dragLeave.observers.length>0&&this.zone.run(()=>{this.dragLeave.next(o)})}),e.subscribe({complete:()=>{r(),be(this.renderer,this.element,this.dragActiveClass),m&&(be(this.renderer,this.element,this.dragOverClass),this.drop.observers.length>0&&this.zone.run(()=>{this.drop.next(o)}))}})})}ngOnDestroy(){this.currentDragSubscription&&this.currentDragSubscription.unsubscribe()}}return t.\u0275fac=function(e){return new(e||t)(a.rXU(a.aKT),a.rXU(xt),a.rXU(a.SKi),a.rXU(a.sFG),a.rXU(ot,8))},t.\u0275dir=a.FsC({type:t,selectors:[["","mwlDroppable",""]],inputs:{dragOverClass:"dragOverClass",dragActiveClass:"dragActiveClass",validateDrop:"validateDrop"},outputs:{dragEnter:"dragEnter",dragLeave:"dragLeave",dragOver:"dragOver",drop:"drop"}}),t})(),Ve=(()=>{class t{}return t.\u0275fac=function(e){return new(e||t)},t.\u0275mod=a.$C({type:t}),t.\u0275inj=a.G2t({}),t})();var ce=D(9969);!(typeof window>"u")&&("ontouchstart"in window||navigator.maxTouchPoints>0||navigator),Object.freeze({topLeft:"nw-resize",topRight:"ne-resize",bottomLeft:"sw-resize",bottomRight:"se-resize",leftOrRight:"col-resize",topOrBottom:"row-resize"});let Rt=(()=>{class t{}return t.\u0275fac=function(e){return new(e||t)},t.\u0275mod=a.$C({type:t}),t.\u0275inj=a.G2t({}),t})();const zt=function(t){return{action:t}};function fn(t,i){if(1&t){const e=a.RV6();a.j41(0,"a",5),a.bIt("mwlClick",function(r){const s=a.eBV(e).$implicit,d=a.XpG(2).event;return a.Njj(s.onClick({event:d,sourceEvent:r}))})("mwlKeydownEnter",function(r){const s=a.eBV(e).$implicit,d=a.XpG(2).event;return a.Njj(s.onClick({event:d,sourceEvent:r}))}),a.nI1(1,"calendarA11y"),a.k0s()}if(2&t){const e=i.$implicit;a.Y8G("ngClass",e.cssClass)("innerHtml",e.label,a.npT),a.BMQ("aria-label",a.i5U(1,3,a.eq3(6,zt,e),"actionButtonLabel"))}}function Ht(t,i){if(1&t&&(a.j41(0,"span",3),a.DNE(1,fn,2,8,"a",4),a.k0s()),2&t){const e=a.XpG(),n=e.event,r=e.trackByActionId;a.R7$(1),a.Y8G("ngForOf",n.actions)("ngForTrackBy",r)}}function gn(t,i){1&t&&a.DNE(0,Ht,2,2,"span",2),2&t&&a.Y8G("ngIf",i.event.actions)}function Wt(t,i){}const yn=function(t,i){return{event:t,trackByActionId:i}},Ce=function(){return{}};function wn(t,i){if(1&t&&(a.nrm(0,"span",2),a.nI1(1,"calendarEventTitle"),a.nI1(2,"calendarA11y")),2&t){const e=i.event;a.Y8G("innerHTML",a.brH(1,2,e.title,i.view,e),a.npT),a.BMQ("aria-hidden",a.i5U(2,6,a.lJ4(9,Ce),"hideEventTitle"))}}function Vt(t,i){}const _n=function(t,i){return{event:t,view:i}};function It(t,i){if(1&t&&(a.j41(0,"div",2),a.nrm(1,"div",3)(2,"div",4),a.k0s()),2&t){const e=i.contents;a.Y8G("ngClass","cal-tooltip-"+i.placement),a.R7$(2),a.Y8G("innerHtml",e,a.npT)}}function Dn(t,i){}const Ft=function(t,i,e){return{contents:t,placement:i,event:e}};function u(t,i){if(1&t&&(a.j41(0,"span",7),a.EFF(1),a.k0s()),2&t){const e=a.XpG().day;a.R7$(1),a.JRh(e.badgeTotal)}}const c=function(t){return{backgroundColor:t}},h=function(t,i){return{event:t,draggedFrom:i}},f=function(t,i){return{x:t,y:i}},z=function(){return{delay:300,delta:30}};function Y(t,i){if(1&t){const e=a.RV6();a.j41(0,"div",10),a.bIt("mouseenter",function(){const o=a.eBV(e).$implicit,s=a.XpG(2).highlightDay;return a.Njj(s.emit({event:o}))})("mouseleave",function(){const o=a.eBV(e).$implicit,s=a.XpG(2).unhighlightDay;return a.Njj(s.emit({event:o}))})("mwlClick",function(r){const s=a.eBV(e).$implicit,d=a.XpG(2).eventClicked;return a.Njj(d.emit({event:s,sourceEvent:r}))}),a.nI1(1,"calendarEventTitle"),a.nI1(2,"calendarA11y"),a.k0s()}if(2&t){const e=i.$implicit,n=a.XpG(2),r=n.tooltipPlacement,o=n.tooltipTemplate,s=n.tooltipAppendToBody,d=n.tooltipDelay,m=n.day,p=n.validateDrag;a.AVh("cal-draggable",e.draggable),a.Y8G("ngStyle",a.eq3(22,c,null==e.color?null:e.color.primary))("ngClass",null==e?null:e.cssClass)("mwlCalendarTooltip",a.brH(1,15,e.title,"monthTooltip",e))("tooltipPlacement",r)("tooltipEvent",e)("tooltipTemplate",o)("tooltipAppendToBody",s)("tooltipDelay",d)("dropData",a.l_i(24,h,e,m))("dragAxis",a.l_i(27,f,e.draggable,e.draggable))("validateDrag",p)("touchStartLongPress",a.lJ4(30,z)),a.BMQ("aria-hidden",a.i5U(2,19,a.lJ4(31,Ce),"hideMonthCellEvents"))}}function q(t,i){if(1&t&&(a.j41(0,"div",8),a.DNE(1,Y,3,32,"div",9),a.k0s()),2&t){const e=a.XpG(),n=e.day,r=e.trackByEventId;a.R7$(1),a.Y8G("ngForOf",n.events)("ngForTrackBy",r)}}const ge=function(t,i){return{day:t,locale:i}};function Fe(t,i){if(1&t&&(a.j41(0,"div",2),a.nI1(1,"calendarA11y"),a.j41(2,"span",3),a.DNE(3,u,2,1,"span",4),a.j41(4,"span",5),a.EFF(5),a.nI1(6,"calendarDate"),a.k0s()()(),a.DNE(7,q,2,2,"div",6)),2&t){const e=i.day,n=i.locale;a.BMQ("aria-label",a.i5U(1,4,a.l_i(11,ge,e,n),"monthCell")),a.R7$(3),a.Y8G("ngIf",e.badgeTotal>0),a.R7$(2),a.JRh(a.brH(6,7,e.date,"monthViewDayNumber",n)),a.R7$(2),a.Y8G("ngIf",e.events.length>0)}}function An(t,i){}const Pn=function(t,i,e,n,r,o,s,d,m,p,v,y){return{day:t,openDay:i,locale:e,tooltipPlacement:n,highlightDay:r,unhighlightDay:o,eventClicked:s,tooltipTemplate:d,tooltipAppendToBody:m,tooltipDelay:p,trackByEventId:v,validateDrag:y}},$n=function(t){return{event:t}},Cn=function(t,i){return{event:t,locale:i}};function Ln(t,i){if(1&t){const e=a.RV6();a.j41(0,"div",7),a.nrm(1,"span",8),a.EFF(2," "),a.j41(3,"mwl-calendar-event-title",9),a.bIt("mwlClick",function(r){const s=a.eBV(e).$implicit,d=a.XpG(2).eventClicked;return a.Njj(d.emit({event:s,sourceEvent:r}))})("mwlKeydownEnter",function(r){const s=a.eBV(e).$implicit,d=a.XpG(2).eventClicked;return a.Njj(d.emit({event:s,sourceEvent:r}))}),a.nI1(4,"calendarA11y"),a.k0s(),a.EFF(5," "),a.nrm(6,"mwl-calendar-event-actions",10),a.k0s()}if(2&t){const e=i.$implicit,n=a.XpG(2).validateDrag,r=a.XpG();a.AVh("cal-draggable",e.draggable),a.Y8G("ngClass",null==e?null:e.cssClass)("dropData",a.eq3(16,$n,e))("dragAxis",a.l_i(18,f,e.draggable,e.draggable))("validateDrag",n)("touchStartLongPress",a.lJ4(21,z)),a.R7$(1),a.Y8G("ngStyle",a.eq3(22,c,null==e.color?null:e.color.primary)),a.R7$(2),a.Y8G("event",e)("customTemplate",r.eventTitleTemplate),a.BMQ("aria-label",a.i5U(4,13,a.l_i(24,Cn,e,r.locale),"eventDescription")),a.R7$(3),a.Y8G("event",e)("customTemplate",r.eventActionsTemplate)}}const En=function(t,i){return{date:t,locale:i}};function Yn(t,i){if(1&t&&(a.j41(0,"div",3),a.nrm(1,"span",4),a.nI1(2,"calendarA11y"),a.nrm(3,"span",5),a.nI1(4,"calendarA11y"),a.DNE(5,Ln,7,27,"div",6),a.k0s()),2&t){const e=a.XpG(),n=e.events,r=e.trackByEventId,o=a.XpG();a.Y8G("@collapse",void 0),a.R7$(1),a.BMQ("aria-label",a.i5U(2,5,a.l_i(11,En,o.date,o.locale),"openDayEventsAlert")),a.R7$(2),a.BMQ("aria-label",a.i5U(4,8,a.l_i(14,En,o.date,o.locale),"openDayEventsLandmark")),a.R7$(2),a.Y8G("ngForOf",n)("ngForTrackBy",r)}}function jn(t,i){1&t&&a.DNE(0,Yn,6,17,"div",2),2&t&&a.Y8G("ngIf",i.isOpen)}function Nn(t,i){}const Un=function(t,i,e,n,r){return{events:t,eventClicked:i,isOpen:e,trackByEventId:n,validateDrag:r}};function Gn(t,i){if(1&t){const e=a.RV6();a.j41(0,"div",4),a.bIt("click",function(r){const s=a.eBV(e).$implicit,d=a.XpG(2);return a.Njj(d.columnHeaderClicked.emit({isoDayNumber:s.day,sourceEvent:r}))}),a.EFF(1),a.nI1(2,"calendarDate"),a.k0s()}if(2&t){const e=i.$implicit,n=a.XpG().locale;a.AVh("cal-past",e.isPast)("cal-today",e.isToday)("cal-future",e.isFuture)("cal-weekend",e.isWeekend),a.Y8G("ngClass",e.cssClass),a.R7$(1),a.SpI(" ",a.brH(2,10,e.date,"monthViewColumnHeader",n)," ")}}function Xn(t,i){if(1&t&&(a.j41(0,"div",2),a.DNE(1,Gn,3,14,"div",3),a.k0s()),2&t){const e=i.days,n=i.trackByWeekDayHeaderDate;a.R7$(1),a.Y8G("ngForOf",e)("ngForTrackBy",n)}}function Kn(t,i){}const Zn=function(t,i,e){return{days:t,locale:i,trackByWeekDayHeaderDate:e}};function Jn(t,i){if(1&t){const e=a.RV6();a.j41(0,"mwl-calendar-month-cell",7),a.bIt("mwlClick",function(r){const s=a.eBV(e).$implicit,d=a.XpG(2);return a.Njj(d.dayClicked.emit({day:s,sourceEvent:r}))})("mwlKeydownEnter",function(r){const s=a.eBV(e).$implicit,d=a.XpG(2);return a.Njj(d.dayClicked.emit({day:s,sourceEvent:r}))})("highlightDay",function(r){a.eBV(e);const o=a.XpG(2);return a.Njj(o.toggleDayHighlight(r.event,!0))})("unhighlightDay",function(r){a.eBV(e);const o=a.XpG(2);return a.Njj(o.toggleDayHighlight(r.event,!1))})("drop",function(r){const s=a.eBV(e).$implicit,d=a.XpG(2);return a.Njj(d.eventDropped(s,r.dropData.event,r.dropData.draggedFrom))})("eventClicked",function(r){a.eBV(e);const o=a.XpG(2);return a.Njj(o.eventClicked.emit({event:r.event,sourceEvent:r.sourceEvent}))}),a.nI1(1,"calendarA11y"),a.k0s()}if(2&t){const e=i.$implicit,n=a.XpG(2);a.Y8G("ngClass",null==e?null:e.cssClass)("day",e)("openDay",n.openDay)("locale",n.locale)("tooltipPlacement",n.tooltipPlacement)("tooltipAppendToBody",n.tooltipAppendToBody)("tooltipTemplate",n.tooltipTemplate)("tooltipDelay",n.tooltipDelay)("customTemplate",n.cellTemplate)("ngStyle",a.eq3(15,c,e.backgroundColor))("clickListenerDisabled",0===n.dayClicked.observers.length),a.BMQ("tabindex",a.i5U(1,12,a.lJ4(17,Ce),"monthCellTabIndex"))}}function Qn(t,i){if(1&t){const e=a.RV6();a.j41(0,"div")(1,"div",4),a.DNE(2,Jn,2,18,"mwl-calendar-month-cell",5),a.nI1(3,"slice"),a.k0s(),a.j41(4,"mwl-calendar-open-day-events",6),a.bIt("eventClicked",function(r){a.eBV(e);const o=a.XpG();return a.Njj(o.eventClicked.emit({event:r.event,sourceEvent:r.sourceEvent}))})("drop",function(r){a.eBV(e);const o=a.XpG();return a.Njj(o.eventDropped(o.openDay,r.dropData.event,r.dropData.draggedFrom))}),a.k0s()()}if(2&t){const e=i.$implicit,n=a.XpG();a.R7$(2),a.Y8G("ngForOf",a.brH(3,9,n.view.days,e,e+n.view.totalDaysVisibleInWeek))("ngForTrackBy",n.trackByDate),a.R7$(2),a.Y8G("locale",n.locale)("isOpen",n.openRowIndex===e)("events",null==n.openDay?null:n.openDay.events)("date",null==n.openDay?null:n.openDay.date)("customTemplate",n.openDayEventsTemplate)("eventTitleTemplate",n.eventTitleTemplate)("eventActionsTemplate",n.eventActionsTemplate)}}let dt=(()=>{class t{constructor(e,n,r){this.renderer=e,this.elm=n,this.document=r,this.clickListenerDisabled=!1,this.click=new a.bkB,this.destroy$=new j.B}ngOnInit(){this.clickListenerDisabled||this.listen().pipe((0,x.Q)(this.destroy$)).subscribe(e=>{e.stopPropagation(),this.click.emit(e)})}ngOnDestroy(){this.destroy$.next()}listen(){return new N.c(e=>this.renderer.listen(this.elm.nativeElement,"click",n=>{e.next(n)}))}}return t.\u0275fac=function(e){return new(e||t)(a.rXU(a.sFG),a.rXU(a.aKT),a.rXU(l.qQ))},t.\u0275dir=a.FsC({type:t,selectors:[["","mwlClick",""]],inputs:{clickListenerDisabled:"clickListenerDisabled"},outputs:{click:"mwlClick"}}),t})(),Bt=(()=>{class t{constructor(e,n,r){this.host=e,this.ngZone=n,this.renderer=r,this.keydown=new a.bkB,this.keydownListener=null}ngOnInit(){this.ngZone.runOutsideAngular(()=>{this.keydownListener=this.renderer.listen(this.host.nativeElement,"keydown",e=>{(13===e.keyCode||13===e.which||"Enter"===e.key)&&(e.preventDefault(),e.stopPropagation(),this.ngZone.run(()=>{this.keydown.emit(e)}))})})}ngOnDestroy(){null!==this.keydownListener&&(this.keydownListener(),this.keydownListener=null)}}return t.\u0275fac=function(e){return new(e||t)(a.rXU(a.aKT),a.rXU(a.SKi),a.rXU(a.sFG))},t.\u0275dir=a.FsC({type:t,selectors:[["","mwlKeydownEnter",""]],outputs:{keydown:"mwlKeydownEnter"}}),t})(),ct=(()=>{class t{constructor(e){this.i18nPlural=e}monthCell({day:e,locale:n}){return e.badgeTotal>0?`\n        ${(0,l.Yq)(e.date,"EEEE MMMM d",n)},\n        ${this.i18nPlural.transform(e.badgeTotal,{"=0":"No events","=1":"One event",other:"# events"})},\n         click to expand\n      `:`${(0,l.Yq)(e.date,"EEEE MMMM d",n)}`}openDayEventsLandmark({date:e,locale:n}){return`\n      Beginning of expanded view for ${(0,l.Yq)(e,"EEEE MMMM dd",n)}\n    `}openDayEventsAlert({date:e,locale:n}){return`${(0,l.Yq)(e,"EEEE MMMM dd",n)} expanded`}eventDescription({event:e,locale:n}){if(!0===e.allDay)return this.allDayEventDescription({event:e,locale:n});const r=`\n      ${(0,l.Yq)(e.start,"EEEE MMMM dd",n)},\n      ${e.title}, from ${(0,l.Yq)(e.start,"hh:mm a",n)}\n    `;return e.end?r+` to ${(0,l.Yq)(e.end,"hh:mm a",n)}`:r}allDayEventDescription({event:e,locale:n}){const r=`\n      ${e.title}, event spans multiple days:\n      start time ${(0,l.Yq)(e.start,"MMMM dd hh:mm a",n)}\n    `;return e.end?r+`, stop time ${(0,l.Yq)(e.end,"MMMM d hh:mm a",n)}`:r+", no stop time"}actionButtonLabel({action:e}){return e.a11yLabel}monthCellTabIndex(){return 0}hideMonthCellEvents(){return!0}hideEventTitle(){return!0}hideWeekHourSegment(){return!0}hideDayHourSegment(){return!0}}return t.\u0275fac=function(e){return new(e||t)(a.KVO(l.jo))},t.\u0275prov=a.jDH({token:t,factory:t.\u0275fac}),t})(),Ne=(()=>{class t{constructor(e,n){this.calendarA11y=e,this.locale=n}transform(e,n){if(e.locale=e.locale||this.locale,typeof this.calendarA11y[n]>"u"){const r=Object.getOwnPropertyNames(Object.getPrototypeOf(ct.prototype)).filter(o=>"constructor"!==o);throw new Error(`${n} is not a valid a11y method. Can only be one of ${r.join(", ")}`)}return this.calendarA11y[n](e)}}return t.\u0275fac=function(e){return new(e||t)(a.rXU(ct,16),a.rXU(a.xe9,16))},t.\u0275pipe=a.EJ8({name:"calendarA11y",type:t,pure:!0}),t})(),ki=(()=>{class t{constructor(){this.trackByActionId=(e,n)=>n.id?n.id:n}}return t.\u0275fac=function(e){return new(e||t)},t.\u0275cmp=a.VBU({type:t,selectors:[["mwl-calendar-event-actions"]],inputs:{event:"event",customTemplate:"customTemplate"},decls:3,vars:5,consts:[["defaultTemplate",""],[3,"ngTemplateOutlet","ngTemplateOutletContext"],["class","cal-event-actions",4,"ngIf"],[1,"cal-event-actions"],["class","cal-event-action","href","javascript:;","tabindex","0","role","button",3,"ngClass","innerHtml","mwlClick","mwlKeydownEnter",4,"ngFor","ngForOf","ngForTrackBy"],["href","javascript:;","tabindex","0","role","button",1,"cal-event-action",3,"ngClass","innerHtml","mwlClick","mwlKeydownEnter"]],template:function(e,n){if(1&e&&(a.DNE(0,gn,1,1,"ng-template",null,0,a.C5r),a.DNE(2,Wt,0,0,"ng-template",1)),2&e){const r=a.sdS(1);a.R7$(2),a.Y8G("ngTemplateOutlet",n.customTemplate||r)("ngTemplateOutletContext",a.l_i(2,yn,n.event,n.trackByActionId))}},dependencies:[l.YU,l.Sq,l.bT,l.T3,dt,Bt,Ne],encapsulation:2}),t})();class At{month(i,e){return i.title}monthTooltip(i,e){return i.title}week(i,e){return i.title}weekTooltip(i,e){return i.title}day(i,e){return i.title}dayTooltip(i,e){return i.title}}let kn=(()=>{class t{constructor(e){this.calendarEventTitle=e}transform(e,n,r){return this.calendarEventTitle[n](r,e)}}return t.\u0275fac=function(e){return new(e||t)(a.rXU(At,16))},t.\u0275pipe=a.EJ8({name:"calendarEventTitle",type:t,pure:!0}),t})(),Si=(()=>{class t{}return t.\u0275fac=function(e){return new(e||t)},t.\u0275cmp=a.VBU({type:t,selectors:[["mwl-calendar-event-title"]],inputs:{event:"event",customTemplate:"customTemplate",view:"view"},decls:3,vars:5,consts:[["defaultTemplate",""],[3,"ngTemplateOutlet","ngTemplateOutletContext"],[1,"cal-event-title",3,"innerHTML"]],template:function(e,n){if(1&e&&(a.DNE(0,wn,3,10,"ng-template",null,0,a.C5r),a.DNE(2,Vt,0,0,"ng-template",1)),2&e){const r=a.sdS(1);a.R7$(2),a.Y8G("ngTemplateOutlet",n.customTemplate||r)("ngTemplateOutletContext",a.l_i(2,_n,n.event,n.view))}},dependencies:[l.T3,kn,Ne],encapsulation:2}),t})(),bi=(()=>{class t{}return t.\u0275fac=function(e){return new(e||t)},t.\u0275cmp=a.VBU({type:t,selectors:[["mwl-calendar-tooltip-window"]],inputs:{contents:"contents",placement:"placement",event:"event",customTemplate:"customTemplate"},decls:3,vars:6,consts:[["defaultTemplate",""],[3,"ngTemplateOutlet","ngTemplateOutletContext"],[1,"cal-tooltip",3,"ngClass"],[1,"cal-tooltip-arrow"],[1,"cal-tooltip-inner",3,"innerHtml"]],template:function(e,n){if(1&e&&(a.DNE(0,It,3,2,"ng-template",null,0,a.C5r),a.DNE(2,Dn,0,0,"ng-template",1)),2&e){const r=a.sdS(1);a.R7$(2),a.Y8G("ngTemplateOutlet",n.customTemplate||r)("ngTemplateOutletContext",a.sMw(2,Ft,n.contents,n.placement,n.event))}},dependencies:[l.YU,l.T3],encapsulation:2}),t})(),xi=(()=>{class t{constructor(e,n,r,o,s,d){this.elementRef=e,this.injector=n,this.renderer=r,this.viewContainerRef=s,this.document=d,this.placement="auto",this.delay=null,this.cancelTooltipDelay$=new j.B,this.tooltipFactory=o.resolveComponentFactory(bi)}ngOnChanges(e){this.tooltipRef&&(e.contents||e.customTemplate||e.event)&&(this.tooltipRef.instance.contents=this.contents,this.tooltipRef.instance.customTemplate=this.customTemplate,this.tooltipRef.instance.event=this.event,this.tooltipRef.changeDetectorRef.markForCheck(),this.contents||this.hide())}ngOnDestroy(){this.hide()}onMouseOver(){(null===this.delay?(0,ee.of)("now"):(0,T.O)(this.delay)).pipe((0,x.Q)(this.cancelTooltipDelay$)).subscribe(()=>{this.show()})}onMouseOut(){this.hide()}show(){!this.tooltipRef&&this.contents&&(this.tooltipRef=this.viewContainerRef.createComponent(this.tooltipFactory,0,this.injector,[]),this.tooltipRef.instance.contents=this.contents,this.tooltipRef.instance.customTemplate=this.customTemplate,this.tooltipRef.instance.event=this.event,this.appendToBody&&this.document.body.appendChild(this.tooltipRef.location.nativeElement),requestAnimationFrame(()=>{this.positionTooltip()}))}hide(){this.tooltipRef&&(this.viewContainerRef.remove(this.viewContainerRef.indexOf(this.tooltipRef.hostView)),this.tooltipRef=null),this.cancelTooltipDelay$.next()}positionTooltip(e=[]){this.tooltipRef&&(this.tooltipRef.changeDetectorRef.detectChanges(),this.tooltipRef.instance.placement=function H(t,i,e,n,r){var o=Array.isArray(e)?e:e.split(K),s=["top","bottom","left","right","top-left","top-right","bottom-left","bottom-right","left-top","left-bottom","right-top","right-bottom"],d=i.classList,m=function(E){var O=E.split("-"),R=O[0],b=O[1],V=[];return r&&(V.push(r+"-"+R),b&&V.push(r+"-"+R+"-"+b),V.forEach(function(A){d.add(A)})),V};r&&s.forEach(function(E){d.remove(r+"-"+E)});var p=o.findIndex(function(E){return"auto"===E});p>=0&&s.forEach(function(E){null==o.find(function(O){return-1!==O.search("^"+E)})&&o.splice(p++,1,E)});var v=i.style;v.position="absolute",v.top="0",v.left="0",v["will-change"]="transform";for(var y,S=!1,g=0,C=o;g<C.length;g++){var M=m(y=C[g]);if(pe.positionElements(t,i,y,n)){S=!0;break}r&&M.forEach(function(E){d.remove(E)})}return S||(m(y=o[0]),pe.positionElements(t,i,y,n)),y}(this.elementRef.nativeElement,this.tooltipRef.location.nativeElement.children[0],this.placement,this.appendToBody),-1===e.indexOf(this.tooltipRef.instance.placement)&&this.positionTooltip([...e,this.tooltipRef.instance.placement]))}}return t.\u0275fac=function(e){return new(e||t)(a.rXU(a.aKT),a.rXU(a.zZn),a.rXU(a.sFG),a.rXU(a.OM3),a.rXU(a.c1b),a.rXU(l.qQ))},t.\u0275dir=a.FsC({type:t,selectors:[["","mwlCalendarTooltip",""]],hostBindings:function(e,n){1&e&&a.bIt("mouseenter",function(){return n.onMouseOver()})("mouseleave",function(){return n.onMouseOut()})},inputs:{contents:["mwlCalendarTooltip","contents"],placement:["tooltipPlacement","placement"],customTemplate:["tooltipTemplate","customTemplate"],event:["tooltipEvent","event"],appendToBody:["tooltipAppendToBody","appendToBody"],delay:["tooltipDelay","delay"]},features:[a.OA$]}),t})();var Sn=function(t){return t.Month="month",t.Week="week",t.Day="day",t}(Sn||{});const xn=(t,i)=>i.id?i.id:i,Hi=(t,i)=>i.date.toISOString();function Pt({x:t,y:i}){return Math.abs(t)>1||Math.abs(i)>1}class ut{}let Bi=(()=>{class t{constructor(e){this.dateAdapter=e}monthViewColumnHeader({date:e,locale:n}){return(0,l.Yq)(e,"EEEE",n)}monthViewDayNumber({date:e,locale:n}){return(0,l.Yq)(e,"d",n)}monthViewTitle({date:e,locale:n}){return(0,l.Yq)(e,"LLLL y",n)}weekViewColumnHeader({date:e,locale:n}){return(0,l.Yq)(e,"EEEE",n)}weekViewColumnSubHeader({date:e,locale:n}){return(0,l.Yq)(e,"MMM d",n)}weekViewTitle({date:e,locale:n,weekStartsOn:r,excludeDays:o,daysInWeek:s}){const{viewStart:d,viewEnd:m}=function Fi(t,i,e,n=[],r){let o=r?t.startOfDay(i):t.startOfWeek(i,{weekStartsOn:e});const s=t.endOfWeek(i,{weekStartsOn:e});for(;n.indexOf(t.getDay(o))>-1&&o<s;)o=t.addDays(o,1);if(r){const d=t.endOfDay(function Ii(t,i,e,n){let r=0,o=0;const s=e<0?t.subDays:t.addDays;let d=i;for(;o<=Math.abs(e);){d=s(i,r);const m=t.getDay(d);-1===n.indexOf(m)&&o++,r++}return d}(t,o,r-1,n));return{viewStart:o,viewEnd:d}}{let d=s;for(;n.indexOf(t.getDay(d))>-1&&d>o;)d=t.subDays(d,1);return{viewStart:o,viewEnd:d}}}(this.dateAdapter,e,r,o,s),p=(v,y)=>(0,l.Yq)(v,"MMM d"+(y?", yyyy":""),n);return`${p(d,d.getUTCFullYear()!==m.getUTCFullYear())} - ${p(m,!0)}`}weekViewHour({date:e,locale:n}){return(0,l.Yq)(e,"h a",n)}dayViewHour({date:e,locale:n}){return(0,l.Yq)(e,"h a",n)}dayViewTitle({date:e,locale:n}){return(0,l.Yq)(e,"EEEE, MMMM d, y",n)}}return t.\u0275fac=function(e){return new(e||t)(a.KVO(ut))},t.\u0275prov=a.jDH({token:t,factory:t.\u0275fac}),t})(),mt=(()=>{class t extends Bi{}return t.\u0275fac=function(){let i;return function(n){return(i||(i=a.xGo(t)))(n||t)}}(),t.\u0275prov=a.jDH({token:t,factory:t.\u0275fac}),t})(),Mn=(()=>{class t{constructor(e,n){this.dateFormatter=e,this.locale=n}transform(e,n,r=this.locale,o=0,s=[],d){if(typeof this.dateFormatter[n]>"u"){const m=Object.getOwnPropertyNames(Object.getPrototypeOf(mt.prototype)).filter(p=>"constructor"!==p);throw new Error(`${n} is not a valid date formatter. Can only be one of ${m.join(", ")}`)}return this.dateFormatter[n]({date:e,locale:r,weekStartsOn:o,excludeDays:s,daysInWeek:d})}}return t.\u0275fac=function(e){return new(e||t)(a.rXU(mt,16),a.rXU(a.xe9,16))},t.\u0275pipe=a.EJ8({name:"calendarDate",type:t,pure:!0}),t})(),$t=(()=>{class t{constructor(e){this.dateAdapter=e}getMonthView(e){return function Ke(t,i){var e=i.events,n=void 0===e?[]:e,r=i.viewDate,o=i.weekStartsOn,s=i.excluded,d=void 0===s?[]:s,m=i.viewStart,p=void 0===m?t.startOfMonth(r):m,v=i.viewEnd,y=void 0===v?t.endOfMonth(r):v,S=i.weekendDays;n||(n=[]);for(var W,C=t.endOfWeek,M=t.differenceInDays,E=t.startOfDay,O=t.addHours,R=t.endOfDay,b=t.isSameMonth,V=t.getDay,A=(0,t.startOfWeek)(p,{weekStartsOn:o}),Z=C(y,{weekStartsOn:o}),te=ye(t,{events:n,periodStart:A,periodEnd:Z}),B=[],F=function(Nt){var ae;if(W?(ae=E(O(W,24)),W.getTime()===ae.getTime()&&(ae=E(O(W,25))),W=ae):ae=W=A,!d.some(function(Ut){return V(ae)===Ut})){var Be=ie(t,{date:ae,weekendDays:S}),ue=ye(t,{events:te,periodStart:E(ae),periodEnd:R(ae)});Be.inMonth=b(ae,r),Be.events=ue,Be.badgeTotal=ue.length,B.push(Be)}},_=0;_<M(Z,A)+1;_++)F();var w=[],J=xe-d.length;if(J<xe)for(_=0;_<B.length;_+=J){var X=B.slice(_,_+J);X.some(function(ae){return p<=ae.date&&ae.date<y})&&(w=le(le([],w,!0),X,!0))}else w=B;var jt=Math.floor(w.length/J),pt=[];for(_=0;_<jt;_++)pt.push(_*J);return{rowOffsets:pt,totalDaysVisibleInWeek:J,days:w,period:{start:w[0].date,end:R(w[w.length-1].date),events:te}}}(this.dateAdapter,e)}getWeekViewHeader(e){return Xe(this.dateAdapter,e)}getWeekView(e){return function Kt(t,i){var e=i.events,n=void 0===e?[]:e,r=i.viewDate,o=i.weekStartsOn,s=i.excluded,d=void 0===s?[]:s,m=i.precision,p=void 0===m?"days":m,v=i.absolutePositionedEvents,y=void 0!==v&&v,S=i.hourSegments,g=i.hourDuration,C=i.dayStart,M=i.dayEnd,E=i.weekendDays,O=i.segmentHeight,R=i.minimumEventHeight,b=i.viewStart,V=void 0===b?t.startOfWeek(r,{weekStartsOn:o}):b,A=i.viewEnd,Z=void 0===A?t.endOfWeek(r,{weekStartsOn:o}):A;n||(n=[]);var B=t.endOfDay,W=ye(t,{events:n,periodStart:V=(0,t.startOfDay)(V),periodEnd:Z=B(Z)}),F=Xe(t,{viewDate:r,weekStartsOn:o,excluded:d,weekendDays:E,viewStart:V,viewEnd:Z});return{allDayEventRows:Xt(t,{events:W,excluded:d,precision:p,absolutePositionedEvents:y,viewStart:V,viewEnd:Z}),period:{events:W,start:F[0].date,end:B(F[F.length-1].date)},hourColumns:Ae(t,{events:n,viewDate:r,hourSegments:S,hourDuration:g,dayStart:C,dayEnd:M,weekStartsOn:o,excluded:d,weekendDays:E,segmentHeight:O,viewStart:V,viewEnd:Z,minimumEventHeight:R})}}(this.dateAdapter,e)}}return t.\u0275fac=function(e){return new(e||t)(a.KVO(ut))},t.\u0275prov=a.jDH({token:t,factory:t.\u0275fac}),t})();new a.nKC("Moment");var On=function(t){return t.Drag="drag",t.Drop="drop",t.Resize="resize",t}(On||{});let Ue=(()=>{class t{static forRoot(e,n={}){return{ngModule:t,providers:[e,n.eventTitleFormatter||At,n.dateFormatter||mt,n.utils||$t,n.a11y||ct]}}}return t.\u0275fac=function(e){return new(e||t)},t.\u0275mod=a.$C({type:t}),t.\u0275inj=a.G2t({providers:[l.jo],imports:[l.MD]}),t})(),Ai=(()=>{class t{constructor(){this.highlightDay=new a.bkB,this.unhighlightDay=new a.bkB,this.eventClicked=new a.bkB,this.trackByEventId=xn,this.validateDrag=Pt}}return t.\u0275fac=function(e){return new(e||t)},t.\u0275cmp=a.VBU({type:t,selectors:[["mwl-calendar-month-cell"]],hostAttrs:[1,"cal-cell","cal-day-cell"],hostVars:18,hostBindings:function(e,n){2&e&&a.AVh("cal-past",n.day.isPast)("cal-today",n.day.isToday)("cal-future",n.day.isFuture)("cal-weekend",n.day.isWeekend)("cal-in-month",n.day.inMonth)("cal-out-month",!n.day.inMonth)("cal-has-events",n.day.events.length>0)("cal-open",n.day===n.openDay)("cal-event-highlight",!!n.day.backgroundColor)},inputs:{day:"day",openDay:"openDay",locale:"locale",tooltipPlacement:"tooltipPlacement",tooltipAppendToBody:"tooltipAppendToBody",customTemplate:"customTemplate",tooltipTemplate:"tooltipTemplate",tooltipDelay:"tooltipDelay"},outputs:{highlightDay:"highlightDay",unhighlightDay:"unhighlightDay",eventClicked:"eventClicked"},decls:3,vars:15,consts:[["defaultTemplate",""],[3,"ngTemplateOutlet","ngTemplateOutletContext"],[1,"cal-cell-top"],["aria-hidden","true"],["class","cal-day-badge",4,"ngIf"],[1,"cal-day-number"],["class","cal-events",4,"ngIf"],[1,"cal-day-badge"],[1,"cal-events"],["class","cal-event","mwlDraggable","","dragActiveClass","cal-drag-active",3,"ngStyle","ngClass","mwlCalendarTooltip","tooltipPlacement","tooltipEvent","tooltipTemplate","tooltipAppendToBody","tooltipDelay","cal-draggable","dropData","dragAxis","validateDrag","touchStartLongPress","mouseenter","mouseleave","mwlClick",4,"ngFor","ngForOf","ngForTrackBy"],["mwlDraggable","","dragActiveClass","cal-drag-active",1,"cal-event",3,"ngStyle","ngClass","mwlCalendarTooltip","tooltipPlacement","tooltipEvent","tooltipTemplate","tooltipAppendToBody","tooltipDelay","dropData","dragAxis","validateDrag","touchStartLongPress","mouseenter","mouseleave","mwlClick"]],template:function(e,n){if(1&e&&(a.DNE(0,Fe,8,14,"ng-template",null,0,a.C5r),a.DNE(2,An,0,0,"ng-template",1)),2&e){const r=a.sdS(1);a.R7$(2),a.Y8G("ngTemplateOutlet",n.customTemplate||r)("ngTemplateOutletContext",a.zJS(2,Pn,[n.day,n.openDay,n.locale,n.tooltipPlacement,n.highlightDay,n.unhighlightDay,n.eventClicked,n.tooltipTemplate,n.tooltipAppendToBody,n.tooltipDelay,n.trackByEventId,n.validateDrag]))}},dependencies:[l.YU,l.Sq,l.bT,l.T3,l.B3,Mt,xi,dt,Mn,kn,Ne],encapsulation:2}),t})();const Pi=(0,ce.hZ)("collapse",[(0,ce.wk)("void",(0,ce.iF)({height:0,overflow:"hidden","padding-top":0,"padding-bottom":0})),(0,ce.wk)("*",(0,ce.iF)({height:"*",overflow:"hidden","padding-top":"*","padding-bottom":"*"})),(0,ce.kY)("* => void",(0,ce.i0)("150ms ease-out")),(0,ce.kY)("void => *",(0,ce.i0)("150ms ease-in"))]);let $i=(()=>{class t{constructor(){this.isOpen=!1,this.eventClicked=new a.bkB,this.trackByEventId=xn,this.validateDrag=Pt}}return t.\u0275fac=function(e){return new(e||t)},t.\u0275cmp=a.VBU({type:t,selectors:[["mwl-calendar-open-day-events"]],inputs:{locale:"locale",isOpen:"isOpen",events:"events",customTemplate:"customTemplate",eventTitleTemplate:"eventTitleTemplate",eventActionsTemplate:"eventActionsTemplate",date:"date"},outputs:{eventClicked:"eventClicked"},decls:3,vars:8,consts:[["defaultTemplate",""],[3,"ngTemplateOutlet","ngTemplateOutletContext"],["class","cal-open-day-events","role","application",4,"ngIf"],["role","application",1,"cal-open-day-events"],["tabindex","-1","role","alert"],["tabindex","0","role","landmark"],["mwlDraggable","","dragActiveClass","cal-drag-active",3,"ngClass","cal-draggable","dropData","dragAxis","validateDrag","touchStartLongPress",4,"ngFor","ngForOf","ngForTrackBy"],["mwlDraggable","","dragActiveClass","cal-drag-active",3,"ngClass","dropData","dragAxis","validateDrag","touchStartLongPress"],[1,"cal-event",3,"ngStyle"],["view","month","tabindex","0",3,"event","customTemplate","mwlClick","mwlKeydownEnter"],[3,"event","customTemplate"]],template:function(e,n){if(1&e&&(a.DNE(0,jn,1,1,"ng-template",null,0,a.C5r),a.DNE(2,Nn,0,0,"ng-template",1)),2&e){const r=a.sdS(1);a.R7$(2),a.Y8G("ngTemplateOutlet",n.customTemplate||r)("ngTemplateOutletContext",a.s1E(2,Un,n.events,n.eventClicked,n.isOpen,n.trackByEventId,n.validateDrag))}},dependencies:[l.YU,l.Sq,l.bT,l.T3,l.B3,Mt,ki,Si,dt,Bt,Ne],encapsulation:2,data:{animation:[Pi]}}),t})(),Li=(()=>{class t{constructor(){this.columnHeaderClicked=new a.bkB,this.trackByWeekDayHeaderDate=Hi}}return t.\u0275fac=function(e){return new(e||t)},t.\u0275cmp=a.VBU({type:t,selectors:[["mwl-calendar-month-view-header"]],inputs:{days:"days",locale:"locale",customTemplate:"customTemplate"},outputs:{columnHeaderClicked:"columnHeaderClicked"},decls:3,vars:6,consts:[["defaultTemplate",""],[3,"ngTemplateOutlet","ngTemplateOutletContext"],["role","row",1,"cal-cell-row","cal-header"],["class","cal-cell","tabindex","0","role","columnheader",3,"cal-past","cal-today","cal-future","cal-weekend","ngClass","click",4,"ngFor","ngForOf","ngForTrackBy"],["tabindex","0","role","columnheader",1,"cal-cell",3,"ngClass","click"]],template:function(e,n){if(1&e&&(a.DNE(0,Xn,2,2,"ng-template",null,0,a.C5r),a.DNE(2,Kn,0,0,"ng-template",1)),2&e){const r=a.sdS(1);a.R7$(2),a.Y8G("ngTemplateOutlet",n.customTemplate||r)("ngTemplateOutletContext",a.sMw(2,Zn,n.days,n.locale,n.trackByWeekDayHeaderDate))}},dependencies:[l.YU,l.Sq,l.T3,Mn],encapsulation:2}),t})(),Yi=(()=>{class t{constructor(e,n,r,o){this.cdr=e,this.utils=n,this.dateAdapter=o,this.events=[],this.excludeDays=[],this.activeDayIsOpen=!1,this.tooltipPlacement="auto",this.tooltipAppendToBody=!0,this.tooltipDelay=null,this.beforeViewRender=new a.bkB,this.dayClicked=new a.bkB,this.eventClicked=new a.bkB,this.columnHeaderClicked=new a.bkB,this.eventTimesChanged=new a.bkB,this.trackByRowOffset=(s,d)=>this.view.days.slice(d,this.view.totalDaysVisibleInWeek).map(m=>m.date.toISOString()).join("-"),this.trackByDate=(s,d)=>d.date.toISOString(),this.locale=r}ngOnInit(){this.refresh&&(this.refreshSubscription=this.refresh.subscribe(()=>{this.refreshAll(),this.cdr.markForCheck()}))}ngOnChanges(e){const n=e.viewDate||e.excludeDays||e.weekendDays,r=e.viewDate||e.events||e.excludeDays||e.weekendDays;n&&this.refreshHeader(),e.events&&(t=>{(function Jt(t,i){var e=!0;function n(r,o){i(r,o),e=!1}Array.isArray(t)?t.forEach(function(r){r.start?r.start instanceof Date||n(we.StartPropertyNotDate,r):n(we.StartPropertyMissing,r),r.end&&(r.end instanceof Date||n(we.EndPropertyNotDate,r),r.start>r.end&&n(we.EndsBeforeStart,r))}):i(we.NotArray,t)})(t,(...e)=>console.warn("angular-calendar",...e))})(this.events),r&&this.refreshBody(),(n||r)&&this.emitBeforeViewRender(),(e.activeDayIsOpen||e.viewDate||e.events||e.excludeDays||e.activeDay)&&this.checkActiveDayIsOpen()}ngOnDestroy(){this.refreshSubscription&&this.refreshSubscription.unsubscribe()}toggleDayHighlight(e,n){this.view.days.forEach(r=>{n&&r.events.indexOf(e)>-1?r.backgroundColor=e.color&&e.color.secondary||"#D1E8FF":delete r.backgroundColor})}eventDropped(e,n,r){if(e!==r){const o=this.dateAdapter.getYear(e.date),s=this.dateAdapter.getMonth(e.date),d=this.dateAdapter.getDate(e.date),m=this.dateAdapter.setDate(this.dateAdapter.setMonth(this.dateAdapter.setYear(n.start,o),s),d);let p;if(n.end){const v=this.dateAdapter.differenceInSeconds(m,n.start);p=this.dateAdapter.addSeconds(n.end,v)}this.eventTimesChanged.emit({event:n,newStart:m,newEnd:p,day:e,type:On.Drop})}}refreshHeader(){this.columnHeaders=this.utils.getWeekViewHeader({viewDate:this.viewDate,weekStartsOn:this.weekStartsOn,excluded:this.excludeDays,weekendDays:this.weekendDays})}refreshBody(){this.view=this.utils.getMonthView({events:this.events,viewDate:this.viewDate,weekStartsOn:this.weekStartsOn,excluded:this.excludeDays,weekendDays:this.weekendDays})}checkActiveDayIsOpen(){if(!0===this.activeDayIsOpen){const e=this.activeDay||this.viewDate;this.openDay=this.view.days.find(r=>this.dateAdapter.isSameDay(r.date,e));const n=this.view.days.indexOf(this.openDay);this.openRowIndex=Math.floor(n/this.view.totalDaysVisibleInWeek)*this.view.totalDaysVisibleInWeek}else this.openRowIndex=null,this.openDay=null}refreshAll(){this.refreshHeader(),this.refreshBody(),this.emitBeforeViewRender(),this.checkActiveDayIsOpen()}emitBeforeViewRender(){this.columnHeaders&&this.view&&this.beforeViewRender.emit({header:this.columnHeaders,body:this.view.days,period:this.view.period})}}return t.\u0275fac=function(e){return new(e||t)(a.rXU(a.gRc),a.rXU($t),a.rXU(a.xe9),a.rXU(ut))},t.\u0275cmp=a.VBU({type:t,selectors:[["mwl-calendar-month-view"]],inputs:{viewDate:"viewDate",events:"events",excludeDays:"excludeDays",activeDayIsOpen:"activeDayIsOpen",activeDay:"activeDay",refresh:"refresh",locale:"locale",tooltipPlacement:"tooltipPlacement",tooltipTemplate:"tooltipTemplate",tooltipAppendToBody:"tooltipAppendToBody",tooltipDelay:"tooltipDelay",weekStartsOn:"weekStartsOn",headerTemplate:"headerTemplate",cellTemplate:"cellTemplate",openDayEventsTemplate:"openDayEventsTemplate",eventTitleTemplate:"eventTitleTemplate",eventActionsTemplate:"eventActionsTemplate",weekendDays:"weekendDays"},outputs:{beforeViewRender:"beforeViewRender",dayClicked:"dayClicked",eventClicked:"eventClicked",columnHeaderClicked:"columnHeaderClicked",eventTimesChanged:"eventTimesChanged"},features:[a.OA$],decls:4,vars:5,consts:[["role","grid",1,"cal-month-view"],[3,"days","locale","customTemplate","columnHeaderClicked"],[1,"cal-days"],[4,"ngFor","ngForOf","ngForTrackBy"],["role","row",1,"cal-cell-row"],["role","gridcell","mwlDroppable","","dragOverClass","cal-drag-over",3,"ngClass","day","openDay","locale","tooltipPlacement","tooltipAppendToBody","tooltipTemplate","tooltipDelay","customTemplate","ngStyle","clickListenerDisabled","mwlClick","mwlKeydownEnter","highlightDay","unhighlightDay","drop","eventClicked",4,"ngFor","ngForOf","ngForTrackBy"],["mwlDroppable","","dragOverClass","cal-drag-over",3,"locale","isOpen","events","date","customTemplate","eventTitleTemplate","eventActionsTemplate","eventClicked","drop"],["role","gridcell","mwlDroppable","","dragOverClass","cal-drag-over",3,"ngClass","day","openDay","locale","tooltipPlacement","tooltipAppendToBody","tooltipTemplate","tooltipDelay","customTemplate","ngStyle","clickListenerDisabled","mwlClick","mwlKeydownEnter","highlightDay","unhighlightDay","drop","eventClicked"]],template:function(e,n){1&e&&(a.j41(0,"div",0)(1,"mwl-calendar-month-view-header",1),a.bIt("columnHeaderClicked",function(o){return n.columnHeaderClicked.emit(o)}),a.k0s(),a.j41(2,"div",2),a.DNE(3,Qn,5,13,"div",3),a.k0s()()),2&e&&(a.R7$(1),a.Y8G("days",n.columnHeaders)("locale",n.locale)("customTemplate",n.headerTemplate),a.R7$(2),a.Y8G("ngForOf",n.view.rowOffsets)("ngForTrackBy",n.trackByRowOffset))},dependencies:[l.YU,l.Sq,l.B3,dn,dt,Bt,Ai,$i,Li,l.P9,Ne],encapsulation:2}),t})(),Rn=(()=>{class t{}return t.\u0275fac=function(e){return new(e||t)},t.\u0275mod=a.$C({type:t}),t.\u0275inj=a.G2t({imports:[l.MD,Ve,Ue,Ve]}),t})(),Lt=(()=>{class t{}return t.\u0275fac=function(e){return new(e||t)},t.\u0275mod=a.$C({type:t}),t.\u0275inj=a.G2t({imports:[l.MD,Rt,Ve,Ue,Rt,Ve]}),t})(),zn=(()=>{class t{}return t.\u0275fac=function(e){return new(e||t)},t.\u0275mod=a.$C({type:t}),t.\u0275inj=a.G2t({imports:[l.MD,Ue,Lt]}),t})(),ji=(()=>{class t{static forRoot(e,n={}){return{ngModule:t,providers:[e,n.eventTitleFormatter||At,n.dateFormatter||mt,n.utils||$t,n.a11y||ct]}}}return t.\u0275fac=function(e){return new(e||t)},t.\u0275mod=a.$C({type:t}),t.\u0275inj=a.G2t({imports:[Ue,Rn,Lt,zn,Ue,Rn,Lt,zn]}),t})()}}]);