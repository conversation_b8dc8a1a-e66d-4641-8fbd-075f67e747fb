{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/OneDrive/Bureau/Project PI/devBridge/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { BehaviorSubject, throwError } from 'rxjs';\nimport { map, catchError } from 'rxjs/operators';\nimport { CallStatus } from '../models/message.model';\nimport { INITIATE_CALL_MUTATION, ACCEPT_CALL_MUTATION, REJECT_CALL_MUTATION, END_CALL_MUTATION, TOGGLE_CALL_MEDIA_MUTATION, INCOMING_CALL_SUBSCRIPTION, CALL_STATUS_CHANGED_SUBSCRIPTION, CALL_SIGNAL_SUBSCRIPTION } from '../graphql/message.graphql';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"apollo-angular\";\nimport * as i2 from \"./logger.service\";\nexport class CallService {\n  constructor(apollo, logger) {\n    this.apollo = apollo;\n    this.logger = logger;\n    // État des appels\n    this.activeCall = new BehaviorSubject(null);\n    this.incomingCall = new BehaviorSubject(null);\n    // Observables publics\n    this.activeCall$ = this.activeCall.asObservable();\n    this.incomingCall$ = this.incomingCall.asObservable();\n    // Propriétés pour la gestion des sons\n    this.sounds = {};\n    this.isPlaying = {};\n    // États simples pour les médias\n    this.isVideoEnabled = true;\n    this.isAudioEnabled = true;\n    // WebRTC\n    this.peerConnection = null;\n    this.localStream = null;\n    this.remoteStream = null;\n    this.localVideoElement = null;\n    this.remoteVideoElement = null;\n    this.currentCallId = null;\n    this.webrtcExchangeStarted = false;\n    this.initializeSounds();\n    this.initializeSubscriptions();\n    this.initializeWebRTC();\n  }\n  /**\n   * Initialise les sons\n   */\n  initializeSounds() {\n    this.createSyntheticSounds();\n  }\n  /**\n   * Crée des sons synthétiques\n   */\n  createSyntheticSounds() {\n    try {\n      const audioContext = new (window.AudioContext || window.webkitAudioContext)();\n      this.sounds['ringtone'] = this.createRingtoneSound(audioContext);\n      this.sounds['call-connected'] = this.createConnectedSound(audioContext);\n      this.sounds['call-end'] = this.createEndSound(audioContext);\n      this.sounds['notification'] = this.createNotificationSound(audioContext);\n    } catch (error) {\n      console.warn('Could not create synthetic sounds:', error);\n    }\n  }\n  /**\n   * Crée une sonnerie\n   */\n  createRingtoneSound(audioContext) {\n    const audio = new Audio();\n    audio.volume = 0.5;\n    let isPlaying = false;\n    let timeoutIds = [];\n    audio.playSynthetic = () => {\n      if (isPlaying) return Promise.resolve();\n      isPlaying = true;\n      const playMelody = () => {\n        if (!isPlaying) return;\n        const melody = [{\n          freq: 659.25,\n          duration: 0.125\n        }, {\n          freq: 587.33,\n          duration: 0.125\n        }, {\n          freq: 739.99,\n          duration: 0.25\n        }, {\n          freq: 783.99,\n          duration: 0.25\n        }];\n        let currentTime = audioContext.currentTime;\n        melody.forEach(note => {\n          if (!isPlaying) return;\n          const oscillator = audioContext.createOscillator();\n          const gainNode = audioContext.createGain();\n          oscillator.connect(gainNode);\n          gainNode.connect(audioContext.destination);\n          oscillator.frequency.value = note.freq;\n          oscillator.type = 'square';\n          gainNode.gain.setValueAtTime(0, currentTime);\n          gainNode.gain.linearRampToValueAtTime(0.3, currentTime + 0.02);\n          gainNode.gain.exponentialRampToValueAtTime(0.01, currentTime + note.duration);\n          oscillator.start(currentTime);\n          oscillator.stop(currentTime + note.duration);\n          currentTime += note.duration + 0.05;\n        });\n        const timeoutId = setTimeout(() => {\n          if (isPlaying) playMelody();\n        }, (currentTime - audioContext.currentTime + 0.8) * 1000);\n        timeoutIds.push(timeoutId);\n      };\n      playMelody();\n      return Promise.resolve();\n    };\n    audio.stopSynthetic = () => {\n      isPlaying = false;\n      timeoutIds.forEach(id => clearTimeout(id));\n      timeoutIds = [];\n    };\n    return audio;\n  }\n  /**\n   * Crée un son de connexion\n   */\n  createConnectedSound(audioContext) {\n    const audio = new Audio();\n    audio.volume = 0.5;\n    audio.playSynthetic = () => {\n      const melody = [{\n        freq: 523.25,\n        duration: 0.15\n      }, {\n        freq: 659.25,\n        duration: 0.15\n      }, {\n        freq: 783.99,\n        duration: 0.15\n      }, {\n        freq: 1046.5,\n        duration: 0.4\n      }];\n      let currentTime = audioContext.currentTime;\n      melody.forEach(note => {\n        const oscillator = audioContext.createOscillator();\n        const gainNode = audioContext.createGain();\n        oscillator.connect(gainNode);\n        gainNode.connect(audioContext.destination);\n        oscillator.frequency.value = note.freq;\n        oscillator.type = 'triangle';\n        gainNode.gain.setValueAtTime(0, currentTime);\n        gainNode.gain.linearRampToValueAtTime(0.25, currentTime + 0.02);\n        gainNode.gain.exponentialRampToValueAtTime(0.01, currentTime + note.duration);\n        oscillator.start(currentTime);\n        oscillator.stop(currentTime + note.duration);\n        currentTime += note.duration * 0.8;\n      });\n      return Promise.resolve();\n    };\n    return audio;\n  }\n  /**\n   * Crée un son de fin d'appel\n   */\n  createEndSound(audioContext) {\n    const audio = new Audio();\n    audio.volume = 0.4;\n    audio.playSynthetic = () => {\n      const melody = [{\n        freq: 783.99,\n        duration: 0.2\n      }, {\n        freq: 659.25,\n        duration: 0.2\n      }, {\n        freq: 523.25,\n        duration: 0.2\n      }, {\n        freq: 392.0,\n        duration: 0.4\n      }];\n      let currentTime = audioContext.currentTime;\n      melody.forEach(note => {\n        const oscillator = audioContext.createOscillator();\n        const gainNode = audioContext.createGain();\n        oscillator.connect(gainNode);\n        gainNode.connect(audioContext.destination);\n        oscillator.frequency.value = note.freq;\n        oscillator.type = 'sine';\n        gainNode.gain.setValueAtTime(0, currentTime);\n        gainNode.gain.linearRampToValueAtTime(0.2, currentTime + 0.05);\n        gainNode.gain.exponentialRampToValueAtTime(0.01, currentTime + note.duration);\n        oscillator.start(currentTime);\n        oscillator.stop(currentTime + note.duration);\n        currentTime += note.duration * 0.9;\n      });\n      return Promise.resolve();\n    };\n    return audio;\n  }\n  /**\n   * Crée un son de notification\n   */\n  createNotificationSound(audioContext) {\n    const audio = new Audio();\n    audio.volume = 0.6;\n    audio.playSynthetic = () => {\n      const notes = [{\n        freq: 523.25,\n        duration: 0.15,\n        delay: 0\n      }, {\n        freq: 783.99,\n        duration: 0.25,\n        delay: 0.2\n      }];\n      notes.forEach(note => {\n        setTimeout(() => {\n          const oscillator = audioContext.createOscillator();\n          const gainNode = audioContext.createGain();\n          oscillator.connect(gainNode);\n          gainNode.connect(audioContext.destination);\n          oscillator.frequency.value = note.freq;\n          oscillator.type = 'triangle';\n          const startTime = audioContext.currentTime;\n          gainNode.gain.setValueAtTime(0, startTime);\n          gainNode.gain.linearRampToValueAtTime(0.4, startTime + 0.02);\n          gainNode.gain.exponentialRampToValueAtTime(0.01, startTime + note.duration);\n          oscillator.start(startTime);\n          oscillator.stop(startTime + note.duration);\n        }, note.delay * 1000);\n      });\n      return Promise.resolve();\n    };\n    return audio;\n  }\n  /**\n   * Joue un son\n   */\n  play(soundName, loop = false) {\n    const sound = this.sounds[soundName];\n    if (!sound) return;\n    this.isPlaying[soundName] = true;\n    if (sound.playSynthetic) {\n      sound.playSynthetic();\n      if (loop) {\n        const interval = setInterval(() => {\n          if (this.isPlaying[soundName]) {\n            sound.playSynthetic();\n          } else {\n            clearInterval(interval);\n          }\n        }, 3000);\n      }\n    }\n  }\n  /**\n   * Arrête un son\n   */\n  stop(soundName) {\n    this.isPlaying[soundName] = false;\n    const sound = this.sounds[soundName];\n    if (sound && sound.stopSynthetic) {\n      sound.stopSynthetic();\n    }\n  }\n  /**\n   * Arrête tous les sons\n   */\n  stopAllSounds() {\n    Object.keys(this.sounds).forEach(name => {\n      this.stop(name);\n    });\n  }\n  /**\n   * Initialise les subscriptions\n   */\n  initializeSubscriptions() {\n    setTimeout(() => {\n      this.subscribeToIncomingCalls();\n      this.subscribeToCallStatusChanges();\n      this.subscribeToCallSignals();\n    }, 1000);\n  }\n  /**\n   * S'abonne aux appels entrants\n   */\n  subscribeToIncomingCalls() {\n    this.apollo.subscribe({\n      query: INCOMING_CALL_SUBSCRIPTION,\n      errorPolicy: 'all'\n    }).subscribe({\n      next: ({\n        data,\n        errors\n      }) => {\n        if (data?.incomingCall) {\n          this.handleIncomingCall(data.incomingCall);\n        }\n      },\n      error: error => {\n        console.error('Error in incoming call subscription:', error);\n        setTimeout(() => this.subscribeToIncomingCalls(), 5000);\n      }\n    });\n  }\n  /**\n   * S'abonne aux changements de statut d'appel\n   */\n  subscribeToCallStatusChanges() {\n    this.apollo.subscribe({\n      query: CALL_STATUS_CHANGED_SUBSCRIPTION,\n      errorPolicy: 'all'\n    }).subscribe({\n      next: ({\n        data,\n        errors\n      }) => {\n        if (data?.callStatusChanged) {\n          this.handleCallStatusChange(data.callStatusChanged);\n        }\n      },\n      error: error => {\n        console.error('Error in call status subscription:', error);\n        setTimeout(() => this.subscribeToCallStatusChanges(), 5000);\n      }\n    });\n  }\n  /**\n   * S'abonne aux signaux d'appel\n   */\n  subscribeToCallSignals() {\n    this.apollo.subscribe({\n      query: CALL_SIGNAL_SUBSCRIPTION,\n      errorPolicy: 'all'\n    }).subscribe({\n      next: ({\n        data,\n        errors\n      }) => {\n        if (data?.callSignal) {\n          this.handleCallSignal(data.callSignal);\n        }\n      },\n      error: error => {\n        console.error('Error in call signal subscription:', error);\n        setTimeout(() => this.subscribeToCallSignals(), 5000);\n      }\n    });\n  }\n  /**\n   * Gère un appel entrant\n   */\n  handleIncomingCall(call) {\n    this.incomingCall.next(call);\n    this.play('ringtone', true);\n  }\n  /**\n   * Gère les changements de statut d'appel\n   */\n  handleCallStatusChange(call) {\n    switch (call.status) {\n      case CallStatus.CONNECTED:\n        this.stop('ringtone');\n        this.play('call-connected');\n        this.activeCall.next(call);\n        this.incomingCall.next(null);\n        break;\n      case CallStatus.ENDED:\n        this.play('call-end');\n        this.activeCall.next(null);\n        this.incomingCall.next(null);\n        this.cleanupWebRTC();\n        break;\n      case CallStatus.REJECTED:\n        this.stop('ringtone');\n        this.play('call-end');\n        this.activeCall.next(null);\n        this.incomingCall.next(null);\n        break;\n    }\n  }\n  /**\n   * Gère les signaux d'appel\n   */\n  handleCallSignal(signal) {\n    if (!this.peerConnection) return;\n    switch (signal.type) {\n      case 'offer':\n        this.handleRemoteOffer(signal);\n        break;\n      case 'answer':\n        this.handleRemoteAnswer(signal);\n        break;\n      case 'ice-candidate':\n        this.handleRemoteICECandidate(signal);\n        break;\n    }\n  }\n  /**\n   * Initialise WebRTC\n   */\n  initializeWebRTC() {\n    const configuration = {\n      iceServers: [{\n        urls: 'stun:stun.l.google.com:19302'\n      }, {\n        urls: 'stun:stun1.l.google.com:19302'\n      }]\n    };\n    try {\n      this.peerConnection = new RTCPeerConnection(configuration);\n      this.setupPeerConnectionEvents();\n    } catch (error) {\n      console.error('Failed to initialize WebRTC:', error);\n    }\n  }\n  /**\n   * Configure les événements de la PeerConnection\n   */\n  setupPeerConnectionEvents() {\n    if (!this.peerConnection) return;\n    this.peerConnection.ontrack = event => {\n      this.remoteStream = event.streams[0];\n      this.attachRemoteStream();\n    };\n    this.peerConnection.onicecandidate = event => {\n      if (event.candidate) {\n        this.sendSignal('ice-candidate', JSON.stringify(event.candidate));\n      }\n    };\n  }\n  /**\n   * Attache le stream distant\n   */\n  attachRemoteStream() {\n    if (this.remoteStream && this.remoteVideoElement) {\n      this.remoteVideoElement.srcObject = this.remoteStream;\n      this.remoteVideoElement.muted = false;\n      this.remoteVideoElement.volume = 1;\n      this.remoteVideoElement.play();\n    }\n    // Attacher à tous les éléments vidéo disponibles\n    const videos = document.querySelectorAll('video');\n    for (let i = 1; i < videos.length; i++) {\n      if (this.remoteStream && !videos[i].srcObject) {\n        videos[i].srcObject = this.remoteStream;\n        videos[i].muted = false;\n        videos[i].volume = 1;\n        videos[i].play();\n        break;\n      }\n    }\n  }\n  /**\n   * Initie un appel\n   */\n  initiateCall(recipientId, callType, conversationId) {\n    // Générer un ID d'appel unique\n    const callId = 'call_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);\n    return this.apollo.mutate({\n      mutation: INITIATE_CALL_MUTATION,\n      variables: {\n        recipientId,\n        callType,\n        callId,\n        offer: '',\n        conversationId\n      }\n    }).pipe(map(result => {\n      console.log('🔍 [CallService] Mutation result:', result);\n      if (!result.data?.initiateCall) {\n        console.error('❌ [CallService] No call data in result:', result);\n        throw new Error('No call data received from server');\n      }\n      const call = result.data.initiateCall;\n      console.log('✅ [CallService] Call initiated successfully:', call);\n      this.activeCall.next(call);\n      this.currentCallId = call.id;\n      return call;\n    }), catchError(error => {\n      console.error('❌ [CallService] Error initiating call:', error);\n      return throwError(() => new Error(\"Erreur lors de l'initiation de l'appel: \" + error.message));\n    }));\n  }\n  /**\n   * Accepte un appel\n   */\n  acceptCall(incomingCall) {\n    return this.apollo.mutate({\n      mutation: ACCEPT_CALL_MUTATION,\n      variables: {\n        callId: incomingCall.id,\n        answer: null\n      }\n    }).pipe(map(result => {\n      if (!result.data?.acceptCall) {\n        throw new Error('No call data received');\n      }\n      const call = result.data.acceptCall;\n      this.stop('ringtone');\n      this.play('call-connected');\n      this.activeCall.next(call);\n      this.incomingCall.next(null);\n      return call;\n    }), catchError(error => {\n      console.error('Error accepting call:', error);\n      return throwError(() => new Error(\"Erreur lors de l'acceptation de l'appel\"));\n    }));\n  }\n  /**\n   * Rejette un appel\n   */\n  rejectCall(callId, reason) {\n    return this.apollo.mutate({\n      mutation: REJECT_CALL_MUTATION,\n      variables: {\n        callId,\n        reason: reason || 'User rejected'\n      }\n    }).pipe(map(result => {\n      this.stop('ringtone');\n      this.incomingCall.next(null);\n      this.activeCall.next(null);\n      return result.data.rejectCall;\n    }), catchError(error => {\n      console.error('Error rejecting call:', error);\n      return throwError(() => new Error(\"Erreur lors du rejet de l'appel\"));\n    }));\n  }\n  /**\n   * Termine un appel\n   */\n  endCall(callId) {\n    return this.apollo.mutate({\n      mutation: END_CALL_MUTATION,\n      variables: {\n        callId,\n        feedback: null\n      }\n    }).pipe(map(result => {\n      this.play('call-end');\n      this.activeCall.next(null);\n      this.incomingCall.next(null);\n      this.cleanupWebRTC();\n      return result.data.endCall;\n    }), catchError(error => {\n      console.error('Error ending call:', error);\n      return throwError(() => new Error(\"Erreur lors de la fin de l'appel\"));\n    }));\n  }\n  /**\n   * Configure les éléments vidéo\n   */\n  setVideoElements(localVideo, remoteVideo) {\n    this.localVideoElement = localVideo;\n    this.remoteVideoElement = remoteVideo;\n  }\n  /**\n   * Envoie un signal\n   */\n  sendSignal(type, data) {\n    // Simulation pour les tests\n    setTimeout(() => {\n      this.handleCallSignal({\n        callId: this.currentCallId,\n        senderId: 'other-user',\n        type,\n        data\n      });\n    }, 100);\n  }\n  /**\n   * Gère une offre distante\n   */\n  handleRemoteOffer(signal) {\n    return _asyncToGenerator(function* () {})();\n  } // Implémentation simplifiée\n  /**\n   * Gère une réponse distante\n   */\n  handleRemoteAnswer(signal) {\n    return _asyncToGenerator(function* () {})();\n  } // Implémentation simplifiée\n  /**\n   * Gère un candidat ICE distant\n   */\n  handleRemoteICECandidate(signal) {\n    return _asyncToGenerator(function* () {})();\n  } // Implémentation simplifiée\n  /**\n   * Nettoie les ressources WebRTC\n   */\n  cleanupWebRTC() {\n    if (this.localStream) {\n      this.localStream.getTracks().forEach(track => track.stop());\n      this.localStream = null;\n    }\n    if (this.peerConnection) {\n      this.peerConnection.close();\n      this.peerConnection = null;\n    }\n    this.remoteStream = null;\n  }\n  /**\n   * Active les sons après interaction utilisateur\n   */\n  enableSounds() {\n    console.log('Sounds enabled after user interaction');\n  }\n  /**\n   * Bascule l'audio\n   */\n  toggleAudio() {\n    this.isAudioEnabled = !this.isAudioEnabled;\n    return this.isAudioEnabled;\n  }\n  /**\n   * Bascule la vidéo\n   */\n  toggleVideo() {\n    this.isVideoEnabled = !this.isVideoEnabled;\n    return this.isVideoEnabled;\n  }\n  /**\n   * Bascule les médias\n   */\n  toggleMedia(callId, enableVideo, enableAudio) {\n    return this.apollo.mutate({\n      mutation: TOGGLE_CALL_MEDIA_MUTATION,\n      variables: {\n        callId,\n        video: enableVideo,\n        audio: enableAudio\n      }\n    }).pipe(map(result => {\n      return result.data.toggleCallMedia;\n    }), catchError(error => {\n      console.error('Error toggling media:', error);\n      return throwError(() => new Error('Erreur lors du changement des médias'));\n    }));\n  }\n  ngOnDestroy() {\n    this.stopAllSounds();\n    this.cleanupWebRTC();\n  }\n  static {\n    this.ɵfac = function CallService_Factory(t) {\n      return new (t || CallService)(i0.ɵɵinject(i1.Apollo), i0.ɵɵinject(i2.LoggerService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: CallService,\n      factory: CallService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["BehaviorSubject", "throwError", "map", "catchError", "CallStatus", "INITIATE_CALL_MUTATION", "ACCEPT_CALL_MUTATION", "REJECT_CALL_MUTATION", "END_CALL_MUTATION", "TOGGLE_CALL_MEDIA_MUTATION", "INCOMING_CALL_SUBSCRIPTION", "CALL_STATUS_CHANGED_SUBSCRIPTION", "CALL_SIGNAL_SUBSCRIPTION", "CallService", "constructor", "apollo", "logger", "activeCall", "incomingCall", "activeCall$", "asObservable", "incomingCall$", "sounds", "isPlaying", "isVideoEnabled", "isAudioEnabled", "peerConnection", "localStream", "remoteStream", "localVideoElement", "remoteVideoElement", "currentCallId", "webrtcExchangeStarted", "initializeSounds", "initializeSubscriptions", "initializeWebRTC", "createSyntheticSounds", "audioContext", "window", "AudioContext", "webkitAudioContext", "createRingtoneSound", "createConnectedSound", "createEndSound", "createNotificationSound", "error", "console", "warn", "audio", "Audio", "volume", "timeoutIds", "playSynthetic", "Promise", "resolve", "playMelody", "melody", "freq", "duration", "currentTime", "for<PERSON>ach", "note", "oscillator", "createOscillator", "gainNode", "createGain", "connect", "destination", "frequency", "value", "type", "gain", "setValueAtTime", "linearRampToValueAtTime", "exponentialRampToValueAtTime", "start", "stop", "timeoutId", "setTimeout", "push", "stopSynthetic", "id", "clearTimeout", "notes", "delay", "startTime", "play", "soundName", "loop", "sound", "interval", "setInterval", "clearInterval", "stopAllSounds", "Object", "keys", "name", "subscribeToIncomingCalls", "subscribeToCallStatusChanges", "subscribeToCallSignals", "subscribe", "query", "errorPolicy", "next", "data", "errors", "handleIncomingCall", "callStatusChanged", "handleCallStatusChange", "callSignal", "handleCallSignal", "call", "status", "CONNECTED", "ENDED", "cleanupWebRTC", "REJECTED", "signal", "handleRemoteOffer", "handleRemoteAnswer", "handleRemoteICECandidate", "configuration", "iceServers", "urls", "RTCPeerConnection", "setupPeerConnectionEvents", "ontrack", "event", "streams", "attachRemoteStream", "onicecandidate", "candidate", "sendSignal", "JSON", "stringify", "srcObject", "muted", "videos", "document", "querySelectorAll", "i", "length", "initiateCall", "recipientId", "callType", "conversationId", "callId", "Date", "now", "Math", "random", "toString", "substr", "mutate", "mutation", "variables", "offer", "pipe", "result", "log", "Error", "message", "acceptCall", "answer", "rejectCall", "reason", "endCall", "feedback", "setVideoElements", "localVideo", "remoteVideo", "senderId", "_asyncToGenerator", "getTracks", "track", "close", "enableSounds", "toggleAudio", "toggleVideo", "toggleMedia", "enableVideo", "enableAudio", "video", "toggleCallMedia", "ngOnDestroy", "i0", "ɵɵinject", "i1", "Apollo", "i2", "LoggerService", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\services\\call.service.ts"], "sourcesContent": ["import { Injectable, <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';\nimport { Apollo } from 'apollo-angular';\nimport { BehaviorSubject, Observable, throwError, from } from 'rxjs';\nimport { map, catchError, switchMap } from 'rxjs/operators';\nimport {\n  Call,\n  CallType,\n  CallStatus,\n  IncomingCall,\n  CallSuccess,\n} from '../models/message.model';\nimport {\n  INITIATE_CALL_MUTATION,\n  ACCEPT_CALL_MUTATION,\n  REJECT_CALL_MUTATION,\n  END_CALL_MUTATION,\n  TOGGLE_CALL_MEDIA_MUTATION,\n  INCOMING_CALL_SUBSCRIPTION,\n  CALL_STATUS_CHANGED_SUBSCRIPTION,\n  CALL_SIGNAL_SUBSCRIPTION,\n} from '../graphql/message.graphql';\nimport { LoggerService } from './logger.service';\n\n@Injectable({\n  providedIn: 'root',\n})\nexport class CallService implements OnDestroy {\n  // État des appels\n  private activeCall = new BehaviorSubject<Call | null>(null);\n  private incomingCall = new BehaviorSubject<IncomingCall | null>(null);\n\n  // Observables publics\n  public activeCall$ = this.activeCall.asObservable();\n  public incomingCall$ = this.incomingCall.asObservable();\n\n  // Propriétés pour la gestion des sons\n  private sounds: { [key: string]: HTMLAudioElement } = {};\n  private isPlaying: { [key: string]: boolean } = {};\n\n  // États simples pour les médias\n  private isVideoEnabled = true;\n  private isAudioEnabled = true;\n\n  // WebRTC\n  private peerConnection: RTCPeerConnection | null = null;\n  private localStream: MediaStream | null = null;\n  private remoteStream: MediaStream | null = null;\n  private localVideoElement: HTMLVideoElement | null = null;\n  private remoteVideoElement: HTMLVideoElement | null = null;\n  private currentCallId: string | null = null;\n  private webrtcExchangeStarted: boolean = false;\n\n  constructor(private apollo: Apollo, private logger: LoggerService) {\n    this.initializeSounds();\n    this.initializeSubscriptions();\n    this.initializeWebRTC();\n  }\n\n  /**\n   * Initialise les sons\n   */\n  private initializeSounds(): void {\n    this.createSyntheticSounds();\n  }\n\n  /**\n   * Crée des sons synthétiques\n   */\n  private createSyntheticSounds(): void {\n    try {\n      const audioContext = new (window.AudioContext ||\n        (window as any).webkitAudioContext)();\n      this.sounds['ringtone'] = this.createRingtoneSound(audioContext);\n      this.sounds['call-connected'] = this.createConnectedSound(audioContext);\n      this.sounds['call-end'] = this.createEndSound(audioContext);\n      this.sounds['notification'] = this.createNotificationSound(audioContext);\n    } catch (error) {\n      console.warn('Could not create synthetic sounds:', error);\n    }\n  }\n\n  /**\n   * Crée une sonnerie\n   */\n  private createRingtoneSound(audioContext: AudioContext): HTMLAudioElement {\n    const audio = new Audio();\n    audio.volume = 0.5;\n    let isPlaying = false;\n    let timeoutIds: any[] = [];\n\n    (audio as any).playSynthetic = () => {\n      if (isPlaying) return Promise.resolve();\n      isPlaying = true;\n\n      const playMelody = () => {\n        if (!isPlaying) return;\n        const melody = [\n          { freq: 659.25, duration: 0.125 },\n          { freq: 587.33, duration: 0.125 },\n          { freq: 739.99, duration: 0.25 },\n          { freq: 783.99, duration: 0.25 },\n        ];\n\n        let currentTime = audioContext.currentTime;\n        melody.forEach((note) => {\n          if (!isPlaying) return;\n          const oscillator = audioContext.createOscillator();\n          const gainNode = audioContext.createGain();\n          oscillator.connect(gainNode);\n          gainNode.connect(audioContext.destination);\n          oscillator.frequency.value = note.freq;\n          oscillator.type = 'square';\n          gainNode.gain.setValueAtTime(0, currentTime);\n          gainNode.gain.linearRampToValueAtTime(0.3, currentTime + 0.02);\n          gainNode.gain.exponentialRampToValueAtTime(\n            0.01,\n            currentTime + note.duration\n          );\n          oscillator.start(currentTime);\n          oscillator.stop(currentTime + note.duration);\n          currentTime += note.duration + 0.05;\n        });\n\n        const timeoutId = setTimeout(() => {\n          if (isPlaying) playMelody();\n        }, (currentTime - audioContext.currentTime + 0.8) * 1000);\n        timeoutIds.push(timeoutId);\n      };\n\n      playMelody();\n      return Promise.resolve();\n    };\n\n    (audio as any).stopSynthetic = () => {\n      isPlaying = false;\n      timeoutIds.forEach((id) => clearTimeout(id));\n      timeoutIds = [];\n    };\n\n    return audio;\n  }\n\n  /**\n   * Crée un son de connexion\n   */\n  private createConnectedSound(audioContext: AudioContext): HTMLAudioElement {\n    const audio = new Audio();\n    audio.volume = 0.5;\n\n    (audio as any).playSynthetic = () => {\n      const melody = [\n        { freq: 523.25, duration: 0.15 },\n        { freq: 659.25, duration: 0.15 },\n        { freq: 783.99, duration: 0.15 },\n        { freq: 1046.5, duration: 0.4 },\n      ];\n\n      let currentTime = audioContext.currentTime;\n      melody.forEach((note) => {\n        const oscillator = audioContext.createOscillator();\n        const gainNode = audioContext.createGain();\n        oscillator.connect(gainNode);\n        gainNode.connect(audioContext.destination);\n        oscillator.frequency.value = note.freq;\n        oscillator.type = 'triangle';\n        gainNode.gain.setValueAtTime(0, currentTime);\n        gainNode.gain.linearRampToValueAtTime(0.25, currentTime + 0.02);\n        gainNode.gain.exponentialRampToValueAtTime(\n          0.01,\n          currentTime + note.duration\n        );\n        oscillator.start(currentTime);\n        oscillator.stop(currentTime + note.duration);\n        currentTime += note.duration * 0.8;\n      });\n\n      return Promise.resolve();\n    };\n\n    return audio;\n  }\n\n  /**\n   * Crée un son de fin d'appel\n   */\n  private createEndSound(audioContext: AudioContext): HTMLAudioElement {\n    const audio = new Audio();\n    audio.volume = 0.4;\n\n    (audio as any).playSynthetic = () => {\n      const melody = [\n        { freq: 783.99, duration: 0.2 },\n        { freq: 659.25, duration: 0.2 },\n        { freq: 523.25, duration: 0.2 },\n        { freq: 392.0, duration: 0.4 },\n      ];\n\n      let currentTime = audioContext.currentTime;\n      melody.forEach((note) => {\n        const oscillator = audioContext.createOscillator();\n        const gainNode = audioContext.createGain();\n        oscillator.connect(gainNode);\n        gainNode.connect(audioContext.destination);\n        oscillator.frequency.value = note.freq;\n        oscillator.type = 'sine';\n        gainNode.gain.setValueAtTime(0, currentTime);\n        gainNode.gain.linearRampToValueAtTime(0.2, currentTime + 0.05);\n        gainNode.gain.exponentialRampToValueAtTime(\n          0.01,\n          currentTime + note.duration\n        );\n        oscillator.start(currentTime);\n        oscillator.stop(currentTime + note.duration);\n        currentTime += note.duration * 0.9;\n      });\n\n      return Promise.resolve();\n    };\n\n    return audio;\n  }\n\n  /**\n   * Crée un son de notification\n   */\n  private createNotificationSound(\n    audioContext: AudioContext\n  ): HTMLAudioElement {\n    const audio = new Audio();\n    audio.volume = 0.6;\n\n    (audio as any).playSynthetic = () => {\n      const notes = [\n        { freq: 523.25, duration: 0.15, delay: 0 },\n        { freq: 783.99, duration: 0.25, delay: 0.2 },\n      ];\n\n      notes.forEach((note) => {\n        setTimeout(() => {\n          const oscillator = audioContext.createOscillator();\n          const gainNode = audioContext.createGain();\n          oscillator.connect(gainNode);\n          gainNode.connect(audioContext.destination);\n          oscillator.frequency.value = note.freq;\n          oscillator.type = 'triangle';\n          const startTime = audioContext.currentTime;\n          gainNode.gain.setValueAtTime(0, startTime);\n          gainNode.gain.linearRampToValueAtTime(0.4, startTime + 0.02);\n          gainNode.gain.exponentialRampToValueAtTime(\n            0.01,\n            startTime + note.duration\n          );\n          oscillator.start(startTime);\n          oscillator.stop(startTime + note.duration);\n        }, note.delay * 1000);\n      });\n\n      return Promise.resolve();\n    };\n\n    return audio;\n  }\n\n  /**\n   * Joue un son\n   */\n  private play(soundName: string, loop: boolean = false): void {\n    const sound = this.sounds[soundName];\n    if (!sound) return;\n\n    this.isPlaying[soundName] = true;\n\n    if ((sound as any).playSynthetic) {\n      (sound as any).playSynthetic();\n      if (loop) {\n        const interval = setInterval(() => {\n          if (this.isPlaying[soundName]) {\n            (sound as any).playSynthetic();\n          } else {\n            clearInterval(interval);\n          }\n        }, 3000);\n      }\n    }\n  }\n\n  /**\n   * Arrête un son\n   */\n  private stop(soundName: string): void {\n    this.isPlaying[soundName] = false;\n    const sound = this.sounds[soundName];\n    if (sound && (sound as any).stopSynthetic) {\n      (sound as any).stopSynthetic();\n    }\n  }\n\n  /**\n   * Arrête tous les sons\n   */\n  private stopAllSounds(): void {\n    Object.keys(this.sounds).forEach((name) => {\n      this.stop(name);\n    });\n  }\n\n  /**\n   * Initialise les subscriptions\n   */\n  private initializeSubscriptions(): void {\n    setTimeout(() => {\n      this.subscribeToIncomingCalls();\n      this.subscribeToCallStatusChanges();\n      this.subscribeToCallSignals();\n    }, 1000);\n  }\n\n  /**\n   * S'abonne aux appels entrants\n   */\n  private subscribeToIncomingCalls(): void {\n    this.apollo\n      .subscribe<{ incomingCall: IncomingCall }>({\n        query: INCOMING_CALL_SUBSCRIPTION,\n        errorPolicy: 'all',\n      })\n      .subscribe({\n        next: ({ data, errors }) => {\n          if (data?.incomingCall) {\n            this.handleIncomingCall(data.incomingCall);\n          }\n        },\n        error: (error) => {\n          console.error('Error in incoming call subscription:', error);\n          setTimeout(() => this.subscribeToIncomingCalls(), 5000);\n        },\n      });\n  }\n\n  /**\n   * S'abonne aux changements de statut d'appel\n   */\n  private subscribeToCallStatusChanges(): void {\n    this.apollo\n      .subscribe<{ callStatusChanged: Call }>({\n        query: CALL_STATUS_CHANGED_SUBSCRIPTION,\n        errorPolicy: 'all',\n      })\n      .subscribe({\n        next: ({ data, errors }) => {\n          if (data?.callStatusChanged) {\n            this.handleCallStatusChange(data.callStatusChanged);\n          }\n        },\n        error: (error) => {\n          console.error('Error in call status subscription:', error);\n          setTimeout(() => this.subscribeToCallStatusChanges(), 5000);\n        },\n      });\n  }\n\n  /**\n   * S'abonne aux signaux d'appel\n   */\n  private subscribeToCallSignals(): void {\n    this.apollo\n      .subscribe<{ callSignal: any }>({\n        query: CALL_SIGNAL_SUBSCRIPTION,\n        errorPolicy: 'all',\n      })\n      .subscribe({\n        next: ({ data, errors }) => {\n          if (data?.callSignal) {\n            this.handleCallSignal(data.callSignal);\n          }\n        },\n        error: (error) => {\n          console.error('Error in call signal subscription:', error);\n          setTimeout(() => this.subscribeToCallSignals(), 5000);\n        },\n      });\n  }\n\n  /**\n   * Gère un appel entrant\n   */\n  private handleIncomingCall(call: IncomingCall): void {\n    this.incomingCall.next(call);\n    this.play('ringtone', true);\n  }\n\n  /**\n   * Gère les changements de statut d'appel\n   */\n  private handleCallStatusChange(call: Call): void {\n    switch (call.status) {\n      case CallStatus.CONNECTED:\n        this.stop('ringtone');\n        this.play('call-connected');\n        this.activeCall.next(call);\n        this.incomingCall.next(null);\n        break;\n      case CallStatus.ENDED:\n        this.play('call-end');\n        this.activeCall.next(null);\n        this.incomingCall.next(null);\n        this.cleanupWebRTC();\n        break;\n      case CallStatus.REJECTED:\n        this.stop('ringtone');\n        this.play('call-end');\n        this.activeCall.next(null);\n        this.incomingCall.next(null);\n        break;\n    }\n  }\n\n  /**\n   * Gère les signaux d'appel\n   */\n  private handleCallSignal(signal: any): void {\n    if (!this.peerConnection) return;\n\n    switch (signal.type) {\n      case 'offer':\n        this.handleRemoteOffer(signal);\n        break;\n      case 'answer':\n        this.handleRemoteAnswer(signal);\n        break;\n      case 'ice-candidate':\n        this.handleRemoteICECandidate(signal);\n        break;\n    }\n  }\n\n  /**\n   * Initialise WebRTC\n   */\n  private initializeWebRTC(): void {\n    const configuration: RTCConfiguration = {\n      iceServers: [\n        { urls: 'stun:stun.l.google.com:19302' },\n        { urls: 'stun:stun1.l.google.com:19302' },\n      ],\n    };\n\n    try {\n      this.peerConnection = new RTCPeerConnection(configuration);\n      this.setupPeerConnectionEvents();\n    } catch (error) {\n      console.error('Failed to initialize WebRTC:', error);\n    }\n  }\n\n  /**\n   * Configure les événements de la PeerConnection\n   */\n  private setupPeerConnectionEvents(): void {\n    if (!this.peerConnection) return;\n\n    this.peerConnection.ontrack = (event) => {\n      this.remoteStream = event.streams[0];\n      this.attachRemoteStream();\n    };\n\n    this.peerConnection.onicecandidate = (event) => {\n      if (event.candidate) {\n        this.sendSignal('ice-candidate', JSON.stringify(event.candidate));\n      }\n    };\n  }\n\n  /**\n   * Attache le stream distant\n   */\n  private attachRemoteStream(): void {\n    if (this.remoteStream && this.remoteVideoElement) {\n      this.remoteVideoElement.srcObject = this.remoteStream;\n      this.remoteVideoElement.muted = false;\n      this.remoteVideoElement.volume = 1;\n      this.remoteVideoElement.play();\n    }\n\n    // Attacher à tous les éléments vidéo disponibles\n    const videos = document.querySelectorAll('video');\n    for (let i = 1; i < videos.length; i++) {\n      if (this.remoteStream && !videos[i].srcObject) {\n        videos[i].srcObject = this.remoteStream;\n        videos[i].muted = false;\n        videos[i].volume = 1;\n        videos[i].play();\n        break;\n      }\n    }\n  }\n\n  /**\n   * Initie un appel\n   */\n  initiateCall(\n    recipientId: string,\n    callType: CallType,\n    conversationId?: string\n  ): Observable<Call> {\n    // Générer un ID d'appel unique\n    const callId =\n      'call_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);\n\n    return this.apollo\n      .mutate<{ initiateCall: Call }>({\n        mutation: INITIATE_CALL_MUTATION,\n        variables: {\n          recipientId,\n          callType,\n          callId,\n          offer: '', // Offre vide pour l'instant\n          conversationId,\n        },\n      })\n      .pipe(\n        map((result) => {\n          console.log('🔍 [CallService] Mutation result:', result);\n\n          if (!result.data?.initiateCall) {\n            console.error('❌ [CallService] No call data in result:', result);\n            throw new Error('No call data received from server');\n          }\n\n          const call = result.data.initiateCall;\n          console.log('✅ [CallService] Call initiated successfully:', call);\n\n          this.activeCall.next(call);\n          this.currentCallId = call.id;\n\n          return call;\n        }),\n        catchError((error) => {\n          console.error('❌ [CallService] Error initiating call:', error);\n          return throwError(\n            () =>\n              new Error(\n                \"Erreur lors de l'initiation de l'appel: \" + error.message\n              )\n          );\n        })\n      );\n  }\n\n  /**\n   * Accepte un appel\n   */\n  acceptCall(incomingCall: IncomingCall): Observable<Call> {\n    return this.apollo\n      .mutate<{ acceptCall: Call }>({\n        mutation: ACCEPT_CALL_MUTATION,\n        variables: { callId: incomingCall.id, answer: null },\n      })\n      .pipe(\n        map((result) => {\n          if (!result.data?.acceptCall) {\n            throw new Error('No call data received');\n          }\n          const call = result.data.acceptCall;\n          this.stop('ringtone');\n          this.play('call-connected');\n          this.activeCall.next(call);\n          this.incomingCall.next(null);\n          return call;\n        }),\n        catchError((error) => {\n          console.error('Error accepting call:', error);\n          return throwError(\n            () => new Error(\"Erreur lors de l'acceptation de l'appel\")\n          );\n        })\n      );\n  }\n\n  /**\n   * Rejette un appel\n   */\n  rejectCall(callId: string, reason?: string): Observable<CallSuccess> {\n    return this.apollo\n      .mutate<{ rejectCall: CallSuccess }>({\n        mutation: REJECT_CALL_MUTATION,\n        variables: { callId, reason: reason || 'User rejected' },\n      })\n      .pipe(\n        map((result) => {\n          this.stop('ringtone');\n          this.incomingCall.next(null);\n          this.activeCall.next(null);\n          return result.data!.rejectCall;\n        }),\n        catchError((error) => {\n          console.error('Error rejecting call:', error);\n          return throwError(() => new Error(\"Erreur lors du rejet de l'appel\"));\n        })\n      );\n  }\n\n  /**\n   * Termine un appel\n   */\n  endCall(callId: string): Observable<CallSuccess> {\n    return this.apollo\n      .mutate<{ endCall: CallSuccess }>({\n        mutation: END_CALL_MUTATION,\n        variables: { callId, feedback: null },\n      })\n      .pipe(\n        map((result) => {\n          this.play('call-end');\n          this.activeCall.next(null);\n          this.incomingCall.next(null);\n          this.cleanupWebRTC();\n          return result.data!.endCall;\n        }),\n        catchError((error) => {\n          console.error('Error ending call:', error);\n          return throwError(\n            () => new Error(\"Erreur lors de la fin de l'appel\")\n          );\n        })\n      );\n  }\n\n  /**\n   * Configure les éléments vidéo\n   */\n  setVideoElements(\n    localVideo: HTMLVideoElement,\n    remoteVideo: HTMLVideoElement\n  ): void {\n    this.localVideoElement = localVideo;\n    this.remoteVideoElement = remoteVideo;\n  }\n\n  /**\n   * Envoie un signal\n   */\n  private sendSignal(type: string, data: string): void {\n    // Simulation pour les tests\n    setTimeout(() => {\n      this.handleCallSignal({\n        callId: this.currentCallId,\n        senderId: 'other-user',\n        type,\n        data,\n      });\n    }, 100);\n  }\n\n  /**\n   * Gère une offre distante\n   */\n  private async handleRemoteOffer(signal: any): Promise<void> {\n    // Implémentation simplifiée\n  }\n\n  /**\n   * Gère une réponse distante\n   */\n  private async handleRemoteAnswer(signal: any): Promise<void> {\n    // Implémentation simplifiée\n  }\n\n  /**\n   * Gère un candidat ICE distant\n   */\n  private async handleRemoteICECandidate(signal: any): Promise<void> {\n    // Implémentation simplifiée\n  }\n\n  /**\n   * Nettoie les ressources WebRTC\n   */\n  private cleanupWebRTC(): void {\n    if (this.localStream) {\n      this.localStream.getTracks().forEach((track) => track.stop());\n      this.localStream = null;\n    }\n\n    if (this.peerConnection) {\n      this.peerConnection.close();\n      this.peerConnection = null;\n    }\n\n    this.remoteStream = null;\n  }\n\n  /**\n   * Active les sons après interaction utilisateur\n   */\n  enableSounds(): void {\n    console.log('Sounds enabled after user interaction');\n  }\n\n  /**\n   * Bascule l'audio\n   */\n  toggleAudio(): boolean {\n    this.isAudioEnabled = !this.isAudioEnabled;\n    return this.isAudioEnabled;\n  }\n\n  /**\n   * Bascule la vidéo\n   */\n  toggleVideo(): boolean {\n    this.isVideoEnabled = !this.isVideoEnabled;\n    return this.isVideoEnabled;\n  }\n\n  /**\n   * Bascule les médias\n   */\n  toggleMedia(\n    callId: string,\n    enableVideo?: boolean,\n    enableAudio?: boolean\n  ): Observable<CallSuccess> {\n    return this.apollo\n      .mutate<{ toggleCallMedia: CallSuccess }>({\n        mutation: TOGGLE_CALL_MEDIA_MUTATION,\n        variables: { callId, video: enableVideo, audio: enableAudio },\n      })\n      .pipe(\n        map((result) => {\n          return result.data!.toggleCallMedia;\n        }),\n        catchError((error) => {\n          console.error('Error toggling media:', error);\n          return throwError(\n            () => new Error('Erreur lors du changement des médias')\n          );\n        })\n      );\n  }\n\n  ngOnDestroy(): void {\n    this.stopAllSounds();\n    this.cleanupWebRTC();\n  }\n}\n"], "mappings": ";AAEA,SAASA,eAAe,EAAcC,UAAU,QAAc,MAAM;AACpE,SAASC,GAAG,EAAEC,UAAU,QAAmB,gBAAgB;AAC3D,SAGEC,UAAU,QAGL,yBAAyB;AAChC,SACEC,sBAAsB,EACtBC,oBAAoB,EACpBC,oBAAoB,EACpBC,iBAAiB,EACjBC,0BAA0B,EAC1BC,0BAA0B,EAC1BC,gCAAgC,EAChCC,wBAAwB,QACnB,4BAA4B;;;;AAMnC,OAAM,MAAOC,WAAW;EA0BtBC,YAAoBC,MAAc,EAAUC,MAAqB;IAA7C,KAAAD,MAAM,GAANA,MAAM;IAAkB,KAAAC,MAAM,GAANA,MAAM;IAzBlD;IACQ,KAAAC,UAAU,GAAG,IAAIjB,eAAe,CAAc,IAAI,CAAC;IACnD,KAAAkB,YAAY,GAAG,IAAIlB,eAAe,CAAsB,IAAI,CAAC;IAErE;IACO,KAAAmB,WAAW,GAAG,IAAI,CAACF,UAAU,CAACG,YAAY,EAAE;IAC5C,KAAAC,aAAa,GAAG,IAAI,CAACH,YAAY,CAACE,YAAY,EAAE;IAEvD;IACQ,KAAAE,MAAM,GAAwC,EAAE;IAChD,KAAAC,SAAS,GAA+B,EAAE;IAElD;IACQ,KAAAC,cAAc,GAAG,IAAI;IACrB,KAAAC,cAAc,GAAG,IAAI;IAE7B;IACQ,KAAAC,cAAc,GAA6B,IAAI;IAC/C,KAAAC,WAAW,GAAuB,IAAI;IACtC,KAAAC,YAAY,GAAuB,IAAI;IACvC,KAAAC,iBAAiB,GAA4B,IAAI;IACjD,KAAAC,kBAAkB,GAA4B,IAAI;IAClD,KAAAC,aAAa,GAAkB,IAAI;IACnC,KAAAC,qBAAqB,GAAY,KAAK;IAG5C,IAAI,CAACC,gBAAgB,EAAE;IACvB,IAAI,CAACC,uBAAuB,EAAE;IAC9B,IAAI,CAACC,gBAAgB,EAAE;EACzB;EAEA;;;EAGQF,gBAAgBA,CAAA;IACtB,IAAI,CAACG,qBAAqB,EAAE;EAC9B;EAEA;;;EAGQA,qBAAqBA,CAAA;IAC3B,IAAI;MACF,MAAMC,YAAY,GAAG,KAAKC,MAAM,CAACC,YAAY,IAC1CD,MAAc,CAACE,kBAAkB,EAAC,CAAE;MACvC,IAAI,CAAClB,MAAM,CAAC,UAAU,CAAC,GAAG,IAAI,CAACmB,mBAAmB,CAACJ,YAAY,CAAC;MAChE,IAAI,CAACf,MAAM,CAAC,gBAAgB,CAAC,GAAG,IAAI,CAACoB,oBAAoB,CAACL,YAAY,CAAC;MACvE,IAAI,CAACf,MAAM,CAAC,UAAU,CAAC,GAAG,IAAI,CAACqB,cAAc,CAACN,YAAY,CAAC;MAC3D,IAAI,CAACf,MAAM,CAAC,cAAc,CAAC,GAAG,IAAI,CAACsB,uBAAuB,CAACP,YAAY,CAAC;KACzE,CAAC,OAAOQ,KAAK,EAAE;MACdC,OAAO,CAACC,IAAI,CAAC,oCAAoC,EAAEF,KAAK,CAAC;;EAE7D;EAEA;;;EAGQJ,mBAAmBA,CAACJ,YAA0B;IACpD,MAAMW,KAAK,GAAG,IAAIC,KAAK,EAAE;IACzBD,KAAK,CAACE,MAAM,GAAG,GAAG;IAClB,IAAI3B,SAAS,GAAG,KAAK;IACrB,IAAI4B,UAAU,GAAU,EAAE;IAEzBH,KAAa,CAACI,aAAa,GAAG,MAAK;MAClC,IAAI7B,SAAS,EAAE,OAAO8B,OAAO,CAACC,OAAO,EAAE;MACvC/B,SAAS,GAAG,IAAI;MAEhB,MAAMgC,UAAU,GAAGA,CAAA,KAAK;QACtB,IAAI,CAAChC,SAAS,EAAE;QAChB,MAAMiC,MAAM,GAAG,CACb;UAAEC,IAAI,EAAE,MAAM;UAAEC,QAAQ,EAAE;QAAK,CAAE,EACjC;UAAED,IAAI,EAAE,MAAM;UAAEC,QAAQ,EAAE;QAAK,CAAE,EACjC;UAAED,IAAI,EAAE,MAAM;UAAEC,QAAQ,EAAE;QAAI,CAAE,EAChC;UAAED,IAAI,EAAE,MAAM;UAAEC,QAAQ,EAAE;QAAI,CAAE,CACjC;QAED,IAAIC,WAAW,GAAGtB,YAAY,CAACsB,WAAW;QAC1CH,MAAM,CAACI,OAAO,CAAEC,IAAI,IAAI;UACtB,IAAI,CAACtC,SAAS,EAAE;UAChB,MAAMuC,UAAU,GAAGzB,YAAY,CAAC0B,gBAAgB,EAAE;UAClD,MAAMC,QAAQ,GAAG3B,YAAY,CAAC4B,UAAU,EAAE;UAC1CH,UAAU,CAACI,OAAO,CAACF,QAAQ,CAAC;UAC5BA,QAAQ,CAACE,OAAO,CAAC7B,YAAY,CAAC8B,WAAW,CAAC;UAC1CL,UAAU,CAACM,SAAS,CAACC,KAAK,GAAGR,IAAI,CAACJ,IAAI;UACtCK,UAAU,CAACQ,IAAI,GAAG,QAAQ;UAC1BN,QAAQ,CAACO,IAAI,CAACC,cAAc,CAAC,CAAC,EAAEb,WAAW,CAAC;UAC5CK,QAAQ,CAACO,IAAI,CAACE,uBAAuB,CAAC,GAAG,EAAEd,WAAW,GAAG,IAAI,CAAC;UAC9DK,QAAQ,CAACO,IAAI,CAACG,4BAA4B,CACxC,IAAI,EACJf,WAAW,GAAGE,IAAI,CAACH,QAAQ,CAC5B;UACDI,UAAU,CAACa,KAAK,CAAChB,WAAW,CAAC;UAC7BG,UAAU,CAACc,IAAI,CAACjB,WAAW,GAAGE,IAAI,CAACH,QAAQ,CAAC;UAC5CC,WAAW,IAAIE,IAAI,CAACH,QAAQ,GAAG,IAAI;QACrC,CAAC,CAAC;QAEF,MAAMmB,SAAS,GAAGC,UAAU,CAAC,MAAK;UAChC,IAAIvD,SAAS,EAAEgC,UAAU,EAAE;QAC7B,CAAC,EAAE,CAACI,WAAW,GAAGtB,YAAY,CAACsB,WAAW,GAAG,GAAG,IAAI,IAAI,CAAC;QACzDR,UAAU,CAAC4B,IAAI,CAACF,SAAS,CAAC;MAC5B,CAAC;MAEDtB,UAAU,EAAE;MACZ,OAAOF,OAAO,CAACC,OAAO,EAAE;IAC1B,CAAC;IAEAN,KAAa,CAACgC,aAAa,GAAG,MAAK;MAClCzD,SAAS,GAAG,KAAK;MACjB4B,UAAU,CAACS,OAAO,CAAEqB,EAAE,IAAKC,YAAY,CAACD,EAAE,CAAC,CAAC;MAC5C9B,UAAU,GAAG,EAAE;IACjB,CAAC;IAED,OAAOH,KAAK;EACd;EAEA;;;EAGQN,oBAAoBA,CAACL,YAA0B;IACrD,MAAMW,KAAK,GAAG,IAAIC,KAAK,EAAE;IACzBD,KAAK,CAACE,MAAM,GAAG,GAAG;IAEjBF,KAAa,CAACI,aAAa,GAAG,MAAK;MAClC,MAAMI,MAAM,GAAG,CACb;QAAEC,IAAI,EAAE,MAAM;QAAEC,QAAQ,EAAE;MAAI,CAAE,EAChC;QAAED,IAAI,EAAE,MAAM;QAAEC,QAAQ,EAAE;MAAI,CAAE,EAChC;QAAED,IAAI,EAAE,MAAM;QAAEC,QAAQ,EAAE;MAAI,CAAE,EAChC;QAAED,IAAI,EAAE,MAAM;QAAEC,QAAQ,EAAE;MAAG,CAAE,CAChC;MAED,IAAIC,WAAW,GAAGtB,YAAY,CAACsB,WAAW;MAC1CH,MAAM,CAACI,OAAO,CAAEC,IAAI,IAAI;QACtB,MAAMC,UAAU,GAAGzB,YAAY,CAAC0B,gBAAgB,EAAE;QAClD,MAAMC,QAAQ,GAAG3B,YAAY,CAAC4B,UAAU,EAAE;QAC1CH,UAAU,CAACI,OAAO,CAACF,QAAQ,CAAC;QAC5BA,QAAQ,CAACE,OAAO,CAAC7B,YAAY,CAAC8B,WAAW,CAAC;QAC1CL,UAAU,CAACM,SAAS,CAACC,KAAK,GAAGR,IAAI,CAACJ,IAAI;QACtCK,UAAU,CAACQ,IAAI,GAAG,UAAU;QAC5BN,QAAQ,CAACO,IAAI,CAACC,cAAc,CAAC,CAAC,EAAEb,WAAW,CAAC;QAC5CK,QAAQ,CAACO,IAAI,CAACE,uBAAuB,CAAC,IAAI,EAAEd,WAAW,GAAG,IAAI,CAAC;QAC/DK,QAAQ,CAACO,IAAI,CAACG,4BAA4B,CACxC,IAAI,EACJf,WAAW,GAAGE,IAAI,CAACH,QAAQ,CAC5B;QACDI,UAAU,CAACa,KAAK,CAAChB,WAAW,CAAC;QAC7BG,UAAU,CAACc,IAAI,CAACjB,WAAW,GAAGE,IAAI,CAACH,QAAQ,CAAC;QAC5CC,WAAW,IAAIE,IAAI,CAACH,QAAQ,GAAG,GAAG;MACpC,CAAC,CAAC;MAEF,OAAOL,OAAO,CAACC,OAAO,EAAE;IAC1B,CAAC;IAED,OAAON,KAAK;EACd;EAEA;;;EAGQL,cAAcA,CAACN,YAA0B;IAC/C,MAAMW,KAAK,GAAG,IAAIC,KAAK,EAAE;IACzBD,KAAK,CAACE,MAAM,GAAG,GAAG;IAEjBF,KAAa,CAACI,aAAa,GAAG,MAAK;MAClC,MAAMI,MAAM,GAAG,CACb;QAAEC,IAAI,EAAE,MAAM;QAAEC,QAAQ,EAAE;MAAG,CAAE,EAC/B;QAAED,IAAI,EAAE,MAAM;QAAEC,QAAQ,EAAE;MAAG,CAAE,EAC/B;QAAED,IAAI,EAAE,MAAM;QAAEC,QAAQ,EAAE;MAAG,CAAE,EAC/B;QAAED,IAAI,EAAE,KAAK;QAAEC,QAAQ,EAAE;MAAG,CAAE,CAC/B;MAED,IAAIC,WAAW,GAAGtB,YAAY,CAACsB,WAAW;MAC1CH,MAAM,CAACI,OAAO,CAAEC,IAAI,IAAI;QACtB,MAAMC,UAAU,GAAGzB,YAAY,CAAC0B,gBAAgB,EAAE;QAClD,MAAMC,QAAQ,GAAG3B,YAAY,CAAC4B,UAAU,EAAE;QAC1CH,UAAU,CAACI,OAAO,CAACF,QAAQ,CAAC;QAC5BA,QAAQ,CAACE,OAAO,CAAC7B,YAAY,CAAC8B,WAAW,CAAC;QAC1CL,UAAU,CAACM,SAAS,CAACC,KAAK,GAAGR,IAAI,CAACJ,IAAI;QACtCK,UAAU,CAACQ,IAAI,GAAG,MAAM;QACxBN,QAAQ,CAACO,IAAI,CAACC,cAAc,CAAC,CAAC,EAAEb,WAAW,CAAC;QAC5CK,QAAQ,CAACO,IAAI,CAACE,uBAAuB,CAAC,GAAG,EAAEd,WAAW,GAAG,IAAI,CAAC;QAC9DK,QAAQ,CAACO,IAAI,CAACG,4BAA4B,CACxC,IAAI,EACJf,WAAW,GAAGE,IAAI,CAACH,QAAQ,CAC5B;QACDI,UAAU,CAACa,KAAK,CAAChB,WAAW,CAAC;QAC7BG,UAAU,CAACc,IAAI,CAACjB,WAAW,GAAGE,IAAI,CAACH,QAAQ,CAAC;QAC5CC,WAAW,IAAIE,IAAI,CAACH,QAAQ,GAAG,GAAG;MACpC,CAAC,CAAC;MAEF,OAAOL,OAAO,CAACC,OAAO,EAAE;IAC1B,CAAC;IAED,OAAON,KAAK;EACd;EAEA;;;EAGQJ,uBAAuBA,CAC7BP,YAA0B;IAE1B,MAAMW,KAAK,GAAG,IAAIC,KAAK,EAAE;IACzBD,KAAK,CAACE,MAAM,GAAG,GAAG;IAEjBF,KAAa,CAACI,aAAa,GAAG,MAAK;MAClC,MAAM+B,KAAK,GAAG,CACZ;QAAE1B,IAAI,EAAE,MAAM;QAAEC,QAAQ,EAAE,IAAI;QAAE0B,KAAK,EAAE;MAAC,CAAE,EAC1C;QAAE3B,IAAI,EAAE,MAAM;QAAEC,QAAQ,EAAE,IAAI;QAAE0B,KAAK,EAAE;MAAG,CAAE,CAC7C;MAEDD,KAAK,CAACvB,OAAO,CAAEC,IAAI,IAAI;QACrBiB,UAAU,CAAC,MAAK;UACd,MAAMhB,UAAU,GAAGzB,YAAY,CAAC0B,gBAAgB,EAAE;UAClD,MAAMC,QAAQ,GAAG3B,YAAY,CAAC4B,UAAU,EAAE;UAC1CH,UAAU,CAACI,OAAO,CAACF,QAAQ,CAAC;UAC5BA,QAAQ,CAACE,OAAO,CAAC7B,YAAY,CAAC8B,WAAW,CAAC;UAC1CL,UAAU,CAACM,SAAS,CAACC,KAAK,GAAGR,IAAI,CAACJ,IAAI;UACtCK,UAAU,CAACQ,IAAI,GAAG,UAAU;UAC5B,MAAMe,SAAS,GAAGhD,YAAY,CAACsB,WAAW;UAC1CK,QAAQ,CAACO,IAAI,CAACC,cAAc,CAAC,CAAC,EAAEa,SAAS,CAAC;UAC1CrB,QAAQ,CAACO,IAAI,CAACE,uBAAuB,CAAC,GAAG,EAAEY,SAAS,GAAG,IAAI,CAAC;UAC5DrB,QAAQ,CAACO,IAAI,CAACG,4BAA4B,CACxC,IAAI,EACJW,SAAS,GAAGxB,IAAI,CAACH,QAAQ,CAC1B;UACDI,UAAU,CAACa,KAAK,CAACU,SAAS,CAAC;UAC3BvB,UAAU,CAACc,IAAI,CAACS,SAAS,GAAGxB,IAAI,CAACH,QAAQ,CAAC;QAC5C,CAAC,EAAEG,IAAI,CAACuB,KAAK,GAAG,IAAI,CAAC;MACvB,CAAC,CAAC;MAEF,OAAO/B,OAAO,CAACC,OAAO,EAAE;IAC1B,CAAC;IAED,OAAON,KAAK;EACd;EAEA;;;EAGQsC,IAAIA,CAACC,SAAiB,EAAEC,IAAA,GAAgB,KAAK;IACnD,MAAMC,KAAK,GAAG,IAAI,CAACnE,MAAM,CAACiE,SAAS,CAAC;IACpC,IAAI,CAACE,KAAK,EAAE;IAEZ,IAAI,CAAClE,SAAS,CAACgE,SAAS,CAAC,GAAG,IAAI;IAEhC,IAAKE,KAAa,CAACrC,aAAa,EAAE;MAC/BqC,KAAa,CAACrC,aAAa,EAAE;MAC9B,IAAIoC,IAAI,EAAE;QACR,MAAME,QAAQ,GAAGC,WAAW,CAAC,MAAK;UAChC,IAAI,IAAI,CAACpE,SAAS,CAACgE,SAAS,CAAC,EAAE;YAC5BE,KAAa,CAACrC,aAAa,EAAE;WAC/B,MAAM;YACLwC,aAAa,CAACF,QAAQ,CAAC;;QAE3B,CAAC,EAAE,IAAI,CAAC;;;EAGd;EAEA;;;EAGQd,IAAIA,CAACW,SAAiB;IAC5B,IAAI,CAAChE,SAAS,CAACgE,SAAS,CAAC,GAAG,KAAK;IACjC,MAAME,KAAK,GAAG,IAAI,CAACnE,MAAM,CAACiE,SAAS,CAAC;IACpC,IAAIE,KAAK,IAAKA,KAAa,CAACT,aAAa,EAAE;MACxCS,KAAa,CAACT,aAAa,EAAE;;EAElC;EAEA;;;EAGQa,aAAaA,CAAA;IACnBC,MAAM,CAACC,IAAI,CAAC,IAAI,CAACzE,MAAM,CAAC,CAACsC,OAAO,CAAEoC,IAAI,IAAI;MACxC,IAAI,CAACpB,IAAI,CAACoB,IAAI,CAAC;IACjB,CAAC,CAAC;EACJ;EAEA;;;EAGQ9D,uBAAuBA,CAAA;IAC7B4C,UAAU,CAAC,MAAK;MACd,IAAI,CAACmB,wBAAwB,EAAE;MAC/B,IAAI,CAACC,4BAA4B,EAAE;MACnC,IAAI,CAACC,sBAAsB,EAAE;IAC/B,CAAC,EAAE,IAAI,CAAC;EACV;EAEA;;;EAGQF,wBAAwBA,CAAA;IAC9B,IAAI,CAAClF,MAAM,CACRqF,SAAS,CAAiC;MACzCC,KAAK,EAAE3F,0BAA0B;MACjC4F,WAAW,EAAE;KACd,CAAC,CACDF,SAAS,CAAC;MACTG,IAAI,EAAEA,CAAC;QAAEC,IAAI;QAAEC;MAAM,CAAE,KAAI;QACzB,IAAID,IAAI,EAAEtF,YAAY,EAAE;UACtB,IAAI,CAACwF,kBAAkB,CAACF,IAAI,CAACtF,YAAY,CAAC;;MAE9C,CAAC;MACD2B,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;QAC5DiC,UAAU,CAAC,MAAM,IAAI,CAACmB,wBAAwB,EAAE,EAAE,IAAI,CAAC;MACzD;KACD,CAAC;EACN;EAEA;;;EAGQC,4BAA4BA,CAAA;IAClC,IAAI,CAACnF,MAAM,CACRqF,SAAS,CAA8B;MACtCC,KAAK,EAAE1F,gCAAgC;MACvC2F,WAAW,EAAE;KACd,CAAC,CACDF,SAAS,CAAC;MACTG,IAAI,EAAEA,CAAC;QAAEC,IAAI;QAAEC;MAAM,CAAE,KAAI;QACzB,IAAID,IAAI,EAAEG,iBAAiB,EAAE;UAC3B,IAAI,CAACC,sBAAsB,CAACJ,IAAI,CAACG,iBAAiB,CAAC;;MAEvD,CAAC;MACD9D,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;QAC1DiC,UAAU,CAAC,MAAM,IAAI,CAACoB,4BAA4B,EAAE,EAAE,IAAI,CAAC;MAC7D;KACD,CAAC;EACN;EAEA;;;EAGQC,sBAAsBA,CAAA;IAC5B,IAAI,CAACpF,MAAM,CACRqF,SAAS,CAAsB;MAC9BC,KAAK,EAAEzF,wBAAwB;MAC/B0F,WAAW,EAAE;KACd,CAAC,CACDF,SAAS,CAAC;MACTG,IAAI,EAAEA,CAAC;QAAEC,IAAI;QAAEC;MAAM,CAAE,KAAI;QACzB,IAAID,IAAI,EAAEK,UAAU,EAAE;UACpB,IAAI,CAACC,gBAAgB,CAACN,IAAI,CAACK,UAAU,CAAC;;MAE1C,CAAC;MACDhE,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;QAC1DiC,UAAU,CAAC,MAAM,IAAI,CAACqB,sBAAsB,EAAE,EAAE,IAAI,CAAC;MACvD;KACD,CAAC;EACN;EAEA;;;EAGQO,kBAAkBA,CAACK,IAAkB;IAC3C,IAAI,CAAC7F,YAAY,CAACqF,IAAI,CAACQ,IAAI,CAAC;IAC5B,IAAI,CAACzB,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC;EAC7B;EAEA;;;EAGQsB,sBAAsBA,CAACG,IAAU;IACvC,QAAQA,IAAI,CAACC,MAAM;MACjB,KAAK5G,UAAU,CAAC6G,SAAS;QACvB,IAAI,CAACrC,IAAI,CAAC,UAAU,CAAC;QACrB,IAAI,CAACU,IAAI,CAAC,gBAAgB,CAAC;QAC3B,IAAI,CAACrE,UAAU,CAACsF,IAAI,CAACQ,IAAI,CAAC;QAC1B,IAAI,CAAC7F,YAAY,CAACqF,IAAI,CAAC,IAAI,CAAC;QAC5B;MACF,KAAKnG,UAAU,CAAC8G,KAAK;QACnB,IAAI,CAAC5B,IAAI,CAAC,UAAU,CAAC;QACrB,IAAI,CAACrE,UAAU,CAACsF,IAAI,CAAC,IAAI,CAAC;QAC1B,IAAI,CAACrF,YAAY,CAACqF,IAAI,CAAC,IAAI,CAAC;QAC5B,IAAI,CAACY,aAAa,EAAE;QACpB;MACF,KAAK/G,UAAU,CAACgH,QAAQ;QACtB,IAAI,CAACxC,IAAI,CAAC,UAAU,CAAC;QACrB,IAAI,CAACU,IAAI,CAAC,UAAU,CAAC;QACrB,IAAI,CAACrE,UAAU,CAACsF,IAAI,CAAC,IAAI,CAAC;QAC1B,IAAI,CAACrF,YAAY,CAACqF,IAAI,CAAC,IAAI,CAAC;QAC5B;;EAEN;EAEA;;;EAGQO,gBAAgBA,CAACO,MAAW;IAClC,IAAI,CAAC,IAAI,CAAC3F,cAAc,EAAE;IAE1B,QAAQ2F,MAAM,CAAC/C,IAAI;MACjB,KAAK,OAAO;QACV,IAAI,CAACgD,iBAAiB,CAACD,MAAM,CAAC;QAC9B;MACF,KAAK,QAAQ;QACX,IAAI,CAACE,kBAAkB,CAACF,MAAM,CAAC;QAC/B;MACF,KAAK,eAAe;QAClB,IAAI,CAACG,wBAAwB,CAACH,MAAM,CAAC;QACrC;;EAEN;EAEA;;;EAGQlF,gBAAgBA,CAAA;IACtB,MAAMsF,aAAa,GAAqB;MACtCC,UAAU,EAAE,CACV;QAAEC,IAAI,EAAE;MAA8B,CAAE,EACxC;QAAEA,IAAI,EAAE;MAA+B,CAAE;KAE5C;IAED,IAAI;MACF,IAAI,CAACjG,cAAc,GAAG,IAAIkG,iBAAiB,CAACH,aAAa,CAAC;MAC1D,IAAI,CAACI,yBAAyB,EAAE;KACjC,CAAC,OAAOhF,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;;EAExD;EAEA;;;EAGQgF,yBAAyBA,CAAA;IAC/B,IAAI,CAAC,IAAI,CAACnG,cAAc,EAAE;IAE1B,IAAI,CAACA,cAAc,CAACoG,OAAO,GAAIC,KAAK,IAAI;MACtC,IAAI,CAACnG,YAAY,GAAGmG,KAAK,CAACC,OAAO,CAAC,CAAC,CAAC;MACpC,IAAI,CAACC,kBAAkB,EAAE;IAC3B,CAAC;IAED,IAAI,CAACvG,cAAc,CAACwG,cAAc,GAAIH,KAAK,IAAI;MAC7C,IAAIA,KAAK,CAACI,SAAS,EAAE;QACnB,IAAI,CAACC,UAAU,CAAC,eAAe,EAAEC,IAAI,CAACC,SAAS,CAACP,KAAK,CAACI,SAAS,CAAC,CAAC;;IAErE,CAAC;EACH;EAEA;;;EAGQF,kBAAkBA,CAAA;IACxB,IAAI,IAAI,CAACrG,YAAY,IAAI,IAAI,CAACE,kBAAkB,EAAE;MAChD,IAAI,CAACA,kBAAkB,CAACyG,SAAS,GAAG,IAAI,CAAC3G,YAAY;MACrD,IAAI,CAACE,kBAAkB,CAAC0G,KAAK,GAAG,KAAK;MACrC,IAAI,CAAC1G,kBAAkB,CAACoB,MAAM,GAAG,CAAC;MAClC,IAAI,CAACpB,kBAAkB,CAACwD,IAAI,EAAE;;IAGhC;IACA,MAAMmD,MAAM,GAAGC,QAAQ,CAACC,gBAAgB,CAAC,OAAO,CAAC;IACjD,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,MAAM,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;MACtC,IAAI,IAAI,CAAChH,YAAY,IAAI,CAAC6G,MAAM,CAACG,CAAC,CAAC,CAACL,SAAS,EAAE;QAC7CE,MAAM,CAACG,CAAC,CAAC,CAACL,SAAS,GAAG,IAAI,CAAC3G,YAAY;QACvC6G,MAAM,CAACG,CAAC,CAAC,CAACJ,KAAK,GAAG,KAAK;QACvBC,MAAM,CAACG,CAAC,CAAC,CAAC1F,MAAM,GAAG,CAAC;QACpBuF,MAAM,CAACG,CAAC,CAAC,CAACtD,IAAI,EAAE;QAChB;;;EAGN;EAEA;;;EAGAwD,YAAYA,CACVC,WAAmB,EACnBC,QAAkB,EAClBC,cAAuB;IAEvB;IACA,MAAMC,MAAM,GACV,OAAO,GAAGC,IAAI,CAACC,GAAG,EAAE,GAAG,GAAG,GAAGC,IAAI,CAACC,MAAM,EAAE,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;IAEtE,OAAO,IAAI,CAACzI,MAAM,CACf0I,MAAM,CAAyB;MAC9BC,QAAQ,EAAErJ,sBAAsB;MAChCsJ,SAAS,EAAE;QACTZ,WAAW;QACXC,QAAQ;QACRE,MAAM;QACNU,KAAK,EAAE,EAAE;QACTX;;KAEH,CAAC,CACDY,IAAI,CACH3J,GAAG,CAAE4J,MAAM,IAAI;MACbhH,OAAO,CAACiH,GAAG,CAAC,mCAAmC,EAAED,MAAM,CAAC;MAExD,IAAI,CAACA,MAAM,CAACtD,IAAI,EAAEsC,YAAY,EAAE;QAC9BhG,OAAO,CAACD,KAAK,CAAC,yCAAyC,EAAEiH,MAAM,CAAC;QAChE,MAAM,IAAIE,KAAK,CAAC,mCAAmC,CAAC;;MAGtD,MAAMjD,IAAI,GAAG+C,MAAM,CAACtD,IAAI,CAACsC,YAAY;MACrChG,OAAO,CAACiH,GAAG,CAAC,8CAA8C,EAAEhD,IAAI,CAAC;MAEjE,IAAI,CAAC9F,UAAU,CAACsF,IAAI,CAACQ,IAAI,CAAC;MAC1B,IAAI,CAAChF,aAAa,GAAGgF,IAAI,CAAC9B,EAAE;MAE5B,OAAO8B,IAAI;IACb,CAAC,CAAC,EACF5G,UAAU,CAAE0C,KAAK,IAAI;MACnBC,OAAO,CAACD,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;MAC9D,OAAO5C,UAAU,CACf,MACE,IAAI+J,KAAK,CACP,0CAA0C,GAAGnH,KAAK,CAACoH,OAAO,CAC3D,CACJ;IACH,CAAC,CAAC,CACH;EACL;EAEA;;;EAGAC,UAAUA,CAAChJ,YAA0B;IACnC,OAAO,IAAI,CAACH,MAAM,CACf0I,MAAM,CAAuB;MAC5BC,QAAQ,EAAEpJ,oBAAoB;MAC9BqJ,SAAS,EAAE;QAAET,MAAM,EAAEhI,YAAY,CAAC+D,EAAE;QAAEkF,MAAM,EAAE;MAAI;KACnD,CAAC,CACDN,IAAI,CACH3J,GAAG,CAAE4J,MAAM,IAAI;MACb,IAAI,CAACA,MAAM,CAACtD,IAAI,EAAE0D,UAAU,EAAE;QAC5B,MAAM,IAAIF,KAAK,CAAC,uBAAuB,CAAC;;MAE1C,MAAMjD,IAAI,GAAG+C,MAAM,CAACtD,IAAI,CAAC0D,UAAU;MACnC,IAAI,CAACtF,IAAI,CAAC,UAAU,CAAC;MACrB,IAAI,CAACU,IAAI,CAAC,gBAAgB,CAAC;MAC3B,IAAI,CAACrE,UAAU,CAACsF,IAAI,CAACQ,IAAI,CAAC;MAC1B,IAAI,CAAC7F,YAAY,CAACqF,IAAI,CAAC,IAAI,CAAC;MAC5B,OAAOQ,IAAI;IACb,CAAC,CAAC,EACF5G,UAAU,CAAE0C,KAAK,IAAI;MACnBC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,OAAO5C,UAAU,CACf,MAAM,IAAI+J,KAAK,CAAC,yCAAyC,CAAC,CAC3D;IACH,CAAC,CAAC,CACH;EACL;EAEA;;;EAGAI,UAAUA,CAAClB,MAAc,EAAEmB,MAAe;IACxC,OAAO,IAAI,CAACtJ,MAAM,CACf0I,MAAM,CAA8B;MACnCC,QAAQ,EAAEnJ,oBAAoB;MAC9BoJ,SAAS,EAAE;QAAET,MAAM;QAAEmB,MAAM,EAAEA,MAAM,IAAI;MAAe;KACvD,CAAC,CACDR,IAAI,CACH3J,GAAG,CAAE4J,MAAM,IAAI;MACb,IAAI,CAAClF,IAAI,CAAC,UAAU,CAAC;MACrB,IAAI,CAAC1D,YAAY,CAACqF,IAAI,CAAC,IAAI,CAAC;MAC5B,IAAI,CAACtF,UAAU,CAACsF,IAAI,CAAC,IAAI,CAAC;MAC1B,OAAOuD,MAAM,CAACtD,IAAK,CAAC4D,UAAU;IAChC,CAAC,CAAC,EACFjK,UAAU,CAAE0C,KAAK,IAAI;MACnBC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,OAAO5C,UAAU,CAAC,MAAM,IAAI+J,KAAK,CAAC,iCAAiC,CAAC,CAAC;IACvE,CAAC,CAAC,CACH;EACL;EAEA;;;EAGAM,OAAOA,CAACpB,MAAc;IACpB,OAAO,IAAI,CAACnI,MAAM,CACf0I,MAAM,CAA2B;MAChCC,QAAQ,EAAElJ,iBAAiB;MAC3BmJ,SAAS,EAAE;QAAET,MAAM;QAAEqB,QAAQ,EAAE;MAAI;KACpC,CAAC,CACDV,IAAI,CACH3J,GAAG,CAAE4J,MAAM,IAAI;MACb,IAAI,CAACxE,IAAI,CAAC,UAAU,CAAC;MACrB,IAAI,CAACrE,UAAU,CAACsF,IAAI,CAAC,IAAI,CAAC;MAC1B,IAAI,CAACrF,YAAY,CAACqF,IAAI,CAAC,IAAI,CAAC;MAC5B,IAAI,CAACY,aAAa,EAAE;MACpB,OAAO2C,MAAM,CAACtD,IAAK,CAAC8D,OAAO;IAC7B,CAAC,CAAC,EACFnK,UAAU,CAAE0C,KAAK,IAAI;MACnBC,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1C,OAAO5C,UAAU,CACf,MAAM,IAAI+J,KAAK,CAAC,kCAAkC,CAAC,CACpD;IACH,CAAC,CAAC,CACH;EACL;EAEA;;;EAGAQ,gBAAgBA,CACdC,UAA4B,EAC5BC,WAA6B;IAE7B,IAAI,CAAC7I,iBAAiB,GAAG4I,UAAU;IACnC,IAAI,CAAC3I,kBAAkB,GAAG4I,WAAW;EACvC;EAEA;;;EAGQtC,UAAUA,CAAC9D,IAAY,EAAEkC,IAAY;IAC3C;IACA1B,UAAU,CAAC,MAAK;MACd,IAAI,CAACgC,gBAAgB,CAAC;QACpBoC,MAAM,EAAE,IAAI,CAACnH,aAAa;QAC1B4I,QAAQ,EAAE,YAAY;QACtBrG,IAAI;QACJkC;OACD,CAAC;IACJ,CAAC,EAAE,GAAG,CAAC;EACT;EAEA;;;EAGcc,iBAAiBA,CAACD,MAAW;IAAA,OAAAuD,iBAAA;EAE3C,CAAC,CADC;EAGF;;;EAGcrD,kBAAkBA,CAACF,MAAW;IAAA,OAAAuD,iBAAA;EAE5C,CAAC,CADC;EAGF;;;EAGcpD,wBAAwBA,CAACH,MAAW;IAAA,OAAAuD,iBAAA;EAElD,CAAC,CADC;EAGF;;;EAGQzD,aAAaA,CAAA;IACnB,IAAI,IAAI,CAACxF,WAAW,EAAE;MACpB,IAAI,CAACA,WAAW,CAACkJ,SAAS,EAAE,CAACjH,OAAO,CAAEkH,KAAK,IAAKA,KAAK,CAAClG,IAAI,EAAE,CAAC;MAC7D,IAAI,CAACjD,WAAW,GAAG,IAAI;;IAGzB,IAAI,IAAI,CAACD,cAAc,EAAE;MACvB,IAAI,CAACA,cAAc,CAACqJ,KAAK,EAAE;MAC3B,IAAI,CAACrJ,cAAc,GAAG,IAAI;;IAG5B,IAAI,CAACE,YAAY,GAAG,IAAI;EAC1B;EAEA;;;EAGAoJ,YAAYA,CAAA;IACVlI,OAAO,CAACiH,GAAG,CAAC,uCAAuC,CAAC;EACtD;EAEA;;;EAGAkB,WAAWA,CAAA;IACT,IAAI,CAACxJ,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;IAC1C,OAAO,IAAI,CAACA,cAAc;EAC5B;EAEA;;;EAGAyJ,WAAWA,CAAA;IACT,IAAI,CAAC1J,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;IAC1C,OAAO,IAAI,CAACA,cAAc;EAC5B;EAEA;;;EAGA2J,WAAWA,CACTjC,MAAc,EACdkC,WAAqB,EACrBC,WAAqB;IAErB,OAAO,IAAI,CAACtK,MAAM,CACf0I,MAAM,CAAmC;MACxCC,QAAQ,EAAEjJ,0BAA0B;MACpCkJ,SAAS,EAAE;QAAET,MAAM;QAAEoC,KAAK,EAAEF,WAAW;QAAEpI,KAAK,EAAEqI;MAAW;KAC5D,CAAC,CACDxB,IAAI,CACH3J,GAAG,CAAE4J,MAAM,IAAI;MACb,OAAOA,MAAM,CAACtD,IAAK,CAAC+E,eAAe;IACrC,CAAC,CAAC,EACFpL,UAAU,CAAE0C,KAAK,IAAI;MACnBC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,OAAO5C,UAAU,CACf,MAAM,IAAI+J,KAAK,CAAC,sCAAsC,CAAC,CACxD;IACH,CAAC,CAAC,CACH;EACL;EAEAwB,WAAWA,CAAA;IACT,IAAI,CAAC3F,aAAa,EAAE;IACpB,IAAI,CAACsB,aAAa,EAAE;EACtB;;;uBA9sBWtG,WAAW,EAAA4K,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,aAAA;IAAA;EAAA;;;aAAXjL,WAAW;MAAAkL,OAAA,EAAXlL,WAAW,CAAAmL,IAAA;MAAAC,UAAA,EAFV;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}