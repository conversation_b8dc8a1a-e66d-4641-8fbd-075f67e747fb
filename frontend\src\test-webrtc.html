<!DOCTYPE html>
<html>
<head>
    <title>Test WebRTC</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #1a1a1a;
            color: white;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .video-container {
            display: flex;
            gap: 20px;
            margin: 20px 0;
        }
        video {
            width: 300px;
            height: 200px;
            background: #333;
            border: 2px solid #00ff88;
            border-radius: 8px;
        }
        button {
            background: #00ff88;
            color: #1a1a1a;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
        }
        button:hover {
            background: #00cc66;
        }
        button:disabled {
            background: #666;
            cursor: not-allowed;
        }
        .status {
            background: #333;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎥 Test WebRTC - Communication Audio/Vidéo</h1>
        
        <div class="status" id="status">
            Status: Prêt à tester
        </div>
        
        <div>
            <button onclick="testUserMedia()">📹 Tester Caméra/Micro</button>
            <button onclick="testAudioOnly()">🎤 Tester Audio Seulement</button>
            <button onclick="stopMedia()">⏹️ Arrêter</button>
        </div>
        
        <div class="video-container">
            <div>
                <h3>📹 Vidéo Locale (Vous)</h3>
                <video id="localVideo" autoplay muted playsinline></video>
            </div>
            <div>
                <h3>📺 Vidéo Distante (Autre utilisateur)</h3>
                <video id="remoteVideo" autoplay playsinline></video>
            </div>
        </div>
        
        <div>
            <h3>🔊 Test des Sons d'Appel</h3>
            <button onclick="testRingtone()">🎵 Sonnerie</button>
            <button onclick="testConnected()">🎶 Connexion</button>
            <button onclick="testEnd()">🔊 Fin d'appel</button>
            <button onclick="testNotification()">🔔 Notification</button>
        </div>
        
        <div class="status">
            <h3>📋 Instructions:</h3>
            <ol>
                <li>Cliquez sur "Tester Caméra/Micro" pour autoriser l'accès</li>
                <li>Vous devriez voir votre vidéo dans "Vidéo Locale"</li>
                <li>Testez les sons d'appel avec les boutons</li>
                <li>Ouvrez deux onglets pour simuler un appel entre utilisateurs</li>
            </ol>
        </div>
    </div>

    <script>
        let localStream = null;
        let peerConnection = null;
        
        function updateStatus(message) {
            document.getElementById('status').textContent = 'Status: ' + message;
            console.log('🔍 [Test]', message);
        }
        
        async function testUserMedia() {
            try {
                updateStatus('Demande d\'accès à la caméra/micro...');
                
                const stream = await navigator.mediaDevices.getUserMedia({
                    video: true,
                    audio: true
                });
                
                localStream = stream;
                const localVideo = document.getElementById('localVideo');
                localVideo.srcObject = stream;
                
                updateStatus('✅ Caméra/micro activés avec succès!');
                
                // Afficher les informations sur les tracks
                const videoTracks = stream.getVideoTracks();
                const audioTracks = stream.getAudioTracks();
                
                console.log('📹 Video tracks:', videoTracks.length);
                console.log('🎤 Audio tracks:', audioTracks.length);
                
                if (videoTracks.length > 0) {
                    console.log('📹 Video track settings:', videoTracks[0].getSettings());
                }
                if (audioTracks.length > 0) {
                    console.log('🎤 Audio track settings:', audioTracks[0].getSettings());
                }
                
            } catch (error) {
                updateStatus('❌ Erreur: ' + error.message);
                console.error('❌ [Test] Erreur getUserMedia:', error);
            }
        }
        
        async function testAudioOnly() {
            try {
                updateStatus('Demande d\'accès au micro seulement...');
                
                const stream = await navigator.mediaDevices.getUserMedia({
                    video: false,
                    audio: true
                });
                
                localStream = stream;
                updateStatus('✅ Micro activé avec succès!');
                
                const audioTracks = stream.getAudioTracks();
                console.log('🎤 Audio tracks:', audioTracks.length);
                
            } catch (error) {
                updateStatus('❌ Erreur: ' + error.message);
                console.error('❌ [Test] Erreur getUserMedia audio:', error);
            }
        }
        
        function stopMedia() {
            if (localStream) {
                localStream.getTracks().forEach(track => {
                    track.stop();
                });
                localStream = null;
                
                const localVideo = document.getElementById('localVideo');
                localVideo.srcObject = null;
                
                updateStatus('⏹️ Médias arrêtés');
            }
        }
        
        // Test des sons (simulation)
        function testRingtone() {
            updateStatus('🎵 Test de la sonnerie...');
            playTestSound([659.25, 587.33, 739.99, 783.99], 'Sonnerie Nokia moderne');
        }
        
        function testConnected() {
            updateStatus('🎶 Test du son de connexion...');
            playTestSound([523.25, 659.25, 783.99, 1046.5], 'Son de connexion');
        }
        
        function testEnd() {
            updateStatus('🔊 Test du son de fin...');
            playTestSound([783.99, 659.25, 523.25, 392.00], 'Son de fin');
        }
        
        function testNotification() {
            updateStatus('🔔 Test du son de notification...');
            playTestSound([523.25, 783.99], 'Notification');
        }
        
        function playTestSound(frequencies, name) {
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            
            frequencies.forEach((freq, index) => {
                setTimeout(() => {
                    const oscillator = audioContext.createOscillator();
                    const gainNode = audioContext.createGain();
                    
                    oscillator.connect(gainNode);
                    gainNode.connect(audioContext.destination);
                    
                    oscillator.frequency.value = freq;
                    oscillator.type = 'triangle';
                    
                    gainNode.gain.setValueAtTime(0, audioContext.currentTime);
                    gainNode.gain.linearRampToValueAtTime(0.3, audioContext.currentTime + 0.02);
                    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2);
                    
                    oscillator.start(audioContext.currentTime);
                    oscillator.stop(audioContext.currentTime + 0.2);
                }, index * 150);
            });
            
            setTimeout(() => {
                updateStatus('✅ ' + name + ' joué');
            }, frequencies.length * 150 + 200);
        }
        
        // Vérifier les capacités du navigateur
        window.onload = function() {
            if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
                updateStatus('❌ WebRTC non supporté par ce navigateur');
                return;
            }
            
            if (!window.RTCPeerConnection) {
                updateStatus('❌ RTCPeerConnection non supporté');
                return;
            }
            
            updateStatus('✅ WebRTC supporté - Prêt à tester!');
        };
    </script>
</body>
</html>
