import { Injectable, <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';
import { Apollo } from 'apollo-angular';
import { BehaviorSubject, Observable, throwError, of, from } from 'rxjs';
import { map, catchError, switchMap } from 'rxjs/operators';
import {
  Call,
  CallType,
  CallStatus,
  IncomingCall,
  CallSuccess,
} from '../models/message.model';
import {
  INITIATE_CALL_MUTATION,
  ACCEPT_CALL_MUTATION,
  REJECT_CALL_MUTATION,
  END_CALL_MUTATION,
  TOGGLE_CALL_MEDIA_MUTATION,
  INCOMING_CALL_SUBSCRIPTION,
  CALL_STATUS_CHANGED_SUBSCRIPTION,
} from '../graphql/message.graphql';
import { LoggerService } from './logger.service';

@Injectable({
  providedIn: 'root',
})
export class CallService implements OnDestroy {
  // État des appels
  private activeCall = new BehaviorSubject<Call | null>(null);
  private incomingCall = new BehaviorSubject<IncomingCall | null>(null);

  // Observables publics
  public activeCall$ = this.activeCall.asObservable();
  public incomingCall$ = this.incomingCall.asObservable();

  // Propriétés pour la gestion des sons
  private sounds: { [key: string]: HTMLAudioElement } = {};
  private isPlaying: { [key: string]: boolean } = {};
  private muted = false;

  // États simples pour les médias
  private isVideoEnabled = true;
  private isAudioEnabled = true;

  constructor(private apollo: Apollo, private logger: LoggerService) {
    this.preloadSounds();
    this.initializeSubscriptions();
  }

  /**
   * Initialise les subscriptions avec un délai pour s'assurer que l'authentification est prête
   */
  private initializeSubscriptions(): void {
    // Attendre un peu pour s'assurer que l'authentification est prête
    setTimeout(() => {
      this.subscribeToIncomingCalls();
      this.subscribeToCallStatusChanges();
    }, 1000);

    // Réessayer après 5 secondes si la première tentative échoue
    setTimeout(() => {
      if (!this.incomingCall.value) {
        console.log('🔄 [CallService] Retrying subscription initialization...');
        this.subscribeToIncomingCalls();
        this.subscribeToCallStatusChanges();
      }
    }, 5000);
  }

  /**
   * Précharge les sons utilisés dans l'application
   */
  private preloadSounds(): void {
    // Créer des sons synthétiques si les fichiers ne sont pas disponibles
    this.createSyntheticSounds();

    // Essayer de charger les vrais fichiers audio
    this.loadSound('ringtone', '/assets/sounds/ringtone.mp3');
    this.loadSound('call-end', '/assets/sounds/call-end.mp3');
    this.loadSound('call-connected', '/assets/sounds/call-connected.mp3');
  }

  /**
   * Crée des sons synthétiques comme fallback
   */
  private createSyntheticSounds(): void {
    try {
      // Créer un contexte audio pour les sons synthétiques
      const audioContext = new (window.AudioContext ||
        (window as any).webkitAudioContext)();

      // Son de sonnerie (bip répétitif)
      this.sounds['ringtone-synthetic'] = this.createBeepSound(
        audioContext,
        800,
        0.3,
        true
      );

      // Son de connexion (bip court)
      this.sounds['call-connected-synthetic'] = this.createBeepSound(
        audioContext,
        1000,
        0.2,
        false
      );

      // Son de fin d'appel (bip grave)
      this.sounds['call-end-synthetic'] = this.createBeepSound(
        audioContext,
        400,
        0.5,
        false
      );

      console.log('🔊 [CallService] Synthetic sounds created as fallback');
    } catch (error) {
      console.warn(
        '⚠️ [CallService] Could not create synthetic sounds:',
        error
      );
    }
  }

  /**
   * Crée un son de bip synthétique
   */
  private createBeepSound(
    audioContext: AudioContext,
    frequency: number,
    duration: number,
    loop: boolean
  ): HTMLAudioElement {
    // Créer un buffer audio
    const sampleRate = audioContext.sampleRate;
    const numSamples = sampleRate * duration;
    const buffer = audioContext.createBuffer(1, numSamples, sampleRate);
    const channelData = buffer.getChannelData(0);

    // Générer une onde sinusoïdale
    for (let i = 0; i < numSamples; i++) {
      channelData[i] =
        Math.sin((2 * Math.PI * frequency * i) / sampleRate) * 0.3;
    }

    // Créer un élément audio factice qui utilise le son synthétique
    const audio = new Audio();
    audio.loop = loop;
    audio.volume = 0.3;

    // Simuler la lecture avec Web Audio API
    (audio as any).playSynthetic = () => {
      const source = audioContext.createBufferSource();
      source.buffer = buffer;
      source.connect(audioContext.destination);
      source.start();
      return Promise.resolve();
    };

    return audio;
  }

  /**
   * Charge un fichier audio
   */
  private loadSound(name: string, path: string): void {
    try {
      const audio = new Audio();

      // Configuration de l'audio
      audio.preload = 'auto';
      audio.volume = 0.7;

      // Gérer les événements de chargement
      audio.addEventListener('canplaythrough', () => {
        console.log(
          `✅ [CallService] Sound ${name} loaded successfully from ${path}`
        );
      });

      audio.addEventListener('error', (e) => {
        console.error(
          `❌ [CallService] Error loading sound ${name} from ${path}:`,
          e
        );
        console.log(
          `🔄 [CallService] Trying to load ${name} with different approach...`
        );

        // Essayer de charger avec un chemin relatif
        const altPath = path.startsWith('/') ? path.substring(1) : path;
        if (altPath !== path) {
          setTimeout(() => {
            audio.src = altPath;
            audio.load();
          }, 100);
        }
      });

      audio.addEventListener('ended', () => {
        this.isPlaying[name] = false;
        console.log(`🔇 [CallService] Sound ${name} ended`);
      });

      // Définir la source et charger
      audio.src = path;
      audio.load();

      this.sounds[name] = audio;
      this.isPlaying[name] = false;

      console.log(`🔊 [CallService] Loading sound ${name} from ${path}`);
    } catch (error) {
      console.error(
        `❌ [CallService] Error creating audio element for ${name}:`,
        error
      );
    }
  }

  /**
   * Joue un son
   */
  private play(name: string, loop: boolean = false): void {
    if (this.muted) {
      console.log(`🔇 [CallService] Sound ${name} muted`);
      return;
    }

    try {
      let sound = this.sounds[name];

      // Si le son principal n'est pas disponible, essayer la version synthétique
      if (!sound || sound.error) {
        const syntheticName = `${name}-synthetic`;
        sound = this.sounds[syntheticName];
        if (sound) {
          console.log(`🔊 [CallService] Using synthetic sound for ${name}`);
        }
      }

      if (!sound) {
        console.warn(
          `🔊 [CallService] Sound ${name} not found (neither original nor synthetic)`
        );
        // Créer un bip simple comme dernier recours
        this.playSimpleBeep(
          name === 'ringtone' ? 800 : name === 'call-connected' ? 1000 : 400
        );
        return;
      }

      sound.loop = loop;
      sound.volume = 0.7;

      if (!this.isPlaying[name]) {
        console.log(`🔊 [CallService] Playing sound: ${name} (loop: ${loop})`);

        // Vérifier si c'est un son synthétique
        if ((sound as any).playSynthetic) {
          (sound as any)
            .playSynthetic()
            .then(() => {
              console.log(
                `✅ [CallService] Synthetic sound ${name} started successfully`
              );
              this.isPlaying[name] = true;

              // Gérer la boucle pour les sons synthétiques
              if (loop) {
                setTimeout(() => {
                  if (this.isPlaying[name]) {
                    this.play(name, loop);
                  }
                }, 1000);
              } else {
                setTimeout(() => {
                  this.isPlaying[name] = false;
                }, 500);
              }
            })
            .catch((error: any) => {
              console.error(
                `❌ [CallService] Error playing synthetic sound ${name}:`,
                error
              );
            });
        } else {
          // Son normal
          sound.currentTime = 0;
          sound
            .play()
            .then(() => {
              console.log(
                `✅ [CallService] Sound ${name} started successfully`
              );
              this.isPlaying[name] = true;
            })
            .catch((error) => {
              console.error(
                `❌ [CallService] Error playing sound ${name}:`,
                error
              );

              // Essayer le son synthétique en cas d'échec
              const syntheticName = `${name}-synthetic`;
              const syntheticSound = this.sounds[syntheticName];
              if (syntheticSound && (syntheticSound as any).playSynthetic) {
                console.log(
                  `🔄 [CallService] Falling back to synthetic sound for ${name}`
                );
                this.play(name, loop);
              } else {
                // Dernier recours : bip simple
                this.playSimpleBeep(
                  name === 'ringtone'
                    ? 800
                    : name === 'call-connected'
                    ? 1000
                    : 400
                );
              }
            });
        }
      } else {
        console.log(`🔊 [CallService] Sound ${name} already playing`);
      }
    } catch (error) {
      console.error(
        `❌ [CallService] Error in play method for ${name}:`,
        error
      );
      // Dernier recours
      this.playSimpleBeep(
        name === 'ringtone' ? 800 : name === 'call-connected' ? 1000 : 400
      );
    }
  }

  /**
   * Joue un bip simple comme dernier recours
   */
  private playSimpleBeep(frequency: number): void {
    try {
      const audioContext = new (window.AudioContext ||
        (window as any).webkitAudioContext)();
      const oscillator = audioContext.createOscillator();
      const gainNode = audioContext.createGain();

      oscillator.connect(gainNode);
      gainNode.connect(audioContext.destination);

      oscillator.frequency.value = frequency;
      oscillator.type = 'sine';

      gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
      gainNode.gain.exponentialRampToValueAtTime(
        0.01,
        audioContext.currentTime + 0.3
      );

      oscillator.start(audioContext.currentTime);
      oscillator.stop(audioContext.currentTime + 0.3);

      console.log(`🔊 [CallService] Playing simple beep at ${frequency}Hz`);
    } catch (error) {
      console.error('❌ [CallService] Could not play simple beep:', error);
    }
  }

  /**
   * Arrête un son
   */
  private stop(name: string): void {
    try {
      const sound = this.sounds[name];
      if (!sound) {
        console.warn(`🔊 [CallService] Sound ${name} not found for stopping`);
        return;
      }

      if (this.isPlaying[name]) {
        console.log(`🔇 [CallService] Stopping sound: ${name}`);
        sound.pause();
        sound.currentTime = 0;
        this.isPlaying[name] = false;
      } else {
        console.log(`🔇 [CallService] Sound ${name} was not playing`);
      }
    } catch (error) {
      console.error(`❌ [CallService] Error stopping sound ${name}:`, error);
    }
  }

  /**
   * S'abonne aux appels entrants
   */
  private subscribeToIncomingCalls(): void {
    console.log('🔔 [CallService] Setting up incoming call subscription...');

    try {
      this.apollo
        .subscribe<{ incomingCall: IncomingCall }>({
          query: INCOMING_CALL_SUBSCRIPTION,
          errorPolicy: 'all', // Continuer même en cas d'erreur
        })
        .subscribe({
          next: ({ data, errors }) => {
            if (errors) {
              console.warn(
                '⚠️ [CallService] GraphQL errors in subscription:',
                errors
              );
            }

            if (data?.incomingCall) {
              console.log('📞 [CallService] Incoming call received:', {
                callId: data.incomingCall.id,
                callType: data.incomingCall.type,
                caller: data.incomingCall.caller?.username,
                conversationId: data.incomingCall.conversationId,
              });
              this.handleIncomingCall(data.incomingCall);
            }
          },
          error: (error) => {
            console.error(
              '❌ [CallService] Error in incoming call subscription:',
              error
            );

            // Réessayer après 5 secondes en cas d'erreur
            setTimeout(() => {
              console.log(
                '🔄 [CallService] Retrying incoming call subscription...'
              );
              this.subscribeToIncomingCalls();
            }, 5000);
          },
          complete: () => {
            console.log(
              '🔚 [CallService] Incoming call subscription completed'
            );
            // Réessayer si la subscription se ferme de manière inattendue
            setTimeout(() => {
              console.log(
                '🔄 [CallService] Restarting subscription after completion...'
              );
              this.subscribeToIncomingCalls();
            }, 2000);
          },
        });
    } catch (error) {
      console.error('❌ [CallService] Failed to create subscription:', error);
      // Réessayer après 3 secondes
      setTimeout(() => {
        this.subscribeToIncomingCalls();
      }, 3000);
    }
  }

  /**
   * Méthode publique pour forcer la réinitialisation de la subscription
   */
  public reinitializeSubscription(): void {
    console.log('🔄 [CallService] Manually reinitializing subscription...');
    this.subscribeToIncomingCalls();
    this.subscribeToCallStatusChanges();
  }

  /**
   * S'abonne aux changements de statut d'appel
   */
  private subscribeToCallStatusChanges(): void {
    console.log(
      '📞 [CallService] Setting up call status change subscription...'
    );

    try {
      this.apollo
        .subscribe<{ callStatusChanged: Call }>({
          query: CALL_STATUS_CHANGED_SUBSCRIPTION,
          errorPolicy: 'all',
        })
        .subscribe({
          next: ({ data, errors }) => {
            if (errors) {
              console.warn(
                '⚠️ [CallService] GraphQL errors in call status subscription:',
                errors
              );
            }

            if (data?.callStatusChanged) {
              console.log(
                '📞 [CallService] Call status changed:',
                data.callStatusChanged
              );
              this.handleCallStatusChange(data.callStatusChanged);
            }
          },
          error: (error) => {
            console.error(
              '❌ [CallService] Error in call status subscription:',
              error
            );
            // Réessayer après 5 secondes
            setTimeout(() => {
              this.subscribeToCallStatusChanges();
            }, 5000);
          },
          complete: () => {
            console.log('🔚 [CallService] Call status subscription completed');
            // Réessayer si la subscription se ferme
            setTimeout(() => {
              this.subscribeToCallStatusChanges();
            }, 2000);
          },
        });
    } catch (error) {
      console.error(
        '❌ [CallService] Failed to create call status subscription:',
        error
      );
      setTimeout(() => {
        this.subscribeToCallStatusChanges();
      }, 3000);
    }
  }

  /**
   * Gère un appel entrant
   */
  private handleIncomingCall(call: IncomingCall): void {
    console.log('🔔 [CallService] Handling incoming call:', {
      callId: call.id,
      callType: call.type,
      caller: call.caller?.username,
      conversationId: call.conversationId,
    });

    this.incomingCall.next(call);
    this.play('ringtone', true);

    console.log(
      '🔊 [CallService] Ringtone started, call notification sent to UI'
    );
  }

  /**
   * Gère les changements de statut d'appel
   */
  private handleCallStatusChange(call: Call): void {
    console.log('📞 [CallService] Call status changed:', call.status);

    switch (call.status) {
      case CallStatus.REJECTED:
        console.log('❌ [CallService] Call was rejected');
        this.stop('ringtone');
        this.play('call-end');
        this.activeCall.next(null);
        this.incomingCall.next(null);
        break;

      case CallStatus.ENDED:
        console.log('📞 [CallService] Call ended');
        this.stopAllSounds();
        this.play('call-end');
        this.activeCall.next(null);
        this.incomingCall.next(null);
        break;

      case CallStatus.CONNECTED:
        console.log('✅ [CallService] Call connected');
        this.stop('ringtone');
        this.play('call-connected');
        this.activeCall.next(call);
        this.incomingCall.next(null);
        break;

      case CallStatus.RINGING:
        console.log('📞 [CallService] Call is ringing');
        this.play('ringtone', true);
        break;

      default:
        console.log('📞 [CallService] Unknown call status:', call.status);
        break;
    }
  }

  /**
   * Initie un appel WebRTC
   */
  initiateCall(
    recipientId: string,
    callType: CallType,
    conversationId?: string
  ): Observable<Call> {
    console.log('🔄 [CallService] Initiating call:', {
      recipientId,
      callType,
      conversationId,
    });

    if (!recipientId) {
      const error = new Error('Recipient ID is required');
      console.error('❌ [CallService] initiateCall error:', error);
      return throwError(() => error);
    }

    // Générer un ID unique pour l'appel
    const callId = `call_${Date.now()}_${Math.random()
      .toString(36)
      .substr(2, 9)}`;

    // Pour l'instant, utiliser une offre WebRTC factice
    const offer = JSON.stringify({
      type: 'offer',
      sdp: 'v=0\r\no=- 0 0 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\n',
    });

    const variables = {
      recipientId,
      callType: callType,
      callId,
      offer,
      conversationId,
    };

    console.log('📤 [CallService] Sending initiate call mutation:', variables);

    return this.apollo
      .mutate<{ initiateCall: Call }>({
        mutation: INITIATE_CALL_MUTATION,
        variables,
      })
      .pipe(
        map((result) => {
          console.log('✅ [CallService] Call initiated successfully:', result);

          if (!result.data?.initiateCall) {
            throw new Error('No call data received from server');
          }

          const call = result.data.initiateCall;
          console.log('📞 [CallService] Call details:', {
            id: call.id,
            type: call.type,
            status: call.status,
            caller: call.caller?.username,
            recipient: call.recipient?.username,
          });

          // Mettre à jour l'état local
          this.activeCall.next(call);

          return call;
        }),
        catchError((error) => {
          console.error('❌ [CallService] initiateCall error:', error);
          this.logger.error('Error initiating call:', error);

          let errorMessage = "Erreur lors de l'initiation de l'appel";
          if (error.networkError) {
            errorMessage = 'Erreur de connexion réseau';
          } else if (error.graphQLErrors?.length > 0) {
            errorMessage = error.graphQLErrors[0].message || errorMessage;
          }

          return throwError(() => new Error(errorMessage));
        })
      );
  }

  /**
   * Accepte un appel entrant
   */
  acceptCall(incomingCall: IncomingCall): Observable<Call> {
    console.log('🔄 [CallService] Accepting call:', incomingCall.id);

    // Générer une réponse WebRTC factice
    const answer = JSON.stringify({
      type: 'answer',
      sdp: 'v=0\r\no=- 0 0 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\n',
    });

    return this.apollo
      .mutate<{ acceptCall: Call }>({
        mutation: ACCEPT_CALL_MUTATION,
        variables: {
          callId: incomingCall.id,
          answer,
        },
      })
      .pipe(
        switchMap((result) => {
          console.log('✅ [CallService] Call accepted successfully:', result);

          if (!result.data?.acceptCall) {
            throw new Error('No call data received from server');
          }

          const call = result.data.acceptCall;

          // Arrêter la sonnerie
          this.stop('ringtone');
          this.play('call-connected');

          // Démarrer les médias pour l'appel de manière asynchrone
          return from(this.startMediaForCall(incomingCall, call));
        }),
        catchError((error) => {
          console.error('❌ [CallService] acceptCall error:', error);
          this.logger.error('Error accepting call:', error);
          return throwError(
            () => new Error("Erreur lors de l'acceptation de l'appel")
          );
        })
      );
  }

  /**
   * Rejette un appel entrant
   */
  rejectCall(callId: string, reason?: string): Observable<CallSuccess> {
    console.log('🔄 [CallService] Rejecting call:', callId, reason);

    return this.apollo
      .mutate<{ rejectCall: CallSuccess }>({
        mutation: REJECT_CALL_MUTATION,
        variables: {
          callId,
          reason: reason || 'User rejected',
        },
      })
      .pipe(
        map((result) => {
          console.log('✅ [CallService] Call rejected successfully:', result);

          if (!result.data?.rejectCall) {
            throw new Error('No response received from server');
          }

          // Mettre à jour l'état local
          this.incomingCall.next(null);
          this.activeCall.next(null);

          // Arrêter la sonnerie
          this.stop('ringtone');

          return result.data.rejectCall;
        }),
        catchError((error) => {
          console.error('❌ [CallService] rejectCall error:', error);
          this.logger.error('Error rejecting call:', error);
          return throwError(() => new Error("Erreur lors du rejet de l'appel"));
        })
      );
  }

  /**
   * Termine un appel en cours
   */
  endCall(callId: string): Observable<CallSuccess> {
    console.log('🔄 [CallService] Ending call:', callId);

    return this.apollo
      .mutate<{ endCall: CallSuccess }>({
        mutation: END_CALL_MUTATION,
        variables: {
          callId,
          feedback: null, // Pas de feedback pour l'instant
        },
      })
      .pipe(
        map((result) => {
          console.log('✅ [CallService] Call ended successfully:', result);

          if (!result.data?.endCall) {
            throw new Error('No response received from server');
          }

          // Mettre à jour l'état local
          this.activeCall.next(null);
          this.incomingCall.next(null);

          // Arrêter tous les sons
          this.stopAllSounds();
          this.play('call-end');

          return result.data.endCall;
        }),
        catchError((error) => {
          console.error('❌ [CallService] endCall error:', error);
          this.logger.error('Error ending call:', error);
          return throwError(
            () => new Error("Erreur lors de la fin de l'appel")
          );
        })
      );
  }

  /**
   * Bascule l'état des médias (audio/vidéo) pendant un appel
   */
  toggleMedia(
    callId: string,
    enableVideo?: boolean,
    enableAudio?: boolean
  ): Observable<CallSuccess> {
    console.log('🔄 [CallService] Toggling media:', {
      callId,
      enableVideo,
      enableAudio,
    });

    return this.apollo
      .mutate<{ toggleCallMedia: CallSuccess }>({
        mutation: TOGGLE_CALL_MEDIA_MUTATION,
        variables: {
          callId,
          video: enableVideo,
          audio: enableAudio,
        },
      })
      .pipe(
        map((result) => {
          console.log('✅ [CallService] Media toggled successfully:', result);

          if (!result.data?.toggleCallMedia) {
            throw new Error('No response received from server');
          }

          return result.data.toggleCallMedia;
        }),
        catchError((error) => {
          console.error('❌ [CallService] toggleMedia error:', error);
          this.logger.error('Error toggling media:', error);
          return throwError(
            () => new Error('Erreur lors du changement des médias')
          );
        })
      );
  }

  /**
   * Démarre les médias pour un appel accepté
   */
  private async startMediaForCall(
    incomingCall: IncomingCall,
    call: Call
  ): Promise<Call> {
    console.log('🎥 [CallService] Call connected - playing connection sound');

    // Jouer le son de connexion
    this.play('call-connected');

    // Mettre à jour l'état local
    this.activeCall.next(call);
    this.incomingCall.next(null); // Supprimer l'appel entrant

    return call;
  }

  /**
   * Active/désactive la vidéo
   */
  toggleVideo(): boolean {
    this.isVideoEnabled = !this.isVideoEnabled;
    console.log('📹 [CallService] Video toggled:', this.isVideoEnabled);
    return this.isVideoEnabled;
  }

  /**
   * Active/désactive l'audio
   */
  toggleAudio(): boolean {
    this.isAudioEnabled = !this.isAudioEnabled;
    console.log('🎤 [CallService] Audio toggled:', this.isAudioEnabled);
    return this.isAudioEnabled;
  }

  /**
   * Obtient l'état de la vidéo
   */
  getVideoEnabled(): boolean {
    return this.isVideoEnabled;
  }

  /**
   * Obtient l'état de l'audio
   */
  getAudioEnabled(): boolean {
    return this.isAudioEnabled;
  }

  /**
   * Arrête tous les sons
   */
  private stopAllSounds(): void {
    console.log('🔇 [CallService] Stopping all sounds');
    Object.keys(this.sounds).forEach((name) => {
      this.stop(name);
    });
  }

  /**
   * Active les sons après interaction utilisateur (pour contourner les restrictions du navigateur)
   */
  public enableSounds(): void {
    console.log('🔊 [CallService] Enabling sounds after user interaction');
    Object.values(this.sounds).forEach((sound) => {
      if (sound) {
        sound.muted = false;
        sound.volume = 0.7;
        // Jouer et arrêter immédiatement pour "débloquer" l'audio
        sound
          .play()
          .then(() => {
            sound.pause();
            sound.currentTime = 0;
          })
          .catch(() => {
            // Ignorer les erreurs ici
          });
      }
    });
  }

  ngOnDestroy(): void {
    this.stopAllSounds();
  }
}
