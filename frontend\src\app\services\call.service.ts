import { Injectable, On<PERSON><PERSON>roy } from '@angular/core';
import { Apollo } from 'apollo-angular';
import { BehaviorSubject, Observable, throwError, from, of } from 'rxjs';
import { map, catchError, switchMap, tap, retry } from 'rxjs/operators';
import {
  Call,
  CallType,
  CallStatus,
  IncomingCall,
  CallSuccess,
  CallSignal,
} from '../models/message.model';
import {
  INITIATE_CALL_MUTATION,
  ACCEPT_CALL_MUTATION,
  REJECT_CALL_MUTATION,
  END_CALL_MUTATION,
  TOGGLE_CALL_MEDIA_MUTATION,
  INCOMING_CALL_SUBSCRIPTION,
  CALL_STATUS_CHANGED_SUBSCRIPTION,
  CALL_SIGNAL_SUBSCRIPTION,
  SEND_CALL_SIGNAL_MUTATION,
} from '../graphql/message.graphql';
import { LoggerService } from './logger.service';

@Injectable({
  providedIn: 'root',
})
export class CallService implements OnD<PERSON>roy {
  // Observables pour l'état des appels
  private activeCall = new BehaviorSubject<Call | null>(null);
  private incomingCall = new BehaviorSubject<IncomingCall | null>(null);
  private callSignals = new BehaviorSubject<CallSignal | null>(null);

  // Observables publics
  public activeCall$ = this.activeCall.asObservable();
  public incomingCall$ = this.incomingCall.asObservable();
  public callSignals$ = this.callSignals.asObservable();

  // État des médias
  private isAudioEnabled = true;
  private isVideoEnabled = true;

  // Gestion des sons
  private sounds: { [key: string]: HTMLAudioElement } = {};
  private isPlaying: { [key: string]: boolean } = {};

  // WebRTC
  private peerConnection: RTCPeerConnection | null = null;
  private localStream: MediaStream | null = null;
  private remoteStream: MediaStream | null = null;
  private localVideoElement: HTMLVideoElement | null = null;
  private remoteVideoElement: HTMLVideoElement | null = null;
  private currentCallId: string | null = null;
  private webrtcExchangeStarted: boolean = false;

  // Configuration WebRTC
  private readonly rtcConfig: RTCConfiguration = {
    iceServers: [
      { urls: 'stun:stun.l.google.com:19302' },
      { urls: 'stun:stun1.l.google.com:19302' },
      { urls: 'stun:stun2.l.google.com:19302' },
    ],
  };

  constructor(private apollo: Apollo, private logger: LoggerService) {
    this.logger.info('CallService', '🚀 Initializing CallService...');
    this.initializeSounds();
    this.initializeSubscriptions();
    this.initializeWebRTC();
    this.logger.info('CallService', '✅ CallService initialized successfully');
  }

  ngOnDestroy(): void {
    this.logger.info('CallService', '🔄 Destroying CallService...');
    this.cleanupWebRTC();
    this.stopAllSounds();
  }

  /**
   * Initialise les sons
   */
  private initializeSounds(): void {
    this.logger.debug('CallService', 'Initializing sounds...');
    this.createSyntheticSounds();
  }

  /**
   * Crée des sons synthétiques
   */
  private createSyntheticSounds(): void {
    this.logger.debug('CallService', 'Creating synthetic sounds...');

    // Créer des sons synthétiques pour éviter les problèmes de fichiers manquants
    this.createSyntheticSound('ringtone', [440, 554.37], 1.5, true);
    this.createSyntheticSound(
      'call-connected',
      [523.25, 659.25, 783.99],
      0.8,
      false
    );
    this.createSyntheticSound('call-end', [392, 329.63, 261.63], 1.2, false);
  }

  /**
   * Crée un son synthétique
   */
  private createSyntheticSound(
    name: string,
    frequencies: number[],
    duration: number,
    loop: boolean
  ): void {
    try {
      // Créer un contexte audio
      const audioContext = new (window.AudioContext ||
        (window as any).webkitAudioContext)();

      // Créer un buffer audio
      const sampleRate = audioContext.sampleRate;
      const frameCount = sampleRate * duration;
      const buffer = audioContext.createBuffer(1, frameCount, sampleRate);
      const channelData = buffer.getChannelData(0);

      // Générer le son
      for (let i = 0; i < frameCount; i++) {
        let sample = 0;
        frequencies.forEach((freq, index) => {
          const amplitude = 0.3 / frequencies.length;
          const phase = (i / sampleRate) * freq * 2 * Math.PI;
          sample += Math.sin(phase) * amplitude;
        });

        // Appliquer une enveloppe pour éviter les clics
        const envelope = Math.sin((i / frameCount) * Math.PI);
        channelData[i] = sample * envelope;
      }

      // Créer un élément audio factice pour l'interface
      const audio = new Audio();
      audio.loop = loop;

      // Stocker une fonction de lecture personnalisée
      (audio as any).customPlay = () => {
        const source = audioContext.createBufferSource();
        source.buffer = buffer;
        source.loop = loop;
        source.connect(audioContext.destination);
        source.start();

        if (!loop) {
          setTimeout(() => {
            this.isPlaying[name] = false;
          }, duration * 1000);
        }

        return source;
      };

      this.sounds[name] = audio;
      this.isPlaying[name] = false;

      this.logger.debug(
        'CallService',
        `Synthetic sound '${name}' created successfully`
      );
    } catch (error) {
      this.logger.error(
        'CallService',
        `Error creating synthetic sound '${name}':`,
        error
      );
    }
  }

  /**
   * Joue un son
   */
  play(name: string, loop: boolean = false): void {
    try {
      const sound = this.sounds[name];
      if (!sound) {
        this.logger.warn('CallService', `Sound '${name}' not found`);
        return;
      }

      if (!this.isPlaying[name]) {
        if ((sound as any).customPlay) {
          (sound as any).currentSource = (sound as any).customPlay();
          this.isPlaying[name] = true;
          this.logger.debug('CallService', `Playing synthetic sound: ${name}`);
        } else {
          sound.currentTime = 0;
          sound.loop = loop;
          sound.play().catch((error) => {
            this.logger.error(
              'CallService',
              `Error playing sound '${name}':`,
              error
            );
          });
          this.isPlaying[name] = true;
        }
      }
    } catch (error) {
      this.logger.error(
        'CallService',
        `Error in play method for '${name}':`,
        error
      );
    }
  }

  /**
   * Arrête un son
   */
  stop(name: string): void {
    try {
      const sound = this.sounds[name];
      if (!sound) return;

      if (this.isPlaying[name]) {
        if ((sound as any).currentSource) {
          (sound as any).currentSource.stop();
          (sound as any).currentSource = null;
        } else {
          sound.pause();
          sound.currentTime = 0;
        }
        this.isPlaying[name] = false;
        this.logger.debug('CallService', `Stopped sound: ${name}`);
      }
    } catch (error) {
      this.logger.error(
        'CallService',
        `Error stopping sound '${name}':`,
        error
      );
    }
  }

  /**
   * Arrête tous les sons
   */
  stopAllSounds(): void {
    Object.keys(this.sounds).forEach((name) => {
      this.stop(name);
    });
  }

  /**
   * Initialise les subscriptions
   */
  private initializeSubscriptions(): void {
    this.logger.debug('CallService', 'Initializing subscriptions...');
    this.subscribeToIncomingCalls();
    this.subscribeToCallStatusChanges();
    this.subscribeToCallSignals();
  }

  /**
   * S'abonne aux appels entrants
   */
  private subscribeToIncomingCalls(): void {
    this.logger.debug(
      'CallService',
      'Setting up incoming calls subscription...'
    );

    this.apollo
      .subscribe<{ incomingCall: IncomingCall }>({
        query: INCOMING_CALL_SUBSCRIPTION,
        errorPolicy: 'all',
      })
      .subscribe({
        next: ({ data, errors }) => {
          if (data?.incomingCall) {
            this.logger.info(
              'CallService',
              'Incoming call received:',
              data.incomingCall
            );
            this.handleIncomingCall(data.incomingCall);
          }
          if (errors) {
            this.logger.error(
              'CallService',
              'Incoming call subscription errors:',
              errors
            );
          }
        },
        error: (error) => {
          this.logger.error(
            'CallService',
            'Error in incoming call subscription:',
            error
          );
          // Retry subscription after 5 seconds
          setTimeout(() => this.subscribeToIncomingCalls(), 5000);
        },
      });
  }

  /**
   * S'abonne aux changements de statut d'appel
   */
  private subscribeToCallStatusChanges(): void {
    this.logger.debug(
      'CallService',
      'Setting up call status changes subscription...'
    );

    this.apollo
      .subscribe<{ callStatusChanged: Call }>({
        query: CALL_STATUS_CHANGED_SUBSCRIPTION,
        errorPolicy: 'all',
      })
      .subscribe({
        next: ({ data, errors }) => {
          if (data?.callStatusChanged) {
            this.logger.info(
              'CallService',
              'Call status changed:',
              data.callStatusChanged
            );
            this.handleCallStatusChange(data.callStatusChanged);
          }
          if (errors) {
            this.logger.error(
              'CallService',
              'Call status subscription errors:',
              errors
            );
          }
        },
        error: (error) => {
          this.logger.error(
            'CallService',
            'Error in call status subscription:',
            error
          );
          setTimeout(() => this.subscribeToCallStatusChanges(), 5000);
        },
      });
  }

  /**
   * S'abonne aux signaux d'appel
   */
  private subscribeToCallSignals(): void {
    this.logger.debug('CallService', 'Setting up call signals subscription...');

    this.apollo
      .subscribe<{ callSignal: CallSignal }>({
        query: CALL_SIGNAL_SUBSCRIPTION,
        errorPolicy: 'all',
      })
      .subscribe({
        next: ({ data, errors }) => {
          if (data?.callSignal) {
            this.logger.debug(
              'CallService',
              'Call signal received:',
              data.callSignal
            );
            this.handleCallSignal(data.callSignal);
          }
          if (errors) {
            this.logger.error(
              'CallService',
              'Call signal subscription errors:',
              errors
            );
          }
        },
        error: (error) => {
          this.logger.error(
            'CallService',
            'Error in call signal subscription:',
            error
          );
          setTimeout(() => this.subscribeToCallSignals(), 5000);
        },
      });
  }

  /**
   * Initialise WebRTC
   */
  private initializeWebRTC(): void {
    this.logger.debug('CallService', 'Initializing WebRTC...');
    this.createPeerConnection();
  }

  /**
   * Crée une nouvelle PeerConnection
   */
  private createPeerConnection(): void {
    try {
      this.peerConnection = new RTCPeerConnection(this.rtcConfig);
      this.logger.debug('CallService', 'PeerConnection created successfully');

      // Gestion des candidats ICE
      this.peerConnection.onicecandidate = (event) => {
        if (event.candidate && this.currentCallId) {
          this.logger.debug(
            'CallService',
            'ICE candidate generated:',
            event.candidate
          );
          this.sendSignal('ice-candidate', JSON.stringify(event.candidate));
        }
      };

      // Gestion du stream distant
      this.peerConnection.ontrack = (event) => {
        this.logger.info(
          'CallService',
          'Remote track received:',
          event.track.kind
        );
        this.remoteStream = event.streams[0];
        this.attachRemoteStream();
      };

      // Gestion des changements d'état de connexion
      this.peerConnection.onconnectionstatechange = () => {
        this.logger.debug(
          'CallService',
          'Connection state changed:',
          this.peerConnection?.connectionState
        );

        if (this.peerConnection?.connectionState === 'connected') {
          this.logger.info('CallService', '✅ WebRTC connection established');
          this.stop('ringtone');
          this.play('call-connected');
        } else if (this.peerConnection?.connectionState === 'failed') {
          this.logger.error('CallService', '❌ WebRTC connection failed');
          this.handleConnectionFailure();
        }
      };

      // Gestion des changements d'état ICE
      this.peerConnection.oniceconnectionstatechange = () => {
        this.logger.debug(
          'CallService',
          'ICE connection state:',
          this.peerConnection?.iceConnectionState
        );
      };
    } catch (error) {
      this.logger.error('CallService', 'Error creating PeerConnection:', error);
    }
  }

  /**
   * Gère un appel entrant
   */
  private handleIncomingCall(call: IncomingCall): void {
    this.logger.info('CallService', 'Handling incoming call:', call);

    this.incomingCall.next(call);
    this.currentCallId = call.id;
    this.play('ringtone', true);

    // Préparer WebRTC pour l'appel entrant
    this.prepareForIncomingCall(call);
  }

  /**
   * Prépare WebRTC pour un appel entrant
   */
  private async prepareForIncomingCall(call: IncomingCall): Promise<void> {
    try {
      this.logger.debug('CallService', 'Preparing WebRTC for incoming call');

      // Créer une nouvelle PeerConnection si nécessaire
      if (!this.peerConnection) {
        this.createPeerConnection();
      }

      // Obtenir les médias utilisateur
      const stream = await this.getUserMedia(call.type);
      this.addLocalStreamToPeerConnection(stream);
    } catch (error) {
      this.logger.error(
        'CallService',
        'Error preparing for incoming call:',
        error
      );
    }
  }

  /**
   * Gère les changements de statut d'appel
   */
  private handleCallStatusChange(call: Call): void {
    this.logger.info('CallService', 'Handling call status change:', call);

    if (call.id === this.currentCallId) {
      this.activeCall.next(call);

      switch (call.status) {
        case CallStatus.CONNECTED:
          this.stop('ringtone');
          this.play('call-connected');
          break;
        case CallStatus.ENDED:
        case CallStatus.REJECTED:
          this.handleCallEnd();
          break;
      }
    }
  }

  /**
   * Gère les signaux d'appel
   */
  private handleCallSignal(signal: CallSignal): void {
    if (signal.callId !== this.currentCallId) {
      this.logger.debug('CallService', 'Ignoring signal for different call');
      return;
    }

    this.logger.debug('CallService', 'Processing call signal:', signal.type);

    switch (signal.type) {
      case 'offer':
        this.handleRemoteOffer(signal);
        break;
      case 'answer':
        this.handleRemoteAnswer(signal);
        break;
      case 'ice-candidate':
        this.handleRemoteICECandidate(signal);
        break;
      default:
        this.logger.warn('CallService', 'Unknown signal type:', signal.type);
    }
  }

  /**
   * Obtient les médias utilisateur (caméra/micro)
   */
  private async getUserMedia(callType: CallType): Promise<MediaStream> {
    this.logger.info('CallService', `🎥 Getting user media for: ${callType}`);

    const constraints: MediaStreamConstraints = {
      audio: true,
      video: callType === CallType.VIDEO,
    };

    this.logger.debug('CallService', 'Media constraints:', constraints);

    try {
      const stream = await navigator.mediaDevices.getUserMedia(constraints);
      this.logger.info('CallService', '✅ User media obtained successfully');

      this.localStream = stream;

      // Log des détails du stream
      this.logger.debug(
        'CallService',
        `Stream tracks: ${stream.getTracks().length}`
      );
      stream.getTracks().forEach((track) => {
        this.logger.debug(
          'CallService',
          `Track: ${track.kind} - ${track.label}`
        );
      });

      return stream;
    } catch (error) {
      this.logger.error('CallService', '❌ Error getting user media:', error);
      throw new Error("Impossible d'accéder au microphone/caméra");
    }
  }

  // ===== MÉTHODES PUBLIQUES D'APPEL =====

  /**
   * Initie un appel WebRTC
   */
  initiateCall(
    recipientId: string,
    callType: CallType,
    conversationId?: string
  ): Observable<Call> {
    this.logger.info('CallService', '🔄 Initiating WebRTC call:', {
      recipientId,
      callType,
      conversationId,
    });

    if (!recipientId) {
      const error = new Error('Recipient ID is required');
      this.logger.error('CallService', 'initiateCall error:', error);
      return throwError(() => error);
    }

    // Générer un ID unique pour l'appel
    const callId = `call_${Date.now()}_${Math.random()
      .toString(36)
      .substr(2, 9)}`;

    // Utiliser GraphQL pour initier l'appel
    return this.apollo
      .mutate<{ initiateCall: Call }>({
        mutation: INITIATE_CALL_MUTATION,
        variables: {
          recipientId,
          callType: callType,
          callId,
          offer: '{"type":"offer","sdp":"fake-offer"}', // Sera remplacé par une vraie offre WebRTC
          conversationId,
        },
      })
      .pipe(
        map((result) => {
          const call = result.data?.initiateCall;
          if (!call) {
            throw new Error('Failed to initiate call');
          }

          this.logger.info(
            'CallService',
            '✅ Call initiated successfully:',
            call
          );

          // Démarrer la sonnerie
          this.play('ringtone', true);

          // Mettre à jour l'état
          this.activeCall.next(call);
          this.currentCallId = call.id;

          // Démarrer les médias WebRTC
          this.startOutgoingCallMedia(callType);

          return call;
        }),
        catchError((error) => {
          this.logger.error('CallService', 'Error initiating call:', error);

          // Fallback vers version temporaire en cas d'erreur GraphQL
          return this.initiateCallTemporary(
            recipientId,
            callType,
            conversationId
          );
        })
      );
  }

  /**
   * Version temporaire pour les tests sans backend
   */
  private initiateCallTemporary(
    recipientId: string,
    callType: CallType,
    conversationId?: string
  ): Observable<Call> {
    this.logger.info('CallService', '🔄 Using temporary call initiation');

    const fakeCall: Call = {
      id: 'call_' + Date.now(),
      caller: {
        _id: 'current-user',
        id: 'current-user',
        username: 'You',
        email: '<EMAIL>',
        role: 'user',
        isActive: true,
        image: '/assets/images/default-avatar.png',
      },
      recipient: {
        _id: recipientId,
        id: recipientId,
        username: 'Other User',
        email: '<EMAIL>',
        role: 'user',
        isActive: true,
        image: '/assets/images/default-avatar.png',
      },
      type: callType,
      status: CallStatus.RINGING,
      startTime: new Date().toISOString(),
      conversationId: conversationId || '',
    };

    return new Observable<Call>((observer) => {
      setTimeout(() => {
        this.logger.info(
          'CallService',
          '✅ Temporary call initiated:',
          fakeCall
        );

        this.play('ringtone', true);
        this.activeCall.next(fakeCall);
        this.currentCallId = fakeCall.id;

        // Démarrer les médias pour l'appel sortant
        this.startOutgoingCallMedia(callType);

        // Simuler un appel entrant après 3 secondes pour tester l'acceptation
        setTimeout(() => {
          this.simulateIncomingCall(fakeCall);
        }, 3000);

        observer.next(fakeCall);
        observer.complete();
      }, 500);
    });
  }

  /**
   * Accepte un appel entrant
   */
  acceptCall(call: IncomingCall): Observable<Call> {
    this.logger.info('CallService', '✅ Accepting incoming call:', call.id);

    if (!call) {
      const error = new Error('No call to accept');
      this.logger.error('CallService', 'acceptCall error:', error);
      return throwError(() => error);
    }

    return this.apollo
      .mutate<{ acceptCall: Call }>({
        mutation: ACCEPT_CALL_MUTATION,
        variables: { callId: call.id },
      })
      .pipe(
        map((result) => {
          const acceptedCall = result.data?.acceptCall;
          if (!acceptedCall) {
            throw new Error('Failed to accept call');
          }

          this.logger.info(
            'CallService',
            '✅ Call accepted successfully:',
            acceptedCall
          );

          // Arrêter la sonnerie
          this.stop('ringtone');
          this.play('call-connected');

          // Mettre à jour l'état
          this.activeCall.next(acceptedCall);
          this.incomingCall.next(null);

          return acceptedCall;
        }),
        catchError((error) => {
          this.logger.error('CallService', 'Error accepting call:', error);
          return throwError(() => new Error('Failed to accept call'));
        })
      );
  }

  /**
   * Rejette un appel entrant
   */
  rejectCall(callId: string, reason?: string): Observable<CallSuccess> {
    this.logger.info('CallService', '❌ Rejecting call:', callId);

    return this.apollo
      .mutate<{ rejectCall: CallSuccess }>({
        mutation: REJECT_CALL_MUTATION,
        variables: { callId, reason: reason || 'User rejected' },
      })
      .pipe(
        map((result) => {
          const success = result.data?.rejectCall;
          if (!success) {
            throw new Error('Failed to reject call');
          }

          this.logger.info(
            'CallService',
            '✅ Call rejected successfully:',
            success
          );

          // Arrêter la sonnerie et jouer le son de fin
          this.stop('ringtone');
          this.play('call-end');

          // Nettoyer l'état
          this.incomingCall.next(null);
          this.activeCall.next(null);
          this.cleanupWebRTC();

          return success;
        }),
        catchError((error) => {
          this.logger.error('CallService', 'Error rejecting call:', error);
          return throwError(() => new Error('Failed to reject call'));
        })
      );
  }

  /**
   * Termine un appel actif
   */
  endCall(callId: string): Observable<CallSuccess> {
    this.logger.info('CallService', '🔚 Ending call:', callId);

    return this.apollo
      .mutate<{ endCall: CallSuccess }>({
        mutation: END_CALL_MUTATION,
        variables: { callId },
      })
      .pipe(
        map((result) => {
          const success = result.data?.endCall;
          if (!success) {
            throw new Error('Failed to end call');
          }

          this.logger.info(
            'CallService',
            '✅ Call ended successfully:',
            success
          );

          // Jouer le son de fin
          this.play('call-end');

          // Nettoyer l'état
          this.handleCallEnd();

          return success;
        }),
        catchError((error) => {
          this.logger.error('CallService', 'Error ending call:', error);

          // Fallback: nettoyer localement même en cas d'erreur
          this.handleCallEnd();

          return of({
            success: true,
            message: 'Call ended locally (server error)',
          });
        })
      );
  }

  // ===== MÉTHODES WEBRTC PRIVÉES =====

  /**
   * Démarre les médias pour un appel sortant
   */
  private startOutgoingCallMedia(callType: CallType): void {
    this.logger.info('CallService', '🎥 Starting outgoing call media');

    this.getUserMedia(callType)
      .then((stream) => {
        this.addLocalStreamToPeerConnection(stream);
        this.attachLocalStream();

        // Créer une offre WebRTC
        return this.createOffer();
      })
      .then((offer) => {
        this.logger.info('CallService', '✅ Offer created for outgoing call');
        // Dans un vrai scénario, on enverrait cette offre au destinataire
        this.sendSignal('offer', JSON.stringify(offer));
      })
      .catch((error) => {
        this.logger.error(
          'CallService',
          '❌ Error starting outgoing call media:',
          error
        );
      });
  }

  /**
   * Ajoute le stream local à la PeerConnection
   */
  private addLocalStreamToPeerConnection(stream: MediaStream): void {
    if (!this.peerConnection) {
      this.logger.error('CallService', 'No PeerConnection available');
      return;
    }

    try {
      stream.getTracks().forEach((track) => {
        this.peerConnection!.addTrack(track, stream);
        this.logger.debug(
          'CallService',
          `Added ${track.kind} track to PeerConnection`
        );
      });
    } catch (error) {
      this.logger.error(
        'CallService',
        'Error adding stream to PeerConnection:',
        error
      );
    }
  }

  /**
   * Attache le stream local à l'élément vidéo
   */
  private attachLocalStream(): void {
    if (this.localVideoElement && this.localStream) {
      this.localVideoElement.srcObject = this.localStream;
      this.logger.debug(
        'CallService',
        'Local stream attached to video element'
      );
    }
  }

  /**
   * Attache le stream distant à l'élément vidéo
   */
  private attachRemoteStream(): void {
    if (this.remoteVideoElement && this.remoteStream) {
      this.remoteVideoElement.srcObject = this.remoteStream;
      this.logger.debug(
        'CallService',
        'Remote stream attached to video element'
      );
    }
  }

  /**
   * Crée une offre WebRTC
   */
  private async createOffer(): Promise<RTCSessionDescriptionInit> {
    if (!this.peerConnection) {
      throw new Error('PeerConnection not initialized');
    }

    try {
      const offer = await this.peerConnection.createOffer({
        offerToReceiveAudio: true,
        offerToReceiveVideo: true,
      });

      await this.peerConnection.setLocalDescription(offer);
      this.logger.info(
        'CallService',
        '✅ Offer created and set as local description'
      );

      return offer;
    } catch (error) {
      this.logger.error('CallService', '❌ Error creating offer:', error);
      throw error;
    }
  }

  /**
   * Crée une réponse WebRTC
   */
  private async createAnswer(
    offer: RTCSessionDescriptionInit
  ): Promise<RTCSessionDescriptionInit> {
    if (!this.peerConnection) {
      throw new Error('PeerConnection not initialized');
    }

    try {
      await this.peerConnection.setRemoteDescription(offer);
      this.logger.info('CallService', '✅ Remote description set');

      const answer = await this.peerConnection.createAnswer();
      await this.peerConnection.setLocalDescription(answer);
      this.logger.info(
        'CallService',
        '✅ Answer created and set as local description'
      );

      return answer;
    } catch (error) {
      this.logger.error('CallService', '❌ Error creating answer:', error);
      throw error;
    }
  }

  /**
   * Gère une offre distante
   */
  private async handleRemoteOffer(signal: CallSignal): Promise<void> {
    try {
      const offer = JSON.parse(signal.data);
      this.logger.info('CallService', '📥 Handling remote offer:', offer);

      // Obtenir les médias utilisateur
      const stream = await this.getUserMedia(CallType.AUDIO);
      this.addLocalStreamToPeerConnection(stream);

      // Créer et envoyer la réponse
      const answer = await this.createAnswer(offer);
      this.sendSignal('answer', JSON.stringify(answer));

      this.logger.info('CallService', '✅ Remote offer handled successfully');
    } catch (error) {
      this.logger.error(
        'CallService',
        '❌ Error handling remote offer:',
        error
      );
    }
  }

  /**
   * Gère une réponse distante
   */
  private async handleRemoteAnswer(signal: CallSignal): Promise<void> {
    try {
      const answer = JSON.parse(signal.data);
      this.logger.info('CallService', '📥 Handling remote answer:', answer);

      if (this.peerConnection) {
        await this.peerConnection.setRemoteDescription(answer);
        this.logger.info(
          'CallService',
          '✅ Remote answer handled successfully'
        );
      }
    } catch (error) {
      this.logger.error(
        'CallService',
        '❌ Error handling remote answer:',
        error
      );
    }
  }

  /**
   * Gère un candidat ICE distant
   */
  private async handleRemoteICECandidate(signal: CallSignal): Promise<void> {
    try {
      const candidate = JSON.parse(signal.data);
      this.logger.debug(
        'CallService',
        '📥 Handling remote ICE candidate:',
        candidate
      );

      if (this.peerConnection && candidate) {
        await this.peerConnection.addIceCandidate(
          new RTCIceCandidate(candidate)
        );
        this.logger.debug(
          'CallService',
          '✅ Remote ICE candidate added successfully'
        );
      }
    } catch (error) {
      this.logger.error(
        'CallService',
        '❌ Error handling ICE candidate:',
        error
      );
    }
  }

  /**
   * Envoie un signal WebRTC
   */
  private sendSignal(type: string, data: string): void {
    if (!this.currentCallId) {
      this.logger.error('CallService', 'No current call ID for signal');
      return;
    }

    this.logger.debug('CallService', `📤 Sending signal: ${type}`);

    // Utiliser GraphQL pour envoyer le signal
    this.apollo
      .mutate({
        mutation: SEND_CALL_SIGNAL_MUTATION,
        variables: {
          callId: this.currentCallId,
          signalType: type,
          signalData: data,
        },
      })
      .subscribe({
        next: (result) => {
          this.logger.debug('CallService', '✅ Signal sent successfully');
        },
        error: (error) => {
          this.logger.error('CallService', '❌ Error sending signal:', error);

          // Fallback: simulation locale pour les tests
          setTimeout(() => {
            this.handleCallSignal({
              callId: this.currentCallId!,
              senderId: 'other-user',
              type,
              data,
              timestamp: new Date().toISOString(),
            });
          }, 100);
        },
      });
  }

  // ===== MÉTHODES UTILITAIRES =====

  /**
   * Configure les éléments vidéo
   */
  setVideoElements(
    localVideo: HTMLVideoElement,
    remoteVideo: HTMLVideoElement
  ): void {
    this.localVideoElement = localVideo;
    this.remoteVideoElement = remoteVideo;
    this.logger.debug('CallService', 'Video elements configured');
  }

  /**
   * Bascule l'audio
   */
  toggleAudio(): boolean {
    this.isAudioEnabled = !this.isAudioEnabled;

    if (this.localStream) {
      this.localStream.getAudioTracks().forEach((track) => {
        track.enabled = this.isAudioEnabled;
      });
    }

    this.logger.info(
      'CallService',
      `Audio ${this.isAudioEnabled ? 'enabled' : 'disabled'}`
    );
    return this.isAudioEnabled;
  }

  /**
   * Bascule la vidéo
   */
  toggleVideo(): boolean {
    this.isVideoEnabled = !this.isVideoEnabled;

    if (this.localStream) {
      this.localStream.getVideoTracks().forEach((track) => {
        track.enabled = this.isVideoEnabled;
      });
    }

    this.logger.info(
      'CallService',
      `Video ${this.isVideoEnabled ? 'enabled' : 'disabled'}`
    );
    return this.isVideoEnabled;
  }

  /**
   * Bascule les médias via GraphQL
   */
  toggleMedia(
    callId: string,
    enableVideo?: boolean,
    enableAudio?: boolean
  ): Observable<CallSuccess> {
    return this.apollo
      .mutate<{ toggleCallMedia: CallSuccess }>({
        mutation: TOGGLE_CALL_MEDIA_MUTATION,
        variables: { callId, video: enableVideo, audio: enableAudio },
      })
      .pipe(
        map((result) => {
          return result.data!.toggleCallMedia;
        }),
        catchError((error) => {
          this.logger.error('CallService', 'Error toggling media:', error);
          return throwError(
            () => new Error('Erreur lors du changement des médias')
          );
        })
      );
  }

  /**
   * Gère la fin d'appel
   */
  private handleCallEnd(): void {
    this.logger.info('CallService', '🔚 Handling call end');

    this.stopAllSounds();
    this.activeCall.next(null);
    this.incomingCall.next(null);
    this.currentCallId = null;
    this.cleanupWebRTC();
  }

  /**
   * Gère l'échec de connexion WebRTC
   */
  private handleConnectionFailure(): void {
    this.logger.error('CallService', '❌ WebRTC connection failed');

    this.play('call-end');
    this.handleCallEnd();
  }

  /**
   * Nettoie les ressources WebRTC
   */
  private cleanupWebRTC(): void {
    this.logger.debug('CallService', '🧹 Cleaning up WebRTC resources');

    if (this.localStream) {
      this.localStream.getTracks().forEach((track) => {
        track.stop();
        this.logger.debug('CallService', `Stopped ${track.kind} track`);
      });
      this.localStream = null;
    }

    if (this.peerConnection) {
      this.peerConnection.close();
      this.peerConnection = null;
      this.logger.debug('CallService', 'PeerConnection closed');
    }

    this.remoteStream = null;
    this.webrtcExchangeStarted = false;

    // Réinitialiser les éléments vidéo
    if (this.localVideoElement) {
      this.localVideoElement.srcObject = null;
    }
    if (this.remoteVideoElement) {
      this.remoteVideoElement.srcObject = null;
    }
  }

  /**
   * Simule un appel entrant (pour les tests)
   */
  private simulateIncomingCall(originalCall: Call): void {
    const incomingCall: IncomingCall = {
      id: originalCall.id,
      caller: originalCall.recipient, // Inverser caller/recipient
      type: originalCall.type,
      conversationId: originalCall.conversationId,
      offer: '{"type":"offer","sdp":"fake-offer"}',
      timestamp: new Date().toISOString(),
    };

    this.logger.info(
      'CallService',
      '📞 Simulating incoming call:',
      incomingCall
    );

    this.stop('ringtone'); // Arrêter la sonnerie sortante
    this.incomingCall.next(incomingCall);
    this.activeCall.next(null); // Nettoyer l'appel sortant

    // Jouer la sonnerie d'appel entrant
    this.play('ringtone', true);
  }

  /**
   * Active les sons après interaction utilisateur
   */
  enableSounds(): void {
    this.logger.info('CallService', 'Sounds enabled after user interaction');
  }

  // ===== GETTERS =====

  /**
   * Obtient l'état audio actuel
   */
  get audioEnabled(): boolean {
    return this.isAudioEnabled;
  }

  /**
   * Obtient l'état vidéo actuel
   */
  get videoEnabled(): boolean {
    return this.isVideoEnabled;
  }

  /**
   * Obtient l'appel actuel
   */
  get currentCall(): Call | null {
    return this.activeCall.value;
  }

  /**
   * Obtient l'appel entrant actuel
   */
  get currentIncomingCall(): IncomingCall | null {
    return this.incomingCall.value;
  }
}
