import { Injectable, <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';
import { Apollo } from 'apollo-angular';
import { BehaviorSubject, Observable, throwError, from } from 'rxjs';
import { map, catchError, switchMap } from 'rxjs/operators';
import {
  Call,
  CallType,
  CallStatus,
  IncomingCall,
  CallSuccess,
} from '../models/message.model';
import {
  INITIATE_CALL_MUTATION,
  ACCEPT_CALL_MUTATION,
  REJECT_CALL_MUTATION,
  END_CALL_MUTATION,
  TOGGLE_CALL_MEDIA_MUTATION,
  INCOMING_CALL_SUBSCRIPTION,
  CALL_STATUS_CHANGED_SUBSCRIPTION,
  CALL_SIGNAL_SUBSCRIPTION,
} from '../graphql/message.graphql';
import { LoggerService } from './logger.service';

@Injectable({
  providedIn: 'root',
})
export class CallService implements OnDestroy {
  // État des appels
  private activeCall = new BehaviorSubject<Call | null>(null);
  private incomingCall = new BehaviorSubject<IncomingCall | null>(null);

  // Observables publics
  public activeCall$ = this.activeCall.asObservable();
  public incomingCall$ = this.incomingCall.asObservable();

  // Propriétés pour la gestion des sons
  private sounds: { [key: string]: HTMLAudioElement } = {};
  private isPlaying: { [key: string]: boolean } = {};

  // États simples pour les médias
  private isVideoEnabled = true;
  private isAudioEnabled = true;

  // WebRTC
  private peerConnection: RTCPeerConnection | null = null;
  private localStream: MediaStream | null = null;
  private remoteStream: MediaStream | null = null;
  private localVideoElement: HTMLVideoElement | null = null;
  private remoteVideoElement: HTMLVideoElement | null = null;
  private currentCallId: string | null = null;
  private webrtcExchangeStarted: boolean = false;

  constructor(private apollo: Apollo, private logger: LoggerService) {
    this.initializeSounds();
    this.initializeSubscriptions();
    this.initializeWebRTC();
  }

  /**
   * Initialise les sons
   */
  private initializeSounds(): void {
    this.createSyntheticSounds();
  }

  /**
   * Crée des sons synthétiques
   */
  private createSyntheticSounds(): void {
    try {
      const audioContext = new (window.AudioContext ||
        (window as any).webkitAudioContext)();
      this.sounds['ringtone'] = this.createRingtoneSound(audioContext);
      this.sounds['call-connected'] = this.createConnectedSound(audioContext);
      this.sounds['call-end'] = this.createEndSound(audioContext);
      this.sounds['notification'] = this.createNotificationSound(audioContext);
    } catch (error) {
      console.warn('Could not create synthetic sounds:', error);
    }
  }

  /**
   * Crée une sonnerie
   */
  private createRingtoneSound(audioContext: AudioContext): HTMLAudioElement {
    const audio = new Audio();
    audio.volume = 0.5;
    let isPlaying = false;
    let timeoutIds: any[] = [];

    (audio as any).playSynthetic = () => {
      if (isPlaying) return Promise.resolve();
      isPlaying = true;

      const playMelody = () => {
        if (!isPlaying) return;
        const melody = [
          { freq: 659.25, duration: 0.125 },
          { freq: 587.33, duration: 0.125 },
          { freq: 739.99, duration: 0.25 },
          { freq: 783.99, duration: 0.25 },
        ];

        let currentTime = audioContext.currentTime;
        melody.forEach((note) => {
          if (!isPlaying) return;
          const oscillator = audioContext.createOscillator();
          const gainNode = audioContext.createGain();
          oscillator.connect(gainNode);
          gainNode.connect(audioContext.destination);
          oscillator.frequency.value = note.freq;
          oscillator.type = 'square';
          gainNode.gain.setValueAtTime(0, currentTime);
          gainNode.gain.linearRampToValueAtTime(0.3, currentTime + 0.02);
          gainNode.gain.exponentialRampToValueAtTime(
            0.01,
            currentTime + note.duration
          );
          oscillator.start(currentTime);
          oscillator.stop(currentTime + note.duration);
          currentTime += note.duration + 0.05;
        });

        const timeoutId = setTimeout(() => {
          if (isPlaying) playMelody();
        }, (currentTime - audioContext.currentTime + 0.8) * 1000);
        timeoutIds.push(timeoutId);
      };

      playMelody();
      return Promise.resolve();
    };

    (audio as any).stopSynthetic = () => {
      isPlaying = false;
      timeoutIds.forEach((id) => clearTimeout(id));
      timeoutIds = [];
    };

    return audio;
  }

  /**
   * Crée un son de connexion
   */
  private createConnectedSound(audioContext: AudioContext): HTMLAudioElement {
    const audio = new Audio();
    audio.volume = 0.5;

    (audio as any).playSynthetic = () => {
      const melody = [
        { freq: 523.25, duration: 0.15 },
        { freq: 659.25, duration: 0.15 },
        { freq: 783.99, duration: 0.15 },
        { freq: 1046.5, duration: 0.4 },
      ];

      let currentTime = audioContext.currentTime;
      melody.forEach((note) => {
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();
        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);
        oscillator.frequency.value = note.freq;
        oscillator.type = 'triangle';
        gainNode.gain.setValueAtTime(0, currentTime);
        gainNode.gain.linearRampToValueAtTime(0.25, currentTime + 0.02);
        gainNode.gain.exponentialRampToValueAtTime(
          0.01,
          currentTime + note.duration
        );
        oscillator.start(currentTime);
        oscillator.stop(currentTime + note.duration);
        currentTime += note.duration * 0.8;
      });

      return Promise.resolve();
    };

    return audio;
  }

  /**
   * Crée un son de fin d'appel
   */
  private createEndSound(audioContext: AudioContext): HTMLAudioElement {
    const audio = new Audio();
    audio.volume = 0.4;

    (audio as any).playSynthetic = () => {
      const melody = [
        { freq: 783.99, duration: 0.2 },
        { freq: 659.25, duration: 0.2 },
        { freq: 523.25, duration: 0.2 },
        { freq: 392.0, duration: 0.4 },
      ];

      let currentTime = audioContext.currentTime;
      melody.forEach((note) => {
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();
        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);
        oscillator.frequency.value = note.freq;
        oscillator.type = 'sine';
        gainNode.gain.setValueAtTime(0, currentTime);
        gainNode.gain.linearRampToValueAtTime(0.2, currentTime + 0.05);
        gainNode.gain.exponentialRampToValueAtTime(
          0.01,
          currentTime + note.duration
        );
        oscillator.start(currentTime);
        oscillator.stop(currentTime + note.duration);
        currentTime += note.duration * 0.9;
      });

      return Promise.resolve();
    };

    return audio;
  }

  /**
   * Crée un son de notification
   */
  private createNotificationSound(
    audioContext: AudioContext
  ): HTMLAudioElement {
    const audio = new Audio();
    audio.volume = 0.6;

    (audio as any).playSynthetic = () => {
      const notes = [
        { freq: 523.25, duration: 0.15, delay: 0 },
        { freq: 783.99, duration: 0.25, delay: 0.2 },
      ];

      notes.forEach((note) => {
        setTimeout(() => {
          const oscillator = audioContext.createOscillator();
          const gainNode = audioContext.createGain();
          oscillator.connect(gainNode);
          gainNode.connect(audioContext.destination);
          oscillator.frequency.value = note.freq;
          oscillator.type = 'triangle';
          const startTime = audioContext.currentTime;
          gainNode.gain.setValueAtTime(0, startTime);
          gainNode.gain.linearRampToValueAtTime(0.4, startTime + 0.02);
          gainNode.gain.exponentialRampToValueAtTime(
            0.01,
            startTime + note.duration
          );
          oscillator.start(startTime);
          oscillator.stop(startTime + note.duration);
        }, note.delay * 1000);
      });

      return Promise.resolve();
    };

    return audio;
  }

  /**
   * Joue un son
   */
  private play(soundName: string, loop: boolean = false): void {
    const sound = this.sounds[soundName];
    if (!sound) return;

    this.isPlaying[soundName] = true;

    if ((sound as any).playSynthetic) {
      (sound as any).playSynthetic();
      if (loop) {
        const interval = setInterval(() => {
          if (this.isPlaying[soundName]) {
            (sound as any).playSynthetic();
          } else {
            clearInterval(interval);
          }
        }, 3000);
      }
    }
  }

  /**
   * Arrête un son
   */
  private stop(soundName: string): void {
    this.isPlaying[soundName] = false;
    const sound = this.sounds[soundName];
    if (sound && (sound as any).stopSynthetic) {
      (sound as any).stopSynthetic();
    }
  }

  /**
   * Arrête tous les sons
   */
  private stopAllSounds(): void {
    Object.keys(this.sounds).forEach((name) => {
      this.stop(name);
    });
  }

  /**
   * Initialise les subscriptions
   */
  private initializeSubscriptions(): void {
    setTimeout(() => {
      this.subscribeToIncomingCalls();
      this.subscribeToCallStatusChanges();
      this.subscribeToCallSignals();
    }, 1000);
  }

  /**
   * S'abonne aux appels entrants
   */
  private subscribeToIncomingCalls(): void {
    this.apollo
      .subscribe<{ incomingCall: IncomingCall }>({
        query: INCOMING_CALL_SUBSCRIPTION,
        errorPolicy: 'all',
      })
      .subscribe({
        next: ({ data, errors }) => {
          if (data?.incomingCall) {
            this.handleIncomingCall(data.incomingCall);
          }
        },
        error: (error) => {
          console.error('Error in incoming call subscription:', error);
          setTimeout(() => this.subscribeToIncomingCalls(), 5000);
        },
      });
  }

  /**
   * S'abonne aux changements de statut d'appel
   */
  private subscribeToCallStatusChanges(): void {
    this.apollo
      .subscribe<{ callStatusChanged: Call }>({
        query: CALL_STATUS_CHANGED_SUBSCRIPTION,
        errorPolicy: 'all',
      })
      .subscribe({
        next: ({ data, errors }) => {
          if (data?.callStatusChanged) {
            this.handleCallStatusChange(data.callStatusChanged);
          }
        },
        error: (error) => {
          console.error('Error in call status subscription:', error);
          setTimeout(() => this.subscribeToCallStatusChanges(), 5000);
        },
      });
  }

  /**
   * S'abonne aux signaux d'appel
   */
  private subscribeToCallSignals(): void {
    this.apollo
      .subscribe<{ callSignal: any }>({
        query: CALL_SIGNAL_SUBSCRIPTION,
        errorPolicy: 'all',
      })
      .subscribe({
        next: ({ data, errors }) => {
          if (data?.callSignal) {
            this.handleCallSignal(data.callSignal);
          }
        },
        error: (error) => {
          console.error('Error in call signal subscription:', error);
          setTimeout(() => this.subscribeToCallSignals(), 5000);
        },
      });
  }

  /**
   * Gère un appel entrant
   */
  private handleIncomingCall(call: IncomingCall): void {
    this.incomingCall.next(call);
    this.play('ringtone', true);
  }

  /**
   * Gère les changements de statut d'appel
   */
  private handleCallStatusChange(call: Call): void {
    switch (call.status) {
      case CallStatus.CONNECTED:
        this.stop('ringtone');
        this.play('call-connected');
        this.activeCall.next(call);
        this.incomingCall.next(null);
        break;
      case CallStatus.ENDED:
        this.play('call-end');
        this.activeCall.next(null);
        this.incomingCall.next(null);
        this.cleanupWebRTC();
        break;
      case CallStatus.REJECTED:
        this.stop('ringtone');
        this.play('call-end');
        this.activeCall.next(null);
        this.incomingCall.next(null);
        break;
    }
  }

  /**
   * Gère les signaux d'appel
   */
  private handleCallSignal(signal: any): void {
    if (!this.peerConnection) return;

    switch (signal.type) {
      case 'offer':
        this.handleRemoteOffer(signal);
        break;
      case 'answer':
        this.handleRemoteAnswer(signal);
        break;
      case 'ice-candidate':
        this.handleRemoteICECandidate(signal);
        break;
    }
  }

  /**
   * Initialise WebRTC
   */
  private initializeWebRTC(): void {
    const configuration: RTCConfiguration = {
      iceServers: [
        { urls: 'stun:stun.l.google.com:19302' },
        { urls: 'stun:stun1.l.google.com:19302' },
      ],
    };

    try {
      this.peerConnection = new RTCPeerConnection(configuration);
      this.setupPeerConnectionEvents();
    } catch (error) {
      console.error('Failed to initialize WebRTC:', error);
    }
  }

  /**
   * Configure les événements de la PeerConnection
   */
  private setupPeerConnectionEvents(): void {
    if (!this.peerConnection) return;

    this.peerConnection.ontrack = (event) => {
      this.remoteStream = event.streams[0];
      this.attachRemoteStream();
    };

    this.peerConnection.onicecandidate = (event) => {
      if (event.candidate) {
        this.sendSignal('ice-candidate', JSON.stringify(event.candidate));
      }
    };
  }

  /**
   * Attache le stream distant
   */
  private attachRemoteStream(): void {
    if (this.remoteStream && this.remoteVideoElement) {
      this.remoteVideoElement.srcObject = this.remoteStream;
      this.remoteVideoElement.muted = false;
      this.remoteVideoElement.volume = 1;
      this.remoteVideoElement.play();
    }

    // Attacher à tous les éléments vidéo disponibles
    const videos = document.querySelectorAll('video');
    for (let i = 1; i < videos.length; i++) {
      if (this.remoteStream && !videos[i].srcObject) {
        videos[i].srcObject = this.remoteStream;
        videos[i].muted = false;
        videos[i].volume = 1;
        videos[i].play();
        break;
      }
    }
  }

  /**
   * Initie un appel - VERSION TEMPORAIRE SANS BACKEND
   */
  initiateCall(
    recipientId: string,
    callType: CallType,
    conversationId?: string
  ): Observable<Call> {
    console.log('🔄 [CallService] Initiating call (temporary version):', {
      recipientId,
      callType,
      conversationId,
    });

    // Créer un appel factice pour les tests
    const fakeCall: Call = {
      id: 'call_' + Date.now(),
      caller: {
        id: 'current-user',
        username: 'You',
        image: '/assets/images/default-avatar.png',
      },
      recipient: {
        id: recipientId,
        username: 'Other User',
        image: '/assets/images/default-avatar.png',
      },
      type: callType,
      status: CallStatus.RINGING,
      startTime: new Date().toISOString(),
      conversationId: conversationId || '',
    };

    // Simuler un délai réseau
    return new Observable<Call>((observer) => {
      setTimeout(() => {
        console.log('✅ [CallService] Fake call created:', fakeCall);

        this.activeCall.next(fakeCall);
        this.currentCallId = fakeCall.id;

        // Jouer la sonnerie
        this.play('ringtone', true);

        // Simuler un appel entrant après 3 secondes pour tester l'acceptation
        setTimeout(() => {
          this.simulateIncomingCall(fakeCall);
        }, 3000);

        observer.next(fakeCall);
        observer.complete();
      }, 500);
    });

    // VERSION AVEC BACKEND (à réactiver quand le backend sera prêt)
    /*
    const callId = 'call_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);

    return this.apollo
      .mutate<{ initiateCall: Call }>({
        mutation: INITIATE_CALL_MUTATION,
        variables: {
          recipientId,
          callType,
          callId,
          offer: '',
          conversationId,
        },
      })
      .pipe(
        map((result) => {
          if (!result.data?.initiateCall) {
            throw new Error('No call data received from server');
          }
          const call = result.data.initiateCall;
          this.activeCall.next(call);
          this.currentCallId = call.id;
          return call;
        }),
        catchError((error) => {
          console.error('Error initiating call:', error);
          return throwError(() => new Error("Erreur lors de l'initiation de l'appel: " + error.message));
        })
      );
    */
  }

  /**
   * Accepte un appel - VERSION TEMPORAIRE
   */
  acceptCall(incomingCall: IncomingCall): Observable<Call> {
    console.log(
      '🔄 [CallService] Accepting call (temporary version):',
      incomingCall
    );

    // Créer un appel accepté factice
    const acceptedCall: Call = {
      id: incomingCall.id,
      caller: incomingCall.caller,
      recipient: {
        id: 'current-user',
        username: 'You',
        image: '/assets/images/default-avatar.png',
      },
      type: incomingCall.type,
      status: CallStatus.CONNECTED,
      startTime: new Date().toISOString(),
      conversationId: incomingCall.conversationId || '',
    };

    return new Observable<Call>((observer) => {
      setTimeout(() => {
        console.log('✅ [CallService] Call accepted (fake):', acceptedCall);

        this.stop('ringtone');
        this.play('call-connected');
        this.activeCall.next(acceptedCall);
        this.incomingCall.next(null);

        observer.next(acceptedCall);
        observer.complete();
      }, 300);
    });
  }

  /**
   * Rejette un appel
   */
  rejectCall(callId: string, reason?: string): Observable<CallSuccess> {
    return this.apollo
      .mutate<{ rejectCall: CallSuccess }>({
        mutation: REJECT_CALL_MUTATION,
        variables: { callId, reason: reason || 'User rejected' },
      })
      .pipe(
        map((result) => {
          this.stop('ringtone');
          this.incomingCall.next(null);
          this.activeCall.next(null);
          return result.data!.rejectCall;
        }),
        catchError((error) => {
          console.error('Error rejecting call:', error);
          return throwError(() => new Error("Erreur lors du rejet de l'appel"));
        })
      );
  }

  /**
   * Termine un appel
   */
  endCall(callId: string): Observable<CallSuccess> {
    return this.apollo
      .mutate<{ endCall: CallSuccess }>({
        mutation: END_CALL_MUTATION,
        variables: { callId, feedback: null },
      })
      .pipe(
        map((result) => {
          this.play('call-end');
          this.activeCall.next(null);
          this.incomingCall.next(null);
          this.cleanupWebRTC();
          return result.data!.endCall;
        }),
        catchError((error) => {
          console.error('Error ending call:', error);
          return throwError(
            () => new Error("Erreur lors de la fin de l'appel")
          );
        })
      );
  }

  /**
   * Configure les éléments vidéo
   */
  setVideoElements(
    localVideo: HTMLVideoElement,
    remoteVideo: HTMLVideoElement
  ): void {
    this.localVideoElement = localVideo;
    this.remoteVideoElement = remoteVideo;
  }

  /**
   * Envoie un signal
   */
  private sendSignal(type: string, data: string): void {
    // Simulation pour les tests
    setTimeout(() => {
      this.handleCallSignal({
        callId: this.currentCallId,
        senderId: 'other-user',
        type,
        data,
      });
    }, 100);
  }

  /**
   * Gère une offre distante
   */
  private async handleRemoteOffer(signal: any): Promise<void> {
    // Implémentation simplifiée
  }

  /**
   * Gère une réponse distante
   */
  private async handleRemoteAnswer(signal: any): Promise<void> {
    // Implémentation simplifiée
  }

  /**
   * Gère un candidat ICE distant
   */
  private async handleRemoteICECandidate(signal: any): Promise<void> {
    // Implémentation simplifiée
  }

  /**
   * Nettoie les ressources WebRTC
   */
  private cleanupWebRTC(): void {
    if (this.localStream) {
      this.localStream.getTracks().forEach((track) => track.stop());
      this.localStream = null;
    }

    if (this.peerConnection) {
      this.peerConnection.close();
      this.peerConnection = null;
    }

    this.remoteStream = null;
  }

  /**
   * Active les sons après interaction utilisateur
   */
  enableSounds(): void {
    console.log('Sounds enabled after user interaction');
  }

  /**
   * Bascule l'audio
   */
  toggleAudio(): boolean {
    this.isAudioEnabled = !this.isAudioEnabled;
    return this.isAudioEnabled;
  }

  /**
   * Bascule la vidéo
   */
  toggleVideo(): boolean {
    this.isVideoEnabled = !this.isVideoEnabled;
    return this.isVideoEnabled;
  }

  /**
   * Bascule les médias
   */
  toggleMedia(
    callId: string,
    enableVideo?: boolean,
    enableAudio?: boolean
  ): Observable<CallSuccess> {
    return this.apollo
      .mutate<{ toggleCallMedia: CallSuccess }>({
        mutation: TOGGLE_CALL_MEDIA_MUTATION,
        variables: { callId, video: enableVideo, audio: enableAudio },
      })
      .pipe(
        map((result) => {
          return result.data!.toggleCallMedia;
        }),
        catchError((error) => {
          console.error('Error toggling media:', error);
          return throwError(
            () => new Error('Erreur lors du changement des médias')
          );
        })
      );
  }

  /**
   * Simule un appel entrant (pour les tests)
   */
  private simulateIncomingCall(originalCall: Call): void {
    const incomingCall: IncomingCall = {
      id: originalCall.id,
      caller: originalCall.recipient, // Inverser caller/recipient
      type: originalCall.type,
      conversationId: originalCall.conversationId,
      offer: '{"type":"offer","sdp":"fake-offer"}',
      timestamp: new Date().toISOString(),
    };

    console.log('📞 [CallService] Simulating incoming call:', incomingCall);

    this.stop('ringtone'); // Arrêter la sonnerie sortante
    this.incomingCall.next(incomingCall);
    this.activeCall.next(null); // Nettoyer l'appel sortant

    // Jouer la sonnerie d'appel entrant
    this.play('ringtone', true);
  }

  ngOnDestroy(): void {
    this.stopAllSounds();
    this.cleanupWebRTC();
  }
}
