import { Injectable, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { Apollo } from 'apollo-angular';
import { BehaviorSubject, Observable, throwError, of, from } from 'rxjs';
import { map, catchError, switchMap } from 'rxjs/operators';
import {
  Call,
  CallType,
  CallStatus,
  IncomingCall,
  CallSuccess,
} from '../models/message.model';
import {
  INITIATE_CALL_MUTATION,
  ACCEPT_CALL_MUTATION,
  REJECT_CALL_MUTATION,
  END_CALL_MUTATION,
  TOGGLE_CALL_MEDIA_MUTATION,
  INCOMING_CALL_SUBSCRIPTION,
  CALL_STATUS_CHANGED_SUBSCRIPTION,
  CALL_SIGNAL_SUBSCRIPTION,
} from '../graphql/message.graphql';
import { LoggerService } from './logger.service';

@Injectable({
  providedIn: 'root',
})
export class CallService implements OnDestroy {
  // État des appels
  private activeCall = new BehaviorSubject<Call | null>(null);
  private incomingCall = new BehaviorSubject<IncomingCall | null>(null);

  // Observables publics
  public activeCall$ = this.activeCall.asObservable();
  public incomingCall$ = this.incomingCall.asObservable();

  // Propriétés pour la gestion des sons
  private sounds: { [key: string]: HTMLAudioElement } = {};
  private isPlaying: { [key: string]: boolean } = {};
  private muted = false;

  // États simples pour les médias
  private isVideoEnabled = true;
  private isAudioEnabled = true;

  // WebRTC
  private peerConnection: RTCPeerConnection | null = null;
  private localStream: MediaStream | null = null;
  private remoteStream: MediaStream | null = null;
  private localVideoElement: HTMLVideoElement | null = null;
  private remoteVideoElement: HTMLVideoElement | null = null;
  private currentCallId: string | null = null;
  private webrtcExchangeStarted: boolean = false;

  constructor(private apollo: Apollo, private logger: LoggerService) {
    this.preloadSounds();
    this.initializeSubscriptions();
    this.initializeWebRTC();
  }

  /**
   * Initialise les subscriptions avec un délai pour s'assurer que l'authentification est prête
   */
  private initializeSubscriptions(): void {
    // Attendre un peu pour s'assurer que l'authentification est prête
    setTimeout(() => {
      this.subscribeToIncomingCalls();
      this.subscribeToCallStatusChanges();
      this.subscribeToCallSignals();
    }, 1000);

    // Réessayer UNE SEULE FOIS après 10 secondes si nécessaire
    setTimeout(() => {
      if (!this.incomingCall.value) {
        // console.log('🔄 [CallService] Retrying subscription initialization...');
        this.subscribeToIncomingCalls();
        this.subscribeToCallStatusChanges();
        this.subscribeToCallSignals();
      }
    }, 10000);
  }

  /**
   * Initialise WebRTC
   */
  private initializeWebRTC(): void {
    console.log('🔧 [CallService] Initializing WebRTC...');

    // Configuration des serveurs STUN/TURN
    const configuration: RTCConfiguration = {
      iceServers: [
        { urls: 'stun:stun.l.google.com:19302' },
        { urls: 'stun:stun1.l.google.com:19302' },
      ],
    };

    try {
      this.peerConnection = new RTCPeerConnection(configuration);
      this.setupPeerConnectionEvents();
      console.log('✅ [CallService] WebRTC initialized successfully');
    } catch (error) {
      console.error('❌ [CallService] Failed to initialize WebRTC:', error);
    }
  }

  /**
   * Configure les événements de la PeerConnection
   */
  private setupPeerConnectionEvents(): void {
    if (!this.peerConnection) return;

    // Quand on reçoit un stream distant
    this.peerConnection.ontrack = (event) => {
      console.log('📺 [CallService] Remote stream received');
      console.log('🔍 [CallService] Stream details:', {
        streamId: event.streams[0]?.id,
        tracks: event.streams[0]?.getTracks().map((t) => ({
          kind: t.kind,
          enabled: t.enabled,
          readyState: t.readyState,
        })),
      });

      this.remoteStream = event.streams[0];

      // Forcer l'attachement immédiat à tous les éléments vidéo disponibles
      this.forceAttachRemoteStream();

      if (this.remoteVideoElement) {
        console.log('🎥 [CallService] Setting remote stream to video element');
        this.remoteVideoElement.srcObject = this.remoteStream;
        this.remoteVideoElement.muted = false;
        this.remoteVideoElement.volume = 1;
        this.remoteVideoElement.autoplay = true;

        // Activer tous les tracks audio
        this.remoteStream.getAudioTracks().forEach((track) => {
          track.enabled = true;
          console.log('🔊 [CallService] Remote audio track enabled');
        });

        // Forcer la lecture immédiatement
        this.remoteVideoElement
          .play()
          .then(() => {
            console.log('✅ [CallService] Remote video playing successfully');
          })
          .catch((e) => {
            console.warn(
              '⚠️ [CallService] Remote video play failed (autoplay blocked):',
              e
            );
            console.log(
              '💡 [CallService] Click the green "Activate Audio" button to enable sound'
            );
          });
      } else {
        console.warn('⚠️ [CallService] No remote video element available');
      }
    };

    // Gestion des candidats ICE
    this.peerConnection.onicecandidate = (event) => {
      if (event.candidate) {
        console.log('🧊 [CallService] ICE candidate generated');
        this.sendSignal('ice-candidate', JSON.stringify(event.candidate));
      }
    };

    // État de la connexion
    this.peerConnection.onconnectionstatechange = () => {
      console.log(
        '🔗 [CallService] Connection state:',
        this.peerConnection?.connectionState
      );
    };
  }

  /**
   * Précharge les sons utilisés dans l'application
   */
  private preloadSounds(): void {
    // Créer des sons synthétiques de haute qualité
    this.createSyntheticSounds();

    // Charger le son de notification qui existe encore
    this.loadSound('notification', 'assets/sounds/notification.mp3');

    console.log(
      '🎵 [CallService] Beautiful synthetic melodies created for calls'
    );
  }

  /**
   * Crée des sons synthétiques comme fallback
   */
  private createSyntheticSounds(): void {
    try {
      // Créer un contexte audio pour les sons synthétiques
      const audioContext = new (window.AudioContext ||
        (window as any).webkitAudioContext)();

      // Son de sonnerie (mélodie agréable)
      this.sounds['ringtone-synthetic'] =
        this.createRingtoneSound(audioContext);

      // Son de connexion (accord agréable)
      this.sounds['call-connected-synthetic'] =
        this.createConnectedSound(audioContext);

      // Son de fin d'appel (ton descendant)
      this.sounds['call-end-synthetic'] = this.createEndSound(audioContext);

      // Son de notification (bip agréable)
      this.sounds['notification-synthetic'] =
        this.createNotificationSound(audioContext);

      console.log('🔊 [CallService] Synthetic sounds created as fallback');
    } catch (error) {
      console.warn(
        '⚠️ [CallService] Could not create synthetic sounds:',
        error
      );
    }
  }

  /**
   * Crée une sonnerie agréable (mélodie)
   */
  private createRingtoneSound(audioContext: AudioContext): HTMLAudioElement {
    const audio = new Audio();
    audio.volume = 0.5;

    let isPlaying = false;
    let timeoutIds: any[] = [];

    (audio as any).playSynthetic = () => {
      if (isPlaying) return Promise.resolve();

      isPlaying = true;

      const playMelody = () => {
        if (!isPlaying) return;

        // Mélodie inspirée de Nokia mais plus moderne : Mi-Ré-Fa#-Sol-Do#-Si-Ré-Do
        const melody = [
          { freq: 659.25, duration: 0.125 }, // Mi
          { freq: 587.33, duration: 0.125 }, // Ré
          { freq: 739.99, duration: 0.25 }, // Fa#
          { freq: 783.99, duration: 0.25 }, // Sol
          { freq: 554.37, duration: 0.125 }, // Do#
          { freq: 493.88, duration: 0.125 }, // Si
          { freq: 587.33, duration: 0.25 }, // Ré
          { freq: 523.25, duration: 0.25 }, // Do
        ];

        let currentTime = audioContext.currentTime;

        melody.forEach((note, index) => {
          if (!isPlaying) return;

          const oscillator = audioContext.createOscillator();
          const gainNode = audioContext.createGain();

          oscillator.connect(gainNode);
          gainNode.connect(audioContext.destination);

          oscillator.frequency.value = note.freq;
          oscillator.type = 'square'; // Son plus moderne

          // Enveloppe ADSR pour un son plus naturel
          gainNode.gain.setValueAtTime(0, currentTime);
          gainNode.gain.linearRampToValueAtTime(0.3, currentTime + 0.02);
          gainNode.gain.linearRampToValueAtTime(
            0.2,
            currentTime + note.duration * 0.7
          );
          gainNode.gain.exponentialRampToValueAtTime(
            0.01,
            currentTime + note.duration
          );

          oscillator.start(currentTime);
          oscillator.stop(currentTime + note.duration);

          currentTime += note.duration + 0.05; // Petite pause entre les notes
        });

        // Répéter la mélodie après une pause
        const timeoutId = setTimeout(() => {
          if (isPlaying) {
            playMelody();
          }
        }, (currentTime - audioContext.currentTime + 0.8) * 1000);

        timeoutIds.push(timeoutId);
      };

      playMelody();
      return Promise.resolve();
    };

    (audio as any).stopSynthetic = () => {
      isPlaying = false;
      timeoutIds.forEach((id) => clearTimeout(id));
      timeoutIds = [];
    };

    return audio;
  }

  /**
   * Crée un son de connexion agréable (mélodie ascendante)
   */
  private createConnectedSound(audioContext: AudioContext): HTMLAudioElement {
    const audio = new Audio();
    audio.volume = 0.5;

    (audio as any).playSynthetic = () => {
      // Mélodie ascendante positive : Do-Mi-Sol-Do (octave supérieure)
      const melody = [
        { freq: 523.25, duration: 0.15 }, // Do
        { freq: 659.25, duration: 0.15 }, // Mi
        { freq: 783.99, duration: 0.15 }, // Sol
        { freq: 1046.5, duration: 0.4 }, // Do (octave supérieure)
      ];

      let currentTime = audioContext.currentTime;

      melody.forEach((note, index) => {
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();

        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);

        oscillator.frequency.value = note.freq;
        oscillator.type = 'triangle'; // Son plus doux

        // Enveloppe pour un son naturel
        gainNode.gain.setValueAtTime(0, currentTime);
        gainNode.gain.linearRampToValueAtTime(0.25, currentTime + 0.02);
        gainNode.gain.linearRampToValueAtTime(
          0.15,
          currentTime + note.duration * 0.8
        );
        gainNode.gain.exponentialRampToValueAtTime(
          0.01,
          currentTime + note.duration
        );

        oscillator.start(currentTime);
        oscillator.stop(currentTime + note.duration);

        currentTime += note.duration * 0.8; // Chevauchement léger des notes
      });

      return Promise.resolve();
    };

    return audio;
  }

  /**
   * Crée un son de fin d'appel (mélodie descendante douce)
   */
  private createEndSound(audioContext: AudioContext): HTMLAudioElement {
    const audio = new Audio();
    audio.volume = 0.4;

    (audio as any).playSynthetic = () => {
      // Mélodie descendante douce : Sol-Mi-Do-Sol(grave)
      const melody = [
        { freq: 783.99, duration: 0.2 }, // Sol
        { freq: 659.25, duration: 0.2 }, // Mi
        { freq: 523.25, duration: 0.2 }, // Do
        { freq: 392.0, duration: 0.4 }, // Sol (octave inférieure)
      ];

      let currentTime = audioContext.currentTime;

      melody.forEach((note, index) => {
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();

        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);

        oscillator.frequency.value = note.freq;
        oscillator.type = 'sine'; // Son doux pour la fin

        // Enveloppe douce pour un son apaisant
        gainNode.gain.setValueAtTime(0, currentTime);
        gainNode.gain.linearRampToValueAtTime(0.2, currentTime + 0.05);
        gainNode.gain.linearRampToValueAtTime(
          0.1,
          currentTime + note.duration * 0.7
        );
        gainNode.gain.exponentialRampToValueAtTime(
          0.01,
          currentTime + note.duration
        );

        oscillator.start(currentTime);
        oscillator.stop(currentTime + note.duration);

        currentTime += note.duration * 0.9; // Léger chevauchement
      });

      return Promise.resolve();
    };

    return audio;
  }

  /**
   * Crée un son de notification agréable (double bip)
   */
  private createNotificationSound(
    audioContext: AudioContext
  ): HTMLAudioElement {
    const audio = new Audio();
    audio.volume = 0.6;

    (audio as any).playSynthetic = () => {
      // Double bip agréable : Do-Sol
      const notes = [
        { freq: 523.25, duration: 0.15, delay: 0 }, // Do
        { freq: 783.99, duration: 0.25, delay: 0.2 }, // Sol
      ];

      notes.forEach((note) => {
        setTimeout(() => {
          const oscillator = audioContext.createOscillator();
          const gainNode = audioContext.createGain();

          oscillator.connect(gainNode);
          gainNode.connect(audioContext.destination);

          oscillator.frequency.value = note.freq;
          oscillator.type = 'triangle'; // Son doux et agréable

          const startTime = audioContext.currentTime;

          // Enveloppe pour un son naturel
          gainNode.gain.setValueAtTime(0, startTime);
          gainNode.gain.linearRampToValueAtTime(0.4, startTime + 0.02);
          gainNode.gain.linearRampToValueAtTime(
            0.2,
            startTime + note.duration * 0.7
          );
          gainNode.gain.exponentialRampToValueAtTime(
            0.01,
            startTime + note.duration
          );

          oscillator.start(startTime);
          oscillator.stop(startTime + note.duration);
        }, note.delay * 1000);
      });

      return Promise.resolve();
    };

    return audio;
  }

  /**
   * Charge un fichier audio
   */
  private loadSound(name: string, path: string): void {
    try {
      const audio = new Audio();

      // Configuration de l'audio
      audio.preload = 'auto';
      audio.volume = 0.7;

      // Gérer les événements de chargement
      audio.addEventListener('canplaythrough', () => {
        console.log(
          `✅ [CallService] Sound ${name} loaded successfully from ${path}`
        );
      });

      audio.addEventListener('error', (e) => {
        console.error(
          `❌ [CallService] Error loading sound ${name} from ${path}:`,
          e
        );
        console.log(
          `🔄 [CallService] Trying to load ${name} with different approach...`
        );

        // Essayer de charger avec un chemin relatif
        const altPath = path.startsWith('/') ? path.substring(1) : path;
        if (altPath !== path) {
          setTimeout(() => {
            audio.src = altPath;
            audio.load();
          }, 100);
        }
      });

      audio.addEventListener('ended', () => {
        this.isPlaying[name] = false;
        console.log(`🔇 [CallService] Sound ${name} ended`);
      });

      // Définir la source et charger
      audio.src = path;
      audio.load();

      this.sounds[name] = audio;
      this.isPlaying[name] = false;

      console.log(`🔊 [CallService] Loading sound ${name} from ${path}`);
    } catch (error) {
      console.error(
        `❌ [CallService] Error creating audio element for ${name}:`,
        error
      );
    }
  }

  /**
   * Joue un son
   */
  private play(name: string, loop: boolean = false): void {
    if (this.muted) {
      console.log(`🔇 [CallService] Sound ${name} muted`);
      return;
    }

    try {
      // Pour les sons d'appel, utiliser directement les versions synthétiques
      let sound;
      if (
        name === 'ringtone' ||
        name === 'call-connected' ||
        name === 'call-end'
      ) {
        const syntheticName = `${name}-synthetic`;
        sound = this.sounds[syntheticName];
        if (sound) {
          console.log(
            `🎵 [CallService] Using beautiful synthetic melody for ${name}`
          );
        }
      } else {
        // Pour les autres sons (comme notification), essayer d'abord le fichier
        sound = this.sounds[name];
        if (!sound || sound.error) {
          const syntheticName = `${name}-synthetic`;
          sound = this.sounds[syntheticName];
          if (sound) {
            console.log(`🔊 [CallService] Using synthetic sound for ${name}`);
          }
        }
      }

      if (!sound) {
        console.warn(
          `🔊 [CallService] Sound ${name} not found (neither original nor synthetic)`
        );
        // Créer un bip simple comme dernier recours
        this.playSimpleBeep(
          name === 'ringtone' ? 800 : name === 'call-connected' ? 1000 : 400
        );
        return;
      }

      sound.loop = loop;
      sound.volume = 0.7;

      if (!this.isPlaying[name]) {
        console.log(`🔊 [CallService] Playing sound: ${name} (loop: ${loop})`);

        // Vérifier si c'est un son synthétique
        if ((sound as any).playSynthetic) {
          (sound as any)
            .playSynthetic()
            .then(() => {
              console.log(
                `✅ [CallService] Synthetic sound ${name} started successfully`
              );
              this.isPlaying[name] = true;

              // Pour la sonnerie synthétique, elle gère sa propre boucle
              if (name === 'ringtone' && !loop) {
                // Si ce n'est pas en boucle, arrêter après un certain temps
                setTimeout(() => {
                  this.isPlaying[name] = false;
                }, 3000);
              } else if (name !== 'ringtone') {
                // Pour les autres sons, arrêter après leur durée
                setTimeout(
                  () => {
                    this.isPlaying[name] = false;
                  },
                  name === 'call-connected' ? 1200 : 1000
                );
              }
            })
            .catch((error: any) => {
              console.error(
                `❌ [CallService] Error playing synthetic sound ${name}:`,
                error
              );
            });
        } else {
          // Son normal
          sound.currentTime = 0;
          sound
            .play()
            .then(() => {
              console.log(
                `✅ [CallService] Sound ${name} started successfully`
              );
              this.isPlaying[name] = true;
            })
            .catch((error) => {
              console.error(
                `❌ [CallService] Error playing sound ${name}:`,
                error
              );

              // Essayer le son synthétique en cas d'échec
              const syntheticName = `${name}-synthetic`;
              const syntheticSound = this.sounds[syntheticName];
              if (syntheticSound && (syntheticSound as any).playSynthetic) {
                console.log(
                  `🔄 [CallService] Falling back to synthetic sound for ${name}`
                );
                this.play(name, loop);
              } else {
                // Dernier recours : bip simple
                this.playSimpleBeep(
                  name === 'ringtone'
                    ? 800
                    : name === 'call-connected'
                    ? 1000
                    : 400
                );
              }
            });
        }
      } else {
        console.log(`🔊 [CallService] Sound ${name} already playing`);
      }
    } catch (error) {
      console.error(
        `❌ [CallService] Error in play method for ${name}:`,
        error
      );
      // Dernier recours
      this.playSimpleBeep(
        name === 'ringtone' ? 800 : name === 'call-connected' ? 1000 : 400
      );
    }
  }

  /**
   * Joue un bip simple comme dernier recours
   */
  private playSimpleBeep(frequency: number): void {
    try {
      const audioContext = new (window.AudioContext ||
        (window as any).webkitAudioContext)();
      const oscillator = audioContext.createOscillator();
      const gainNode = audioContext.createGain();

      oscillator.connect(gainNode);
      gainNode.connect(audioContext.destination);

      oscillator.frequency.value = frequency;
      oscillator.type = 'sine';

      gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
      gainNode.gain.exponentialRampToValueAtTime(
        0.01,
        audioContext.currentTime + 0.3
      );

      oscillator.start(audioContext.currentTime);
      oscillator.stop(audioContext.currentTime + 0.3);

      console.log(`🔊 [CallService] Playing simple beep at ${frequency}Hz`);
    } catch (error) {
      console.error('❌ [CallService] Could not play simple beep:', error);
    }
  }

  /**
   * Arrête un son
   */
  private stop(name: string): void {
    try {
      let sound = this.sounds[name];

      // Essayer aussi la version synthétique
      if (!sound) {
        sound = this.sounds[`${name}-synthetic`];
      }

      if (!sound) {
        console.warn(`🔊 [CallService] Sound ${name} not found for stopping`);
        return;
      }

      if (this.isPlaying[name]) {
        console.log(`🔇 [CallService] Stopping sound: ${name}`);

        // Arrêter le son synthétique si c'est le cas
        if ((sound as any).stopSynthetic) {
          (sound as any).stopSynthetic();
        } else {
          sound.pause();
          sound.currentTime = 0;
        }

        this.isPlaying[name] = false;
      } else {
        console.log(`🔇 [CallService] Sound ${name} was not playing`);
      }
    } catch (error) {
      console.error(`❌ [CallService] Error stopping sound ${name}:`, error);
    }
  }

  /**
   * S'abonne aux appels entrants
   */
  private subscribeToIncomingCalls(): void {
    // console.log('🔔 [CallService] Setting up incoming call subscription...');

    try {
      this.apollo
        .subscribe<{ incomingCall: IncomingCall }>({
          query: INCOMING_CALL_SUBSCRIPTION,
          errorPolicy: 'all', // Continuer même en cas d'erreur
        })
        .subscribe({
          next: ({ data, errors }) => {
            if (errors) {
              console.warn(
                '⚠️ [CallService] GraphQL errors in subscription:',
                errors
              );
            }

            if (data?.incomingCall) {
              // console.log('📞 [CallService] Incoming call received:', data.incomingCall.id);
              this.handleIncomingCall(data.incomingCall);
            }
          },
          error: (error) => {
            console.error(
              '❌ [CallService] Error in incoming call subscription:',
              error
            );

            // Réessayer UNE SEULE FOIS après 10 secondes
            setTimeout(() => {
              this.subscribeToIncomingCalls();
            }, 10000);
          },
          complete: () => {
            // console.log('🔚 [CallService] Incoming call subscription completed');
            // Réessayer si la subscription se ferme de manière inattendue
            setTimeout(() => {
              this.subscribeToIncomingCalls();
            }, 5000);
          },
        });
    } catch (error) {
      console.error('❌ [CallService] Failed to create subscription:', error);
      // Réessayer après 10 secondes
      setTimeout(() => {
        this.subscribeToIncomingCalls();
      }, 10000);
    }
  }

  /**
   * Méthode publique pour forcer la réinitialisation de la subscription
   */
  public reinitializeSubscription(): void {
    // console.log('🔄 [CallService] Manually reinitializing subscription...');
    this.subscribeToIncomingCalls();
    this.subscribeToCallStatusChanges();
    this.subscribeToCallSignals();
  }

  /**
   * S'abonne aux signaux WebRTC (offres, réponses, candidats ICE)
   */
  private subscribeToCallSignals(): void {
    console.log('📡 [CallService] Setting up call signal subscription...');

    try {
      this.apollo
        .subscribe<{ callSignal: any }>({
          query: CALL_SIGNAL_SUBSCRIPTION,
          variables: {
            callId: this.currentCallId || null,
          },
          errorPolicy: 'all',
        })
        .subscribe({
          next: ({ data, errors }) => {
            if (errors) {
              console.warn(
                '⚠️ [CallService] Call signal subscription errors:',
                errors
              );
            }

            if (data?.callSignal) {
              console.log(
                '📡 [CallService] Received call signal:',
                data.callSignal
              );
              this.handleCallSignal(data.callSignal);
            }
          },
          error: (error) => {
            console.error(
              '❌ [CallService] Error in call signal subscription:',
              error
            );
            // Réessayer après 5 secondes
            setTimeout(() => {
              this.subscribeToCallSignals();
            }, 5000);
          },
          complete: () => {
            console.log('🔚 [CallService] Call signal subscription completed');
            // Réessayer si la subscription se ferme
            setTimeout(() => {
              this.subscribeToCallSignals();
            }, 2000);
          },
        });
    } catch (error) {
      console.error(
        '❌ [CallService] Failed to create call signal subscription:',
        error
      );
      setTimeout(() => {
        this.subscribeToCallSignals();
      }, 3000);
    }
  }

  /**
   * Gère les signaux WebRTC reçus
   */
  private async handleCallSignal(signal: any): Promise<void> {
    console.log('🔄 [CallService] Handling call signal:', signal.type);

    try {
      switch (signal.type) {
        case 'offer':
          await this.handleRemoteOffer(signal);
          break;
        case 'answer':
          await this.handleRemoteAnswer(signal);
          break;
        case 'ice-candidate':
          await this.handleRemoteIceCandidate(signal);
          break;
        default:
          console.warn('⚠️ [CallService] Unknown signal type:', signal.type);
      }
    } catch (error) {
      console.error('❌ [CallService] Error handling call signal:', error);
    }
  }

  /**
   * Gère une offre WebRTC distante
   */
  private async handleRemoteOffer(signal: any): Promise<void> {
    console.log('📥 [CallService] Handling remote offer');

    if (!this.peerConnection) {
      console.log('🔧 [CallService] Initializing WebRTC for remote offer');
      this.initializeWebRTC();
    }

    try {
      // Créer et obtenir le stream local si pas encore fait
      await this.createLocalStream();

      // Ajouter le stream local à la peer connection
      if (this.localStream) {
        this.localStream.getTracks().forEach((track) => {
          // Vérifier si le track n'est pas déjà ajouté
          const existingSender = this.peerConnection!.getSenders().find(
            (sender) => sender.track === track
          );

          if (!existingSender) {
            this.peerConnection!.addTrack(track, this.localStream!);
            console.log(
              `✅ [CallService] Added ${track.kind} track for answer`
            );
          } else {
            console.log(
              `⚠️ [CallService] Track ${track.kind} already exists for answer, skipping`
            );
          }
        });
      }

      // Créer une réponse en utilisant la méthode dédiée
      const offerData = JSON.parse(signal.data);
      const answer = await this.createAnswer(offerData);

      // Envoyer la réponse
      this.sendSignal('answer', JSON.stringify(answer));
    } catch (error) {
      console.error('❌ [CallService] Error handling remote offer:', error);
    }
  }

  /**
   * Gère une réponse WebRTC distante
   */
  private async handleRemoteAnswer(signal: any): Promise<void> {
    console.log('📥 [CallService] Handling remote answer');

    if (!this.peerConnection) {
      console.error('❌ [CallService] No peer connection for remote answer');
      return;
    }

    const answer = JSON.parse(signal.data);
    await this.peerConnection.setRemoteDescription(answer);
    console.log('✅ [CallService] Remote answer set successfully');
  }

  /**
   * Gère un candidat ICE distant
   */
  private async handleRemoteIceCandidate(signal: any): Promise<void> {
    console.log('🧊 [CallService] Handling remote ICE candidate');

    if (!this.peerConnection) {
      console.error('❌ [CallService] No peer connection for ICE candidate');
      return;
    }

    const candidate = JSON.parse(signal.data);
    await this.peerConnection.addIceCandidate(candidate);
    console.log('✅ [CallService] Remote ICE candidate added successfully');
  }

  /**
   * Envoie un signal WebRTC via GraphQL
   */
  private sendSignal(type: string, data: string): void {
    if (!this.currentCallId) {
      console.warn('⚠️ [CallService] No current call ID for sending signal');
      return;
    }

    console.log(`📤 [CallService] Sending signal: ${type}`);

    // TODO: Implémenter l'envoi via mutation GraphQL
    // Pour l'instant, simulons l'échange direct
    this.simulateSignalExchange(type, data);
  }

  /**
   * Simule l'échange de signaux WebRTC (temporaire)
   */
  private simulateSignalExchange(type: string, data: string): void {
    console.log(`🔄 [CallService] Simulating signal exchange: ${type}`);

    // Simuler la réception du signal par l'autre peer après un délai
    setTimeout(() => {
      const signal = {
        callId: this.currentCallId,
        senderId: 'other-user',
        type: type,
        data: data,
      };

      console.log('📡 [CallService] Simulated received signal:', signal);
      this.handleCallSignal(signal);
    }, 100);
  }

  /**
   * Démarre l'échange WebRTC (création d'offre)
   */
  private async startWebRTCExchange(): Promise<void> {
    if (this.webrtcExchangeStarted) {
      console.log('⚠️ [CallService] WebRTC exchange already started, skipping');
      return;
    }

    console.log('🚀 [CallService] Starting WebRTC exchange...');
    this.webrtcExchangeStarted = true;

    if (!this.peerConnection) {
      console.error('❌ [CallService] No peer connection available');
      return;
    }

    try {
      // Créer et obtenir le stream local
      await this.createLocalStream();

      // Ajouter le stream local à la peer connection
      if (this.localStream) {
        console.log('🔗 [CallService] Adding local stream to peer connection');
        this.localStream.getTracks().forEach((track) => {
          // Vérifier si le track n'est pas déjà ajouté
          const existingSender = this.peerConnection!.getSenders().find(
            (sender) => sender.track === track
          );

          if (!existingSender) {
            this.peerConnection!.addTrack(track, this.localStream!);
            console.log(
              `✅ [CallService] Added ${track.kind} track to peer connection`
            );
          } else {
            console.log(
              `⚠️ [CallService] Track ${track.kind} already exists, skipping`
            );
          }
        });
      }

      // Créer une offre
      console.log('📝 [CallService] Creating WebRTC offer...');
      const offer = await this.peerConnection.createOffer({
        offerToReceiveAudio: true,
        offerToReceiveVideo: false,
      });

      await this.peerConnection.setLocalDescription(offer);
      console.log('✅ [CallService] Local description set');

      // Envoyer l'offre
      this.sendSignal('offer', JSON.stringify(offer));
    } catch (error) {
      console.error('❌ [CallService] Error starting WebRTC exchange:', error);
    }
  }

  /**
   * Crée le stream local (audio)
   */
  private async createLocalStream(): Promise<void> {
    if (this.localStream) {
      console.log('✅ [CallService] Local stream already exists');
      return;
    }

    try {
      console.log('🎤 [CallService] Creating local audio stream...');
      this.localStream = await navigator.mediaDevices.getUserMedia({
        audio: true,
        video: false,
      });

      console.log(
        '🔍 [CallService] Stream tracks:',
        this.localStream.getTracks().map((t) => ({
          kind: t.kind,
          enabled: t.enabled,
          readyState: t.readyState,
        }))
      );

      // Attacher immédiatement au local video element
      if (this.localVideoElement) {
        this.localVideoElement.srcObject = this.localStream;
        this.localVideoElement.muted = true;
        this.localVideoElement.play();
        console.log('✅ [CallService] Local stream attached to video element');
      }

      // Forcer aussi l'attachement à tous les éléments vidéo disponibles
      this.forceAttachLocalStream();
    } catch (error) {
      console.error('❌ [CallService] Error creating local stream:', error);
    }
  }

  /**
   * Active l'audio manuellement (résout les problèmes d'autoplay)
   */
  public forceActivateAudio(): void {
    console.log('🔊 [CallService] Force activating audio...');

    try {
      // 1. Activer AudioContext
      const audioContext = new AudioContext();
      if (audioContext.state === 'suspended') {
        audioContext.resume().then(() => {
          console.log('✅ [CallService] AudioContext resumed');
        });
      }

      // 2. Forcer la lecture des éléments vidéo
      if (this.remoteVideoElement && this.remoteStream) {
        console.log('🔊 [CallService] Force activating remote video element');
        this.remoteVideoElement.volume = 1;
        this.remoteVideoElement.muted = false;

        // Activer tous les tracks audio
        this.remoteStream.getAudioTracks().forEach((track) => {
          track.enabled = true;
          console.log('✅ [CallService] Remote audio track force enabled');
        });

        // Forcer la lecture
        this.remoteVideoElement
          .play()
          .then(() => {
            console.log('✅ [CallService] Remote video force playing');
          })
          .catch((e) => {
            console.warn('⚠️ [CallService] Force play still failed:', e);
          });
      }

      // 3. Forcer aussi sur tous les éléments vidéo du DOM
      const videos = document.querySelectorAll('video');
      videos.forEach((video, index) => {
        if (video.srcObject && index > 0) {
          // Pas le local
          video.volume = 1;
          video.muted = false;
          video.play();
          console.log(`✅ [CallService] Force activated Video ${index}`);
        }
      });

      // 4. Test audio système et solutions de contournement
      this.testSystemAudio();
      this.applyAudioWorkarounds();

      console.log('✅ [CallService] Audio force activation completed');
    } catch (error) {
      console.error('❌ [CallService] Error force activating audio:', error);
    }
  }

  /**
   * Test audio système pour diagnostiquer les problèmes
   */
  private testSystemAudio(): void {
    console.log('🔍 [CallService] Testing system audio...');

    try {
      // Test avec un bip simple
      const audioContext = new AudioContext();
      const oscillator = audioContext.createOscillator();
      const gainNode = audioContext.createGain();

      oscillator.connect(gainNode);
      gainNode.connect(audioContext.destination);

      oscillator.frequency.value = 440; // Note A
      gainNode.gain.value = 0.3;

      oscillator.start();
      oscillator.stop(audioContext.currentTime + 0.5);

      console.log(
        '🔊 [CallService] System audio test - you should hear a beep!'
      );

      // Vérifier les permissions
      navigator.permissions
        .query({ name: 'microphone' as PermissionName })
        .then((result) => {
          console.log('🎤 [CallService] Microphone permission:', result.state);
        })
        .catch((e) => {
          console.warn('⚠️ [CallService] Permission check failed:', e);
        });
    } catch (error) {
      console.error('❌ [CallService] System audio test failed:', error);
    }
  }

  /**
   * Applique des solutions de contournement pour l'audio
   */
  private applyAudioWorkarounds(): void {
    console.log('🔧 [CallService] Applying audio workarounds...');

    try {
      // Solution 1: Web Audio API pour forcer la sortie
      const videos = document.querySelectorAll('video');
      videos.forEach((video, index) => {
        if (video.srcObject instanceof MediaStream && index > 0) {
          try {
            const audioContext = new AudioContext();
            const source = audioContext.createMediaStreamSource(
              video.srcObject
            );
            const gainNode = audioContext.createGain();

            source.connect(gainNode);
            gainNode.connect(audioContext.destination);
            gainNode.gain.value = 2.0; // Volume amplifié

            console.log(
              `🔊 [CallService] Web Audio API connected for Video ${index}`
            );
          } catch (error) {
            console.warn(
              `⚠️ [CallService] Web Audio API failed for Video ${index}:`,
              error
            );
          }
        }
      });

      // Solution 2: Créer des éléments audio dédiés
      if (this.remoteStream) {
        const audioElement = document.createElement('audio');
        audioElement.srcObject = this.remoteStream;
        audioElement.volume = 1.0;
        audioElement.autoplay = true;
        audioElement.controls = false;
        audioElement.style.display = 'none';

        // Ajouter au DOM temporairement
        document.body.appendChild(audioElement);

        audioElement
          .play()
          .then(() => {
            console.log('✅ [CallService] Dedicated audio element playing');
          })
          .catch((e) => {
            console.warn('⚠️ [CallService] Dedicated audio element failed:', e);
            if (document.body.contains(audioElement)) {
              document.body.removeChild(audioElement);
            }
          });

        // Nettoyer après 30 secondes
        setTimeout(() => {
          if (document.body.contains(audioElement)) {
            document.body.removeChild(audioElement);
            console.log('🧹 [CallService] Cleaned up dedicated audio element');
          }
        }, 30000);
      }

      // Solution 3: Notification visuelle si pas d'audio
      setTimeout(() => {
        this.showAudioTroubleshootingTip();
      }, 3000);
    } catch (error) {
      console.error('❌ [CallService] Audio workarounds failed:', error);
    }
  }

  /**
   * Affiche un conseil de dépannage audio
   */
  private showAudioTroubleshootingTip(): void {
    console.log('💡 [CallService] Audio troubleshooting tips:');
    console.log('1. Check Windows volume mixer (right-click speaker icon)');
    console.log('2. Ensure browser is not muted in volume mixer');
    console.log('3. Check audio output device in Windows settings');
    console.log(
      '4. Try refreshing the page and accepting microphone permission'
    );

    // Créer une notification visuelle temporaire
    const notification = document.createElement('div');
    notification.innerHTML = `
      <div style="position: fixed; top: 20px; right: 20px; background: #ff4444; color: white; padding: 15px; border-radius: 8px; z-index: 10000; max-width: 300px; font-size: 14px; box-shadow: 0 4px 12px rgba(0,0,0,0.3);">
        <strong>🔊 Audio Troubleshooting:</strong><br>
        • Check Windows volume mixer<br>
        • Ensure browser not muted<br>
        • Verify audio output device<br>
        • Try headphones/speakers<br>
        <button onclick="this.parentElement.parentElement.remove()" style="background: white; color: #ff4444; border: none; padding: 5px 10px; border-radius: 4px; margin-top: 10px; cursor: pointer; font-weight: bold;">Got it!</button>
      </div>
    `;

    document.body.appendChild(notification);

    // Auto-remove après 20 secondes
    setTimeout(() => {
      if (document.body.contains(notification)) {
        document.body.removeChild(notification);
      }
    }, 20000);
  }

  /**
   * Méthode de test pour vérifier les sons
   */
  public testSounds(): void {
    console.log('🧪 [CallService] Testing sounds...');

    // Test de la sonnerie
    console.log('🔊 Testing ringtone...');
    this.play('ringtone', true);

    setTimeout(() => {
      this.stop('ringtone');
      console.log('🔊 Testing call-connected...');
      this.play('call-connected');
    }, 3000);

    setTimeout(() => {
      console.log('🔊 Testing call-end...');
      this.play('call-end');
    }, 5000);
  }

  /**
   * Test spécifique pour la sonnerie
   */
  public testRingtone(): void {
    console.log('🧪 [CallService] Testing ringtone specifically...');
    console.log('🔊 [CallService] Available sounds:', Object.keys(this.sounds));

    // Vérifier si le son est chargé
    const ringtone = this.sounds['ringtone'];
    const ringtoneSynthetic = this.sounds['ringtone-synthetic'];

    if (ringtone) {
      console.log('✅ [CallService] Ringtone MP3 found:', {
        src: ringtone.src,
        readyState: ringtone.readyState,
        error: ringtone.error,
        duration: ringtone.duration,
      });
    } else {
      console.log('❌ [CallService] Ringtone MP3 not found');
    }

    if (ringtoneSynthetic) {
      console.log('✅ [CallService] Ringtone synthetic found');
    }

    // Jouer la sonnerie (elle va automatiquement utiliser le fallback si nécessaire)
    console.log('🎵 [CallService] Playing beautiful ringtone melody...');
    this.play('ringtone', true);

    // Arrêter après 8 secondes pour entendre plusieurs cycles de la mélodie
    setTimeout(() => {
      this.stop('ringtone');
      console.log('🔇 [CallService] Ringtone test stopped');
    }, 8000);
  }

  /**
   * Test tous les nouveaux sons améliorés
   */
  public testBeautifulSounds(): void {
    console.log('🧪 [CallService] Testing all beautiful sounds...');

    // Test de la sonnerie (mélodie)
    // console.log('🎵 Testing beautiful ringtone melody...');
    this.play('ringtone', true);

    setTimeout(() => {
      this.stop('ringtone');
      // console.log('🎵 Testing beautiful connection sound...');
      this.play('call-connected');
    }, 4000);

    setTimeout(() => {
      // console.log('🎵 Testing beautiful end sound...');
      this.play('call-end');
    }, 6000);

    setTimeout(() => {
      // console.log('🎵 Testing beautiful notification sound...');
      this.play('notification');
    }, 8000);

    setTimeout(() => {
      console.log('🎵 All sound tests completed!');
    }, 10000);
  }

  /**
   * Joue le son de notification (méthode publique)
   */
  public playNotification(): void {
    console.log('🔔 [CallService] Playing notification sound...');
    this.play('notification');
  }

  /**
   * S'abonne aux changements de statut d'appel
   */
  private subscribeToCallStatusChanges(): void {
    console.log(
      '📞 [CallService] Setting up call status change subscription...'
    );

    try {
      this.apollo
        .subscribe<{ callStatusChanged: Call }>({
          query: CALL_STATUS_CHANGED_SUBSCRIPTION,
          errorPolicy: 'all',
        })
        .subscribe({
          next: ({ data, errors }) => {
            if (errors) {
              console.warn(
                '⚠️ [CallService] GraphQL errors in call status subscription:',
                errors
              );
            }

            if (data?.callStatusChanged) {
              console.log(
                '📞 [CallService] Call status changed:',
                data.callStatusChanged
              );
              this.handleCallStatusChange(data.callStatusChanged);
            }
          },
          error: (error) => {
            console.error(
              '❌ [CallService] Error in call status subscription:',
              error
            );
            // Réessayer après 5 secondes
            setTimeout(() => {
              this.subscribeToCallStatusChanges();
            }, 5000);
          },
          complete: () => {
            console.log('🔚 [CallService] Call status subscription completed');
            // Réessayer si la subscription se ferme
            setTimeout(() => {
              this.subscribeToCallStatusChanges();
            }, 2000);
          },
        });
    } catch (error) {
      console.error(
        '❌ [CallService] Failed to create call status subscription:',
        error
      );
      setTimeout(() => {
        this.subscribeToCallStatusChanges();
      }, 3000);
    }
  }

  /**
   * Gère un appel entrant
   */
  private handleIncomingCall(call: IncomingCall): void {
    console.log('🔔 [CallService] Handling incoming call:', {
      callId: call.id,
      callType: call.type,
      caller: call.caller?.username,
      conversationId: call.conversationId,
    });

    this.incomingCall.next(call);
    this.play('ringtone', true);

    console.log(
      '🔊 [CallService] Ringtone started, call notification sent to UI'
    );
  }

  /**
   * Gère les changements de statut d'appel
   */
  private handleCallStatusChange(call: Call): void {
    console.log('📞 [CallService] Call status changed:', call.status);

    switch (call.status) {
      case CallStatus.REJECTED:
        console.log('❌ [CallService] Call was rejected');
        this.stop('ringtone');
        this.play('call-end');
        this.activeCall.next(null);
        this.incomingCall.next(null);
        break;

      case CallStatus.ENDED:
        console.log('📞 [CallService] Call ended');
        this.stopAllSounds();
        this.play('call-end');
        this.currentCallId = null; // Réinitialiser l'ID
        this.webrtcExchangeStarted = false; // Réinitialiser le flag
        this.activeCall.next(null);
        this.incomingCall.next(null);
        break;

      case CallStatus.CONNECTED:
        console.log('✅ [CallService] Call connected');
        this.stop('ringtone');
        this.play('call-connected');
        this.currentCallId = call.id; // Stocker l'ID pour la subscription
        this.activeCall.next(call);
        this.incomingCall.next(null);

        // Démarrer l'échange WebRTC
        setTimeout(() => {
          this.startWebRTCExchange();
        }, 1000);
        break;

      case CallStatus.RINGING:
        console.log('📞 [CallService] Call is ringing');
        this.play('ringtone', true);
        break;

      default:
        console.log('📞 [CallService] Unknown call status:', call.status);
        break;
    }
  }

  /**
   * Obtient les médias utilisateur (caméra/micro)
   */
  private async getUserMedia(callType: CallType): Promise<MediaStream> {
    console.log('🎥 [CallService] Getting user media for:', callType);

    const constraints: MediaStreamConstraints = {
      audio: true,
      video: callType === CallType.VIDEO,
    };

    try {
      const stream = await navigator.mediaDevices.getUserMedia(constraints);
      console.log('✅ [CallService] User media obtained');
      console.log(
        '🔍 [CallService] Stream tracks:',
        stream.getTracks().map((t) => ({
          kind: t.kind,
          enabled: t.enabled,
          readyState: t.readyState,
        }))
      );
      this.localStream = stream;

      // Afficher le stream local si on a un élément vidéo
      if (this.localVideoElement && callType === CallType.VIDEO) {
        this.localVideoElement.srcObject = stream;
      }

      return stream;
    } catch (error) {
      console.error('❌ [CallService] Failed to get user media:', error);
      throw new Error("Impossible d'accéder à la caméra/microphone");
    }
  }

  /**
   * Ajoute le stream local à la PeerConnection
   */
  private addLocalStreamToPeerConnection(stream: MediaStream): void {
    if (!this.peerConnection) {
      console.error('❌ [CallService] No peer connection available');
      return;
    }

    console.log('📤 [CallService] Adding local stream to peer connection');
    stream.getTracks().forEach((track) => {
      this.peerConnection!.addTrack(track, stream);
    });
  }

  /**
   * Crée une offre WebRTC
   */
  private async createOffer(): Promise<RTCSessionDescriptionInit> {
    if (!this.peerConnection) {
      throw new Error('No peer connection available');
    }

    console.log('📝 [CallService] Creating WebRTC offer');
    const offer = await this.peerConnection.createOffer();
    await this.peerConnection.setLocalDescription(offer);
    return offer;
  }

  /**
   * Crée une réponse WebRTC
   */
  private async createAnswer(
    offer: RTCSessionDescriptionInit
  ): Promise<RTCSessionDescriptionInit> {
    if (!this.peerConnection) {
      throw new Error('No peer connection available');
    }

    try {
      console.log('📝 [CallService] Creating WebRTC answer');

      // Vérifier l'état de la peer connection
      console.log(
        '🔍 [CallService] PeerConnection state:',
        this.peerConnection.connectionState
      );

      await this.peerConnection.setRemoteDescription(offer);
      console.log('✅ [CallService] Remote description set for answer');

      const answer = await this.peerConnection.createAnswer({
        offerToReceiveAudio: true,
        offerToReceiveVideo: false,
      });

      await this.peerConnection.setLocalDescription(answer);
      console.log('✅ [CallService] Local description (answer) set');

      return answer;
    } catch (error) {
      console.error('❌ [CallService] Error in createAnswer:', error);
      throw error;
    }
  }

  /**
   * Configure les éléments vidéo
   */
  public setVideoElements(
    localVideo: HTMLVideoElement,
    remoteVideo: HTMLVideoElement
  ): void {
    console.log('📺 [CallService] Setting video elements');
    this.localVideoElement = localVideo;
    this.remoteVideoElement = remoteVideo;

    // Configurer les éléments pour la lecture audio
    try {
      this.setupAudioPlayback(localVideo, remoteVideo);
    } catch (error) {
      console.warn('⚠️ [CallService] setupAudioPlayback error:', error);
    }

    // Si on a déjà des streams, les connecter IMMÉDIATEMENT
    if (this.localStream && localVideo) {
      console.log('🔗 [CallService] Attaching local stream to video element');
      localVideo.srcObject = this.localStream;
      localVideo.muted = true; // Local toujours muet pour éviter l'écho
      localVideo.volume = 0;
      localVideo.autoplay = true;
      try {
        localVideo.play();
        console.log('✅ [CallService] Local video playing');
      } catch (error) {
        console.warn('⚠️ [CallService] Local video play error:', error);
      }
    } else {
      console.warn(
        '⚠️ [CallService] No local stream or video element available'
      );
    }

    if (this.remoteStream && remoteVideo) {
      console.log('🔗 [CallService] Attaching remote stream to video element');
      remoteVideo.srcObject = this.remoteStream;
      remoteVideo.muted = false; // Remote avec audio
      remoteVideo.volume = 1;
      remoteVideo.autoplay = true;
      try {
        remoteVideo.play();
        console.log('✅ [CallService] Remote video playing');
      } catch (error) {
        console.warn('⚠️ [CallService] Remote video play error:', error);
      }
    } else {
      console.warn(
        '⚠️ [CallService] No remote stream or video element available'
      );
    }

    // Forcer l'attachement après un délai pour s'assurer que les streams sont prêts
    setTimeout(() => {
      this.forceStreamAttachment();
    }, 1000);
  }

  /**
   * Force l'attachement des streams aux éléments vidéo
   */
  private forceStreamAttachment(): void {
    console.log('🔄 [CallService] Forcing stream attachment...');

    // Forcer l'attachement du stream local
    if (this.localStream && this.localVideoElement) {
      if (!this.localVideoElement.srcObject) {
        console.log('🔗 [CallService] Force attaching local stream');
        this.localVideoElement.srcObject = this.localStream;
        this.localVideoElement.muted = true;
        this.localVideoElement.autoplay = true;
        this.localVideoElement
          .play()
          .catch((e) =>
            console.warn('⚠️ [CallService] Force local play failed:', e)
          );
      }
    }

    // Forcer l'attachement du stream distant
    if (this.remoteStream && this.remoteVideoElement) {
      if (!this.remoteVideoElement.srcObject) {
        console.log('🔗 [CallService] Force attaching remote stream');
        this.remoteVideoElement.srcObject = this.remoteStream;
        this.remoteVideoElement.muted = false;
        this.remoteVideoElement.volume = 1;
        this.remoteVideoElement.autoplay = true;
        this.remoteVideoElement
          .play()
          .catch((e) =>
            console.warn('⚠️ [CallService] Force remote play failed:', e)
          );
      }
    }

    // Vérifier l'état après forçage
    console.log('📊 [CallService] Stream attachment status:', {
      localAttached: !!this.localVideoElement?.srcObject,
      remoteAttached: !!this.remoteVideoElement?.srcObject,
      localStreamExists: !!this.localStream,
      remoteStreamExists: !!this.remoteStream,
    });
  }

  /**
   * Force l'attachement du stream distant à tous les éléments vidéo disponibles
   */
  private forceAttachRemoteStream(): void {
    console.log(
      '🔧 [CallService] Force attaching remote stream to all video elements...'
    );

    if (!this.remoteStream) {
      console.warn('⚠️ [CallService] No remote stream to attach');
      return;
    }

    // Trouver tous les éléments vidéo dans le DOM
    const videos = document.querySelectorAll('video');
    console.log(`📺 [CallService] Found ${videos.length} video elements`);

    // Attacher le stream distant au premier élément vidéo libre (pas le local)
    let attached = false;
    videos.forEach((video, index) => {
      if (!attached && index > 0 && !video.srcObject) {
        console.log(
          `🔗 [CallService] Attaching remote stream to Video ${index}`
        );
        video.srcObject = this.remoteStream;
        video.muted = false;
        video.volume = 1;
        video.autoplay = true;

        // Activer les tracks audio
        this.remoteStream!.getAudioTracks().forEach((track) => {
          track.enabled = true;
          console.log(
            '🔊 [CallService] Audio track enabled on forced attachment'
          );
        });

        // Forcer la lecture
        video
          .play()
          .then(() => {
            console.log(
              `✅ [CallService] Video ${index} playing with remote stream`
            );
          })
          .catch((e) => {
            console.warn(`⚠️ [CallService] Video ${index} play failed:`, e);
          });

        attached = true;
      }
    });

    if (!attached) {
      console.warn(
        '⚠️ [CallService] Could not find available video element for remote stream'
      );
    }
  }

  /**
   * Force l'attachement du stream local au premier élément vidéo disponible
   */
  private forceAttachLocalStream(): void {
    console.log(
      '🔧 [CallService] Force attaching local stream to video elements...'
    );

    if (!this.localStream) {
      console.warn('⚠️ [CallService] No local stream to attach');
      return;
    }

    // Trouver tous les éléments vidéo dans le DOM
    const videos = document.querySelectorAll('video');
    console.log(
      `📺 [CallService] Found ${videos.length} video elements for local stream`
    );

    // Attacher le stream local au premier élément vidéo libre
    let attached = false;
    videos.forEach((video, index) => {
      if (!attached && !video.srcObject) {
        console.log(
          `🔗 [CallService] Attaching local stream to Video ${index}`
        );
        video.srcObject = this.localStream;
        video.muted = true; // Local toujours muet
        video.volume = 0;
        video.autoplay = true;

        // Forcer la lecture
        video
          .play()
          .then(() => {
            console.log(
              `✅ [CallService] Video ${index} playing with local stream`
            );
          })
          .catch((e) => {
            console.warn(
              `⚠️ [CallService] Video ${index} local play failed:`,
              e
            );
          });

        attached = true;
      }
    });

    if (!attached) {
      console.warn(
        '⚠️ [CallService] Could not find available video element for local stream'
      );
    }
  }

  /**
   * Configure la lecture audio pour les éléments vidéo
   */
  private setupAudioPlayback(
    localVideo: HTMLVideoElement,
    remoteVideo: HTMLVideoElement
  ): void {
    console.log('🔊 [CallService] Setting up audio playback');

    // Configurer les propriétés audio
    localVideo.volume = 0; // Local toujours muet pour éviter l'écho
    remoteVideo.volume = 1; // Remote avec volume maximum

    // Forcer l'autoplay
    localVideo.autoplay = true;
    remoteVideo.autoplay = true;

    // Événements pour débugger
    remoteVideo.addEventListener('loadedmetadata', () => {
      console.log('🎵 [CallService] Remote video metadata loaded');
    });

    remoteVideo.addEventListener('canplay', () => {
      console.log('🎵 [CallService] Remote video can play');
      this.ensureAudioPlayback(remoteVideo);
    });

    remoteVideo.addEventListener('play', () => {
      console.log('🎵 [CallService] Remote video started playing');
    });

    remoteVideo.addEventListener('pause', () => {
      console.log('⏸️ [CallService] Remote video paused');
    });
  }

  /**
   * Force la lecture audio
   */
  private ensureAudioPlayback(videoElement: HTMLVideoElement): void {
    console.log(
      '🔊 [CallService] Ensuring audio playback for element:',
      videoElement === this.localVideoElement ? 'local' : 'remote'
    );

    // Forcer la lecture
    videoElement
      .play()
      .then(() => {
        console.log(
          '✅ [CallService] Video/audio playback started successfully'
        );
      })
      .catch((error) => {
        console.warn(
          '⚠️ [CallService] Autoplay prevented, user interaction required:',
          error
        );

        // Essayer de jouer après interaction utilisateur
        const playOnInteraction = () => {
          videoElement
            .play()
            .then(() => {
              console.log(
                '✅ [CallService] Video/audio playback started after user interaction'
              );
              document.removeEventListener('click', playOnInteraction);
              document.removeEventListener('keydown', playOnInteraction);
            })
            .catch((err) => {
              console.error(
                '❌ [CallService] Failed to play after interaction:',
                err
              );
            });
        };

        document.addEventListener('click', playOnInteraction);
        document.addEventListener('keydown', playOnInteraction);
      });
  }

  /**
   * Initie un appel WebRTC
   */
  initiateCall(
    recipientId: string,
    callType: CallType,
    conversationId?: string
  ): Observable<Call> {
    console.log('🔄 [CallService] Initiating call:', {
      recipientId,
      callType,
      conversationId,
    });

    if (!recipientId) {
      const error = new Error('Recipient ID is required');
      console.error('❌ [CallService] initiateCall error:', error);
      return throwError(() => error);
    }

    // Générer un ID unique pour l'appel
    const callId = `call_${Date.now()}_${Math.random()
      .toString(36)
      .substr(2, 9)}`;

    // Créer une vraie offre WebRTC
    return from(this.createWebRTCOffer(callType)).pipe(
      switchMap((offer) => {
        const variables = {
          recipientId,
          callType: callType,
          callId,
          offer: JSON.stringify(offer),
          conversationId,
        };

        console.log(
          '📤 [CallService] Sending initiate call mutation:',
          variables
        );

        return this.apollo
          .mutate<{ initiateCall: Call }>({
            mutation: INITIATE_CALL_MUTATION,
            variables,
          })
          .pipe(
            map((result) => {
              console.log(
                '✅ [CallService] Call initiated successfully:',
                result
              );

              if (!result.data?.initiateCall) {
                throw new Error('No call data received from server');
              }

              const call = result.data.initiateCall;
              console.log('📞 [CallService] Call details:', {
                id: call.id,
                type: call.type,
                status: call.status,
                caller: call.caller?.username,
                recipient: call.recipient?.username,
              });

              // Mettre à jour l'état local
              this.activeCall.next(call);

              return call;
            }),
            catchError((error) => {
              console.error('❌ [CallService] initiateCall error:', error);
              this.logger.error('Error initiating call:', error);

              let errorMessage = "Erreur lors de l'initiation de l'appel";
              if (error.networkError) {
                errorMessage = 'Erreur de connexion réseau';
              } else if (error.graphQLErrors?.length > 0) {
                errorMessage = error.graphQLErrors[0].message || errorMessage;
              }

              return throwError(() => new Error(errorMessage));
            })
          );
      }),
      catchError((error) => {
        console.error('❌ [CallService] WebRTC error:', error);
        return throwError(() => new Error('Erreur WebRTC: ' + error.message));
      })
    );
  }

  /**
   * Crée une offre WebRTC complète avec médias
   */
  private async createWebRTCOffer(
    callType: CallType
  ): Promise<RTCSessionDescriptionInit> {
    try {
      // Réinitialiser la PeerConnection si nécessaire
      if (!this.peerConnection) {
        this.initializeWebRTC();
      }

      // Obtenir les médias utilisateur
      const stream = await this.getUserMedia(callType);

      // Ajouter le stream à la PeerConnection
      this.addLocalStreamToPeerConnection(stream);

      // Créer l'offre
      const offer = await this.createOffer();

      console.log('✅ [CallService] WebRTC offer created successfully');
      return offer;
    } catch (error) {
      console.error('❌ [CallService] Failed to create WebRTC offer:', error);
      throw error;
    }
  }

  /**
   * Accepte un appel entrant
   */
  acceptCall(incomingCall: IncomingCall): Observable<Call> {
    console.log('🔄 [CallService] Accepting call:', incomingCall.id);

    // Créer une réponse WebRTC simplifiée
    return from(this.createSimpleAnswer(incomingCall)).pipe(
      switchMap((answer) => {
        return this.apollo
          .mutate<{ acceptCall: Call }>({
            mutation: ACCEPT_CALL_MUTATION,
            variables: {
              callId: incomingCall.id,
              answer: JSON.stringify(answer),
            },
          })
          .pipe(
            switchMap((result) => {
              console.log(
                '✅ [CallService] Call accepted successfully:',
                result
              );

              if (!result.data?.acceptCall) {
                throw new Error('No call data received from server');
              }

              const call = result.data.acceptCall;

              // Arrêter la sonnerie
              this.stop('ringtone');
              this.play('call-connected');

              // Démarrer les médias pour l'appel de manière asynchrone
              return from(this.startMediaForCall(incomingCall, call));
            }),
            catchError((error) => {
              console.error('❌ [CallService] acceptCall error:', error);
              this.logger.error('Error accepting call:', error);
              return throwError(
                () => new Error("Erreur lors de l'acceptation de l'appel")
              );
            })
          );
      }),
      catchError((error) => {
        console.error('❌ [CallService] WebRTC answer error:', error);
        return throwError(() => new Error('Erreur WebRTC: ' + error.message));
      })
    );
  }

  /**
   * Crée une réponse WebRTC simplifiée - SANS CONFLIT
   */
  private async createSimpleAnswer(
    incomingCall: IncomingCall
  ): Promise<RTCSessionDescriptionInit> {
    try {
      console.log(
        '🔄 [CallService] Creating simple WebRTC answer:',
        incomingCall.id
      );

      // Initialiser WebRTC si nécessaire
      if (!this.peerConnection) {
        console.log('🔧 [CallService] Initializing WebRTC for answer');
        this.initializeWebRTC();
      }

      // Vérifier l'offre
      if (!incomingCall.offer) {
        throw new Error('No offer received in incoming call');
      }

      const offer = JSON.parse(incomingCall.offer);
      if (!offer || !offer.type || !offer.sdp) {
        throw new Error('Invalid offer format received');
      }

      // Créer un stream audio simple (éviter les conflits getUserMedia)
      try {
        if (!this.localStream) {
          console.log('🎤 [CallService] Creating simple audio stream...');
          this.localStream = await navigator.mediaDevices.getUserMedia({
            audio: true,
            video: false,
          });

          // Ajouter à la peer connection
          this.localStream.getTracks().forEach((track) => {
            this.peerConnection!.addTrack(track, this.localStream!);
          });
        }
      } catch (streamError) {
        console.warn(
          '⚠️ [CallService] Stream creation failed, continuing without local stream:',
          streamError
        );
      }

      // Créer la réponse avec la méthode existante
      const answer = await this.createAnswer(offer);

      console.log('✅ [CallService] Simple WebRTC answer created successfully');
      return answer;
    } catch (error) {
      console.error(
        '❌ [CallService] Failed to create simple WebRTC answer:',
        error
      );
      throw error;
    }
  }

  /**
   * Rejette un appel entrant
   */
  rejectCall(callId: string, reason?: string): Observable<CallSuccess> {
    console.log('🔄 [CallService] Rejecting call:', callId, reason);

    return this.apollo
      .mutate<{ rejectCall: CallSuccess }>({
        mutation: REJECT_CALL_MUTATION,
        variables: {
          callId,
          reason: reason || 'User rejected',
        },
      })
      .pipe(
        map((result) => {
          console.log('✅ [CallService] Call rejected successfully:', result);

          if (!result.data?.rejectCall) {
            throw new Error('No response received from server');
          }

          // Mettre à jour l'état local
          this.incomingCall.next(null);
          this.activeCall.next(null);

          // Arrêter la sonnerie
          this.stop('ringtone');

          return result.data.rejectCall;
        }),
        catchError((error) => {
          console.error('❌ [CallService] rejectCall error:', error);
          this.logger.error('Error rejecting call:', error);
          return throwError(() => new Error("Erreur lors du rejet de l'appel"));
        })
      );
  }

  /**
   * Termine un appel en cours
   */
  endCall(callId: string): Observable<CallSuccess> {
    console.log('🔄 [CallService] Ending call:', callId);

    return this.apollo
      .mutate<{ endCall: CallSuccess }>({
        mutation: END_CALL_MUTATION,
        variables: {
          callId,
          feedback: null, // Pas de feedback pour l'instant
        },
      })
      .pipe(
        map((result) => {
          console.log('✅ [CallService] Call ended successfully:', result);

          if (!result.data?.endCall) {
            throw new Error('No response received from server');
          }

          // Mettre à jour l'état local
          this.activeCall.next(null);
          this.incomingCall.next(null);

          // Arrêter tous les sons
          this.stopAllSounds();
          this.play('call-end');

          // Nettoyer les ressources WebRTC
          this.cleanupWebRTC();

          return result.data.endCall;
        }),
        catchError((error) => {
          console.error('❌ [CallService] endCall error:', error);
          this.logger.error('Error ending call:', error);
          return throwError(
            () => new Error("Erreur lors de la fin de l'appel")
          );
        })
      );
  }

  /**
   * Bascule l'état des médias (audio/vidéo) pendant un appel
   */
  toggleMedia(
    callId: string,
    enableVideo?: boolean,
    enableAudio?: boolean
  ): Observable<CallSuccess> {
    console.log('🔄 [CallService] Toggling media:', {
      callId,
      enableVideo,
      enableAudio,
    });

    return this.apollo
      .mutate<{ toggleCallMedia: CallSuccess }>({
        mutation: TOGGLE_CALL_MEDIA_MUTATION,
        variables: {
          callId,
          video: enableVideo,
          audio: enableAudio,
        },
      })
      .pipe(
        map((result) => {
          console.log('✅ [CallService] Media toggled successfully:', result);

          if (!result.data?.toggleCallMedia) {
            throw new Error('No response received from server');
          }

          return result.data.toggleCallMedia;
        }),
        catchError((error) => {
          console.error('❌ [CallService] toggleMedia error:', error);
          this.logger.error('Error toggling media:', error);
          return throwError(
            () => new Error('Erreur lors du changement des médias')
          );
        })
      );
  }

  /**
   * Démarre les médias pour un appel accepté
   */
  private async startMediaForCall(
    incomingCall: IncomingCall,
    call: Call
  ): Promise<Call> {
    console.log('🎥 [CallService] Call connected - playing connection sound');

    // Jouer le son de connexion
    this.play('call-connected');

    // Mettre à jour l'état local
    this.activeCall.next(call);
    this.incomingCall.next(null); // Supprimer l'appel entrant

    return call;
  }

  /**
   * Active/désactive la vidéo
   */
  toggleVideo(): boolean {
    this.isVideoEnabled = !this.isVideoEnabled;
    console.log('📹 [CallService] Video toggled:', this.isVideoEnabled);
    return this.isVideoEnabled;
  }

  /**
   * Active/désactive l'audio
   */
  toggleAudio(): boolean {
    this.isAudioEnabled = !this.isAudioEnabled;
    console.log('🎤 [CallService] Audio toggled:', this.isAudioEnabled);
    return this.isAudioEnabled;
  }

  /**
   * Obtient l'état de la vidéo
   */
  getVideoEnabled(): boolean {
    return this.isVideoEnabled;
  }

  /**
   * Obtient l'état de l'audio
   */
  getAudioEnabled(): boolean {
    return this.isAudioEnabled;
  }

  /**
   * Arrête tous les sons
   */
  private stopAllSounds(): void {
    console.log('🔇 [CallService] Stopping all sounds');
    Object.keys(this.sounds).forEach((name) => {
      this.stop(name);
    });
  }

  /**
   * Active les sons après interaction utilisateur (pour contourner les restrictions du navigateur)
   */
  public enableSounds(): void {
    console.log('🔊 [CallService] Enabling sounds after user interaction');
    Object.values(this.sounds).forEach((sound) => {
      if (sound) {
        sound.muted = false;
        sound.volume = 0.7;
        // Jouer et arrêter immédiatement pour "débloquer" l'audio
        sound
          .play()
          .then(() => {
            sound.pause();
            sound.currentTime = 0;
          })
          .catch(() => {
            // Ignorer les erreurs ici
          });
      }
    });
  }

  /**
   * Nettoie les ressources WebRTC
   */
  private cleanupWebRTC(): void {
    console.log('🧹 [CallService] Cleaning up WebRTC resources');

    // Arrêter les tracks du stream local
    if (this.localStream) {
      this.localStream.getTracks().forEach((track) => {
        track.stop();
      });
      this.localStream = null;
    }

    // Nettoyer les éléments vidéo
    if (this.localVideoElement) {
      this.localVideoElement.srcObject = null;
    }
    if (this.remoteVideoElement) {
      this.remoteVideoElement.srcObject = null;
    }

    // Fermer la PeerConnection
    if (this.peerConnection) {
      this.peerConnection.close();
      this.peerConnection = null;
    }

    this.remoteStream = null;
  }

  /**
   * Getters pour l'état
   */
  get hasLocalStream(): boolean {
    return !!this.localStream;
  }

  get hasRemoteStream(): boolean {
    return !!this.remoteStream;
  }

  get hasLocalVideoElement(): boolean {
    return !!this.localVideoElement;
  }

  get hasRemoteVideoElement(): boolean {
    return !!this.remoteVideoElement;
  }

  ngOnDestroy(): void {
    this.stopAllSounds();
    this.cleanupWebRTC();
  }
}
