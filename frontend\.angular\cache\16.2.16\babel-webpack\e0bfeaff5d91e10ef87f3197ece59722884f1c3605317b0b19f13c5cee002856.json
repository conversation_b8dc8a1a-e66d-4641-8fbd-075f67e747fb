{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { ActiveCallComponent } from '../active-call/active-call.component';\nimport { IncomingCallComponent } from '../incoming-call/incoming-call.component';\nimport { CallTestComponent } from '../call-test/call-test.component';\nimport * as i0 from \"@angular/core\";\nexport class CallModule {\n  static {\n    this.ɵfac = function CallModule_Factory(t) {\n      return new (t || CallModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: CallModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, FormsModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(CallModule, {\n    declarations: [ActiveCallComponent, IncomingCallComponent, CallTestComponent],\n    imports: [CommonModule, FormsModule],\n    exports: [ActiveCallComponent, IncomingCallComponent, CallTestComponent]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "ActiveCallComponent", "IncomingCallComponent", "CallTestComponent", "CallModule", "declarations", "imports", "exports"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\components\\call\\call.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { ActiveCallComponent } from '../active-call/active-call.component';\nimport { IncomingCallComponent } from '../incoming-call/incoming-call.component';\nimport { CallTestComponent } from '../call-test/call-test.component';\n\n@NgModule({\n  declarations: [ActiveCallComponent, IncomingCallComponent, CallTestComponent],\n  imports: [CommonModule, FormsModule],\n  exports: [ActiveCallComponent, IncomingCallComponent, CallTestComponent],\n})\nexport class CallModule {}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,mBAAmB,QAAQ,sCAAsC;AAC1E,SAASC,qBAAqB,QAAQ,0CAA0C;AAChF,SAASC,iBAAiB,QAAQ,kCAAkC;;AAOpE,OAAM,MAAOC,UAAU;;;uBAAVA,UAAU;IAAA;EAAA;;;YAAVA;IAAU;EAAA;;;gBAHXL,YAAY,EAAEC,WAAW;IAAA;EAAA;;;2EAGxBI,UAAU;IAAAC,YAAA,GAJNJ,mBAAmB,EAAEC,qBAAqB,EAAEC,iBAAiB;IAAAG,OAAA,GAClEP,YAAY,EAAEC,WAAW;IAAAO,OAAA,GACzBN,mBAAmB,EAAEC,qBAAqB,EAAEC,iBAAiB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}