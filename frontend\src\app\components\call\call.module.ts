import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ActiveCallComponent } from '../active-call/active-call.component';
import { IncomingCallComponent } from '../incoming-call/incoming-call.component';
import { CallTestComponent } from '../call-test/call-test.component';

@NgModule({
  declarations: [ActiveCallComponent, IncomingCallComponent, CallTestComponent],
  imports: [CommonModule, FormsModule],
  exports: [ActiveCallComponent, IncomingCallComponent, CallTestComponent],
})
export class CallModule {}
