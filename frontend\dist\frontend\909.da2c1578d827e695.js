"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[909],{4909:(oe,m,a)=>{a.r(m),a.d(m,{ProjectsModule:()=>te});var s=a(177),u=a(6647),v=a(5312),e=a(7705),p=a(1873),g=a(9271),f=a(7169),h=a(4704);function x(r,i){if(1&r&&(e.qSk(),e.joV(),e.j41(0,"div",21)(1,"div",22)(2,"div",23)(3,"div")(4,"p",24),e.<PERSON><PERSON>(5,"Total"),e.k0s(),e.j41(6,"p",25),e.EFF(7),e.k0s(),e.j41(8,"p",26),e.<PERSON><PERSON>(9,"Projets"),e.k0s()(),e.j41(10,"div",27),e.qSk(),e.j41(11,"svg",28),e.nrm(12,"path",29),e.k0s()()()(),e.joV(),e.j41(13,"div",22)(14,"div",23)(15,"div")(16,"p",30),e.EFF(17,"Rendus"),e.k0s(),e.j41(18,"p",31),e.EFF(19),e.k0s(),e.j41(20,"p",26),e.EFF(21,"Compl\xe9t\xe9s"),e.k0s()(),e.j41(22,"div",32),e.qSk(),e.j41(23,"svg",33),e.nrm(24,"path",34),e.k0s()()()(),e.joV(),e.j41(25,"div",22)(26,"div",23)(27,"div")(28,"p",35),e.EFF(29,"En attente"),e.k0s(),e.j41(30,"p",36),e.EFF(31),e.k0s(),e.j41(32,"p",26),e.EFF(33,"\xc0 rendre"),e.k0s()(),e.j41(34,"div",37),e.qSk(),e.j41(35,"svg",38),e.nrm(36,"path",39),e.k0s()()()(),e.joV(),e.j41(37,"div",22)(38,"div",23)(39,"div")(40,"p",24),e.EFF(41,"Taux"),e.k0s(),e.j41(42,"p",25),e.EFF(43),e.k0s(),e.j41(44,"p",26),e.EFF(45,"R\xe9ussite"),e.k0s()(),e.j41(46,"div",27),e.qSk(),e.j41(47,"svg",28),e.nrm(48,"path",40),e.k0s()()()()()),2&r){const t=e.XpG();e.R7$(7),e.JRh(t.getTotalProjects()),e.R7$(12),e.JRh(t.getRendusCount()),e.R7$(12),e.JRh(t.getPendingCount()),e.R7$(12),e.SpI("",t.getSuccessRate(),"%")}}function b(r,i){if(1&r&&(e.qSk(),e.joV(),e.j41(0,"div",41)(1,"div",42)(2,"h3",43),e.EFF(3,"Progression globale"),e.k0s(),e.j41(4,"span",44),e.EFF(5),e.k0s()(),e.j41(6,"div",45),e.nrm(7,"div",46),e.k0s(),e.j41(8,"div",47)(9,"span"),e.EFF(10),e.k0s(),e.j41(11,"span"),e.EFF(12),e.k0s()()()),2&r){const t=e.XpG();e.R7$(5),e.SpI("",t.getSuccessRate(),"% compl\xe9t\xe9"),e.R7$(2),e.xc7("width",t.getSuccessRate(),"%"),e.R7$(3),e.SpI("",t.getRendusCount()," projets rendus"),e.R7$(2),e.SpI("",t.getPendingCount()," en attente")}}function j(r,i){1&r&&(e.qSk(),e.joV(),e.j41(0,"div",48)(1,"div",49),e.nrm(2,"div",50)(3,"div",51),e.k0s()())}function w(r,i){1&r&&(e.qSk(),e.joV(),e.j41(0,"div",52)(1,"div",53),e.qSk(),e.j41(2,"svg",54),e.nrm(3,"path",55),e.k0s(),e.joV(),e.nrm(4,"div",51),e.k0s(),e.j41(5,"h3",56),e.EFF(6," Aucun projet disponible "),e.k0s(),e.j41(7,"p",57),e.EFF(8," Vos missions appara\xeetront ici "),e.k0s()())}function F(r,i){if(1&r&&(e.j41(0,"div",88)(1,"div",89)(2,"div",90),e.qSk(),e.j41(3,"svg",91),e.nrm(4,"path",92),e.k0s(),e.joV(),e.nrm(5,"div",93),e.k0s(),e.j41(6,"span",94),e.EFF(7,"Document"),e.k0s()(),e.j41(8,"a",95),e.nrm(9,"div",96)(10,"div",97),e.j41(11,"span",98),e.qSk(),e.j41(12,"svg",99),e.nrm(13,"path",100),e.k0s(),e.EFF(14," T\xe9l\xe9charger "),e.k0s()()()),2&r){const t=i.$implicit,o=e.XpG(4);e.R7$(8),e.Y8G("href",o.getFileUrl(t),e.B4B),e.BMQ("download",o.getFileName(t))}}function _(r,i){if(1&r&&(e.j41(0,"div",84)(1,"h4",85),e.EFF(2," Fichiers "),e.k0s(),e.j41(3,"div",86),e.DNE(4,F,15,2,"div",87),e.k0s()()),2&r){const t=e.XpG().$implicit;e.R7$(4),e.Y8G("ngForOf",t.fichiers)}}function E(r,i){1&r&&(e.qex(0),e.j41(1,"span",101)(2,"div",79),e.qSk(),e.j41(3,"svg",102),e.nrm(4,"path",103),e.k0s(),e.joV(),e.nrm(5,"div",104),e.k0s(),e.j41(6,"span"),e.EFF(7,"Rendu"),e.k0s()(),e.bVm())}const S=function(r){return["/projects/submit",r]};function y(r,i){if(1&r&&(e.qex(0),e.j41(1,"a",105),e.nrm(2,"div",106)(3,"div",107),e.j41(4,"span",108),e.EFF(5," Rendre "),e.k0s()(),e.bVm()),2&r){const t=e.XpG().$implicit;e.R7$(1),e.Y8G("routerLink",e.eq3(1,S,t._id))}}const R=function(r){return["/projects/detail",r]};function C(r,i){if(1&r&&(e.j41(0,"div",60)(1,"div",61),e.nrm(2,"div",62)(3,"div",63),e.j41(4,"div",64)(5,"div",65)(6,"span",66),e.EFF(7),e.k0s()(),e.j41(8,"h3",67),e.EFF(9),e.k0s(),e.j41(10,"div",68)(11,"span",69),e.qSk(),e.j41(12,"svg",70),e.nrm(13,"path",71),e.k0s(),e.EFF(14),e.k0s(),e.joV(),e.j41(15,"span",72),e.EFF(16,"\u2022"),e.k0s(),e.j41(17,"span",69),e.qSk(),e.j41(18,"svg",70),e.nrm(19,"path",73),e.k0s(),e.EFF(20),e.nI1(21,"date"),e.k0s()()()(),e.joV(),e.j41(22,"div",74)(23,"p",75),e.EFF(24),e.k0s(),e.DNE(25,_,5,1,"div",76),e.j41(26,"div",77)(27,"a",78)(28,"div",79),e.qSk(),e.j41(29,"svg",80),e.nrm(30,"path",81),e.k0s(),e.joV(),e.nrm(31,"div",82),e.k0s(),e.j41(32,"span"),e.EFF(33,"D\xe9tails"),e.k0s()(),e.DNE(34,E,8,0,"ng-container",83),e.DNE(35,y,6,3,"ng-container",83),e.k0s()()()),2&r){const t=i.$implicit,o=e.XpG(2);e.R7$(6),e.Y8G("ngClass",o.getStatusClass(t)),e.R7$(1),e.SpI(" ",o.getStatusText(t)," "),e.R7$(2),e.SpI(" ",t.titre," "),e.R7$(5),e.SpI(" ",t.groupe||"Tous"," "),e.R7$(6),e.SpI(" ",e.i5U(21,10,t.dateLimite,"dd/MM/yyyy")," "),e.R7$(4),e.SpI(" ",t.description||"Aucune description"," "),e.R7$(1),e.Y8G("ngIf",t.fichiers&&t.fichiers.length>0),e.R7$(2),e.Y8G("routerLink",e.eq3(13,R,t._id)),e.R7$(7),e.Y8G("ngIf",o.isRendu(t._id)),e.R7$(1),e.Y8G("ngIf",!o.isRendu(t._id))}}function M(r,i){if(1&r&&(e.qSk(),e.joV(),e.j41(0,"div",58),e.DNE(1,C,36,15,"div",59),e.k0s()),2&r){const t=e.XpG();e.R7$(1),e.Y8G("ngForOf",t.projets)}}let I=(()=>{class r{constructor(t,o,n,d){this.projetService=t,this.authService=o,this.rendusService=n,this.fileService=d,this.projets=[],this.rendusMap=new Map,this.isLoading=!0,this.userGroup=""}ngOnInit(){this.userGroup=this.authService.getCurrentUser()?.groupe||"",this.loadProjets()}loadProjets(){this.isLoading=!0,this.projetService.getProjets().subscribe({next:t=>{this.projets=t,this.isLoading=!1,this.projets.forEach(o=>{o._id&&this.checkRenduStatus(o._id)})},error:t=>{console.error("Erreur lors du chargement des projets",t),this.isLoading=!1}})}checkRenduStatus(t){const o=this.authService.getCurrentUserId();o&&this.rendusService.checkRenduExists(t,o).subscribe({next:n=>{this.rendusMap.set(t,n)},error:n=>{console.error(`Erreur lors de la v\xe9rification du rendu pour le projet ${t}`,n)}})}getFileUrl(t){if(!t)return"";let o=t;if(t.includes("/")||t.includes("\\")){const n=t.split(/[\/\\]/);o=n[n.length-1]}return`${v.c.urlBackend}projets/telecharger/${o}`}getFileName(t){if(!t)return"fichier";if(t.includes("/")||t.includes("\\")){const o=t.split(/[\/\\]/);return o[o.length-1]}return t}isRendu(t){return!!t&&!0===this.rendusMap.get(t)}getTotalProjects(){return this.projets.length}getRendusCount(){return this.projets.filter(t=>t._id&&this.isRendu(t._id)).length}getPendingCount(){return this.projets.filter(t=>t._id&&!this.isRendu(t._id)).length}getSuccessRate(){return 0===this.projets.length?0:Math.round(this.getRendusCount()/this.projets.length*100)}getUrgentProjects(){const t=new Date,o=new Date;return o.setDate(o.getDate()+7),this.projets.filter(n=>{if(!n.dateLimite||this.isRendu(n._id))return!1;const d=new Date(n.dateLimite);return d>=t&&d<=o})}getExpiredProjects(){const t=new Date;return this.projets.filter(o=>!(!o.dateLimite||this.isRendu(o._id))&&new Date(o.dateLimite)<t)}getProjectStatus(t){if(this.isRendu(t._id))return"completed";if(!t.dateLimite)return"active";const o=new Date,n=new Date(t.dateLimite);if(n<o)return"expired";const d=new Date;return d.setDate(d.getDate()+7),n<=d?"urgent":"active"}getStatusClass(t){switch(this.getProjectStatus(t)){case"completed":return"bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400";case"urgent":return"bg-orange-100 dark:bg-orange-900/30 text-orange-800 dark:text-orange-400";case"expired":return"bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-400";default:return"bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-400"}}getStatusText(t){switch(this.getProjectStatus(t)){case"completed":return"Rendu";case"urgent":return"Urgent";case"expired":return"Expir\xe9";default:return"Actif"}}static{this.\u0275fac=function(o){return new(o||r)(e.rXU(p.e),e.rXU(g.V),e.rXU(f.R),e.rXU(h.E))}}static{this.\u0275cmp=e.VBU({type:r,selectors:[["app-project-list"]],decls:34,vars:5,consts:[[1,"min-h-screen","bg-[#edf1f4]","dark:bg-[#121212]","p-4","md:p-6","relative"],[1,"absolute","inset-0","overflow-hidden","pointer-events-none"],[1,"absolute","top-[15%]","left-[10%]","w-64","h-64","rounded-full","bg-gradient-to-br","from-[#4f5fad]/5","to-transparent","dark:from-[#6d78c9]/3","dark:to-transparent","blur-3xl"],[1,"absolute","bottom-[20%]","right-[10%]","w-80","h-80","rounded-full","bg-gradient-to-tl","from-[#4f5fad]/5","to-transparent","dark:from-[#6d78c9]/3","dark:to-transparent","blur-3xl"],[1,"absolute","inset-0","opacity-5","dark:opacity-[0.03]"],[1,"h-full","grid","grid-cols-12"],[1,"border-r","border-[#4f5fad]","dark:border-[#6d78c9]"],[1,"max-w-6xl","mx-auto","relative","z-10"],[1,"mb-8"],[1,"flex","flex-col","lg:flex-row","lg:justify-between","lg:items-center","mb-6"],[1,"text-3xl","font-bold","bg-gradient-to-r","from-[#3d4a85]","to-[#4f5fad]","dark:from-[#6d78c9]","dark:to-[#4f5fad]","bg-clip-text","text-transparent"],[1,"text-[#6d6870]","dark:text-[#a0a0a0]","text-sm","md:text-base","mt-1"],[1,"h-12","w-12","rounded-full","bg-gradient-to-br","from-[#3d4a85]","to-[#4f5fad]","dark:from-[#3d4a85]","dark:to-[#6d78c9]","flex","items-center","justify-center","text-white","shadow-lg","relative","group","overflow-hidden","mt-4","lg:mt-0"],[1,"absolute","inset-0","bg-gradient-to-r","from-[#3d4a85]","to-[#4f5fad]","dark:from-[#3d4a85]","dark:to-[#6d78c9]","rounded-full","opacity-0","group-hover:opacity-100","blur-xl","transition-opacity","duration-300"],["xmlns","http://www.w3.org/2000/svg","fill","none","viewBox","0 0 24 24","stroke","currentColor",1,"h-6","w-6","relative","z-10","group-hover:scale-110","transition-transform","duration-300"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"],["class","grid grid-cols-2 md:grid-cols-4 gap-4 mb-8",4,"ngIf"],["class","bg-white/80 dark:bg-[#1e1e1e]/80 backdrop-blur-sm rounded-xl p-6 border border-[#edf1f4]/50 dark:border-[#2a2a2a] shadow-md mb-8",4,"ngIf"],["class","flex justify-center my-12",4,"ngIf"],["class","bg-white dark:bg-[#1e1e1e] rounded-xl shadow-md dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)] p-8 text-center backdrop-blur-sm border border-[#edf1f4]/50 dark:border-[#2a2a2a]",4,"ngIf"],["class","grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",4,"ngIf"],[1,"grid","grid-cols-2","md:grid-cols-4","gap-4","mb-8"],[1,"bg-white/80","dark:bg-[#1e1e1e]/80","backdrop-blur-sm","rounded-xl","p-4","border","border-[#edf1f4]/50","dark:border-[#2a2a2a]","shadow-md","hover:shadow-lg","transition-all","duration-300","group"],[1,"flex","items-center","justify-between"],[1,"text-xs","font-medium","text-[#4f5fad]","dark:text-[#6d78c9]","uppercase","tracking-wider"],[1,"text-2xl","font-bold","text-[#3d4a85]","dark:text-[#6d78c9]"],[1,"text-xs","text-[#6d6870]","dark:text-[#a0a0a0]","mt-1"],[1,"bg-[#4f5fad]/10","dark:bg-[#6d78c9]/10","p-3","rounded-xl","group-hover:scale-110","transition-transform"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-5","h-5","text-[#4f5fad]","dark:text-[#6d78c9]"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M19 11H5m14-7H3a2 2 0 00-2 2v12a2 2 0 002 2h16a2 2 0 002-2V6a2 2 0 00-2-2z"],[1,"text-xs","font-medium","text-green-600","dark:text-green-400","uppercase","tracking-wider"],[1,"text-2xl","font-bold","text-green-700","dark:text-green-400"],[1,"bg-green-100","dark:bg-green-900/30","p-3","rounded-xl","group-hover:scale-110","transition-transform"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-5","h-5","text-green-600","dark:text-green-400"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"],[1,"text-xs","font-medium","text-orange-600","dark:text-orange-400","uppercase","tracking-wider"],[1,"text-2xl","font-bold","text-orange-700","dark:text-orange-400"],[1,"bg-orange-100","dark:bg-orange-900/30","p-3","rounded-xl","group-hover:scale-110","transition-transform"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-5","h-5","text-orange-600","dark:text-orange-400"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"],[1,"bg-white/80","dark:bg-[#1e1e1e]/80","backdrop-blur-sm","rounded-xl","p-6","border","border-[#edf1f4]/50","dark:border-[#2a2a2a]","shadow-md","mb-8"],[1,"flex","items-center","justify-between","mb-3"],[1,"text-lg","font-semibold","text-[#3d4a85]","dark:text-[#6d78c9]"],[1,"text-sm","font-medium","text-[#6d6870]","dark:text-[#a0a0a0]"],[1,"w-full","bg-[#edf1f4]","dark:bg-[#2a2a2a]","rounded-full","h-3"],[1,"bg-gradient-to-r","from-[#3d4a85]","to-[#4f5fad]","dark:from-[#6d78c9]","dark:to-[#4f5fad]","h-3","rounded-full","transition-all","duration-500"],[1,"flex","justify-between","text-xs","text-[#6d6870]","dark:text-[#a0a0a0]","mt-2"],[1,"flex","justify-center","my-12"],[1,"relative"],[1,"w-14","h-14","border-4","border-[#4f5fad]/20","dark:border-[#6d78c9]/20","border-t-[#4f5fad]","dark:border-t-[#6d78c9]","rounded-full","animate-spin"],[1,"absolute","inset-0","bg-[#4f5fad]/20","dark:bg-[#6d78c9]/20","blur-xl","rounded-full","transform","scale-150","-z-10"],[1,"bg-white","dark:bg-[#1e1e1e]","rounded-xl","shadow-md","dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)]","p-8","text-center","backdrop-blur-sm","border","border-[#edf1f4]/50","dark:border-[#2a2a2a]"],[1,"w-24","h-24","mx-auto","mb-6","bg-[#4f5fad]/10","dark:bg-[#6d78c9]/10","rounded-full","flex","items-center","justify-center","relative"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-12","h-12","text-[#4f5fad]","dark:text-[#6d78c9]","relative","z-10"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","1.5","d","M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"],[1,"text-xl","font-medium","bg-gradient-to-r","from-[#3d4a85]","to-[#4f5fad]","dark:from-[#6d78c9]","dark:to-[#4f5fad]","bg-clip-text","text-transparent","mb-2"],[1,"text-[#6d6870]","dark:text-[#a0a0a0]","mt-1"],[1,"grid","grid-cols-1","md:grid-cols-2","lg:grid-cols-3","gap-6"],["class","bg-white dark:bg-[#1e1e1e] rounded-xl shadow-md dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)] overflow-hidden hover:shadow-lg dark:hover:shadow-[0_8px_30px_rgba(0,0,0,0.3)] transition-all duration-300 hover:-translate-y-1 backdrop-blur-sm border border-[#edf1f4]/50 dark:border-[#2a2a2a] group",4,"ngFor","ngForOf"],[1,"bg-white","dark:bg-[#1e1e1e]","rounded-xl","shadow-md","dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)]","overflow-hidden","hover:shadow-lg","dark:hover:shadow-[0_8px_30px_rgba(0,0,0,0.3)]","transition-all","duration-300","hover:-translate-y-1","backdrop-blur-sm","border","border-[#edf1f4]/50","dark:border-[#2a2a2a]","group"],[1,"relative","overflow-hidden"],[1,"absolute","top-0","left-0","right-0","h-1","bg-gradient-to-r","from-[#3d4a85]","to-[#4f5fad]","dark:from-[#6d78c9]","dark:to-[#4f5fad]"],[1,"absolute","top-0","left-0","right-0","h-1","bg-gradient-to-r","from-[#3d4a85]","to-[#4f5fad]","dark:from-[#6d78c9]","dark:to-[#4f5fad]","opacity-0","group-hover:opacity-100","blur-md","transition-opacity","duration-300"],[1,"p-5","bg-white","dark:bg-[#1e1e1e]","relative"],[1,"absolute","top-3","right-3"],[1,"text-xs","px-2","py-1","rounded-full","font-medium","backdrop-blur-sm",3,"ngClass"],[1,"text-lg","font-bold","pr-16","bg-gradient-to-r","from-[#3d4a85]","to-[#4f5fad]","dark:from-[#6d78c9]","dark:to-[#4f5fad]","bg-clip-text","text-transparent","group-hover:scale-[1.01]","transition-transform","duration-300","origin-left"],[1,"flex","items-center","mt-2","text-xs","space-x-2"],[1,"bg-[#4f5fad]/10","dark:bg-[#6d78c9]/10","text-[#4f5fad]","dark:text-[#6d78c9]","px-2","py-0.5","rounded-full","backdrop-blur-sm","flex","items-center"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-3","h-3","mr-1"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"],[1,"text-[#6d6870]","dark:text-[#a0a0a0]"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"],[1,"p-5"],[1,"text-sm","text-[#6d6870]","dark:text-[#a0a0a0]","mb-4","line-clamp-3"],["class","mb-4",4,"ngIf"],[1,"flex","justify-between","items-center","pt-3","border-t","border-[#edf1f4]/50","dark:border-[#2a2a2a]"],[1,"text-[#4f5fad]","dark:text-[#6d78c9]","hover:text-[#3d4a85]","dark:hover:text-[#4f5fad]","text-sm","font-medium","flex","items-center","transition-colors","relative","group/details",3,"routerLink"],[1,"relative","mr-1"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-4","h-4","relative","z-10","group-hover/details:scale-110","transition-transform"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"],[1,"absolute","inset-0","bg-[#4f5fad]/20","dark:bg-[#6d78c9]/20","opacity-0","group-hover/details:opacity-100","transition-opacity","blur-md","rounded-full"],[4,"ngIf"],[1,"mb-4"],[1,"text-xs","font-semibold","text-[#6d6870]","dark:text-[#a0a0a0]","uppercase","tracking-wider","mb-2"],[1,"space-y-2"],["class","flex items-center justify-between bg-[#edf1f4]/70 dark:bg-[#2a2a2a]/70 rounded-lg p-2.5 backdrop-blur-sm group/file hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] transition-colors",4,"ngFor","ngForOf"],[1,"flex","items-center","justify-between","bg-[#edf1f4]/70","dark:bg-[#2a2a2a]/70","rounded-lg","p-2.5","backdrop-blur-sm","group/file","hover:bg-[#edf1f4]","dark:hover:bg-[#2a2a2a]","transition-colors"],[1,"flex","items-center","truncate"],[1,"relative","mr-2"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-4","h-4","text-[#4f5fad]","dark:text-[#6d78c9]","relative","z-10","group-hover/file:scale-110","transition-transform"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"],[1,"absolute","inset-0","bg-[#4f5fad]/20","dark:bg-[#6d78c9]/20","opacity-0","group-hover/file:opacity-100","transition-opacity","blur-md","rounded-full"],[1,"text-xs","text-[#6d6870]","dark:text-[#a0a0a0]","truncate"],["download","",1,"relative","overflow-hidden","group/download",3,"href"],[1,"absolute","inset-0","bg-gradient-to-r","from-[#3d4a85]","to-[#4f5fad]","dark:from-[#3d4a85]","dark:to-[#6d78c9]","rounded-lg","transition-transform","duration-300","group-hover/download:scale-105"],[1,"absolute","inset-0","bg-gradient-to-r","from-[#3d4a85]","to-[#4f5fad]","dark:from-[#3d4a85]","dark:to-[#6d78c9]","rounded-lg","opacity-0","group-hover/download:opacity-100","blur-md","transition-opacity","duration-300"],[1,"relative","flex","items-center","text-white","text-xs","px-3","py-1","rounded-lg","transition-all","z-10"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-3","h-3","mr-1","group-hover/download:scale-110","transition-transform"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"],[1,"bg-gradient-to-r","from-green-100","to-green-50","dark:from-green-900/30","dark:to-green-800/30","text-green-800","dark:text-green-400","text-xs","px-3","py-1.5","rounded-full","flex","items-center","shadow-sm","backdrop-blur-sm"],["fill","currentColor","viewBox","0 0 20 20",1,"w-3","h-3","relative","z-10"],["fill-rule","evenodd","d","M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z","clip-rule","evenodd"],[1,"absolute","inset-0","bg-green-500/20","blur-md","rounded-full","transform","scale-150","-z-10"],[1,"relative","overflow-hidden","group/submit",3,"routerLink"],[1,"absolute","inset-0","bg-gradient-to-r","from-[#3d4a85]","to-[#4f5fad]","dark:from-[#3d4a85]","dark:to-[#6d78c9]","rounded-lg","transition-transform","duration-300","group-hover/submit:scale-105"],[1,"absolute","inset-0","bg-gradient-to-r","from-[#3d4a85]","to-[#4f5fad]","dark:from-[#3d4a85]","dark:to-[#6d78c9]","rounded-lg","opacity-0","group-hover/submit:opacity-100","blur-md","transition-opacity","duration-300"],[1,"relative","flex","items-center","text-white","text-sm","font-medium","px-3","py-1.5","rounded-lg","transition-all","z-10"]],template:function(o,n){1&o&&(e.j41(0,"div",0)(1,"div",1),e.nrm(2,"div",2)(3,"div",3),e.j41(4,"div",4)(5,"div",5),e.nrm(6,"div",6)(7,"div",6)(8,"div",6)(9,"div",6)(10,"div",6)(11,"div",6)(12,"div",6)(13,"div",6)(14,"div",6)(15,"div",6)(16,"div",6),e.k0s()()(),e.j41(17,"div",7)(18,"div",8)(19,"div",9)(20,"div")(21,"h1",10),e.EFF(22," Mes Projets "),e.k0s(),e.j41(23,"p",11),e.EFF(24," G\xe9rez vos missions acad\xe9miques et suivez vos rendus "),e.k0s()(),e.j41(25,"div",12),e.nrm(26,"div",13),e.qSk(),e.j41(27,"svg",14),e.nrm(28,"path",15),e.k0s()()(),e.DNE(29,x,49,4,"div",16),e.DNE(30,b,13,5,"div",17),e.k0s(),e.DNE(31,j,4,0,"div",18),e.DNE(32,w,9,0,"div",19),e.DNE(33,M,2,1,"div",20),e.k0s()()),2&o&&(e.R7$(29),e.Y8G("ngIf",!n.isLoading),e.R7$(1),e.Y8G("ngIf",!n.isLoading&&n.projets.length>0),e.R7$(1),e.Y8G("ngIf",n.isLoading),e.R7$(1),e.Y8G("ngIf",!n.isLoading&&0===n.projets.length),e.R7$(1),e.Y8G("ngIf",!n.isLoading))},dependencies:[s.YU,s.Sq,s.bT,u.Wk,s.vh],styles:[".badge[_ngcontent-%COMP%]{display:inline-flex;align-items:center;padding:.25rem .5rem;border-radius:9999px;font-size:.75rem;font-weight:500}.badge-group[_ngcontent-%COMP%], .badge-deadline[_ngcontent-%COMP%]{background-color:#fff3;color:#fff}.line-clamp-3[_ngcontent-%COMP%]{display:-webkit-box;-webkit-line-clamp:3;-webkit-box-orient:vertical;overflow:hidden}"]})}}return r})();function V(r,i){1&r&&(e.j41(0,"div",30)(1,"div",31),e.nrm(2,"div",32)(3,"div",33),e.k0s()())}function D(r,i){if(1&r&&(e.j41(0,"div",78)(1,"div",79)(2,"div",80)(3,"div",81)(4,"div",82),e.qSk(),e.j41(5,"svg",39),e.nrm(6,"path",83),e.k0s()(),e.joV(),e.j41(7,"div",84)(8,"p",85),e.EFF(9),e.k0s(),e.j41(10,"p",86),e.EFF(11,"Document de projet"),e.k0s()()(),e.j41(12,"a",87)(13,"div",19),e.qSk(),e.j41(14,"svg",88),e.nrm(15,"path",89),e.k0s(),e.joV(),e.j41(16,"span"),e.EFF(17,"T\xe9l\xe9charger"),e.k0s()()()()()()),2&r){const t=i.$implicit,o=e.XpG(3);e.R7$(9),e.SpI(" ",o.getFileName(t)," "),e.R7$(3),e.Y8G("href",o.getFileUrl(t),e.B4B)}}function $(r,i){if(1&r&&(e.j41(0,"div",76),e.DNE(1,D,18,2,"div",77),e.k0s()),2&r){const t=e.XpG(2);e.R7$(1),e.Y8G("ngForOf",t.projet.fichiers)}}function P(r,i){1&r&&(e.j41(0,"div",90)(1,"div",91)(2,"div",92),e.qSk(),e.j41(3,"svg",93),e.nrm(4,"path",46),e.k0s()(),e.joV(),e.j41(5,"p",25),e.EFF(6,"Aucun fichier joint \xe0 ce projet"),e.k0s()()())}function L(r,i){1&r&&(e.qex(0),e.j41(1,"div",94)(2,"div",26)(3,"div",66),e.qSk(),e.j41(4,"svg",67),e.nrm(5,"path",95),e.k0s()(),e.joV(),e.j41(6,"div")(7,"p",96),e.EFF(8,"Projet soumis"),e.k0s(),e.j41(9,"p",97),e.EFF(10,"Votre rendu a \xe9t\xe9 enregistr\xe9 avec succ\xe8s"),e.k0s()()()(),e.bVm())}const q=function(r){return["/projects/submit",r]};function z(r,i){if(1&r&&(e.qex(0),e.j41(1,"a",98)(2,"div",73),e.qSk(),e.j41(3,"svg",74),e.nrm(4,"path",99),e.k0s(),e.joV(),e.j41(5,"span"),e.EFF(6,"Soumettre mon projet"),e.k0s()()(),e.bVm()),2&r){const t=e.XpG(2);e.R7$(1),e.Y8G("routerLink",e.eq3(1,q,t.projetId))}}function G(r,i){if(1&r&&(e.j41(0,"div",34)(1,"div",35)(2,"div",36)(3,"div",37)(4,"div",38),e.qSk(),e.j41(5,"svg",39),e.nrm(6,"path",40),e.k0s()(),e.joV(),e.j41(7,"h3",41),e.EFF(8,"Description du projet"),e.k0s()(),e.j41(9,"div",42)(10,"p",43),e.EFF(11),e.k0s()()(),e.j41(12,"div",36)(13,"div",37)(14,"div",44),e.qSk(),e.j41(15,"svg",45),e.nrm(16,"path",46),e.k0s()(),e.joV(),e.j41(17,"h3",41),e.EFF(18," Ressources du projet "),e.j41(19,"span",47),e.EFF(20),e.k0s()()(),e.DNE(21,$,2,1,"div",48),e.DNE(22,P,7,0,"div",49),e.k0s()(),e.j41(23,"div",50)(24,"div",51)(25,"div",52)(26,"div",26)(27,"div",53),e.qSk(),e.j41(28,"svg",54),e.nrm(29,"path",55),e.k0s()(),e.joV(),e.j41(30,"div")(31,"h3",56),e.EFF(32,"Informations"),e.k0s(),e.j41(33,"p",57),e.EFF(34,"D\xe9tails du projet"),e.k0s()()()(),e.j41(35,"div",58)(36,"div",59)(37,"div",26)(38,"div",44),e.qSk(),e.j41(39,"svg",60),e.nrm(40,"path",24),e.k0s()(),e.joV(),e.j41(41,"div")(42,"p",61),e.EFF(43,"Date limite"),e.k0s(),e.j41(44,"p",62),e.EFF(45),e.nI1(46,"date"),e.k0s()()()(),e.j41(47,"div",59)(48,"div",26)(49,"div",63),e.qSk(),e.j41(50,"svg",64),e.nrm(51,"path",65),e.k0s()(),e.joV(),e.j41(52,"div")(53,"p",61),e.EFF(54,"Temps restant"),e.k0s(),e.j41(55,"p",62),e.EFF(56),e.k0s()()()(),e.j41(57,"div",59)(58,"div",26)(59,"div",38),e.qSk(),e.j41(60,"svg",20),e.nrm(61,"path",21),e.k0s()(),e.joV(),e.j41(62,"div")(63,"p",61),e.EFF(64,"Groupe cible"),e.k0s(),e.j41(65,"p",62),e.EFF(66),e.k0s()()()()()(),e.j41(67,"div",36)(68,"div",37)(69,"div",66),e.qSk(),e.j41(70,"svg",67),e.nrm(71,"path",68)(72,"path",69),e.k0s()(),e.joV(),e.j41(73,"h3",41),e.EFF(74,"Actions"),e.k0s()(),e.j41(75,"div",70),e.DNE(76,L,11,0,"ng-container",71),e.DNE(77,z,7,3,"ng-container",71),e.j41(78,"a",72)(79,"div",73),e.qSk(),e.j41(80,"svg",74),e.nrm(81,"path",75),e.k0s(),e.joV(),e.j41(82,"span"),e.EFF(83,"Retour aux projets"),e.k0s()()()()()()()),2&r){const t=e.XpG();e.R7$(11),e.SpI(" ",(null==t.projet?null:t.projet.description)||"Aucune description fournie pour ce projet."," "),e.R7$(9),e.Lme(" (",(null==t.projet||null==t.projet.fichiers?null:t.projet.fichiers.length)||0," fichier",((null==t.projet||null==t.projet.fichiers?null:t.projet.fichiers.length)||0)>1?"s":"",") "),e.R7$(1),e.Y8G("ngIf",(null==t.projet||null==t.projet.fichiers?null:t.projet.fichiers.length)>0),e.R7$(1),e.Y8G("ngIf",!(null!=t.projet&&t.projet.fichiers)||0===t.projet.fichiers.length),e.R7$(23),e.JRh(e.i5U(46,10,null==t.projet?null:t.projet.dateLimite,"dd/MM/yyyy")),e.R7$(11),e.SpI("",t.getRemainingDays()," jours"),e.R7$(10),e.JRh((null==t.projet?null:t.projet.groupe)||"Tous les groupes"),e.R7$(10),e.Y8G("ngIf",t.hasSubmitted),e.R7$(1),e.Y8G("ngIf",!t.hasSubmitted)}}let T=(()=>{class r{constructor(t,o,n,d,c){this.route=t,this.router=o,this.projetService=n,this.rendusService=d,this.authService=c,this.projetId="",this.isLoading=!0,this.hasSubmitted=!1}ngOnInit(){this.projetId=this.route.snapshot.paramMap.get("id")||"",this.loadProjetDetails(),this.checkRenduStatus()}loadProjetDetails(){this.isLoading=!0,this.projetService.getProjetById(this.projetId).subscribe({next:t=>{this.projet=t,this.isLoading=!1},error:t=>{console.error("Erreur lors du chargement du projet",t),this.isLoading=!1,this.router.navigate(["/projects"])}})}checkRenduStatus(){const t=this.authService.getCurrentUserId();t&&this.rendusService.checkRenduExists(this.projetId,t).subscribe({next:o=>{console.log(o),this.hasSubmitted=o},error:o=>{console.error("Erreur lors de la v\xe9rification du rendu",o)}})}getFileUrl(t){let o=t;if(t.includes("/")||t.includes("\\")){const n=t.split(/[\/\\]/);o=n[n.length-1]}return`http://localhost:3000/api/projets/download/${o}`}getFileName(t){if(!t)return"Fichier";if(t.includes("/")||t.includes("\\")){const o=t.split(/[\/\\]/);return o[o.length-1]}return t}getProjectStatus(){if(this.hasSubmitted)return"completed";if(!this.projet?.dateLimite)return"active";const t=new Date,o=new Date(this.projet.dateLimite);if(o<t)return"expired";const n=new Date;return n.setDate(n.getDate()+7),o<=n?"urgent":"active"}getStatusClass(){switch(this.getProjectStatus()){case"completed":return"bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400 border border-green-200 dark:border-green-800/30";case"urgent":return"bg-orange-100 dark:bg-orange-900/30 text-orange-800 dark:text-orange-400 border border-orange-200 dark:border-orange-800/30";case"expired":return"bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-400 border border-red-200 dark:border-red-800/30";default:return"bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-400 border border-blue-200 dark:border-blue-800/30"}}getStatusText(){switch(this.getProjectStatus()){case"completed":return"Projet soumis";case"urgent":return"Urgent";case"expired":return"Expir\xe9";default:return"Actif"}}getRemainingDays(){if(!this.projet?.dateLimite)return 0;const t=new Date,n=new Date(this.projet.dateLimite).getTime()-t.getTime(),d=Math.ceil(n/864e5);return Math.max(0,d)}static{this.\u0275fac=function(o){return new(o||r)(e.rXU(u.nX),e.rXU(u.Ix),e.rXU(p.e),e.rXU(f.R),e.rXU(g.V))}}static{this.\u0275cmp=e.VBU({type:r,selectors:[["app-project-detail"]],decls:39,vars:11,consts:[[1,"min-h-screen","bg-gradient-to-br","from-gray-50","via-blue-50","to-indigo-100","dark:from-dark-bg-primary","dark:via-dark-bg-secondary","dark:to-dark-bg-tertiary","relative"],[1,"absolute","inset-0","overflow-hidden","pointer-events-none"],[1,"absolute","top-[15%]","left-[10%]","w-64","h-64","rounded-full","bg-gradient-to-br","from-[#4f5fad]/5","to-transparent","dark:from-[#6d78c9]/3","dark:to-transparent","blur-3xl"],[1,"absolute","bottom-[20%]","right-[10%]","w-80","h-80","rounded-full","bg-gradient-to-tl","from-[#4f5fad]/5","to-transparent","dark:from-[#6d78c9]/3","dark:to-transparent","blur-3xl"],[1,"container","mx-auto","px-4","py-8","relative","z-10"],[1,"mb-8"],[1,"flex","items-center","space-x-2","text-sm","text-[#6d6870]","dark:text-[#a0a0a0]","mb-4"],["routerLink","/projects",1,"hover:text-[#4f5fad]","dark:hover:text-[#6d78c9]","transition-colors"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-4","h-4"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M9 5l7 7-7 7"],[1,"text-[#4f5fad]","dark:text-[#6d78c9]","font-medium"],[1,"bg-white/80","dark:bg-[#1e1e1e]/80","backdrop-blur-sm","rounded-2xl","p-8","shadow-lg","border","border-[#edf1f4]/50","dark:border-[#2a2a2a]"],[1,"flex","flex-col","lg:flex-row","lg:items-center","lg:justify-between"],[1,"flex","items-center","space-x-4","mb-6","lg:mb-0"],[1,"h-16","w-16","rounded-2xl","bg-gradient-to-br","from-[#3d4a85]","to-[#4f5fad]","dark:from-[#6d78c9]","dark:to-[#4f5fad]","flex","items-center","justify-center","shadow-lg"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-8","h-8","text-white"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M19 11H5m14-7H3a2 2 0 00-2 2v12a2 2 0 002 2h16a2 2 0 002-2V6a2 2 0 00-2-2z"],[1,"text-3xl","font-bold","bg-gradient-to-r","from-[#3d4a85]","to-[#4f5fad]","dark:from-[#6d78c9]","dark:to-[#4f5fad]","bg-clip-text","text-transparent"],[1,"flex","items-center","space-x-4","mt-2"],[1,"flex","items-center","space-x-1"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-4","h-4","text-[#4f5fad]","dark:text-[#6d78c9]"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"],[1,"text-sm","font-medium","text-[#6d6870]","dark:text-[#a0a0a0]"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-4","h-4","text-orange-500"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"],[1,"text-sm","text-[#6d6870]","dark:text-[#a0a0a0]"],[1,"flex","items-center","space-x-3"],[1,"px-4","py-2","rounded-xl","text-sm","font-medium",3,"ngClass"],["class","flex justify-center my-12",4,"ngIf"],["class","grid grid-cols-1 lg:grid-cols-3 gap-8",4,"ngIf"],[1,"flex","justify-center","my-12"],[1,"relative"],[1,"w-14","h-14","border-4","border-[#4f5fad]/20","dark:border-[#6d78c9]/20","border-t-[#4f5fad]","dark:border-t-[#6d78c9]","rounded-full","animate-spin"],[1,"absolute","inset-0","bg-[#4f5fad]/20","dark:bg-[#6d78c9]/20","blur-xl","rounded-full","transform","scale-150","-z-10"],[1,"grid","grid-cols-1","lg:grid-cols-3","gap-8"],[1,"lg:col-span-2","space-y-6"],[1,"bg-white/80","dark:bg-[#1e1e1e]/80","backdrop-blur-sm","rounded-2xl","p-6","shadow-lg","border","border-[#edf1f4]/50","dark:border-[#2a2a2a]"],[1,"flex","items-center","space-x-3","mb-4"],[1,"bg-[#4f5fad]/10","dark:bg-[#6d78c9]/10","p-2","rounded-lg"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-5","h-5","text-[#4f5fad]","dark:text-[#6d78c9]"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M4 6h16M4 12h16M4 18h7"],[1,"text-lg","font-semibold","text-[#3d4a85]","dark:text-[#6d78c9]"],[1,"prose","prose-gray","dark:prose-invert","max-w-none"],[1,"text-[#6d6870]","dark:text-[#a0a0a0]","leading-relaxed"],[1,"bg-orange-100","dark:bg-orange-900/30","p-2","rounded-lg"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-5","h-5","text-orange-600","dark:text-orange-400"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"],[1,"text-sm","font-normal","text-[#6d6870]","dark:text-[#a0a0a0]","ml-2"],["class","grid grid-cols-1 sm:grid-cols-2 gap-4",4,"ngIf"],["class","text-center py-8",4,"ngIf"],[1,"space-y-6"],[1,"bg-white/80","dark:bg-[#1e1e1e]/80","backdrop-blur-sm","rounded-2xl","shadow-lg","border","border-[#edf1f4]/50","dark:border-[#2a2a2a]","overflow-hidden"],[1,"bg-gradient-to-r","from-[#3d4a85]","to-[#4f5fad]","dark:from-[#6d78c9]","dark:to-[#4f5fad]","p-6","text-white"],[1,"bg-white/20","p-2","rounded-lg"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-6","h-6"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"],[1,"text-lg","font-semibold"],[1,"text-sm","text-white/80"],[1,"p-6","space-y-4"],[1,"flex","items-center","justify-between","p-3","bg-[#edf1f4]/50","dark:bg-[#2a2a2a]/50","rounded-xl"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-4","h-4","text-orange-600","dark:text-orange-400"],[1,"text-xs","font-medium","text-[#6d6870]","dark:text-[#a0a0a0]","uppercase","tracking-wider"],[1,"text-sm","font-semibold","text-[#3d4a85]","dark:text-[#6d78c9]"],[1,"bg-blue-100","dark:bg-blue-900/30","p-2","rounded-lg"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-4","h-4","text-blue-600","dark:text-blue-400"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"],[1,"bg-green-100","dark:bg-green-900/30","p-2","rounded-lg"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-5","h-5","text-green-600","dark:text-green-400"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M15 12a3 3 0 11-6 0 3 3 0 016 0z"],[1,"space-y-3"],[4,"ngIf"],["routerLink","/projects",1,"block","w-full","px-6","py-3","bg-[#edf1f4]","dark:bg-[#2a2a2a]","text-[#3d4a85]","dark:text-[#6d78c9]","hover:bg-[#4f5fad]/10","dark:hover:bg-[#6d78c9]/10","rounded-xl","transition-all","duration-200","font-medium","text-center"],[1,"flex","items-center","justify-center","space-x-2"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-5","h-5"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M10 19l-7-7m0 0l7-7m-7 7h18"],[1,"grid","grid-cols-1","sm:grid-cols-2","gap-4"],["class","group",4,"ngFor","ngForOf"],[1,"group"],[1,"bg-[#edf1f4]/70","dark:bg-[#2a2a2a]/70","rounded-xl","p-4","border","border-[#edf1f4]","dark:border-[#2a2a2a]","hover:border-[#4f5fad]","dark:hover:border-[#6d78c9]","transition-all","duration-200","hover:shadow-md"],[1,"flex","items-center","justify-between"],[1,"flex","items-center","space-x-3","flex-1","min-w-0"],[1,"bg-[#4f5fad]/10","dark:bg-[#6d78c9]/10","p-2","rounded-lg","group-hover:scale-110","transition-transform"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"],[1,"flex-1","min-w-0"],[1,"text-sm","font-medium","text-[#3d4a85]","dark:text-[#6d78c9]","truncate"],[1,"text-xs","text-[#6d6870]","dark:text-[#a0a0a0]"],["download","",1,"ml-3","px-3","py-2","bg-[#4f5fad]/10","dark:bg-[#6d78c9]/10","text-[#4f5fad]","dark:text-[#6d78c9]","hover:bg-[#4f5fad]","hover:text-white","dark:hover:bg-[#6d78c9]","rounded-lg","transition-all","duration-200","text-xs","font-medium","group-hover:scale-105",3,"href"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-3","h-3"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"],[1,"text-center","py-8"],[1,"bg-[#edf1f4]/70","dark:bg-[#2a2a2a]/70","rounded-xl","p-6"],[1,"bg-[#edf1f4]","dark:bg-[#2a2a2a]","p-3","rounded-lg","inline-flex","items-center","justify-center","mb-3"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-6","h-6","text-[#6d6870]","dark:text-[#a0a0a0]"],[1,"p-4","bg-green-50","dark:bg-green-900/20","border","border-green-200","dark:border-green-800/30","rounded-xl"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"],[1,"text-sm","font-semibold","text-green-800","dark:text-green-400"],[1,"text-xs","text-green-600","dark:text-green-500"],[1,"block","w-full","px-6","py-3","bg-gradient-to-r","from-[#3d4a85]","to-[#4f5fad]","dark:from-[#6d78c9]","dark:to-[#4f5fad]","text-white","rounded-xl","hover:shadow-lg","hover:scale-105","transition-all","duration-200","font-medium","text-center",3,"routerLink"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"]],template:function(o,n){1&o&&(e.j41(0,"div",0)(1,"div",1),e.nrm(2,"div",2)(3,"div",3),e.k0s(),e.j41(4,"div",4)(5,"div",5)(6,"nav",6)(7,"a",7),e.EFF(8,"Mes Projets"),e.k0s(),e.qSk(),e.j41(9,"svg",8),e.nrm(10,"path",9),e.k0s(),e.joV(),e.j41(11,"span",10),e.EFF(12),e.k0s()(),e.j41(13,"div",11)(14,"div",12)(15,"div",13)(16,"div",14),e.qSk(),e.j41(17,"svg",15),e.nrm(18,"path",16),e.k0s()(),e.joV(),e.j41(19,"div")(20,"h1",17),e.EFF(21),e.k0s(),e.j41(22,"div",18)(23,"div",19),e.qSk(),e.j41(24,"svg",20),e.nrm(25,"path",21),e.k0s(),e.joV(),e.j41(26,"span",22),e.EFF(27),e.k0s()(),e.j41(28,"div",19),e.qSk(),e.j41(29,"svg",23),e.nrm(30,"path",24),e.k0s(),e.joV(),e.j41(31,"span",25),e.EFF(32),e.nI1(33,"date"),e.k0s()()()()(),e.j41(34,"div",26)(35,"span",27),e.EFF(36),e.k0s()()()()(),e.DNE(37,V,4,0,"div",28),e.DNE(38,G,84,13,"div",29),e.k0s()()),2&o&&(e.R7$(12),e.JRh((null==n.projet?null:n.projet.titre)||"D\xe9tails du projet"),e.R7$(9),e.SpI(" ",(null==n.projet?null:n.projet.titre)||"Chargement..."," "),e.R7$(6),e.JRh((null==n.projet?null:n.projet.groupe)||"Tous les groupes"),e.R7$(5),e.JRh(e.i5U(33,8,null==n.projet?null:n.projet.dateLimite,"dd/MM/yyyy")),e.R7$(3),e.Y8G("ngClass",n.getStatusClass()),e.R7$(1),e.SpI(" ",n.getStatusText()," "),e.R7$(1),e.Y8G("ngIf",n.isLoading),e.R7$(1),e.Y8G("ngIf",!n.isLoading))},dependencies:[s.YU,s.Sq,s.bT,u.Wk,s.vh],styles:[".project-container[_ngcontent-%COMP%]{padding:1.5rem;background-color:#fff;border-radius:.5rem;box-shadow:0 2px 4px #0000001a}.project-header[_ngcontent-%COMP%]{margin-bottom:1.5rem;border-bottom:1px solid #e5e7eb;padding-bottom:1rem}.project-description[_ngcontent-%COMP%]{margin-bottom:1.5rem}.project-meta[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;gap:1rem;margin-bottom:1.5rem}.project-meta-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem}"]})}}return r})();var l=a(4341);function B(r,i){1&r&&(e.j41(0,"div",21)(1,"div",22),e.nrm(2,"div",23)(3,"div",24),e.k0s()())}function N(r,i){1&r&&(e.j41(0,"div",105),e.qSk(),e.j41(1,"svg",8),e.nrm(2,"path",106),e.k0s(),e.joV(),e.j41(3,"span"),e.EFF(4,"La description est requise et doit contenir au moins 10 caract\xe8res."),e.k0s()())}function Y(r,i){1&r&&(e.j41(0,"div",105),e.qSk(),e.j41(1,"svg",8),e.nrm(2,"path",106),e.k0s(),e.joV(),e.j41(3,"span"),e.EFF(4,"Veuillez s\xe9lectionner au moins un fichier."),e.k0s()())}function U(r,i){if(1&r){const t=e.RV6();e.j41(0,"div",109)(1,"div",73)(2,"div",53),e.qSk(),e.j41(3,"svg",43),e.nrm(4,"path",37),e.k0s()(),e.joV(),e.j41(5,"div")(6,"p",110),e.EFF(7),e.k0s(),e.j41(8,"p",111),e.EFF(9),e.k0s()()(),e.j41(10,"button",112),e.bIt("click",function(){const d=e.eBV(t).index,c=e.XpG(3);return e.Njj(c.removeFile(d))}),e.qSk(),e.j41(11,"svg",8),e.nrm(12,"path",113),e.k0s()()()}if(2&r){const t=i.$implicit,o=e.XpG(3);e.R7$(7),e.JRh(t.name),e.R7$(2),e.JRh(o.getFileSize(t.size))}}function X(r,i){if(1&r&&(e.j41(0,"div",46)(1,"div",89)(2,"p",93),e.EFF(3,"Fichiers s\xe9lectionn\xe9s :"),e.k0s(),e.j41(4,"span",51),e.EFF(5),e.k0s()(),e.j41(6,"div",107),e.DNE(7,U,13,2,"div",108),e.k0s()()),2&r){const t=e.XpG(2);e.R7$(5),e.Lme("",t.selectedFiles.length," fichier",t.selectedFiles.length>1?"s":"",""),e.R7$(2),e.Y8G("ngForOf",t.selectedFiles)}}function O(r,i){1&r&&(e.qSk(),e.j41(0,"svg",61),e.nrm(1,"path",16),e.k0s())}function H(r,i){1&r&&e.nrm(0,"div",114)}function A(r,i){1&r&&(e.j41(0,"span"),e.EFF(1,"Soumettre le projet"),e.k0s())}function J(r,i){1&r&&(e.j41(0,"span"),e.EFF(1,"Soumission en cours..."),e.k0s())}function W(r,i){1&r&&(e.j41(0,"div",115),e.qSk(),e.j41(1,"svg",8),e.nrm(2,"path",104),e.k0s(),e.joV(),e.j41(3,"span"),e.EFF(4,"Compl\xe9tez le formulaire pour soumettre"),e.k0s()())}function K(r,i){1&r&&(e.j41(0,"div",116),e.qSk(),e.j41(1,"svg",8),e.nrm(2,"path",102),e.k0s(),e.joV(),e.j41(3,"span"),e.EFF(4,"Pr\xeat \xe0 soumettre"),e.k0s()())}const k=function(r){return["/projects/detail",r]};function Q(r,i){if(1&r){const t=e.RV6();e.j41(0,"div",25)(1,"div",26)(2,"div",27)(3,"div",28)(4,"div",29),e.qSk(),e.j41(5,"svg",30),e.nrm(6,"path",31),e.k0s()(),e.joV(),e.j41(7,"h3",32),e.EFF(8,"Description de votre travail"),e.k0s()(),e.j41(9,"form",33),e.bIt("ngSubmit",function(){e.eBV(t);const n=e.XpG();return e.Njj(n.onSubmit())}),e.j41(10,"div",34)(11,"label",35),e.qSk(),e.j41(12,"svg",36),e.nrm(13,"path",37),e.k0s(),e.joV(),e.j41(14,"span"),e.EFF(15,"Rapport de projet"),e.k0s(),e.j41(16,"span",38),e.EFF(17,"*"),e.k0s()(),e.j41(18,"div",22),e.nrm(19,"textarea",39),e.k0s(),e.j41(20,"div",40)(21,"span"),e.EFF(22,"Minimum 10 caract\xe8res requis"),e.k0s(),e.j41(23,"span"),e.EFF(24),e.k0s()(),e.DNE(25,N,5,0,"div",41),e.k0s(),e.j41(26,"div",34)(27,"label",42),e.qSk(),e.j41(28,"svg",43),e.nrm(29,"path",16),e.k0s(),e.joV(),e.j41(30,"span"),e.EFF(31,"Fichiers du projet"),e.k0s(),e.j41(32,"span",38),e.EFF(33,"*"),e.k0s()(),e.j41(34,"div",22)(35,"input",44),e.bIt("change",function(n){e.eBV(t);const d=e.XpG();return e.Njj(d.onFileChange(n))}),e.k0s(),e.j41(36,"div",45)(37,"div",46)(38,"div",47),e.qSk(),e.j41(39,"svg",48),e.nrm(40,"path",16),e.k0s()(),e.joV(),e.j41(41,"div")(42,"p",49),e.EFF(43,"Glissez vos fichiers ici"),e.k0s(),e.j41(44,"p",50),e.EFF(45,"ou cliquez pour parcourir"),e.k0s()(),e.j41(46,"p",51),e.EFF(47,"Tous types de fichiers accept\xe9s"),e.k0s()()()(),e.DNE(48,Y,5,0,"div",41),e.DNE(49,X,8,3,"div",52),e.k0s()()(),e.j41(50,"div",27)(51,"div",28)(52,"div",53),e.qSk(),e.j41(53,"svg",54),e.nrm(54,"path",55)(55,"path",56),e.k0s()(),e.joV(),e.j41(56,"h3",32),e.EFF(57,"Actions"),e.k0s()(),e.j41(58,"div",57)(59,"div",58)(60,"a",59)(61,"div",60),e.qSk(),e.j41(62,"svg",61),e.nrm(63,"path",62),e.k0s(),e.joV(),e.j41(64,"span"),e.EFF(65,"Retour aux d\xe9tails"),e.k0s()()(),e.j41(66,"button",63)(67,"div",60),e.DNE(68,O,2,0,"svg",64),e.DNE(69,H,1,0,"div",65),e.DNE(70,A,2,0,"span",66),e.DNE(71,J,2,0,"span",66),e.k0s()()(),e.j41(72,"div",67),e.DNE(73,W,5,0,"div",68),e.DNE(74,K,5,0,"div",69),e.k0s()()()(),e.j41(75,"div",70)(76,"div",71)(77,"div",72)(78,"div",73)(79,"div",74),e.qSk(),e.j41(80,"svg",75),e.nrm(81,"path",76),e.k0s()(),e.joV(),e.j41(82,"div")(83,"h3",77),e.EFF(84,"Informations"),e.k0s(),e.j41(85,"p",78),e.EFF(86,"D\xe9tails du projet"),e.k0s()()()(),e.j41(87,"div",79)(88,"div",80)(89,"div",81)(90,"div",82),e.qSk(),e.j41(91,"svg",83),e.nrm(92,"path",84),e.k0s()(),e.joV(),e.j41(93,"div",85)(94,"p",86),e.EFF(95,"Titre"),e.k0s(),e.j41(96,"p",87),e.EFF(97),e.k0s()()()(),e.j41(98,"div",80)(99,"div",81)(100,"div",29),e.qSk(),e.j41(101,"svg",36),e.nrm(102,"path",31),e.k0s()(),e.joV(),e.j41(103,"div",85)(104,"p",86),e.EFF(105,"Description"),e.k0s(),e.j41(106,"p",88),e.EFF(107),e.k0s()()()(),e.j41(108,"div",80)(109,"div",89)(110,"div",73)(111,"div",90),e.qSk(),e.j41(112,"svg",91),e.nrm(113,"path",92),e.k0s()(),e.joV(),e.j41(114,"div")(115,"p",86),e.EFF(116,"Date limite"),e.k0s(),e.j41(117,"p",93),e.EFF(118),e.nI1(119,"date"),e.k0s()()(),e.j41(120,"div",94)(121,"p",51),e.EFF(122),e.k0s(),e.j41(123,"p",95),e.EFF(124,"restants"),e.k0s()()()(),e.j41(125,"div",80)(126,"div",73)(127,"div",96),e.qSk(),e.j41(128,"svg",97),e.nrm(129,"path",98),e.k0s()(),e.joV(),e.j41(130,"div")(131,"p",86),e.EFF(132,"Groupe cible"),e.k0s(),e.j41(133,"p",93),e.EFF(134),e.k0s()()()()()(),e.j41(135,"div",27)(136,"div",28)(137,"div",29),e.qSk(),e.j41(138,"svg",30),e.nrm(139,"path",76),e.k0s()(),e.joV(),e.j41(140,"h3",32),e.EFF(141,"Conseils"),e.k0s()(),e.j41(142,"div",99)(143,"div",100),e.qSk(),e.j41(144,"svg",101),e.nrm(145,"path",102),e.k0s(),e.joV(),e.j41(146,"span"),e.EFF(147,"D\xe9crivez clairement votre travail et les technologies utilis\xe9es"),e.k0s()(),e.j41(148,"div",100),e.qSk(),e.j41(149,"svg",101),e.nrm(150,"path",102),e.k0s(),e.joV(),e.j41(151,"span"),e.EFF(152,"Incluez tous les fichiers sources et la documentation"),e.k0s()(),e.j41(153,"div",100),e.qSk(),e.j41(154,"svg",101),e.nrm(155,"path",102),e.k0s(),e.joV(),e.j41(156,"span"),e.EFF(157,"Mentionnez les difficult\xe9s rencontr\xe9es et solutions apport\xe9es"),e.k0s()(),e.j41(158,"div",100),e.qSk(),e.j41(159,"svg",103),e.nrm(160,"path",104),e.k0s(),e.joV(),e.j41(161,"span"),e.EFF(162,"V\xe9rifiez que tous vos fichiers sont bien s\xe9lectionn\xe9s"),e.k0s()()()()()()}if(2&r){const t=e.XpG();let o,n;e.R7$(9),e.Y8G("formGroup",t.submissionForm),e.R7$(15),e.SpI("",(null==(o=t.submissionForm.get("description"))||null==o.value?null:o.value.length)||0," caract\xe8res"),e.R7$(1),e.Y8G("ngIf",(null==(n=t.submissionForm.get("description"))?null:n.invalid)&&(null==(n=t.submissionForm.get("description"))?null:n.touched)),e.R7$(23),e.Y8G("ngIf",0===t.selectedFiles.length&&t.submissionForm.touched),e.R7$(1),e.Y8G("ngIf",t.selectedFiles.length>0),e.R7$(11),e.Y8G("routerLink",e.eq3(21,k,t.projetId)),e.R7$(6),e.Y8G("disabled",t.submissionForm.invalid||0===t.selectedFiles.length||t.isSubmitting),e.R7$(2),e.Y8G("ngIf",!t.isSubmitting),e.R7$(1),e.Y8G("ngIf",t.isSubmitting),e.R7$(1),e.Y8G("ngIf",!t.isSubmitting),e.R7$(1),e.Y8G("ngIf",t.isSubmitting),e.R7$(2),e.Y8G("ngIf",t.submissionForm.invalid||0===t.selectedFiles.length),e.R7$(1),e.Y8G("ngIf",t.submissionForm.valid&&t.selectedFiles.length>0),e.R7$(23),e.JRh(t.projet.titre),e.R7$(10),e.JRh(t.projet.description||"Aucune description"),e.R7$(11),e.JRh(e.i5U(119,18,t.projet.dateLimite,"dd/MM/yyyy")),e.R7$(4),e.SpI("",t.getRemainingDays()," jours"),e.R7$(12),e.JRh(t.projet.groupe||"Tous les groupes")}}const Z=[{path:"",component:I},{path:"detail/:id",component:T},{path:"submit/:id",component:(()=>{class r{constructor(t,o,n,d,c,re){this.fb=t,this.route=o,this.router=n,this.projetService=d,this.rendusService=c,this.authService=re,this.projetId="",this.selectedFiles=[],this.isLoading=!0,this.isSubmitting=!1,this.submissionForm=this.fb.group({description:["",[l.k0.required,l.k0.minLength(10)]]})}ngOnInit(){this.projetId=this.route.snapshot.paramMap.get("id")||"",this.loadProjetDetails()}loadProjetDetails(){this.isLoading=!0,this.projetService.getProjetById(this.projetId).subscribe({next:t=>{this.projet=t,this.isLoading=!1},error:t=>{console.error("Erreur lors du chargement du projet",t),this.isLoading=!1,this.router.navigate(["/projects"])}})}onFileChange(t){const o=t.target;o.files&&(this.selectedFiles=Array.from(o.files))}onSubmit(){if(this.submissionForm.invalid||0===this.selectedFiles.length)return;this.isSubmitting=!0;const t=new FormData;t.append("projet",this.projetId),t.append("etudiant",this.authService.getCurrentUserId()||""),t.append("description",this.submissionForm.value.description),this.selectedFiles.forEach(o=>{t.append("fichiers",o)}),this.rendusService.submitRendu(t).subscribe({next:o=>{alert("Votre projet a \xe9t\xe9 soumis avec succ\xe8s"),this.router.navigate(["/projects"])},error:o=>{console.error("Erreur lors de la soumission du projet",o),alert("Une erreur est survenue lors de la soumission du projet"),this.isSubmitting=!1}})}removeFile(t){this.selectedFiles.splice(t,1)}getFileSize(t){if(0===t)return"0 B";const d=Math.floor(Math.log(t)/Math.log(1024));return parseFloat((t/Math.pow(1024,d)).toFixed(2))+" "+["B","KB","MB","GB"][d]}getRemainingDays(){if(!this.projet?.dateLimite)return 0;const t=new Date,n=new Date(this.projet.dateLimite).getTime()-t.getTime(),d=Math.ceil(n/864e5);return Math.max(0,d)}static{this.\u0275fac=function(o){return new(o||r)(e.rXU(l.ok),e.rXU(u.nX),e.rXU(u.Ix),e.rXU(p.e),e.rXU(f.R),e.rXU(g.V))}}static{this.\u0275cmp=e.VBU({type:r,selectors:[["app-project-submission"]],decls:29,vars:6,consts:[[1,"min-h-screen","bg-gradient-to-br","from-gray-50","via-blue-50","to-indigo-100","dark:from-dark-bg-primary","dark:via-dark-bg-secondary","dark:to-dark-bg-tertiary","relative"],[1,"absolute","inset-0","overflow-hidden","pointer-events-none"],[1,"absolute","top-[15%]","left-[10%]","w-64","h-64","rounded-full","bg-gradient-to-br","from-[#4f5fad]/5","to-transparent","dark:from-[#6d78c9]/3","dark:to-transparent","blur-3xl"],[1,"absolute","bottom-[20%]","right-[10%]","w-80","h-80","rounded-full","bg-gradient-to-tl","from-[#4f5fad]/5","to-transparent","dark:from-[#6d78c9]/3","dark:to-transparent","blur-3xl"],[1,"container","mx-auto","px-4","py-8","relative","z-10"],[1,"mb-8"],[1,"flex","items-center","space-x-2","text-sm","text-[#6d6870]","dark:text-[#a0a0a0]","mb-4"],["routerLink","/projects",1,"hover:text-[#4f5fad]","dark:hover:text-[#6d78c9]","transition-colors"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-4","h-4"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M9 5l7 7-7 7"],[1,"hover:text-[#4f5fad]","dark:hover:text-[#6d78c9]","transition-colors",3,"routerLink"],[1,"text-[#4f5fad]","dark:text-[#6d78c9]","font-medium"],[1,"bg-white/80","dark:bg-[#1e1e1e]/80","backdrop-blur-sm","rounded-2xl","p-8","shadow-lg","border","border-[#edf1f4]/50","dark:border-[#2a2a2a]"],[1,"flex","items-center","space-x-4"],[1,"h-16","w-16","rounded-2xl","bg-gradient-to-br","from-green-500","to-green-600","dark:from-green-600","dark:to-green-700","flex","items-center","justify-center","shadow-lg"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-8","h-8","text-white"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"],[1,"text-3xl","font-bold","bg-gradient-to-r","from-green-600","to-green-700","dark:from-green-400","dark:to-green-500","bg-clip-text","text-transparent"],[1,"text-[#6d6870]","dark:text-[#a0a0a0]"],["class","flex justify-center my-12",4,"ngIf"],["class","grid grid-cols-1 lg:grid-cols-3 gap-8",4,"ngIf"],[1,"flex","justify-center","my-12"],[1,"relative"],[1,"w-14","h-14","border-4","border-[#4f5fad]/20","dark:border-[#6d78c9]/20","border-t-[#4f5fad]","dark:border-t-[#6d78c9]","rounded-full","animate-spin"],[1,"absolute","inset-0","bg-[#4f5fad]/20","dark:bg-[#6d78c9]/20","blur-xl","rounded-full","transform","scale-150","-z-10"],[1,"grid","grid-cols-1","lg:grid-cols-3","gap-8"],[1,"lg:col-span-2","space-y-6"],[1,"bg-white/80","dark:bg-[#1e1e1e]/80","backdrop-blur-sm","rounded-2xl","p-6","shadow-lg","border","border-[#edf1f4]/50","dark:border-[#2a2a2a]"],[1,"flex","items-center","space-x-3","mb-4"],[1,"bg-blue-100","dark:bg-blue-900/30","p-2","rounded-lg"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-5","h-5","text-blue-600","dark:text-blue-400"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M4 6h16M4 12h16M4 18h7"],[1,"text-lg","font-semibold","text-[#3d4a85]","dark:text-[#6d78c9]"],["id","submissionForm",1,"space-y-6",3,"formGroup","ngSubmit"],[1,"space-y-2"],["for","description",1,"flex","items-center","space-x-2","text-sm","font-semibold","text-[#3d4a85]","dark:text-[#6d78c9]"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-4","h-4","text-blue-600","dark:text-blue-400"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"],[1,"text-red-500"],["id","description","formControlName","description","rows","6","placeholder","D\xe9crivez votre travail : fonctionnalit\xe9s impl\xe9ment\xe9es, technologies utilis\xe9es, difficult\xe9s rencontr\xe9es, solutions apport\xe9es...",1,"w-full","px-4","py-3","bg-white","dark:bg-[#2a2a2a]","border-2","border-[#edf1f4]","dark:border-[#2a2a2a]","rounded-xl","focus:outline-none","focus:border-[#4f5fad]","dark:focus:border-[#6d78c9]","focus:ring-4","focus:ring-[#4f5fad]/10","dark:focus:ring-[#6d78c9]/20","transition-all","duration-200","text-[#3d4a85]","dark:text-[#6d78c9]","placeholder-[#6d6870]","dark:placeholder-[#a0a0a0]","resize-none"],[1,"flex","justify-between","text-xs","text-[#6d6870]","dark:text-[#a0a0a0]"],["class","flex items-center space-x-2 text-red-500 text-sm",4,"ngIf"],["for","fichiers",1,"flex","items-center","space-x-2","text-sm","font-semibold","text-[#3d4a85]","dark:text-[#6d78c9]"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-4","h-4","text-green-600","dark:text-green-400"],["type","file","id","fichiers","multiple","",1,"absolute","inset-0","w-full","h-full","opacity-0","cursor-pointer","z-10",3,"change"],[1,"w-full","px-6","py-8","bg-[#edf1f4]/70","dark:bg-[#2a2a2a]/70","border-2","border-dashed","border-[#4f5fad]/30","dark:border-[#6d78c9]/30","rounded-xl","hover:border-[#4f5fad]","dark:hover:border-[#6d78c9]","transition-all","duration-200","text-center"],[1,"space-y-3"],[1,"bg-[#4f5fad]/10","dark:bg-[#6d78c9]/10","p-3","rounded-xl","w-fit","mx-auto"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-8","h-8","text-[#4f5fad]","dark:text-[#6d78c9]"],[1,"text-[#3d4a85]","dark:text-[#6d78c9]","font-medium"],[1,"text-sm","text-[#6d6870]","dark:text-[#a0a0a0]"],[1,"text-xs","text-[#6d6870]","dark:text-[#a0a0a0]"],["class","space-y-3",4,"ngIf"],[1,"bg-green-100","dark:bg-green-900/30","p-2","rounded-lg"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-5","h-5","text-green-600","dark:text-green-400"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M15 12a3 3 0 11-6 0 3 3 0 016 0z"],[1,"space-y-4"],[1,"grid","grid-cols-1","sm:grid-cols-2","gap-4"],[1,"flex","items-center","justify-center","px-6","py-3","bg-[#edf1f4]","dark:bg-[#2a2a2a]","text-[#3d4a85]","dark:text-[#6d78c9]","hover:bg-[#4f5fad]/10","dark:hover:bg-[#6d78c9]/10","rounded-xl","transition-all","duration-200","font-medium","order-2","sm:order-1",3,"routerLink"],[1,"flex","items-center","space-x-2"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-5","h-5"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M10 19l-7-7m0 0l7-7m-7 7h18"],["type","submit","form","submissionForm",1,"flex","items-center","justify-center","px-6","py-3","bg-gradient-to-r","from-green-500","to-green-600","dark:from-green-600","dark:to-green-700","text-white","rounded-xl","hover:shadow-lg","hover:scale-105","transition-all","duration-200","font-medium","disabled:opacity-50","disabled:cursor-not-allowed","disabled:hover:scale-100","disabled:hover:shadow-none","order-1","sm:order-2",3,"disabled"],["class","w-5 h-5","fill","none","stroke","currentColor","viewBox","0 0 24 24",4,"ngIf"],["class","w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin",4,"ngIf"],[4,"ngIf"],[1,"text-center"],["class","flex items-center justify-center space-x-2 text-orange-600 dark:text-orange-400 text-sm",4,"ngIf"],["class","flex items-center justify-center space-x-2 text-green-600 dark:text-green-400 text-sm",4,"ngIf"],[1,"space-y-6"],[1,"bg-white/80","dark:bg-[#1e1e1e]/80","backdrop-blur-sm","rounded-2xl","shadow-lg","border","border-[#edf1f4]/50","dark:border-[#2a2a2a]","overflow-hidden"],[1,"bg-gradient-to-r","from-[#3d4a85]","to-[#4f5fad]","dark:from-[#6d78c9]","dark:to-[#4f5fad]","p-6","text-white"],[1,"flex","items-center","space-x-3"],[1,"bg-white/20","p-2","rounded-lg"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-6","h-6"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"],[1,"text-lg","font-semibold"],[1,"text-sm","text-white/80"],[1,"p-6","space-y-4"],[1,"p-3","bg-[#edf1f4]/50","dark:bg-[#2a2a2a]/50","rounded-xl"],[1,"flex","items-start","space-x-3"],[1,"bg-[#4f5fad]/10","dark:bg-[#6d78c9]/10","p-2","rounded-lg"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-4","h-4","text-[#4f5fad]","dark:text-[#6d78c9]"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a1.994 1.994 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"],[1,"flex-1"],[1,"text-xs","font-medium","text-[#6d6870]","dark:text-[#a0a0a0]","uppercase","tracking-wider"],[1,"text-sm","font-semibold","text-[#3d4a85]","dark:text-[#6d78c9]","leading-tight"],[1,"text-sm","text-[#3d4a85]","dark:text-[#6d78c9]","leading-tight","line-clamp-3"],[1,"flex","items-center","justify-between"],[1,"bg-orange-100","dark:bg-orange-900/30","p-2","rounded-lg"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-4","h-4","text-orange-600","dark:text-orange-400"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"],[1,"text-sm","font-semibold","text-[#3d4a85]","dark:text-[#6d78c9]"],[1,"text-right"],[1,"text-xs","text-orange-600","dark:text-orange-400"],[1,"bg-purple-100","dark:bg-purple-900/30","p-2","rounded-lg"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-4","h-4","text-purple-600","dark:text-purple-400"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"],[1,"space-y-3","text-sm","text-[#6d6870]","dark:text-[#a0a0a0]"],[1,"flex","items-start","space-x-2"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-4","h-4","text-green-600","dark:text-green-400","mt-0.5","flex-shrink-0"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-4","h-4","text-orange-600","dark:text-orange-400","mt-0.5","flex-shrink-0"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"],[1,"flex","items-center","space-x-2","text-red-500","text-sm"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"],[1,"grid","grid-cols-1","gap-2"],["class","flex items-center justify-between p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800/30 rounded-xl",4,"ngFor","ngForOf"],[1,"flex","items-center","justify-between","p-3","bg-green-50","dark:bg-green-900/20","border","border-green-200","dark:border-green-800/30","rounded-xl"],[1,"text-sm","font-medium","text-green-800","dark:text-green-400"],[1,"text-xs","text-green-600","dark:text-green-500"],["type","button",1,"text-red-500","hover:text-red-700","transition-colors",3,"click"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M6 18L18 6M6 6l12 12"],[1,"w-5","h-5","border-2","border-white/30","border-t-white","rounded-full","animate-spin"],[1,"flex","items-center","justify-center","space-x-2","text-orange-600","dark:text-orange-400","text-sm"],[1,"flex","items-center","justify-center","space-x-2","text-green-600","dark:text-green-400","text-sm"]],template:function(o,n){1&o&&(e.j41(0,"div",0)(1,"div",1),e.nrm(2,"div",2)(3,"div",3),e.k0s(),e.j41(4,"div",4)(5,"div",5)(6,"nav",6)(7,"a",7),e.EFF(8,"Mes Projets"),e.k0s(),e.qSk(),e.j41(9,"svg",8),e.nrm(10,"path",9),e.k0s(),e.joV(),e.j41(11,"a",10),e.EFF(12),e.k0s(),e.qSk(),e.j41(13,"svg",8),e.nrm(14,"path",9),e.k0s(),e.joV(),e.j41(15,"span",11),e.EFF(16,"Soumettre"),e.k0s()(),e.j41(17,"div",12)(18,"div",13)(19,"div",14),e.qSk(),e.j41(20,"svg",15),e.nrm(21,"path",16),e.k0s()(),e.joV(),e.j41(22,"div")(23,"h1",17),e.EFF(24," Soumettre mon projet "),e.k0s(),e.j41(25,"p",18),e.EFF(26," T\xe9l\xe9chargez vos fichiers et d\xe9crivez votre travail "),e.k0s()()()()(),e.DNE(27,B,4,0,"div",19),e.DNE(28,Q,163,23,"div",20),e.k0s()()),2&o&&(e.R7$(11),e.Y8G("routerLink",e.eq3(4,k,n.projetId)),e.R7$(1),e.JRh((null==n.projet?null:n.projet.titre)||"Projet"),e.R7$(15),e.Y8G("ngIf",n.isLoading),e.R7$(1),e.Y8G("ngIf",n.projet&&!n.isLoading))},dependencies:[s.Sq,s.bT,u.Wk,l.qT,l.me,l.BC,l.cb,l.j4,l.JD,s.vh],styles:[".submission-form[_ngcontent-%COMP%]{max-width:800px;margin:0 auto}.form-section[_ngcontent-%COMP%]{margin-bottom:2rem}.file-upload[_ngcontent-%COMP%]{border:2px dashed #ccc;padding:1.5rem;text-align:center;border-radius:.5rem;margin-bottom:1rem}.file-upload[_ngcontent-%COMP%]:hover{border-color:#6366f1}.file-list[_ngcontent-%COMP%]{margin-top:1rem}.file-item[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;padding:.5rem;background-color:#f9fafb;border-radius:.25rem;margin-bottom:.5rem}"]})}}return r})()}];let ee=(()=>{class r{static{this.\u0275fac=function(o){return new(o||r)}}static{this.\u0275mod=e.$C({type:r})}static{this.\u0275inj=e.G2t({imports:[u.iI.forChild(Z),u.iI]})}}return r})(),te=(()=>{class r{static{this.\u0275fac=function(o){return new(o||r)}}static{this.\u0275mod=e.$C({type:r})}static{this.\u0275inj=e.G2t({imports:[s.MD,ee,l.YN,l.X1,u.iI]})}}return r})()}}]);