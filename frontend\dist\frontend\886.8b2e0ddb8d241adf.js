"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[886],{5886:(Te,_,p)=>{p.r(_),p.d(_,{ReunionsModule:()=>$e});var m=p(177),f=p(6647),g=p(9969),e=p(7705),b=p(78),x=p(9271),k=p(345),v=p(8397),l=p(4341),F=p(8448);function j(t,s){if(1&t){const n=e.RV6();e.j41(0,"button",30),e.bIt("click",function(){e.eBV(n);const i=e.XpG(2);return e.Njj(i.clearSearch())}),e.qSk(),e.j41(1,"svg",20),e.nrm(2,"path",31),e.k0s()()}}function y(t,s){if(1&t&&(e.j41(0,"option",32),e.EFF(1),e.k0s()),2&t){const n=s.$implicit;e.Y8G("value",n.id),e.R7$(1),e.JRh(n.titre)}}const C=function(t){return{"animate__animated animate__fadeInDown":t}};function w(t,s){if(1&t){const n=e.RV6();e.qSk(),e.joV(),e.j41(0,"div",15)(1,"div",16)(2,"div",17)(3,"div",4)(4,"div",18)(5,"div",19),e.qSk(),e.j41(6,"svg",20),e.nrm(7,"path",7),e.k0s()(),e.joV(),e.j41(8,"input",21),e.bIt("ngModelChange",function(i){e.eBV(n);const o=e.XpG();return e.Njj(o.searchTerm=i)})("input",function(){e.eBV(n);const i=e.XpG();return e.Njj(i.searchReunions())}),e.k0s(),e.DNE(9,j,3,0,"button",22),e.k0s()()(),e.j41(10,"div",23)(11,"div",4)(12,"div",18)(13,"select",24),e.bIt("ngModelChange",function(i){e.eBV(n);const o=e.XpG();return e.Njj(o.selectedPlanning=i)})("change",function(){e.eBV(n);const i=e.XpG();return e.Njj(i.searchReunions())}),e.j41(14,"option",25),e.EFF(15,"Tous les plannings"),e.k0s(),e.DNE(16,y,2,2,"option",26),e.k0s(),e.j41(17,"div",27),e.qSk(),e.j41(18,"svg",28),e.nrm(19,"path",29),e.k0s()()()()()()()}if(2&t){const n=e.XpG();e.Y8G("ngClass",e.eq3(5,C,n.showSearchBar)),e.R7$(8),e.Y8G("ngModel",n.searchTerm),e.R7$(1),e.Y8G("ngIf",n.searchTerm),e.R7$(4),e.Y8G("ngModel",n.selectedPlanning),e.R7$(3),e.Y8G("ngForOf",n.uniquePlannings)}}function I(t,s){1&t&&(e.qSk(),e.joV(),e.j41(0,"div",33),e.qSk(),e.j41(1,"svg",34),e.nrm(2,"path",35),e.k0s(),e.joV(),e.j41(3,"span"),e.EFF(4,"Les r\xe9unions \xe0 "),e.j41(5,"span",36),e.EFF(6,"pr\xe9sence obligatoire"),e.k0s(),e.EFF(7," sont affich\xe9es en premier"),e.k0s()())}function S(t,s){1&t&&(e.j41(0,"span",40),e.EFF(1," Aucune r\xe9union ne correspond \xe0 votre recherche. "),e.k0s())}function P(t,s){if(1&t&&(e.j41(0,"span"),e.EFF(1),e.k0s()),2&t){const n=e.XpG(2);e.R7$(1),e.SpI(" ",n.filteredReunions.length," r\xe9union(s) trouv\xe9e(s) ")}}function D(t,s){if(1&t&&(e.qSk(),e.joV(),e.j41(0,"div",37),e.DNE(1,S,2,0,"span",38),e.DNE(2,P,2,1,"span",39),e.k0s()),2&t){const n=e.XpG();e.R7$(1),e.Y8G("ngIf",0===n.filteredReunions.length),e.R7$(1),e.Y8G("ngIf",n.filteredReunions.length>0)}}function M(t,s){1&t&&(e.qSk(),e.joV(),e.j41(0,"div",41),e.nrm(1,"div",42),e.j41(2,"p",43),e.EFF(3,"Chargement de vos r\xe9unions..."),e.k0s()())}function $(t,s){if(1&t&&(e.qSk(),e.joV(),e.j41(0,"div",44)(1,"div",18),e.qSk(),e.j41(2,"svg",45),e.nrm(3,"path",46),e.k0s(),e.joV(),e.j41(4,"p"),e.EFF(5),e.k0s()()()),2&t){const n=e.XpG();e.R7$(5),e.SpI("Erreur lors du chargement des r\xe9unions: ",n.error.message,"")}}function T(t,s){if(1&t&&(e.qSk(),e.joV(),e.j41(0,"div",47)(1,"div",48),e.qSk(),e.j41(2,"svg",49),e.nrm(3,"path",50),e.k0s(),e.joV(),e.j41(4,"h3",51),e.EFF(5,"Aucune r\xe9union pr\xe9vue"),e.k0s(),e.j41(6,"p",52),e.EFF(7,"Vous pouvez cr\xe9er des r\xe9unions depuis la page d\xe9tail d'un planning."),e.k0s()()()),2&t){const n=e.XpG();e.AVh("animated",!n.loading)}}function V(t,s){1&t&&(e.j41(0,"span",82),e.EFF(1," Pr\xe9sence Obligatoire "),e.k0s())}function G(t,s){if(1&t&&(e.j41(0,"div",83)(1,"div",18),e.qSk(),e.j41(2,"svg",69),e.nrm(3,"path",84),e.k0s(),e.joV(),e.j41(4,"strong"),e.EFF(5,"Participants:\xa0"),e.k0s(),e.EFF(6),e.k0s()()),2&t){const n=e.XpG().$implicit;e.R7$(6),e.SpI("",n.participants.length," ")}}function O(t,s){if(1&t&&(e.j41(0,"div",85)(1,"a",86),e.qSk(),e.j41(2,"svg",75),e.nrm(3,"path",87),e.k0s(),e.EFF(4," Rejoindre la visioconf\xe9rence "),e.k0s()()),2&t){const n=e.XpG().$implicit;e.R7$(1),e.FS9("href",n.lienVisio,e.B4B)}}const L=function(t){return["/reunions/reunionDetails",t]};function Y(t,s){if(1&t){const n=e.RV6();e.j41(0,"div",55)(1,"div",56)(2,"div",17)(3,"div",18)(4,"h3",57)(5,"a",58),e.EFF(6),e.k0s()(),e.DNE(7,V,2,0,"span",59),e.k0s(),e.nrm(8,"p",60),e.nI1(9,"highlightPresence"),e.j41(10,"div",61),e.qSk(),e.j41(11,"svg",62),e.nrm(12,"path",63),e.k0s(),e.joV(),e.j41(13,"span"),e.EFF(14),e.nI1(15,"date"),e.k0s()()(),e.j41(16,"div",64)(17,"span"),e.EFF(18),e.nI1(19,"titlecase"),e.k0s(),e.j41(20,"button",65),e.bIt("click",function(i){const a=e.eBV(n).$implicit;return e.XpG(2).deleteReunion(a._id||a.id),e.Njj(i.stopPropagation())}),e.qSk(),e.j41(21,"svg",66),e.nrm(22,"path",67),e.k0s()()()(),e.joV(),e.j41(23,"div",68),e.qSk(),e.j41(24,"svg",69),e.nrm(25,"path",70),e.k0s(),e.joV(),e.j41(26,"span")(27,"strong"),e.EFF(28,"Cr\xe9ateur:"),e.k0s(),e.EFF(29),e.k0s()(),e.DNE(30,G,7,1,"div",71),e.j41(31,"div",68),e.qSk(),e.j41(32,"svg",69),e.nrm(33,"path",50),e.k0s(),e.joV(),e.j41(34,"span")(35,"strong"),e.EFF(36,"Planning:"),e.k0s(),e.EFF(37),e.k0s()(),e.DNE(38,O,5,1,"div",72),e.j41(39,"div",73)(40,"div",74),e.qSk(),e.j41(41,"svg",75),e.nrm(42,"path",76)(43,"path",77),e.k0s(),e.EFF(44),e.k0s(),e.joV(),e.j41(45,"div",78)(46,"a",79),e.bIt("click",function(){const o=e.eBV(n).$implicit,a=e.XpG(2);return e.Njj(a.editReunion(o._id||o.id))}),e.qSk(),e.j41(47,"svg",80),e.nrm(48,"path",81),e.k0s(),e.EFF(49," Modifier "),e.k0s()()()()}if(2&t){const n=s.$implicit,r=s.index,i=e.XpG(2);e.xc7("animation-delay",100*r+"ms"),e.AVh("animated",i.animateItems)("border-l-4",i.hasPresenceObligatoire(n))("border-red-500",i.hasPresenceObligatoire(n)),e.R7$(5),e.Y8G("routerLink",e.eq3(30,L,n._id)),e.R7$(1),e.JRh(n.titre),e.R7$(1),e.Y8G("ngIf",i.hasPresenceObligatoire(n)),e.R7$(1),e.Y8G("innerHTML",e.bMT(9,23,n.description),e.npT),e.R7$(6),e.E5c("",e.i5U(15,25,n.date,"mediumDate")," \u2022 ",n.heureDebut," - ",n.heureFin,""),e.R7$(3),e.HbH("px-3 py-1 text-xs rounded-full font-medium "+i.getStatutClass(n.statut)),e.R7$(1),e.SpI(" ",e.bMT(19,28,n.statut)," "),e.R7$(11),e.SpI(" ",n.createur.username,""),e.R7$(1),e.Y8G("ngIf",n.participants.length>0),e.R7$(7),e.SpI(" ",n.planning.titre,""),e.R7$(1),e.Y8G("ngIf",n.lienVisio),e.R7$(6),e.SpI(" ",n.lieu||"Lieu non sp\xe9cifi\xe9"," ")}}function N(t,s){if(1&t&&(e.qSk(),e.joV(),e.j41(0,"div",53),e.DNE(1,Y,50,32,"div",54),e.k0s()),2&t){const n=e.XpG();e.R7$(1),e.Y8G("ngForOf",n.searchTerm||n.selectedPlanning?n.filteredReunions:n.reunions)}}let U=(()=>{class t{get pageTitle(){return"admin"===this.authService.getCurrentUserRole()?"Toutes les R\xe9unions":"Mes R\xe9unions"}constructor(n,r,i,o,a){this.reunionService=n,this.router=r,this.authService=i,this.sanitizer=o,this.toastService=a,this.reunions=[],this.filteredReunions=[],this.loading=!0,this.animateItems=!1,this.showSearchBar=!1,this.searchTerm="",this.selectedPlanning="",this.uniquePlannings=[]}ngOnInit(){this.loadReunions(),console.log("\u{1f9ea} Test du service de toast...")}ngAfterViewInit(){setTimeout(()=>{this.animateItems=!0},100)}toggleSearchBar(){this.showSearchBar=!this.showSearchBar,this.showSearchBar||this.clearSearch()}clearSearch(){this.searchTerm="",this.selectedPlanning="",this.searchReunions()}searchReunions(){this.filteredReunions=this.searchTerm||this.selectedPlanning?this.reunions.filter(n=>(!this.searchTerm||n.titre&&n.titre.toLowerCase().includes(this.searchTerm.toLowerCase())||n.description&&n.description.toLowerCase().includes(this.searchTerm.toLowerCase()))&&(!this.selectedPlanning||n.planning&&n.planning._id===this.selectedPlanning)):[...this.reunions]}loadReunions(){this.loading=!0,this.animateItems=!1;const n=this.authService.getCurrentUserId(),r=this.authService.getCurrentUserRole();if(!n)return this.error="Utilisateur non connect\xe9",void(this.loading=!1);("admin"===r?this.reunionService.getAllReunionsAdmin():this.reunionService.getProchainesReunions(n)).subscribe({next:o=>{console.log("R\xe9unions charg\xe9es:",o),this.error=null,setTimeout(()=>{let a="admin"===r?o.data||o.reunions||[]:o.reunions||[];console.log("R\xe9unions r\xe9cup\xe9r\xe9es pour admin:",a),console.log("Structure de la premi\xe8re r\xe9union:",a[0]),a=this.ajouterPresenceObligatoirePourTest(a),this.reunions=this.trierReunionsParPresenceObligatoire(a),this.filteredReunions=[...this.reunions],this.extractUniquePlannings(),this.loading=!1,setTimeout(()=>{this.animateItems=!0},100)},300)},error:o=>{console.error("Erreur d\xe9taill\xe9e:",JSON.stringify(o)),this.error=`Erreur lors du chargement des r\xe9unions: ${o.message||o.statusText||"Erreur inconnue"}`,this.loading=!1}})}getStatutClass(n){switch(n){case"planifiee":return"bg-blue-100 text-blue-800";case"en_cours":return"bg-yellow-100 text-yellow-800";case"terminee":return"bg-green-100 text-green-800";case"annulee":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}}editReunion(n){console.log(n),this.reunions&&this.router.navigate(["/reunions/modifier",n])}deleteReunion(n){if(console.log("\u{1f5d1}\ufe0f Tentative de suppression de la r\xe9union avec ID:",n),confirm("\xcates-vous s\xfbr de vouloir supprimer cette r\xe9union ?")){const r=this.authService.getCurrentUserRole();console.log("\u{1f464} R\xf4le utilisateur:",r);const i="admin"===r?this.reunionService.forceDeleteReunion(n):this.reunionService.deleteReunion(n);console.log("\u{1f680} Envoi de la requ\xeate de suppression..."),i.subscribe({next:o=>{console.log("\u2705 R\xe9union supprim\xe9e avec succ\xe8s:",o),this.handleSuccessfulDeletion(n)},error:o=>{if(console.error("\u274c Erreur lors de la suppression:",o),console.error("\u{1f4cb} D\xe9tails de l'erreur:",{status:o.status,statusText:o.statusText,message:o.error?.message,fullError:o}),0===o.status||200===o.status)return console.log("\u{1f504} Erreur probablement li\xe9e \xe0 CORS ou r\xe9ponse mal format\xe9e, on consid\xe8re la suppression comme r\xe9ussie"),void this.handleSuccessfulDeletion(n);o.status>=500&&(console.log("\u{1f504} Erreur serveur, v\xe9rification de la suppression dans 2 secondes..."),setTimeout(()=>{this.loadReunions()},2e3)),403===o.status?this.toastService.accessDenied("supprimer cette r\xe9union",o.status):401===o.status?this.toastService.error("Non autoris\xe9","Vous devez \xeatre connect\xe9 pour supprimer une r\xe9union"):this.toastService.error("Erreur de suppression",o.error?.message||"Erreur lors de la suppression de la r\xe9union",8e3)}})}else console.log("\u274c Suppression annul\xe9e par l'utilisateur")}handleSuccessfulDeletion(n){console.log("\u{1f3af} Traitement de la suppression r\xe9ussie pour l'ID:",n);const r=this.reunions.length;this.reunions=this.reunions.filter(o=>o._id!==n&&o.id!==n),this.filteredReunions=this.filteredReunions.filter(o=>o._id!==n&&o.id!==n),console.log(`\u{1f4ca} R\xe9unions avant suppression: ${r}, apr\xe8s: ${this.reunions.length}`),this.extractUniquePlannings(),this.toastService.success("R\xe9union supprim\xe9e","La r\xe9union a \xe9t\xe9 supprim\xe9e avec succ\xe8s"),console.log("\u{1f389} Toast de succ\xe8s affich\xe9 et liste mise \xe0 jour"),this.loadReunions()}formatDescription(n){if(!n)return this.sanitizer.bypassSecurityTrustHtml("");const r=n.replace(/\(presence obligatoire\)/gi,'<span class="text-red-600 font-semibold">(presence obligatoire)</span>');return this.sanitizer.bypassSecurityTrustHtml(r)}hasPresenceObligatoire(n){return!!n.description&&[/presence obligatoire/i,/pr\xe9sence obligatoire/i,/obligatoire/i,/\(obligatoire\)/i,/\(presence obligatoire\)/i,/\(pr\xe9sence obligatoire\)/i].some(i=>i.test(n.description))}trierReunionsParPresenceObligatoire(n){if(!n||!n.length)return[];console.log("Avant tri - Nombre de r\xe9unions:",n.length),n.forEach((i,o)=>{const a=this.hasPresenceObligatoire(i);console.log(`R\xe9union ${o+1} - Titre: ${i.titre}, Description: ${i.description}, Pr\xe9sence Obligatoire: ${a}`)});const r=[...n].sort((i,o)=>{const a=this.hasPresenceObligatoire(i),u=this.hasPresenceObligatoire(o);return a&&!u?-1:!a&&u?1:new Date(o.date).getTime()-new Date(i.date).getTime()});return console.log("Apr\xe8s tri - Ordre des r\xe9unions:"),r.forEach((i,o)=>{const a=this.hasPresenceObligatoire(i);console.log(`Position ${o+1} - Titre: ${i.titre}, Pr\xe9sence Obligatoire: ${a}`)}),r}extractUniquePlannings(){const n=new Map;this.reunions.forEach(r=>{r.planning&&r.planning._id&&(n.has(r.planning._id)||n.set(r.planning._id,{id:r.planning._id,titre:r.planning.titre}))}),this.uniquePlannings=Array.from(n.values()),this.uniquePlannings.sort((r,i)=>r.titre.localeCompare(i.titre))}ajouterPresenceObligatoirePourTest(n){if(!n||0===n.length)return n;if(!n.some(i=>this.hasPresenceObligatoire(i))){if(console.log("Aucune r\xe9union avec pr\xe9sence obligatoire trouv\xe9e, ajout pour le test..."),n.length>0){const i=n[0];i.description=i.description?i.description+" (pr\xe9sence obligatoire)":"(pr\xe9sence obligatoire)",console.log(`Ajout de "pr\xe9sence obligatoire" \xe0 la r\xe9union: ${i.titre}`)}if(n.length>=3){const i=n[2];i.description=i.description?i.description+" (pr\xe9sence obligatoire)":"(pr\xe9sence obligatoire)",console.log(`Ajout de "pr\xe9sence obligatoire" \xe0 la r\xe9union: ${i.titre}`)}}return n}static{this.\u0275fac=function(r){return new(r||t)(e.rXU(b.C),e.rXU(f.Ix),e.rXU(x.V),e.rXU(k.up),e.rXU(v.f))}}static{this.\u0275cmp=e.VBU({type:t,selectors:[["app-reunion-list"]],decls:17,vars:8,consts:[[1,"container","mx-auto","px-4","py-6","page-container","page-enter"],[1,"flex","flex-col","mb-8"],[1,"flex","justify-between","items-center"],[1,"text-2xl","font-bold","text-gray-800","page-title"],[1,"relative"],[1,"search-button","px-4","py-2","bg-purple-200","text-purple-800","rounded-md","hover:bg-purple-300","transition-colors","transform","hover:scale-105","duration-200","flex","items-center","shadow-sm","border","border-purple-300",3,"click"],["fill","none","viewBox","0 0 24 24","stroke","currentColor",1,"h-5","w-5","mr-2","text-purple-600"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"],["class","mt-4 bg-white p-4 rounded-lg shadow-md transition-all duration-300 animate-fadeIn",3,"ngClass",4,"ngIf"],["class","mt-2 text-sm text-gray-600 flex items-center",4,"ngIf"],["class","mt-2 text-sm text-gray-600",4,"ngIf"],["class","text-center py-12",4,"ngIf"],["class","bg-red-100 border-l-4 border-red-500 text-red-700 p-4 rounded-md mb-6 shadow-md transform transition-all duration-500 hover:shadow-lg",4,"ngIf"],["class","text-center py-12 empty-container",3,"animated",4,"ngIf"],["class","grid grid-cols-1 md:grid-cols-2 gap-6",4,"ngIf"],[1,"mt-4","bg-white","p-4","rounded-lg","shadow-md","transition-all","duration-300","animate-fadeIn",3,"ngClass"],[1,"flex","flex-col","md:flex-row","gap-4"],[1,"flex-1"],[1,"flex","items-center"],[1,"absolute","left-3","top-1/2","transform","-translate-y-1/2","text-purple-400"],["fill","none","viewBox","0 0 24 24","stroke","currentColor",1,"h-5","w-5"],["type","text","id","searchTerm","placeholder","Rechercher par titre ou description",1,"w-full","pl-10","pr-10","py-3","border","border-gray-300","rounded-md","focus:ring-2","focus:ring-purple-300","focus:border-purple-400","transition-all","duration-300",3,"ngModel","ngModelChange","input"],["class","absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-purple-600 transition-colors",3,"click",4,"ngIf"],[1,"md:w-1/3"],["id","planningFilter",1,"w-full","px-4","py-3","border","border-gray-300","rounded-md","focus:ring-2","focus:ring-purple-300","focus:border-purple-400","transition-all","duration-300","appearance-none",3,"ngModel","ngModelChange","change"],["value",""],[3,"value",4,"ngFor","ngForOf"],[1,"absolute","inset-y-0","right-0","flex","items-center","pr-3","pointer-events-none"],["fill","none","viewBox","0 0 24 24","stroke","currentColor",1,"h-5","w-5","text-purple-500"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M19 9l-7 7-7-7"],[1,"absolute","right-3","top-1/2","transform","-translate-y-1/2","text-gray-400","hover:text-purple-600","transition-colors",3,"click"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M6 18L18 6M6 6l12 12"],[3,"value"],[1,"mt-2","text-sm","text-gray-600","flex","items-center"],["fill","none","viewBox","0 0 24 24","stroke","currentColor",1,"h-4","w-4","mr-2","text-red-500"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M13 10V3L4 14h7v7l9-11h-7z"],[1,"font-semibold","text-red-600"],[1,"mt-2","text-sm","text-gray-600"],["class","text-red-500",4,"ngIf"],[4,"ngIf"],[1,"text-red-500"],[1,"text-center","py-12"],[1,"loading-spinner","rounded-full","h-16","w-16","border-4","border-purple-200","border-t-purple-600","mx-auto"],[1,"mt-4","text-gray-600","animate-pulse"],[1,"bg-red-100","border-l-4","border-red-500","text-red-700","p-4","rounded-md","mb-6","shadow-md","transform","transition-all","duration-500","hover:shadow-lg"],["fill","none","viewBox","0 0 24 24","stroke","currentColor",1,"h-6","w-6","mr-3","text-red-500"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"],[1,"text-center","py-12","empty-container"],[1,"bg-white","rounded-lg","shadow-md","p-8","max-w-md","mx-auto"],["fill","none","viewBox","0 0 24 24","stroke","currentColor",1,"mx-auto","h-16","w-16","text-purple-400"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"],[1,"mt-4","text-xl","font-medium","text-gray-900"],[1,"mt-2","text-gray-500"],[1,"grid","grid-cols-1","md:grid-cols-2","gap-6"],["class","bg-white rounded-lg shadow-md p-5 hover:shadow-xl transition-all duration-300 reunion-card",3,"animated","border-l-4","border-red-500","animation-delay",4,"ngFor","ngForOf"],[1,"bg-white","rounded-lg","shadow-md","p-5","hover:shadow-xl","transition-all","duration-300","reunion-card"],[1,"flex","justify-between","items-start"],[1,"text-lg","font-semibold","text-gray-800","hover:text-purple-600","transition-colors"],[1,"hover:text-purple-600",3,"routerLink"],["class","ml-2 px-2 py-1 text-xs bg-red-100 text-red-800 rounded-full font-bold animate-pulse",4,"ngIf"],[1,"text-sm","mt-1",3,"innerHTML"],[1,"mt-3","flex","items-center","text-sm","text-gray-500"],["fill","none","viewBox","0 0 24 24","stroke","currentColor",1,"h-4","w-4","mr-2","text-purple-500"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"],[1,"flex","items-start","space-x-2"],["title","Supprimer la r\xe9union",1,"text-red-500","hover:text-red-700","transition-colors","duration-300","p-1","rounded-full","hover:bg-red-50",3,"click"],["xmlns","http://www.w3.org/2000/svg","fill","none","viewBox","0 0 24 24","stroke","currentColor",1,"h-5","w-5"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"],[1,"mt-3","text-sm","text-gray-600","flex","items-center"],["fill","none","viewBox","0 0 24 24","stroke","currentColor",1,"h-4","w-4","mr-2","text-gray-500"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"],["class","mt-3 text-sm text-gray-600",4,"ngIf"],["class","mt-3 text-sm",4,"ngIf"],[1,"mt-4","pt-3","border-t","border-gray-100","flex","justify-between","items-center"],[1,"flex","items-center","text-sm","text-gray-500"],["fill","none","viewBox","0 0 24 24","stroke","currentColor",1,"h-4","w-4","mr-2"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M15 11a3 3 0 11-6 0 3 3 0 016 0z"],[1,"flex","items-center","space-x-2"],[1,"px-4","py-2","bg-purple-600","text-white","rounded-md","hover:bg-purple-700","transition-all","duration-300","transform","hover:scale-105","flex","items-center",3,"click"],["fill","none","viewBox","0 0 24 24","stroke","currentColor",1,"h-4","w-4","mr-1"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"],[1,"ml-2","px-2","py-1","text-xs","bg-red-100","text-red-800","rounded-full","font-bold","animate-pulse"],[1,"mt-3","text-sm","text-gray-600"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"],[1,"mt-3","text-sm"],["target","_blank",1,"text-purple-600","hover:text-purple-800","flex","items-center","transition-colors",3,"href"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"]],template:function(r,i){1&r&&(e.j41(0,"div",0)(1,"div",1)(2,"div",2)(3,"h1",3),e.EFF(4),e.k0s(),e.j41(5,"div",4)(6,"button",5),e.bIt("click",function(){return i.toggleSearchBar()}),e.qSk(),e.j41(7,"svg",6),e.nrm(8,"path",7),e.k0s(),e.EFF(9," Rechercher "),e.k0s()()(),e.DNE(10,w,20,7,"div",8),e.DNE(11,I,8,0,"div",9),e.DNE(12,D,3,2,"div",10),e.k0s(),e.DNE(13,M,4,0,"div",11),e.DNE(14,$,6,1,"div",12),e.DNE(15,T,8,2,"div",13),e.DNE(16,N,2,1,"div",14),e.k0s()),2&r&&(e.R7$(4),e.JRh(i.pageTitle),e.R7$(6),e.Y8G("ngIf",i.showSearchBar),e.R7$(1),e.Y8G("ngIf",!i.loading&&i.reunions.length>0),e.R7$(1),e.Y8G("ngIf",i.searchTerm||i.selectedPlanning),e.R7$(1),e.Y8G("ngIf",i.loading),e.R7$(1),e.Y8G("ngIf",i.error),e.R7$(1),e.Y8G("ngIf",!i.loading&&0===i.reunions.length),e.R7$(1),e.Y8G("ngIf",!i.loading&&i.reunions.length>0))},dependencies:[m.YU,m.Sq,m.bT,f.Wk,l.xH,l.y7,l.me,l.wz,l.BC,l.vS,m.PV,m.vh,F.I],styles:['.page-container[_ngcontent-%COMP%]{overflow:hidden}.page-title[_ngcontent-%COMP%]{position:relative;display:inline-block}.page-title[_ngcontent-%COMP%]:after{content:"";position:absolute;width:0;height:3px;bottom:-5px;left:0;background-color:#8b5cf6;transition:width .6s ease}.page-title[_ngcontent-%COMP%]:hover:after{width:100%}.reunion-card[_ngcontent-%COMP%]{transform:translateY(30px);opacity:0;transition:all .5s cubic-bezier(.4,0,.2,1)}.reunion-card.animated[_ngcontent-%COMP%]{transform:translateY(0);opacity:1}.empty-container[_ngcontent-%COMP%]{transform:scale(.8);opacity:0;transition:all .6s cubic-bezier(.34,1.56,.64,1)}.empty-container.animated[_ngcontent-%COMP%]{transform:scale(1);opacity:1}.loading-spinner[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_pulse 1.5s infinite ease-in-out}@keyframes _ngcontent-%COMP%_pulse{0%{transform:scale(.95);box-shadow:0 0 #8b5cf6b3}70%{transform:scale(1);box-shadow:0 0 0 10px #8b5cf600}to{transform:scale(.95);box-shadow:0 0 #8b5cf600}}.page-enter[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_fadeInUp .8s forwards}@keyframes _ngcontent-%COMP%_fadeInUp{0%{opacity:0;transform:translateY(40px)}to{opacity:1;transform:translateY(0)}}.animate-fadeIn[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_fadeIn .3s ease-in-out}@keyframes _ngcontent-%COMP%_slideInFromRight{0%{transform:translate(30px);opacity:0}to{transform:translate(0);opacity:1}}.flex-col[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(1){animation:_ngcontent-%COMP%_slideInFromRight .4s ease-out forwards}.flex-col[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(2){animation:_ngcontent-%COMP%_slideInFromRight .4s ease-out .1s forwards;opacity:0}input[_ngcontent-%COMP%], select[_ngcontent-%COMP%]{transition:all .3s ease}input[_ngcontent-%COMP%]:focus, select[_ngcontent-%COMP%]:focus{transform:translateY(-2px);box-shadow:0 4px 6px -1px #8b5cf61a,0 2px 4px -1px #8b5cf60f}@keyframes _ngcontent-%COMP%_gentle-pulse{0%{box-shadow:0 0 #a78bfa66}70%{box-shadow:0 0 0 6px #a78bfa00}to{box-shadow:0 0 #a78bfa00}}.search-button[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_gentle-pulse 2s infinite}@keyframes _ngcontent-%COMP%_fadeIn{0%{opacity:0;transform:translateY(-10px)}to{opacity:1;transform:translateY(0)}}.search-input-container[_ngcontent-%COMP%]{position:relative;overflow:hidden;transition:all .3s ease}.search-input-container[_ngcontent-%COMP%]:hover{transform:translateY(-2px)}.search-input[_ngcontent-%COMP%]{position:relative;z-index:1;background:transparent;transition:all .3s ease}.search-input[_ngcontent-%COMP%]:focus{box-shadow:0 0 0 3px #8b5cf64d}.search-input-container[_ngcontent-%COMP%]:before{content:"";position:absolute;top:0;left:-100%;width:100%;height:100%;background:linear-gradient(90deg,transparent,rgba(139,92,246,.1),transparent);transition:all .6s ease;z-index:0}.search-input-container[_ngcontent-%COMP%]:hover:before{left:100%;transition:all .6s ease}@keyframes _ngcontent-%COMP%_subtlePulse{0%{box-shadow:0 0 #8b5cf633}50%{box-shadow:0 0 0 5px #8b5cf600}to{box-shadow:0 0 #8b5cf600}}.search-input-container[_ngcontent-%COMP%]:focus-within{animation:_ngcontent-%COMP%_subtlePulse 2s infinite;border-color:#8b5cf6}@keyframes _ngcontent-%COMP%_rotateIcon{0%{transform:rotate(0)}25%{transform:rotate(-10deg)}75%{transform:rotate(10deg)}to{transform:rotate(0)}}.search-icon[_ngcontent-%COMP%]{transition:all .3s ease}.search-input-container[_ngcontent-%COMP%]:focus-within   .search-icon[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_rotateIcon 1s ease;color:#8b5cf6}.search-input[_ngcontent-%COMP%]::placeholder{transition:all .3s ease}.search-input[_ngcontent-%COMP%]:focus::placeholder{opacity:.5;transform:translate(10px)}.floating-label[_ngcontent-%COMP%]{position:absolute;left:12px;top:50%;transform:translateY(-50%);font-size:14px;color:#9ca3af;pointer-events:none;transition:all .3s ease;z-index:2}.search-input[_ngcontent-%COMP%]:focus ~ .floating-label[_ngcontent-%COMP%], .search-input[_ngcontent-%COMP%]:not(:placeholder-shown) ~ .floating-label[_ngcontent-%COMP%]{top:0;left:8px;font-size:12px;padding:0 4px;background-color:#fff;color:#8b5cf6;transform:translateY(-50%)}.search-select[_ngcontent-%COMP%]{position:relative;transition:all .3s ease;background-image:linear-gradient(to right,#f9fafb 0%,white 100%)}.search-select[_ngcontent-%COMP%]:hover{background-image:linear-gradient(to right,#f3f4f6 0%,white 100%)}.search-select[_ngcontent-%COMP%]:focus{box-shadow:0 0 0 3px #8b5cf64d;background-image:linear-gradient(to right,#f3f4f6 0%,white 100%)}.staggered-item[_ngcontent-%COMP%]{opacity:0;transform:translateY(20px)}.staggered-item.animated[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_fadeInStaggered .5s forwards}@keyframes _ngcontent-%COMP%_fadeInStaggered{to{opacity:1;transform:translateY(0)}}'],data:{animation:[(0,g.hZ)("fadeIn",[(0,g.kY)(":enter",[(0,g.iF)({opacity:0,transform:"translateY(20px)"}),(0,g.i0)("0.4s ease-out",(0,g.iF)({opacity:1,transform:"translateY(0)"}))])]),(0,g.hZ)("staggerList",[(0,g.kY)("* => *",[(0,g.P)(":enter",[(0,g.iF)({opacity:0,transform:"translateY(30px)"}),(0,g.yc)("100ms",[(0,g.i0)("0.5s ease-out",(0,g.iF)({opacity:1,transform:"translateY(0)"}))])],{optional:!0})])])]}})}}return t})();var X=p(152),z=p(3294),R=p(6543),E=p(8490);function B(t,s){if(1&t&&(e.j41(0,"div",61),e.EFF(1),e.k0s()),2&t){const n=e.XpG();e.R7$(1),e.SpI(" ",n.error.message||"Une erreur est survenue"," ")}}function q(t,s){if(1&t&&(e.j41(0,"div",62),e.EFF(1),e.k0s()),2&t){const n=e.XpG();e.R7$(1),e.SpI(" ",n.successMessage," ")}}function H(t,s){1&t&&(e.j41(0,"div",63),e.nrm(1,"i",64),e.EFF(2," Le titre est obligatoire "),e.k0s())}function A(t,s){1&t&&(e.j41(0,"div",63),e.nrm(1,"i",64),e.EFF(2," La date est obligatoire "),e.k0s())}function J(t,s){1&t&&(e.j41(0,"div",63),e.nrm(1,"i",64),e.EFF(2," L'heure de d\xe9but est obligatoire "),e.k0s())}function W(t,s){1&t&&(e.j41(0,"div",63),e.nrm(1,"i",64),e.EFF(2," L'heure de fin est obligatoire "),e.k0s())}function Z(t,s){1&t&&(e.j41(0,"span",65),e.qSk(),e.j41(1,"svg",66),e.nrm(2,"circle",67)(3,"path",68),e.k0s(),e.EFF(4," V\xe9rification... "),e.k0s())}function K(t,s){if(1&t&&(e.j41(0,"div",69),e.nrm(1,"i",70),e.EFF(2),e.k0s()),2&t){const n=e.XpG();e.R7$(2),e.SpI(" ",n.lienVisioError," ")}}function Q(t,s){1&t&&(e.j41(0,"div",71),e.nrm(1,"i",72),e.EFF(2," Lien disponible "),e.k0s())}function ee(t,s){if(1&t&&(e.j41(0,"option",81),e.EFF(1),e.k0s()),2&t){const n=s.$implicit;e.Y8G("value",n._id),e.R7$(1),e.JRh(n.titre)}}function ne(t,s){1&t&&(e.j41(0,"div",63),e.nrm(1,"i",64),e.EFF(2," Le planning est obligatoire "),e.k0s())}function te(t,s){if(1&t&&(e.j41(0,"div",73)(1,"h3",74),e.nrm(2,"i",75),e.EFF(3," Planning "),e.k0s(),e.j41(4,"label",76),e.nrm(5,"i",77),e.EFF(6," S\xe9lectionnez un planning * "),e.k0s(),e.j41(7,"select",78)(8,"option",79),e.EFF(9,"Choisissez un planning..."),e.k0s(),e.DNE(10,ee,2,2,"option",80),e.k0s(),e.DNE(11,ne,3,0,"div",13),e.k0s()),2&t){const n=e.XpG();let r;e.R7$(10),e.Y8G("ngForOf",n.plannings),e.R7$(1),e.Y8G("ngIf",(null==(r=n.reunionForm.get("planning"))?null:r.invalid)&&(null==(r=n.reunionForm.get("planning"))?null:r.touched))}}function ie(t,s){if(1&t&&(e.j41(0,"div",90),e.nrm(1,"i",53),e.EFF(2),e.k0s()),2&t){const n=e.XpG(2);e.R7$(2),e.SpI(" ",n.selectedPlanning.description," ")}}function re(t,s){if(1&t&&(e.j41(0,"div",82)(1,"h3",83),e.nrm(2,"i",84),e.EFF(3," Planning s\xe9lectionn\xe9 "),e.k0s(),e.j41(4,"div",85)(5,"span",86),e.nrm(6,"i",75),e.EFF(7),e.k0s(),e.j41(8,"span",87),e.nrm(9,"i",88),e.EFF(10),e.nI1(11,"date"),e.nI1(12,"date"),e.k0s()(),e.DNE(13,ie,3,1,"div",89),e.k0s()),2&t){const n=e.XpG();e.R7$(7),e.SpI(" ",n.selectedPlanning.titre," "),e.R7$(3),e.Lme(" ",e.i5U(11,4,n.selectedPlanning.dateDebut,"dd/MM/yyyy")," - ",e.i5U(12,7,n.selectedPlanning.dateFin,"dd/MM/yyyy")," "),e.R7$(3),e.Y8G("ngIf",n.selectedPlanning.description)}}function oe(t,s){if(1&t&&(e.j41(0,"option",92),e.nrm(1,"i",93),e.EFF(2),e.k0s()),2&t){const n=s.$implicit;e.Y8G("value",n._id),e.R7$(2),e.SpI("",n.username," ")}}function se(t,s){if(1&t&&(e.qex(0),e.DNE(1,oe,3,2,"option",91),e.bVm()),2&t){const n=s.ngIf;e.R7$(1),e.Y8G("ngForOf",n)}}function ae(t,s){1&t&&e.nrm(0,"i",94)}function le(t,s){1&t&&e.nrm(0,"i",95)}function ue(t,s){1&t&&e.nrm(0,"i",96)}let de=(()=>{class t{constructor(n,r,i,o,a,u,d,c){this.fb=n,this.reunionService=r,this.planningService=i,this.userService=o,this.route=a,this.router=u,this.authService=d,this.toastService=c,this.plannings=[],this.loading=!0,this.isSubmitting=!1,this.error=null,this.successMessage=null,this.isEditMode=!1,this.currentReunionId=null,this.planningIdFromUrl=null,this.selectedPlanning=null,this.lienVisioError=null,this.isCheckingLienVisio=!1,this.reunionForm=this.fb.group({titre:["",l.k0.required],description:[""],date:["",l.k0.required],heureDebut:["",l.k0.required],heureFin:["",l.k0.required],lieu:[""],lienVisio:[""],planning:["",l.k0.required],participants:[[]]}),this.users$=this.userService.getAllUsers()}ngOnInit(){this.loadPlannings(),this.checkEditMode(),this.checkPlanningParam(),this.setupLienVisioValidation()}checkEditMode(){const n=this.route.snapshot.paramMap.get("id");n&&(this.isEditMode=!0,this.currentReunionId=n,this.loadReunion(n))}loadPlannings(){const n=this.authService.getCurrentUserId();n&&this.planningService.getPlanningsByUser(n).subscribe({next:r=>{this.plannings=r.plannings||[],console.log("\u{1f50d} Plannings charg\xe9s:",this.plannings),console.log("\u{1f50d} Premier planning:",this.plannings[0])},error:r=>{this.error=r,console.error("\u274c Erreur chargement plannings:",r)}})}loadReunion(n){this.reunionService.getReunionById(n).subscribe({next:r=>{this.reunionForm.patchValue({titre:r.titre,description:r.description,dateDebut:this.formatDateForInput(r.dateDebut),dateFin:this.formatDateForInput(r.dateFin),lieu:r.lieu,lienVisio:r.lienVisio,planningId:r.planningId,participants:r.participants}),this.loading=!1},error:r=>{this.error=r,this.loading=!1}})}formatDateForInput(n){return new Date(n).toISOString().slice(0,16)}checkPlanningParam(){const n=this.route.snapshot.queryParamMap.get("planningId");n&&(this.planningIdFromUrl=n,this.reunionForm.patchValue({planning:n}),this.planningService.getPlanningById(n).subscribe({next:r=>{this.selectedPlanning=r.planning,this.selectedPlanning&&!this.plannings.find(i=>i._id===n)&&(this.plannings.push(this.selectedPlanning),console.log("\u2705 Planning ajout\xe9 \xe0 la liste locale pour validation:",this.selectedPlanning))},error:r=>{console.error("Erreur lors de la r\xe9cup\xe9ration du planning:",r),this.toastService.error("Planning introuvable","Le planning s\xe9lectionn\xe9 n'existe pas ou vous n'avez pas les permissions pour y acc\xe9der")}}))}onSubmit(){if(this.reunionForm.invalid||!this.canSubmit())return void this.toastService.warning("Formulaire invalide","Veuillez corriger les erreurs avant de soumettre le formulaire");if(!this.validateDateInPlanningRange())return;this.isSubmitting=!0,this.error=null,this.successMessage=null;const n=this.reunionForm.value,a={titre:n.titre,description:n.description,date:n.date,heureDebut:n.heureDebut,heureFin:n.heureFin,lieu:n.lieu,lienVisio:n.lienVisio,planning:n.planning,participants:n.participants||[]};console.log("\u{1f50d} Donn\xe9es de la r\xe9union \xe0 envoyer:",a),console.log("\u{1f50d} Planning ID s\xe9lectionn\xe9:",n.planning),console.log("\u{1f50d} Type du planning ID:",typeof n.planning),console.log("\u{1f50d} Plannings disponibles:",this.plannings),this.reunionService.createReunion(a).subscribe({next:()=>{this.isSubmitting=!1,this.toastService.success("R\xe9union cr\xe9\xe9e","La r\xe9union a \xe9t\xe9 cr\xe9\xe9e avec succ\xe8s"),this.resetForm(),this.router.navigate(["/reunions"])},error:u=>{this.isSubmitting=!1,console.error("Erreur lors de la cr\xe9ation de la r\xe9union:",u),403===u.status?this.toastService.accessDenied("cr\xe9er une r\xe9union",u.status):401===u.status?this.toastService.error("Non autoris\xe9","Vous devez \xeatre connect\xe9 pour cr\xe9er une r\xe9union"):this.toastService.error("Erreur de cr\xe9ation",u.error?.message||"Erreur lors de la cr\xe9ation de la r\xe9union",8e3)}})}resetForm(){this.reunionForm.reset({titre:"",description:"",date:"",heureDebut:"",heureFin:"",lieu:"",lienVisio:"",planning:"",participants:[]}),this.reunionForm.markAsPristine(),this.reunionForm.markAsUntouched()}goReunion(){this.router.navigate(["/reunions"])}setupLienVisioValidation(){this.reunionForm.get("lienVisio")?.valueChanges.pipe((0,X.B)(500),(0,z.F)()).subscribe(n=>{n&&""!==n.trim()?this.checkLienVisioUniqueness(n.trim()):this.lienVisioError=null})}checkLienVisioUniqueness(n){n&&""!==n.trim()?(this.isCheckingLienVisio=!0,this.lienVisioError=null,this.reunionService.checkLienVisioUniqueness(n,this.currentReunionId||void 0).subscribe({next:r=>{this.isCheckingLienVisio=!1,this.lienVisioError=r.success&&!r.isUnique?`Ce lien est d\xe9j\xe0 utilis\xe9 par la r\xe9union "${r.conflictWith?.titre}"`:null},error:r=>{this.isCheckingLienVisio=!1,console.error("Erreur lors de la v\xe9rification du lien visio:",r),this.lienVisioError="Erreur lors de la v\xe9rification du lien"}})):this.lienVisioError=null}canSubmit(){return this.reunionForm.valid&&!this.lienVisioError&&!this.isCheckingLienVisio}validateDateInPlanningRange(){const n=this.reunionForm.value,r=n.date,i=n.planning;if(!r||!i)return!0;let o=this.plannings.find(c=>c._id===i);if(!o&&this.selectedPlanning&&this.selectedPlanning._id===i&&(o=this.selectedPlanning),!o)return console.warn("\u26a0\ufe0f Planning non trouv\xe9 pour validation:",i),console.log("\u{1f4cb} Plannings disponibles:",this.plannings.map(c=>({id:c._id,titre:c.titre}))),console.log("\u{1f3af} Selected planning:",this.selectedPlanning),!0;const a=new Date(r),u=new Date(o.dateDebut),d=new Date(o.dateFin);return a.setHours(0,0,0,0),u.setHours(0,0,0,0),d.setHours(0,0,0,0),!(a<u||a>d)||(this.toastService.error("Date invalide",`La date de la r\xe9union doit \xeatre comprise entre le ${u.toLocaleDateString("fr-FR")} et le ${d.toLocaleDateString("fr-FR")} (p\xe9riode du planning "${o.titre}")`,1e4),!1)}static{this.\u0275fac=function(r){return new(r||t)(e.rXU(l.ok),e.rXU(b.C),e.rXU(R.z),e.rXU(E.u),e.rXU(f.nX),e.rXU(f.Ix),e.rXU(x.V),e.rXU(v.f))}}static{this.\u0275cmp=e.VBU({type:t,selectors:[["app-reunion-form"]],decls:87,vars:24,consts:[[1,"container","mx-auto","px-4","py-6","max-w-3xl"],[1,"bg-gradient-to-r","from-purple-600","to-indigo-600","rounded-t-lg","p-6","text-white","mb-0"],[1,"text-2xl","font-bold","flex","items-center"],[1,"fas","fa-plus-circle","mr-3","text-purple-200"],[1,"text-purple-100","mt-2"],[1,"bg-white","rounded-b-lg","shadow-lg","p-6","border-t-0",3,"formGroup","ngSubmit"],["class","bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4",4,"ngIf"],["class","bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4",4,"ngIf"],[1,"grid","grid-cols-1","gap-6"],[1,"relative"],["for","titre",1,"block","text-sm","font-medium","text-purple-700","mb-2"],[1,"fas","fa-tag","mr-2","text-purple-500"],["id","titre","type","text","formControlName","titre","placeholder","Nom de votre r\xe9union...",1,"mt-1","block","w-full","rounded-lg","border-2","border-purple-200","shadow-sm","focus:border-purple-500","focus:ring-purple-500","focus:ring-2","transition-all","duration-200","px-4","py-3"],["class","text-red-500 text-sm mt-2 flex items-center",4,"ngIf"],["for","description",1,"block","text-sm","font-medium","text-indigo-700","mb-2"],[1,"fas","fa-align-left","mr-2","text-indigo-500"],["id","description","formControlName","description","rows","3","placeholder","D\xe9crivez votre r\xe9union...",1,"mt-1","block","w-full","rounded-lg","border-2","border-indigo-200","shadow-sm","focus:border-indigo-500","focus:ring-indigo-500","focus:ring-2","transition-all","duration-200","px-4","py-3"],[1,"bg-gradient-to-r","from-blue-50","to-cyan-50","p-4","rounded-lg","border","border-blue-200"],[1,"text-lg","font-semibold","text-blue-800","mb-4","flex","items-center"],[1,"fas","fa-calendar-clock","mr-2","text-blue-600"],[1,"grid","grid-cols-1","md:grid-cols-3","gap-6"],["for","date",1,"block","text-sm","font-medium","text-blue-700","mb-2"],[1,"fas","fa-calendar","mr-2","text-blue-500"],["id","date","type","date","formControlName","date",1,"mt-1","block","w-full","rounded-lg","border-2","border-blue-200","shadow-sm","focus:border-blue-500","focus:ring-blue-500","focus:ring-2","transition-all","duration-200","px-4","py-3"],["for","heureDebut",1,"block","text-sm","font-medium","text-green-700","mb-2"],[1,"fas","fa-play","mr-2","text-green-500"],["id","heureDebut","type","time","formControlName","heureDebut",1,"mt-1","block","w-full","rounded-lg","border-2","border-green-200","shadow-sm","focus:border-green-500","focus:ring-green-500","focus:ring-2","transition-all","duration-200","px-4","py-3"],["for","heureFin",1,"block","text-sm","font-medium","text-red-700","mb-2"],[1,"fas","fa-stop","mr-2","text-red-500"],["id","heureFin","type","time","formControlName","heureFin",1,"mt-1","block","w-full","rounded-lg","border-2","border-red-200","shadow-sm","focus:border-red-500","focus:ring-red-500","focus:ring-2","transition-all","duration-200","px-4","py-3"],[1,"bg-gradient-to-r","from-orange-50","to-yellow-50","p-4","rounded-lg","border","border-orange-200"],[1,"text-lg","font-semibold","text-orange-800","mb-4","flex","items-center"],[1,"fas","fa-map-marker-alt","mr-2","text-orange-600"],[1,"grid","grid-cols-1","md:grid-cols-2","gap-6"],["for","lieu",1,"block","text-sm","font-medium","text-orange-700","mb-2"],[1,"fas","fa-map-marker-alt","mr-2","text-orange-500"],["id","lieu","type","text","formControlName","lieu","placeholder","Salle 101, Bureau A, Google Meet...",1,"mt-1","block","w-full","rounded-lg","border-2","border-orange-200","shadow-sm","focus:border-orange-500","focus:ring-orange-500","focus:ring-2","transition-all","duration-200","px-4","py-3"],["for","lienVisio",1,"block","text-sm","font-medium","text-cyan-700","mb-2"],[1,"fas","fa-video","mr-2","text-cyan-500"],["class","ml-2 text-xs text-cyan-500",4,"ngIf"],["id","lienVisio","type","url","formControlName","lienVisio","placeholder","https://meet.google.com/..."],["class","text-red-500 text-sm mt-2 flex items-center bg-red-50 p-2 rounded border border-red-200",4,"ngIf"],["class","text-green-600 text-sm mt-2 flex items-center bg-green-50 p-2 rounded border border-green-200",4,"ngIf"],["class","bg-gradient-to-r from-purple-50 to-pink-50 p-4 rounded-lg border border-purple-200",4,"ngIf"],["class","bg-gradient-to-r from-purple-50 to-indigo-50 p-4 rounded-lg border-2 border-purple-200 shadow-sm",4,"ngIf"],[1,"bg-gradient-to-r","from-emerald-50","to-teal-50","p-4","rounded-lg","border","border-emerald-200"],[1,"text-lg","font-semibold","text-emerald-800","mb-4","flex","items-center"],[1,"fas","fa-users","mr-2","text-emerald-600"],[1,"block","text-sm","font-medium","text-emerald-700","mb-2"],[1,"fas","fa-user-friends","mr-2","text-emerald-500"],["formControlName","participants","multiple","",1,"mt-1","block","w-full","px-4","py-3","border-2","border-emerald-200","rounded-lg","shadow-sm","focus:ring-emerald-500","focus:border-emerald-500","focus:ring-2","transition-all","duration-200","text-sm","min-h-[120px]"],[4,"ngIf"],[1,"text-xs","text-emerald-600","mt-2"],[1,"fas","fa-info-circle","mr-1"],[1,"mt-8","flex","justify-end","space-x-4","bg-gray-50","p-4","rounded-lg","border-t","border-gray-200"],["type","button",1,"px-6","py-3","border-2","border-gray-300","rounded-lg","text-sm","font-medium","text-gray-700","hover:bg-gray-100","hover:border-gray-400","transition-all","duration-200","flex","items-center",3,"click"],[1,"fas","fa-times","mr-2"],["type","submit",1,"px-6","py-3","rounded-lg","text-sm","font-medium","text-white","bg-gradient-to-r","from-purple-600","to-indigo-600","hover:from-purple-700","hover:to-indigo-700","disabled:opacity-50","disabled:cursor-not-allowed","transition-all","duration-200","flex","items-center","shadow-lg",3,"disabled"],["class","fas fa-save mr-2",4,"ngIf"],["class","fas fa-spinner fa-spin mr-2",4,"ngIf"],["class","fas fa-search mr-2",4,"ngIf"],[1,"bg-red-100","border","border-red-400","text-red-700","px-4","py-3","rounded","mb-4"],[1,"bg-green-100","border","border-green-400","text-green-700","px-4","py-3","rounded","mb-4"],[1,"text-red-500","text-sm","mt-2","flex","items-center"],[1,"fas","fa-exclamation-circle","mr-1"],[1,"ml-2","text-xs","text-cyan-500"],["fill","none","viewBox","0 0 24 24",1,"inline","h-3","w-3","animate-spin"],["cx","12","cy","12","r","10","stroke","currentColor","stroke-width","4",1,"opacity-25"],["fill","currentColor","d","M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z",1,"opacity-75"],[1,"text-red-500","text-sm","mt-2","flex","items-center","bg-red-50","p-2","rounded","border","border-red-200"],[1,"fas","fa-exclamation-triangle","mr-2"],[1,"text-green-600","text-sm","mt-2","flex","items-center","bg-green-50","p-2","rounded","border","border-green-200"],[1,"fas","fa-check-circle","mr-2"],[1,"bg-gradient-to-r","from-purple-50","to-pink-50","p-4","rounded-lg","border","border-purple-200"],[1,"text-lg","font-semibold","text-purple-800","mb-4","flex","items-center"],[1,"fas","fa-calendar-alt","mr-2","text-purple-600"],["for","planning",1,"block","text-sm","font-medium","text-purple-700","mb-2"],[1,"fas","fa-list","mr-2","text-purple-500"],["id","planning","formControlName","planning",1,"mt-1","block","w-full","rounded-lg","border-2","border-purple-200","shadow-sm","focus:border-purple-500","focus:ring-purple-500","focus:ring-2","transition-all","duration-200","px-4","py-3"],["value",""],[3,"value",4,"ngFor","ngForOf"],[3,"value"],[1,"bg-gradient-to-r","from-purple-50","to-indigo-50","p-4","rounded-lg","border-2","border-purple-200","shadow-sm"],[1,"text-lg","font-semibold","text-purple-800","mb-3","flex","items-center"],[1,"fas","fa-calendar-check","mr-2","text-purple-600"],[1,"flex","items-center","justify-between"],[1,"font-semibold","text-purple-800","text-lg"],[1,"text-sm","font-medium","text-red-600","bg-red-50","px-2","py-1","rounded-full","border","border-red-200"],[1,"fas","fa-clock","mr-1"],["class","text-sm text-indigo-700 mt-2 bg-indigo-50 p-2 rounded border-l-4 border-indigo-300",4,"ngIf"],[1,"text-sm","text-indigo-700","mt-2","bg-indigo-50","p-2","rounded","border-l-4","border-indigo-300"],["class","py-2",3,"value",4,"ngFor","ngForOf"],[1,"py-2",3,"value"],[1,"fas","fa-user","mr-2"],[1,"fas","fa-save","mr-2"],[1,"fas","fa-spinner","fa-spin","mr-2"],[1,"fas","fa-search","mr-2"]],template:function(r,i){if(1&r&&(e.j41(0,"div",0)(1,"div",1)(2,"h1",2),e.nrm(3,"i",3),e.EFF(4),e.k0s(),e.j41(5,"p",4),e.EFF(6),e.k0s()(),e.j41(7,"form",5),e.bIt("ngSubmit",function(){return i.onSubmit()}),e.DNE(8,B,2,1,"div",6),e.DNE(9,q,2,1,"div",7),e.j41(10,"div",8)(11,"div",9)(12,"label",10),e.nrm(13,"i",11),e.EFF(14," Titre * "),e.k0s(),e.nrm(15,"input",12),e.DNE(16,H,3,0,"div",13),e.k0s(),e.j41(17,"div",9)(18,"label",14),e.nrm(19,"i",15),e.EFF(20," Description "),e.k0s(),e.nrm(21,"textarea",16),e.k0s(),e.j41(22,"div",17)(23,"h3",18),e.nrm(24,"i",19),e.EFF(25," Planification "),e.k0s(),e.j41(26,"div",20)(27,"div")(28,"label",21),e.nrm(29,"i",22),e.EFF(30," Date * "),e.k0s(),e.nrm(31,"input",23),e.DNE(32,A,3,0,"div",13),e.k0s(),e.j41(33,"div")(34,"label",24),e.nrm(35,"i",25),e.EFF(36," Heure de d\xe9but * "),e.k0s(),e.nrm(37,"input",26),e.DNE(38,J,3,0,"div",13),e.k0s(),e.j41(39,"div")(40,"label",27),e.nrm(41,"i",28),e.EFF(42," Heure de fin * "),e.k0s(),e.nrm(43,"input",29),e.DNE(44,W,3,0,"div",13),e.k0s()()(),e.j41(45,"div",30)(46,"h3",31),e.nrm(47,"i",32),e.EFF(48," Localisation "),e.k0s(),e.j41(49,"div",33)(50,"div")(51,"label",34),e.nrm(52,"i",35),e.EFF(53," Lieu / Salle "),e.k0s(),e.nrm(54,"input",36),e.k0s(),e.j41(55,"div")(56,"label",37),e.nrm(57,"i",38),e.EFF(58," Lien Visio "),e.DNE(59,Z,5,0,"span",39),e.k0s(),e.nrm(60,"input",40),e.DNE(61,K,3,1,"div",41),e.DNE(62,Q,3,0,"div",42),e.k0s()()(),e.DNE(63,te,12,2,"div",43),e.DNE(64,re,14,10,"div",44),e.j41(65,"div",45)(66,"h3",46),e.nrm(67,"i",47),e.EFF(68," Participants "),e.k0s(),e.j41(69,"label",48),e.nrm(70,"i",49),e.EFF(71," S\xe9lectionnez les participants "),e.k0s(),e.j41(72,"select",50),e.DNE(73,se,2,1,"ng-container",51),e.nI1(74,"async"),e.k0s(),e.j41(75,"p",52),e.nrm(76,"i",53),e.EFF(77," Maintenez Ctrl (ou Cmd) pour s\xe9lectionner plusieurs participants "),e.k0s()()(),e.j41(78,"div",54)(79,"button",55),e.bIt("click",function(){return i.goReunion()}),e.nrm(80,"i",56),e.EFF(81," Annuler "),e.k0s(),e.j41(82,"button",57),e.DNE(83,ae,1,0,"i",58),e.DNE(84,le,1,0,"i",59),e.DNE(85,ue,1,0,"i",60),e.EFF(86),e.k0s()()()()),2&r){let o,a,u,d,c;e.R7$(4),e.SpI(" ",i.isEditMode?"Modifier la R\xe9union":"Nouvelle R\xe9union"," "),e.R7$(2),e.SpI(" ",i.isEditMode?"Modifiez les d\xe9tails de votre r\xe9union":"Cr\xe9ez une nouvelle r\xe9union pour votre \xe9quipe"," "),e.R7$(1),e.Y8G("formGroup",i.reunionForm),e.R7$(1),e.Y8G("ngIf",i.error),e.R7$(1),e.Y8G("ngIf",i.successMessage),e.R7$(7),e.Y8G("ngIf",(null==(o=i.reunionForm.get("titre"))?null:o.invalid)&&(null==(o=i.reunionForm.get("titre"))?null:o.touched)),e.R7$(16),e.Y8G("ngIf",(null==(a=i.reunionForm.get("date"))?null:a.invalid)&&(null==(a=i.reunionForm.get("date"))?null:a.touched)),e.R7$(6),e.Y8G("ngIf",(null==(u=i.reunionForm.get("heureDebut"))?null:u.invalid)&&(null==(u=i.reunionForm.get("heureDebut"))?null:u.touched)),e.R7$(6),e.Y8G("ngIf",(null==(d=i.reunionForm.get("heureFin"))?null:d.invalid)&&(null==(d=i.reunionForm.get("heureFin"))?null:d.touched)),e.R7$(15),e.Y8G("ngIf",i.isCheckingLienVisio),e.R7$(1),e.HbH("mt-1 block w-full rounded-lg shadow-sm focus:ring-cyan-500 focus:ring-2 transition-all duration-200 px-4 py-3 "+(i.lienVisioError?"border-2 border-red-300 focus:border-red-500":"border-2 border-cyan-200 focus:border-cyan-500")),e.R7$(1),e.Y8G("ngIf",i.lienVisioError),e.R7$(1),e.Y8G("ngIf",!i.lienVisioError&&!i.isCheckingLienVisio&&(null==(c=i.reunionForm.get("lienVisio"))?null:c.value)&&""!==(null==(c=i.reunionForm.get("lienVisio"))?null:c.value.trim())),e.R7$(1),e.Y8G("ngIf",!i.planningIdFromUrl),e.R7$(1),e.Y8G("ngIf",i.planningIdFromUrl&&i.selectedPlanning),e.R7$(9),e.Y8G("ngIf",e.bMT(74,22,i.users$)),e.R7$(9),e.Y8G("disabled",!i.canSubmit()||i.isSubmitting),e.R7$(1),e.Y8G("ngIf",!i.isSubmitting&&!i.isCheckingLienVisio),e.R7$(1),e.Y8G("ngIf",i.isSubmitting),e.R7$(1),e.Y8G("ngIf",i.isCheckingLienVisio),e.R7$(1),e.SpI(" ",i.isSubmitting?"Enregistrement...":i.isCheckingLienVisio?"V\xe9rification...":"Cr\xe9er la r\xe9union"," ")}},dependencies:[m.Sq,m.bT,l.qT,l.xH,l.y7,l.me,l.wz,l.W0,l.BC,l.cb,l.j4,l.JD,m.Jj,m.vh]})}}return t})();function ce(t,s){1&t&&(e.qSk(),e.joV(),e.j41(0,"div",7),e.nrm(1,"div",8),e.k0s())}function pe(t,s){if(1&t&&(e.qSk(),e.joV(),e.j41(0,"div",9),e.EFF(1),e.k0s()),2&t){const n=e.XpG();e.R7$(1),e.SpI(" ",n.error," ")}}function me(t,s){if(1&t&&(e.j41(0,"li",24),e.EFF(1),e.k0s()),2&t){const n=s.$implicit;e.R7$(1),e.Lme(" ",n.username," (",n.email,") ")}}function ge(t,s){if(1&t&&(e.j41(0,"div",20)(1,"h2",26),e.EFF(2,"Lieu:"),e.k0s(),e.j41(3,"div",27),e.qSk(),e.j41(4,"svg",22),e.nrm(5,"path",31)(6,"path",32),e.k0s(),e.joV(),e.j41(7,"span",24),e.EFF(8),e.k0s()()()),2&t){const n=e.XpG(2);e.R7$(8),e.JRh(n.reunion.lieu)}}function fe(t,s){if(1&t&&(e.j41(0,"div",20)(1,"h2",26),e.EFF(2,"Lien Visio:"),e.k0s(),e.j41(3,"a",33),e.qSk(),e.j41(4,"svg",34),e.nrm(5,"path",35),e.k0s(),e.EFF(6," Rejoindre la r\xe9union "),e.k0s()()),2&t){const n=e.XpG(2);e.R7$(3),e.Y8G("href",n.reunion.lienVisio,e.B4B)}}function he(t,s){if(1&t){const n=e.RV6();e.qSk(),e.joV(),e.j41(0,"div",10)(1,"div",11)(2,"div")(3,"h1",12),e.EFF(4),e.k0s(),e.nrm(5,"p",13),e.nI1(6,"highlightPresence"),e.k0s(),e.j41(7,"div",14)(8,"button",15),e.bIt("click",function(){e.eBV(n);const i=e.XpG();return e.Njj(i.editReunion())}),e.qSk(),e.j41(9,"svg",16),e.nrm(10,"path",17),e.k0s(),e.EFF(11," Modifier "),e.k0s(),e.joV(),e.j41(12,"button",18),e.bIt("click",function(){e.eBV(n);const i=e.XpG();return e.Njj(i.deleteReunion())}),e.qSk(),e.j41(13,"svg",16),e.nrm(14,"path",19),e.k0s(),e.EFF(15," Supprimer "),e.k0s()()(),e.joV(),e.j41(16,"div",20)(17,"div",21),e.qSk(),e.j41(18,"svg",22),e.nrm(19,"path",23),e.k0s(),e.joV(),e.j41(20,"span",24),e.EFF(21),e.nI1(22,"date"),e.k0s()()(),e.j41(23,"div",25)(24,"h2",26),e.EFF(25,"Cr\xe9ateur:"),e.k0s(),e.j41(26,"div",27)(27,"span",24),e.EFF(28),e.k0s()()(),e.j41(29,"div",20)(30,"h2",26),e.EFF(31,"Participants:"),e.k0s(),e.j41(32,"ul",28),e.DNE(33,me,2,2,"li",29),e.k0s()(),e.j41(34,"div",20)(35,"h2",26),e.EFF(36,"Planning:"),e.k0s(),e.j41(37,"div",24)(38,"p"),e.EFF(39),e.k0s(),e.j41(40,"p"),e.EFF(41),e.nI1(42,"date"),e.nI1(43,"date"),e.k0s()()(),e.DNE(44,ge,9,1,"div",30),e.DNE(45,fe,7,1,"div",30),e.k0s()}if(2&t){const n=e.XpG();e.R7$(4),e.JRh(n.reunion.titre),e.R7$(1),e.Y8G("innerHTML",e.bMT(6,13,n.reunion.description),e.npT),e.R7$(16),e.E5c(" ",e.i5U(22,15,n.reunion.date,"fullDate"),", ",n.reunion.heureDebut," - ",n.reunion.heureFin," "),e.R7$(7),e.Lme("",null==n.reunion.createur?null:n.reunion.createur.username," (",null==n.reunion.createur?null:n.reunion.createur.email,")"),e.R7$(5),e.Y8G("ngForOf",n.reunion.participants),e.R7$(6),e.JRh(null==n.reunion.planning?null:n.reunion.planning.titre),e.R7$(2),e.Lme("Du ",e.i5U(42,18,null==n.reunion.planning?null:n.reunion.planning.dateDebut,"mediumDate")," au ",e.i5U(43,21,null==n.reunion.planning?null:n.reunion.planning.dateFin,"mediumDate"),""),e.R7$(3),e.Y8G("ngIf",n.reunion.lieu),e.R7$(1),e.Y8G("ngIf",n.reunion.lienVisio)}}let be=(()=>{class t{constructor(n,r,i,o,a){this.route=n,this.router=r,this.reunionService=i,this.sanitizer=o,this.toastService=a,this.reunion=null,this.loading=!0,this.error=null}ngOnInit(){this.loadReunionDetails()}loadReunionDetails(){const n=this.route.snapshot.paramMap.get("id");if(!n)return this.error="ID de r\xe9union non fourni",void(this.loading=!1);this.reunionService.getReunionById(n).subscribe({next:r=>{this.reunion=r.reunion,this.loading=!1},error:r=>{this.error=r.error?.message||"Erreur lors du chargement",this.loading=!1,console.error("Erreur:",r)}})}formatDescription(n){if(!n)return this.sanitizer.bypassSecurityTrustHtml("");const r=n.replace(/\(presence obligatoire\)/gi,'<span class="text-red-600 font-semibold">(presence obligatoire)</span>');return this.sanitizer.bypassSecurityTrustHtml(r)}editReunion(){this.reunion&&this.router.navigate(["/reunions/edit",this.reunion._id])}deleteReunion(){this.reunion&&confirm("\xcates-vous s\xfbr de vouloir supprimer cette r\xe9union ? Cette action est irr\xe9versible.")&&this.reunionService.deleteReunion(this.reunion._id).subscribe({next:n=>{console.log("R\xe9union supprim\xe9e avec succ\xe8s:",n),this.toastService.success("R\xe9union supprim\xe9e","La r\xe9union a \xe9t\xe9 supprim\xe9e avec succ\xe8s"),this.router.navigate(["/reunions"])},error:n=>{console.error("Erreur lors de la suppression:",n),403===n.status?this.toastService.accessDenied("supprimer cette r\xe9union",n.status):401===n.status?this.toastService.error("Non autoris\xe9","Vous devez \xeatre connect\xe9 pour supprimer une r\xe9union"):this.toastService.error("Erreur de suppression",n.error?.message||"Erreur lors de la suppression de la r\xe9union",8e3)}})}static{this.\u0275fac=function(r){return new(r||t)(e.rXU(f.nX),e.rXU(f.Ix),e.rXU(b.C),e.rXU(k.up),e.rXU(v.f))}}static{this.\u0275cmp=e.VBU({type:t,selectors:[["app-reunion-detail"]],decls:8,vars:3,consts:[[1,"container","mx-auto","px-4","py-6"],[1,"mb-4","flex","items-center","text-purple-600","hover:text-purple-800",3,"click"],["xmlns","http://www.w3.org/2000/svg","viewBox","0 0 20 20","fill","currentColor",1,"h-5","w-5","mr-1"],["fill-rule","evenodd","d","M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z","clip-rule","evenodd"],["class","text-center py-8",4,"ngIf"],["class","bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4",4,"ngIf"],["class","bg-white rounded-lg shadow-md p-6",4,"ngIf"],[1,"text-center","py-8"],[1,"animate-spin","rounded-full","h-12","w-12","border-b-2","border-purple-600","mx-auto"],[1,"bg-red-100","border","border-red-400","text-red-700","px-4","py-3","rounded","mb-4"],[1,"bg-white","rounded-lg","shadow-md","p-6"],[1,"flex","justify-between","items-start","mb-4"],[1,"text-2xl","font-bold","text-gray-800"],[1,"mt-1",3,"innerHTML"],[1,"flex","space-x-2"],[1,"px-4","py-2","bg-blue-500","text-white","rounded","hover:bg-blue-600","transition-colors","flex","items-center",3,"click"],["fill","none","viewBox","0 0 24 24","stroke","currentColor",1,"h-4","w-4","mr-1"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"],[1,"px-4","py-2","bg-red-500","text-white","rounded","hover:bg-red-600","transition-colors","flex","items-center",3,"click"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"],[1,"mb-6"],[1,"flex","items-center","mb-2"],["fill","none","viewBox","0 0 24 24","stroke","currentColor",1,"h-5","w-5","text-gray-500","mr-2"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"],[1,"text-gray-700"],[1,"mb-4"],[1,"text-lg","font-semibold","mb-2","text-gray-800"],[1,"flex","items-center"],[1,"list-disc","pl-5"],["class","text-gray-700",4,"ngFor","ngForOf"],["class","mb-6",4,"ngIf"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M15 11a3 3 0 11-6 0 3 3 0 016 0z"],["target","_blank",1,"text-blue-600","hover:underline","flex","items-center",3,"href"],["fill","none","viewBox","0 0 24 24","stroke","currentColor",1,"h-5","w-5","mr-2"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"]],template:function(r,i){1&r&&(e.j41(0,"div",0)(1,"button",1),e.bIt("click",function(){return i.router.navigate(["/reunions"])}),e.qSk(),e.j41(2,"svg",2),e.nrm(3,"path",3),e.k0s(),e.EFF(4," Retour aux r\xe9unions "),e.k0s(),e.DNE(5,ce,2,0,"div",4),e.DNE(6,pe,2,1,"div",5),e.DNE(7,he,46,24,"div",6),e.k0s()),2&r&&(e.R7$(5),e.Y8G("ngIf",i.loading),e.R7$(1),e.Y8G("ngIf",i.error),e.R7$(1),e.Y8G("ngIf",!i.loading&&i.reunion))},dependencies:[m.Sq,m.bT,m.vh,F.I]})}}return t})();var ve=p(4866);function xe(t,s){if(1&t&&(e.j41(0,"div",62),e.EFF(1),e.k0s()),2&t){const n=e.XpG();e.R7$(1),e.SpI(" ",n.error.message||"Une erreur est survenue"," ")}}function _e(t,s){1&t&&(e.j41(0,"div",63),e.nrm(1,"i",64),e.EFF(2," Le titre est obligatoire "),e.k0s())}function ke(t,s){1&t&&(e.j41(0,"div",63),e.nrm(1,"i",64),e.EFF(2," La date est obligatoire "),e.k0s())}function Fe(t,s){1&t&&(e.j41(0,"div",63),e.nrm(1,"i",64),e.EFF(2," L'heure de d\xe9but est obligatoire "),e.k0s())}function Re(t,s){1&t&&(e.j41(0,"div",63),e.nrm(1,"i",64),e.EFF(2," L'heure de fin est obligatoire "),e.k0s())}function Ee(t,s){if(1&t&&(e.j41(0,"div",72),e.nrm(1,"i",55),e.EFF(2),e.k0s()),2&t){const n=e.XpG(2);e.R7$(2),e.SpI(" ",n.currentReunionPlanning.description," ")}}function je(t,s){if(1&t&&(e.j41(0,"div",65)(1,"div",66)(2,"span",67),e.nrm(3,"i",68),e.EFF(4),e.k0s(),e.j41(5,"span",69),e.nrm(6,"i",70),e.EFF(7),e.nI1(8,"date"),e.nI1(9,"date"),e.k0s()(),e.DNE(10,Ee,3,1,"div",71),e.k0s()),2&t){const n=e.XpG();e.R7$(4),e.SpI(" ",n.currentReunionPlanning.titre," "),e.R7$(3),e.Lme(" ",e.i5U(8,4,n.currentReunionPlanning.dateDebut,"dd/MM/yyyy")," - ",e.i5U(9,7,n.currentReunionPlanning.dateFin,"dd/MM/yyyy")," "),e.R7$(3),e.Y8G("ngIf",n.currentReunionPlanning.description)}}function ye(t,s){if(1&t&&(e.j41(0,"option",73),e.EFF(1),e.k0s()),2&t){const n=s.$implicit;e.Y8G("value",n._id),e.R7$(1),e.JRh(n.titre)}}function Ce(t,s){if(1&t&&(e.j41(0,"option",75),e.nrm(1,"i",76),e.EFF(2),e.k0s()),2&t){const n=s.$implicit;e.Y8G("value",n._id),e.R7$(2),e.SpI("",n.username," ")}}function we(t,s){if(1&t&&(e.qex(0),e.DNE(1,Ce,3,2,"option",74),e.bVm()),2&t){const n=e.XpG();e.R7$(1),e.Y8G("ngForOf",n.users)}}function Ie(t,s){1&t&&e.nrm(0,"i",77)}function Se(t,s){1&t&&e.nrm(0,"i",78)}const Pe=[{path:"",component:U},{path:"nouvelleReunion",component:de},{path:"reunionDetails/:id",component:be},{path:"modifier/:id",component:(()=>{class t{constructor(n,r,i,o,a,u,d,c,h){this.fb=n,this.route=r,this.router=i,this.reunionService=o,this.userService=a,this.planningService=u,this.toastService=d,this.authService=c,this.roleService=h,this.error=null,this.isSubmitting=!1,this.users=[],this.plannings=[],this.currentReunionPlanning=null,this.isAdmin=!1}ngOnInit(){this.reunionId=this.route.snapshot.paramMap.get("id"),this.checkUserRole(),this.initForm(),this.fetchUsers(),this.fetchPlannings(),this.loadReunion()}checkUserRole(){this.isAdmin=this.roleService.isAdmin(),console.log("\u{1f50d} Utilisateur admin:",this.isAdmin)}initForm(){this.reunionForm=this.fb.group({titre:["",l.k0.required],description:[""],date:["",l.k0.required],heureDebut:["",l.k0.required],heureFin:["",l.k0.required],lieu:[""],lienVisio:[""],planning:["",l.k0.required],participants:[[]]})}fetchUsers(){this.userService.getAllUsers().subscribe(n=>{this.users=n})}fetchPlannings(){const n=this.authService.getCurrentUserId();n&&(this.isAdmin?this.planningService.getAllPlanningsAdmin():this.planningService.getPlanningsByUser(n)).subscribe({next:i=>{this.isAdmin?(this.plannings=i.data||[],console.log("\u{1f50d} Tous les plannings (admin) r\xe9cup\xe9r\xe9s:",this.plannings)):(this.plannings=i.plannings||[],console.log("\u{1f50d} Plannings utilisateur r\xe9cup\xe9r\xe9s:",this.plannings))},error:i=>{console.error("\u274c Erreur chargement plannings:",i),this.toastService.error("Erreur","Impossible de r\xe9cup\xe9rer les plannings")}})}loadReunion(){this.reunionService.getReunionById(this.reunionId).subscribe({next:n=>{this.currentReunionPlanning=n.reunion.planning,this.reunionForm.patchValue({titre:n.reunion.titre,description:n.reunion.description,date:n.reunion.date?.split("T")[0],heureDebut:n.reunion.heureDebut,heureFin:n.reunion.heureFin,lieu:n.reunion.lieu,lienVisio:n.reunion.lienVisio,planning:n.reunion.planning?.id||n.reunion.planning?._id,participants:n.reunion.participants?.map(r=>r._id)}),this.reunionForm.get("planning")?.disable(),console.log("\u{1f50d} R\xe9union charg\xe9e:",n.reunion),console.log("\u{1f50d} Planning actuel:",this.currentReunionPlanning)},error:n=>{console.error("Erreur lors du chargement de la r\xe9union:",n),403===n.status?this.toastService.accessDenied("acc\xe9der \xe0 cette r\xe9union",n.status):404===n.status?this.toastService.error("R\xe9union introuvable","La r\xe9union demand\xe9e n'existe pas ou a \xe9t\xe9 supprim\xe9e"):this.toastService.error("Erreur de chargement",n.error?.message||"Erreur lors du chargement de la r\xe9union")}})}onSubmit(){if(this.reunionForm.invalid)return void this.toastService.warning("Formulaire invalide","Veuillez corriger les erreurs avant de soumettre le formulaire");if(!this.validateDateInPlanningRange())return;this.isSubmitting=!0;const n=this.reunionForm.value;this.currentReunionPlanning&&(n.planning=this.currentReunionPlanning._id||this.currentReunionPlanning.id),console.log("\u{1f50d} Donn\xe9es de la r\xe9union \xe0 mettre \xe0 jour:",n),this.reunionService.updateReunion(this.reunionId,n).subscribe({next:()=>{this.isSubmitting=!1,this.toastService.success("R\xe9union mise \xe0 jour","La r\xe9union a \xe9t\xe9 modifi\xe9e avec succ\xe8s"),this.router.navigate(["/reunions"])},error:r=>{this.isSubmitting=!1,console.error("Erreur lors de la mise \xe0 jour de la r\xe9union:",r),403===r.status?this.toastService.accessDenied("modifier cette r\xe9union",r.status):401===r.status?this.toastService.error("Non autoris\xe9","Vous devez \xeatre connect\xe9 pour effectuer cette action"):this.toastService.error("Erreur de mise \xe0 jour",r.error?.message||"Erreur lors de la mise \xe0 jour de la r\xe9union",8e3)}})}goReunion(){this.router.navigate(["/reunions"])}validateDateInPlanningRange(){const n=this.reunionForm.value,r=n.date,i=n.planning;if(!r||!i)return!0;const o=this.plannings.find(c=>c._id===i);if(!o)return console.warn("\u26a0\ufe0f Planning non trouv\xe9 dans la liste locale, tentative de r\xe9cup\xe9ration depuis le serveur"),this.planningService.getPlanningById(i).subscribe({next:c=>{const h=c.planning;h&&(this.plannings.push(h),console.log("\u2705 Planning r\xe9cup\xe9r\xe9 et ajout\xe9 \xe0 la liste locale:",h))},error:c=>{console.error("\u274c Erreur lors de la r\xe9cup\xe9ration du planning:",c),this.toastService.error("Planning introuvable","Le planning s\xe9lectionn\xe9 n'existe pas ou vous n'avez pas les permissions pour y acc\xe9der")}}),!0;const a=new Date(r),u=new Date(o.dateDebut),d=new Date(o.dateFin);return a.setHours(0,0,0,0),u.setHours(0,0,0,0),d.setHours(0,0,0,0),!(a<u||a>d)||(this.toastService.error("Date invalide",`La date de la r\xe9union doit \xeatre comprise entre le ${u.toLocaleDateString("fr-FR")} et le ${d.toLocaleDateString("fr-FR")} (p\xe9riode du planning "${o.titre}")`,1e4),!1)}static{this.\u0275fac=function(r){return new(r||t)(e.rXU(l.ok),e.rXU(f.nX),e.rXU(f.Ix),e.rXU(b.C),e.rXU(E.u),e.rXU(R.z),e.rXU(v.f),e.rXU(x.V),e.rXU(ve.W))}}static{this.\u0275cmp=e.VBU({type:t,selectors:[["app-reunion-edit"]],decls:91,vars:13,consts:[[1,"container","mx-auto","px-4","py-6","max-w-3xl"],[1,"bg-gradient-to-r","from-purple-600","to-indigo-600","rounded-t-lg","p-6","text-white","mb-0"],[1,"text-2xl","font-bold","flex","items-center"],[1,"fas","fa-edit","mr-3","text-purple-200"],[1,"text-purple-100","mt-2"],[1,"bg-white","rounded-b-lg","shadow-lg","p-6","border-t-0",3,"formGroup","ngSubmit"],["class","bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4",4,"ngIf"],[1,"grid","grid-cols-1","gap-6"],[1,"relative"],["for","titre",1,"block","text-sm","font-medium","text-purple-700","mb-2"],[1,"fas","fa-tag","mr-2","text-purple-500"],["id","titre","type","text","formControlName","titre",1,"mt-1","block","w-full","rounded-lg","border-2","border-purple-200","shadow-sm","focus:border-purple-500","focus:ring-purple-500","focus:ring-2","transition-all","duration-200","px-4","py-3"],["class","text-red-500 text-sm mt-2 flex items-center",4,"ngIf"],["for","description",1,"block","text-sm","font-medium","text-indigo-700","mb-2"],[1,"fas","fa-align-left","mr-2","text-indigo-500"],["id","description","formControlName","description","rows","3","placeholder","D\xe9crivez votre r\xe9union...",1,"mt-1","block","w-full","rounded-lg","border-2","border-indigo-200","shadow-sm","focus:border-indigo-500","focus:ring-indigo-500","focus:ring-2","transition-all","duration-200","px-4","py-3"],[1,"bg-gradient-to-r","from-blue-50","to-cyan-50","p-4","rounded-lg","border","border-blue-200"],[1,"text-lg","font-semibold","text-blue-800","mb-4","flex","items-center"],[1,"fas","fa-calendar-clock","mr-2","text-blue-600"],[1,"grid","grid-cols-1","md:grid-cols-3","gap-6"],["for","date",1,"block","text-sm","font-medium","text-blue-700","mb-2"],[1,"fas","fa-calendar","mr-2","text-blue-500"],["id","date","type","date","formControlName","date",1,"mt-1","block","w-full","rounded-lg","border-2","border-blue-200","shadow-sm","focus:border-blue-500","focus:ring-blue-500","focus:ring-2","transition-all","duration-200","px-4","py-3"],["for","heureDebut",1,"block","text-sm","font-medium","text-green-700","mb-2"],[1,"fas","fa-play","mr-2","text-green-500"],["id","heureDebut","type","time","formControlName","heureDebut",1,"mt-1","block","w-full","rounded-lg","border-2","border-green-200","shadow-sm","focus:border-green-500","focus:ring-green-500","focus:ring-2","transition-all","duration-200","px-4","py-3"],["for","heureFin",1,"block","text-sm","font-medium","text-red-700","mb-2"],[1,"fas","fa-stop","mr-2","text-red-500"],["id","heureFin","type","time","formControlName","heureFin",1,"mt-1","block","w-full","rounded-lg","border-2","border-red-200","shadow-sm","focus:border-red-500","focus:ring-red-500","focus:ring-2","transition-all","duration-200","px-4","py-3"],[1,"bg-gradient-to-r","from-orange-50","to-yellow-50","p-4","rounded-lg","border","border-orange-200"],[1,"text-lg","font-semibold","text-orange-800","mb-4","flex","items-center"],[1,"fas","fa-map-marker-alt","mr-2","text-orange-600"],[1,"grid","grid-cols-1","md:grid-cols-2","gap-6"],["for","lieu",1,"block","text-sm","font-medium","text-orange-700","mb-2"],[1,"fas","fa-map-marker-alt","mr-2","text-orange-500"],["id","lieu","type","text","formControlName","lieu","placeholder","Salle 101, Bureau A, Google Meet...",1,"mt-1","block","w-full","rounded-lg","border-2","border-orange-200","shadow-sm","focus:border-orange-500","focus:ring-orange-500","focus:ring-2","transition-all","duration-200","px-4","py-3"],["for","lienVisio",1,"block","text-sm","font-medium","text-cyan-700","mb-2"],[1,"fas","fa-video","mr-2","text-cyan-500"],["id","lienVisio","type","url","formControlName","lienVisio","placeholder","https://meet.google.com/...",1,"mt-1","block","w-full","rounded-lg","border-2","border-cyan-200","shadow-sm","focus:border-cyan-500","focus:ring-cyan-500","focus:ring-2","transition-all","duration-200","px-4","py-3"],["for","planning",1,"block","text-sm","font-medium","text-gray-700"],["class","mt-1 block w-full px-4 py-3 bg-gradient-to-r from-purple-50 to-indigo-50 border-2 border-purple-200 rounded-lg shadow-sm",4,"ngIf"],["id","planning","formControlName","planning",1,"hidden"],["value",""],[3,"value",4,"ngFor","ngForOf"],[1,"text-sm","text-purple-600","mt-3","bg-purple-50","p-3","rounded-lg","border","border-purple-200","flex","items-center"],[1,"fas","fa-lock","mr-2","text-purple-500"],[1,"font-medium"],[1,"bg-gradient-to-r","from-emerald-50","to-teal-50","p-4","rounded-lg","border","border-emerald-200"],[1,"text-lg","font-semibold","text-emerald-800","mb-4","flex","items-center"],[1,"fas","fa-users","mr-2","text-emerald-600"],[1,"block","text-sm","font-medium","text-emerald-700","mb-2"],[1,"fas","fa-user-friends","mr-2","text-emerald-500"],["formControlName","participants","multiple","",1,"mt-1","block","w-full","px-4","py-3","border-2","border-emerald-200","rounded-lg","shadow-sm","focus:ring-emerald-500","focus:border-emerald-500","focus:ring-2","transition-all","duration-200","text-sm","min-h-[120px]"],[4,"ngIf"],[1,"text-xs","text-emerald-600","mt-2"],[1,"fas","fa-info-circle","mr-1"],[1,"mt-8","flex","justify-end","space-x-4","bg-gray-50","p-4","rounded-lg","border-t","border-gray-200"],["type","button",1,"px-6","py-3","border-2","border-gray-300","rounded-lg","text-sm","font-medium","text-gray-700","hover:bg-gray-100","hover:border-gray-400","transition-all","duration-200","flex","items-center",3,"click"],[1,"fas","fa-times","mr-2"],["type","submit",1,"px-6","py-3","rounded-lg","text-sm","font-medium","text-white","bg-gradient-to-r","from-purple-600","to-indigo-600","hover:from-purple-700","hover:to-indigo-700","disabled:opacity-50","disabled:cursor-not-allowed","transition-all","duration-200","flex","items-center","shadow-lg",3,"disabled"],["class","fas fa-save mr-2",4,"ngIf"],["class","fas fa-spinner fa-spin mr-2",4,"ngIf"],[1,"bg-red-100","border","border-red-400","text-red-700","px-4","py-3","rounded","mb-4"],[1,"text-red-500","text-sm","mt-2","flex","items-center"],[1,"fas","fa-exclamation-circle","mr-1"],[1,"mt-1","block","w-full","px-4","py-3","bg-gradient-to-r","from-purple-50","to-indigo-50","border-2","border-purple-200","rounded-lg","shadow-sm"],[1,"flex","items-center","justify-between"],[1,"font-semibold","text-purple-800","text-lg"],[1,"fas","fa-calendar-alt","mr-2","text-purple-600"],[1,"text-sm","font-medium","text-red-600","bg-red-50","px-2","py-1","rounded-full","border","border-red-200"],[1,"fas","fa-clock","mr-1"],["class","text-sm text-indigo-700 mt-2 bg-indigo-50 p-2 rounded border-l-4 border-indigo-300",4,"ngIf"],[1,"text-sm","text-indigo-700","mt-2","bg-indigo-50","p-2","rounded","border-l-4","border-indigo-300"],[3,"value"],["class","py-2",3,"value",4,"ngFor","ngForOf"],[1,"py-2",3,"value"],[1,"fas","fa-user","mr-2"],[1,"fas","fa-save","mr-2"],[1,"fas","fa-spinner","fa-spin","mr-2"]],template:function(r,i){if(1&r&&(e.j41(0,"div",0)(1,"div",1)(2,"h1",2),e.nrm(3,"i",3),e.EFF(4," Modifier la R\xe9union "),e.k0s(),e.j41(5,"p",4),e.EFF(6,"Modifiez les d\xe9tails de votre r\xe9union"),e.k0s()(),e.j41(7,"form",5),e.bIt("ngSubmit",function(){return i.onSubmit()}),e.DNE(8,xe,2,1,"div",6),e.j41(9,"div",7)(10,"div",8)(11,"label",9),e.nrm(12,"i",10),e.EFF(13," Titre * "),e.k0s(),e.nrm(14,"input",11),e.DNE(15,_e,3,0,"div",12),e.k0s(),e.j41(16,"div",8)(17,"label",13),e.nrm(18,"i",14),e.EFF(19," Description "),e.k0s(),e.nrm(20,"textarea",15),e.k0s(),e.j41(21,"div",16)(22,"h3",17),e.nrm(23,"i",18),e.EFF(24," Planification "),e.k0s(),e.j41(25,"div",19)(26,"div")(27,"label",20),e.nrm(28,"i",21),e.EFF(29," Date * "),e.k0s(),e.nrm(30,"input",22),e.DNE(31,ke,3,0,"div",12),e.k0s(),e.j41(32,"div")(33,"label",23),e.nrm(34,"i",24),e.EFF(35," Heure de d\xe9but * "),e.k0s(),e.nrm(36,"input",25),e.DNE(37,Fe,3,0,"div",12),e.k0s(),e.j41(38,"div")(39,"label",26),e.nrm(40,"i",27),e.EFF(41," Heure de fin * "),e.k0s(),e.nrm(42,"input",28),e.DNE(43,Re,3,0,"div",12),e.k0s()()(),e.j41(44,"div",29)(45,"h3",30),e.nrm(46,"i",31),e.EFF(47," Localisation "),e.k0s(),e.j41(48,"div",32)(49,"div")(50,"label",33),e.nrm(51,"i",34),e.EFF(52," Lieu / Salle "),e.k0s(),e.nrm(53,"input",35),e.k0s(),e.j41(54,"div")(55,"label",36),e.nrm(56,"i",37),e.EFF(57," Lien Visio "),e.k0s(),e.nrm(58,"input",38),e.k0s()()(),e.j41(59,"div")(60,"label",39),e.EFF(61,"Planning *"),e.k0s(),e.DNE(62,je,11,10,"div",40),e.j41(63,"select",41)(64,"option",42),e.EFF(65,"S\xe9lectionnez un planning"),e.k0s(),e.DNE(66,ye,2,2,"option",43),e.k0s(),e.j41(67,"div",44),e.nrm(68,"i",45),e.j41(69,"span",46),e.EFF(70,"Le planning ne peut pas \xeatre modifi\xe9 lors de l'\xe9dition d'une r\xe9union"),e.k0s()()(),e.j41(71,"div",47)(72,"h3",48),e.nrm(73,"i",49),e.EFF(74," Participants "),e.k0s(),e.j41(75,"label",50),e.nrm(76,"i",51),e.EFF(77," S\xe9lectionnez les participants "),e.k0s(),e.j41(78,"select",52),e.DNE(79,we,2,1,"ng-container",53),e.k0s(),e.j41(80,"p",54),e.nrm(81,"i",55),e.EFF(82," Maintenez Ctrl (ou Cmd) pour s\xe9lectionner plusieurs participants "),e.k0s()()(),e.j41(83,"div",56)(84,"button",57),e.bIt("click",function(){return i.goReunion()}),e.nrm(85,"i",58),e.EFF(86," Annuler "),e.k0s(),e.j41(87,"button",59),e.DNE(88,Ie,1,0,"i",60),e.DNE(89,Se,1,0,"i",61),e.EFF(90),e.k0s()()()()),2&r){let o,a,u,d;e.R7$(7),e.Y8G("formGroup",i.reunionForm),e.R7$(1),e.Y8G("ngIf",i.error),e.R7$(7),e.Y8G("ngIf",(null==(o=i.reunionForm.get("titre"))?null:o.invalid)&&(null==(o=i.reunionForm.get("titre"))?null:o.touched)),e.R7$(16),e.Y8G("ngIf",(null==(a=i.reunionForm.get("date"))?null:a.invalid)&&(null==(a=i.reunionForm.get("date"))?null:a.touched)),e.R7$(6),e.Y8G("ngIf",(null==(u=i.reunionForm.get("heureDebut"))?null:u.invalid)&&(null==(u=i.reunionForm.get("heureDebut"))?null:u.touched)),e.R7$(6),e.Y8G("ngIf",(null==(d=i.reunionForm.get("heureFin"))?null:d.invalid)&&(null==(d=i.reunionForm.get("heureFin"))?null:d.touched)),e.R7$(19),e.Y8G("ngIf",i.currentReunionPlanning),e.R7$(4),e.Y8G("ngForOf",i.plannings),e.R7$(13),e.Y8G("ngIf",i.users),e.R7$(8),e.Y8G("disabled",i.reunionForm.invalid||i.isSubmitting),e.R7$(1),e.Y8G("ngIf",!i.isSubmitting),e.R7$(1),e.Y8G("ngIf",i.isSubmitting),e.R7$(1),e.SpI(" ",i.isSubmitting?"Enregistrement...":"Enregistrer les modifications"," ")}},dependencies:[m.Sq,m.bT,l.qT,l.xH,l.y7,l.me,l.wz,l.W0,l.BC,l.cb,l.j4,l.JD,m.vh]})}}return t})()}];let De=(()=>{class t{static{this.\u0275fac=function(r){return new(r||t)}}static{this.\u0275mod=e.$C({type:t})}static{this.\u0275inj=e.G2t({imports:[f.iI.forChild(Pe),f.iI]})}}return t})();var Me=p(1683);let $e=(()=>{class t{static{this.\u0275fac=function(r){return new(r||t)}}static{this.\u0275mod=e.$C({type:t})}static{this.\u0275inj=e.G2t({imports:[m.MD,De,f.iI,l.YN,l.X1,Me.Y]})}}return t})()}}]);