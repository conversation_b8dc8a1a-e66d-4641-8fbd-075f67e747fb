{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/OneDrive/Bureau/Project PI/devBridge/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Validators } from '@angular/forms';\nimport { Subscription } from 'rxjs';\nimport { CallType } from '../../../../models/message.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"../../../../services/message.service\";\nimport * as i4 from \"../../../../services/call.service\";\nimport * as i5 from \"../../../../services/toast.service\";\nimport * as i6 from \"@angular/common\";\nconst _c0 = [\"messagesContainer\"];\nconst _c1 = [\"fileInput\"];\nconst _c2 = [\"localVideo\"];\nconst _c3 = [\"remoteVideo\"];\nfunction MessageChatComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 53);\n  }\n}\nfunction MessageChatComponent_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 54)(1, \"span\");\n    i0.ɵɵtext(2, \"En train d'\\u00E9crire\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 55);\n    i0.ɵɵelement(4, \"div\", 56)(5, \"div\", 57)(6, \"div\", 58);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction MessageChatComponent_span_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r4.otherParticipant == null ? null : ctx_r4.otherParticipant.isOnline) ? \"En ligne\" : ctx_r4.formatLastActive(ctx_r4.otherParticipant == null ? null : ctx_r4.otherParticipant.lastActive), \" \");\n  }\n}\nfunction MessageChatComponent_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 59)(1, \"div\", 60)(2, \"button\", 61);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_27_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r19 = i0.ɵɵnextContext();\n      ctx_r19.toggleSearch();\n      return i0.ɵɵresetView(ctx_r19.showMainMenu = false);\n    });\n    i0.ɵɵelement(3, \"i\", 62);\n    i0.ɵɵelementStart(4, \"span\", 63);\n    i0.ɵɵtext(5, \"Rechercher\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"button\", 64);\n    i0.ɵɵelement(7, \"i\", 65);\n    i0.ɵɵelementStart(8, \"span\", 63);\n    i0.ɵɵtext(9, \"Voir le profil\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(10, \"hr\", 66);\n    i0.ɵɵelementStart(11, \"button\", 64);\n    i0.ɵɵelement(12, \"i\", 67);\n    i0.ɵɵelementStart(13, \"span\", 63);\n    i0.ɵɵtext(14, \"Param\\u00E8tres\");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nfunction MessageChatComponent_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 68)(1, \"div\", 69);\n    i0.ɵɵelement(2, \"i\", 70);\n    i0.ɵɵelementStart(3, \"p\", 71);\n    i0.ɵɵtext(4, \" D\\u00E9posez vos fichiers ici \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 72);\n    i0.ɵɵtext(6, \" Images, vid\\u00E9os, documents... \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction MessageChatComponent_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 73);\n    i0.ɵɵelement(1, \"div\", 74);\n    i0.ɵɵelementStart(2, \"span\", 75);\n    i0.ɵɵtext(3, \"Chargement des messages...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction MessageChatComponent_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 76)(1, \"div\", 77);\n    i0.ɵɵelement(2, \"i\", 78);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\", 79);\n    i0.ɵɵtext(4, \" Aucun message \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 80);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" Commencez votre conversation avec \", ctx_r9.otherParticipant == null ? null : ctx_r9.otherParticipant.username, \" \");\n  }\n}\nfunction MessageChatComponent_div_33_ng_container_1_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 94)(1, \"div\", 95)(2, \"span\", 96);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const message_r23 = i0.ɵɵnextContext().$implicit;\n    const ctx_r25 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r25.formatDateSeparator(message_r23.timestamp), \" \");\n  }\n}\nfunction MessageChatComponent_div_33_ng_container_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r35 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 97)(1, \"img\", 98);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_33_ng_container_1_div_3_Template_img_click_1_listener() {\n      i0.ɵɵrestoreView(_r35);\n      const message_r23 = i0.ɵɵnextContext().$implicit;\n      const ctx_r33 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r33.openUserProfile(message_r23.sender == null ? null : message_r23.sender.id));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const message_r23 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", (message_r23.sender == null ? null : message_r23.sender.image) || \"assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", message_r23.sender == null ? null : message_r23.sender.username);\n  }\n}\nfunction MessageChatComponent_div_33_ng_container_1_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 99);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r23 = i0.ɵɵnextContext().$implicit;\n    const ctx_r27 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleProp(\"color\", ctx_r27.getUserColor(message_r23.sender == null ? null : message_r23.sender.id));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", message_r23.sender == null ? null : message_r23.sender.username, \" \");\n  }\n}\nfunction MessageChatComponent_div_33_ng_container_1_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 100);\n    i0.ɵɵelement(1, \"div\", 101);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r23 = i0.ɵɵnextContext().$implicit;\n    const ctx_r28 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"innerHTML\", ctx_r28.formatMessageContent(message_r23.content), i0.ɵɵsanitizeHtml);\n  }\n}\nfunction MessageChatComponent_div_33_ng_container_1_div_7_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 105);\n  }\n  if (rf & 2) {\n    const message_r23 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r39 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleProp(\"color\", (message_r23.sender == null ? null : message_r23.sender.id) === ctx_r39.currentUserId ? \"#ffffff\" : \"#111827\");\n    i0.ɵɵproperty(\"innerHTML\", ctx_r39.formatMessageContent(message_r23.content), i0.ɵɵsanitizeHtml);\n  }\n}\nfunction MessageChatComponent_div_33_ng_container_1_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r43 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 102)(1, \"img\", 103);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_33_ng_container_1_div_7_Template_img_click_1_listener() {\n      i0.ɵɵrestoreView(_r43);\n      const message_r23 = i0.ɵɵnextContext().$implicit;\n      const ctx_r41 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r41.openImageViewer(message_r23));\n    })(\"load\", function MessageChatComponent_div_33_ng_container_1_div_7_Template_img_load_1_listener($event) {\n      i0.ɵɵrestoreView(_r43);\n      const message_r23 = i0.ɵɵnextContext().$implicit;\n      const ctx_r44 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r44.onImageLoad($event, message_r23));\n    })(\"error\", function MessageChatComponent_div_33_ng_container_1_div_7_Template_img_error_1_listener($event) {\n      i0.ɵɵrestoreView(_r43);\n      const message_r23 = i0.ɵɵnextContext().$implicit;\n      const ctx_r46 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r46.onImageError($event, message_r23));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(2, MessageChatComponent_div_33_ng_container_1_div_7_div_2_Template, 1, 3, \"div\", 104);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r23 = i0.ɵɵnextContext().$implicit;\n    const ctx_r29 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", ctx_r29.getImageUrl(message_r23), i0.ɵɵsanitizeUrl)(\"alt\", message_r23.content || \"Image\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r23.content);\n  }\n}\nfunction MessageChatComponent_div_33_ng_container_1_div_8_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 114);\n  }\n  if (rf & 2) {\n    const wave_r51 = ctx.$implicit;\n    const i_r52 = ctx.index;\n    const message_r23 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r49 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleProp(\"height\", ctx_r49.isVoicePlaying(message_r23.id) ? wave_r51 : 8, \"px\")(\"animation\", ctx_r49.isVoicePlaying(message_r23.id) ? \"pulse 1s infinite\" : \"none\")(\"animation-delay\", i_r52 * 0.1 + \"s\");\n  }\n}\nfunction MessageChatComponent_div_33_ng_container_1_div_8_button_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r56 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 115);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_33_ng_container_1_div_8_button_8_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r56);\n      const message_r23 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r54 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r54.changeVoiceSpeed(message_r23));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r23 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r50 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r50.getVoiceSpeed(message_r23), \"x \");\n  }\n}\nfunction MessageChatComponent_div_33_ng_container_1_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r60 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 106)(1, \"button\", 107);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_33_ng_container_1_div_8_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r60);\n      const message_r23 = i0.ɵɵnextContext().$implicit;\n      const ctx_r58 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r58.toggleVoicePlayback(message_r23));\n    });\n    i0.ɵɵelement(2, \"i\", 108);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 109);\n    i0.ɵɵtemplate(4, MessageChatComponent_div_33_ng_container_1_div_8_div_4_Template, 1, 6, \"div\", 110);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 111)(6, \"div\", 112);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, MessageChatComponent_div_33_ng_container_1_div_8_button_8_Template, 2, 1, \"button\", 113);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const message_r23 = i0.ɵɵnextContext().$implicit;\n    const ctx_r30 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(ctx_r30.isVoicePlaying(message_r23.id) ? \"fas fa-pause\" : \"fas fa-play\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r30.voiceWaves);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r30.getVoiceDuration(message_r23), \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r30.isVoicePlaying(message_r23.id));\n  }\n}\nfunction MessageChatComponent_div_33_ng_container_1_div_12_i_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 121);\n  }\n}\nfunction MessageChatComponent_div_33_ng_container_1_div_12_i_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 122);\n  }\n}\nfunction MessageChatComponent_div_33_ng_container_1_div_12_i_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 123);\n  }\n}\nfunction MessageChatComponent_div_33_ng_container_1_div_12_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 124);\n  }\n}\nfunction MessageChatComponent_div_33_ng_container_1_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 116);\n    i0.ɵɵtemplate(1, MessageChatComponent_div_33_ng_container_1_div_12_i_1_Template, 1, 0, \"i\", 117);\n    i0.ɵɵtemplate(2, MessageChatComponent_div_33_ng_container_1_div_12_i_2_Template, 1, 0, \"i\", 118);\n    i0.ɵɵtemplate(3, MessageChatComponent_div_33_ng_container_1_div_12_i_3_Template, 1, 0, \"i\", 119);\n    i0.ɵɵtemplate(4, MessageChatComponent_div_33_ng_container_1_div_12_i_4_Template, 1, 0, \"i\", 120);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r23 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r23.status === \"SENDING\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r23.status === \"SENT\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r23.status === \"DELIVERED\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r23.status === \"READ\");\n  }\n}\nfunction MessageChatComponent_div_33_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r68 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, MessageChatComponent_div_33_ng_container_1_div_1_Template, 4, 1, \"div\", 84);\n    i0.ɵɵelementStart(2, \"div\", 85);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_33_ng_container_1_Template_div_click_2_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r68);\n      const message_r23 = restoredCtx.$implicit;\n      const ctx_r67 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r67.onMessageClick(message_r23, $event));\n    })(\"contextmenu\", function MessageChatComponent_div_33_ng_container_1_Template_div_contextmenu_2_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r68);\n      const message_r23 = restoredCtx.$implicit;\n      const ctx_r69 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r69.onMessageContextMenu(message_r23, $event));\n    });\n    i0.ɵɵtemplate(3, MessageChatComponent_div_33_ng_container_1_div_3_Template, 2, 2, \"div\", 86);\n    i0.ɵɵelementStart(4, \"div\", 87);\n    i0.ɵɵtemplate(5, MessageChatComponent_div_33_ng_container_1_div_5_Template, 2, 3, \"div\", 88);\n    i0.ɵɵtemplate(6, MessageChatComponent_div_33_ng_container_1_div_6_Template, 2, 1, \"div\", 89);\n    i0.ɵɵtemplate(7, MessageChatComponent_div_33_ng_container_1_div_7_Template, 3, 3, \"div\", 90);\n    i0.ɵɵtemplate(8, MessageChatComponent_div_33_ng_container_1_div_8_Template, 9, 5, \"div\", 91);\n    i0.ɵɵelementStart(9, \"div\", 92)(10, \"span\");\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(12, MessageChatComponent_div_33_ng_container_1_div_12_Template, 5, 4, \"div\", 93);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const message_r23 = ctx.$implicit;\n    const i_r24 = ctx.index;\n    const ctx_r21 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r21.shouldShowDateSeparator(i_r24));\n    i0.ɵɵadvance(1);\n    i0.ɵɵstyleProp(\"justify-content\", (message_r23.sender == null ? null : message_r23.sender.id) === ctx_r21.currentUserId ? \"flex-end\" : \"flex-start\");\n    i0.ɵɵproperty(\"id\", \"message-\" + message_r23.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (message_r23.sender == null ? null : message_r23.sender.id) !== ctx_r21.currentUserId && ctx_r21.shouldShowAvatar(i_r24));\n    i0.ɵɵadvance(1);\n    i0.ɵɵstyleProp(\"background-color\", (message_r23.sender == null ? null : message_r23.sender.id) === ctx_r21.currentUserId ? \"#3b82f6\" : \"#ffffff\")(\"color\", (message_r23.sender == null ? null : message_r23.sender.id) === ctx_r21.currentUserId ? \"#ffffff\" : \"#111827\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r21.isGroupConversation() && (message_r23.sender == null ? null : message_r23.sender.id) !== ctx_r21.currentUserId && ctx_r21.shouldShowSenderName(i_r24));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r21.getMessageType(message_r23) === \"text\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r21.hasImage(message_r23));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r21.getMessageType(message_r23) === \"audio\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r21.formatMessageTime(message_r23.timestamp));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (message_r23.sender == null ? null : message_r23.sender.id) === ctx_r21.currentUserId);\n  }\n}\nfunction MessageChatComponent_div_33_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 125);\n    i0.ɵɵelement(1, \"img\", 126);\n    i0.ɵɵelementStart(2, \"div\", 127)(3, \"div\", 128);\n    i0.ɵɵelement(4, \"div\", 129)(5, \"div\", 130)(6, \"div\", 131);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r22 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", (ctx_r22.otherParticipant == null ? null : ctx_r22.otherParticipant.image) || \"assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", ctx_r22.otherParticipant == null ? null : ctx_r22.otherParticipant.username);\n  }\n}\nfunction MessageChatComponent_div_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 81);\n    i0.ɵɵtemplate(1, MessageChatComponent_div_33_ng_container_1_Template, 13, 15, \"ng-container\", 82);\n    i0.ɵɵtemplate(2, MessageChatComponent_div_33_div_2_Template, 7, 2, \"div\", 83);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r10.messages)(\"ngForTrackBy\", ctx_r10.trackByMessageId);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.otherUserIsTyping);\n  }\n}\nfunction MessageChatComponent_div_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 132);\n  }\n}\nfunction MessageChatComponent_i_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 133);\n  }\n}\nfunction MessageChatComponent_div_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 134);\n  }\n}\nfunction MessageChatComponent_div_49_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r73 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 140);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_49_button_5_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r73);\n      const emoji_r71 = restoredCtx.$implicit;\n      const ctx_r72 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r72.insertEmoji(emoji_r71));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const emoji_r71 = ctx.$implicit;\n    i0.ɵɵproperty(\"title\", emoji_r71.name);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", emoji_r71.emoji, \" \");\n  }\n}\nfunction MessageChatComponent_div_49_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 135)(1, \"div\", 136)(2, \"h4\", 137);\n    i0.ɵɵtext(3, \" \\u00C9mojis \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 138);\n    i0.ɵɵtemplate(5, MessageChatComponent_div_49_button_5_Template, 2, 2, \"button\", 139);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r14.getEmojisForCategory(ctx_r14.selectedEmojiCategory));\n  }\n}\nfunction MessageChatComponent_div_50_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r75 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 141)(1, \"div\", 136)(2, \"h4\", 137);\n    i0.ɵɵtext(3, \" Pi\\u00E8ces jointes \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 142)(5, \"button\", 143);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_50_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r75);\n      const ctx_r74 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r74.triggerFileInput(\"image\"));\n    });\n    i0.ɵɵelementStart(6, \"div\", 144);\n    i0.ɵɵelement(7, \"i\", 145);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"span\", 146);\n    i0.ɵɵtext(9, \"Images\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"button\", 143);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_50_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r75);\n      const ctx_r76 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r76.triggerFileInput(\"document\"));\n    });\n    i0.ɵɵelementStart(11, \"div\", 147);\n    i0.ɵɵelement(12, \"i\", 148);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"span\", 146);\n    i0.ɵɵtext(14, \"Documents\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"button\", 143);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_50_Template_button_click_15_listener() {\n      i0.ɵɵrestoreView(_r75);\n      const ctx_r77 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r77.openCamera());\n    });\n    i0.ɵɵelementStart(16, \"div\", 149);\n    i0.ɵɵelement(17, \"i\", 150);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"span\", 146);\n    i0.ɵɵtext(19, \"Cam\\u00E9ra\");\n    i0.ɵɵelementEnd()()()()();\n  }\n}\nfunction MessageChatComponent_div_53_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r79 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 151);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_53_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r79);\n      const ctx_r78 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r78.closeAllMenus());\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MessageChatComponent_div_54_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 164);\n  }\n  if (rf & 2) {\n    const wave_r81 = ctx.$implicit;\n    const i_r82 = ctx.index;\n    i0.ɵɵstyleProp(\"height\", wave_r81, \"px\")(\"animation\", \"bounce 1s infinite\")(\"animation-delay\", i_r82 * 0.1 + \"s\");\n  }\n}\nfunction MessageChatComponent_div_54_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r84 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 152)(1, \"div\", 153);\n    i0.ɵɵelement(2, \"i\", 154);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 155)(4, \"div\", 156);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 157);\n    i0.ɵɵtemplate(7, MessageChatComponent_div_54_div_7_Template, 1, 6, \"div\", 158);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 159);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 35)(11, \"button\", 160);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_54_Template_button_click_11_listener($event) {\n      i0.ɵɵrestoreView(_r84);\n      const ctx_r83 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r83.onRecordCancel($event));\n    });\n    i0.ɵɵelement(12, \"i\", 161);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"button\", 162);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_54_Template_button_click_13_listener($event) {\n      i0.ɵɵrestoreView(_r84);\n      const ctx_r85 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r85.onRecordEnd($event));\n    });\n    i0.ɵɵelement(14, \"i\", 163);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r18 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r18.formatRecordingDuration(ctx_r18.voiceRecordingDuration), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r18.voiceWaves);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" Format: \", ctx_r18.getRecordingFormat(), \" \");\n  }\n}\nexport class MessageChatComponent {\n  constructor(fb, route, router, MessageService, callService, toastService, cdr) {\n    this.fb = fb;\n    this.route = route;\n    this.router = router;\n    this.MessageService = MessageService;\n    this.callService = callService;\n    this.toastService = toastService;\n    this.cdr = cdr;\n    // === DONNÉES PRINCIPALES ===\n    this.conversation = null;\n    this.messages = [];\n    this.currentUserId = null;\n    this.currentUsername = 'You';\n    this.otherParticipant = null;\n    // === ÉTATS DE L'INTERFACE ===\n    this.isLoading = false;\n    this.isLoadingMore = false;\n    this.hasMoreMessages = true;\n    this.showEmojiPicker = false;\n    this.showAttachmentMenu = false;\n    this.showSearch = false;\n    this.searchQuery = '';\n    this.searchResults = [];\n    this.searchMode = false;\n    this.isSendingMessage = false;\n    this.otherUserIsTyping = false;\n    this.showMainMenu = false;\n    this.showMessageContextMenu = false;\n    this.selectedMessage = null;\n    this.contextMenuPosition = {\n      x: 0,\n      y: 0\n    };\n    this.showReactionPicker = false;\n    this.reactionPickerMessage = null;\n    this.showImageViewer = false;\n    this.selectedImage = null;\n    this.uploadProgress = 0;\n    this.isUploading = false;\n    this.isDragOver = false;\n    // === GESTION VOCALE OPTIMISÉE ===\n    this.isRecordingVoice = false;\n    this.voiceRecordingDuration = 0;\n    this.voiceRecordingState = 'idle';\n    this.mediaRecorder = null;\n    this.audioChunks = [];\n    this.recordingTimer = null;\n    this.voiceWaves = [4, 8, 12, 16, 20, 16, 12, 8, 4, 8, 12, 16, 20, 16, 12, 8];\n    // Lecture des messages vocaux\n    this.currentAudio = null;\n    this.playingMessageId = null;\n    this.voicePlayback = {};\n    // === APPELS WEBRTC ===\n    this.isInCall = false;\n    this.callType = null;\n    this.callDuration = 0;\n    this.callTimer = null;\n    // État de l'appel WebRTC\n    this.activeCall = null;\n    this.isCallConnected = false;\n    this.isMuted = false;\n    this.isVideoEnabled = true;\n    this.localVideoElement = null;\n    this.remoteVideoElement = null;\n    // === ÉMOJIS ===\n    this.emojiCategories = [{\n      id: 'smileys',\n      name: 'Smileys',\n      icon: '😀',\n      emojis: [{\n        emoji: '😀',\n        name: 'grinning face'\n      }, {\n        emoji: '😃',\n        name: 'grinning face with big eyes'\n      }, {\n        emoji: '😄',\n        name: 'grinning face with smiling eyes'\n      }, {\n        emoji: '😁',\n        name: 'beaming face with smiling eyes'\n      }, {\n        emoji: '😆',\n        name: 'grinning squinting face'\n      }, {\n        emoji: '😅',\n        name: 'grinning face with sweat'\n      }, {\n        emoji: '😂',\n        name: 'face with tears of joy'\n      }, {\n        emoji: '🤣',\n        name: 'rolling on the floor laughing'\n      }, {\n        emoji: '😊',\n        name: 'smiling face with smiling eyes'\n      }, {\n        emoji: '😇',\n        name: 'smiling face with halo'\n      }]\n    }, {\n      id: 'people',\n      name: 'People',\n      icon: '👤',\n      emojis: [{\n        emoji: '👶',\n        name: 'baby'\n      }, {\n        emoji: '🧒',\n        name: 'child'\n      }, {\n        emoji: '👦',\n        name: 'boy'\n      }, {\n        emoji: '👧',\n        name: 'girl'\n      }, {\n        emoji: '🧑',\n        name: 'person'\n      }, {\n        emoji: '👨',\n        name: 'man'\n      }, {\n        emoji: '👩',\n        name: 'woman'\n      }, {\n        emoji: '👴',\n        name: 'old man'\n      }, {\n        emoji: '👵',\n        name: 'old woman'\n      }]\n    }, {\n      id: 'nature',\n      name: 'Nature',\n      icon: '🌿',\n      emojis: [{\n        emoji: '🐶',\n        name: 'dog face'\n      }, {\n        emoji: '🐱',\n        name: 'cat face'\n      }, {\n        emoji: '🐭',\n        name: 'mouse face'\n      }, {\n        emoji: '🐹',\n        name: 'hamster'\n      }, {\n        emoji: '🐰',\n        name: 'rabbit face'\n      }, {\n        emoji: '🦊',\n        name: 'fox'\n      }, {\n        emoji: '🐻',\n        name: 'bear'\n      }, {\n        emoji: '🐼',\n        name: 'panda'\n      }]\n    }];\n    this.selectedEmojiCategory = this.emojiCategories[0];\n    // === PAGINATION ===\n    this.MAX_MESSAGES_TO_LOAD = 10;\n    this.currentPage = 1;\n    // === AUTRES ÉTATS ===\n    this.isTyping = false;\n    this.isUserTyping = false;\n    this.typingTimeout = null;\n    this.subscriptions = new Subscription();\n    this.messageForm = this.fb.group({\n      content: ['', [Validators.required, Validators.minLength(1)]]\n    });\n  }\n  // Méthode pour vérifier si le champ de saisie doit être désactivé\n  isInputDisabled() {\n    return !this.otherParticipant || this.isRecordingVoice || this.isSendingMessage;\n  }\n  // Méthode pour gérer l'état du contrôle de saisie\n  updateInputState() {\n    const contentControl = this.messageForm.get('content');\n    if (this.isInputDisabled()) {\n      contentControl?.disable();\n    } else {\n      contentControl?.enable();\n    }\n  }\n  ngOnInit() {\n    console.log('🚀 MessageChatComponent initialized');\n    this.initializeComponent();\n    // Activer les sons après interaction utilisateur\n    this.enableSoundsOnFirstInteraction();\n  }\n  enableSoundsOnFirstInteraction() {\n    const enableSounds = () => {\n      this.callService.enableSounds();\n      document.removeEventListener('click', enableSounds);\n      document.removeEventListener('keydown', enableSounds);\n      document.removeEventListener('touchstart', enableSounds);\n    };\n    document.addEventListener('click', enableSounds, {\n      once: true\n    });\n    document.addEventListener('keydown', enableSounds, {\n      once: true\n    });\n    document.addEventListener('touchstart', enableSounds, {\n      once: true\n    });\n  }\n  initializeComponent() {\n    this.loadCurrentUser();\n    this.loadConversation();\n    this.setupCallSubscriptions();\n  }\n  setupCallSubscriptions() {\n    // S'abonner aux appels entrants\n    this.subscriptions.add(this.callService.incomingCall$.subscribe({\n      next: incomingCall => {\n        if (incomingCall) {\n          console.log('📞 Incoming call received:', incomingCall);\n          this.handleIncomingCall(incomingCall);\n        }\n      },\n      error: error => {\n        console.error('❌ Error in incoming call subscription:', error);\n      }\n    }));\n    // S'abonner aux changements d'état d'appel\n    this.subscriptions.add(this.callService.activeCall$.subscribe({\n      next: call => {\n        if (call) {\n          console.log('📞 Active call updated:', call);\n          this.activeCall = call;\n        }\n      },\n      error: error => {\n        console.error('❌ Error in active call subscription:', error);\n      }\n    }));\n  }\n  handleIncomingCall(incomingCall) {\n    // Afficher une notification ou modal d'appel entrant\n    // Pour l'instant, on log juste\n    console.log('🔔 Handling incoming call from:', incomingCall.caller.username);\n    // Jouer la sonnerie\n    this.MessageService.play('ringtone');\n    // Ici on pourrait afficher une modal ou notification\n    // Pour l'instant, on accepte automatiquement pour tester\n    // this.acceptCall(incomingCall);\n  }\n\n  loadCurrentUser() {\n    try {\n      const userString = localStorage.getItem('user');\n      console.log('🔍 Raw user from localStorage:', userString);\n      if (!userString || userString === 'null' || userString === 'undefined') {\n        console.error('❌ No user data in localStorage');\n        this.currentUserId = null;\n        this.currentUsername = 'You';\n        return;\n      }\n      const user = JSON.parse(userString);\n      console.log('🔍 Parsed user object:', user);\n      // Essayer différentes propriétés pour l'ID utilisateur\n      const userId = user._id || user.id || user.userId;\n      console.log('🔍 Trying to extract user ID:', {\n        _id: user._id,\n        id: user.id,\n        userId: user.userId,\n        extracted: userId\n      });\n      if (userId) {\n        this.currentUserId = userId;\n        this.currentUsername = user.username || user.name || 'You';\n        console.log('✅ Current user loaded successfully:', {\n          id: this.currentUserId,\n          username: this.currentUsername\n        });\n      } else {\n        console.error('❌ No valid user ID found in user object:', user);\n        this.currentUserId = null;\n        this.currentUsername = 'You';\n      }\n    } catch (error) {\n      console.error('❌ Error parsing user from localStorage:', error);\n      this.currentUserId = null;\n      this.currentUsername = 'You';\n    }\n  }\n  loadConversation() {\n    const conversationId = this.route.snapshot.paramMap.get('id');\n    console.log('Loading conversation with ID:', conversationId);\n    if (!conversationId) {\n      this.toastService.showError('ID de conversation manquant');\n      return;\n    }\n    this.isLoading = true;\n    this.MessageService.getConversation(conversationId).subscribe({\n      next: conversation => {\n        console.log('🔍 Conversation loaded successfully:', conversation);\n        console.log('🔍 Conversation structure:', {\n          id: conversation?.id,\n          participants: conversation?.participants,\n          participantsCount: conversation?.participants?.length,\n          isGroup: conversation?.isGroup,\n          messages: conversation?.messages,\n          messagesCount: conversation?.messages?.length\n        });\n        this.conversation = conversation;\n        this.setOtherParticipant();\n        this.loadMessages();\n        // Configurer les subscriptions temps réel après le chargement de la conversation\n        this.setupSubscriptions();\n      },\n      error: error => {\n        console.error('Erreur lors du chargement de la conversation:', error);\n        this.toastService.showError('Erreur lors du chargement de la conversation');\n        this.isLoading = false;\n      }\n    });\n  }\n  setOtherParticipant() {\n    if (!this.conversation?.participants || this.conversation.participants.length === 0) {\n      console.warn('No participants found in conversation');\n      this.otherParticipant = null;\n      return;\n    }\n    console.log('Setting other participant...');\n    console.log('Current user ID:', this.currentUserId);\n    console.log('All participants:', this.conversation.participants);\n    // Dans une conversation 1-à-1, on veut afficher l'autre personne (pas l'utilisateur actuel)\n    // Dans une conversation de groupe, on peut afficher le nom du groupe ou le premier autre participant\n    if (this.conversation.isGroup) {\n      // Pour les groupes, on pourrait afficher le nom du groupe\n      // Mais pour l'instant, on prend le premier participant qui n'est pas l'utilisateur actuel\n      this.otherParticipant = this.conversation.participants.find(p => {\n        const participantId = p.id || p._id;\n        return String(participantId) !== String(this.currentUserId);\n      });\n    } else {\n      // Pour les conversations 1-à-1, on prend l'autre participant\n      this.otherParticipant = this.conversation.participants.find(p => {\n        const participantId = p.id || p._id;\n        console.log('Comparing participant ID:', participantId, 'with current user ID:', this.currentUserId);\n        return String(participantId) !== String(this.currentUserId);\n      });\n    }\n    // Fallback si aucun autre participant n'est trouvé\n    if (!this.otherParticipant && this.conversation.participants.length > 0) {\n      console.log('Fallback: using first participant');\n      this.otherParticipant = this.conversation.participants[0];\n      // Si le premier participant est l'utilisateur actuel et qu'il y en a d'autres\n      if (this.conversation.participants.length > 1) {\n        const firstParticipantId = this.otherParticipant.id || this.otherParticipant._id;\n        if (String(firstParticipantId) === String(this.currentUserId)) {\n          console.log('First participant is current user, using second participant');\n          this.otherParticipant = this.conversation.participants[1];\n        }\n      }\n    }\n    // Vérification finale et logs\n    if (this.otherParticipant) {\n      console.log('✅ Other participant set successfully:', {\n        id: this.otherParticipant.id || this.otherParticipant._id,\n        username: this.otherParticipant.username,\n        image: this.otherParticipant.image,\n        isOnline: this.otherParticipant.isOnline\n      });\n      // Log très visible pour debug\n      console.log('🎯 FINAL RESULT: otherParticipant =', this.otherParticipant.username);\n      console.log('🎯 Should display in sidebar:', this.otherParticipant.username);\n    } else {\n      console.error('❌ No other participant found! This should not happen.');\n      console.log('Conversation participants:', this.conversation.participants);\n      console.log('Current user ID:', this.currentUserId);\n      // Log très visible pour debug\n      console.log('🚨 ERROR: No otherParticipant found!');\n    }\n    // Mettre à jour l'état du champ de saisie\n    this.updateInputState();\n  }\n  loadMessages() {\n    if (!this.conversation?.id) return;\n    // Les messages sont déjà chargés avec la conversation\n    let messages = this.conversation.messages || [];\n    // Trier les messages par timestamp (plus anciens en premier)\n    this.messages = messages.sort((a, b) => {\n      const dateA = new Date(a.timestamp || a.createdAt).getTime();\n      const dateB = new Date(b.timestamp || b.createdAt).getTime();\n      return dateA - dateB; // Ordre croissant (plus anciens en premier)\n    });\n\n    console.log('📋 Messages loaded and sorted:', {\n      total: this.messages.length,\n      first: this.messages[0]?.content,\n      last: this.messages[this.messages.length - 1]?.content\n    });\n    this.hasMoreMessages = this.messages.length === this.MAX_MESSAGES_TO_LOAD;\n    this.isLoading = false;\n    this.scrollToBottom();\n  }\n  loadMoreMessages() {\n    if (this.isLoadingMore || !this.hasMoreMessages || !this.conversation?.id) return;\n    this.isLoadingMore = true;\n    this.currentPage++;\n    // Calculer l'offset basé sur les messages déjà chargés\n    const offset = this.messages.length;\n    this.MessageService.getMessages(this.currentUserId,\n    // senderId\n    this.otherParticipant?.id || this.otherParticipant?._id,\n    // receiverId\n    this.conversation.id, this.currentPage, this.MAX_MESSAGES_TO_LOAD).subscribe({\n      next: newMessages => {\n        if (newMessages && newMessages.length > 0) {\n          // Ajouter les nouveaux messages au début de la liste\n          this.messages = [...newMessages.reverse(), ...this.messages];\n          this.hasMoreMessages = newMessages.length === this.MAX_MESSAGES_TO_LOAD;\n        } else {\n          this.hasMoreMessages = false;\n        }\n        this.isLoadingMore = false;\n      },\n      error: error => {\n        console.error('Erreur lors du chargement des messages:', error);\n        this.toastService.showError('Erreur lors du chargement des messages');\n        this.isLoadingMore = false;\n        this.currentPage--; // Revenir à la page précédente en cas d'erreur\n      }\n    });\n  }\n\n  setupSubscriptions() {\n    if (!this.conversation?.id) {\n      console.warn('❌ Cannot setup subscriptions: no conversation ID');\n      return;\n    }\n    console.log('🔄 Setting up real-time subscriptions for conversation:', this.conversation.id);\n    // Subscription pour les nouveaux messages\n    console.log('📨 Setting up message subscription...');\n    this.subscriptions.add(this.MessageService.subscribeToNewMessages(this.conversation.id).subscribe({\n      next: newMessage => {\n        console.log('📨 New message received via subscription:', newMessage);\n        console.log('📨 Message structure:', {\n          id: newMessage.id,\n          type: newMessage.type,\n          content: newMessage.content,\n          sender: newMessage.sender,\n          senderId: newMessage.senderId,\n          receiverId: newMessage.receiverId,\n          attachments: newMessage.attachments\n        });\n        // Debug des attachments\n        console.log('📨 [Debug] Message type detected:', this.getMessageType(newMessage));\n        console.log('📨 [Debug] Has image:', this.hasImage(newMessage));\n        console.log('📨 [Debug] Has file:', this.hasFile(newMessage));\n        console.log('📨 [Debug] Image URL:', this.getImageUrl(newMessage));\n        if (newMessage.attachments) {\n          newMessage.attachments.forEach((att, index) => {\n            console.log(`📨 [Debug] Attachment ${index}:`, {\n              type: att.type,\n              url: att.url,\n              path: att.path,\n              name: att.name,\n              size: att.size\n            });\n          });\n        }\n        // Ajouter le message à la liste s'il n'existe pas déjà\n        const messageExists = this.messages.some(msg => msg.id === newMessage.id);\n        if (!messageExists) {\n          // Ajouter le nouveau message à la fin (en bas)\n          this.messages.push(newMessage);\n          console.log('✅ Message added to list, total messages:', this.messages.length);\n          // Forcer la détection de changements\n          this.cdr.detectChanges();\n          // Scroll vers le bas après un court délai\n          setTimeout(() => {\n            this.scrollToBottom();\n          }, 50);\n          // Marquer comme lu si ce n'est pas notre message\n          const senderId = newMessage.sender?.id || newMessage.senderId;\n          console.log('📨 Checking if message should be marked as read:', {\n            senderId,\n            currentUserId: this.currentUserId,\n            shouldMarkAsRead: senderId !== this.currentUserId\n          });\n          if (senderId && senderId !== this.currentUserId) {\n            this.markMessageAsRead(newMessage.id);\n          }\n        }\n      },\n      error: error => {\n        console.error('❌ Error in message subscription:', error);\n      }\n    }));\n    // Subscription pour les indicateurs de frappe\n    console.log('📝 Setting up typing indicator subscription...');\n    this.subscriptions.add(this.MessageService.subscribeToTypingIndicator(this.conversation.id).subscribe({\n      next: typingData => {\n        console.log('📝 Typing indicator received:', typingData);\n        // Afficher l'indicateur seulement si c'est l'autre utilisateur qui tape\n        if (typingData.userId !== this.currentUserId) {\n          this.otherUserIsTyping = typingData.isTyping;\n          this.isUserTyping = typingData.isTyping; // Pour compatibilité avec le template\n          console.log('📝 Other user typing status updated:', this.otherUserIsTyping);\n          this.cdr.detectChanges();\n        }\n      },\n      error: error => {\n        console.error('❌ Error in typing subscription:', error);\n      }\n    }));\n    // Subscription pour les mises à jour de conversation\n    this.subscriptions.add(this.MessageService.subscribeToConversationUpdates(this.conversation.id).subscribe({\n      next: conversationUpdate => {\n        console.log('📋 Conversation update:', conversationUpdate);\n        // Mettre à jour la conversation si nécessaire\n        if (conversationUpdate.id === this.conversation.id) {\n          this.conversation = {\n            ...this.conversation,\n            ...conversationUpdate\n          };\n          this.cdr.detectChanges();\n        }\n      },\n      error: error => {\n        console.error('❌ Error in conversation subscription:', error);\n      }\n    }));\n  }\n  markMessageAsRead(messageId) {\n    this.MessageService.markMessageAsRead(messageId).subscribe({\n      next: () => {\n        console.log('✅ Message marked as read:', messageId);\n      },\n      error: error => {\n        console.error('❌ Error marking message as read:', error);\n      }\n    });\n  }\n  // === ENVOI DE MESSAGES ===\n  sendMessage() {\n    if (!this.messageForm.valid || !this.conversation?.id) return;\n    const content = this.messageForm.get('content')?.value?.trim();\n    if (!content) return;\n    const receiverId = this.otherParticipant?.id || this.otherParticipant?._id;\n    if (!receiverId) {\n      this.toastService.showError('Destinataire introuvable');\n      return;\n    }\n    // Désactiver le bouton d'envoi\n    this.isSendingMessage = true;\n    this.updateInputState();\n    console.log('📤 Sending message:', {\n      content,\n      receiverId,\n      conversationId: this.conversation.id\n    });\n    this.MessageService.sendMessage(receiverId, content, undefined, 'TEXT', this.conversation.id).subscribe({\n      next: message => {\n        console.log('✅ Message sent successfully:', message);\n        // Ajouter le message à la liste s'il n'y est pas déjà\n        const messageExists = this.messages.some(msg => msg.id === message.id);\n        if (!messageExists) {\n          this.messages.push(message);\n          console.log('📋 Message added to local list, total:', this.messages.length);\n        }\n        // Réinitialiser le formulaire\n        this.messageForm.reset();\n        this.isSendingMessage = false;\n        this.updateInputState();\n        // Forcer la détection de changements et scroll\n        this.cdr.detectChanges();\n        setTimeout(() => {\n          this.scrollToBottom();\n        }, 50);\n      },\n      error: error => {\n        console.error(\"❌ Erreur lors de l'envoi du message:\", error);\n        this.toastService.showError(\"Erreur lors de l'envoi du message\");\n        this.isSendingMessage = false;\n        this.updateInputState();\n      }\n    });\n  }\n  scrollToBottom() {\n    setTimeout(() => {\n      if (this.messagesContainer) {\n        const element = this.messagesContainer.nativeElement;\n        element.scrollTop = element.scrollHeight;\n      }\n    }, 100);\n  }\n  // === MÉTHODES UTILITAIRES OPTIMISÉES ===\n  formatLastActive(lastActive) {\n    if (!lastActive) return 'Hors ligne';\n    const diffMins = Math.floor((Date.now() - new Date(lastActive).getTime()) / 60000);\n    if (diffMins < 1) return \"À l'instant\";\n    if (diffMins < 60) return `Il y a ${diffMins} min`;\n    if (diffMins < 1440) return `Il y a ${Math.floor(diffMins / 60)}h`;\n    return `Il y a ${Math.floor(diffMins / 1440)}j`;\n  }\n  // Méthodes utilitaires pour les messages vocaux\n  getVoicePlaybackData(messageId) {\n    return this.voicePlayback[messageId] || {\n      progress: 0,\n      duration: 0,\n      currentTime: 0,\n      speed: 1\n    };\n  }\n  setVoicePlaybackData(messageId, data) {\n    this.voicePlayback[messageId] = {\n      ...this.getVoicePlaybackData(messageId),\n      ...data\n    };\n  }\n  // === MÉTHODES POUR LES MESSAGES VOCAUX (TEMPLATE) ===\n  // === MÉTHODES POUR LES APPELS (TEMPLATE) ===\n  startVideoCall() {\n    if (!this.otherParticipant?.id) {\n      this.toastService.showError(\"Impossible de démarrer l'appel\");\n      return;\n    }\n    console.log('📹 Starting video call with:', this.otherParticipant.username);\n    this.initiateCall(CallType.VIDEO);\n  }\n  startVoiceCall() {\n    if (!this.otherParticipant?.id) {\n      this.toastService.showError(\"Impossible de démarrer l'appel\");\n      return;\n    }\n    console.log('📞 Starting voice call with:', this.otherParticipant.username);\n    this.initiateCall(CallType.AUDIO);\n  }\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // onCallAccepted, onCallRejected - définies plus loin\n  // === MÉTHODES POUR LES FICHIERS (TEMPLATE) ===\n  formatFileSize(bytes) {\n    if (bytes < 1024) return bytes + ' B';\n    if (bytes < 1048576) return Math.round(bytes / 1024) + ' KB';\n    return Math.round(bytes / 1048576) + ' MB';\n  }\n  downloadFile(message) {\n    const fileAttachment = message.attachments?.find(att => !att.type?.startsWith('image/'));\n    if (fileAttachment?.url) {\n      const link = document.createElement('a');\n      link.href = fileAttachment.url;\n      link.download = fileAttachment.name || 'file';\n      link.target = '_blank';\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      this.toastService.showSuccess('Téléchargement démarré');\n    }\n  }\n  // === MÉTHODES POUR L'INTERFACE UTILISATEUR (TEMPLATE) ===\n  toggleSearch() {\n    this.searchMode = !this.searchMode;\n    this.showSearch = this.searchMode;\n  }\n  toggleMainMenu() {\n    this.showMainMenu = !this.showMainMenu;\n  }\n  goBackToConversations() {\n    console.log('🔙 Going back to conversations');\n    // Naviguer vers la liste des conversations\n    this.router.navigate(['/front/messages/conversations']).then(() => {\n      console.log('✅ Navigation to conversations successful');\n    }).catch(error => {\n      console.error('❌ Navigation error:', error);\n      // Fallback: essayer la route parent\n      this.router.navigate(['/front/messages']).catch(() => {\n        // Dernier recours: recharger la page\n        window.location.href = '/front/messages/conversations';\n      });\n    });\n  }\n  // === MÉTHODES POUR LES MENUS ET INTERACTIONS ===\n  closeAllMenus() {\n    this.showEmojiPicker = false;\n    this.showAttachmentMenu = false;\n    this.showMainMenu = false;\n    this.showMessageContextMenu = false;\n    this.showReactionPicker = false;\n  }\n  onMessageContextMenu(message, event) {\n    event.preventDefault();\n    this.selectedMessage = message;\n    this.contextMenuPosition = {\n      x: event.clientX,\n      y: event.clientY\n    };\n    this.showMessageContextMenu = true;\n  }\n  showQuickReactions(message, event) {\n    event.stopPropagation();\n    this.reactionPickerMessage = message;\n    this.contextMenuPosition = {\n      x: event.clientX,\n      y: event.clientY\n    };\n    this.showReactionPicker = true;\n  }\n  quickReact(emoji) {\n    if (this.reactionPickerMessage) {\n      this.toggleReaction(this.reactionPickerMessage.id, emoji);\n    }\n    this.showReactionPicker = false;\n  }\n  toggleReaction(messageId, emoji) {\n    console.log('👍 Toggling reaction:', emoji, 'for message:', messageId);\n    if (!messageId || !emoji) {\n      console.error('❌ Missing messageId or emoji for reaction');\n      return;\n    }\n    // Appeler le service pour ajouter/supprimer la réaction\n    this.MessageService.reactToMessage(messageId, emoji).subscribe({\n      next: result => {\n        console.log('✅ Reaction toggled successfully:', result);\n        // Mettre à jour le message local avec les nouvelles réactions\n        const messageIndex = this.messages.findIndex(msg => msg.id === messageId);\n        if (messageIndex !== -1) {\n          this.messages[messageIndex] = result;\n          this.cdr.detectChanges();\n        }\n        this.toastService.showSuccess(`Réaction ${emoji} ajoutée`);\n      },\n      error: error => {\n        console.error('❌ Error toggling reaction:', error);\n        this.toastService.showError(\"Erreur lors de l'ajout de la réaction\");\n      }\n    });\n  }\n  hasUserReacted(reaction, userId) {\n    return reaction.userId === userId;\n  }\n  replyToMessage(message) {\n    console.log('↩️ Replying to message:', message.id);\n    this.closeAllMenus();\n  }\n  forwardMessage(message) {\n    console.log('➡️ Forwarding message:', message.id);\n    this.closeAllMenus();\n  }\n  deleteMessage(message) {\n    console.log('🗑️ Deleting message:', message.id);\n    if (!message.id) {\n      console.error('❌ No message ID provided for deletion');\n      this.toastService.showError('Erreur: ID du message manquant');\n      return;\n    }\n    // Vérifier si l'utilisateur peut supprimer ce message\n    const canDelete = message.sender?.id === this.currentUserId || message.senderId === this.currentUserId;\n    if (!canDelete) {\n      this.toastService.showError('Vous ne pouvez supprimer que vos propres messages');\n      this.closeAllMenus();\n      return;\n    }\n    // Demander confirmation\n    if (!confirm('Êtes-vous sûr de vouloir supprimer ce message ?')) {\n      this.closeAllMenus();\n      return;\n    }\n    // Appeler le service pour supprimer le message\n    this.MessageService.deleteMessage(message.id).subscribe({\n      next: result => {\n        console.log('✅ Message deleted successfully:', result);\n        // Supprimer le message de la liste locale\n        this.messages = this.messages.filter(msg => msg.id !== message.id);\n        this.toastService.showSuccess('Message supprimé');\n        this.cdr.detectChanges();\n      },\n      error: error => {\n        console.error('❌ Error deleting message:', error);\n        this.toastService.showError('Erreur lors de la suppression du message');\n      }\n    });\n    this.closeAllMenus();\n  }\n  // === MÉTHODES POUR LES ÉMOJIS ET PIÈCES JOINTES ===\n  toggleEmojiPicker() {\n    this.showEmojiPicker = !this.showEmojiPicker;\n  }\n  selectEmojiCategory(category) {\n    this.selectedEmojiCategory = category;\n  }\n  getEmojisForCategory(category) {\n    return category?.emojis || [];\n  }\n  insertEmoji(emoji) {\n    const currentContent = this.messageForm.get('content')?.value || '';\n    const newContent = currentContent + emoji.emoji;\n    this.messageForm.patchValue({\n      content: newContent\n    });\n    this.showEmojiPicker = false;\n  }\n  toggleAttachmentMenu() {\n    this.showAttachmentMenu = !this.showAttachmentMenu;\n  }\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // triggerFileInput, getFileAcceptTypes, onFileSelected - définies plus loin\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // Ces méthodes sont déjà définies plus loin dans le fichier\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // handleTypingIndicator - définie plus loin\n  // === MÉTHODES UTILITAIRES POUR LE TEMPLATE ===\n  trackByMessageId(index, message) {\n    return message.id || message._id || index.toString();\n  }\n  // === MÉTHODES MANQUANTES POUR LE TEMPLATE ===\n  testAddMessage() {\n    console.log('🧪 Test: Adding message');\n    const testMessage = {\n      id: `test-${Date.now()}`,\n      content: `Message de test ${new Date().toLocaleTimeString()}`,\n      timestamp: new Date().toISOString(),\n      sender: {\n        id: this.otherParticipant?.id || 'test-user',\n        username: this.otherParticipant?.username || 'Test User',\n        image: this.otherParticipant?.image || 'assets/images/default-avatar.png'\n      },\n      type: 'TEXT',\n      isRead: false\n    };\n    this.messages.push(testMessage);\n    this.cdr.detectChanges();\n    setTimeout(() => this.scrollToBottom(), 50);\n  }\n  isGroupConversation() {\n    return this.conversation?.isGroup || this.conversation?.participants?.length > 2 || false;\n  }\n  openCamera() {\n    console.log('📷 Opening camera');\n    this.showAttachmentMenu = false;\n    // TODO: Implémenter l'ouverture de la caméra\n  }\n\n  zoomImage(factor) {\n    const imageElement = document.querySelector('.image-viewer-zoom');\n    if (imageElement) {\n      const currentTransform = imageElement.style.transform || 'scale(1)';\n      const currentScale = parseFloat(currentTransform.match(/scale\\(([^)]+)\\)/)?.[1] || '1');\n      const newScale = Math.max(0.5, Math.min(3, currentScale * factor));\n      imageElement.style.transform = `scale(${newScale})`;\n      if (newScale > 1) {\n        imageElement.classList.add('zoomed');\n      } else {\n        imageElement.classList.remove('zoomed');\n      }\n    }\n  }\n  resetZoom() {\n    const imageElement = document.querySelector('.image-viewer-zoom');\n    if (imageElement) {\n      imageElement.style.transform = 'scale(1)';\n      imageElement.classList.remove('zoomed');\n    }\n  }\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // formatFileSize, downloadFile, toggleReaction, hasUserReacted, showQuickReactions\n  // toggleEmojiPicker, toggleAttachmentMenu, selectEmojiCategory, getEmojisForCategory, insertEmoji\n  // Ces méthodes sont déjà définies plus loin dans le fichier\n  triggerFileInput(type) {\n    const input = this.fileInput?.nativeElement;\n    if (!input) {\n      console.error('File input element not found');\n      return;\n    }\n    // Configurer le type de fichier accepté\n    if (type === 'image') {\n      input.accept = 'image/*';\n    } else if (type === 'video') {\n      input.accept = 'video/*';\n    } else if (type === 'document') {\n      input.accept = '.pdf,.doc,.docx,.xls,.xlsx,.txt';\n    } else {\n      input.accept = '*/*';\n    }\n    // Réinitialiser la valeur pour permettre la sélection du même fichier\n    input.value = '';\n    // Déclencher la sélection de fichier\n    input.click();\n    this.showAttachmentMenu = false;\n  }\n  formatMessageTime(timestamp) {\n    if (!timestamp) return '';\n    const date = new Date(timestamp);\n    return date.toLocaleTimeString('fr-FR', {\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  }\n  formatDateSeparator(timestamp) {\n    if (!timestamp) return '';\n    const date = new Date(timestamp);\n    const today = new Date();\n    const yesterday = new Date(today);\n    yesterday.setDate(yesterday.getDate() - 1);\n    if (date.toDateString() === today.toDateString()) {\n      return \"Aujourd'hui\";\n    } else if (date.toDateString() === yesterday.toDateString()) {\n      return 'Hier';\n    } else {\n      return date.toLocaleDateString('fr-FR');\n    }\n  }\n  formatMessageContent(content) {\n    if (!content) return '';\n    // Remplacer les URLs par des liens\n    const urlRegex = /(https?:\\/\\/[^\\s]+)/g;\n    return content.replace(urlRegex, '<a href=\"$1\" target=\"_blank\" class=\"text-blue-500 underline\">$1</a>');\n  }\n  shouldShowDateSeparator(index) {\n    if (index === 0) return true;\n    const currentMessage = this.messages[index];\n    const previousMessage = this.messages[index - 1];\n    if (!currentMessage?.timestamp || !previousMessage?.timestamp) return false;\n    const currentDate = new Date(currentMessage.timestamp).toDateString();\n    const previousDate = new Date(previousMessage.timestamp).toDateString();\n    return currentDate !== previousDate;\n  }\n  shouldShowAvatar(index) {\n    const currentMessage = this.messages[index];\n    const nextMessage = this.messages[index + 1];\n    if (!nextMessage) return true;\n    return currentMessage.sender?.id !== nextMessage.sender?.id;\n  }\n  shouldShowSenderName(index) {\n    const currentMessage = this.messages[index];\n    const previousMessage = this.messages[index - 1];\n    if (!previousMessage) return true;\n    return currentMessage.sender?.id !== previousMessage.sender?.id;\n  }\n  getMessageType(message) {\n    // Vérifier d'abord le type de message explicite\n    if (message.type) {\n      if (message.type === 'IMAGE' || message.type === 'image') return 'image';\n      if (message.type === 'VIDEO' || message.type === 'video') return 'video';\n      if (message.type === 'AUDIO' || message.type === 'audio') return 'audio';\n      if (message.type === 'VOICE_MESSAGE') return 'audio';\n      if (message.type === 'FILE' || message.type === 'file') return 'file';\n    }\n    // Ensuite vérifier les attachments\n    if (message.attachments && message.attachments.length > 0) {\n      const attachment = message.attachments[0];\n      if (attachment.type?.startsWith('image/')) return 'image';\n      if (attachment.type?.startsWith('video/')) return 'video';\n      if (attachment.type?.startsWith('audio/')) return 'audio';\n      return 'file';\n    }\n    // Vérifier si c'est un message vocal basé sur les propriétés\n    if (message.voiceUrl || message.audioUrl || message.voice) return 'audio';\n    return 'text';\n  }\n  hasImage(message) {\n    // Vérifier le type de message\n    if (message.type === 'IMAGE' || message.type === 'image') {\n      return true;\n    }\n    // Vérifier les attachments\n    const hasImageAttachment = message.attachments?.some(att => {\n      return att.type?.startsWith('image/') || att.type === 'IMAGE';\n    }) || false;\n    // Vérifier les propriétés directes d'image\n    const hasImageUrl = !!(message.imageUrl || message.image);\n    return hasImageAttachment || hasImageUrl;\n  }\n  hasFile(message) {\n    // Vérifier le type de message\n    if (message.type === 'FILE' || message.type === 'file') {\n      return true;\n    }\n    // Vérifier les attachments non-image\n    const hasFileAttachment = message.attachments?.some(att => {\n      return !att.type?.startsWith('image/') && att.type !== 'IMAGE';\n    }) || false;\n    return hasFileAttachment;\n  }\n  getImageUrl(message) {\n    // Vérifier les propriétés directes d'image\n    if (message.imageUrl) {\n      return message.imageUrl;\n    }\n    if (message.image) {\n      return message.image;\n    }\n    // Vérifier les attachments\n    const imageAttachment = message.attachments?.find(att => att.type?.startsWith('image/') || att.type === 'IMAGE');\n    if (imageAttachment) {\n      return imageAttachment.url || imageAttachment.path || '';\n    }\n    return '';\n  }\n  getFileName(message) {\n    const fileAttachment = message.attachments?.find(att => !att.type?.startsWith('image/'));\n    return fileAttachment?.name || 'Fichier';\n  }\n  getFileSize(message) {\n    const fileAttachment = message.attachments?.find(att => !att.type?.startsWith('image/'));\n    if (!fileAttachment?.size) return '';\n    const bytes = fileAttachment.size;\n    if (bytes < 1024) return bytes + ' B';\n    if (bytes < 1048576) return Math.round(bytes / 1024) + ' KB';\n    return Math.round(bytes / 1048576) + ' MB';\n  }\n  getFileIcon(message) {\n    const fileAttachment = message.attachments?.find(att => !att.type?.startsWith('image/'));\n    if (!fileAttachment?.type) return 'fas fa-file';\n    if (fileAttachment.type.startsWith('audio/')) return 'fas fa-file-audio';\n    if (fileAttachment.type.startsWith('video/')) return 'fas fa-file-video';\n    if (fileAttachment.type.includes('pdf')) return 'fas fa-file-pdf';\n    if (fileAttachment.type.includes('word')) return 'fas fa-file-word';\n    if (fileAttachment.type.includes('excel')) return 'fas fa-file-excel';\n    return 'fas fa-file';\n  }\n  getUserColor(userId) {\n    // Générer une couleur basée sur l'ID utilisateur\n    const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8'];\n    const index = userId.charCodeAt(0) % colors.length;\n    return colors[index];\n  }\n  // === MÉTHODES D'INTERACTION ===\n  onMessageClick(message, event) {\n    console.log('Message clicked:', message);\n  }\n  onInputChange(event) {\n    // Gérer les changements dans le champ de saisie\n    this.handleTypingIndicator();\n  }\n  onInputKeyDown(event) {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n  onInputFocus() {\n    // Gérer le focus sur le champ de saisie\n  }\n  onInputBlur() {\n    // Gérer la perte de focus sur le champ de saisie\n  }\n  onScroll(event) {\n    // Gérer le scroll pour charger plus de messages\n    const element = event.target;\n    if (element.scrollTop === 0 && this.hasMoreMessages && !this.isLoadingMore) {\n      this.loadMoreMessages();\n    }\n  }\n  openUserProfile(userId) {\n    console.log('Opening user profile for:', userId);\n  }\n  onImageLoad(event, message) {\n    console.log('🖼️ [Debug] Image loaded successfully for message:', message.id, event.target.src);\n  }\n  onImageError(event, message) {\n    console.error('🖼️ [Debug] Image failed to load for message:', message.id, {\n      src: event.target.src,\n      error: event\n    });\n    // Optionnel : afficher une image de remplacement\n    event.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjNmNGY2Ii8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzZiNzI4MCIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkltYWdlIG5vbiBkaXNwb25pYmxlPC90ZXh0Pjwvc3ZnPg==';\n  }\n  openImageViewer(message) {\n    const imageAttachment = message.attachments?.find(att => att.type?.startsWith('image/'));\n    if (imageAttachment?.url) {\n      this.selectedImage = {\n        url: imageAttachment.url,\n        name: imageAttachment.name || 'Image',\n        size: this.formatFileSize(imageAttachment.size || 0),\n        message: message\n      };\n      this.showImageViewer = true;\n      console.log('🖼️ [ImageViewer] Opening image:', this.selectedImage);\n    }\n  }\n  closeImageViewer() {\n    this.showImageViewer = false;\n    this.selectedImage = null;\n    console.log('🖼️ [ImageViewer] Closed');\n  }\n  downloadImage() {\n    if (this.selectedImage?.url) {\n      const link = document.createElement('a');\n      link.href = this.selectedImage.url;\n      link.download = this.selectedImage.name || 'image';\n      link.target = '_blank';\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      this.toastService.showSuccess('Téléchargement démarré');\n      console.log('🖼️ [ImageViewer] Download started:', this.selectedImage.name);\n    }\n  }\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // zoomImage, resetZoom, formatFileSize, downloadFile, toggleReaction, hasUserReacted, toggleSearch\n  // Ces méthodes sont déjà définies plus loin dans le fichier\n  searchMessages() {\n    if (!this.searchQuery.trim()) {\n      this.searchResults = [];\n      return;\n    }\n    this.searchResults = this.messages.filter(message => message.content?.toLowerCase().includes(this.searchQuery.toLowerCase()) || message.sender?.username?.toLowerCase().includes(this.searchQuery.toLowerCase()));\n  }\n  onSearchQueryChange() {\n    this.searchMessages();\n  }\n  clearSearch() {\n    this.searchQuery = '';\n    this.searchResults = [];\n  }\n  jumpToMessage(messageId) {\n    const messageElement = document.getElementById(`message-${messageId}`);\n    if (messageElement) {\n      messageElement.scrollIntoView({\n        behavior: 'smooth',\n        block: 'center'\n      });\n      // Highlight temporairement le message\n      messageElement.classList.add('highlight');\n      setTimeout(() => {\n        messageElement.classList.remove('highlight');\n      }, 2000);\n    }\n  }\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // toggleMainMenu, toggleTheme, onMessageContextMenu\n  // Ces méthodes sont déjà définies plus loin dans le fichier\n  closeContextMenu() {\n    this.showMessageContextMenu = false;\n    this.selectedMessage = null;\n  }\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // replyToMessage, forwardMessage, deleteMessage, showQuickReactions, closeReactionPicker\n  // quickReact, closeAllMenus, testAddMessage, toggleAttachmentMenu, toggleEmojiPicker\n  // Ces méthodes sont déjà définies plus loin dans le fichier\n  // === MÉTHODE SUPPRIMÉE (DUPLICATION) ===\n  // triggerFileInput - définie plus loin\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // openCamera, getEmojisForCategory, selectEmojiCategory, insertEmoji\n  // goBackToConversations, startVideoCall, startVoiceCall\n  // Ces méthodes sont déjà définies plus loin dans le fichier\n  initiateCall(callType) {\n    console.log('🚀 [MessageChat] Starting call initiation process...');\n    console.log('📋 [MessageChat] Call details:', {\n      callType,\n      otherParticipant: this.otherParticipant,\n      conversation: this.conversation?.id,\n      currentUserId: this.currentUserId\n    });\n    if (!this.otherParticipant) {\n      console.error('❌ [MessageChat] No recipient selected');\n      this.toastService.showError('Aucun destinataire sélectionné');\n      return;\n    }\n    const recipientId = this.otherParticipant.id || this.otherParticipant._id;\n    if (!recipientId) {\n      console.error('❌ [MessageChat] Recipient ID not found');\n      this.toastService.showError('ID du destinataire introuvable');\n      return;\n    }\n    console.log(`📞 [MessageChat] Initiating ${callType} call to user:`, {\n      recipientId,\n      recipientName: this.otherParticipant.username || this.otherParticipant.name,\n      conversationId: this.conversation?.id\n    });\n    this.isInCall = true;\n    this.callType = callType === CallType.VIDEO ? 'VIDEO' : 'AUDIO';\n    this.callDuration = 0;\n    // Démarrer le timer d'appel\n    this.startCallTimer();\n    console.log('🔄 [MessageChat] Calling CallService.initiateCall...');\n    // Utiliser le CallService\n    this.callService.initiateCall(recipientId, callType, this.conversation?.id).subscribe({\n      next: call => {\n        console.log('✅ [MessageChat] Call initiated successfully:', {\n          callId: call.id,\n          callType: call.type,\n          callStatus: call.status,\n          caller: call.caller?.username,\n          recipient: call.recipient?.username,\n          conversationId: call.conversationId\n        });\n        this.activeCall = call;\n        this.isCallConnected = false;\n        this.toastService.showSuccess(`Appel ${callType === CallType.VIDEO ? 'vidéo' : 'audio'} initié`);\n        console.log('📡 [MessageChat] Call should now be sent to recipient via WebSocket');\n      },\n      error: error => {\n        console.error('❌ [MessageChat] Error initiating call:', {\n          error: error.message || error,\n          recipientId,\n          callType,\n          conversationId: this.conversation?.id\n        });\n        this.endCall();\n        this.toastService.showError(\"Erreur lors de l'initiation de l'appel\");\n      }\n    });\n  }\n  acceptCall(incomingCall) {\n    console.log('🔄 Accepting incoming call:', incomingCall);\n    this.callService.acceptCall(incomingCall).subscribe({\n      next: call => {\n        console.log('✅ Call accepted successfully:', call);\n        this.activeCall = call;\n        this.isInCall = true;\n        this.isCallConnected = true;\n        this.callType = call.type === CallType.VIDEO ? 'VIDEO' : 'AUDIO';\n        this.startCallTimer();\n        this.toastService.showSuccess('Appel accepté');\n      },\n      error: error => {\n        console.error('❌ Error accepting call:', error);\n        this.toastService.showError(\"Erreur lors de l'acceptation de l'appel\");\n      }\n    });\n  }\n  rejectCall(incomingCall) {\n    console.log('🔄 Rejecting incoming call:', incomingCall);\n    this.callService.rejectCall(incomingCall.id, 'User rejected').subscribe({\n      next: () => {\n        console.log('✅ Call rejected successfully');\n        this.toastService.showSuccess('Appel rejeté');\n      },\n      error: error => {\n        console.error('❌ Error rejecting call:', error);\n        this.toastService.showError(\"Erreur lors du rejet de l'appel\");\n      }\n    });\n  }\n  // === MÉTHODE CORRIGÉE POUR TERMINER L'APPEL ===\n  endCall() {\n    console.log('📞 [MessageChat] Ending call...');\n    if (this.activeCall?.id) {\n      this.callService.endCall(this.activeCall.id).subscribe({\n        next: () => {\n          console.log('✅ [MessageChat] Call ended successfully');\n          this.resetCallState();\n        },\n        error: error => {\n          console.error('❌ [MessageChat] Error ending call:', error);\n          this.resetCallState(); // Reset même en cas d'erreur\n        }\n      });\n    } else {\n      console.log('🔄 [MessageChat] No active call to end, resetting state');\n      this.resetCallState();\n    }\n  }\n  startCallTimer() {\n    this.callDuration = 0;\n    this.callTimer = setInterval(() => {\n      this.callDuration++;\n      this.cdr.detectChanges();\n    }, 1000);\n  }\n  resetCallState() {\n    if (this.callTimer) {\n      clearInterval(this.callTimer);\n      this.callTimer = null;\n    }\n    this.isInCall = false;\n    this.callType = null;\n    this.callDuration = 0;\n    this.activeCall = null;\n    this.isCallConnected = false;\n    this.isMuted = false;\n    this.isVideoEnabled = true;\n  }\n  // === CONTRÔLES D'APPEL ===\n  toggleMute() {\n    if (!this.activeCall) return;\n    this.isMuted = !this.isMuted;\n    // Utiliser la méthode toggleMedia du CallService\n    this.callService.toggleMedia(this.activeCall.id, undefined,\n    // video unchanged\n    !this.isMuted // audio state\n    ).subscribe({\n      next: () => {\n        this.toastService.showSuccess(this.isMuted ? 'Micro coupé' : 'Micro activé');\n      },\n      error: error => {\n        console.error('❌ Error toggling mute:', error);\n        // Revert state on error\n        this.isMuted = !this.isMuted;\n        this.toastService.showError('Erreur lors du changement du micro');\n      }\n    });\n  }\n  toggleVideo() {\n    if (!this.activeCall) return;\n    this.isVideoEnabled = !this.isVideoEnabled;\n    // Utiliser la méthode toggleMedia du CallService\n    this.callService.toggleMedia(this.activeCall.id, this.isVideoEnabled,\n    // video state\n    undefined // audio unchanged\n    ).subscribe({\n      next: () => {\n        this.toastService.showSuccess(this.isVideoEnabled ? 'Caméra activée' : 'Caméra désactivée');\n      },\n      error: error => {\n        console.error('❌ Error toggling video:', error);\n        // Revert state on error\n        this.isVideoEnabled = !this.isVideoEnabled;\n        this.toastService.showError('Erreur lors du changement de la caméra');\n      }\n    });\n  }\n  formatCallDuration(duration) {\n    const hours = Math.floor(duration / 3600);\n    const minutes = Math.floor(duration % 3600 / 60);\n    const seconds = duration % 60;\n    if (hours > 0) {\n      return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;\n    }\n    return `${minutes}:${seconds.toString().padStart(2, '0')}`;\n  }\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // trackByMessageId, isGroupConversation - Ces méthodes sont déjà définies plus loin dans le fichier\n  startVoiceRecording() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      console.log('🎤 [Voice] Starting voice recording...');\n      try {\n        // Vérifier le support du navigateur\n        if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {\n          throw new Error(\"Votre navigateur ne supporte pas l'enregistrement audio\");\n        }\n        // Vérifier si MediaRecorder est supporté\n        if (!window.MediaRecorder) {\n          throw new Error(\"MediaRecorder n'est pas supporté par votre navigateur\");\n        }\n        console.log('🎤 [Voice] Requesting microphone access...');\n        // Demander l'accès au microphone avec des contraintes optimisées\n        const stream = yield navigator.mediaDevices.getUserMedia({\n          audio: {\n            echoCancellation: true,\n            noiseSuppression: true,\n            autoGainControl: true,\n            sampleRate: 44100,\n            channelCount: 1\n          }\n        });\n        console.log('🎤 [Voice] Microphone access granted');\n        // Vérifier les types MIME supportés\n        let mimeType = 'audio/webm;codecs=opus';\n        if (!MediaRecorder.isTypeSupported(mimeType)) {\n          mimeType = 'audio/webm';\n          if (!MediaRecorder.isTypeSupported(mimeType)) {\n            mimeType = 'audio/mp4';\n            if (!MediaRecorder.isTypeSupported(mimeType)) {\n              mimeType = ''; // Laisser le navigateur choisir\n            }\n          }\n        }\n\n        console.log('🎤 [Voice] Using MIME type:', mimeType);\n        // Créer le MediaRecorder\n        _this.mediaRecorder = new MediaRecorder(stream, {\n          mimeType: mimeType || undefined\n        });\n        // Initialiser les variables\n        _this.audioChunks = [];\n        _this.isRecordingVoice = true;\n        _this.voiceRecordingDuration = 0;\n        _this.voiceRecordingState = 'recording';\n        console.log('🎤 [Voice] MediaRecorder created, starting timer...');\n        // Démarrer le timer\n        _this.recordingTimer = setInterval(() => {\n          _this.voiceRecordingDuration++;\n          // Animer les waves\n          _this.animateVoiceWaves();\n          _this.cdr.detectChanges();\n        }, 1000);\n        // Gérer les événements du MediaRecorder\n        _this.mediaRecorder.ondataavailable = event => {\n          console.log('🎤 [Voice] Data available:', event.data.size, 'bytes');\n          if (event.data.size > 0) {\n            _this.audioChunks.push(event.data);\n          }\n        };\n        _this.mediaRecorder.onstop = () => {\n          console.log('🎤 [Voice] MediaRecorder stopped, processing audio...');\n          _this.processRecordedAudio();\n        };\n        _this.mediaRecorder.onerror = event => {\n          console.error('🎤 [Voice] MediaRecorder error:', event.error);\n          _this.toastService.showError(\"Erreur lors de l'enregistrement\");\n          _this.cancelVoiceRecording();\n        };\n        // Démarrer l'enregistrement\n        _this.mediaRecorder.start(100); // Collecter les données toutes les 100ms\n        console.log('🎤 [Voice] Recording started successfully');\n        _this.toastService.showSuccess('🎤 Enregistrement vocal démarré');\n      } catch (error) {\n        console.error('🎤 [Voice] Error starting recording:', error);\n        let errorMessage = \"Impossible de démarrer l'enregistrement vocal\";\n        if (error.name === 'NotAllowedError') {\n          errorMessage = \"Accès au microphone refusé. Veuillez autoriser l'accès dans les paramètres de votre navigateur.\";\n        } else if (error.name === 'NotFoundError') {\n          errorMessage = 'Aucun microphone détecté. Veuillez connecter un microphone.';\n        } else if (error.name === 'NotSupportedError') {\n          errorMessage = \"Votre navigateur ne supporte pas l'enregistrement audio.\";\n        } else if (error.message) {\n          errorMessage = error.message;\n        }\n        _this.toastService.showError(errorMessage);\n        _this.cancelVoiceRecording();\n      }\n    })();\n  }\n  stopVoiceRecording() {\n    if (this.mediaRecorder && this.mediaRecorder.state === 'recording') {\n      this.mediaRecorder.stop();\n      this.mediaRecorder.stream.getTracks().forEach(track => track.stop());\n    }\n    if (this.recordingTimer) {\n      clearInterval(this.recordingTimer);\n      this.recordingTimer = null;\n    }\n    this.isRecordingVoice = false;\n    this.voiceRecordingState = 'processing';\n  }\n  cancelVoiceRecording() {\n    if (this.mediaRecorder) {\n      if (this.mediaRecorder.state === 'recording') {\n        this.mediaRecorder.stop();\n      }\n      this.mediaRecorder.stream.getTracks().forEach(track => track.stop());\n      this.mediaRecorder = null;\n    }\n    if (this.recordingTimer) {\n      clearInterval(this.recordingTimer);\n      this.recordingTimer = null;\n    }\n    this.isRecordingVoice = false;\n    this.voiceRecordingDuration = 0;\n    this.voiceRecordingState = 'idle';\n    this.audioChunks = [];\n  }\n  processRecordedAudio() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      console.log('🎤 [Voice] Processing recorded audio...');\n      try {\n        // Vérifier qu'on a des données audio\n        if (_this2.audioChunks.length === 0) {\n          console.error('🎤 [Voice] No audio chunks available');\n          _this2.toastService.showError('Aucun audio enregistré');\n          _this2.cancelVoiceRecording();\n          return;\n        }\n        console.log('🎤 [Voice] Audio chunks:', _this2.audioChunks.length, 'Duration:', _this2.voiceRecordingDuration);\n        // Vérifier la durée minimale\n        if (_this2.voiceRecordingDuration < 1) {\n          console.error('🎤 [Voice] Recording too short:', _this2.voiceRecordingDuration);\n          _this2.toastService.showError('Enregistrement trop court (minimum 1 seconde)');\n          _this2.cancelVoiceRecording();\n          return;\n        }\n        // Déterminer le type MIME du blob\n        let mimeType = 'audio/webm;codecs=opus';\n        if (_this2.mediaRecorder?.mimeType) {\n          mimeType = _this2.mediaRecorder.mimeType;\n        }\n        console.log('🎤 [Voice] Creating audio blob with MIME type:', mimeType);\n        // Créer le blob audio\n        const audioBlob = new Blob(_this2.audioChunks, {\n          type: mimeType\n        });\n        console.log('🎤 [Voice] Audio blob created:', {\n          size: audioBlob.size,\n          type: audioBlob.type\n        });\n        // Déterminer l'extension du fichier\n        let extension = '.webm';\n        if (mimeType.includes('mp4')) {\n          extension = '.mp4';\n        } else if (mimeType.includes('wav')) {\n          extension = '.wav';\n        } else if (mimeType.includes('ogg')) {\n          extension = '.ogg';\n        }\n        // Créer le fichier\n        const audioFile = new File([audioBlob], `voice_${Date.now()}${extension}`, {\n          type: mimeType\n        });\n        console.log('🎤 [Voice] Audio file created:', {\n          name: audioFile.name,\n          size: audioFile.size,\n          type: audioFile.type\n        });\n        // Envoyer le message vocal\n        _this2.voiceRecordingState = 'processing';\n        yield _this2.sendVoiceMessage(audioFile);\n        console.log('🎤 [Voice] Voice message sent successfully');\n        _this2.toastService.showSuccess('🎤 Message vocal envoyé');\n      } catch (error) {\n        console.error('🎤 [Voice] Error processing audio:', error);\n        _this2.toastService.showError(\"Erreur lors de l'envoi du message vocal: \" + (error.message || 'Erreur inconnue'));\n      } finally {\n        // Nettoyer l'état\n        _this2.voiceRecordingState = 'idle';\n        _this2.voiceRecordingDuration = 0;\n        _this2.audioChunks = [];\n        _this2.isRecordingVoice = false;\n        console.log('🎤 [Voice] Audio processing completed, state reset');\n      }\n    })();\n  }\n  sendVoiceMessage(audioFile) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      const receiverId = _this3.otherParticipant?.id || _this3.otherParticipant?._id;\n      if (!receiverId) {\n        throw new Error('Destinataire introuvable');\n      }\n      return new Promise((resolve, reject) => {\n        _this3.MessageService.sendMessage(receiverId, '', audioFile, 'AUDIO', _this3.conversation.id).subscribe({\n          next: message => {\n            _this3.messages.push(message);\n            _this3.scrollToBottom();\n            resolve();\n          },\n          error: error => {\n            console.error(\"Erreur lors de l'envoi du message vocal:\", error);\n            reject(error);\n          }\n        });\n      });\n    })();\n  }\n  formatRecordingDuration(duration) {\n    const minutes = Math.floor(duration / 60);\n    const seconds = duration % 60;\n    return `${minutes}:${seconds.toString().padStart(2, '0')}`;\n  }\n  // === MÉTHODES D'ENREGISTREMENT VOCAL AMÉLIORÉES ===\n  onRecordStart(event) {\n    event.preventDefault();\n    console.log('🎤 [Voice] Record start triggered');\n    console.log('🎤 [Voice] Current state:', {\n      isRecordingVoice: this.isRecordingVoice,\n      voiceRecordingState: this.voiceRecordingState,\n      voiceRecordingDuration: this.voiceRecordingDuration,\n      mediaRecorder: !!this.mediaRecorder\n    });\n    // Vérifier si on peut enregistrer\n    if (this.voiceRecordingState === 'processing') {\n      console.log('🎤 [Voice] Already processing, ignoring start');\n      this.toastService.showWarning('Traitement en cours...');\n      return;\n    }\n    if (this.isRecordingVoice) {\n      console.log('🎤 [Voice] Already recording, ignoring start');\n      this.toastService.showWarning('Enregistrement déjà en cours...');\n      return;\n    }\n    // Afficher un message de début\n    this.toastService.showInfo(\"🎤 Démarrage de l'enregistrement vocal...\");\n    // Démarrer l'enregistrement\n    this.startVoiceRecording().catch(error => {\n      console.error('🎤 [Voice] Failed to start recording:', error);\n      this.toastService.showError(\"Impossible de démarrer l'enregistrement vocal: \" + (error.message || 'Erreur inconnue'));\n    });\n  }\n  onRecordEnd(event) {\n    event.preventDefault();\n    console.log('🎤 [Voice] Record end triggered');\n    if (!this.isRecordingVoice) {\n      console.log('🎤 [Voice] Not recording, ignoring end');\n      return;\n    }\n    // Arrêter l'enregistrement et envoyer\n    this.stopVoiceRecording();\n  }\n  onRecordCancel(event) {\n    event.preventDefault();\n    console.log('🎤 [Voice] Record cancel triggered');\n    if (!this.isRecordingVoice) {\n      console.log('🎤 [Voice] Not recording, ignoring cancel');\n      return;\n    }\n    // Annuler l'enregistrement\n    this.cancelVoiceRecording();\n  }\n  getRecordingFormat() {\n    if (this.mediaRecorder?.mimeType) {\n      if (this.mediaRecorder.mimeType.includes('webm')) return 'WebM';\n      if (this.mediaRecorder.mimeType.includes('mp4')) return 'MP4';\n      if (this.mediaRecorder.mimeType.includes('wav')) return 'WAV';\n      if (this.mediaRecorder.mimeType.includes('ogg')) return 'OGG';\n    }\n    return 'Auto';\n  }\n  // === ANIMATION DES WAVES VOCALES ===\n  animateVoiceWaves() {\n    // Animer les waves pendant l'enregistrement\n    this.voiceWaves = this.voiceWaves.map(() => {\n      return Math.floor(Math.random() * 20) + 4; // Hauteur entre 4 et 24px\n    });\n  }\n\n  onFileSelected(event) {\n    console.log('📁 [Upload] File selection triggered');\n    const files = event.target.files;\n    if (!files || files.length === 0) {\n      console.log('📁 [Upload] No files selected');\n      return;\n    }\n    console.log(`📁 [Upload] ${files.length} file(s) selected:`, files);\n    for (let file of files) {\n      console.log(`📁 [Upload] Processing file: ${file.name}, size: ${file.size}, type: ${file.type}`);\n      this.uploadFile(file);\n    }\n  }\n  uploadFile(file) {\n    console.log(`📁 [Upload] Starting upload for file: ${file.name}`);\n    const receiverId = this.otherParticipant?.id || this.otherParticipant?._id;\n    if (!receiverId) {\n      console.error('📁 [Upload] No receiver ID found');\n      this.toastService.showError('Destinataire introuvable');\n      return;\n    }\n    console.log(`📁 [Upload] Receiver ID: ${receiverId}`);\n    // Vérifier la taille du fichier (max 50MB)\n    const maxSize = 50 * 1024 * 1024; // 50MB\n    if (file.size > maxSize) {\n      console.error(`📁 [Upload] File too large: ${file.size} bytes`);\n      this.toastService.showError('Fichier trop volumineux (max 50MB)');\n      return;\n    }\n    // 🖼️ Compression d'image si nécessaire\n    if (file.type.startsWith('image/') && file.size > 1024 * 1024) {\n      // > 1MB\n      console.log('🖼️ [Compression] Compressing image:', file.name, 'Original size:', file.size);\n      this.compressImage(file).then(compressedFile => {\n        console.log('🖼️ [Compression] ✅ Image compressed successfully. New size:', compressedFile.size);\n        this.sendFileToServer(compressedFile, receiverId);\n      }).catch(error => {\n        console.error('🖼️ [Compression] ❌ Error compressing image:', error);\n        // Envoyer le fichier original en cas d'erreur\n        this.sendFileToServer(file, receiverId);\n      });\n      return;\n    }\n    // Envoyer le fichier sans compression\n    this.sendFileToServer(file, receiverId);\n  }\n  sendFileToServer(file, receiverId) {\n    const messageType = this.getFileMessageType(file);\n    console.log(`📁 [Upload] Message type determined: ${messageType}`);\n    console.log(`📁 [Upload] Conversation ID: ${this.conversation.id}`);\n    this.isSendingMessage = true;\n    this.isUploading = true;\n    this.uploadProgress = 0;\n    console.log('📁 [Upload] Calling MessageService.sendMessage...');\n    // Simuler la progression d'upload\n    const progressInterval = setInterval(() => {\n      this.uploadProgress += Math.random() * 15;\n      if (this.uploadProgress >= 90) {\n        clearInterval(progressInterval);\n      }\n      this.cdr.detectChanges();\n    }, 300);\n    this.MessageService.sendMessage(receiverId, '', file, messageType, this.conversation.id).subscribe({\n      next: message => {\n        console.log('📁 [Upload] ✅ File sent successfully:', message);\n        console.log('📁 [Debug] Sent message structure:', {\n          id: message.id,\n          type: message.type,\n          attachments: message.attachments,\n          hasImage: this.hasImage(message),\n          hasFile: this.hasFile(message),\n          imageUrl: this.getImageUrl(message)\n        });\n        clearInterval(progressInterval);\n        this.uploadProgress = 100;\n        setTimeout(() => {\n          this.messages.push(message);\n          this.scrollToBottom();\n          this.toastService.showSuccess('Fichier envoyé avec succès');\n          this.resetUploadState();\n        }, 500);\n      },\n      error: error => {\n        console.error('📁 [Upload] ❌ Error sending file:', error);\n        clearInterval(progressInterval);\n        this.toastService.showError(\"Erreur lors de l'envoi du fichier\");\n        this.resetUploadState();\n      }\n    });\n  }\n  getFileMessageType(file) {\n    if (file.type.startsWith('image/')) return 'IMAGE';\n    if (file.type.startsWith('video/')) return 'VIDEO';\n    if (file.type.startsWith('audio/')) return 'AUDIO';\n    return 'FILE';\n  }\n  getFileAcceptTypes() {\n    return '*/*';\n  }\n  resetUploadState() {\n    this.isSendingMessage = false;\n    this.isUploading = false;\n    this.uploadProgress = 0;\n  }\n  // === DRAG & DROP ===\n  onDragOver(event) {\n    event.preventDefault();\n    event.stopPropagation();\n    this.isDragOver = true;\n  }\n  onDragLeave(event) {\n    event.preventDefault();\n    event.stopPropagation();\n    // Vérifier si on quitte vraiment la zone (pas un enfant)\n    const rect = event.currentTarget.getBoundingClientRect();\n    const x = event.clientX;\n    const y = event.clientY;\n    if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {\n      this.isDragOver = false;\n    }\n  }\n  onDrop(event) {\n    event.preventDefault();\n    event.stopPropagation();\n    this.isDragOver = false;\n    const files = event.dataTransfer?.files;\n    if (files && files.length > 0) {\n      console.log('📁 [Drag&Drop] Files dropped:', files.length);\n      // Traiter chaque fichier\n      Array.from(files).forEach(file => {\n        console.log('📁 [Drag&Drop] Processing file:', file.name, file.type, file.size);\n        this.uploadFile(file);\n      });\n      this.toastService.showSuccess(`${files.length} fichier(s) en cours d'envoi`);\n    }\n  }\n  // === COMPRESSION D'IMAGES ===\n  compressImage(file, quality = 0.8) {\n    return new Promise((resolve, reject) => {\n      const canvas = document.createElement('canvas');\n      const ctx = canvas.getContext('2d');\n      const img = new Image();\n      img.onload = () => {\n        // Calculer les nouvelles dimensions (max 1920x1080)\n        const maxWidth = 1920;\n        const maxHeight = 1080;\n        let {\n          width,\n          height\n        } = img;\n        if (width > maxWidth || height > maxHeight) {\n          const ratio = Math.min(maxWidth / width, maxHeight / height);\n          width *= ratio;\n          height *= ratio;\n        }\n        canvas.width = width;\n        canvas.height = height;\n        // Dessiner l'image redimensionnée\n        ctx?.drawImage(img, 0, 0, width, height);\n        // Convertir en blob avec compression\n        canvas.toBlob(blob => {\n          if (blob) {\n            const compressedFile = new File([blob], file.name, {\n              type: file.type,\n              lastModified: Date.now()\n            });\n            resolve(compressedFile);\n          } else {\n            reject(new Error('Failed to compress image'));\n          }\n        }, file.type, quality);\n      };\n      img.onerror = () => reject(new Error('Failed to load image'));\n      img.src = URL.createObjectURL(file);\n    });\n  }\n  // === MÉTHODES DE FORMATAGE SUPPLÉMENTAIRES ===\n  handleTypingIndicator() {\n    if (!this.isTyping) {\n      this.isTyping = true;\n      // Envoyer l'indicateur de frappe à l'autre utilisateur\n      this.sendTypingIndicator(true);\n    }\n    // Reset le timer\n    if (this.typingTimeout) {\n      clearTimeout(this.typingTimeout);\n    }\n    this.typingTimeout = setTimeout(() => {\n      this.isTyping = false;\n      // Arrêter l'indicateur de frappe\n      this.sendTypingIndicator(false);\n    }, 2000);\n  }\n  sendTypingIndicator(isTyping) {\n    // Envoyer l'indicateur de frappe via WebSocket/GraphQL\n    const receiverId = this.otherParticipant?.id || this.otherParticipant?._id;\n    if (receiverId && this.conversation?.id) {\n      console.log(`📝 Sending typing indicator: ${isTyping} to user ${receiverId}`);\n      // TODO: Implémenter l'envoi via WebSocket/GraphQL subscription\n      // this.MessageService.sendTypingIndicator(this.conversation.id, receiverId, isTyping);\n    }\n  }\n  // === MÉTHODES POUR L'INTERFACE D'APPEL ===\n  onCallAccepted(call) {\n    console.log('🔄 Call accepted from interface:', call);\n    this.activeCall = call;\n    this.isInCall = true;\n    this.isCallConnected = true;\n    this.startCallTimer();\n    this.toastService.showSuccess('Appel accepté');\n  }\n  onCallRejected() {\n    console.log('🔄 Call rejected from interface');\n    this.endCall();\n    this.toastService.showInfo('Appel rejeté');\n  }\n  // === MÉTHODES POUR LA LECTURE DES MESSAGES VOCAUX ===\n  playVoiceMessage(message) {\n    console.log('🎵 [Voice] Playing voice message:', message.id);\n    this.toggleVoicePlayback(message);\n  }\n  isVoicePlaying(messageId) {\n    return this.playingMessageId === messageId;\n  }\n  toggleVoicePlayback(message) {\n    const messageId = message.id;\n    const audioUrl = this.getVoiceUrl(message);\n    if (!audioUrl) {\n      console.error('🎵 [Voice] No audio URL found for message:', messageId);\n      this.toastService.showError('Fichier audio introuvable');\n      return;\n    }\n    // Si c'est déjà en cours de lecture, arrêter\n    if (this.isVoicePlaying(messageId)) {\n      this.stopVoicePlayback();\n      return;\n    }\n    // Arrêter toute autre lecture en cours\n    this.stopVoicePlayback();\n    // Démarrer la nouvelle lecture\n    this.startVoicePlayback(message, audioUrl);\n  }\n  startVoicePlayback(message, audioUrl) {\n    const messageId = message.id;\n    try {\n      console.log('🎵 [Voice] Starting playback for:', messageId, 'URL:', audioUrl);\n      this.currentAudio = new Audio(audioUrl);\n      this.playingMessageId = messageId;\n      // Initialiser les valeurs par défaut avec la nouvelle structure\n      const currentData = this.getVoicePlaybackData(messageId);\n      this.setVoicePlaybackData(messageId, {\n        progress: 0,\n        currentTime: 0,\n        speed: currentData.speed || 1,\n        duration: currentData.duration || 0\n      });\n      // Configurer la vitesse de lecture\n      this.currentAudio.playbackRate = currentData.speed || 1;\n      // Événements audio\n      this.currentAudio.addEventListener('loadedmetadata', () => {\n        if (this.currentAudio) {\n          this.setVoicePlaybackData(messageId, {\n            duration: this.currentAudio.duration\n          });\n          console.log('🎵 [Voice] Audio loaded, duration:', this.currentAudio.duration);\n        }\n      });\n      this.currentAudio.addEventListener('timeupdate', () => {\n        if (this.currentAudio && this.playingMessageId === messageId) {\n          const currentTime = this.currentAudio.currentTime;\n          const progress = currentTime / this.currentAudio.duration * 100;\n          this.setVoicePlaybackData(messageId, {\n            currentTime,\n            progress\n          });\n          this.cdr.detectChanges();\n        }\n      });\n      this.currentAudio.addEventListener('ended', () => {\n        console.log('🎵 [Voice] Playback ended for:', messageId);\n        this.stopVoicePlayback();\n      });\n      this.currentAudio.addEventListener('error', error => {\n        console.error('🎵 [Voice] Audio error:', error);\n        this.toastService.showError('Erreur lors de la lecture audio');\n        this.stopVoicePlayback();\n      });\n      // Démarrer la lecture\n      this.currentAudio.play().then(() => {\n        console.log('🎵 [Voice] Playback started successfully');\n        this.toastService.showSuccess('🎵 Lecture du message vocal');\n      }).catch(error => {\n        console.error('🎵 [Voice] Error starting playback:', error);\n        this.toastService.showError('Impossible de lire le message vocal');\n        this.stopVoicePlayback();\n      });\n    } catch (error) {\n      console.error('🎵 [Voice] Error creating audio:', error);\n      this.toastService.showError('Erreur lors de la lecture audio');\n      this.stopVoicePlayback();\n    }\n  }\n  stopVoicePlayback() {\n    if (this.currentAudio) {\n      console.log('🎵 [Voice] Stopping playback for:', this.playingMessageId);\n      this.currentAudio.pause();\n      this.currentAudio.currentTime = 0;\n      this.currentAudio = null;\n    }\n    this.playingMessageId = null;\n    this.cdr.detectChanges();\n  }\n  getVoiceUrl(message) {\n    // Vérifier les propriétés directes d'audio\n    if (message.voiceUrl) return message.voiceUrl;\n    if (message.audioUrl) return message.audioUrl;\n    if (message.voice) return message.voice;\n    // Vérifier les attachments audio\n    const audioAttachment = message.attachments?.find(att => att.type?.startsWith('audio/') || att.type === 'AUDIO');\n    if (audioAttachment) {\n      return audioAttachment.url || audioAttachment.path || '';\n    }\n    return '';\n  }\n  getVoiceWaves(message) {\n    // Générer des waves basées sur l'ID du message pour la cohérence\n    const messageId = message.id || '';\n    const seed = messageId.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);\n    const waves = [];\n    for (let i = 0; i < 16; i++) {\n      const height = 4 + (seed + i * 7) % 20;\n      waves.push(height);\n    }\n    return waves;\n  }\n  getVoiceProgress(message) {\n    const data = this.getVoicePlaybackData(message.id);\n    const totalWaves = 16;\n    return Math.floor(data.progress / 100 * totalWaves);\n  }\n  getVoiceCurrentTime(message) {\n    const data = this.getVoicePlaybackData(message.id);\n    return this.formatAudioTime(data.currentTime);\n  }\n  getVoiceDuration(message) {\n    const data = this.getVoicePlaybackData(message.id);\n    const duration = data.duration || message.metadata?.duration || 0;\n    if (typeof duration === 'string') {\n      return duration; // Déjà formaté\n    }\n\n    return this.formatAudioTime(duration);\n  }\n  formatAudioTime(seconds) {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = Math.floor(seconds % 60);\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  }\n  seekVoiceMessage(message, waveIndex) {\n    const messageId = message.id;\n    if (!this.currentAudio || this.playingMessageId !== messageId) {\n      return;\n    }\n    const totalWaves = 16;\n    const seekPercentage = waveIndex / totalWaves * 100;\n    const seekTime = seekPercentage / 100 * this.currentAudio.duration;\n    this.currentAudio.currentTime = seekTime;\n    console.log('🎵 [Voice] Seeking to:', seekTime, 'seconds');\n  }\n  toggleVoiceSpeed(message) {\n    const messageId = message.id;\n    const data = this.getVoicePlaybackData(messageId);\n    // Cycle entre 1x, 1.5x, 2x\n    const newSpeed = data.speed === 1 ? 1.5 : data.speed === 1.5 ? 2 : 1;\n    this.setVoicePlaybackData(messageId, {\n      speed: newSpeed\n    });\n    if (this.currentAudio && this.playingMessageId === messageId) {\n      this.currentAudio.playbackRate = newSpeed;\n    }\n    this.toastService.showSuccess(`Vitesse: ${newSpeed}x`);\n  }\n  // === MÉTHODES MANQUANTES POUR LE TEMPLATE ===\n  changeVoiceSpeed(message) {\n    this.toggleVoiceSpeed(message);\n  }\n  getVoiceSpeed(message) {\n    const data = this.getVoicePlaybackData(message.id);\n    return data.speed || 1;\n  }\n  ngOnDestroy() {\n    this.subscriptions.unsubscribe();\n    // Nettoyer les timers\n    if (this.callTimer) {\n      clearInterval(this.callTimer);\n    }\n    if (this.recordingTimer) {\n      clearInterval(this.recordingTimer);\n    }\n    if (this.typingTimeout) {\n      clearTimeout(this.typingTimeout);\n    }\n    // Nettoyer les ressources audio\n    if (this.mediaRecorder) {\n      if (this.mediaRecorder.state === 'recording') {\n        this.mediaRecorder.stop();\n      }\n      this.mediaRecorder.stream?.getTracks().forEach(track => track.stop());\n    }\n    // Nettoyer la lecture audio\n    this.stopVoicePlayback();\n  }\n  static {\n    this.ɵfac = function MessageChatComponent_Factory(t) {\n      return new (t || MessageChatComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i4.CallService), i0.ɵɵdirectiveInject(i5.ToastService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: MessageChatComponent,\n      selectors: [[\"app-message-chat\"]],\n      viewQuery: function MessageChatComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n          i0.ɵɵviewQuery(_c2, 5);\n          i0.ɵɵviewQuery(_c3, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.messagesContainer = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.fileInput = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.localVideo = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.remoteVideo = _t.first);\n        }\n      },\n      decls: 55,\n      vars: 54,\n      consts: [[\"autoplay\", \"\", \"muted\", \"\", \"playsinline\", \"\", 2, \"display\", \"none\"], [\"localVideo\", \"\"], [\"autoplay\", \"\", \"playsinline\", \"\", 2, \"display\", \"none\"], [\"remoteVideo\", \"\"], [2, \"display\", \"flex\", \"flex-direction\", \"column\", \"height\", \"100vh\", \"background\", \"linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)\", \"color\", \"#1f2937\", \"font-family\", \"'Inter', -apple-system, BlinkMacSystemFont, sans-serif\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"padding\", \"12px 16px\", \"background\", \"#ffffff\", \"border-bottom\", \"1px solid #e5e7eb\", \"box-shadow\", \"0 1px 3px rgba(0, 0, 0, 0.1)\", \"position\", \"relative\", \"z-index\", \"10\"], [\"onmouseover\", \"this.style.background='#f3f4f6'; this.style.transform='scale(1.05)'\", \"onmouseout\", \"this.style.background='transparent'; this.style.transform='scale(1)'\", \"title\", \"Retour aux conversations\", 2, \"padding\", \"10px\", \"margin-right\", \"12px\", \"border-radius\", \"50%\", \"border\", \"none\", \"background\", \"transparent\", \"cursor\", \"pointer\", \"transition\", \"all 0.2s ease\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"center\", \"min-width\", \"40px\", \"min-height\", \"40px\", 3, \"click\"], [1, \"fas\", \"fa-arrow-left\", 2, \"color\", \"#374151\", \"font-size\", \"18px\", \"font-weight\", \"bold\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"flex\", \"1\", \"min-width\", \"0\"], [2, \"position\", \"relative\", \"margin-right\", \"12px\"], [\"onmouseover\", \"this.style.transform='scale(1.05)'\", \"onmouseout\", \"this.style.transform='scale(1)'\", \"title\", \"Voir le profil\", 2, \"width\", \"40px\", \"height\", \"40px\", \"border-radius\", \"50%\", \"object-fit\", \"cover\", \"border\", \"2px solid transparent\", \"cursor\", \"pointer\", \"transition\", \"transform 0.2s ease\", 3, \"src\", \"alt\", \"click\"], [\"style\", \"\\n            position: absolute;\\n            bottom: 0;\\n            right: 0;\\n            width: 12px;\\n            height: 12px;\\n            background: #10b981;\\n            border: 2px solid transparent;\\n            border-radius: 50%;\\n            animation: pulse 2s infinite;\\n          \", 4, \"ngIf\"], [2, \"flex\", \"1\", \"min-width\", \"0\"], [2, \"font-weight\", \"600\", \"color\", \"#111827\", \"margin\", \"0\", \"font-size\", \"16px\", \"white-space\", \"nowrap\", \"overflow\", \"hidden\", \"text-overflow\", \"ellipsis\"], [2, \"font-size\", \"14px\", \"color\", \"#6b7280\", \"margin-top\", \"2px\"], [\"style\", \"display: flex; align-items: center; gap: 4px; color: #10b981\", 4, \"ngIf\"], [4, \"ngIf\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"8px\"], [\"title\", \"Appel vid\\u00E9o\", \"onmouseover\", \"this.style.transform='scale(1.1)'; this.style.boxShadow='0 6px 20px rgba(59, 130, 246, 0.4)'\", \"onmouseout\", \"this.style.transform='scale(1)'; this.style.boxShadow='0 4px 12px rgba(59, 130, 246, 0.3)'\", 2, \"padding\", \"10px\", \"border-radius\", \"50%\", \"border\", \"none\", \"background\", \"linear-gradient(135deg, #3b82f6, #1d4ed8)\", \"color\", \"white\", \"cursor\", \"pointer\", \"transition\", \"all 0.3s\", \"box-shadow\", \"0 4px 12px rgba(59, 130, 246, 0.3)\", \"width\", \"40px\", \"height\", \"40px\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"center\", 3, \"click\"], [1, \"fas\", \"fa-video\", 2, \"font-size\", \"14px\"], [\"title\", \"Appel vocal\", \"onmouseover\", \"this.style.transform='scale(1.1)'; this.style.boxShadow='0 6px 20px rgba(16, 185, 129, 0.4)'\", \"onmouseout\", \"this.style.transform='scale(1)'; this.style.boxShadow='0 4px 12px rgba(16, 185, 129, 0.3)'\", 2, \"padding\", \"10px\", \"border-radius\", \"50%\", \"border\", \"none\", \"background\", \"linear-gradient(135deg, #10b981, #047857)\", \"color\", \"white\", \"cursor\", \"pointer\", \"transition\", \"all 0.3s\", \"box-shadow\", \"0 4px 12px rgba(16, 185, 129, 0.3)\", \"width\", \"40px\", \"height\", \"40px\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"center\", 3, \"click\"], [1, \"fas\", \"fa-phone\", 2, \"font-size\", \"14px\"], [\"title\", \"Rechercher\", 2, \"padding\", \"8px\", \"border-radius\", \"50%\", \"border\", \"none\", \"background\", \"transparent\", \"color\", \"#6b7280\", \"cursor\", \"pointer\", \"transition\", \"all 0.2s\", 3, \"click\"], [1, \"fas\", \"fa-search\"], [\"title\", \"Menu\", 2, \"padding\", \"8px\", \"border-radius\", \"50%\", \"border\", \"none\", \"background\", \"transparent\", \"color\", \"#6b7280\", \"cursor\", \"pointer\", \"transition\", \"all 0.2s\", \"position\", \"relative\", 3, \"click\"], [1, \"fas\", \"fa-ellipsis-v\"], [\"style\", \"\\n        position: absolute;\\n        top: 64px;\\n        right: 16px;\\n        background: #ffffff;\\n        border-radius: 16px;\\n        box-shadow: 0 20px 25px rgba(0, 0, 0, 0.1);\\n        border: 1px solid #e5e7eb;\\n        z-index: 50;\\n        min-width: 192px;\\n      \", 4, \"ngIf\"], [2, \"flex\", \"1\", \"overflow-y\", \"auto\", \"padding\", \"16px\", \"position\", \"relative\", 3, \"scroll\", \"dragover\", \"dragleave\", \"drop\"], [\"messagesContainer\", \"\"], [\"style\", \"\\n        position: absolute;\\n        top: 0;\\n        left: 0;\\n        right: 0;\\n        bottom: 0;\\n        background: rgba(34, 197, 94, 0.2);\\n        border: 2px dashed transparent;\\n        border-radius: 8px;\\n        display: flex;\\n        align-items: center;\\n        justify-content: center;\\n        z-index: 50;\\n        backdrop-filter: blur(2px);\\n        animation: pulse 2s infinite;\\n      \", 4, \"ngIf\"], [\"style\", \"\\n        display: flex;\\n        flex-direction: column;\\n        align-items: center;\\n        justify-content: center;\\n        padding: 32px 0;\\n      \", 4, \"ngIf\"], [\"style\", \"\\n        display: flex;\\n        flex-direction: column;\\n        align-items: center;\\n        justify-content: center;\\n        padding: 64px 0;\\n      \", 4, \"ngIf\"], [\"style\", \"display: flex; flex-direction: column; gap: 8px\", 4, \"ngIf\"], [2, \"background\", \"#ffffff\", \"border-top\", \"1px solid #e5e7eb\", \"padding\", \"16px\"], [2, \"display\", \"flex\", \"align-items\", \"end\", \"gap\", \"12px\", 3, \"formGroup\", \"ngSubmit\"], [2, \"display\", \"flex\", \"gap\", \"8px\"], [\"type\", \"button\", \"title\", \"\\u00C9mojis\", \"onmouseover\", \"this.style.background=this.style.background === 'transparent' ? '#f3f4f6' : this.style.background\", \"onmouseout\", \"this.style.background=this.style.background === '#f3f4f6' ? 'transparent' : this.style.background\", 2, \"padding\", \"8px\", \"border-radius\", \"50%\", \"border\", \"none\", \"background\", \"transparent\", \"color\", \"#6b7280\", \"cursor\", \"pointer\", \"transition\", \"all 0.2s\", 3, \"click\"], [1, \"fas\", \"fa-smile\"], [\"type\", \"button\", \"title\", \"Pi\\u00E8ces jointes\", \"onmouseover\", \"this.style.background=this.style.background === 'transparent' ? '#f3f4f6' : this.style.background\", \"onmouseout\", \"this.style.background=this.style.background === '#f3f4f6' ? 'transparent' : this.style.background\", 2, \"padding\", \"8px\", \"border-radius\", \"50%\", \"border\", \"none\", \"background\", \"transparent\", \"color\", \"#6b7280\", \"cursor\", \"pointer\", \"transition\", \"all 0.2s\", 3, \"click\"], [1, \"fas\", \"fa-paperclip\"], [\"type\", \"button\", \"title\", \"Maintenir pour enregistrer un message vocal\", \"onmouseover\", \"if(!this.style.background || this.style.background === 'transparent') this.style.background='#f3f4f6'\", \"onmouseout\", \"if(this.style.background === '#f3f4f6') this.style.background='transparent'\", 2, \"padding\", \"8px\", \"border-radius\", \"50%\", \"border\", \"none\", \"background\", \"transparent\", \"color\", \"#6b7280\", \"cursor\", \"pointer\", \"transition\", \"all 0.2s\", \"position\", \"relative\", 3, \"mousedown\", \"mouseup\", \"mouseleave\", \"touchstart\", \"touchend\", \"touchcancel\"], [\"style\", \"\\n              position: absolute;\\n              top: -2px;\\n              right: -2px;\\n              width: 8px;\\n              height: 8px;\\n              background: #ef4444;\\n              border-radius: 50%;\\n              animation: ping 1s infinite;\\n            \", 4, \"ngIf\"], [2, \"flex\", \"1\", \"position\", \"relative\"], [\"formControlName\", \"content\", \"placeholder\", \"Tapez votre message...\", 2, \"width\", \"100%\", \"min-height\", \"44px\", \"max-height\", \"120px\", \"padding\", \"12px 16px\", \"border\", \"1px solid #e5e7eb\", \"border-radius\", \"22px\", \"resize\", \"none\", \"outline\", \"none\", \"font-family\", \"inherit\", \"font-size\", \"14px\", \"line-height\", \"1.4\", \"background\", \"#ffffff\", \"color\", \"#111827\", \"transition\", \"all 0.2s\", 3, \"disabled\", \"keydown\", \"input\", \"focus\"], [\"type\", \"submit\", \"title\", \"Envoyer\", \"onmouseover\", \"if(!this.disabled) this.style.background='#2563eb'\", \"onmouseout\", \"if(!this.disabled) this.style.background='#3b82f6'\", 2, \"padding\", \"12px\", \"border-radius\", \"50%\", \"border\", \"none\", \"background\", \"#3b82f6\", \"color\", \"#ffffff\", \"cursor\", \"pointer\", \"transition\", \"all 0.2s\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"center\", \"min-width\", \"44px\", \"min-height\", \"44px\", 3, \"disabled\"], [\"class\", \"fas fa-paper-plane\", 4, \"ngIf\"], [\"style\", \"\\n            width: 16px;\\n            height: 16px;\\n            border: 2px solid #ffffff;\\n            border-top-color: transparent;\\n            border-radius: 50%;\\n            animation: spin 1s linear infinite;\\n          \", 4, \"ngIf\"], [\"style\", \"\\n        position: absolute;\\n        bottom: 80px;\\n        left: 16px;\\n        background: #ffffff;\\n        border-radius: 16px;\\n        box-shadow: 0 20px 25px rgba(0, 0, 0, 0.1);\\n        border: 1px solid #e5e7eb;\\n        z-index: 50;\\n        width: 320px;\\n        max-height: 300px;\\n        overflow-y: auto;\\n      \", 4, \"ngIf\"], [\"style\", \"\\n        position: absolute;\\n        bottom: 80px;\\n        left: 60px;\\n        background: #ffffff;\\n        border-radius: 16px;\\n        box-shadow: 0 20px 25px rgba(0, 0, 0, 0.1);\\n        border: 1px solid #e5e7eb;\\n        z-index: 50;\\n        min-width: 200px;\\n      \", 4, \"ngIf\"], [\"type\", \"file\", \"multiple\", \"\", 2, \"display\", \"none\", 3, \"accept\", \"change\"], [\"fileInput\", \"\"], [\"style\", \"\\n      position: fixed;\\n      top: 0;\\n      left: 0;\\n      right: 0;\\n      bottom: 0;\\n      background: rgba(0, 0, 0, 0.25);\\n      z-index: 40;\\n    \", 3, \"click\", 4, \"ngIf\"], [\"style\", \"\\n      position: fixed;\\n      bottom: 100px;\\n      left: 50%;\\n      transform: translateX(-50%);\\n      background: linear-gradient(135deg, #f59e0b, #d97706);\\n      color: white;\\n      padding: 20px 24px;\\n      border-radius: 20px;\\n      box-shadow: 0 20px 25px rgba(0, 0, 0, 0.2);\\n      z-index: 60;\\n      display: flex;\\n      align-items: center;\\n      gap: 16px;\\n      min-width: 280px;\\n      animation: slideInUp 0.3s ease-out;\\n    \", 4, \"ngIf\"], [2, \"position\", \"absolute\", \"bottom\", \"0\", \"right\", \"0\", \"width\", \"12px\", \"height\", \"12px\", \"background\", \"#10b981\", \"border\", \"2px solid transparent\", \"border-radius\", \"50%\", \"animation\", \"pulse 2s infinite\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"4px\", \"color\", \"#10b981\"], [2, \"display\", \"flex\", \"gap\", \"2px\"], [2, \"width\", \"4px\", \"height\", \"4px\", \"background\", \"#10b981\", \"border-radius\", \"50%\", \"animation\", \"bounce 1s infinite\"], [2, \"width\", \"4px\", \"height\", \"4px\", \"background\", \"#10b981\", \"border-radius\", \"50%\", \"animation\", \"bounce 1s infinite 0.1s\"], [2, \"width\", \"4px\", \"height\", \"4px\", \"background\", \"#10b981\", \"border-radius\", \"50%\", \"animation\", \"bounce 1s infinite 0.2s\"], [2, \"position\", \"absolute\", \"top\", \"64px\", \"right\", \"16px\", \"background\", \"#ffffff\", \"border-radius\", \"16px\", \"box-shadow\", \"0 20px 25px rgba(0, 0, 0, 0.1)\", \"border\", \"1px solid #e5e7eb\", \"z-index\", \"50\", \"min-width\", \"192px\"], [2, \"padding\", \"8px\"], [\"onmouseover\", \"this.style.background='#f3f4f6'\", \"onmouseout\", \"this.style.background='transparent'\", 2, \"width\", \"100%\", \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"12px\", \"padding\", \"8px 12px\", \"border-radius\", \"8px\", \"border\", \"none\", \"background\", \"transparent\", \"cursor\", \"pointer\", \"transition\", \"all 0.2s\", \"text-align\", \"left\", 3, \"click\"], [1, \"fas\", \"fa-search\", 2, \"color\", \"#3b82f6\"], [2, \"color\", \"#374151\"], [\"onmouseover\", \"this.style.background='#f3f4f6'\", \"onmouseout\", \"this.style.background='transparent'\", 2, \"width\", \"100%\", \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"12px\", \"padding\", \"8px 12px\", \"border-radius\", \"8px\", \"border\", \"none\", \"background\", \"transparent\", \"cursor\", \"pointer\", \"transition\", \"all 0.2s\", \"text-align\", \"left\"], [1, \"fas\", \"fa-user\", 2, \"color\", \"#10b981\"], [2, \"margin\", \"8px 0\", \"border-color\", \"#e5e7eb\"], [1, \"fas\", \"fa-cog\", 2, \"color\", \"#6b7280\"], [2, \"position\", \"absolute\", \"top\", \"0\", \"left\", \"0\", \"right\", \"0\", \"bottom\", \"0\", \"background\", \"rgba(34, 197, 94, 0.2)\", \"border\", \"2px dashed transparent\", \"border-radius\", \"8px\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"center\", \"z-index\", \"50\", \"backdrop-filter\", \"blur(2px)\", \"animation\", \"pulse 2s infinite\"], [2, \"text-align\", \"center\", \"background\", \"#ffffff\", \"padding\", \"24px\", \"border-radius\", \"12px\", \"box-shadow\", \"0 10px 15px rgba(0, 0, 0, 0.1)\", \"border\", \"1px solid transparent\"], [1, \"fas\", \"fa-cloud-upload-alt\", 2, \"font-size\", \"48px\", \"color\", \"#10b981\", \"margin-bottom\", \"12px\", \"animation\", \"bounce 1s infinite\"], [2, \"font-size\", \"20px\", \"font-weight\", \"bold\", \"color\", \"#047857\", \"margin-bottom\", \"8px\"], [2, \"font-size\", \"14px\", \"color\", \"#10b981\"], [2, \"display\", \"flex\", \"flex-direction\", \"column\", \"align-items\", \"center\", \"justify-content\", \"center\", \"padding\", \"32px 0\"], [2, \"width\", \"32px\", \"height\", \"32px\", \"border\", \"2px solid #e5e7eb\", \"border-bottom-color\", \"#10b981\", \"border-radius\", \"50%\", \"animation\", \"spin 1s linear infinite\", \"margin-bottom\", \"16px\"], [2, \"color\", \"#6b7280\"], [2, \"display\", \"flex\", \"flex-direction\", \"column\", \"align-items\", \"center\", \"justify-content\", \"center\", \"padding\", \"64px 0\"], [2, \"font-size\", \"64px\", \"color\", \"#d1d5db\", \"margin-bottom\", \"16px\"], [1, \"fas\", \"fa-comments\"], [2, \"font-size\", \"20px\", \"font-weight\", \"600\", \"color\", \"#374151\", \"margin-bottom\", \"8px\"], [2, \"color\", \"#6b7280\", \"text-align\", \"center\"], [2, \"display\", \"flex\", \"flex-direction\", \"column\", \"gap\", \"8px\"], [4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"style\", \"display: flex; align-items: start; gap: 8px\", 4, \"ngIf\"], [\"style\", \"display: flex; justify-content: center; margin: 16px 0\", 4, \"ngIf\"], [2, \"display\", \"flex\", 3, \"id\", \"click\", \"contextmenu\"], [\"style\", \"margin-right: 8px; flex-shrink: 0\", 4, \"ngIf\"], [2, \"max-width\", \"320px\", \"padding\", \"12px 16px\", \"border-radius\", \"18px\", \"box-shadow\", \"0 1px 3px rgba(0, 0, 0, 0.1)\", \"position\", \"relative\", \"word-wrap\", \"break-word\", \"overflow-wrap\", \"break-word\", \"border\", \"none\"], [\"style\", \"\\n                font-size: 12px;\\n                font-weight: 600;\\n                margin-bottom: 4px;\\n                opacity: 0.75;\\n              \", 3, \"color\", 4, \"ngIf\"], [\"style\", \"word-wrap: break-word; overflow-wrap: break-word\", 4, \"ngIf\"], [\"style\", \"margin: 8px 0\", 4, \"ngIf\"], [\"style\", \"\\n                display: flex;\\n                align-items: center;\\n                gap: 12px;\\n                padding: 12px;\\n                background: rgba(255, 255, 255, 0.1);\\n                border-radius: 12px;\\n                margin: 8px 0;\\n                min-width: 200px;\\n                max-width: 280px;\\n              \", 4, \"ngIf\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"flex-end\", \"gap\", \"4px\", \"margin-top\", \"4px\", \"font-size\", \"12px\", \"opacity\", \"0.75\"], [\"style\", \"display: flex; align-items: center\", 4, \"ngIf\"], [2, \"display\", \"flex\", \"justify-content\", \"center\", \"margin\", \"16px 0\"], [2, \"background\", \"#ffffff\", \"padding\", \"4px 12px\", \"border-radius\", \"20px\", \"box-shadow\", \"0 1px 3px rgba(0, 0, 0, 0.1)\"], [2, \"font-size\", \"12px\", \"color\", \"#6b7280\"], [2, \"margin-right\", \"8px\", \"flex-shrink\", \"0\"], [\"onmouseover\", \"this.style.transform='scale(1.05)'\", \"onmouseout\", \"this.style.transform='scale(1)'\", 2, \"width\", \"32px\", \"height\", \"32px\", \"border-radius\", \"50%\", \"object-fit\", \"cover\", \"cursor\", \"pointer\", \"transition\", \"transform 0.2s\", 3, \"src\", \"alt\", \"click\"], [2, \"font-size\", \"12px\", \"font-weight\", \"600\", \"margin-bottom\", \"4px\", \"opacity\", \"0.75\"], [2, \"word-wrap\", \"break-word\", \"overflow-wrap\", \"break-word\"], [3, \"innerHTML\"], [2, \"margin\", \"8px 0\"], [\"onmouseover\", \"this.style.transform='scale(1.02)'\", \"onmouseout\", \"this.style.transform='scale(1)'\", 2, \"max-width\", \"280px\", \"height\", \"auto\", \"border-radius\", \"12px\", \"cursor\", \"pointer\", \"transition\", \"transform 0.2s\", 3, \"src\", \"alt\", \"click\", \"load\", \"error\"], [\"style\", \"font-size: 14px; margin-top: 8px; line-height: 1.4\", 3, \"color\", \"innerHTML\", 4, \"ngIf\"], [2, \"font-size\", \"14px\", \"margin-top\", \"8px\", \"line-height\", \"1.4\", 3, \"innerHTML\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"12px\", \"padding\", \"12px\", \"background\", \"rgba(255, 255, 255, 0.1)\", \"border-radius\", \"12px\", \"margin\", \"8px 0\", \"min-width\", \"200px\", \"max-width\", \"280px\"], [\"onmouseover\", \"this.style.background='rgba(255, 255, 255, 0.3)'\", \"onmouseout\", \"this.style.background='rgba(255, 255, 255, 0.2)'\", \"title\", \"Lire/Pause\", 2, \"width\", \"40px\", \"height\", \"40px\", \"border-radius\", \"50%\", \"border\", \"none\", \"background\", \"rgba(255, 255, 255, 0.2)\", \"color\", \"inherit\", \"cursor\", \"pointer\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"center\", \"transition\", \"all 0.2s\", \"flex-shrink\", \"0\", 3, \"click\"], [2, \"font-size\", \"14px\"], [2, \"flex\", \"1\", \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"2px\", \"height\", \"24px\", \"overflow\", \"hidden\"], [\"style\", \"\\n                    width: 3px;\\n                    background: currentColor;\\n                    border-radius: 2px;\\n                    opacity: 0.7;\\n                    transition: height 0.3s ease;\\n                  \", 3, \"height\", \"animation\", \"animation-delay\", 4, \"ngFor\", \"ngForOf\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"8px\", \"flex-shrink\", \"0\"], [2, \"font-size\", \"12px\", \"opacity\", \"0.8\", \"min-width\", \"40px\", \"text-align\", \"right\"], [\"style\", \"\\n                    padding: 4px 8px;\\n                    border-radius: 12px;\\n                    border: none;\\n                    background: rgba(255, 255, 255, 0.2);\\n                    color: inherit;\\n                    cursor: pointer;\\n                    font-size: 11px;\\n                    transition: all 0.2s;\\n                  \", \"onmouseover\", \"this.style.background='rgba(255, 255, 255, 0.3)'\", \"onmouseout\", \"this.style.background='rgba(255, 255, 255, 0.2)'\", \"title\", \"Changer la vitesse\", 3, \"click\", 4, \"ngIf\"], [2, \"width\", \"3px\", \"background\", \"currentColor\", \"border-radius\", \"2px\", \"opacity\", \"0.7\", \"transition\", \"height 0.3s ease\"], [\"onmouseover\", \"this.style.background='rgba(255, 255, 255, 0.3)'\", \"onmouseout\", \"this.style.background='rgba(255, 255, 255, 0.2)'\", \"title\", \"Changer la vitesse\", 2, \"padding\", \"4px 8px\", \"border-radius\", \"12px\", \"border\", \"none\", \"background\", \"rgba(255, 255, 255, 0.2)\", \"color\", \"inherit\", \"cursor\", \"pointer\", \"font-size\", \"11px\", \"transition\", \"all 0.2s\", 3, \"click\"], [2, \"display\", \"flex\", \"align-items\", \"center\"], [\"class\", \"fas fa-clock\", \"title\", \"Envoi en cours\", 4, \"ngIf\"], [\"class\", \"fas fa-check\", \"title\", \"Envoy\\u00E9\", 4, \"ngIf\"], [\"class\", \"fas fa-check-double\", \"title\", \"Livr\\u00E9\", 4, \"ngIf\"], [\"class\", \"fas fa-check-double\", \"style\", \"color: #3b82f6\", \"title\", \"Lu\", 4, \"ngIf\"], [\"title\", \"Envoi en cours\", 1, \"fas\", \"fa-clock\"], [\"title\", \"Envoy\\u00E9\", 1, \"fas\", \"fa-check\"], [\"title\", \"Livr\\u00E9\", 1, \"fas\", \"fa-check-double\"], [\"title\", \"Lu\", 1, \"fas\", \"fa-check-double\", 2, \"color\", \"#3b82f6\"], [2, \"display\", \"flex\", \"align-items\", \"start\", \"gap\", \"8px\"], [2, \"width\", \"32px\", \"height\", \"32px\", \"border-radius\", \"50%\", \"object-fit\", \"cover\", 3, \"src\", \"alt\"], [2, \"background\", \"#ffffff\", \"padding\", \"12px 16px\", \"border-radius\", \"18px\", \"box-shadow\", \"0 1px 3px rgba(0, 0, 0, 0.1)\"], [2, \"display\", \"flex\", \"gap\", \"4px\"], [2, \"width\", \"8px\", \"height\", \"8px\", \"background\", \"#6b7280\", \"border-radius\", \"50%\", \"animation\", \"bounce 1s infinite\"], [2, \"width\", \"8px\", \"height\", \"8px\", \"background\", \"#6b7280\", \"border-radius\", \"50%\", \"animation\", \"bounce 1s infinite 0.1s\"], [2, \"width\", \"8px\", \"height\", \"8px\", \"background\", \"#6b7280\", \"border-radius\", \"50%\", \"animation\", \"bounce 1s infinite 0.2s\"], [2, \"position\", \"absolute\", \"top\", \"-2px\", \"right\", \"-2px\", \"width\", \"8px\", \"height\", \"8px\", \"background\", \"#ef4444\", \"border-radius\", \"50%\", \"animation\", \"ping 1s infinite\"], [1, \"fas\", \"fa-paper-plane\"], [2, \"width\", \"16px\", \"height\", \"16px\", \"border\", \"2px solid #ffffff\", \"border-top-color\", \"transparent\", \"border-radius\", \"50%\", \"animation\", \"spin 1s linear infinite\"], [2, \"position\", \"absolute\", \"bottom\", \"80px\", \"left\", \"16px\", \"background\", \"#ffffff\", \"border-radius\", \"16px\", \"box-shadow\", \"0 20px 25px rgba(0, 0, 0, 0.1)\", \"border\", \"1px solid #e5e7eb\", \"z-index\", \"50\", \"width\", \"320px\", \"max-height\", \"300px\", \"overflow-y\", \"auto\"], [2, \"padding\", \"16px\"], [2, \"margin\", \"0 0 12px 0\", \"font-size\", \"14px\", \"font-weight\", \"600\", \"color\", \"#374151\"], [2, \"display\", \"grid\", \"grid-template-columns\", \"repeat(8, 1fr)\", \"gap\", \"8px\"], [\"style\", \"\\n              padding: 8px;\\n              border: none;\\n              background: transparent;\\n              border-radius: 8px;\\n              cursor: pointer;\\n              font-size: 20px;\\n              transition: all 0.2s;\\n            \", \"onmouseover\", \"this.style.background='#f3f4f6'\", \"onmouseout\", \"this.style.background='transparent'\", 3, \"title\", \"click\", 4, \"ngFor\", \"ngForOf\"], [\"onmouseover\", \"this.style.background='#f3f4f6'\", \"onmouseout\", \"this.style.background='transparent'\", 2, \"padding\", \"8px\", \"border\", \"none\", \"background\", \"transparent\", \"border-radius\", \"8px\", \"cursor\", \"pointer\", \"font-size\", \"20px\", \"transition\", \"all 0.2s\", 3, \"title\", \"click\"], [2, \"position\", \"absolute\", \"bottom\", \"80px\", \"left\", \"60px\", \"background\", \"#ffffff\", \"border-radius\", \"16px\", \"box-shadow\", \"0 20px 25px rgba(0, 0, 0, 0.1)\", \"border\", \"1px solid #e5e7eb\", \"z-index\", \"50\", \"min-width\", \"200px\"], [2, \"display\", \"grid\", \"grid-template-columns\", \"repeat(2, 1fr)\", \"gap\", \"12px\"], [\"onmouseover\", \"this.style.background='#f3f4f6'\", \"onmouseout\", \"this.style.background='transparent'\", 2, \"display\", \"flex\", \"flex-direction\", \"column\", \"align-items\", \"center\", \"gap\", \"8px\", \"padding\", \"16px\", \"border\", \"none\", \"background\", \"transparent\", \"border-radius\", \"12px\", \"cursor\", \"pointer\", \"transition\", \"all 0.2s\", 3, \"click\"], [2, \"width\", \"48px\", \"height\", \"48px\", \"background\", \"#dbeafe\", \"border-radius\", \"50%\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"center\"], [1, \"fas\", \"fa-image\", 2, \"color\", \"#3b82f6\", \"font-size\", \"20px\"], [2, \"font-size\", \"14px\", \"font-weight\", \"500\", \"color\", \"#374151\"], [2, \"width\", \"48px\", \"height\", \"48px\", \"background\", \"#fef3c7\", \"border-radius\", \"50%\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"center\"], [1, \"fas\", \"fa-file-alt\", 2, \"color\", \"#f59e0b\", \"font-size\", \"20px\"], [2, \"width\", \"48px\", \"height\", \"48px\", \"background\", \"#dcfce7\", \"border-radius\", \"50%\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"center\"], [1, \"fas\", \"fa-camera\", 2, \"color\", \"#10b981\", \"font-size\", \"20px\"], [2, \"position\", \"fixed\", \"top\", \"0\", \"left\", \"0\", \"right\", \"0\", \"bottom\", \"0\", \"background\", \"rgba(0, 0, 0, 0.25)\", \"z-index\", \"40\", 3, \"click\"], [2, \"position\", \"fixed\", \"bottom\", \"100px\", \"left\", \"50%\", \"transform\", \"translateX(-50%)\", \"background\", \"linear-gradient(135deg, #f59e0b, #d97706)\", \"color\", \"white\", \"padding\", \"20px 24px\", \"border-radius\", \"20px\", \"box-shadow\", \"0 20px 25px rgba(0, 0, 0, 0.2)\", \"z-index\", \"60\", \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"16px\", \"min-width\", \"280px\", \"animation\", \"slideInUp 0.3s ease-out\"], [2, \"width\", \"48px\", \"height\", \"48px\", \"background\", \"rgba(255, 255, 255, 0.2)\", \"border-radius\", \"50%\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"center\", \"animation\", \"pulse 1s infinite\"], [1, \"fas\", \"fa-microphone\", 2, \"font-size\", \"20px\"], [2, \"flex\", \"1\"], [2, \"font-size\", \"18px\", \"font-weight\", \"bold\", \"margin-bottom\", \"4px\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"2px\", \"height\", \"20px\"], [\"style\", \"\\n            width: 3px;\\n            background: rgba(255, 255, 255, 0.8);\\n            border-radius: 2px;\\n            transition: height 0.3s ease;\\n          \", 3, \"height\", \"animation\", \"animation-delay\", 4, \"ngFor\", \"ngForOf\"], [2, \"font-size\", \"12px\", \"opacity\", \"0.8\", \"margin-top\", \"4px\"], [\"onmouseover\", \"this.style.background='rgba(239, 68, 68, 1)'\", \"onmouseout\", \"this.style.background='rgba(239, 68, 68, 0.8)'\", \"title\", \"Annuler l'enregistrement\", 2, \"width\", \"40px\", \"height\", \"40px\", \"border-radius\", \"50%\", \"border\", \"none\", \"background\", \"rgba(239, 68, 68, 0.8)\", \"color\", \"white\", \"cursor\", \"pointer\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"center\", \"transition\", \"all 0.2s\", 3, \"click\"], [1, \"fas\", \"fa-times\", 2, \"font-size\", \"16px\"], [\"onmouseover\", \"this.style.background='rgba(34, 197, 94, 1)'\", \"onmouseout\", \"this.style.background='rgba(34, 197, 94, 0.8)'\", \"title\", \"Envoyer le message vocal\", 2, \"width\", \"40px\", \"height\", \"40px\", \"border-radius\", \"50%\", \"border\", \"none\", \"background\", \"rgba(34, 197, 94, 0.8)\", \"color\", \"white\", \"cursor\", \"pointer\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"center\", \"transition\", \"all 0.2s\", 3, \"click\"], [1, \"fas\", \"fa-paper-plane\", 2, \"font-size\", \"16px\"], [2, \"width\", \"3px\", \"background\", \"rgba(255, 255, 255, 0.8)\", \"border-radius\", \"2px\", \"transition\", \"height 0.3s ease\"]],\n      template: function MessageChatComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"video\", 0, 1)(2, \"video\", 2, 3);\n          i0.ɵɵelementStart(4, \"div\", 4)(5, \"header\", 5)(6, \"button\", 6);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_6_listener() {\n            return ctx.goBackToConversations();\n          });\n          i0.ɵɵelement(7, \"i\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"div\", 8)(9, \"div\", 9)(10, \"img\", 10);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_img_click_10_listener() {\n            return ctx.openUserProfile(ctx.otherParticipant == null ? null : ctx.otherParticipant.id);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(11, MessageChatComponent_div_11_Template, 1, 0, \"div\", 11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"div\", 12)(13, \"h3\", 13);\n          i0.ɵɵtext(14);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"div\", 14);\n          i0.ɵɵtemplate(16, MessageChatComponent_div_16_Template, 7, 0, \"div\", 15);\n          i0.ɵɵtemplate(17, MessageChatComponent_span_17_Template, 2, 1, \"span\", 16);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(18, \"div\", 17)(19, \"button\", 18);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_19_listener() {\n            return ctx.startVideoCall();\n          });\n          i0.ɵɵelement(20, \"i\", 19);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"button\", 20);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_21_listener() {\n            return ctx.startVoiceCall();\n          });\n          i0.ɵɵelement(22, \"i\", 21);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"button\", 22);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_23_listener() {\n            return ctx.toggleSearch();\n          });\n          i0.ɵɵelement(24, \"i\", 23);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"button\", 24);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_25_listener() {\n            return ctx.toggleMainMenu();\n          });\n          i0.ɵɵelement(26, \"i\", 25);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(27, MessageChatComponent_div_27_Template, 15, 0, \"div\", 26);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"main\", 27, 28);\n          i0.ɵɵlistener(\"scroll\", function MessageChatComponent_Template_main_scroll_28_listener($event) {\n            return ctx.onScroll($event);\n          })(\"dragover\", function MessageChatComponent_Template_main_dragover_28_listener($event) {\n            return ctx.onDragOver($event);\n          })(\"dragleave\", function MessageChatComponent_Template_main_dragleave_28_listener($event) {\n            return ctx.onDragLeave($event);\n          })(\"drop\", function MessageChatComponent_Template_main_drop_28_listener($event) {\n            return ctx.onDrop($event);\n          });\n          i0.ɵɵtemplate(30, MessageChatComponent_div_30_Template, 7, 0, \"div\", 29);\n          i0.ɵɵtemplate(31, MessageChatComponent_div_31_Template, 4, 0, \"div\", 30);\n          i0.ɵɵtemplate(32, MessageChatComponent_div_32_Template, 7, 1, \"div\", 31);\n          i0.ɵɵtemplate(33, MessageChatComponent_div_33_Template, 3, 3, \"div\", 32);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"footer\", 33)(35, \"form\", 34);\n          i0.ɵɵlistener(\"ngSubmit\", function MessageChatComponent_Template_form_ngSubmit_35_listener() {\n            return ctx.sendMessage();\n          });\n          i0.ɵɵelementStart(36, \"div\", 35)(37, \"button\", 36);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_37_listener() {\n            return ctx.toggleEmojiPicker();\n          });\n          i0.ɵɵelement(38, \"i\", 37);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(39, \"button\", 38);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_39_listener() {\n            return ctx.toggleAttachmentMenu();\n          });\n          i0.ɵɵelement(40, \"i\", 39);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(41, \"button\", 40);\n          i0.ɵɵlistener(\"mousedown\", function MessageChatComponent_Template_button_mousedown_41_listener($event) {\n            return ctx.onRecordStart($event);\n          })(\"mouseup\", function MessageChatComponent_Template_button_mouseup_41_listener($event) {\n            return ctx.onRecordEnd($event);\n          })(\"mouseleave\", function MessageChatComponent_Template_button_mouseleave_41_listener($event) {\n            return ctx.onRecordCancel($event);\n          })(\"touchstart\", function MessageChatComponent_Template_button_touchstart_41_listener($event) {\n            return ctx.onRecordStart($event);\n          })(\"touchend\", function MessageChatComponent_Template_button_touchend_41_listener($event) {\n            return ctx.onRecordEnd($event);\n          })(\"touchcancel\", function MessageChatComponent_Template_button_touchcancel_41_listener($event) {\n            return ctx.onRecordCancel($event);\n          });\n          i0.ɵɵelement(42, \"i\");\n          i0.ɵɵtemplate(43, MessageChatComponent_div_43_Template, 1, 0, \"div\", 41);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(44, \"div\", 42)(45, \"textarea\", 43);\n          i0.ɵɵlistener(\"keydown\", function MessageChatComponent_Template_textarea_keydown_45_listener($event) {\n            return ctx.onInputKeyDown($event);\n          })(\"input\", function MessageChatComponent_Template_textarea_input_45_listener($event) {\n            return ctx.onInputChange($event);\n          })(\"focus\", function MessageChatComponent_Template_textarea_focus_45_listener() {\n            return ctx.onInputFocus();\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(46, \"button\", 44);\n          i0.ɵɵtemplate(47, MessageChatComponent_i_47_Template, 1, 0, \"i\", 45);\n          i0.ɵɵtemplate(48, MessageChatComponent_div_48_Template, 1, 0, \"div\", 46);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(49, MessageChatComponent_div_49_Template, 6, 1, \"div\", 47);\n          i0.ɵɵtemplate(50, MessageChatComponent_div_50_Template, 20, 0, \"div\", 48);\n          i0.ɵɵelementStart(51, \"input\", 49, 50);\n          i0.ɵɵlistener(\"change\", function MessageChatComponent_Template_input_change_51_listener($event) {\n            return ctx.onFileSelected($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(53, MessageChatComponent_div_53_Template, 1, 0, \"div\", 51);\n          i0.ɵɵtemplate(54, MessageChatComponent_div_54_Template, 15, 3, \"div\", 52);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"src\", (ctx.otherParticipant == null ? null : ctx.otherParticipant.image) || \"assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", ctx.otherParticipant == null ? null : ctx.otherParticipant.username);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.otherParticipant == null ? null : ctx.otherParticipant.isOnline);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", (ctx.otherParticipant == null ? null : ctx.otherParticipant.username) || \"Utilisateur\", \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.isUserTyping);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isUserTyping);\n          i0.ɵɵadvance(6);\n          i0.ɵɵstyleProp(\"background\", ctx.searchMode ? \"#dcfce7\" : \"transparent\")(\"color\", ctx.searchMode ? \"#16a34a\" : \"#6b7280\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵstyleProp(\"background\", ctx.showMainMenu ? \"#dcfce7\" : \"transparent\")(\"color\", ctx.showMainMenu ? \"#16a34a\" : \"#6b7280\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.showMainMenu);\n          i0.ɵɵadvance(1);\n          i0.ɵɵstyleProp(\"background\", ctx.isDragOver ? \"rgba(34, 197, 94, 0.1)\" : \"transparent\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.isDragOver);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.messages.length === 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.messages.length > 0);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"formGroup\", ctx.messageForm);\n          i0.ɵɵadvance(2);\n          i0.ɵɵstyleProp(\"background\", ctx.showEmojiPicker ? \"#dcfce7\" : \"transparent\")(\"color\", ctx.showEmojiPicker ? \"#16a34a\" : \"#6b7280\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵstyleProp(\"background\", ctx.showAttachmentMenu ? \"#dcfce7\" : \"transparent\")(\"color\", ctx.showAttachmentMenu ? \"#16a34a\" : \"#6b7280\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵstyleProp(\"background\", ctx.isRecordingVoice ? \"#fef3c7\" : \"transparent\")(\"color\", ctx.isRecordingVoice ? \"#f59e0b\" : \"#6b7280\")(\"transform\", ctx.isRecordingVoice ? \"scale(1.1)\" : \"scale(1)\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵclassMap(ctx.isRecordingVoice ? \"fas fa-stop\" : \"fas fa-microphone\");\n          i0.ɵɵstyleProp(\"animation\", ctx.isRecordingVoice ? \"pulse 1s infinite\" : \"none\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isRecordingVoice);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", ctx.isInputDisabled());\n          i0.ɵɵadvance(1);\n          i0.ɵɵstyleProp(\"background\", !ctx.messageForm.valid || ctx.isSendingMessage ? \"#9ca3af\" : \"#3b82f6\")(\"cursor\", !ctx.messageForm.valid || ctx.isSendingMessage ? \"not-allowed\" : \"pointer\");\n          i0.ɵɵproperty(\"disabled\", !ctx.messageForm.valid || ctx.isSendingMessage);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isSendingMessage);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isSendingMessage);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.showEmojiPicker);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.showAttachmentMenu);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"accept\", ctx.getFileAcceptTypes());\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.showEmojiPicker || ctx.showAttachmentMenu || ctx.showMainMenu);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isRecordingVoice);\n        }\n      },\n      dependencies: [i6.NgForOf, i6.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName],\n      styles: [\"@keyframes _ngcontent-%COMP%_pulse {\\n      0%,\\n      100% {\\n        opacity: 1;\\n      }\\n      50% {\\n        opacity: 0.5;\\n      }\\n    }\\n    @keyframes _ngcontent-%COMP%_bounce {\\n      0%,\\n      20%,\\n      53%,\\n      80%,\\n      100% {\\n        transform: translateY(0);\\n      }\\n      40%,\\n      43% {\\n        transform: translateY(-8px);\\n      }\\n      70% {\\n        transform: translateY(-4px);\\n      }\\n    }\\n    @keyframes _ngcontent-%COMP%_spin {\\n      from {\\n        transform: rotate(0deg);\\n      }\\n      to {\\n        transform: rotate(360deg);\\n      }\\n    }\\n    @keyframes _ngcontent-%COMP%_ping {\\n      75%,\\n      100% {\\n        transform: scale(2);\\n        opacity: 0;\\n      }\\n    }\\n    @keyframes _ngcontent-%COMP%_slideInUp {\\n      from {\\n        transform: translateX(-50%) translateY(20px);\\n        opacity: 0;\\n      }\\n      to {\\n        transform: translateX(-50%) translateY(0);\\n        opacity: 1;\\n      }\\n    }\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "Subscription", "CallType", "i0", "ɵɵelement", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r4", "otherParticipant", "isOnline", "formatLastActive", "lastActive", "ɵɵlistener", "MessageChatComponent_div_27_Template_button_click_2_listener", "ɵɵrestoreView", "_r20", "ctx_r19", "ɵɵnextContext", "toggleSearch", "ɵɵresetView", "showMainMenu", "ctx_r9", "username", "ctx_r25", "formatDateSeparator", "message_r23", "timestamp", "MessageChatComponent_div_33_ng_container_1_div_3_Template_img_click_1_listener", "_r35", "$implicit", "ctx_r33", "openUserProfile", "sender", "id", "ɵɵproperty", "image", "ɵɵsanitizeUrl", "ɵɵstyleProp", "ctx_r27", "getUserColor", "ctx_r28", "formatMessageContent", "content", "ɵɵsanitizeHtml", "ctx_r39", "currentUserId", "MessageChatComponent_div_33_ng_container_1_div_7_Template_img_click_1_listener", "_r43", "ctx_r41", "openImageViewer", "MessageChatComponent_div_33_ng_container_1_div_7_Template_img_load_1_listener", "$event", "ctx_r44", "onImageLoad", "MessageChatComponent_div_33_ng_container_1_div_7_Template_img_error_1_listener", "ctx_r46", "onImageError", "ɵɵtemplate", "MessageChatComponent_div_33_ng_container_1_div_7_div_2_Template", "ctx_r29", "getImageUrl", "ctx_r49", "isVoicePlaying", "wave_r51", "i_r52", "MessageChatComponent_div_33_ng_container_1_div_8_button_8_Template_button_click_0_listener", "_r56", "ctx_r54", "changeVoiceSpeed", "ctx_r50", "getVoiceSpeed", "MessageChatComponent_div_33_ng_container_1_div_8_Template_button_click_1_listener", "_r60", "ctx_r58", "toggleVoicePlayback", "MessageChatComponent_div_33_ng_container_1_div_8_div_4_Template", "MessageChatComponent_div_33_ng_container_1_div_8_button_8_Template", "ɵɵclassMap", "ctx_r30", "voiceWaves", "getVoiceDuration", "MessageChatComponent_div_33_ng_container_1_div_12_i_1_Template", "MessageChatComponent_div_33_ng_container_1_div_12_i_2_Template", "MessageChatComponent_div_33_ng_container_1_div_12_i_3_Template", "MessageChatComponent_div_33_ng_container_1_div_12_i_4_Template", "status", "ɵɵelementContainerStart", "MessageChatComponent_div_33_ng_container_1_div_1_Template", "MessageChatComponent_div_33_ng_container_1_Template_div_click_2_listener", "restoredCtx", "_r68", "ctx_r67", "onMessageClick", "MessageChatComponent_div_33_ng_container_1_Template_div_contextmenu_2_listener", "ctx_r69", "onMessageContextMenu", "MessageChatComponent_div_33_ng_container_1_div_3_Template", "MessageChatComponent_div_33_ng_container_1_div_5_Template", "MessageChatComponent_div_33_ng_container_1_div_6_Template", "MessageChatComponent_div_33_ng_container_1_div_7_Template", "MessageChatComponent_div_33_ng_container_1_div_8_Template", "MessageChatComponent_div_33_ng_container_1_div_12_Template", "ɵɵelementContainerEnd", "ctx_r21", "shouldShowDateSeparator", "i_r24", "shouldShowAvatar", "isGroupConversation", "shouldShowSenderName", "getMessageType", "hasImage", "ɵɵtextInterpolate", "formatMessageTime", "ctx_r22", "MessageChatComponent_div_33_ng_container_1_Template", "MessageChatComponent_div_33_div_2_Template", "ctx_r10", "messages", "trackByMessageId", "otherUserIsTyping", "MessageChatComponent_div_49_button_5_Template_button_click_0_listener", "_r73", "emoji_r71", "ctx_r72", "insert<PERSON><PERSON><PERSON>", "name", "emoji", "MessageChatComponent_div_49_button_5_Template", "ctx_r14", "getEmojisForCategory", "selectedEmojiCategory", "MessageChatComponent_div_50_Template_button_click_5_listener", "_r75", "ctx_r74", "triggerFileInput", "MessageChatComponent_div_50_Template_button_click_10_listener", "ctx_r76", "MessageChatComponent_div_50_Template_button_click_15_listener", "ctx_r77", "openCamera", "MessageChatComponent_div_53_Template_div_click_0_listener", "_r79", "ctx_r78", "closeAllMenus", "wave_r81", "i_r82", "MessageChatComponent_div_54_div_7_Template", "MessageChatComponent_div_54_Template_button_click_11_listener", "_r84", "ctx_r83", "onRecordCancel", "MessageChatComponent_div_54_Template_button_click_13_listener", "ctx_r85", "onRecordEnd", "ctx_r18", "formatRecordingDuration", "voiceRecordingDuration", "getRecordingFormat", "MessageChatComponent", "constructor", "fb", "route", "router", "MessageService", "callService", "toastService", "cdr", "conversation", "currentUsername", "isLoading", "isLoadingMore", "hasMoreMessages", "showEmojiPicker", "showAttachmentMenu", "showSearch", "searchQuery", "searchResults", "searchMode", "isSendingMessage", "showMessageContextMenu", "selectedMessage", "contextMenuPosition", "x", "y", "showReactionPicker", "reactionPickerMessage", "showImageViewer", "selectedImage", "uploadProgress", "isUploading", "isDragOver", "isRecordingVoice", "voiceRecordingState", "mediaRecorder", "audioChunks", "recordingTimer", "currentAudio", "playingMessageId", "voicePlayback", "isInCall", "callType", "callDuration", "callTimer", "activeCall", "isCallConnected", "isMuted", "isVideoEnabled", "localVideoElement", "remoteVideoElement", "emojiCategories", "icon", "emojis", "MAX_MESSAGES_TO_LOAD", "currentPage", "isTyping", "isUserTyping", "typingTimeout", "subscriptions", "messageForm", "group", "required", "<PERSON><PERSON><PERSON><PERSON>", "isInputDisabled", "updateInputState", "contentControl", "get", "disable", "enable", "ngOnInit", "console", "log", "initializeComponent", "enableSoundsOnFirstInteraction", "enableSounds", "document", "removeEventListener", "addEventListener", "once", "loadCurrentUser", "loadConversation", "setupCallSubscriptions", "add", "incomingCall$", "subscribe", "next", "incomingCall", "handleIncomingCall", "error", "activeCall$", "call", "caller", "play", "userString", "localStorage", "getItem", "user", "JSON", "parse", "userId", "_id", "extracted", "conversationId", "snapshot", "paramMap", "showError", "getConversation", "participants", "participantsCount", "length", "isGroup", "messagesCount", "setOtherParticipant", "loadMessages", "setupSubscriptions", "warn", "find", "p", "participantId", "String", "firstParticipantId", "sort", "a", "b", "dateA", "Date", "createdAt", "getTime", "dateB", "total", "first", "last", "scrollToBottom", "loadMoreMessages", "offset", "getMessages", "newMessages", "reverse", "subscribeToNewMessages", "newMessage", "type", "senderId", "receiverId", "attachments", "hasFile", "for<PERSON>ach", "att", "index", "url", "path", "size", "messageExists", "some", "msg", "push", "detectChanges", "setTimeout", "shouldMarkAsRead", "markMessageAsRead", "subscribeToTypingIndicator", "typingData", "subscribeToConversationUpdates", "conversationUpdate", "messageId", "sendMessage", "valid", "value", "trim", "undefined", "message", "reset", "messagesContainer", "element", "nativeElement", "scrollTop", "scrollHeight", "diffMins", "Math", "floor", "now", "getVoicePlaybackData", "progress", "duration", "currentTime", "speed", "setVoicePlaybackData", "data", "startVideoCall", "initiateCall", "VIDEO", "startVoiceCall", "AUDIO", "formatFileSize", "bytes", "round", "downloadFile", "fileAttachment", "startsWith", "link", "createElement", "href", "download", "target", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "showSuccess", "toggleMainMenu", "goBackToConversations", "navigate", "then", "catch", "window", "location", "event", "preventDefault", "clientX", "clientY", "showQuickReactions", "stopPropagation", "quickReact", "toggleReaction", "reactToMessage", "result", "messageIndex", "findIndex", "hasUserReacted", "reaction", "replyToMessage", "forwardMessage", "deleteMessage", "canDelete", "confirm", "filter", "toggleEmojiPicker", "selectEmojiCategory", "category", "currentC<PERSON>nt", "newContent", "patchValue", "toggleAttachmentMenu", "toString", "testAddMessage", "testMessage", "toLocaleTimeString", "toISOString", "isRead", "zoomImage", "factor", "imageElement", "querySelector", "currentTransform", "style", "transform", "currentScale", "parseFloat", "match", "newScale", "max", "min", "classList", "remove", "resetZoom", "input", "fileInput", "accept", "date", "hour", "minute", "today", "yesterday", "setDate", "getDate", "toDateString", "toLocaleDateString", "urlRegex", "replace", "currentMessage", "previousMessage", "currentDate", "previousDate", "nextMessage", "attachment", "voiceUrl", "audioUrl", "voice", "hasImageAttachment", "hasImageUrl", "imageUrl", "hasFileAttachment", "imageAttachment", "getFileName", "getFileSize", "getFileIcon", "includes", "colors", "charCodeAt", "onInputChange", "handleTypingIndicator", "onInputKeyDown", "key", "shift<PERSON>ey", "onInputFocus", "onInputBlur", "onScroll", "src", "closeImageViewer", "downloadImage", "searchMessages", "toLowerCase", "onSearchQueryChange", "clearSearch", "jumpToMessage", "messageElement", "getElementById", "scrollIntoView", "behavior", "block", "closeContextMenu", "recipientId", "<PERSON><PERSON><PERSON>", "startCallTimer", "callId", "callStatus", "recipient", "endCall", "acceptCall", "rejectCall", "resetCallState", "setInterval", "clearInterval", "toggleMute", "toggleMedia", "toggleVideo", "formatCallDuration", "hours", "minutes", "seconds", "padStart", "startVoiceRecording", "_this", "_asyncToGenerator", "navigator", "mediaDevices", "getUserMedia", "Error", "MediaRecorder", "stream", "audio", "echoCancellation", "noiseSuppression", "autoGainControl", "sampleRate", "channelCount", "mimeType", "isTypeSupported", "animateVoice<PERSON>aves", "ondataavailable", "onstop", "processRecordedAudio", "onerror", "cancelVoiceRecording", "start", "errorMessage", "stopVoiceRecording", "state", "stop", "getTracks", "track", "_this2", "audioBlob", "Blob", "extension", "audioFile", "File", "sendVoiceMessage", "_this3", "Promise", "resolve", "reject", "onRecordStart", "showWarning", "showInfo", "map", "random", "onFileSelected", "files", "file", "uploadFile", "maxSize", "compressImage", "compressedFile", "sendFileToServer", "messageType", "getFileMessageType", "progressInterval", "resetUploadState", "getFileAcceptTypes", "onDragOver", "onDragLeave", "rect", "currentTarget", "getBoundingClientRect", "left", "right", "top", "bottom", "onDrop", "dataTransfer", "Array", "from", "quality", "canvas", "ctx", "getContext", "img", "Image", "onload", "max<PERSON><PERSON><PERSON>", "maxHeight", "width", "height", "ratio", "drawImage", "toBlob", "blob", "lastModified", "URL", "createObjectURL", "sendTypingIndicator", "clearTimeout", "onCallAccepted", "onCallRejected", "playVoiceMessage", "getVoiceUrl", "stopVoicePlayback", "startVoicePlayback", "Audio", "currentData", "playbackRate", "pause", "audioAttachment", "getVoiceWaves", "seed", "split", "reduce", "acc", "char", "waves", "i", "getVoiceProgress", "totalWaves", "getVoiceCurrentTime", "formatAudioTime", "metadata", "remainingSeconds", "seekVoiceMessage", "waveIndex", "seekPercentage", "seekTime", "toggleVoiceSpeed", "newSpeed", "ngOnDestroy", "unsubscribe", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "ActivatedRoute", "Router", "i3", "i4", "CallService", "i5", "ToastService", "ChangeDetectorRef", "selectors", "viewQuery", "MessageChatComponent_Query", "rf", "MessageChatComponent_Template_button_click_6_listener", "MessageChatComponent_Template_img_click_10_listener", "MessageChatComponent_div_11_Template", "MessageChatComponent_div_16_Template", "MessageChatComponent_span_17_Template", "MessageChatComponent_Template_button_click_19_listener", "MessageChatComponent_Template_button_click_21_listener", "MessageChatComponent_Template_button_click_23_listener", "MessageChatComponent_Template_button_click_25_listener", "MessageChatComponent_div_27_Template", "MessageChatComponent_Template_main_scroll_28_listener", "MessageChatComponent_Template_main_dragover_28_listener", "MessageChatComponent_Template_main_dragleave_28_listener", "MessageChatComponent_Template_main_drop_28_listener", "MessageChatComponent_div_30_Template", "MessageChatComponent_div_31_Template", "MessageChatComponent_div_32_Template", "MessageChatComponent_div_33_Template", "MessageChatComponent_Template_form_ngSubmit_35_listener", "MessageChatComponent_Template_button_click_37_listener", "MessageChatComponent_Template_button_click_39_listener", "MessageChatComponent_Template_button_mousedown_41_listener", "MessageChatComponent_Template_button_mouseup_41_listener", "MessageChatComponent_Template_button_mouseleave_41_listener", "MessageChatComponent_Template_button_touchstart_41_listener", "MessageChatComponent_Template_button_touchend_41_listener", "MessageChatComponent_Template_button_touchcancel_41_listener", "MessageChatComponent_div_43_Template", "MessageChatComponent_Template_textarea_keydown_45_listener", "MessageChatComponent_Template_textarea_input_45_listener", "MessageChatComponent_Template_textarea_focus_45_listener", "MessageChatComponent_i_47_Template", "MessageChatComponent_div_48_Template", "MessageChatComponent_div_49_Template", "MessageChatComponent_div_50_Template", "MessageChatComponent_Template_input_change_51_listener", "MessageChatComponent_div_53_Template", "MessageChatComponent_div_54_Template"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\messages\\message-chat\\message-chat.component.ts", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\messages\\message-chat\\message-chat.component.html"], "sourcesContent": ["import {\n  Compo<PERSON>,\n  <PERSON><PERSON><PERSON><PERSON>,\n  <PERSON><PERSON><PERSON><PERSON>,\n  AfterViewInit,\n  ViewChild,\n  ElementRef,\n  ChangeDetectorRef,\n} from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { Subscription } from 'rxjs';\nimport { MessageService } from '../../../../services/message.service';\nimport { CallService } from '../../../../services/call.service';\nimport { ToastService } from '../../../../services/toast.service';\nimport { CallType, Call, IncomingCall } from '../../../../models/message.model';\n\n@Component({\n  selector: 'app-message-chat',\n  templateUrl: './message-chat.component.html',\n})\nexport class MessageChatComponent implements OnInit, OnDestroy {\n  // === RÉFÉRENCES DOM ===\n  @ViewChild('messagesContainer') private messagesContainer!: ElementRef;\n  @ViewChild('fileInput', { static: false })\n  fileInput!: ElementRef<HTMLInputElement>;\n  @ViewChild('localVideo', { static: false })\n  localVideo!: ElementRef<HTMLVideoElement>;\n  @ViewChild('remoteVideo', { static: false })\n  remoteVideo!: ElementRef<HTMLVideoElement>;\n\n  // === DONNÉES PRINCIPALES ===\n  conversation: any = null;\n  messages: any[] = [];\n  currentUserId: string | null = null;\n  currentUsername = 'You';\n  messageForm: FormGroup;\n  otherParticipant: any = null;\n\n  // === ÉTATS DE L'INTERFACE ===\n  isLoading = false;\n  isLoadingMore = false;\n  hasMoreMessages = true;\n  showEmojiPicker = false;\n  showAttachmentMenu = false;\n  showSearch = false;\n  searchQuery = '';\n  searchResults: any[] = [];\n  searchMode = false;\n  isSendingMessage = false;\n  otherUserIsTyping = false;\n  showMainMenu = false;\n  showMessageContextMenu = false;\n  selectedMessage: any = null;\n  contextMenuPosition = { x: 0, y: 0 };\n  showReactionPicker = false;\n  reactionPickerMessage: any = null;\n\n  showImageViewer = false;\n  selectedImage: any = null;\n  uploadProgress = 0;\n  isUploading = false;\n  isDragOver = false;\n\n  // === GESTION VOCALE OPTIMISÉE ===\n  isRecordingVoice = false;\n  voiceRecordingDuration = 0;\n  voiceRecordingState: 'idle' | 'recording' | 'processing' = 'idle';\n  private mediaRecorder: MediaRecorder | null = null;\n  private audioChunks: Blob[] = [];\n  private recordingTimer: any = null;\n  voiceWaves: number[] = [\n    4, 8, 12, 16, 20, 16, 12, 8, 4, 8, 12, 16, 20, 16, 12, 8,\n  ];\n\n  // Lecture des messages vocaux\n  private currentAudio: HTMLAudioElement | null = null;\n  private playingMessageId: string | null = null;\n  private voicePlayback: {\n    [messageId: string]: {\n      progress: number;\n      duration: number;\n      currentTime: number;\n      speed: number;\n    };\n  } = {};\n\n  // === APPELS WEBRTC ===\n  isInCall = false;\n  callType: 'VIDEO' | 'AUDIO' | null = null;\n  callDuration = 0;\n  private callTimer: any = null;\n\n  // État de l'appel WebRTC\n  activeCall: any = null;\n  isCallConnected = false;\n  isMuted = false;\n  isVideoEnabled = true;\n  localVideoElement: HTMLVideoElement | null = null;\n  remoteVideoElement: HTMLVideoElement | null = null;\n\n  // === ÉMOJIS ===\n  emojiCategories: any[] = [\n    {\n      id: 'smileys',\n      name: 'Smileys',\n      icon: '😀',\n      emojis: [\n        { emoji: '😀', name: 'grinning face' },\n        { emoji: '😃', name: 'grinning face with big eyes' },\n        { emoji: '😄', name: 'grinning face with smiling eyes' },\n        { emoji: '😁', name: 'beaming face with smiling eyes' },\n        { emoji: '😆', name: 'grinning squinting face' },\n        { emoji: '😅', name: 'grinning face with sweat' },\n        { emoji: '😂', name: 'face with tears of joy' },\n        { emoji: '🤣', name: 'rolling on the floor laughing' },\n        { emoji: '😊', name: 'smiling face with smiling eyes' },\n        { emoji: '😇', name: 'smiling face with halo' },\n      ],\n    },\n    {\n      id: 'people',\n      name: 'People',\n      icon: '👤',\n      emojis: [\n        { emoji: '👶', name: 'baby' },\n        { emoji: '🧒', name: 'child' },\n        { emoji: '👦', name: 'boy' },\n        { emoji: '👧', name: 'girl' },\n        { emoji: '🧑', name: 'person' },\n        { emoji: '👨', name: 'man' },\n        { emoji: '👩', name: 'woman' },\n        { emoji: '👴', name: 'old man' },\n        { emoji: '👵', name: 'old woman' },\n      ],\n    },\n    {\n      id: 'nature',\n      name: 'Nature',\n      icon: '🌿',\n      emojis: [\n        { emoji: '🐶', name: 'dog face' },\n        { emoji: '🐱', name: 'cat face' },\n        { emoji: '🐭', name: 'mouse face' },\n        { emoji: '🐹', name: 'hamster' },\n        { emoji: '🐰', name: 'rabbit face' },\n        { emoji: '🦊', name: 'fox' },\n        { emoji: '🐻', name: 'bear' },\n        { emoji: '🐼', name: 'panda' },\n      ],\n    },\n  ];\n  selectedEmojiCategory = this.emojiCategories[0];\n\n  // === PAGINATION ===\n  private readonly MAX_MESSAGES_TO_LOAD = 10;\n  private currentPage = 1;\n\n  // === AUTRES ÉTATS ===\n  isTyping = false;\n  isUserTyping = false;\n  private typingTimeout: any = null;\n  private subscriptions = new Subscription();\n\n  constructor(\n    private fb: FormBuilder,\n    private route: ActivatedRoute,\n    private router: Router,\n    private MessageService: MessageService,\n    private callService: CallService,\n    private toastService: ToastService,\n    private cdr: ChangeDetectorRef\n  ) {\n    this.messageForm = this.fb.group({\n      content: ['', [Validators.required, Validators.minLength(1)]],\n    });\n  }\n\n  // Méthode pour vérifier si le champ de saisie doit être désactivé\n  isInputDisabled(): boolean {\n    return (\n      !this.otherParticipant || this.isRecordingVoice || this.isSendingMessage\n    );\n  }\n\n  // Méthode pour gérer l'état du contrôle de saisie\n  private updateInputState(): void {\n    const contentControl = this.messageForm.get('content');\n    if (this.isInputDisabled()) {\n      contentControl?.disable();\n    } else {\n      contentControl?.enable();\n    }\n  }\n\n  ngOnInit(): void {\n    console.log('🚀 MessageChatComponent initialized');\n    this.initializeComponent();\n\n    // Activer les sons après interaction utilisateur\n    this.enableSoundsOnFirstInteraction();\n  }\n\n  private enableSoundsOnFirstInteraction(): void {\n    const enableSounds = () => {\n      this.callService.enableSounds();\n      document.removeEventListener('click', enableSounds);\n      document.removeEventListener('keydown', enableSounds);\n      document.removeEventListener('touchstart', enableSounds);\n    };\n\n    document.addEventListener('click', enableSounds, { once: true });\n    document.addEventListener('keydown', enableSounds, { once: true });\n    document.addEventListener('touchstart', enableSounds, { once: true });\n  }\n\n  private initializeComponent(): void {\n    this.loadCurrentUser();\n    this.loadConversation();\n    this.setupCallSubscriptions();\n  }\n\n  private setupCallSubscriptions(): void {\n    // S'abonner aux appels entrants\n    this.subscriptions.add(\n      this.callService.incomingCall$.subscribe({\n        next: (incomingCall) => {\n          if (incomingCall) {\n            console.log('📞 Incoming call received:', incomingCall);\n            this.handleIncomingCall(incomingCall);\n          }\n        },\n        error: (error) => {\n          console.error('❌ Error in incoming call subscription:', error);\n        },\n      })\n    );\n\n    // S'abonner aux changements d'état d'appel\n    this.subscriptions.add(\n      this.callService.activeCall$.subscribe({\n        next: (call) => {\n          if (call) {\n            console.log('📞 Active call updated:', call);\n            this.activeCall = call;\n          }\n        },\n        error: (error) => {\n          console.error('❌ Error in active call subscription:', error);\n        },\n      })\n    );\n  }\n\n  private handleIncomingCall(incomingCall: IncomingCall): void {\n    // Afficher une notification ou modal d'appel entrant\n    // Pour l'instant, on log juste\n    console.log(\n      '🔔 Handling incoming call from:',\n      incomingCall.caller.username\n    );\n\n    // Jouer la sonnerie\n    this.MessageService.play('ringtone');\n\n    // Ici on pourrait afficher une modal ou notification\n    // Pour l'instant, on accepte automatiquement pour tester\n    // this.acceptCall(incomingCall);\n  }\n\n  private loadCurrentUser(): void {\n    try {\n      const userString = localStorage.getItem('user');\n      console.log('🔍 Raw user from localStorage:', userString);\n\n      if (!userString || userString === 'null' || userString === 'undefined') {\n        console.error('❌ No user data in localStorage');\n        this.currentUserId = null;\n        this.currentUsername = 'You';\n        return;\n      }\n\n      const user = JSON.parse(userString);\n      console.log('🔍 Parsed user object:', user);\n\n      // Essayer différentes propriétés pour l'ID utilisateur\n      const userId = user._id || user.id || user.userId;\n      console.log('🔍 Trying to extract user ID:', {\n        _id: user._id,\n        id: user.id,\n        userId: user.userId,\n        extracted: userId,\n      });\n\n      if (userId) {\n        this.currentUserId = userId;\n        this.currentUsername = user.username || user.name || 'You';\n        console.log('✅ Current user loaded successfully:', {\n          id: this.currentUserId,\n          username: this.currentUsername,\n        });\n      } else {\n        console.error('❌ No valid user ID found in user object:', user);\n        this.currentUserId = null;\n        this.currentUsername = 'You';\n      }\n    } catch (error) {\n      console.error('❌ Error parsing user from localStorage:', error);\n      this.currentUserId = null;\n      this.currentUsername = 'You';\n    }\n  }\n\n  private loadConversation(): void {\n    const conversationId = this.route.snapshot.paramMap.get('id');\n    console.log('Loading conversation with ID:', conversationId);\n\n    if (!conversationId) {\n      this.toastService.showError('ID de conversation manquant');\n      return;\n    }\n\n    this.isLoading = true;\n    this.MessageService.getConversation(conversationId).subscribe({\n      next: (conversation) => {\n        console.log('🔍 Conversation loaded successfully:', conversation);\n        console.log('🔍 Conversation structure:', {\n          id: conversation?.id,\n          participants: conversation?.participants,\n          participantsCount: conversation?.participants?.length,\n          isGroup: conversation?.isGroup,\n          messages: conversation?.messages,\n          messagesCount: conversation?.messages?.length,\n        });\n        this.conversation = conversation;\n        this.setOtherParticipant();\n        this.loadMessages();\n\n        // Configurer les subscriptions temps réel après le chargement de la conversation\n        this.setupSubscriptions();\n      },\n      error: (error) => {\n        console.error('Erreur lors du chargement de la conversation:', error);\n        this.toastService.showError(\n          'Erreur lors du chargement de la conversation'\n        );\n        this.isLoading = false;\n      },\n    });\n  }\n\n  private setOtherParticipant(): void {\n    if (\n      !this.conversation?.participants ||\n      this.conversation.participants.length === 0\n    ) {\n      console.warn('No participants found in conversation');\n      this.otherParticipant = null;\n      return;\n    }\n\n    console.log('Setting other participant...');\n    console.log('Current user ID:', this.currentUserId);\n    console.log('All participants:', this.conversation.participants);\n\n    // Dans une conversation 1-à-1, on veut afficher l'autre personne (pas l'utilisateur actuel)\n    // Dans une conversation de groupe, on peut afficher le nom du groupe ou le premier autre participant\n\n    if (this.conversation.isGroup) {\n      // Pour les groupes, on pourrait afficher le nom du groupe\n      // Mais pour l'instant, on prend le premier participant qui n'est pas l'utilisateur actuel\n      this.otherParticipant = this.conversation.participants.find((p: any) => {\n        const participantId = p.id || p._id;\n        return String(participantId) !== String(this.currentUserId);\n      });\n    } else {\n      // Pour les conversations 1-à-1, on prend l'autre participant\n      this.otherParticipant = this.conversation.participants.find((p: any) => {\n        const participantId = p.id || p._id;\n        console.log(\n          'Comparing participant ID:',\n          participantId,\n          'with current user ID:',\n          this.currentUserId\n        );\n        return String(participantId) !== String(this.currentUserId);\n      });\n    }\n\n    // Fallback si aucun autre participant n'est trouvé\n    if (!this.otherParticipant && this.conversation.participants.length > 0) {\n      console.log('Fallback: using first participant');\n      this.otherParticipant = this.conversation.participants[0];\n\n      // Si le premier participant est l'utilisateur actuel et qu'il y en a d'autres\n      if (this.conversation.participants.length > 1) {\n        const firstParticipantId =\n          this.otherParticipant.id || this.otherParticipant._id;\n        if (String(firstParticipantId) === String(this.currentUserId)) {\n          console.log(\n            'First participant is current user, using second participant'\n          );\n          this.otherParticipant = this.conversation.participants[1];\n        }\n      }\n    }\n\n    // Vérification finale et logs\n    if (this.otherParticipant) {\n      console.log('✅ Other participant set successfully:', {\n        id: this.otherParticipant.id || this.otherParticipant._id,\n        username: this.otherParticipant.username,\n        image: this.otherParticipant.image,\n        isOnline: this.otherParticipant.isOnline,\n      });\n\n      // Log très visible pour debug\n      console.log(\n        '🎯 FINAL RESULT: otherParticipant =',\n        this.otherParticipant.username\n      );\n      console.log(\n        '🎯 Should display in sidebar:',\n        this.otherParticipant.username\n      );\n    } else {\n      console.error('❌ No other participant found! This should not happen.');\n      console.log('Conversation participants:', this.conversation.participants);\n      console.log('Current user ID:', this.currentUserId);\n\n      // Log très visible pour debug\n      console.log('🚨 ERROR: No otherParticipant found!');\n    }\n\n    // Mettre à jour l'état du champ de saisie\n    this.updateInputState();\n  }\n\n  private loadMessages(): void {\n    if (!this.conversation?.id) return;\n\n    // Les messages sont déjà chargés avec la conversation\n    let messages = this.conversation.messages || [];\n\n    // Trier les messages par timestamp (plus anciens en premier)\n    this.messages = messages.sort((a: any, b: any) => {\n      const dateA = new Date(a.timestamp || a.createdAt).getTime();\n      const dateB = new Date(b.timestamp || b.createdAt).getTime();\n      return dateA - dateB; // Ordre croissant (plus anciens en premier)\n    });\n\n    console.log('📋 Messages loaded and sorted:', {\n      total: this.messages.length,\n      first: this.messages[0]?.content,\n      last: this.messages[this.messages.length - 1]?.content,\n    });\n\n    this.hasMoreMessages = this.messages.length === this.MAX_MESSAGES_TO_LOAD;\n    this.isLoading = false;\n    this.scrollToBottom();\n  }\n\n  loadMoreMessages(): void {\n    if (this.isLoadingMore || !this.hasMoreMessages || !this.conversation?.id)\n      return;\n\n    this.isLoadingMore = true;\n    this.currentPage++;\n\n    // Calculer l'offset basé sur les messages déjà chargés\n    const offset = this.messages.length;\n\n    this.MessageService.getMessages(\n      this.currentUserId!, // senderId\n      this.otherParticipant?.id || this.otherParticipant?._id!, // receiverId\n      this.conversation.id,\n      this.currentPage,\n      this.MAX_MESSAGES_TO_LOAD\n    ).subscribe({\n      next: (newMessages: any[]) => {\n        if (newMessages && newMessages.length > 0) {\n          // Ajouter les nouveaux messages au début de la liste\n          this.messages = [...newMessages.reverse(), ...this.messages];\n          this.hasMoreMessages =\n            newMessages.length === this.MAX_MESSAGES_TO_LOAD;\n        } else {\n          this.hasMoreMessages = false;\n        }\n        this.isLoadingMore = false;\n      },\n      error: (error) => {\n        console.error('Erreur lors du chargement des messages:', error);\n        this.toastService.showError('Erreur lors du chargement des messages');\n        this.isLoadingMore = false;\n        this.currentPage--; // Revenir à la page précédente en cas d'erreur\n      },\n    });\n  }\n\n  private setupSubscriptions(): void {\n    if (!this.conversation?.id) {\n      console.warn('❌ Cannot setup subscriptions: no conversation ID');\n      return;\n    }\n\n    console.log(\n      '🔄 Setting up real-time subscriptions for conversation:',\n      this.conversation.id\n    );\n\n    // Subscription pour les nouveaux messages\n    console.log('📨 Setting up message subscription...');\n    this.subscriptions.add(\n      this.MessageService.subscribeToNewMessages(\n        this.conversation.id\n      ).subscribe({\n        next: (newMessage: any) => {\n          console.log('📨 New message received via subscription:', newMessage);\n          console.log('📨 Message structure:', {\n            id: newMessage.id,\n            type: newMessage.type,\n            content: newMessage.content,\n            sender: newMessage.sender,\n            senderId: newMessage.senderId,\n            receiverId: newMessage.receiverId,\n            attachments: newMessage.attachments,\n          });\n\n          // Debug des attachments\n          console.log(\n            '📨 [Debug] Message type detected:',\n            this.getMessageType(newMessage)\n          );\n          console.log('📨 [Debug] Has image:', this.hasImage(newMessage));\n          console.log('📨 [Debug] Has file:', this.hasFile(newMessage));\n          console.log('📨 [Debug] Image URL:', this.getImageUrl(newMessage));\n          if (newMessage.attachments) {\n            newMessage.attachments.forEach((att: any, index: number) => {\n              console.log(`📨 [Debug] Attachment ${index}:`, {\n                type: att.type,\n                url: att.url,\n                path: att.path,\n                name: att.name,\n                size: att.size,\n              });\n            });\n          }\n\n          // Ajouter le message à la liste s'il n'existe pas déjà\n          const messageExists = this.messages.some(\n            (msg) => msg.id === newMessage.id\n          );\n          if (!messageExists) {\n            // Ajouter le nouveau message à la fin (en bas)\n            this.messages.push(newMessage);\n            console.log(\n              '✅ Message added to list, total messages:',\n              this.messages.length\n            );\n\n            // Forcer la détection de changements\n            this.cdr.detectChanges();\n\n            // Scroll vers le bas après un court délai\n            setTimeout(() => {\n              this.scrollToBottom();\n            }, 50);\n\n            // Marquer comme lu si ce n'est pas notre message\n            const senderId = newMessage.sender?.id || newMessage.senderId;\n            console.log('📨 Checking if message should be marked as read:', {\n              senderId,\n              currentUserId: this.currentUserId,\n              shouldMarkAsRead: senderId !== this.currentUserId,\n            });\n\n            if (senderId && senderId !== this.currentUserId) {\n              this.markMessageAsRead(newMessage.id);\n            }\n          }\n        },\n        error: (error) => {\n          console.error('❌ Error in message subscription:', error);\n        },\n      })\n    );\n\n    // Subscription pour les indicateurs de frappe\n    console.log('📝 Setting up typing indicator subscription...');\n    this.subscriptions.add(\n      this.MessageService.subscribeToTypingIndicator(\n        this.conversation.id\n      ).subscribe({\n        next: (typingData: any) => {\n          console.log('📝 Typing indicator received:', typingData);\n\n          // Afficher l'indicateur seulement si c'est l'autre utilisateur qui tape\n          if (typingData.userId !== this.currentUserId) {\n            this.otherUserIsTyping = typingData.isTyping;\n            this.isUserTyping = typingData.isTyping; // Pour compatibilité avec le template\n            console.log(\n              '📝 Other user typing status updated:',\n              this.otherUserIsTyping\n            );\n            this.cdr.detectChanges();\n          }\n        },\n        error: (error: any) => {\n          console.error('❌ Error in typing subscription:', error);\n        },\n      })\n    );\n\n    // Subscription pour les mises à jour de conversation\n    this.subscriptions.add(\n      this.MessageService.subscribeToConversationUpdates(\n        this.conversation.id\n      ).subscribe({\n        next: (conversationUpdate: any) => {\n          console.log('📋 Conversation update:', conversationUpdate);\n\n          // Mettre à jour la conversation si nécessaire\n          if (conversationUpdate.id === this.conversation.id) {\n            this.conversation = { ...this.conversation, ...conversationUpdate };\n            this.cdr.detectChanges();\n          }\n        },\n        error: (error: any) => {\n          console.error('❌ Error in conversation subscription:', error);\n        },\n      })\n    );\n  }\n\n  private markMessageAsRead(messageId: string): void {\n    this.MessageService.markMessageAsRead(messageId).subscribe({\n      next: () => {\n        console.log('✅ Message marked as read:', messageId);\n      },\n      error: (error) => {\n        console.error('❌ Error marking message as read:', error);\n      },\n    });\n  }\n\n  // === ENVOI DE MESSAGES ===\n  sendMessage(): void {\n    if (!this.messageForm.valid || !this.conversation?.id) return;\n\n    const content = this.messageForm.get('content')?.value?.trim();\n    if (!content) return;\n\n    const receiverId = this.otherParticipant?.id || this.otherParticipant?._id;\n\n    if (!receiverId) {\n      this.toastService.showError('Destinataire introuvable');\n      return;\n    }\n\n    // Désactiver le bouton d'envoi\n    this.isSendingMessage = true;\n    this.updateInputState();\n\n    console.log('📤 Sending message:', {\n      content,\n      receiverId,\n      conversationId: this.conversation.id,\n    });\n\n    this.MessageService.sendMessage(\n      receiverId,\n      content,\n      undefined,\n      'TEXT' as any,\n      this.conversation.id\n    ).subscribe({\n      next: (message: any) => {\n        console.log('✅ Message sent successfully:', message);\n\n        // Ajouter le message à la liste s'il n'y est pas déjà\n        const messageExists = this.messages.some(\n          (msg) => msg.id === message.id\n        );\n        if (!messageExists) {\n          this.messages.push(message);\n          console.log(\n            '📋 Message added to local list, total:',\n            this.messages.length\n          );\n        }\n\n        // Réinitialiser le formulaire\n        this.messageForm.reset();\n        this.isSendingMessage = false;\n        this.updateInputState();\n\n        // Forcer la détection de changements et scroll\n        this.cdr.detectChanges();\n        setTimeout(() => {\n          this.scrollToBottom();\n        }, 50);\n      },\n      error: (error: any) => {\n        console.error(\"❌ Erreur lors de l'envoi du message:\", error);\n        this.toastService.showError(\"Erreur lors de l'envoi du message\");\n        this.isSendingMessage = false;\n        this.updateInputState();\n      },\n    });\n  }\n\n  scrollToBottom(): void {\n    setTimeout(() => {\n      if (this.messagesContainer) {\n        const element = this.messagesContainer.nativeElement;\n        element.scrollTop = element.scrollHeight;\n      }\n    }, 100);\n  }\n\n  // === MÉTHODES UTILITAIRES OPTIMISÉES ===\n  formatLastActive(lastActive: string | Date | null): string {\n    if (!lastActive) return 'Hors ligne';\n\n    const diffMins = Math.floor(\n      (Date.now() - new Date(lastActive).getTime()) / 60000\n    );\n\n    if (diffMins < 1) return \"À l'instant\";\n    if (diffMins < 60) return `Il y a ${diffMins} min`;\n    if (diffMins < 1440) return `Il y a ${Math.floor(diffMins / 60)}h`;\n    return `Il y a ${Math.floor(diffMins / 1440)}j`;\n  }\n\n  // Méthodes utilitaires pour les messages vocaux\n  getVoicePlaybackData(messageId: string) {\n    return (\n      this.voicePlayback[messageId] || {\n        progress: 0,\n        duration: 0,\n        currentTime: 0,\n        speed: 1,\n      }\n    );\n  }\n\n  private setVoicePlaybackData(\n    messageId: string,\n    data: Partial<(typeof this.voicePlayback)[string]>\n  ) {\n    this.voicePlayback[messageId] = {\n      ...this.getVoicePlaybackData(messageId),\n      ...data,\n    };\n  }\n\n  // === MÉTHODES POUR LES MESSAGES VOCAUX (TEMPLATE) ===\n\n  // === MÉTHODES POUR LES APPELS (TEMPLATE) ===\n\n  startVideoCall(): void {\n    if (!this.otherParticipant?.id) {\n      this.toastService.showError(\"Impossible de démarrer l'appel\");\n      return;\n    }\n\n    console.log('📹 Starting video call with:', this.otherParticipant.username);\n    this.initiateCall(CallType.VIDEO);\n  }\n\n  startVoiceCall(): void {\n    if (!this.otherParticipant?.id) {\n      this.toastService.showError(\"Impossible de démarrer l'appel\");\n      return;\n    }\n\n    console.log('📞 Starting voice call with:', this.otherParticipant.username);\n    this.initiateCall(CallType.AUDIO);\n  }\n\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // onCallAccepted, onCallRejected - définies plus loin\n\n  // === MÉTHODES POUR LES FICHIERS (TEMPLATE) ===\n\n  formatFileSize(bytes: number): string {\n    if (bytes < 1024) return bytes + ' B';\n    if (bytes < 1048576) return Math.round(bytes / 1024) + ' KB';\n    return Math.round(bytes / 1048576) + ' MB';\n  }\n\n  downloadFile(message: any): void {\n    const fileAttachment = message.attachments?.find(\n      (att: any) => !att.type?.startsWith('image/')\n    );\n    if (fileAttachment?.url) {\n      const link = document.createElement('a');\n      link.href = fileAttachment.url;\n      link.download = fileAttachment.name || 'file';\n      link.target = '_blank';\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      this.toastService.showSuccess('Téléchargement démarré');\n    }\n  }\n\n  // === MÉTHODES POUR L'INTERFACE UTILISATEUR (TEMPLATE) ===\n\n  toggleSearch(): void {\n    this.searchMode = !this.searchMode;\n    this.showSearch = this.searchMode;\n  }\n\n  toggleMainMenu(): void {\n    this.showMainMenu = !this.showMainMenu;\n  }\n\n  goBackToConversations(): void {\n    console.log('🔙 Going back to conversations');\n    // Naviguer vers la liste des conversations\n    this.router\n      .navigate(['/front/messages/conversations'])\n      .then(() => {\n        console.log('✅ Navigation to conversations successful');\n      })\n      .catch((error) => {\n        console.error('❌ Navigation error:', error);\n        // Fallback: essayer la route parent\n        this.router.navigate(['/front/messages']).catch(() => {\n          // Dernier recours: recharger la page\n          window.location.href = '/front/messages/conversations';\n        });\n      });\n  }\n\n  // === MÉTHODES POUR LES MENUS ET INTERACTIONS ===\n\n  closeAllMenus(): void {\n    this.showEmojiPicker = false;\n    this.showAttachmentMenu = false;\n    this.showMainMenu = false;\n    this.showMessageContextMenu = false;\n    this.showReactionPicker = false;\n  }\n\n  onMessageContextMenu(message: any, event: MouseEvent): void {\n    event.preventDefault();\n    this.selectedMessage = message;\n    this.contextMenuPosition = { x: event.clientX, y: event.clientY };\n    this.showMessageContextMenu = true;\n  }\n\n  showQuickReactions(message: any, event: MouseEvent): void {\n    event.stopPropagation();\n    this.reactionPickerMessage = message;\n    this.contextMenuPosition = { x: event.clientX, y: event.clientY };\n    this.showReactionPicker = true;\n  }\n\n  quickReact(emoji: string): void {\n    if (this.reactionPickerMessage) {\n      this.toggleReaction(this.reactionPickerMessage.id, emoji);\n    }\n    this.showReactionPicker = false;\n  }\n\n  toggleReaction(messageId: string, emoji: string): void {\n    console.log('👍 Toggling reaction:', emoji, 'for message:', messageId);\n\n    if (!messageId || !emoji) {\n      console.error('❌ Missing messageId or emoji for reaction');\n      return;\n    }\n\n    // Appeler le service pour ajouter/supprimer la réaction\n    this.MessageService.reactToMessage(messageId, emoji).subscribe({\n      next: (result) => {\n        console.log('✅ Reaction toggled successfully:', result);\n\n        // Mettre à jour le message local avec les nouvelles réactions\n        const messageIndex = this.messages.findIndex(\n          (msg) => msg.id === messageId\n        );\n        if (messageIndex !== -1) {\n          this.messages[messageIndex] = result;\n          this.cdr.detectChanges();\n        }\n\n        this.toastService.showSuccess(`Réaction ${emoji} ajoutée`);\n      },\n      error: (error) => {\n        console.error('❌ Error toggling reaction:', error);\n        this.toastService.showError(\"Erreur lors de l'ajout de la réaction\");\n      },\n    });\n  }\n\n  hasUserReacted(reaction: any, userId: string): boolean {\n    return reaction.userId === userId;\n  }\n\n  replyToMessage(message: any): void {\n    console.log('↩️ Replying to message:', message.id);\n    this.closeAllMenus();\n  }\n\n  forwardMessage(message: any): void {\n    console.log('➡️ Forwarding message:', message.id);\n    this.closeAllMenus();\n  }\n\n  deleteMessage(message: any): void {\n    console.log('🗑️ Deleting message:', message.id);\n\n    if (!message.id) {\n      console.error('❌ No message ID provided for deletion');\n      this.toastService.showError('Erreur: ID du message manquant');\n      return;\n    }\n\n    // Vérifier si l'utilisateur peut supprimer ce message\n    const canDelete =\n      message.sender?.id === this.currentUserId ||\n      message.senderId === this.currentUserId;\n\n    if (!canDelete) {\n      this.toastService.showError(\n        'Vous ne pouvez supprimer que vos propres messages'\n      );\n      this.closeAllMenus();\n      return;\n    }\n\n    // Demander confirmation\n    if (!confirm('Êtes-vous sûr de vouloir supprimer ce message ?')) {\n      this.closeAllMenus();\n      return;\n    }\n\n    // Appeler le service pour supprimer le message\n    this.MessageService.deleteMessage(message.id).subscribe({\n      next: (result) => {\n        console.log('✅ Message deleted successfully:', result);\n\n        // Supprimer le message de la liste locale\n        this.messages = this.messages.filter((msg) => msg.id !== message.id);\n\n        this.toastService.showSuccess('Message supprimé');\n        this.cdr.detectChanges();\n      },\n      error: (error) => {\n        console.error('❌ Error deleting message:', error);\n        this.toastService.showError('Erreur lors de la suppression du message');\n      },\n    });\n\n    this.closeAllMenus();\n  }\n\n  // === MÉTHODES POUR LES ÉMOJIS ET PIÈCES JOINTES ===\n\n  toggleEmojiPicker(): void {\n    this.showEmojiPicker = !this.showEmojiPicker;\n  }\n\n  selectEmojiCategory(category: any): void {\n    this.selectedEmojiCategory = category;\n  }\n\n  getEmojisForCategory(category: any): any[] {\n    return category?.emojis || [];\n  }\n\n  insertEmoji(emoji: any): void {\n    const currentContent = this.messageForm.get('content')?.value || '';\n    const newContent = currentContent + emoji.emoji;\n    this.messageForm.patchValue({ content: newContent });\n    this.showEmojiPicker = false;\n  }\n\n  toggleAttachmentMenu(): void {\n    this.showAttachmentMenu = !this.showAttachmentMenu;\n  }\n\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // triggerFileInput, getFileAcceptTypes, onFileSelected - définies plus loin\n\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // Ces méthodes sont déjà définies plus loin dans le fichier\n\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // handleTypingIndicator - définie plus loin\n\n  // === MÉTHODES UTILITAIRES POUR LE TEMPLATE ===\n\n  trackByMessageId(index: number, message: any): string {\n    return message.id || message._id || index.toString();\n  }\n\n  // === MÉTHODES MANQUANTES POUR LE TEMPLATE ===\n\n  testAddMessage(): void {\n    console.log('🧪 Test: Adding message');\n    const testMessage = {\n      id: `test-${Date.now()}`,\n      content: `Message de test ${new Date().toLocaleTimeString()}`,\n      timestamp: new Date().toISOString(),\n      sender: {\n        id: this.otherParticipant?.id || 'test-user',\n        username: this.otherParticipant?.username || 'Test User',\n        image:\n          this.otherParticipant?.image || 'assets/images/default-avatar.png',\n      },\n      type: 'TEXT',\n      isRead: false,\n    };\n    this.messages.push(testMessage);\n    this.cdr.detectChanges();\n    setTimeout(() => this.scrollToBottom(), 50);\n  }\n\n  isGroupConversation(): boolean {\n    return (\n      this.conversation?.isGroup ||\n      this.conversation?.participants?.length > 2 ||\n      false\n    );\n  }\n\n  openCamera(): void {\n    console.log('📷 Opening camera');\n    this.showAttachmentMenu = false;\n    // TODO: Implémenter l'ouverture de la caméra\n  }\n\n  zoomImage(factor: number): void {\n    const imageElement = document.querySelector(\n      '.image-viewer-zoom'\n    ) as HTMLElement;\n    if (imageElement) {\n      const currentTransform = imageElement.style.transform || 'scale(1)';\n      const currentScale = parseFloat(\n        currentTransform.match(/scale\\(([^)]+)\\)/)?.[1] || '1'\n      );\n      const newScale = Math.max(0.5, Math.min(3, currentScale * factor));\n      imageElement.style.transform = `scale(${newScale})`;\n      if (newScale > 1) {\n        imageElement.classList.add('zoomed');\n      } else {\n        imageElement.classList.remove('zoomed');\n      }\n    }\n  }\n\n  resetZoom(): void {\n    const imageElement = document.querySelector(\n      '.image-viewer-zoom'\n    ) as HTMLElement;\n    if (imageElement) {\n      imageElement.style.transform = 'scale(1)';\n      imageElement.classList.remove('zoomed');\n    }\n  }\n\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // formatFileSize, downloadFile, toggleReaction, hasUserReacted, showQuickReactions\n  // toggleEmojiPicker, toggleAttachmentMenu, selectEmojiCategory, getEmojisForCategory, insertEmoji\n  // Ces méthodes sont déjà définies plus loin dans le fichier\n\n  triggerFileInput(type?: string): void {\n    const input = this.fileInput?.nativeElement;\n    if (!input) {\n      console.error('File input element not found');\n      return;\n    }\n\n    // Configurer le type de fichier accepté\n    if (type === 'image') {\n      input.accept = 'image/*';\n    } else if (type === 'video') {\n      input.accept = 'video/*';\n    } else if (type === 'document') {\n      input.accept = '.pdf,.doc,.docx,.xls,.xlsx,.txt';\n    } else {\n      input.accept = '*/*';\n    }\n\n    // Réinitialiser la valeur pour permettre la sélection du même fichier\n    input.value = '';\n\n    // Déclencher la sélection de fichier\n    input.click();\n    this.showAttachmentMenu = false;\n  }\n\n  formatMessageTime(timestamp: string | Date): string {\n    if (!timestamp) return '';\n\n    const date = new Date(timestamp);\n    return date.toLocaleTimeString('fr-FR', {\n      hour: '2-digit',\n      minute: '2-digit',\n    });\n  }\n\n  formatDateSeparator(timestamp: string | Date): string {\n    if (!timestamp) return '';\n\n    const date = new Date(timestamp);\n    const today = new Date();\n    const yesterday = new Date(today);\n    yesterday.setDate(yesterday.getDate() - 1);\n\n    if (date.toDateString() === today.toDateString()) {\n      return \"Aujourd'hui\";\n    } else if (date.toDateString() === yesterday.toDateString()) {\n      return 'Hier';\n    } else {\n      return date.toLocaleDateString('fr-FR');\n    }\n  }\n\n  formatMessageContent(content: string): string {\n    if (!content) return '';\n\n    // Remplacer les URLs par des liens\n    const urlRegex = /(https?:\\/\\/[^\\s]+)/g;\n    return content.replace(\n      urlRegex,\n      '<a href=\"$1\" target=\"_blank\" class=\"text-blue-500 underline\">$1</a>'\n    );\n  }\n\n  shouldShowDateSeparator(index: number): boolean {\n    if (index === 0) return true;\n\n    const currentMessage = this.messages[index];\n    const previousMessage = this.messages[index - 1];\n\n    if (!currentMessage?.timestamp || !previousMessage?.timestamp) return false;\n\n    const currentDate = new Date(currentMessage.timestamp).toDateString();\n    const previousDate = new Date(previousMessage.timestamp).toDateString();\n\n    return currentDate !== previousDate;\n  }\n\n  shouldShowAvatar(index: number): boolean {\n    const currentMessage = this.messages[index];\n    const nextMessage = this.messages[index + 1];\n\n    if (!nextMessage) return true;\n\n    return currentMessage.sender?.id !== nextMessage.sender?.id;\n  }\n\n  shouldShowSenderName(index: number): boolean {\n    const currentMessage = this.messages[index];\n    const previousMessage = this.messages[index - 1];\n\n    if (!previousMessage) return true;\n\n    return currentMessage.sender?.id !== previousMessage.sender?.id;\n  }\n\n  getMessageType(message: any): string {\n    // Vérifier d'abord le type de message explicite\n    if (message.type) {\n      if (message.type === 'IMAGE' || message.type === 'image') return 'image';\n      if (message.type === 'VIDEO' || message.type === 'video') return 'video';\n      if (message.type === 'AUDIO' || message.type === 'audio') return 'audio';\n      if (message.type === 'VOICE_MESSAGE') return 'audio';\n      if (message.type === 'FILE' || message.type === 'file') return 'file';\n    }\n\n    // Ensuite vérifier les attachments\n    if (message.attachments && message.attachments.length > 0) {\n      const attachment = message.attachments[0];\n      if (attachment.type?.startsWith('image/')) return 'image';\n      if (attachment.type?.startsWith('video/')) return 'video';\n      if (attachment.type?.startsWith('audio/')) return 'audio';\n      return 'file';\n    }\n\n    // Vérifier si c'est un message vocal basé sur les propriétés\n    if (message.voiceUrl || message.audioUrl || message.voice) return 'audio';\n\n    return 'text';\n  }\n\n  hasImage(message: any): boolean {\n    // Vérifier le type de message\n    if (message.type === 'IMAGE' || message.type === 'image') {\n      return true;\n    }\n\n    // Vérifier les attachments\n    const hasImageAttachment =\n      message.attachments?.some((att: any) => {\n        return att.type?.startsWith('image/') || att.type === 'IMAGE';\n      }) || false;\n\n    // Vérifier les propriétés directes d'image\n    const hasImageUrl = !!(message.imageUrl || message.image);\n\n    return hasImageAttachment || hasImageUrl;\n  }\n\n  hasFile(message: any): boolean {\n    // Vérifier le type de message\n    if (message.type === 'FILE' || message.type === 'file') {\n      return true;\n    }\n\n    // Vérifier les attachments non-image\n    const hasFileAttachment =\n      message.attachments?.some((att: any) => {\n        return !att.type?.startsWith('image/') && att.type !== 'IMAGE';\n      }) || false;\n\n    return hasFileAttachment;\n  }\n\n  getImageUrl(message: any): string {\n    // Vérifier les propriétés directes d'image\n    if (message.imageUrl) {\n      return message.imageUrl;\n    }\n    if (message.image) {\n      return message.image;\n    }\n\n    // Vérifier les attachments\n    const imageAttachment = message.attachments?.find(\n      (att: any) => att.type?.startsWith('image/') || att.type === 'IMAGE'\n    );\n\n    if (imageAttachment) {\n      return imageAttachment.url || imageAttachment.path || '';\n    }\n\n    return '';\n  }\n\n  getFileName(message: any): string {\n    const fileAttachment = message.attachments?.find(\n      (att: any) => !att.type?.startsWith('image/')\n    );\n    return fileAttachment?.name || 'Fichier';\n  }\n\n  getFileSize(message: any): string {\n    const fileAttachment = message.attachments?.find(\n      (att: any) => !att.type?.startsWith('image/')\n    );\n    if (!fileAttachment?.size) return '';\n\n    const bytes = fileAttachment.size;\n    if (bytes < 1024) return bytes + ' B';\n    if (bytes < 1048576) return Math.round(bytes / 1024) + ' KB';\n    return Math.round(bytes / 1048576) + ' MB';\n  }\n\n  getFileIcon(message: any): string {\n    const fileAttachment = message.attachments?.find(\n      (att: any) => !att.type?.startsWith('image/')\n    );\n    if (!fileAttachment?.type) return 'fas fa-file';\n\n    if (fileAttachment.type.startsWith('audio/')) return 'fas fa-file-audio';\n    if (fileAttachment.type.startsWith('video/')) return 'fas fa-file-video';\n    if (fileAttachment.type.includes('pdf')) return 'fas fa-file-pdf';\n    if (fileAttachment.type.includes('word')) return 'fas fa-file-word';\n    if (fileAttachment.type.includes('excel')) return 'fas fa-file-excel';\n    return 'fas fa-file';\n  }\n\n  getUserColor(userId: string): string {\n    // Générer une couleur basée sur l'ID utilisateur\n    const colors = [\n      '#FF6B6B',\n      '#4ECDC4',\n      '#45B7D1',\n      '#96CEB4',\n      '#FFEAA7',\n      '#DDA0DD',\n      '#98D8C8',\n    ];\n    const index = userId.charCodeAt(0) % colors.length;\n    return colors[index];\n  }\n\n  // === MÉTHODES D'INTERACTION ===\n  onMessageClick(message: any, event: any): void {\n    console.log('Message clicked:', message);\n  }\n\n  onInputChange(event: any): void {\n    // Gérer les changements dans le champ de saisie\n    this.handleTypingIndicator();\n  }\n\n  onInputKeyDown(event: KeyboardEvent): void {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n\n  onInputFocus(): void {\n    // Gérer le focus sur le champ de saisie\n  }\n\n  onInputBlur(): void {\n    // Gérer la perte de focus sur le champ de saisie\n  }\n\n  onScroll(event: any): void {\n    // Gérer le scroll pour charger plus de messages\n    const element = event.target;\n    if (\n      element.scrollTop === 0 &&\n      this.hasMoreMessages &&\n      !this.isLoadingMore\n    ) {\n      this.loadMoreMessages();\n    }\n  }\n\n  openUserProfile(userId: string): void {\n    console.log('Opening user profile for:', userId);\n  }\n\n  onImageLoad(event: any, message: any): void {\n    console.log(\n      '🖼️ [Debug] Image loaded successfully for message:',\n      message.id,\n      event.target.src\n    );\n  }\n\n  onImageError(event: any, message: any): void {\n    console.error('🖼️ [Debug] Image failed to load for message:', message.id, {\n      src: event.target.src,\n      error: event,\n    });\n    // Optionnel : afficher une image de remplacement\n    event.target.src =\n      'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjNmNGY2Ii8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzZiNzI4MCIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkltYWdlIG5vbiBkaXNwb25pYmxlPC90ZXh0Pjwvc3ZnPg==';\n  }\n\n  openImageViewer(message: any): void {\n    const imageAttachment = message.attachments?.find((att: any) =>\n      att.type?.startsWith('image/')\n    );\n    if (imageAttachment?.url) {\n      this.selectedImage = {\n        url: imageAttachment.url,\n        name: imageAttachment.name || 'Image',\n        size: this.formatFileSize(imageAttachment.size || 0),\n        message: message,\n      };\n      this.showImageViewer = true;\n      console.log('🖼️ [ImageViewer] Opening image:', this.selectedImage);\n    }\n  }\n\n  closeImageViewer(): void {\n    this.showImageViewer = false;\n    this.selectedImage = null;\n    console.log('🖼️ [ImageViewer] Closed');\n  }\n\n  downloadImage(): void {\n    if (this.selectedImage?.url) {\n      const link = document.createElement('a');\n      link.href = this.selectedImage.url;\n      link.download = this.selectedImage.name || 'image';\n      link.target = '_blank';\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      this.toastService.showSuccess('Téléchargement démarré');\n      console.log(\n        '🖼️ [ImageViewer] Download started:',\n        this.selectedImage.name\n      );\n    }\n  }\n\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // zoomImage, resetZoom, formatFileSize, downloadFile, toggleReaction, hasUserReacted, toggleSearch\n  // Ces méthodes sont déjà définies plus loin dans le fichier\n\n  searchMessages(): void {\n    if (!this.searchQuery.trim()) {\n      this.searchResults = [];\n      return;\n    }\n\n    this.searchResults = this.messages.filter(\n      (message) =>\n        message.content\n          ?.toLowerCase()\n          .includes(this.searchQuery.toLowerCase()) ||\n        message.sender?.username\n          ?.toLowerCase()\n          .includes(this.searchQuery.toLowerCase())\n    );\n  }\n\n  onSearchQueryChange(): void {\n    this.searchMessages();\n  }\n\n  clearSearch(): void {\n    this.searchQuery = '';\n    this.searchResults = [];\n  }\n\n  jumpToMessage(messageId: string): void {\n    const messageElement = document.getElementById(`message-${messageId}`);\n    if (messageElement) {\n      messageElement.scrollIntoView({ behavior: 'smooth', block: 'center' });\n      // Highlight temporairement le message\n      messageElement.classList.add('highlight');\n      setTimeout(() => {\n        messageElement.classList.remove('highlight');\n      }, 2000);\n    }\n  }\n\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // toggleMainMenu, toggleTheme, onMessageContextMenu\n  // Ces méthodes sont déjà définies plus loin dans le fichier\n\n  closeContextMenu(): void {\n    this.showMessageContextMenu = false;\n    this.selectedMessage = null;\n  }\n\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // replyToMessage, forwardMessage, deleteMessage, showQuickReactions, closeReactionPicker\n  // quickReact, closeAllMenus, testAddMessage, toggleAttachmentMenu, toggleEmojiPicker\n  // Ces méthodes sont déjà définies plus loin dans le fichier\n\n  // === MÉTHODE SUPPRIMÉE (DUPLICATION) ===\n  // triggerFileInput - définie plus loin\n\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // openCamera, getEmojisForCategory, selectEmojiCategory, insertEmoji\n  // goBackToConversations, startVideoCall, startVoiceCall\n  // Ces méthodes sont déjà définies plus loin dans le fichier\n\n  private initiateCall(callType: CallType): void {\n    console.log('🚀 [MessageChat] Starting call initiation process...');\n    console.log('📋 [MessageChat] Call details:', {\n      callType,\n      otherParticipant: this.otherParticipant,\n      conversation: this.conversation?.id,\n      currentUserId: this.currentUserId,\n    });\n\n    if (!this.otherParticipant) {\n      console.error('❌ [MessageChat] No recipient selected');\n      this.toastService.showError('Aucun destinataire sélectionné');\n      return;\n    }\n\n    const recipientId = this.otherParticipant.id || this.otherParticipant._id;\n    if (!recipientId) {\n      console.error('❌ [MessageChat] Recipient ID not found');\n      this.toastService.showError('ID du destinataire introuvable');\n      return;\n    }\n\n    console.log(`📞 [MessageChat] Initiating ${callType} call to user:`, {\n      recipientId,\n      recipientName:\n        this.otherParticipant.username || this.otherParticipant.name,\n      conversationId: this.conversation?.id,\n    });\n\n    this.isInCall = true;\n    this.callType = callType === CallType.VIDEO ? 'VIDEO' : 'AUDIO';\n    this.callDuration = 0;\n\n    // Démarrer le timer d'appel\n    this.startCallTimer();\n\n    console.log('🔄 [MessageChat] Calling CallService.initiateCall...');\n\n    // Utiliser le CallService\n    this.callService\n      .initiateCall(recipientId, callType, this.conversation?.id)\n      .subscribe({\n        next: (call: Call) => {\n          console.log('✅ [MessageChat] Call initiated successfully:', {\n            callId: call.id,\n            callType: call.type,\n            callStatus: call.status,\n            caller: call.caller?.username,\n            recipient: call.recipient?.username,\n            conversationId: call.conversationId,\n          });\n\n          this.activeCall = call;\n          this.isCallConnected = false;\n          this.toastService.showSuccess(\n            `Appel ${callType === CallType.VIDEO ? 'vidéo' : 'audio'} initié`\n          );\n\n          console.log(\n            '📡 [MessageChat] Call should now be sent to recipient via WebSocket'\n          );\n        },\n        error: (error) => {\n          console.error('❌ [MessageChat] Error initiating call:', {\n            error: error.message || error,\n            recipientId,\n            callType,\n            conversationId: this.conversation?.id,\n          });\n\n          this.endCall();\n          this.toastService.showError(\"Erreur lors de l'initiation de l'appel\");\n        },\n      });\n  }\n\n  acceptCall(incomingCall: IncomingCall): void {\n    console.log('🔄 Accepting incoming call:', incomingCall);\n\n    this.callService.acceptCall(incomingCall).subscribe({\n      next: (call: Call) => {\n        console.log('✅ Call accepted successfully:', call);\n        this.activeCall = call;\n        this.isInCall = true;\n        this.isCallConnected = true;\n        this.callType = call.type === CallType.VIDEO ? 'VIDEO' : 'AUDIO';\n        this.startCallTimer();\n        this.toastService.showSuccess('Appel accepté');\n      },\n      error: (error) => {\n        console.error('❌ Error accepting call:', error);\n        this.toastService.showError(\"Erreur lors de l'acceptation de l'appel\");\n      },\n    });\n  }\n\n  rejectCall(incomingCall: IncomingCall): void {\n    console.log('🔄 Rejecting incoming call:', incomingCall);\n\n    this.callService.rejectCall(incomingCall.id, 'User rejected').subscribe({\n      next: () => {\n        console.log('✅ Call rejected successfully');\n        this.toastService.showSuccess('Appel rejeté');\n      },\n      error: (error) => {\n        console.error('❌ Error rejecting call:', error);\n        this.toastService.showError(\"Erreur lors du rejet de l'appel\");\n      },\n    });\n  }\n\n  // === MÉTHODE CORRIGÉE POUR TERMINER L'APPEL ===\n  endCall(): void {\n    console.log('📞 [MessageChat] Ending call...');\n\n    if (this.activeCall?.id) {\n      this.callService.endCall(this.activeCall.id).subscribe({\n        next: () => {\n          console.log('✅ [MessageChat] Call ended successfully');\n          this.resetCallState();\n        },\n        error: (error) => {\n          console.error('❌ [MessageChat] Error ending call:', error);\n          this.resetCallState(); // Reset même en cas d'erreur\n        },\n      });\n    } else {\n      console.log('🔄 [MessageChat] No active call to end, resetting state');\n      this.resetCallState();\n    }\n  }\n\n  private startCallTimer(): void {\n    this.callDuration = 0;\n    this.callTimer = setInterval(() => {\n      this.callDuration++;\n      this.cdr.detectChanges();\n    }, 1000);\n  }\n\n  private resetCallState(): void {\n    if (this.callTimer) {\n      clearInterval(this.callTimer);\n      this.callTimer = null;\n    }\n\n    this.isInCall = false;\n    this.callType = null;\n    this.callDuration = 0;\n    this.activeCall = null;\n    this.isCallConnected = false;\n    this.isMuted = false;\n    this.isVideoEnabled = true;\n  }\n\n  // === CONTRÔLES D'APPEL ===\n  toggleMute(): void {\n    if (!this.activeCall) return;\n\n    this.isMuted = !this.isMuted;\n\n    // Utiliser la méthode toggleMedia du CallService\n    this.callService\n      .toggleMedia(\n        this.activeCall.id,\n        undefined, // video unchanged\n        !this.isMuted // audio state\n      )\n      .subscribe({\n        next: () => {\n          this.toastService.showSuccess(\n            this.isMuted ? 'Micro coupé' : 'Micro activé'\n          );\n        },\n        error: (error) => {\n          console.error('❌ Error toggling mute:', error);\n          // Revert state on error\n          this.isMuted = !this.isMuted;\n          this.toastService.showError('Erreur lors du changement du micro');\n        },\n      });\n  }\n\n  toggleVideo(): void {\n    if (!this.activeCall) return;\n\n    this.isVideoEnabled = !this.isVideoEnabled;\n\n    // Utiliser la méthode toggleMedia du CallService\n    this.callService\n      .toggleMedia(\n        this.activeCall.id,\n        this.isVideoEnabled, // video state\n        undefined // audio unchanged\n      )\n      .subscribe({\n        next: () => {\n          this.toastService.showSuccess(\n            this.isVideoEnabled ? 'Caméra activée' : 'Caméra désactivée'\n          );\n        },\n        error: (error) => {\n          console.error('❌ Error toggling video:', error);\n          // Revert state on error\n          this.isVideoEnabled = !this.isVideoEnabled;\n          this.toastService.showError('Erreur lors du changement de la caméra');\n        },\n      });\n  }\n\n  formatCallDuration(duration: number): string {\n    const hours = Math.floor(duration / 3600);\n    const minutes = Math.floor((duration % 3600) / 60);\n    const seconds = duration % 60;\n\n    if (hours > 0) {\n      return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds\n        .toString()\n        .padStart(2, '0')}`;\n    }\n    return `${minutes}:${seconds.toString().padStart(2, '0')}`;\n  }\n\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // trackByMessageId, isGroupConversation - Ces méthodes sont déjà définies plus loin dans le fichier\n\n  async startVoiceRecording(): Promise<void> {\n    console.log('🎤 [Voice] Starting voice recording...');\n\n    try {\n      // Vérifier le support du navigateur\n      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {\n        throw new Error(\n          \"Votre navigateur ne supporte pas l'enregistrement audio\"\n        );\n      }\n\n      // Vérifier si MediaRecorder est supporté\n      if (!window.MediaRecorder) {\n        throw new Error(\n          \"MediaRecorder n'est pas supporté par votre navigateur\"\n        );\n      }\n\n      console.log('🎤 [Voice] Requesting microphone access...');\n\n      // Demander l'accès au microphone avec des contraintes optimisées\n      const stream = await navigator.mediaDevices.getUserMedia({\n        audio: {\n          echoCancellation: true,\n          noiseSuppression: true,\n          autoGainControl: true,\n          sampleRate: 44100,\n          channelCount: 1,\n        },\n      });\n\n      console.log('🎤 [Voice] Microphone access granted');\n\n      // Vérifier les types MIME supportés\n      let mimeType = 'audio/webm;codecs=opus';\n      if (!MediaRecorder.isTypeSupported(mimeType)) {\n        mimeType = 'audio/webm';\n        if (!MediaRecorder.isTypeSupported(mimeType)) {\n          mimeType = 'audio/mp4';\n          if (!MediaRecorder.isTypeSupported(mimeType)) {\n            mimeType = ''; // Laisser le navigateur choisir\n          }\n        }\n      }\n\n      console.log('🎤 [Voice] Using MIME type:', mimeType);\n\n      // Créer le MediaRecorder\n      this.mediaRecorder = new MediaRecorder(stream, {\n        mimeType: mimeType || undefined,\n      });\n\n      // Initialiser les variables\n      this.audioChunks = [];\n      this.isRecordingVoice = true;\n      this.voiceRecordingDuration = 0;\n      this.voiceRecordingState = 'recording';\n\n      console.log('🎤 [Voice] MediaRecorder created, starting timer...');\n\n      // Démarrer le timer\n      this.recordingTimer = setInterval(() => {\n        this.voiceRecordingDuration++;\n        // Animer les waves\n        this.animateVoiceWaves();\n        this.cdr.detectChanges();\n      }, 1000);\n\n      // Gérer les événements du MediaRecorder\n      this.mediaRecorder.ondataavailable = (event) => {\n        console.log('🎤 [Voice] Data available:', event.data.size, 'bytes');\n        if (event.data.size > 0) {\n          this.audioChunks.push(event.data);\n        }\n      };\n\n      this.mediaRecorder.onstop = () => {\n        console.log('🎤 [Voice] MediaRecorder stopped, processing audio...');\n        this.processRecordedAudio();\n      };\n\n      this.mediaRecorder.onerror = (event: any) => {\n        console.error('🎤 [Voice] MediaRecorder error:', event.error);\n        this.toastService.showError(\"Erreur lors de l'enregistrement\");\n        this.cancelVoiceRecording();\n      };\n\n      // Démarrer l'enregistrement\n      this.mediaRecorder.start(100); // Collecter les données toutes les 100ms\n      console.log('🎤 [Voice] Recording started successfully');\n\n      this.toastService.showSuccess('🎤 Enregistrement vocal démarré');\n    } catch (error: any) {\n      console.error('🎤 [Voice] Error starting recording:', error);\n\n      let errorMessage = \"Impossible de démarrer l'enregistrement vocal\";\n\n      if (error.name === 'NotAllowedError') {\n        errorMessage =\n          \"Accès au microphone refusé. Veuillez autoriser l'accès dans les paramètres de votre navigateur.\";\n      } else if (error.name === 'NotFoundError') {\n        errorMessage =\n          'Aucun microphone détecté. Veuillez connecter un microphone.';\n      } else if (error.name === 'NotSupportedError') {\n        errorMessage =\n          \"Votre navigateur ne supporte pas l'enregistrement audio.\";\n      } else if (error.message) {\n        errorMessage = error.message;\n      }\n\n      this.toastService.showError(errorMessage);\n      this.cancelVoiceRecording();\n    }\n  }\n\n  stopVoiceRecording(): void {\n    if (this.mediaRecorder && this.mediaRecorder.state === 'recording') {\n      this.mediaRecorder.stop();\n      this.mediaRecorder.stream.getTracks().forEach((track) => track.stop());\n    }\n\n    if (this.recordingTimer) {\n      clearInterval(this.recordingTimer);\n      this.recordingTimer = null;\n    }\n\n    this.isRecordingVoice = false;\n    this.voiceRecordingState = 'processing';\n  }\n\n  cancelVoiceRecording(): void {\n    if (this.mediaRecorder) {\n      if (this.mediaRecorder.state === 'recording') {\n        this.mediaRecorder.stop();\n      }\n      this.mediaRecorder.stream.getTracks().forEach((track) => track.stop());\n      this.mediaRecorder = null;\n    }\n\n    if (this.recordingTimer) {\n      clearInterval(this.recordingTimer);\n      this.recordingTimer = null;\n    }\n\n    this.isRecordingVoice = false;\n    this.voiceRecordingDuration = 0;\n    this.voiceRecordingState = 'idle';\n    this.audioChunks = [];\n  }\n\n  private async processRecordedAudio(): Promise<void> {\n    console.log('🎤 [Voice] Processing recorded audio...');\n\n    try {\n      // Vérifier qu'on a des données audio\n      if (this.audioChunks.length === 0) {\n        console.error('🎤 [Voice] No audio chunks available');\n        this.toastService.showError('Aucun audio enregistré');\n        this.cancelVoiceRecording();\n        return;\n      }\n\n      console.log(\n        '🎤 [Voice] Audio chunks:',\n        this.audioChunks.length,\n        'Duration:',\n        this.voiceRecordingDuration\n      );\n\n      // Vérifier la durée minimale\n      if (this.voiceRecordingDuration < 1) {\n        console.error(\n          '🎤 [Voice] Recording too short:',\n          this.voiceRecordingDuration\n        );\n        this.toastService.showError(\n          'Enregistrement trop court (minimum 1 seconde)'\n        );\n        this.cancelVoiceRecording();\n        return;\n      }\n\n      // Déterminer le type MIME du blob\n      let mimeType = 'audio/webm;codecs=opus';\n      if (this.mediaRecorder?.mimeType) {\n        mimeType = this.mediaRecorder.mimeType;\n      }\n\n      console.log('🎤 [Voice] Creating audio blob with MIME type:', mimeType);\n\n      // Créer le blob audio\n      const audioBlob = new Blob(this.audioChunks, {\n        type: mimeType,\n      });\n\n      console.log('🎤 [Voice] Audio blob created:', {\n        size: audioBlob.size,\n        type: audioBlob.type,\n      });\n\n      // Déterminer l'extension du fichier\n      let extension = '.webm';\n      if (mimeType.includes('mp4')) {\n        extension = '.mp4';\n      } else if (mimeType.includes('wav')) {\n        extension = '.wav';\n      } else if (mimeType.includes('ogg')) {\n        extension = '.ogg';\n      }\n\n      // Créer le fichier\n      const audioFile = new File(\n        [audioBlob],\n        `voice_${Date.now()}${extension}`,\n        {\n          type: mimeType,\n        }\n      );\n\n      console.log('🎤 [Voice] Audio file created:', {\n        name: audioFile.name,\n        size: audioFile.size,\n        type: audioFile.type,\n      });\n\n      // Envoyer le message vocal\n      this.voiceRecordingState = 'processing';\n      await this.sendVoiceMessage(audioFile);\n\n      console.log('🎤 [Voice] Voice message sent successfully');\n      this.toastService.showSuccess('🎤 Message vocal envoyé');\n    } catch (error: any) {\n      console.error('🎤 [Voice] Error processing audio:', error);\n      this.toastService.showError(\n        \"Erreur lors de l'envoi du message vocal: \" +\n          (error.message || 'Erreur inconnue')\n      );\n    } finally {\n      // Nettoyer l'état\n      this.voiceRecordingState = 'idle';\n      this.voiceRecordingDuration = 0;\n      this.audioChunks = [];\n      this.isRecordingVoice = false;\n\n      console.log('🎤 [Voice] Audio processing completed, state reset');\n    }\n  }\n\n  private async sendVoiceMessage(audioFile: File): Promise<void> {\n    const receiverId = this.otherParticipant?.id || this.otherParticipant?._id;\n\n    if (!receiverId) {\n      throw new Error('Destinataire introuvable');\n    }\n\n    return new Promise((resolve, reject) => {\n      this.MessageService.sendMessage(\n        receiverId,\n        '',\n        audioFile,\n        'AUDIO' as any,\n        this.conversation.id\n      ).subscribe({\n        next: (message: any) => {\n          this.messages.push(message);\n          this.scrollToBottom();\n          resolve();\n        },\n        error: (error: any) => {\n          console.error(\"Erreur lors de l'envoi du message vocal:\", error);\n          reject(error);\n        },\n      });\n    });\n  }\n\n  formatRecordingDuration(duration: number): string {\n    const minutes = Math.floor(duration / 60);\n    const seconds = duration % 60;\n    return `${minutes}:${seconds.toString().padStart(2, '0')}`;\n  }\n\n  // === MÉTHODES D'ENREGISTREMENT VOCAL AMÉLIORÉES ===\n\n  onRecordStart(event: Event): void {\n    event.preventDefault();\n    console.log('🎤 [Voice] Record start triggered');\n    console.log('🎤 [Voice] Current state:', {\n      isRecordingVoice: this.isRecordingVoice,\n      voiceRecordingState: this.voiceRecordingState,\n      voiceRecordingDuration: this.voiceRecordingDuration,\n      mediaRecorder: !!this.mediaRecorder,\n    });\n\n    // Vérifier si on peut enregistrer\n    if (this.voiceRecordingState === 'processing') {\n      console.log('🎤 [Voice] Already processing, ignoring start');\n      this.toastService.showWarning('Traitement en cours...');\n      return;\n    }\n\n    if (this.isRecordingVoice) {\n      console.log('🎤 [Voice] Already recording, ignoring start');\n      this.toastService.showWarning('Enregistrement déjà en cours...');\n      return;\n    }\n\n    // Afficher un message de début\n    this.toastService.showInfo(\"🎤 Démarrage de l'enregistrement vocal...\");\n\n    // Démarrer l'enregistrement\n    this.startVoiceRecording().catch((error) => {\n      console.error('🎤 [Voice] Failed to start recording:', error);\n      this.toastService.showError(\n        \"Impossible de démarrer l'enregistrement vocal: \" +\n          (error.message || 'Erreur inconnue')\n      );\n    });\n  }\n\n  onRecordEnd(event: Event): void {\n    event.preventDefault();\n    console.log('🎤 [Voice] Record end triggered');\n\n    if (!this.isRecordingVoice) {\n      console.log('🎤 [Voice] Not recording, ignoring end');\n      return;\n    }\n\n    // Arrêter l'enregistrement et envoyer\n    this.stopVoiceRecording();\n  }\n\n  onRecordCancel(event: Event): void {\n    event.preventDefault();\n    console.log('🎤 [Voice] Record cancel triggered');\n\n    if (!this.isRecordingVoice) {\n      console.log('🎤 [Voice] Not recording, ignoring cancel');\n      return;\n    }\n\n    // Annuler l'enregistrement\n    this.cancelVoiceRecording();\n  }\n\n  getRecordingFormat(): string {\n    if (this.mediaRecorder?.mimeType) {\n      if (this.mediaRecorder.mimeType.includes('webm')) return 'WebM';\n      if (this.mediaRecorder.mimeType.includes('mp4')) return 'MP4';\n      if (this.mediaRecorder.mimeType.includes('wav')) return 'WAV';\n      if (this.mediaRecorder.mimeType.includes('ogg')) return 'OGG';\n    }\n    return 'Auto';\n  }\n\n  // === ANIMATION DES WAVES VOCALES ===\n\n  private animateVoiceWaves(): void {\n    // Animer les waves pendant l'enregistrement\n    this.voiceWaves = this.voiceWaves.map(() => {\n      return Math.floor(Math.random() * 20) + 4; // Hauteur entre 4 et 24px\n    });\n  }\n\n  onFileSelected(event: any): void {\n    console.log('📁 [Upload] File selection triggered');\n    const files = event.target.files;\n\n    if (!files || files.length === 0) {\n      console.log('📁 [Upload] No files selected');\n      return;\n    }\n\n    console.log(`📁 [Upload] ${files.length} file(s) selected:`, files);\n\n    for (let file of files) {\n      console.log(\n        `📁 [Upload] Processing file: ${file.name}, size: ${file.size}, type: ${file.type}`\n      );\n      this.uploadFile(file);\n    }\n  }\n\n  private uploadFile(file: File): void {\n    console.log(`📁 [Upload] Starting upload for file: ${file.name}`);\n\n    const receiverId = this.otherParticipant?.id || this.otherParticipant?._id;\n\n    if (!receiverId) {\n      console.error('📁 [Upload] No receiver ID found');\n      this.toastService.showError('Destinataire introuvable');\n      return;\n    }\n\n    console.log(`📁 [Upload] Receiver ID: ${receiverId}`);\n\n    // Vérifier la taille du fichier (max 50MB)\n    const maxSize = 50 * 1024 * 1024; // 50MB\n    if (file.size > maxSize) {\n      console.error(`📁 [Upload] File too large: ${file.size} bytes`);\n      this.toastService.showError('Fichier trop volumineux (max 50MB)');\n      return;\n    }\n\n    // 🖼️ Compression d'image si nécessaire\n    if (file.type.startsWith('image/') && file.size > 1024 * 1024) {\n      // > 1MB\n      console.log(\n        '🖼️ [Compression] Compressing image:',\n        file.name,\n        'Original size:',\n        file.size\n      );\n      this.compressImage(file)\n        .then((compressedFile) => {\n          console.log(\n            '🖼️ [Compression] ✅ Image compressed successfully. New size:',\n            compressedFile.size\n          );\n          this.sendFileToServer(compressedFile, receiverId);\n        })\n        .catch((error) => {\n          console.error('🖼️ [Compression] ❌ Error compressing image:', error);\n          // Envoyer le fichier original en cas d'erreur\n          this.sendFileToServer(file, receiverId);\n        });\n      return;\n    }\n\n    // Envoyer le fichier sans compression\n    this.sendFileToServer(file, receiverId);\n  }\n\n  private sendFileToServer(file: File, receiverId: string): void {\n    const messageType = this.getFileMessageType(file);\n    console.log(`📁 [Upload] Message type determined: ${messageType}`);\n    console.log(`📁 [Upload] Conversation ID: ${this.conversation.id}`);\n\n    this.isSendingMessage = true;\n    this.isUploading = true;\n    this.uploadProgress = 0;\n    console.log('📁 [Upload] Calling MessageService.sendMessage...');\n\n    // Simuler la progression d'upload\n    const progressInterval = setInterval(() => {\n      this.uploadProgress += Math.random() * 15;\n      if (this.uploadProgress >= 90) {\n        clearInterval(progressInterval);\n      }\n      this.cdr.detectChanges();\n    }, 300);\n\n    this.MessageService.sendMessage(\n      receiverId,\n      '',\n      file,\n      messageType,\n      this.conversation.id\n    ).subscribe({\n      next: (message: any) => {\n        console.log('📁 [Upload] ✅ File sent successfully:', message);\n        console.log('📁 [Debug] Sent message structure:', {\n          id: message.id,\n          type: message.type,\n          attachments: message.attachments,\n          hasImage: this.hasImage(message),\n          hasFile: this.hasFile(message),\n          imageUrl: this.getImageUrl(message),\n        });\n\n        clearInterval(progressInterval);\n        this.uploadProgress = 100;\n\n        setTimeout(() => {\n          this.messages.push(message);\n          this.scrollToBottom();\n          this.toastService.showSuccess('Fichier envoyé avec succès');\n          this.resetUploadState();\n        }, 500);\n      },\n      error: (error: any) => {\n        console.error('📁 [Upload] ❌ Error sending file:', error);\n        clearInterval(progressInterval);\n        this.toastService.showError(\"Erreur lors de l'envoi du fichier\");\n        this.resetUploadState();\n      },\n    });\n  }\n\n  private getFileMessageType(file: File): any {\n    if (file.type.startsWith('image/')) return 'IMAGE' as any;\n    if (file.type.startsWith('video/')) return 'VIDEO' as any;\n    if (file.type.startsWith('audio/')) return 'AUDIO' as any;\n    return 'FILE' as any;\n  }\n\n  getFileAcceptTypes(): string {\n    return '*/*';\n  }\n\n  resetUploadState(): void {\n    this.isSendingMessage = false;\n    this.isUploading = false;\n    this.uploadProgress = 0;\n  }\n\n  // === DRAG & DROP ===\n\n  onDragOver(event: DragEvent): void {\n    event.preventDefault();\n    event.stopPropagation();\n    this.isDragOver = true;\n  }\n\n  onDragLeave(event: DragEvent): void {\n    event.preventDefault();\n    event.stopPropagation();\n    // Vérifier si on quitte vraiment la zone (pas un enfant)\n    const rect = (event.currentTarget as HTMLElement).getBoundingClientRect();\n    const x = event.clientX;\n    const y = event.clientY;\n\n    if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {\n      this.isDragOver = false;\n    }\n  }\n\n  onDrop(event: DragEvent): void {\n    event.preventDefault();\n    event.stopPropagation();\n    this.isDragOver = false;\n\n    const files = event.dataTransfer?.files;\n    if (files && files.length > 0) {\n      console.log('📁 [Drag&Drop] Files dropped:', files.length);\n\n      // Traiter chaque fichier\n      Array.from(files).forEach((file) => {\n        console.log(\n          '📁 [Drag&Drop] Processing file:',\n          file.name,\n          file.type,\n          file.size\n        );\n        this.uploadFile(file);\n      });\n\n      this.toastService.showSuccess(\n        `${files.length} fichier(s) en cours d'envoi`\n      );\n    }\n  }\n\n  // === COMPRESSION D'IMAGES ===\n\n  private compressImage(file: File, quality: number = 0.8): Promise<File> {\n    return new Promise((resolve, reject) => {\n      const canvas = document.createElement('canvas');\n      const ctx = canvas.getContext('2d');\n      const img = new Image();\n\n      img.onload = () => {\n        // Calculer les nouvelles dimensions (max 1920x1080)\n        const maxWidth = 1920;\n        const maxHeight = 1080;\n        let { width, height } = img;\n\n        if (width > maxWidth || height > maxHeight) {\n          const ratio = Math.min(maxWidth / width, maxHeight / height);\n          width *= ratio;\n          height *= ratio;\n        }\n\n        canvas.width = width;\n        canvas.height = height;\n\n        // Dessiner l'image redimensionnée\n        ctx?.drawImage(img, 0, 0, width, height);\n\n        // Convertir en blob avec compression\n        canvas.toBlob(\n          (blob) => {\n            if (blob) {\n              const compressedFile = new File([blob], file.name, {\n                type: file.type,\n                lastModified: Date.now(),\n              });\n              resolve(compressedFile);\n            } else {\n              reject(new Error('Failed to compress image'));\n            }\n          },\n          file.type,\n          quality\n        );\n      };\n\n      img.onerror = () => reject(new Error('Failed to load image'));\n      img.src = URL.createObjectURL(file);\n    });\n  }\n\n  // === MÉTHODES DE FORMATAGE SUPPLÉMENTAIRES ===\n\n  private handleTypingIndicator(): void {\n    if (!this.isTyping) {\n      this.isTyping = true;\n      // Envoyer l'indicateur de frappe à l'autre utilisateur\n      this.sendTypingIndicator(true);\n    }\n\n    // Reset le timer\n    if (this.typingTimeout) {\n      clearTimeout(this.typingTimeout);\n    }\n\n    this.typingTimeout = setTimeout(() => {\n      this.isTyping = false;\n      // Arrêter l'indicateur de frappe\n      this.sendTypingIndicator(false);\n    }, 2000);\n  }\n\n  private sendTypingIndicator(isTyping: boolean): void {\n    // Envoyer l'indicateur de frappe via WebSocket/GraphQL\n    const receiverId = this.otherParticipant?.id || this.otherParticipant?._id;\n    if (receiverId && this.conversation?.id) {\n      console.log(\n        `📝 Sending typing indicator: ${isTyping} to user ${receiverId}`\n      );\n      // TODO: Implémenter l'envoi via WebSocket/GraphQL subscription\n      // this.MessageService.sendTypingIndicator(this.conversation.id, receiverId, isTyping);\n    }\n  }\n\n  // === MÉTHODES POUR L'INTERFACE D'APPEL ===\n\n  onCallAccepted(call: Call): void {\n    console.log('🔄 Call accepted from interface:', call);\n    this.activeCall = call;\n    this.isInCall = true;\n    this.isCallConnected = true;\n    this.startCallTimer();\n    this.toastService.showSuccess('Appel accepté');\n  }\n\n  onCallRejected(): void {\n    console.log('🔄 Call rejected from interface');\n    this.endCall();\n    this.toastService.showInfo('Appel rejeté');\n  }\n\n  // === MÉTHODES POUR LA LECTURE DES MESSAGES VOCAUX ===\n\n  playVoiceMessage(message: any): void {\n    console.log('🎵 [Voice] Playing voice message:', message.id);\n    this.toggleVoicePlayback(message);\n  }\n\n  isVoicePlaying(messageId: string): boolean {\n    return this.playingMessageId === messageId;\n  }\n\n  toggleVoicePlayback(message: any): void {\n    const messageId = message.id;\n    const audioUrl = this.getVoiceUrl(message);\n\n    if (!audioUrl) {\n      console.error('🎵 [Voice] No audio URL found for message:', messageId);\n      this.toastService.showError('Fichier audio introuvable');\n      return;\n    }\n\n    // Si c'est déjà en cours de lecture, arrêter\n    if (this.isVoicePlaying(messageId)) {\n      this.stopVoicePlayback();\n      return;\n    }\n\n    // Arrêter toute autre lecture en cours\n    this.stopVoicePlayback();\n\n    // Démarrer la nouvelle lecture\n    this.startVoicePlayback(message, audioUrl);\n  }\n\n  private startVoicePlayback(message: any, audioUrl: string): void {\n    const messageId = message.id;\n\n    try {\n      console.log(\n        '🎵 [Voice] Starting playback for:',\n        messageId,\n        'URL:',\n        audioUrl\n      );\n\n      this.currentAudio = new Audio(audioUrl);\n      this.playingMessageId = messageId;\n\n      // Initialiser les valeurs par défaut avec la nouvelle structure\n      const currentData = this.getVoicePlaybackData(messageId);\n      this.setVoicePlaybackData(messageId, {\n        progress: 0,\n        currentTime: 0,\n        speed: currentData.speed || 1,\n        duration: currentData.duration || 0,\n      });\n\n      // Configurer la vitesse de lecture\n      this.currentAudio.playbackRate = currentData.speed || 1;\n\n      // Événements audio\n      this.currentAudio.addEventListener('loadedmetadata', () => {\n        if (this.currentAudio) {\n          this.setVoicePlaybackData(messageId, {\n            duration: this.currentAudio.duration,\n          });\n          console.log(\n            '🎵 [Voice] Audio loaded, duration:',\n            this.currentAudio.duration\n          );\n        }\n      });\n\n      this.currentAudio.addEventListener('timeupdate', () => {\n        if (this.currentAudio && this.playingMessageId === messageId) {\n          const currentTime = this.currentAudio.currentTime;\n          const progress = (currentTime / this.currentAudio.duration) * 100;\n          this.setVoicePlaybackData(messageId, { currentTime, progress });\n          this.cdr.detectChanges();\n        }\n      });\n\n      this.currentAudio.addEventListener('ended', () => {\n        console.log('🎵 [Voice] Playback ended for:', messageId);\n        this.stopVoicePlayback();\n      });\n\n      this.currentAudio.addEventListener('error', (error) => {\n        console.error('🎵 [Voice] Audio error:', error);\n        this.toastService.showError('Erreur lors de la lecture audio');\n        this.stopVoicePlayback();\n      });\n\n      // Démarrer la lecture\n      this.currentAudio\n        .play()\n        .then(() => {\n          console.log('🎵 [Voice] Playback started successfully');\n          this.toastService.showSuccess('🎵 Lecture du message vocal');\n        })\n        .catch((error) => {\n          console.error('🎵 [Voice] Error starting playback:', error);\n          this.toastService.showError('Impossible de lire le message vocal');\n          this.stopVoicePlayback();\n        });\n    } catch (error) {\n      console.error('🎵 [Voice] Error creating audio:', error);\n      this.toastService.showError('Erreur lors de la lecture audio');\n      this.stopVoicePlayback();\n    }\n  }\n\n  private stopVoicePlayback(): void {\n    if (this.currentAudio) {\n      console.log('🎵 [Voice] Stopping playback for:', this.playingMessageId);\n      this.currentAudio.pause();\n      this.currentAudio.currentTime = 0;\n      this.currentAudio = null;\n    }\n    this.playingMessageId = null;\n    this.cdr.detectChanges();\n  }\n\n  getVoiceUrl(message: any): string {\n    // Vérifier les propriétés directes d'audio\n    if (message.voiceUrl) return message.voiceUrl;\n    if (message.audioUrl) return message.audioUrl;\n    if (message.voice) return message.voice;\n\n    // Vérifier les attachments audio\n    const audioAttachment = message.attachments?.find(\n      (att: any) => att.type?.startsWith('audio/') || att.type === 'AUDIO'\n    );\n\n    if (audioAttachment) {\n      return audioAttachment.url || audioAttachment.path || '';\n    }\n\n    return '';\n  }\n\n  getVoiceWaves(message: any): number[] {\n    // Générer des waves basées sur l'ID du message pour la cohérence\n    const messageId = message.id || '';\n    const seed = messageId\n      .split('')\n      .reduce((acc: number, char: string) => acc + char.charCodeAt(0), 0);\n    const waves: number[] = [];\n\n    for (let i = 0; i < 16; i++) {\n      const height = 4 + ((seed + i * 7) % 20);\n      waves.push(height);\n    }\n\n    return waves;\n  }\n\n  getVoiceProgress(message: any): number {\n    const data = this.getVoicePlaybackData(message.id);\n    const totalWaves = 16;\n    return Math.floor((data.progress / 100) * totalWaves);\n  }\n\n  getVoiceCurrentTime(message: any): string {\n    const data = this.getVoicePlaybackData(message.id);\n    return this.formatAudioTime(data.currentTime);\n  }\n\n  getVoiceDuration(message: any): string {\n    const data = this.getVoicePlaybackData(message.id);\n    const duration = data.duration || message.metadata?.duration || 0;\n\n    if (typeof duration === 'string') {\n      return duration; // Déjà formaté\n    }\n\n    return this.formatAudioTime(duration);\n  }\n\n  private formatAudioTime(seconds: number): string {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = Math.floor(seconds % 60);\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  }\n\n  seekVoiceMessage(message: any, waveIndex: number): void {\n    const messageId = message.id;\n\n    if (!this.currentAudio || this.playingMessageId !== messageId) {\n      return;\n    }\n\n    const totalWaves = 16;\n    const seekPercentage = (waveIndex / totalWaves) * 100;\n    const seekTime = (seekPercentage / 100) * this.currentAudio.duration;\n\n    this.currentAudio.currentTime = seekTime;\n    console.log('🎵 [Voice] Seeking to:', seekTime, 'seconds');\n  }\n\n  toggleVoiceSpeed(message: any): void {\n    const messageId = message.id;\n    const data = this.getVoicePlaybackData(messageId);\n\n    // Cycle entre 1x, 1.5x, 2x\n    const newSpeed = data.speed === 1 ? 1.5 : data.speed === 1.5 ? 2 : 1;\n\n    this.setVoicePlaybackData(messageId, { speed: newSpeed });\n\n    if (this.currentAudio && this.playingMessageId === messageId) {\n      this.currentAudio.playbackRate = newSpeed;\n    }\n\n    this.toastService.showSuccess(`Vitesse: ${newSpeed}x`);\n  }\n\n  // === MÉTHODES MANQUANTES POUR LE TEMPLATE ===\n\n  changeVoiceSpeed(message: any): void {\n    this.toggleVoiceSpeed(message);\n  }\n\n  getVoiceSpeed(message: any): number {\n    const data = this.getVoicePlaybackData(message.id);\n    return data.speed || 1;\n  }\n\n  ngOnDestroy(): void {\n    this.subscriptions.unsubscribe();\n\n    // Nettoyer les timers\n    if (this.callTimer) {\n      clearInterval(this.callTimer);\n    }\n    if (this.recordingTimer) {\n      clearInterval(this.recordingTimer);\n    }\n    if (this.typingTimeout) {\n      clearTimeout(this.typingTimeout);\n    }\n\n    // Nettoyer les ressources audio\n    if (this.mediaRecorder) {\n      if (this.mediaRecorder.state === 'recording') {\n        this.mediaRecorder.stop();\n      }\n      this.mediaRecorder.stream?.getTracks().forEach((track) => track.stop());\n    }\n\n    // Nettoyer la lecture audio\n    this.stopVoicePlayback();\n  }\n}\n", "<!-- ===== MESSAGE CHAT COMPONENT - REORGANIZED & OPTIMIZED ===== -->\n\n<!-- Éléments vidéo cachés pour WebRTC -->\n<video #localVideo autoplay muted playsinline style=\"display: none\"></video>\n<video #remoteVideo autoplay playsinline style=\"display: none\"></video>\n\n<div\n  style=\"\n    display: flex;\n    flex-direction: column;\n    height: 100vh;\n    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);\n    color: #1f2937;\n    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;\n  \"\n>\n  <!-- ===== ANIMATIONS CSS ===== -->\n  <style>\n    @keyframes pulse {\n      0%,\n      100% {\n        opacity: 1;\n      }\n      50% {\n        opacity: 0.5;\n      }\n    }\n    @keyframes bounce {\n      0%,\n      20%,\n      53%,\n      80%,\n      100% {\n        transform: translateY(0);\n      }\n      40%,\n      43% {\n        transform: translateY(-8px);\n      }\n      70% {\n        transform: translateY(-4px);\n      }\n    }\n    @keyframes spin {\n      from {\n        transform: rotate(0deg);\n      }\n      to {\n        transform: rotate(360deg);\n      }\n    }\n    @keyframes ping {\n      75%,\n      100% {\n        transform: scale(2);\n        opacity: 0;\n      }\n    }\n    @keyframes slideInUp {\n      from {\n        transform: translateX(-50%) translateY(20px);\n        opacity: 0;\n      }\n      to {\n        transform: translateX(-50%) translateY(0);\n        opacity: 1;\n      }\n    }\n  </style>\n\n  <!-- ===== HEADER SECTION ===== -->\n  <header\n    style=\"\n      display: flex;\n      align-items: center;\n      padding: 12px 16px;\n      background: #ffffff;\n      border-bottom: 1px solid #e5e7eb;\n      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n      position: relative;\n      z-index: 10;\n    \"\n  >\n    <!-- Bouton retour -->\n    <button\n      (click)=\"goBackToConversations()\"\n      style=\"\n        padding: 10px;\n        margin-right: 12px;\n        border-radius: 50%;\n        border: none;\n        background: transparent;\n        cursor: pointer;\n        transition: all 0.2s ease;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        min-width: 40px;\n        min-height: 40px;\n      \"\n      onmouseover=\"this.style.background='#f3f4f6'; this.style.transform='scale(1.05)'\"\n      onmouseout=\"this.style.background='transparent'; this.style.transform='scale(1)'\"\n      title=\"Retour aux conversations\"\n    >\n      <i\n        class=\"fas fa-arrow-left\"\n        style=\"color: #374151; font-size: 18px; font-weight: bold\"\n      ></i>\n    </button>\n\n    <!-- Info utilisateur -->\n    <div style=\"display: flex; align-items: center; flex: 1; min-width: 0\">\n      <!-- Avatar avec statut -->\n      <div style=\"position: relative; margin-right: 12px\">\n        <img\n          [src]=\"otherParticipant?.image || 'assets/images/default-avatar.png'\"\n          [alt]=\"otherParticipant?.username\"\n          style=\"\n            width: 40px;\n            height: 40px;\n            border-radius: 50%;\n            object-fit: cover;\n            border: 2px solid transparent;\n            cursor: pointer;\n            transition: transform 0.2s ease;\n          \"\n          (click)=\"openUserProfile(otherParticipant?.id!)\"\n          onmouseover=\"this.style.transform='scale(1.05)'\"\n          onmouseout=\"this.style.transform='scale(1)'\"\n          title=\"Voir le profil\"\n        />\n        <!-- Indicateur en ligne -->\n        <div\n          *ngIf=\"otherParticipant?.isOnline\"\n          style=\"\n            position: absolute;\n            bottom: 0;\n            right: 0;\n            width: 12px;\n            height: 12px;\n            background: #10b981;\n            border: 2px solid transparent;\n            border-radius: 50%;\n            animation: pulse 2s infinite;\n          \"\n        ></div>\n      </div>\n\n      <!-- Nom et statut -->\n      <div style=\"flex: 1; min-width: 0\">\n        <h3\n          style=\"\n            font-weight: 600;\n            color: #111827;\n            margin: 0;\n            font-size: 16px;\n            white-space: nowrap;\n            overflow: hidden;\n            text-overflow: ellipsis;\n          \"\n        >\n          {{ otherParticipant?.username || \"Utilisateur\" }}\n        </h3>\n        <div style=\"font-size: 14px; color: #6b7280; margin-top: 2px\">\n          <!-- Indicateur de frappe -->\n          <div\n            *ngIf=\"isUserTyping\"\n            style=\"display: flex; align-items: center; gap: 4px; color: #10b981\"\n          >\n            <span>En train d'écrire</span>\n            <div style=\"display: flex; gap: 2px\">\n              <div\n                style=\"\n                  width: 4px;\n                  height: 4px;\n                  background: #10b981;\n                  border-radius: 50%;\n                  animation: bounce 1s infinite;\n                \"\n              ></div>\n              <div\n                style=\"\n                  width: 4px;\n                  height: 4px;\n                  background: #10b981;\n                  border-radius: 50%;\n                  animation: bounce 1s infinite 0.1s;\n                \"\n              ></div>\n              <div\n                style=\"\n                  width: 4px;\n                  height: 4px;\n                  background: #10b981;\n                  border-radius: 50%;\n                  animation: bounce 1s infinite 0.2s;\n                \"\n              ></div>\n            </div>\n          </div>\n          <!-- Statut en ligne -->\n          <span *ngIf=\"!isUserTyping\">\n            {{\n              otherParticipant?.isOnline\n                ? \"En ligne\"\n                : formatLastActive(otherParticipant?.lastActive)\n            }}\n          </span>\n        </div>\n      </div>\n    </div>\n\n    <!-- Actions -->\n    <div style=\"display: flex; align-items: center; gap: 8px\">\n      <!-- Appel vidéo -->\n      <button\n        (click)=\"startVideoCall()\"\n        style=\"\n          padding: 10px;\n          border-radius: 50%;\n          border: none;\n          background: linear-gradient(135deg, #3b82f6, #1d4ed8);\n          color: white;\n          cursor: pointer;\n          transition: all 0.3s;\n          box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);\n          width: 40px;\n          height: 40px;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n        \"\n        title=\"Appel vidéo\"\n        onmouseover=\"this.style.transform='scale(1.1)'; this.style.boxShadow='0 6px 20px rgba(59, 130, 246, 0.4)'\"\n        onmouseout=\"this.style.transform='scale(1)'; this.style.boxShadow='0 4px 12px rgba(59, 130, 246, 0.3)'\"\n      >\n        <i class=\"fas fa-video\" style=\"font-size: 14px\"></i>\n      </button>\n\n      <!-- Appel vocal -->\n      <button\n        (click)=\"startVoiceCall()\"\n        style=\"\n          padding: 10px;\n          border-radius: 50%;\n          border: none;\n          background: linear-gradient(135deg, #10b981, #047857);\n          color: white;\n          cursor: pointer;\n          transition: all 0.3s;\n          box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);\n          width: 40px;\n          height: 40px;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n        \"\n        title=\"Appel vocal\"\n        onmouseover=\"this.style.transform='scale(1.1)'; this.style.boxShadow='0 6px 20px rgba(16, 185, 129, 0.4)'\"\n        onmouseout=\"this.style.transform='scale(1)'; this.style.boxShadow='0 4px 12px rgba(16, 185, 129, 0.3)'\"\n      >\n        <i class=\"fas fa-phone\" style=\"font-size: 14px\"></i>\n      </button>\n\n      <!-- Recherche -->\n      <button\n        (click)=\"toggleSearch()\"\n        style=\"\n          padding: 8px;\n          border-radius: 50%;\n          border: none;\n          background: transparent;\n          color: #6b7280;\n          cursor: pointer;\n          transition: all 0.2s;\n        \"\n        [style.background]=\"searchMode ? '#dcfce7' : 'transparent'\"\n        [style.color]=\"searchMode ? '#16a34a' : '#6b7280'\"\n        title=\"Rechercher\"\n      >\n        <i class=\"fas fa-search\"></i>\n      </button>\n\n      <!-- Menu principal -->\n      <button\n        (click)=\"toggleMainMenu()\"\n        style=\"\n          padding: 8px;\n          border-radius: 50%;\n          border: none;\n          background: transparent;\n          color: #6b7280;\n          cursor: pointer;\n          transition: all 0.2s;\n          position: relative;\n        \"\n        [style.background]=\"showMainMenu ? '#dcfce7' : 'transparent'\"\n        [style.color]=\"showMainMenu ? '#16a34a' : '#6b7280'\"\n        title=\"Menu\"\n      >\n        <i class=\"fas fa-ellipsis-v\"></i>\n      </button>\n    </div>\n\n    <!-- Menu dropdown -->\n    <div\n      *ngIf=\"showMainMenu\"\n      style=\"\n        position: absolute;\n        top: 64px;\n        right: 16px;\n        background: #ffffff;\n        border-radius: 16px;\n        box-shadow: 0 20px 25px rgba(0, 0, 0, 0.1);\n        border: 1px solid #e5e7eb;\n        z-index: 50;\n        min-width: 192px;\n      \"\n    >\n      <div style=\"padding: 8px\">\n        <button\n          (click)=\"toggleSearch(); showMainMenu = false\"\n          style=\"\n            width: 100%;\n            display: flex;\n            align-items: center;\n            gap: 12px;\n            padding: 8px 12px;\n            border-radius: 8px;\n            border: none;\n            background: transparent;\n            cursor: pointer;\n            transition: all 0.2s;\n            text-align: left;\n          \"\n          onmouseover=\"this.style.background='#f3f4f6'\"\n          onmouseout=\"this.style.background='transparent'\"\n        >\n          <i class=\"fas fa-search\" style=\"color: #3b82f6\"></i>\n          <span style=\"color: #374151\">Rechercher</span>\n        </button>\n        <button\n          style=\"\n            width: 100%;\n            display: flex;\n            align-items: center;\n            gap: 12px;\n            padding: 8px 12px;\n            border-radius: 8px;\n            border: none;\n            background: transparent;\n            cursor: pointer;\n            transition: all 0.2s;\n            text-align: left;\n          \"\n          onmouseover=\"this.style.background='#f3f4f6'\"\n          onmouseout=\"this.style.background='transparent'\"\n        >\n          <i class=\"fas fa-user\" style=\"color: #10b981\"></i>\n          <span style=\"color: #374151\">Voir le profil</span>\n        </button>\n        <hr style=\"margin: 8px 0; border-color: #e5e7eb\" />\n        <button\n          style=\"\n            width: 100%;\n            display: flex;\n            align-items: center;\n            gap: 12px;\n            padding: 8px 12px;\n            border-radius: 8px;\n            border: none;\n            background: transparent;\n            cursor: pointer;\n            transition: all 0.2s;\n            text-align: left;\n          \"\n          onmouseover=\"this.style.background='#f3f4f6'\"\n          onmouseout=\"this.style.background='transparent'\"\n        >\n          <i class=\"fas fa-cog\" style=\"color: #6b7280\"></i>\n          <span style=\"color: #374151\">Paramètres</span>\n        </button>\n      </div>\n    </div>\n  </header>\n\n  <!-- ===== MAIN MESSAGES SECTION ===== -->\n  <main\n    style=\"flex: 1; overflow-y: auto; padding: 16px; position: relative\"\n    #messagesContainer\n    (scroll)=\"onScroll($event)\"\n    (dragover)=\"onDragOver($event)\"\n    (dragleave)=\"onDragLeave($event)\"\n    (drop)=\"onDrop($event)\"\n    [style.background]=\"isDragOver ? 'rgba(34, 197, 94, 0.1)' : 'transparent'\"\n  >\n    <!-- Drag & Drop Overlay -->\n    <div\n      *ngIf=\"isDragOver\"\n      style=\"\n        position: absolute;\n        top: 0;\n        left: 0;\n        right: 0;\n        bottom: 0;\n        background: rgba(34, 197, 94, 0.2);\n        border: 2px dashed transparent;\n        border-radius: 8px;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        z-index: 50;\n        backdrop-filter: blur(2px);\n        animation: pulse 2s infinite;\n      \"\n    >\n      <div\n        style=\"\n          text-align: center;\n          background: #ffffff;\n          padding: 24px;\n          border-radius: 12px;\n          box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);\n          border: 1px solid transparent;\n        \"\n      >\n        <i\n          class=\"fas fa-cloud-upload-alt\"\n          style=\"\n            font-size: 48px;\n            color: #10b981;\n            margin-bottom: 12px;\n            animation: bounce 1s infinite;\n          \"\n        ></i>\n        <p\n          style=\"\n            font-size: 20px;\n            font-weight: bold;\n            color: #047857;\n            margin-bottom: 8px;\n          \"\n        >\n          Déposez vos fichiers ici\n        </p>\n        <p style=\"font-size: 14px; color: #10b981\">\n          Images, vidéos, documents...\n        </p>\n      </div>\n    </div>\n\n    <!-- Loading State -->\n    <div\n      *ngIf=\"isLoading\"\n      style=\"\n        display: flex;\n        flex-direction: column;\n        align-items: center;\n        justify-content: center;\n        padding: 32px 0;\n      \"\n    >\n      <div\n        style=\"\n          width: 32px;\n          height: 32px;\n          border: 2px solid #e5e7eb;\n          border-bottom-color: #10b981;\n          border-radius: 50%;\n          animation: spin 1s linear infinite;\n          margin-bottom: 16px;\n        \"\n      ></div>\n      <span style=\"color: #6b7280\">Chargement des messages...</span>\n    </div>\n\n    <!-- Empty State -->\n    <div\n      *ngIf=\"!isLoading && messages.length === 0\"\n      style=\"\n        display: flex;\n        flex-direction: column;\n        align-items: center;\n        justify-content: center;\n        padding: 64px 0;\n      \"\n    >\n      <div style=\"font-size: 64px; color: #d1d5db; margin-bottom: 16px\">\n        <i class=\"fas fa-comments\"></i>\n      </div>\n      <h3\n        style=\"\n          font-size: 20px;\n          font-weight: 600;\n          color: #374151;\n          margin-bottom: 8px;\n        \"\n      >\n        Aucun message\n      </h3>\n      <p style=\"color: #6b7280; text-align: center\">\n        Commencez votre conversation avec {{ otherParticipant?.username }}\n      </p>\n    </div>\n\n    <!-- Messages List -->\n    <div\n      *ngIf=\"!isLoading && messages.length > 0\"\n      style=\"display: flex; flex-direction: column; gap: 8px\"\n    >\n      <ng-container\n        *ngFor=\"\n          let message of messages;\n          let i = index;\n          trackBy: trackByMessageId\n        \"\n      >\n        <!-- Date Separator -->\n        <div\n          *ngIf=\"shouldShowDateSeparator(i)\"\n          style=\"display: flex; justify-content: center; margin: 16px 0\"\n        >\n          <div\n            style=\"\n              background: #ffffff;\n              padding: 4px 12px;\n              border-radius: 20px;\n              box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n            \"\n          >\n            <span style=\"font-size: 12px; color: #6b7280\">\n              {{ formatDateSeparator(message.timestamp) }}\n            </span>\n          </div>\n        </div>\n\n        <!-- Message Container -->\n        <div\n          style=\"display: flex\"\n          [style.justify-content]=\"\n            message.sender?.id === currentUserId ? 'flex-end' : 'flex-start'\n          \"\n          [id]=\"'message-' + message.id\"\n          (click)=\"onMessageClick(message, $event)\"\n          (contextmenu)=\"onMessageContextMenu(message, $event)\"\n        >\n          <!-- Avatar for others -->\n          <div\n            *ngIf=\"message.sender?.id !== currentUserId && shouldShowAvatar(i)\"\n            style=\"margin-right: 8px; flex-shrink: 0\"\n          >\n            <img\n              [src]=\"\n                message.sender?.image || 'assets/images/default-avatar.png'\n              \"\n              [alt]=\"message.sender?.username\"\n              style=\"\n                width: 32px;\n                height: 32px;\n                border-radius: 50%;\n                object-fit: cover;\n                cursor: pointer;\n                transition: transform 0.2s;\n              \"\n              (click)=\"openUserProfile(message.sender?.id!)\"\n              onmouseover=\"this.style.transform='scale(1.05)'\"\n              onmouseout=\"this.style.transform='scale(1)'\"\n            />\n          </div>\n\n          <!-- Message Bubble -->\n          <div\n            [style.background-color]=\"\n              message.sender?.id === currentUserId ? '#3b82f6' : '#ffffff'\n            \"\n            [style.color]=\"\n              message.sender?.id === currentUserId ? '#ffffff' : '#111827'\n            \"\n            style=\"\n              max-width: 320px;\n              padding: 12px 16px;\n              border-radius: 18px;\n              box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n              position: relative;\n              word-wrap: break-word;\n              overflow-wrap: break-word;\n              border: none;\n            \"\n          >\n            <!-- Sender Name (for groups) -->\n            <div\n              *ngIf=\"\n                isGroupConversation() &&\n                message.sender?.id !== currentUserId &&\n                shouldShowSenderName(i)\n              \"\n              style=\"\n                font-size: 12px;\n                font-weight: 600;\n                margin-bottom: 4px;\n                opacity: 0.75;\n              \"\n              [style.color]=\"getUserColor(message.sender?.id!)\"\n            >\n              {{ message.sender?.username }}\n            </div>\n\n            <!-- Text Content -->\n            <div\n              *ngIf=\"getMessageType(message) === 'text'\"\n              style=\"word-wrap: break-word; overflow-wrap: break-word\"\n            >\n              <div [innerHTML]=\"formatMessageContent(message.content)\"></div>\n            </div>\n\n            <!-- Image Content -->\n            <div *ngIf=\"hasImage(message)\" style=\"margin: 8px 0\">\n              <img\n                [src]=\"getImageUrl(message)\"\n                [alt]=\"message.content || 'Image'\"\n                (click)=\"openImageViewer(message)\"\n                (load)=\"onImageLoad($event, message)\"\n                (error)=\"onImageError($event, message)\"\n                style=\"\n                  max-width: 280px;\n                  height: auto;\n                  border-radius: 12px;\n                  cursor: pointer;\n                  transition: transform 0.2s;\n                \"\n                onmouseover=\"this.style.transform='scale(1.02)'\"\n                onmouseout=\"this.style.transform='scale(1)'\"\n              />\n              <!-- Image Caption -->\n              <div\n                *ngIf=\"message.content\"\n                [style.color]=\"\n                  message.sender?.id === currentUserId ? '#ffffff' : '#111827'\n                \"\n                style=\"font-size: 14px; margin-top: 8px; line-height: 1.4\"\n                [innerHTML]=\"formatMessageContent(message.content)\"\n              ></div>\n            </div>\n\n            <!-- Voice Message Content -->\n            <div\n              *ngIf=\"getMessageType(message) === 'audio'\"\n              style=\"\n                display: flex;\n                align-items: center;\n                gap: 12px;\n                padding: 12px;\n                background: rgba(255, 255, 255, 0.1);\n                border-radius: 12px;\n                margin: 8px 0;\n                min-width: 200px;\n                max-width: 280px;\n              \"\n            >\n              <!-- Bouton Play/Pause -->\n              <button\n                (click)=\"toggleVoicePlayback(message)\"\n                style=\"\n                  width: 40px;\n                  height: 40px;\n                  border-radius: 50%;\n                  border: none;\n                  background: rgba(255, 255, 255, 0.2);\n                  color: inherit;\n                  cursor: pointer;\n                  display: flex;\n                  align-items: center;\n                  justify-content: center;\n                  transition: all 0.2s;\n                  flex-shrink: 0;\n                \"\n                onmouseover=\"this.style.background='rgba(255, 255, 255, 0.3)'\"\n                onmouseout=\"this.style.background='rgba(255, 255, 255, 0.2)'\"\n                title=\"Lire/Pause\"\n              >\n                <i\n                  [class]=\"\n                    isVoicePlaying(message.id) ? 'fas fa-pause' : 'fas fa-play'\n                  \"\n                  style=\"font-size: 14px\"\n                ></i>\n              </button>\n\n              <!-- Visualisation des ondes sonores -->\n              <div\n                style=\"\n                  flex: 1;\n                  display: flex;\n                  align-items: center;\n                  gap: 2px;\n                  height: 24px;\n                  overflow: hidden;\n                \"\n              >\n                <div\n                  *ngFor=\"let wave of voiceWaves; let i = index\"\n                  style=\"\n                    width: 3px;\n                    background: currentColor;\n                    border-radius: 2px;\n                    opacity: 0.7;\n                    transition: height 0.3s ease;\n                  \"\n                  [style.height.px]=\"isVoicePlaying(message.id) ? wave : 8\"\n                  [style.animation]=\"\n                    isVoicePlaying(message.id) ? 'pulse 1s infinite' : 'none'\n                  \"\n                  [style.animation-delay]=\"i * 0.1 + 's'\"\n                ></div>\n              </div>\n\n              <!-- Durée et contrôles -->\n              <div\n                style=\"\n                  display: flex;\n                  align-items: center;\n                  gap: 8px;\n                  flex-shrink: 0;\n                \"\n              >\n                <!-- Durée -->\n                <div\n                  style=\"\n                    font-size: 12px;\n                    opacity: 0.8;\n                    min-width: 40px;\n                    text-align: right;\n                  \"\n                >\n                  {{ getVoiceDuration(message) }}\n                </div>\n\n                <!-- Vitesse de lecture (si en cours de lecture) -->\n                <button\n                  *ngIf=\"isVoicePlaying(message.id)\"\n                  (click)=\"changeVoiceSpeed(message)\"\n                  style=\"\n                    padding: 4px 8px;\n                    border-radius: 12px;\n                    border: none;\n                    background: rgba(255, 255, 255, 0.2);\n                    color: inherit;\n                    cursor: pointer;\n                    font-size: 11px;\n                    transition: all 0.2s;\n                  \"\n                  onmouseover=\"this.style.background='rgba(255, 255, 255, 0.3)'\"\n                  onmouseout=\"this.style.background='rgba(255, 255, 255, 0.2)'\"\n                  title=\"Changer la vitesse\"\n                >\n                  {{ getVoiceSpeed(message) }}x\n                </button>\n              </div>\n            </div>\n\n            <!-- Message Metadata -->\n            <div\n              style=\"\n                display: flex;\n                align-items: center;\n                justify-content: flex-end;\n                gap: 4px;\n                margin-top: 4px;\n                font-size: 12px;\n                opacity: 0.75;\n              \"\n            >\n              <span>{{ formatMessageTime(message.timestamp) }}</span>\n              <div\n                *ngIf=\"message.sender?.id === currentUserId\"\n                style=\"display: flex; align-items: center\"\n              >\n                <i\n                  class=\"fas fa-clock\"\n                  *ngIf=\"message.status === 'SENDING'\"\n                  title=\"Envoi en cours\"\n                ></i>\n                <i\n                  class=\"fas fa-check\"\n                  *ngIf=\"message.status === 'SENT'\"\n                  title=\"Envoyé\"\n                ></i>\n                <i\n                  class=\"fas fa-check-double\"\n                  *ngIf=\"message.status === 'DELIVERED'\"\n                  title=\"Livré\"\n                ></i>\n                <i\n                  class=\"fas fa-check-double\"\n                  style=\"color: #3b82f6\"\n                  *ngIf=\"message.status === 'READ'\"\n                  title=\"Lu\"\n                ></i>\n              </div>\n            </div>\n          </div>\n        </div>\n      </ng-container>\n\n      <!-- Typing Indicator -->\n      <div\n        *ngIf=\"otherUserIsTyping\"\n        style=\"display: flex; align-items: start; gap: 8px\"\n      >\n        <img\n          [src]=\"otherParticipant?.image || 'assets/images/default-avatar.png'\"\n          [alt]=\"otherParticipant?.username\"\n          style=\"\n            width: 32px;\n            height: 32px;\n            border-radius: 50%;\n            object-fit: cover;\n          \"\n        />\n        <div\n          style=\"\n            background: #ffffff;\n            padding: 12px 16px;\n            border-radius: 18px;\n            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n          \"\n        >\n          <div style=\"display: flex; gap: 4px\">\n            <div\n              style=\"\n                width: 8px;\n                height: 8px;\n                background: #6b7280;\n                border-radius: 50%;\n                animation: bounce 1s infinite;\n              \"\n            ></div>\n            <div\n              style=\"\n                width: 8px;\n                height: 8px;\n                background: #6b7280;\n                border-radius: 50%;\n                animation: bounce 1s infinite 0.1s;\n              \"\n            ></div>\n            <div\n              style=\"\n                width: 8px;\n                height: 8px;\n                background: #6b7280;\n                border-radius: 50%;\n                animation: bounce 1s infinite 0.2s;\n              \"\n            ></div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </main>\n\n  <!-- ===== FOOTER INPUT SECTION ===== -->\n  <footer\n    style=\"background: #ffffff; border-top: 1px solid #e5e7eb; padding: 16px\"\n  >\n    <form\n      [formGroup]=\"messageForm\"\n      (ngSubmit)=\"sendMessage()\"\n      style=\"display: flex; align-items: end; gap: 12px\"\n    >\n      <!-- Left Actions -->\n      <div style=\"display: flex; gap: 8px\">\n        <!-- Emoji Button -->\n        <button\n          type=\"button\"\n          (click)=\"toggleEmojiPicker()\"\n          style=\"\n            padding: 8px;\n            border-radius: 50%;\n            border: none;\n            background: transparent;\n            color: #6b7280;\n            cursor: pointer;\n            transition: all 0.2s;\n          \"\n          [style.background]=\"showEmojiPicker ? '#dcfce7' : 'transparent'\"\n          [style.color]=\"showEmojiPicker ? '#16a34a' : '#6b7280'\"\n          title=\"Émojis\"\n          onmouseover=\"this.style.background=this.style.background === 'transparent' ? '#f3f4f6' : this.style.background\"\n          onmouseout=\"this.style.background=this.style.background === '#f3f4f6' ? 'transparent' : this.style.background\"\n        >\n          <i class=\"fas fa-smile\"></i>\n        </button>\n\n        <!-- Attachment Button -->\n        <button\n          type=\"button\"\n          (click)=\"toggleAttachmentMenu()\"\n          style=\"\n            padding: 8px;\n            border-radius: 50%;\n            border: none;\n            background: transparent;\n            color: #6b7280;\n            cursor: pointer;\n            transition: all 0.2s;\n          \"\n          [style.background]=\"showAttachmentMenu ? '#dcfce7' : 'transparent'\"\n          [style.color]=\"showAttachmentMenu ? '#16a34a' : '#6b7280'\"\n          title=\"Pièces jointes\"\n          onmouseover=\"this.style.background=this.style.background === 'transparent' ? '#f3f4f6' : this.style.background\"\n          onmouseout=\"this.style.background=this.style.background === '#f3f4f6' ? 'transparent' : this.style.background\"\n        >\n          <i class=\"fas fa-paperclip\"></i>\n        </button>\n\n        <!-- Voice Recording Button -->\n        <button\n          type=\"button\"\n          (mousedown)=\"onRecordStart($event)\"\n          (mouseup)=\"onRecordEnd($event)\"\n          (mouseleave)=\"onRecordCancel($event)\"\n          (touchstart)=\"onRecordStart($event)\"\n          (touchend)=\"onRecordEnd($event)\"\n          (touchcancel)=\"onRecordCancel($event)\"\n          style=\"\n            padding: 8px;\n            border-radius: 50%;\n            border: none;\n            background: transparent;\n            color: #6b7280;\n            cursor: pointer;\n            transition: all 0.2s;\n            position: relative;\n          \"\n          [style.background]=\"isRecordingVoice ? '#fef3c7' : 'transparent'\"\n          [style.color]=\"isRecordingVoice ? '#f59e0b' : '#6b7280'\"\n          [style.transform]=\"isRecordingVoice ? 'scale(1.1)' : 'scale(1)'\"\n          title=\"Maintenir pour enregistrer un message vocal\"\n          onmouseover=\"if(!this.style.background || this.style.background === 'transparent') this.style.background='#f3f4f6'\"\n          onmouseout=\"if(this.style.background === '#f3f4f6') this.style.background='transparent'\"\n        >\n          <i\n            [class]=\"isRecordingVoice ? 'fas fa-stop' : 'fas fa-microphone'\"\n            [style.animation]=\"isRecordingVoice ? 'pulse 1s infinite' : 'none'\"\n          ></i>\n\n          <!-- Indicateur d'enregistrement -->\n          <div\n            *ngIf=\"isRecordingVoice\"\n            style=\"\n              position: absolute;\n              top: -2px;\n              right: -2px;\n              width: 8px;\n              height: 8px;\n              background: #ef4444;\n              border-radius: 50%;\n              animation: ping 1s infinite;\n            \"\n          ></div>\n        </button>\n      </div>\n\n      <!-- Message Input -->\n      <div style=\"flex: 1; position: relative\">\n        <textarea\n          formControlName=\"content\"\n          placeholder=\"Tapez votre message...\"\n          (keydown)=\"onInputKeyDown($event)\"\n          (input)=\"onInputChange($event)\"\n          (focus)=\"onInputFocus()\"\n          style=\"\n            width: 100%;\n            min-height: 44px;\n            max-height: 120px;\n            padding: 12px 16px;\n            border: 1px solid #e5e7eb;\n            border-radius: 22px;\n            resize: none;\n            outline: none;\n            font-family: inherit;\n            font-size: 14px;\n            line-height: 1.4;\n            background: #ffffff;\n            color: #111827;\n            transition: all 0.2s;\n          \"\n          [disabled]=\"isInputDisabled()\"\n        ></textarea>\n      </div>\n\n      <!-- Send Button -->\n      <button\n        type=\"submit\"\n        [disabled]=\"!messageForm.valid || isSendingMessage\"\n        style=\"\n          padding: 12px;\n          border-radius: 50%;\n          border: none;\n          background: #3b82f6;\n          color: #ffffff;\n          cursor: pointer;\n          transition: all 0.2s;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          min-width: 44px;\n          min-height: 44px;\n        \"\n        [style.background]=\"\n          !messageForm.valid || isSendingMessage ? '#9ca3af' : '#3b82f6'\n        \"\n        [style.cursor]=\"\n          !messageForm.valid || isSendingMessage ? 'not-allowed' : 'pointer'\n        \"\n        title=\"Envoyer\"\n        onmouseover=\"if(!this.disabled) this.style.background='#2563eb'\"\n        onmouseout=\"if(!this.disabled) this.style.background='#3b82f6'\"\n      >\n        <i class=\"fas fa-paper-plane\" *ngIf=\"!isSendingMessage\"></i>\n        <div\n          *ngIf=\"isSendingMessage\"\n          style=\"\n            width: 16px;\n            height: 16px;\n            border: 2px solid #ffffff;\n            border-top-color: transparent;\n            border-radius: 50%;\n            animation: spin 1s linear infinite;\n          \"\n        ></div>\n      </button>\n    </form>\n\n    <!-- Emoji Picker -->\n    <div\n      *ngIf=\"showEmojiPicker\"\n      style=\"\n        position: absolute;\n        bottom: 80px;\n        left: 16px;\n        background: #ffffff;\n        border-radius: 16px;\n        box-shadow: 0 20px 25px rgba(0, 0, 0, 0.1);\n        border: 1px solid #e5e7eb;\n        z-index: 50;\n        width: 320px;\n        max-height: 300px;\n        overflow-y: auto;\n      \"\n    >\n      <div style=\"padding: 16px\">\n        <h4\n          style=\"\n            margin: 0 0 12px 0;\n            font-size: 14px;\n            font-weight: 600;\n            color: #374151;\n          \"\n        >\n          Émojis\n        </h4>\n        <div\n          style=\"display: grid; grid-template-columns: repeat(8, 1fr); gap: 8px\"\n        >\n          <button\n            *ngFor=\"let emoji of getEmojisForCategory(selectedEmojiCategory)\"\n            (click)=\"insertEmoji(emoji)\"\n            style=\"\n              padding: 8px;\n              border: none;\n              background: transparent;\n              border-radius: 8px;\n              cursor: pointer;\n              font-size: 20px;\n              transition: all 0.2s;\n            \"\n            onmouseover=\"this.style.background='#f3f4f6'\"\n            onmouseout=\"this.style.background='transparent'\"\n            [title]=\"emoji.name\"\n          >\n            {{ emoji.emoji }}\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Attachment Menu -->\n    <div\n      *ngIf=\"showAttachmentMenu\"\n      style=\"\n        position: absolute;\n        bottom: 80px;\n        left: 60px;\n        background: #ffffff;\n        border-radius: 16px;\n        box-shadow: 0 20px 25px rgba(0, 0, 0, 0.1);\n        border: 1px solid #e5e7eb;\n        z-index: 50;\n        min-width: 200px;\n      \"\n    >\n      <div style=\"padding: 16px\">\n        <h4\n          style=\"\n            margin: 0 0 12px 0;\n            font-size: 14px;\n            font-weight: 600;\n            color: #374151;\n          \"\n        >\n          Pièces jointes\n        </h4>\n        <div\n          style=\"\n            display: grid;\n            grid-template-columns: repeat(2, 1fr);\n            gap: 12px;\n          \"\n        >\n          <!-- Images -->\n          <button\n            (click)=\"triggerFileInput('image')\"\n            style=\"\n              display: flex;\n              flex-direction: column;\n              align-items: center;\n              gap: 8px;\n              padding: 16px;\n              border: none;\n              background: transparent;\n              border-radius: 12px;\n              cursor: pointer;\n              transition: all 0.2s;\n            \"\n            onmouseover=\"this.style.background='#f3f4f6'\"\n            onmouseout=\"this.style.background='transparent'\"\n          >\n            <div\n              style=\"\n                width: 48px;\n                height: 48px;\n                background: #dbeafe;\n                border-radius: 50%;\n                display: flex;\n                align-items: center;\n                justify-content: center;\n              \"\n            >\n              <i\n                class=\"fas fa-image\"\n                style=\"color: #3b82f6; font-size: 20px\"\n              ></i>\n            </div>\n            <span style=\"font-size: 14px; font-weight: 500; color: #374151\"\n              >Images</span\n            >\n          </button>\n\n          <!-- Documents -->\n          <button\n            (click)=\"triggerFileInput('document')\"\n            style=\"\n              display: flex;\n              flex-direction: column;\n              align-items: center;\n              gap: 8px;\n              padding: 16px;\n              border: none;\n              background: transparent;\n              border-radius: 12px;\n              cursor: pointer;\n              transition: all 0.2s;\n            \"\n            onmouseover=\"this.style.background='#f3f4f6'\"\n            onmouseout=\"this.style.background='transparent'\"\n          >\n            <div\n              style=\"\n                width: 48px;\n                height: 48px;\n                background: #fef3c7;\n                border-radius: 50%;\n                display: flex;\n                align-items: center;\n                justify-content: center;\n              \"\n            >\n              <i\n                class=\"fas fa-file-alt\"\n                style=\"color: #f59e0b; font-size: 20px\"\n              ></i>\n            </div>\n            <span style=\"font-size: 14px; font-weight: 500; color: #374151\"\n              >Documents</span\n            >\n          </button>\n\n          <!-- Camera -->\n          <button\n            (click)=\"openCamera()\"\n            style=\"\n              display: flex;\n              flex-direction: column;\n              align-items: center;\n              gap: 8px;\n              padding: 16px;\n              border: none;\n              background: transparent;\n              border-radius: 12px;\n              cursor: pointer;\n              transition: all 0.2s;\n            \"\n            onmouseover=\"this.style.background='#f3f4f6'\"\n            onmouseout=\"this.style.background='transparent'\"\n          >\n            <div\n              style=\"\n                width: 48px;\n                height: 48px;\n                background: #dcfce7;\n                border-radius: 50%;\n                display: flex;\n                align-items: center;\n                justify-content: center;\n              \"\n            >\n              <i\n                class=\"fas fa-camera\"\n                style=\"color: #10b981; font-size: 20px\"\n              ></i>\n            </div>\n            <span style=\"font-size: 14px; font-weight: 500; color: #374151\"\n              >Caméra</span\n            >\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Hidden File Input -->\n    <input\n      #fileInput\n      type=\"file\"\n      style=\"display: none\"\n      (change)=\"onFileSelected($event)\"\n      [accept]=\"getFileAcceptTypes()\"\n      multiple\n    />\n  </footer>\n\n  <!-- Overlay to close menus -->\n  <div\n    *ngIf=\"showEmojiPicker || showAttachmentMenu || showMainMenu\"\n    style=\"\n      position: fixed;\n      top: 0;\n      left: 0;\n      right: 0;\n      bottom: 0;\n      background: rgba(0, 0, 0, 0.25);\n      z-index: 40;\n    \"\n    (click)=\"closeAllMenus()\"\n  ></div>\n\n  <!-- Interface d'enregistrement vocal -->\n  <div\n    *ngIf=\"isRecordingVoice\"\n    style=\"\n      position: fixed;\n      bottom: 100px;\n      left: 50%;\n      transform: translateX(-50%);\n      background: linear-gradient(135deg, #f59e0b, #d97706);\n      color: white;\n      padding: 20px 24px;\n      border-radius: 20px;\n      box-shadow: 0 20px 25px rgba(0, 0, 0, 0.2);\n      z-index: 60;\n      display: flex;\n      align-items: center;\n      gap: 16px;\n      min-width: 280px;\n      animation: slideInUp 0.3s ease-out;\n    \"\n  >\n    <!-- Icône microphone animée -->\n    <div\n      style=\"\n        width: 48px;\n        height: 48px;\n        background: rgba(255, 255, 255, 0.2);\n        border-radius: 50%;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        animation: pulse 1s infinite;\n      \"\n    >\n      <i class=\"fas fa-microphone\" style=\"font-size: 20px\"></i>\n    </div>\n\n    <!-- Contenu central -->\n    <div style=\"flex: 1\">\n      <!-- Durée d'enregistrement -->\n      <div style=\"font-size: 18px; font-weight: bold; margin-bottom: 4px\">\n        {{ formatRecordingDuration(voiceRecordingDuration) }}\n      </div>\n\n      <!-- Visualisation des ondes sonores -->\n      <div style=\"display: flex; align-items: center; gap: 2px; height: 20px\">\n        <div\n          *ngFor=\"let wave of voiceWaves; let i = index\"\n          style=\"\n            width: 3px;\n            background: rgba(255, 255, 255, 0.8);\n            border-radius: 2px;\n            transition: height 0.3s ease;\n          \"\n          [style.height.px]=\"wave\"\n          [style.animation]=\"'bounce 1s infinite'\"\n          [style.animation-delay]=\"i * 0.1 + 's'\"\n        ></div>\n      </div>\n\n      <!-- Format d'enregistrement -->\n      <div style=\"font-size: 12px; opacity: 0.8; margin-top: 4px\">\n        Format: {{ getRecordingFormat() }}\n      </div>\n    </div>\n\n    <!-- Boutons d'action -->\n    <div style=\"display: flex; gap: 8px\">\n      <!-- Bouton Annuler -->\n      <button\n        (click)=\"onRecordCancel($event)\"\n        style=\"\n          width: 40px;\n          height: 40px;\n          border-radius: 50%;\n          border: none;\n          background: rgba(239, 68, 68, 0.8);\n          color: white;\n          cursor: pointer;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          transition: all 0.2s;\n        \"\n        onmouseover=\"this.style.background='rgba(239, 68, 68, 1)'\"\n        onmouseout=\"this.style.background='rgba(239, 68, 68, 0.8)'\"\n        title=\"Annuler l'enregistrement\"\n      >\n        <i class=\"fas fa-times\" style=\"font-size: 16px\"></i>\n      </button>\n\n      <!-- Bouton Envoyer -->\n      <button\n        (click)=\"onRecordEnd($event)\"\n        style=\"\n          width: 40px;\n          height: 40px;\n          border-radius: 50%;\n          border: none;\n          background: rgba(34, 197, 94, 0.8);\n          color: white;\n          cursor: pointer;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          transition: all 0.2s;\n        \"\n        onmouseover=\"this.style.background='rgba(34, 197, 94, 1)'\"\n        onmouseout=\"this.style.background='rgba(34, 197, 94, 0.8)'\"\n        title=\"Envoyer le message vocal\"\n      >\n        <i class=\"fas fa-paper-plane\" style=\"font-size: 16px\"></i>\n      </button>\n    </div>\n  </div>\n</div>\n\n<!-- Interface d'appel gérée par les composants globaux app-incoming-call et app-active-call -->\n\n<!-- Les composants d'appel sont maintenant gérés globalement dans app.component.html -->\n"], "mappings": ";AASA,SAAiCA,UAAU,QAAQ,gBAAgB;AAEnE,SAASC,YAAY,QAAQ,MAAM;AAInC,SAASC,QAAQ,QAA4B,kCAAkC;;;;;;;;;;;;;;ICqHvEC,EAAA,CAAAC,SAAA,cAaO;;;;;IAoBLD,EAAA,CAAAE,cAAA,cAGC;IACOF,EAAA,CAAAG,MAAA,6BAAiB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAC9BJ,EAAA,CAAAE,cAAA,cAAqC;IACnCF,EAAA,CAAAC,SAAA,cAQO;IAmBTD,EAAA,CAAAI,YAAA,EAAM;;;;;IAGRJ,EAAA,CAAAE,cAAA,WAA4B;IAC1BF,EAAA,CAAAG,MAAA,GAKF;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;IALLJ,EAAA,CAAAK,SAAA,GAKF;IALEL,EAAA,CAAAM,kBAAA,OAAAC,MAAA,CAAAC,gBAAA,kBAAAD,MAAA,CAAAC,gBAAA,CAAAC,QAAA,iBAAAF,MAAA,CAAAG,gBAAA,CAAAH,MAAA,CAAAC,gBAAA,kBAAAD,MAAA,CAAAC,gBAAA,CAAAG,UAAA,OAKF;;;;;;IAkGNX,EAAA,CAAAE,cAAA,cAaC;IAGKF,EAAA,CAAAY,UAAA,mBAAAC,6DAAA;MAAAb,EAAA,CAAAc,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAASD,OAAA,CAAAE,YAAA,EAAc;MAAA,OAAAlB,EAAA,CAAAmB,WAAA,CAAAH,OAAA,CAAAI,YAAA,GAAiB,KAAK;IAAA,EAAC;IAiB9CpB,EAAA,CAAAC,SAAA,YAAoD;IACpDD,EAAA,CAAAE,cAAA,eAA6B;IAAAF,EAAA,CAAAG,MAAA,iBAAU;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAEhDJ,EAAA,CAAAE,cAAA,iBAgBC;IACCF,EAAA,CAAAC,SAAA,YAAkD;IAClDD,EAAA,CAAAE,cAAA,eAA6B;IAAAF,EAAA,CAAAG,MAAA,qBAAc;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAEpDJ,EAAA,CAAAC,SAAA,cAAmD;IACnDD,EAAA,CAAAE,cAAA,kBAgBC;IACCF,EAAA,CAAAC,SAAA,aAAiD;IACjDD,EAAA,CAAAE,cAAA,gBAA6B;IAAAF,EAAA,CAAAG,MAAA,uBAAU;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;IAiBpDJ,EAAA,CAAAE,cAAA,cAkBC;IAWGF,EAAA,CAAAC,SAAA,YAQK;IACLD,EAAA,CAAAE,cAAA,YAOC;IACCF,EAAA,CAAAG,MAAA,sCACF;IAAAH,EAAA,CAAAI,YAAA,EAAI;IACJJ,EAAA,CAAAE,cAAA,YAA2C;IACzCF,EAAA,CAAAG,MAAA,0CACF;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;IAKRJ,EAAA,CAAAE,cAAA,cASC;IACCF,EAAA,CAAAC,SAAA,cAUO;IACPD,EAAA,CAAAE,cAAA,eAA6B;IAAAF,EAAA,CAAAG,MAAA,iCAA0B;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;IAIhEJ,EAAA,CAAAE,cAAA,cASC;IAEGF,EAAA,CAAAC,SAAA,YAA+B;IACjCD,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,aAOC;IACCF,EAAA,CAAAG,MAAA,sBACF;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAE,cAAA,YAA8C;IAC5CF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;IADFJ,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,wCAAAe,MAAA,CAAAb,gBAAA,kBAAAa,MAAA,CAAAb,gBAAA,CAAAc,QAAA,MACF;;;;;IAgBEtB,EAAA,CAAAE,cAAA,cAGC;IAUKF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;IADLJ,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAiB,OAAA,CAAAC,mBAAA,CAAAC,WAAA,CAAAC,SAAA,OACF;;;;;;IAeF1B,EAAA,CAAAE,cAAA,cAGC;IAcGF,EAAA,CAAAY,UAAA,mBAAAe,+EAAA;MAAA3B,EAAA,CAAAc,aAAA,CAAAc,IAAA;MAAA,MAAAH,WAAA,GAAAzB,EAAA,CAAAiB,aAAA,GAAAY,SAAA;MAAA,MAAAC,OAAA,GAAA9B,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAmB,WAAA,CAAAW,OAAA,CAAAC,eAAA,CAAAN,WAAA,CAAAO,MAAA,kBAAAP,WAAA,CAAAO,MAAA,CAAAC,EAAA,CAAoC;IAAA,EAAC;IAbhDjC,EAAA,CAAAI,YAAA,EAgBE;;;;IAfAJ,EAAA,CAAAK,SAAA,GAEC;IAFDL,EAAA,CAAAkC,UAAA,SAAAT,WAAA,CAAAO,MAAA,kBAAAP,WAAA,CAAAO,MAAA,CAAAG,KAAA,yCAAAnC,EAAA,CAAAoC,aAAA,CAEC,QAAAX,WAAA,CAAAO,MAAA,kBAAAP,WAAA,CAAAO,MAAA,CAAAV,QAAA;;;;;IAoCHtB,EAAA,CAAAE,cAAA,cAaC;IACCF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;IAHJJ,EAAA,CAAAqC,WAAA,UAAAC,OAAA,CAAAC,YAAA,CAAAd,WAAA,CAAAO,MAAA,kBAAAP,WAAA,CAAAO,MAAA,CAAAC,EAAA,EAAiD;IAEjDjC,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAmB,WAAA,CAAAO,MAAA,kBAAAP,WAAA,CAAAO,MAAA,CAAAV,QAAA,MACF;;;;;IAGAtB,EAAA,CAAAE,cAAA,eAGC;IACCF,EAAA,CAAAC,SAAA,eAA+D;IACjED,EAAA,CAAAI,YAAA,EAAM;;;;;IADCJ,EAAA,CAAAK,SAAA,GAAmD;IAAnDL,EAAA,CAAAkC,UAAA,cAAAM,OAAA,CAAAC,oBAAA,CAAAhB,WAAA,CAAAiB,OAAA,GAAA1C,EAAA,CAAA2C,cAAA,CAAmD;;;;;IAsBxD3C,EAAA,CAAAC,SAAA,eAOO;;;;;IALLD,EAAA,CAAAqC,WAAA,WAAAZ,WAAA,CAAAO,MAAA,kBAAAP,WAAA,CAAAO,MAAA,CAAAC,EAAA,MAAAW,OAAA,CAAAC,aAAA,yBAEC;IAED7C,EAAA,CAAAkC,UAAA,cAAAU,OAAA,CAAAH,oBAAA,CAAAhB,WAAA,CAAAiB,OAAA,GAAA1C,EAAA,CAAA2C,cAAA,CAAmD;;;;;;IAxBvD3C,EAAA,CAAAE,cAAA,eAAqD;IAIjDF,EAAA,CAAAY,UAAA,mBAAAkC,+EAAA;MAAA9C,EAAA,CAAAc,aAAA,CAAAiC,IAAA;MAAA,MAAAtB,WAAA,GAAAzB,EAAA,CAAAiB,aAAA,GAAAY,SAAA;MAAA,MAAAmB,OAAA,GAAAhD,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAmB,WAAA,CAAA6B,OAAA,CAAAC,eAAA,CAAAxB,WAAA,CAAwB;IAAA,EAAC,kBAAAyB,8EAAAC,MAAA;MAAAnD,EAAA,CAAAc,aAAA,CAAAiC,IAAA;MAAA,MAAAtB,WAAA,GAAAzB,EAAA,CAAAiB,aAAA,GAAAY,SAAA;MAAA,MAAAuB,OAAA,GAAApD,EAAA,CAAAiB,aAAA;MAAA,OAC1BjB,EAAA,CAAAmB,WAAA,CAAAiC,OAAA,CAAAC,WAAA,CAAAF,MAAA,EAAA1B,WAAA,CAA4B;IAAA,EADF,mBAAA6B,+EAAAH,MAAA;MAAAnD,EAAA,CAAAc,aAAA,CAAAiC,IAAA;MAAA,MAAAtB,WAAA,GAAAzB,EAAA,CAAAiB,aAAA,GAAAY,SAAA;MAAA,MAAA0B,OAAA,GAAAvD,EAAA,CAAAiB,aAAA;MAAA,OAEzBjB,EAAA,CAAAmB,WAAA,CAAAoC,OAAA,CAAAC,YAAA,CAAAL,MAAA,EAAA1B,WAAA,CAA6B;IAAA,EAFJ;IAHpCzB,EAAA,CAAAI,YAAA,EAeE;IAEFJ,EAAA,CAAAyD,UAAA,IAAAC,+DAAA,mBAOO;IACT1D,EAAA,CAAAI,YAAA,EAAM;;;;;IAxBFJ,EAAA,CAAAK,SAAA,GAA4B;IAA5BL,EAAA,CAAAkC,UAAA,QAAAyB,OAAA,CAAAC,WAAA,CAAAnC,WAAA,GAAAzB,EAAA,CAAAoC,aAAA,CAA4B,QAAAX,WAAA,CAAAiB,OAAA;IAiB3B1C,EAAA,CAAAK,SAAA,GAAqB;IAArBL,EAAA,CAAAkC,UAAA,SAAAT,WAAA,CAAAiB,OAAA,CAAqB;;;;;IAgEtB1C,EAAA,CAAAC,SAAA,eAcO;;;;;;;IALLD,EAAA,CAAAqC,WAAA,WAAAwB,OAAA,CAAAC,cAAA,CAAArC,WAAA,CAAAQ,EAAA,IAAA8B,QAAA,WAAyD,cAAAF,OAAA,CAAAC,cAAA,CAAArC,WAAA,CAAAQ,EAAA,qDAAA+B,KAAA;;;;;;IA8B3DhE,EAAA,CAAAE,cAAA,kBAgBC;IAdCF,EAAA,CAAAY,UAAA,mBAAAqD,2FAAA;MAAAjE,EAAA,CAAAc,aAAA,CAAAoD,IAAA;MAAA,MAAAzC,WAAA,GAAAzB,EAAA,CAAAiB,aAAA,IAAAY,SAAA;MAAA,MAAAsC,OAAA,GAAAnE,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAmB,WAAA,CAAAgD,OAAA,CAAAC,gBAAA,CAAA3C,WAAA,CAAyB;IAAA,EAAC;IAenCzB,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;;IADPJ,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAA+D,OAAA,CAAAC,aAAA,CAAA7C,WAAA,QACF;;;;;;IA/GJzB,EAAA,CAAAE,cAAA,eAaC;IAGGF,EAAA,CAAAY,UAAA,mBAAA2D,kFAAA;MAAAvE,EAAA,CAAAc,aAAA,CAAA0D,IAAA;MAAA,MAAA/C,WAAA,GAAAzB,EAAA,CAAAiB,aAAA,GAAAY,SAAA;MAAA,MAAA4C,OAAA,GAAAzE,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAmB,WAAA,CAAAsD,OAAA,CAAAC,mBAAA,CAAAjD,WAAA,CAA4B;IAAA,EAAC;IAmBtCzB,EAAA,CAAAC,SAAA,aAKK;IACPD,EAAA,CAAAI,YAAA,EAAS;IAGTJ,EAAA,CAAAE,cAAA,eASC;IACCF,EAAA,CAAAyD,UAAA,IAAAkB,+DAAA,mBAcO;IACT3E,EAAA,CAAAI,YAAA,EAAM;IAGNJ,EAAA,CAAAE,cAAA,eAOC;IAUGF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAGNJ,EAAA,CAAAyD,UAAA,IAAAmB,kEAAA,sBAkBS;IACX5E,EAAA,CAAAI,YAAA,EAAM;;;;;IA5EFJ,EAAA,CAAAK,SAAA,GAEC;IAFDL,EAAA,CAAA6E,UAAA,CAAAC,OAAA,CAAAhB,cAAA,CAAArC,WAAA,CAAAQ,EAAA,mCAEC;IAiBgBjC,EAAA,CAAAK,SAAA,GAAe;IAAfL,EAAA,CAAAkC,UAAA,YAAA4C,OAAA,CAAAC,UAAA,CAAe;IAkChC/E,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAwE,OAAA,CAAAE,gBAAA,CAAAvD,WAAA,OACF;IAIGzB,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAkC,UAAA,SAAA4C,OAAA,CAAAhB,cAAA,CAAArC,WAAA,CAAAQ,EAAA,EAAgC;;;;;IAsCnCjC,EAAA,CAAAC,SAAA,aAIK;;;;;IACLD,EAAA,CAAAC,SAAA,aAIK;;;;;IACLD,EAAA,CAAAC,SAAA,aAIK;;;;;IACLD,EAAA,CAAAC,SAAA,aAKK;;;;;IAxBPD,EAAA,CAAAE,cAAA,eAGC;IACCF,EAAA,CAAAyD,UAAA,IAAAwB,8DAAA,iBAIK;IACLjF,EAAA,CAAAyD,UAAA,IAAAyB,8DAAA,iBAIK;IACLlF,EAAA,CAAAyD,UAAA,IAAA0B,8DAAA,iBAIK;IACLnF,EAAA,CAAAyD,UAAA,IAAA2B,8DAAA,iBAKK;IACPpF,EAAA,CAAAI,YAAA,EAAM;;;;IAnBDJ,EAAA,CAAAK,SAAA,GAAkC;IAAlCL,EAAA,CAAAkC,UAAA,SAAAT,WAAA,CAAA4D,MAAA,eAAkC;IAKlCrF,EAAA,CAAAK,SAAA,GAA+B;IAA/BL,EAAA,CAAAkC,UAAA,SAAAT,WAAA,CAAA4D,MAAA,YAA+B;IAK/BrF,EAAA,CAAAK,SAAA,GAAoC;IAApCL,EAAA,CAAAkC,UAAA,SAAAT,WAAA,CAAA4D,MAAA,iBAAoC;IAMpCrF,EAAA,CAAAK,SAAA,GAA+B;IAA/BL,EAAA,CAAAkC,UAAA,SAAAT,WAAA,CAAA4D,MAAA,YAA+B;;;;;;IA7R5CrF,EAAA,CAAAsF,uBAAA,GAMC;IAECtF,EAAA,CAAAyD,UAAA,IAAA8B,yDAAA,kBAgBM;IAGNvF,EAAA,CAAAE,cAAA,cAQC;IAFCF,EAAA,CAAAY,UAAA,mBAAA4E,yEAAArC,MAAA;MAAA,MAAAsC,WAAA,GAAAzF,EAAA,CAAAc,aAAA,CAAA4E,IAAA;MAAA,MAAAjE,WAAA,GAAAgE,WAAA,CAAA5D,SAAA;MAAA,MAAA8D,OAAA,GAAA3F,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAmB,WAAA,CAAAwE,OAAA,CAAAC,cAAA,CAAAnE,WAAA,EAAA0B,MAAA,CAA+B;IAAA,EAAC,yBAAA0C,+EAAA1C,MAAA;MAAA,MAAAsC,WAAA,GAAAzF,EAAA,CAAAc,aAAA,CAAA4E,IAAA;MAAA,MAAAjE,WAAA,GAAAgE,WAAA,CAAA5D,SAAA;MAAA,MAAAiE,OAAA,GAAA9F,EAAA,CAAAiB,aAAA;MAAA,OAC1BjB,EAAA,CAAAmB,WAAA,CAAA2E,OAAA,CAAAC,oBAAA,CAAAtE,WAAA,EAAA0B,MAAA,CAAqC;IAAA,EADX;IAIzCnD,EAAA,CAAAyD,UAAA,IAAAuC,yDAAA,kBAqBM;IAGNhG,EAAA,CAAAE,cAAA,cAiBC;IAECF,EAAA,CAAAyD,UAAA,IAAAwC,yDAAA,kBAeM;IAGNjG,EAAA,CAAAyD,UAAA,IAAAyC,yDAAA,kBAKM;IAGNlG,EAAA,CAAAyD,UAAA,IAAA0C,yDAAA,kBA0BM;IAGNnG,EAAA,CAAAyD,UAAA,IAAA2C,yDAAA,kBAiHM;IAGNpG,EAAA,CAAAE,cAAA,cAUC;IACOF,EAAA,CAAAG,MAAA,IAA0C;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACvDJ,EAAA,CAAAyD,UAAA,KAAA4C,0DAAA,kBAyBM;IACRrG,EAAA,CAAAI,YAAA,EAAM;IAGZJ,EAAA,CAAAsG,qBAAA,EAAe;;;;;;IA3RVtG,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAkC,UAAA,SAAAqE,OAAA,CAAAC,uBAAA,CAAAC,KAAA,EAAgC;IAoBjCzG,EAAA,CAAAK,SAAA,GAEC;IAFDL,EAAA,CAAAqC,WAAA,qBAAAZ,WAAA,CAAAO,MAAA,kBAAAP,WAAA,CAAAO,MAAA,CAAAC,EAAA,MAAAsE,OAAA,CAAA1D,aAAA,6BAEC;IACD7C,EAAA,CAAAkC,UAAA,oBAAAT,WAAA,CAAAQ,EAAA,CAA8B;IAM3BjC,EAAA,CAAAK,SAAA,GAAiE;IAAjEL,EAAA,CAAAkC,UAAA,UAAAT,WAAA,CAAAO,MAAA,kBAAAP,WAAA,CAAAO,MAAA,CAAAC,EAAA,MAAAsE,OAAA,CAAA1D,aAAA,IAAA0D,OAAA,CAAAG,gBAAA,CAAAD,KAAA,EAAiE;IAwBlEzG,EAAA,CAAAK,SAAA,GAEC;IAFDL,EAAA,CAAAqC,WAAA,sBAAAZ,WAAA,CAAAO,MAAA,kBAAAP,WAAA,CAAAO,MAAA,CAAAC,EAAA,MAAAsE,OAAA,CAAA1D,aAAA,yBAEC,WAAApB,WAAA,CAAAO,MAAA,kBAAAP,WAAA,CAAAO,MAAA,CAAAC,EAAA,MAAAsE,OAAA,CAAA1D,aAAA;IAiBE7C,EAAA,CAAAK,SAAA,GAKf;IALeL,EAAA,CAAAkC,UAAA,SAAAqE,OAAA,CAAAI,mBAAA,OAAAlF,WAAA,CAAAO,MAAA,kBAAAP,WAAA,CAAAO,MAAA,CAAAC,EAAA,MAAAsE,OAAA,CAAA1D,aAAA,IAAA0D,OAAA,CAAAK,oBAAA,CAAAH,KAAA,EAKf;IAaezG,EAAA,CAAAK,SAAA,GAAwC;IAAxCL,EAAA,CAAAkC,UAAA,SAAAqE,OAAA,CAAAM,cAAA,CAAApF,WAAA,aAAwC;IAOrCzB,EAAA,CAAAK,SAAA,GAAuB;IAAvBL,EAAA,CAAAkC,UAAA,SAAAqE,OAAA,CAAAO,QAAA,CAAArF,WAAA,EAAuB;IA8B1BzB,EAAA,CAAAK,SAAA,GAAyC;IAAzCL,EAAA,CAAAkC,UAAA,SAAAqE,OAAA,CAAAM,cAAA,CAAApF,WAAA,cAAyC;IA8HpCzB,EAAA,CAAAK,SAAA,GAA0C;IAA1CL,EAAA,CAAA+G,iBAAA,CAAAR,OAAA,CAAAS,iBAAA,CAAAvF,WAAA,CAAAC,SAAA,EAA0C;IAE7C1B,EAAA,CAAAK,SAAA,GAA0C;IAA1CL,EAAA,CAAAkC,UAAA,UAAAT,WAAA,CAAAO,MAAA,kBAAAP,WAAA,CAAAO,MAAA,CAAAC,EAAA,MAAAsE,OAAA,CAAA1D,aAAA,CAA0C;;;;;IA+BrD7C,EAAA,CAAAE,cAAA,eAGC;IACCF,EAAA,CAAAC,SAAA,eASE;IACFD,EAAA,CAAAE,cAAA,eAOC;IAEGF,EAAA,CAAAC,SAAA,eAQO;IAmBTD,EAAA,CAAAI,YAAA,EAAM;;;;IA7CNJ,EAAA,CAAAK,SAAA,GAAqE;IAArEL,EAAA,CAAAkC,UAAA,SAAA+E,OAAA,CAAAzG,gBAAA,kBAAAyG,OAAA,CAAAzG,gBAAA,CAAA2B,KAAA,yCAAAnC,EAAA,CAAAoC,aAAA,CAAqE,QAAA6E,OAAA,CAAAzG,gBAAA,kBAAAyG,OAAA,CAAAzG,gBAAA,CAAAc,QAAA;;;;;IAhT3EtB,EAAA,CAAAE,cAAA,cAGC;IACCF,EAAA,CAAAyD,UAAA,IAAAyD,mDAAA,6BAoSe;IAGflH,EAAA,CAAAyD,UAAA,IAAA0D,0CAAA,kBAoDM;IACRnH,EAAA,CAAAI,YAAA,EAAM;;;;IA1VuBJ,EAAA,CAAAK,SAAA,GACZ;IADYL,EAAA,CAAAkC,UAAA,YAAAkF,OAAA,CAAAC,QAAA,CACZ,iBAAAD,OAAA,CAAAE,gBAAA;IAqSZtH,EAAA,CAAAK,SAAA,GAAuB;IAAvBL,EAAA,CAAAkC,UAAA,SAAAkF,OAAA,CAAAG,iBAAA,CAAuB;;;;;IA8ItBvH,EAAA,CAAAC,SAAA,eAYO;;;;;IA4DTD,EAAA,CAAAC,SAAA,aAA4D;;;;;IAC5DD,EAAA,CAAAC,SAAA,eAUO;;;;;;IAmCLD,EAAA,CAAAE,cAAA,kBAeC;IAbCF,EAAA,CAAAY,UAAA,mBAAA4G,sEAAA;MAAA,MAAA/B,WAAA,GAAAzF,EAAA,CAAAc,aAAA,CAAA2G,IAAA;MAAA,MAAAC,SAAA,GAAAjC,WAAA,CAAA5D,SAAA;MAAA,MAAA8F,OAAA,GAAA3H,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAmB,WAAA,CAAAwG,OAAA,CAAAC,WAAA,CAAAF,SAAA,CAAkB;IAAA,EAAC;IAc5B1H,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;IAHPJ,EAAA,CAAAkC,UAAA,UAAAwF,SAAA,CAAAG,IAAA,CAAoB;IAEpB7H,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAoH,SAAA,CAAAI,KAAA,MACF;;;;;IA/CN9H,EAAA,CAAAE,cAAA,eAeC;IAUKF,EAAA,CAAAG,MAAA,oBACF;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAE,cAAA,eAEC;IACCF,EAAA,CAAAyD,UAAA,IAAAsE,6CAAA,sBAiBS;IACX/H,EAAA,CAAAI,YAAA,EAAM;;;;IAjBgBJ,EAAA,CAAAK,SAAA,GAA8C;IAA9CL,EAAA,CAAAkC,UAAA,YAAA8F,OAAA,CAAAC,oBAAA,CAAAD,OAAA,CAAAE,qBAAA,EAA8C;;;;;;IAsBxElI,EAAA,CAAAE,cAAA,eAaC;IAUKF,EAAA,CAAAG,MAAA,4BACF;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAE,cAAA,eAMC;IAGGF,EAAA,CAAAY,UAAA,mBAAAuH,6DAAA;MAAAnI,EAAA,CAAAc,aAAA,CAAAsH,IAAA;MAAA,MAAAC,OAAA,GAAArI,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAmB,WAAA,CAAAkH,OAAA,CAAAC,gBAAA,CAAiB,OAAO,CAAC;IAAA,EAAC;IAgBnCtI,EAAA,CAAAE,cAAA,eAUC;IACCF,EAAA,CAAAC,SAAA,aAGK;IACPD,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,gBACG;IAAAF,EAAA,CAAAG,MAAA,aAAM;IAAAH,EAAA,CAAAI,YAAA,EACR;IAIHJ,EAAA,CAAAE,cAAA,mBAgBC;IAfCF,EAAA,CAAAY,UAAA,mBAAA2H,8DAAA;MAAAvI,EAAA,CAAAc,aAAA,CAAAsH,IAAA;MAAA,MAAAI,OAAA,GAAAxI,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAmB,WAAA,CAAAqH,OAAA,CAAAF,gBAAA,CAAiB,UAAU,CAAC;IAAA,EAAC;IAgBtCtI,EAAA,CAAAE,cAAA,gBAUC;IACCF,EAAA,CAAAC,SAAA,cAGK;IACPD,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,iBACG;IAAAF,EAAA,CAAAG,MAAA,iBAAS;IAAAH,EAAA,CAAAI,YAAA,EACX;IAIHJ,EAAA,CAAAE,cAAA,mBAgBC;IAfCF,EAAA,CAAAY,UAAA,mBAAA6H,8DAAA;MAAAzI,EAAA,CAAAc,aAAA,CAAAsH,IAAA;MAAA,MAAAM,OAAA,GAAA1I,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAmB,WAAA,CAAAuH,OAAA,CAAAC,UAAA,EAAY;IAAA,EAAC;IAgBtB3I,EAAA,CAAAE,cAAA,gBAUC;IACCF,EAAA,CAAAC,SAAA,cAGK;IACPD,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,iBACG;IAAAF,EAAA,CAAAG,MAAA,mBAAM;IAAAH,EAAA,CAAAI,YAAA,EACR;;;;;;IAkBXJ,EAAA,CAAAE,cAAA,eAYC;IADCF,EAAA,CAAAY,UAAA,mBAAAgI,0DAAA;MAAA5I,EAAA,CAAAc,aAAA,CAAA+H,IAAA;MAAA,MAAAC,OAAA,GAAA9I,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAmB,WAAA,CAAA2H,OAAA,CAAAC,aAAA,EAAe;IAAA,EAAC;IAC1B/I,EAAA,CAAAI,YAAA,EAAM;;;;;IAgDDJ,EAAA,CAAAC,SAAA,eAWO;;;;;IAHLD,EAAA,CAAAqC,WAAA,WAAA2G,QAAA,OAAwB,uDAAAC,KAAA;;;;;;IArDhCjJ,EAAA,CAAAE,cAAA,eAmBC;IAcGF,EAAA,CAAAC,SAAA,aAAyD;IAC3DD,EAAA,CAAAI,YAAA,EAAM;IAGNJ,EAAA,CAAAE,cAAA,eAAqB;IAGjBF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAGNJ,EAAA,CAAAE,cAAA,eAAwE;IACtEF,EAAA,CAAAyD,UAAA,IAAAyF,0CAAA,mBAWO;IACTlJ,EAAA,CAAAI,YAAA,EAAM;IAGNJ,EAAA,CAAAE,cAAA,eAA4D;IAC1DF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAIRJ,EAAA,CAAAE,cAAA,eAAqC;IAGjCF,EAAA,CAAAY,UAAA,mBAAAuI,8DAAAhG,MAAA;MAAAnD,EAAA,CAAAc,aAAA,CAAAsI,IAAA;MAAA,MAAAC,OAAA,GAAArJ,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAmB,WAAA,CAAAkI,OAAA,CAAAC,cAAA,CAAAnG,MAAA,CAAsB;IAAA,EAAC;IAkBhCnD,EAAA,CAAAC,SAAA,cAAoD;IACtDD,EAAA,CAAAI,YAAA,EAAS;IAGTJ,EAAA,CAAAE,cAAA,mBAkBC;IAjBCF,EAAA,CAAAY,UAAA,mBAAA2I,8DAAApG,MAAA;MAAAnD,EAAA,CAAAc,aAAA,CAAAsI,IAAA;MAAA,MAAAI,OAAA,GAAAxJ,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAmB,WAAA,CAAAqI,OAAA,CAAAC,WAAA,CAAAtG,MAAA,CAAmB;IAAA,EAAC;IAkB7BnD,EAAA,CAAAC,SAAA,cAA0D;IAC5DD,EAAA,CAAAI,YAAA,EAAS;;;;IAvEPJ,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAoJ,OAAA,CAAAC,uBAAA,CAAAD,OAAA,CAAAE,sBAAA,OACF;IAKqB5J,EAAA,CAAAK,SAAA,GAAe;IAAfL,EAAA,CAAAkC,UAAA,YAAAwH,OAAA,CAAA3E,UAAA,CAAe;IAelC/E,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,cAAAoJ,OAAA,CAAAG,kBAAA,QACF;;;AD7xCN,OAAM,MAAOC,oBAAoB;EA+I/BC,YACUC,EAAe,EACfC,KAAqB,EACrBC,MAAc,EACdC,cAA8B,EAC9BC,WAAwB,EACxBC,YAA0B,EAC1BC,GAAsB;IANtB,KAAAN,EAAE,GAAFA,EAAE;IACF,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,GAAG,GAAHA,GAAG;IA5Ib;IACA,KAAAC,YAAY,GAAQ,IAAI;IACxB,KAAAlD,QAAQ,GAAU,EAAE;IACpB,KAAAxE,aAAa,GAAkB,IAAI;IACnC,KAAA2H,eAAe,GAAG,KAAK;IAEvB,KAAAhK,gBAAgB,GAAQ,IAAI;IAE5B;IACA,KAAAiK,SAAS,GAAG,KAAK;IACjB,KAAAC,aAAa,GAAG,KAAK;IACrB,KAAAC,eAAe,GAAG,IAAI;IACtB,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAC,kBAAkB,GAAG,KAAK;IAC1B,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAC,WAAW,GAAG,EAAE;IAChB,KAAAC,aAAa,GAAU,EAAE;IACzB,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAC,gBAAgB,GAAG,KAAK;IACxB,KAAA3D,iBAAiB,GAAG,KAAK;IACzB,KAAAnG,YAAY,GAAG,KAAK;IACpB,KAAA+J,sBAAsB,GAAG,KAAK;IAC9B,KAAAC,eAAe,GAAQ,IAAI;IAC3B,KAAAC,mBAAmB,GAAG;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAC,CAAE;IACpC,KAAAC,kBAAkB,GAAG,KAAK;IAC1B,KAAAC,qBAAqB,GAAQ,IAAI;IAEjC,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAC,aAAa,GAAQ,IAAI;IACzB,KAAAC,cAAc,GAAG,CAAC;IAClB,KAAAC,WAAW,GAAG,KAAK;IACnB,KAAAC,UAAU,GAAG,KAAK;IAElB;IACA,KAAAC,gBAAgB,GAAG,KAAK;IACxB,KAAAnC,sBAAsB,GAAG,CAAC;IAC1B,KAAAoC,mBAAmB,GAAwC,MAAM;IACzD,KAAAC,aAAa,GAAyB,IAAI;IAC1C,KAAAC,WAAW,GAAW,EAAE;IACxB,KAAAC,cAAc,GAAQ,IAAI;IAClC,KAAApH,UAAU,GAAa,CACrB,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CACzD;IAED;IACQ,KAAAqH,YAAY,GAA4B,IAAI;IAC5C,KAAAC,gBAAgB,GAAkB,IAAI;IACtC,KAAAC,aAAa,GAOjB,EAAE;IAEN;IACA,KAAAC,QAAQ,GAAG,KAAK;IAChB,KAAAC,QAAQ,GAA6B,IAAI;IACzC,KAAAC,YAAY,GAAG,CAAC;IACR,KAAAC,SAAS,GAAQ,IAAI;IAE7B;IACA,KAAAC,UAAU,GAAQ,IAAI;IACtB,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAC,OAAO,GAAG,KAAK;IACf,KAAAC,cAAc,GAAG,IAAI;IACrB,KAAAC,iBAAiB,GAA4B,IAAI;IACjD,KAAAC,kBAAkB,GAA4B,IAAI;IAElD;IACA,KAAAC,eAAe,GAAU,CACvB;MACEhL,EAAE,EAAE,SAAS;MACb4F,IAAI,EAAE,SAAS;MACfqF,IAAI,EAAE,IAAI;MACVC,MAAM,EAAE,CACN;QAAErF,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAe,CAAE,EACtC;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAA6B,CAAE,EACpD;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAiC,CAAE,EACxD;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAgC,CAAE,EACvD;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAyB,CAAE,EAChD;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAA0B,CAAE,EACjD;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAwB,CAAE,EAC/C;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAA+B,CAAE,EACtD;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAgC,CAAE,EACvD;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAwB,CAAE;KAElD,EACD;MACE5F,EAAE,EAAE,QAAQ;MACZ4F,IAAI,EAAE,QAAQ;MACdqF,IAAI,EAAE,IAAI;MACVC,MAAM,EAAE,CACN;QAAErF,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAM,CAAE,EAC7B;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAO,CAAE,EAC9B;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAK,CAAE,EAC5B;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAM,CAAE,EAC7B;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAQ,CAAE,EAC/B;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAK,CAAE,EAC5B;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAO,CAAE,EAC9B;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAS,CAAE,EAChC;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAW,CAAE;KAErC,EACD;MACE5F,EAAE,EAAE,QAAQ;MACZ4F,IAAI,EAAE,QAAQ;MACdqF,IAAI,EAAE,IAAI;MACVC,MAAM,EAAE,CACN;QAAErF,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAU,CAAE,EACjC;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAU,CAAE,EACjC;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAY,CAAE,EACnC;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAS,CAAE,EAChC;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAa,CAAE,EACpC;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAK,CAAE,EAC5B;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAM,CAAE,EAC7B;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAO,CAAE;KAEjC,CACF;IACD,KAAAK,qBAAqB,GAAG,IAAI,CAAC+E,eAAe,CAAC,CAAC,CAAC;IAE/C;IACiB,KAAAG,oBAAoB,GAAG,EAAE;IAClC,KAAAC,WAAW,GAAG,CAAC;IAEvB;IACA,KAAAC,QAAQ,GAAG,KAAK;IAChB,KAAAC,YAAY,GAAG,KAAK;IACZ,KAAAC,aAAa,GAAQ,IAAI;IACzB,KAAAC,aAAa,GAAG,IAAI3N,YAAY,EAAE;IAWxC,IAAI,CAAC4N,WAAW,GAAG,IAAI,CAAC1D,EAAE,CAAC2D,KAAK,CAAC;MAC/BjL,OAAO,EAAE,CAAC,EAAE,EAAE,CAAC7C,UAAU,CAAC+N,QAAQ,EAAE/N,UAAU,CAACgO,SAAS,CAAC,CAAC,CAAC,CAAC;KAC7D,CAAC;EACJ;EAEA;EACAC,eAAeA,CAAA;IACb,OACE,CAAC,IAAI,CAACtN,gBAAgB,IAAI,IAAI,CAACuL,gBAAgB,IAAI,IAAI,CAACb,gBAAgB;EAE5E;EAEA;EACQ6C,gBAAgBA,CAAA;IACtB,MAAMC,cAAc,GAAG,IAAI,CAACN,WAAW,CAACO,GAAG,CAAC,SAAS,CAAC;IACtD,IAAI,IAAI,CAACH,eAAe,EAAE,EAAE;MAC1BE,cAAc,EAAEE,OAAO,EAAE;KAC1B,MAAM;MACLF,cAAc,EAAEG,MAAM,EAAE;;EAE5B;EAEAC,QAAQA,CAAA;IACNC,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;IAClD,IAAI,CAACC,mBAAmB,EAAE;IAE1B;IACA,IAAI,CAACC,8BAA8B,EAAE;EACvC;EAEQA,8BAA8BA,CAAA;IACpC,MAAMC,YAAY,GAAGA,CAAA,KAAK;MACxB,IAAI,CAACrE,WAAW,CAACqE,YAAY,EAAE;MAC/BC,QAAQ,CAACC,mBAAmB,CAAC,OAAO,EAAEF,YAAY,CAAC;MACnDC,QAAQ,CAACC,mBAAmB,CAAC,SAAS,EAAEF,YAAY,CAAC;MACrDC,QAAQ,CAACC,mBAAmB,CAAC,YAAY,EAAEF,YAAY,CAAC;IAC1D,CAAC;IAEDC,QAAQ,CAACE,gBAAgB,CAAC,OAAO,EAAEH,YAAY,EAAE;MAAEI,IAAI,EAAE;IAAI,CAAE,CAAC;IAChEH,QAAQ,CAACE,gBAAgB,CAAC,SAAS,EAAEH,YAAY,EAAE;MAAEI,IAAI,EAAE;IAAI,CAAE,CAAC;IAClEH,QAAQ,CAACE,gBAAgB,CAAC,YAAY,EAAEH,YAAY,EAAE;MAAEI,IAAI,EAAE;IAAI,CAAE,CAAC;EACvE;EAEQN,mBAAmBA,CAAA;IACzB,IAAI,CAACO,eAAe,EAAE;IACtB,IAAI,CAACC,gBAAgB,EAAE;IACvB,IAAI,CAACC,sBAAsB,EAAE;EAC/B;EAEQA,sBAAsBA,CAAA;IAC5B;IACA,IAAI,CAACvB,aAAa,CAACwB,GAAG,CACpB,IAAI,CAAC7E,WAAW,CAAC8E,aAAa,CAACC,SAAS,CAAC;MACvCC,IAAI,EAAGC,YAAY,IAAI;QACrB,IAAIA,YAAY,EAAE;UAChBhB,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEe,YAAY,CAAC;UACvD,IAAI,CAACC,kBAAkB,CAACD,YAAY,CAAC;;MAEzC,CAAC;MACDE,KAAK,EAAGA,KAAK,IAAI;QACflB,OAAO,CAACkB,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;MAChE;KACD,CAAC,CACH;IAED;IACA,IAAI,CAAC9B,aAAa,CAACwB,GAAG,CACpB,IAAI,CAAC7E,WAAW,CAACoF,WAAW,CAACL,SAAS,CAAC;MACrCC,IAAI,EAAGK,IAAI,IAAI;QACb,IAAIA,IAAI,EAAE;UACRpB,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEmB,IAAI,CAAC;UAC5C,IAAI,CAAC9C,UAAU,GAAG8C,IAAI;;MAE1B,CAAC;MACDF,KAAK,EAAGA,KAAK,IAAI;QACflB,OAAO,CAACkB,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC9D;KACD,CAAC,CACH;EACH;EAEQD,kBAAkBA,CAACD,YAA0B;IACnD;IACA;IACAhB,OAAO,CAACC,GAAG,CACT,iCAAiC,EACjCe,YAAY,CAACK,MAAM,CAACpO,QAAQ,CAC7B;IAED;IACA,IAAI,CAAC6I,cAAc,CAACwF,IAAI,CAAC,UAAU,CAAC;IAEpC;IACA;IACA;EACF;;EAEQb,eAAeA,CAAA;IACrB,IAAI;MACF,MAAMc,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;MAC/CzB,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEsB,UAAU,CAAC;MAEzD,IAAI,CAACA,UAAU,IAAIA,UAAU,KAAK,MAAM,IAAIA,UAAU,KAAK,WAAW,EAAE;QACtEvB,OAAO,CAACkB,KAAK,CAAC,gCAAgC,CAAC;QAC/C,IAAI,CAAC1M,aAAa,GAAG,IAAI;QACzB,IAAI,CAAC2H,eAAe,GAAG,KAAK;QAC5B;;MAGF,MAAMuF,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACL,UAAU,CAAC;MACnCvB,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEyB,IAAI,CAAC;MAE3C;MACA,MAAMG,MAAM,GAAGH,IAAI,CAACI,GAAG,IAAIJ,IAAI,CAAC9N,EAAE,IAAI8N,IAAI,CAACG,MAAM;MACjD7B,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE;QAC3C6B,GAAG,EAAEJ,IAAI,CAACI,GAAG;QACblO,EAAE,EAAE8N,IAAI,CAAC9N,EAAE;QACXiO,MAAM,EAAEH,IAAI,CAACG,MAAM;QACnBE,SAAS,EAAEF;OACZ,CAAC;MAEF,IAAIA,MAAM,EAAE;QACV,IAAI,CAACrN,aAAa,GAAGqN,MAAM;QAC3B,IAAI,CAAC1F,eAAe,GAAGuF,IAAI,CAACzO,QAAQ,IAAIyO,IAAI,CAAClI,IAAI,IAAI,KAAK;QAC1DwG,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAE;UACjDrM,EAAE,EAAE,IAAI,CAACY,aAAa;UACtBvB,QAAQ,EAAE,IAAI,CAACkJ;SAChB,CAAC;OACH,MAAM;QACL6D,OAAO,CAACkB,KAAK,CAAC,0CAA0C,EAAEQ,IAAI,CAAC;QAC/D,IAAI,CAAClN,aAAa,GAAG,IAAI;QACzB,IAAI,CAAC2H,eAAe,GAAG,KAAK;;KAE/B,CAAC,OAAO+E,KAAK,EAAE;MACdlB,OAAO,CAACkB,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;MAC/D,IAAI,CAAC1M,aAAa,GAAG,IAAI;MACzB,IAAI,CAAC2H,eAAe,GAAG,KAAK;;EAEhC;EAEQuE,gBAAgBA,CAAA;IACtB,MAAMsB,cAAc,GAAG,IAAI,CAACpG,KAAK,CAACqG,QAAQ,CAACC,QAAQ,CAACtC,GAAG,CAAC,IAAI,CAAC;IAC7DI,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE+B,cAAc,CAAC;IAE5D,IAAI,CAACA,cAAc,EAAE;MACnB,IAAI,CAAChG,YAAY,CAACmG,SAAS,CAAC,6BAA6B,CAAC;MAC1D;;IAGF,IAAI,CAAC/F,SAAS,GAAG,IAAI;IACrB,IAAI,CAACN,cAAc,CAACsG,eAAe,CAACJ,cAAc,CAAC,CAAClB,SAAS,CAAC;MAC5DC,IAAI,EAAG7E,YAAY,IAAI;QACrB8D,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE/D,YAAY,CAAC;QACjE8D,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE;UACxCrM,EAAE,EAAEsI,YAAY,EAAEtI,EAAE;UACpByO,YAAY,EAAEnG,YAAY,EAAEmG,YAAY;UACxCC,iBAAiB,EAAEpG,YAAY,EAAEmG,YAAY,EAAEE,MAAM;UACrDC,OAAO,EAAEtG,YAAY,EAAEsG,OAAO;UAC9BxJ,QAAQ,EAAEkD,YAAY,EAAElD,QAAQ;UAChCyJ,aAAa,EAAEvG,YAAY,EAAElD,QAAQ,EAAEuJ;SACxC,CAAC;QACF,IAAI,CAACrG,YAAY,GAAGA,YAAY;QAChC,IAAI,CAACwG,mBAAmB,EAAE;QAC1B,IAAI,CAACC,YAAY,EAAE;QAEnB;QACA,IAAI,CAACC,kBAAkB,EAAE;MAC3B,CAAC;MACD1B,KAAK,EAAGA,KAAK,IAAI;QACflB,OAAO,CAACkB,KAAK,CAAC,+CAA+C,EAAEA,KAAK,CAAC;QACrE,IAAI,CAAClF,YAAY,CAACmG,SAAS,CACzB,8CAA8C,CAC/C;QACD,IAAI,CAAC/F,SAAS,GAAG,KAAK;MACxB;KACD,CAAC;EACJ;EAEQsG,mBAAmBA,CAAA;IACzB,IACE,CAAC,IAAI,CAACxG,YAAY,EAAEmG,YAAY,IAChC,IAAI,CAACnG,YAAY,CAACmG,YAAY,CAACE,MAAM,KAAK,CAAC,EAC3C;MACAvC,OAAO,CAAC6C,IAAI,CAAC,uCAAuC,CAAC;MACrD,IAAI,CAAC1Q,gBAAgB,GAAG,IAAI;MAC5B;;IAGF6N,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;IAC3CD,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE,IAAI,CAACzL,aAAa,CAAC;IACnDwL,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE,IAAI,CAAC/D,YAAY,CAACmG,YAAY,CAAC;IAEhE;IACA;IAEA,IAAI,IAAI,CAACnG,YAAY,CAACsG,OAAO,EAAE;MAC7B;MACA;MACA,IAAI,CAACrQ,gBAAgB,GAAG,IAAI,CAAC+J,YAAY,CAACmG,YAAY,CAACS,IAAI,CAAEC,CAAM,IAAI;QACrE,MAAMC,aAAa,GAAGD,CAAC,CAACnP,EAAE,IAAImP,CAAC,CAACjB,GAAG;QACnC,OAAOmB,MAAM,CAACD,aAAa,CAAC,KAAKC,MAAM,CAAC,IAAI,CAACzO,aAAa,CAAC;MAC7D,CAAC,CAAC;KACH,MAAM;MACL;MACA,IAAI,CAACrC,gBAAgB,GAAG,IAAI,CAAC+J,YAAY,CAACmG,YAAY,CAACS,IAAI,CAAEC,CAAM,IAAI;QACrE,MAAMC,aAAa,GAAGD,CAAC,CAACnP,EAAE,IAAImP,CAAC,CAACjB,GAAG;QACnC9B,OAAO,CAACC,GAAG,CACT,2BAA2B,EAC3B+C,aAAa,EACb,uBAAuB,EACvB,IAAI,CAACxO,aAAa,CACnB;QACD,OAAOyO,MAAM,CAACD,aAAa,CAAC,KAAKC,MAAM,CAAC,IAAI,CAACzO,aAAa,CAAC;MAC7D,CAAC,CAAC;;IAGJ;IACA,IAAI,CAAC,IAAI,CAACrC,gBAAgB,IAAI,IAAI,CAAC+J,YAAY,CAACmG,YAAY,CAACE,MAAM,GAAG,CAAC,EAAE;MACvEvC,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;MAChD,IAAI,CAAC9N,gBAAgB,GAAG,IAAI,CAAC+J,YAAY,CAACmG,YAAY,CAAC,CAAC,CAAC;MAEzD;MACA,IAAI,IAAI,CAACnG,YAAY,CAACmG,YAAY,CAACE,MAAM,GAAG,CAAC,EAAE;QAC7C,MAAMW,kBAAkB,GACtB,IAAI,CAAC/Q,gBAAgB,CAACyB,EAAE,IAAI,IAAI,CAACzB,gBAAgB,CAAC2P,GAAG;QACvD,IAAImB,MAAM,CAACC,kBAAkB,CAAC,KAAKD,MAAM,CAAC,IAAI,CAACzO,aAAa,CAAC,EAAE;UAC7DwL,OAAO,CAACC,GAAG,CACT,6DAA6D,CAC9D;UACD,IAAI,CAAC9N,gBAAgB,GAAG,IAAI,CAAC+J,YAAY,CAACmG,YAAY,CAAC,CAAC,CAAC;;;;IAK/D;IACA,IAAI,IAAI,CAAClQ,gBAAgB,EAAE;MACzB6N,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE;QACnDrM,EAAE,EAAE,IAAI,CAACzB,gBAAgB,CAACyB,EAAE,IAAI,IAAI,CAACzB,gBAAgB,CAAC2P,GAAG;QACzD7O,QAAQ,EAAE,IAAI,CAACd,gBAAgB,CAACc,QAAQ;QACxCa,KAAK,EAAE,IAAI,CAAC3B,gBAAgB,CAAC2B,KAAK;QAClC1B,QAAQ,EAAE,IAAI,CAACD,gBAAgB,CAACC;OACjC,CAAC;MAEF;MACA4N,OAAO,CAACC,GAAG,CACT,qCAAqC,EACrC,IAAI,CAAC9N,gBAAgB,CAACc,QAAQ,CAC/B;MACD+M,OAAO,CAACC,GAAG,CACT,+BAA+B,EAC/B,IAAI,CAAC9N,gBAAgB,CAACc,QAAQ,CAC/B;KACF,MAAM;MACL+M,OAAO,CAACkB,KAAK,CAAC,uDAAuD,CAAC;MACtElB,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE,IAAI,CAAC/D,YAAY,CAACmG,YAAY,CAAC;MACzErC,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE,IAAI,CAACzL,aAAa,CAAC;MAEnD;MACAwL,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;;IAGrD;IACA,IAAI,CAACP,gBAAgB,EAAE;EACzB;EAEQiD,YAAYA,CAAA;IAClB,IAAI,CAAC,IAAI,CAACzG,YAAY,EAAEtI,EAAE,EAAE;IAE5B;IACA,IAAIoF,QAAQ,GAAG,IAAI,CAACkD,YAAY,CAAClD,QAAQ,IAAI,EAAE;IAE/C;IACA,IAAI,CAACA,QAAQ,GAAGA,QAAQ,CAACmK,IAAI,CAAC,CAACC,CAAM,EAAEC,CAAM,KAAI;MAC/C,MAAMC,KAAK,GAAG,IAAIC,IAAI,CAACH,CAAC,CAAC/P,SAAS,IAAI+P,CAAC,CAACI,SAAS,CAAC,CAACC,OAAO,EAAE;MAC5D,MAAMC,KAAK,GAAG,IAAIH,IAAI,CAACF,CAAC,CAAChQ,SAAS,IAAIgQ,CAAC,CAACG,SAAS,CAAC,CAACC,OAAO,EAAE;MAC5D,OAAOH,KAAK,GAAGI,KAAK,CAAC,CAAC;IACxB,CAAC,CAAC;;IAEF1D,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE;MAC5C0D,KAAK,EAAE,IAAI,CAAC3K,QAAQ,CAACuJ,MAAM;MAC3BqB,KAAK,EAAE,IAAI,CAAC5K,QAAQ,CAAC,CAAC,CAAC,EAAE3E,OAAO;MAChCwP,IAAI,EAAE,IAAI,CAAC7K,QAAQ,CAAC,IAAI,CAACA,QAAQ,CAACuJ,MAAM,GAAG,CAAC,CAAC,EAAElO;KAChD,CAAC;IAEF,IAAI,CAACiI,eAAe,GAAG,IAAI,CAACtD,QAAQ,CAACuJ,MAAM,KAAK,IAAI,CAACxD,oBAAoB;IACzE,IAAI,CAAC3C,SAAS,GAAG,KAAK;IACtB,IAAI,CAAC0H,cAAc,EAAE;EACvB;EAEAC,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAAC1H,aAAa,IAAI,CAAC,IAAI,CAACC,eAAe,IAAI,CAAC,IAAI,CAACJ,YAAY,EAAEtI,EAAE,EACvE;IAEF,IAAI,CAACyI,aAAa,GAAG,IAAI;IACzB,IAAI,CAAC2C,WAAW,EAAE;IAElB;IACA,MAAMgF,MAAM,GAAG,IAAI,CAAChL,QAAQ,CAACuJ,MAAM;IAEnC,IAAI,CAACzG,cAAc,CAACmI,WAAW,CAC7B,IAAI,CAACzP,aAAc;IAAE;IACrB,IAAI,CAACrC,gBAAgB,EAAEyB,EAAE,IAAI,IAAI,CAACzB,gBAAgB,EAAE2P,GAAI;IAAE;IAC1D,IAAI,CAAC5F,YAAY,CAACtI,EAAE,EACpB,IAAI,CAACoL,WAAW,EAChB,IAAI,CAACD,oBAAoB,CAC1B,CAAC+B,SAAS,CAAC;MACVC,IAAI,EAAGmD,WAAkB,IAAI;QAC3B,IAAIA,WAAW,IAAIA,WAAW,CAAC3B,MAAM,GAAG,CAAC,EAAE;UACzC;UACA,IAAI,CAACvJ,QAAQ,GAAG,CAAC,GAAGkL,WAAW,CAACC,OAAO,EAAE,EAAE,GAAG,IAAI,CAACnL,QAAQ,CAAC;UAC5D,IAAI,CAACsD,eAAe,GAClB4H,WAAW,CAAC3B,MAAM,KAAK,IAAI,CAACxD,oBAAoB;SACnD,MAAM;UACL,IAAI,CAACzC,eAAe,GAAG,KAAK;;QAE9B,IAAI,CAACD,aAAa,GAAG,KAAK;MAC5B,CAAC;MACD6E,KAAK,EAAGA,KAAK,IAAI;QACflB,OAAO,CAACkB,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;QAC/D,IAAI,CAAClF,YAAY,CAACmG,SAAS,CAAC,wCAAwC,CAAC;QACrE,IAAI,CAAC9F,aAAa,GAAG,KAAK;QAC1B,IAAI,CAAC2C,WAAW,EAAE,CAAC,CAAC;MACtB;KACD,CAAC;EACJ;;EAEQ4D,kBAAkBA,CAAA;IACxB,IAAI,CAAC,IAAI,CAAC1G,YAAY,EAAEtI,EAAE,EAAE;MAC1BoM,OAAO,CAAC6C,IAAI,CAAC,kDAAkD,CAAC;MAChE;;IAGF7C,OAAO,CAACC,GAAG,CACT,yDAAyD,EACzD,IAAI,CAAC/D,YAAY,CAACtI,EAAE,CACrB;IAED;IACAoM,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;IACpD,IAAI,CAACb,aAAa,CAACwB,GAAG,CACpB,IAAI,CAAC9E,cAAc,CAACsI,sBAAsB,CACxC,IAAI,CAAClI,YAAY,CAACtI,EAAE,CACrB,CAACkN,SAAS,CAAC;MACVC,IAAI,EAAGsD,UAAe,IAAI;QACxBrE,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAEoE,UAAU,CAAC;QACpErE,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE;UACnCrM,EAAE,EAAEyQ,UAAU,CAACzQ,EAAE;UACjB0Q,IAAI,EAAED,UAAU,CAACC,IAAI;UACrBjQ,OAAO,EAAEgQ,UAAU,CAAChQ,OAAO;UAC3BV,MAAM,EAAE0Q,UAAU,CAAC1Q,MAAM;UACzB4Q,QAAQ,EAAEF,UAAU,CAACE,QAAQ;UAC7BC,UAAU,EAAEH,UAAU,CAACG,UAAU;UACjCC,WAAW,EAAEJ,UAAU,CAACI;SACzB,CAAC;QAEF;QACAzE,OAAO,CAACC,GAAG,CACT,mCAAmC,EACnC,IAAI,CAACzH,cAAc,CAAC6L,UAAU,CAAC,CAChC;QACDrE,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE,IAAI,CAACxH,QAAQ,CAAC4L,UAAU,CAAC,CAAC;QAC/DrE,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAACyE,OAAO,CAACL,UAAU,CAAC,CAAC;QAC7DrE,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE,IAAI,CAAC1K,WAAW,CAAC8O,UAAU,CAAC,CAAC;QAClE,IAAIA,UAAU,CAACI,WAAW,EAAE;UAC1BJ,UAAU,CAACI,WAAW,CAACE,OAAO,CAAC,CAACC,GAAQ,EAAEC,KAAa,KAAI;YACzD7E,OAAO,CAACC,GAAG,CAAC,yBAAyB4E,KAAK,GAAG,EAAE;cAC7CP,IAAI,EAAEM,GAAG,CAACN,IAAI;cACdQ,GAAG,EAAEF,GAAG,CAACE,GAAG;cACZC,IAAI,EAAEH,GAAG,CAACG,IAAI;cACdvL,IAAI,EAAEoL,GAAG,CAACpL,IAAI;cACdwL,IAAI,EAAEJ,GAAG,CAACI;aACX,CAAC;UACJ,CAAC,CAAC;;QAGJ;QACA,MAAMC,aAAa,GAAG,IAAI,CAACjM,QAAQ,CAACkM,IAAI,CACrCC,GAAG,IAAKA,GAAG,CAACvR,EAAE,KAAKyQ,UAAU,CAACzQ,EAAE,CAClC;QACD,IAAI,CAACqR,aAAa,EAAE;UAClB;UACA,IAAI,CAACjM,QAAQ,CAACoM,IAAI,CAACf,UAAU,CAAC;UAC9BrE,OAAO,CAACC,GAAG,CACT,0CAA0C,EAC1C,IAAI,CAACjH,QAAQ,CAACuJ,MAAM,CACrB;UAED;UACA,IAAI,CAACtG,GAAG,CAACoJ,aAAa,EAAE;UAExB;UACAC,UAAU,CAAC,MAAK;YACd,IAAI,CAACxB,cAAc,EAAE;UACvB,CAAC,EAAE,EAAE,CAAC;UAEN;UACA,MAAMS,QAAQ,GAAGF,UAAU,CAAC1Q,MAAM,EAAEC,EAAE,IAAIyQ,UAAU,CAACE,QAAQ;UAC7DvE,OAAO,CAACC,GAAG,CAAC,kDAAkD,EAAE;YAC9DsE,QAAQ;YACR/P,aAAa,EAAE,IAAI,CAACA,aAAa;YACjC+Q,gBAAgB,EAAEhB,QAAQ,KAAK,IAAI,CAAC/P;WACrC,CAAC;UAEF,IAAI+P,QAAQ,IAAIA,QAAQ,KAAK,IAAI,CAAC/P,aAAa,EAAE;YAC/C,IAAI,CAACgR,iBAAiB,CAACnB,UAAU,CAACzQ,EAAE,CAAC;;;MAG3C,CAAC;MACDsN,KAAK,EAAGA,KAAK,IAAI;QACflB,OAAO,CAACkB,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MAC1D;KACD,CAAC,CACH;IAED;IACAlB,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;IAC7D,IAAI,CAACb,aAAa,CAACwB,GAAG,CACpB,IAAI,CAAC9E,cAAc,CAAC2J,0BAA0B,CAC5C,IAAI,CAACvJ,YAAY,CAACtI,EAAE,CACrB,CAACkN,SAAS,CAAC;MACVC,IAAI,EAAG2E,UAAe,IAAI;QACxB1F,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEyF,UAAU,CAAC;QAExD;QACA,IAAIA,UAAU,CAAC7D,MAAM,KAAK,IAAI,CAACrN,aAAa,EAAE;UAC5C,IAAI,CAAC0E,iBAAiB,GAAGwM,UAAU,CAACzG,QAAQ;UAC5C,IAAI,CAACC,YAAY,GAAGwG,UAAU,CAACzG,QAAQ,CAAC,CAAC;UACzCe,OAAO,CAACC,GAAG,CACT,sCAAsC,EACtC,IAAI,CAAC/G,iBAAiB,CACvB;UACD,IAAI,CAAC+C,GAAG,CAACoJ,aAAa,EAAE;;MAE5B,CAAC;MACDnE,KAAK,EAAGA,KAAU,IAAI;QACpBlB,OAAO,CAACkB,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACzD;KACD,CAAC,CACH;IAED;IACA,IAAI,CAAC9B,aAAa,CAACwB,GAAG,CACpB,IAAI,CAAC9E,cAAc,CAAC6J,8BAA8B,CAChD,IAAI,CAACzJ,YAAY,CAACtI,EAAE,CACrB,CAACkN,SAAS,CAAC;MACVC,IAAI,EAAG6E,kBAAuB,IAAI;QAChC5F,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE2F,kBAAkB,CAAC;QAE1D;QACA,IAAIA,kBAAkB,CAAChS,EAAE,KAAK,IAAI,CAACsI,YAAY,CAACtI,EAAE,EAAE;UAClD,IAAI,CAACsI,YAAY,GAAG;YAAE,GAAG,IAAI,CAACA,YAAY;YAAE,GAAG0J;UAAkB,CAAE;UACnE,IAAI,CAAC3J,GAAG,CAACoJ,aAAa,EAAE;;MAE5B,CAAC;MACDnE,KAAK,EAAGA,KAAU,IAAI;QACpBlB,OAAO,CAACkB,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;MAC/D;KACD,CAAC,CACH;EACH;EAEQsE,iBAAiBA,CAACK,SAAiB;IACzC,IAAI,CAAC/J,cAAc,CAAC0J,iBAAiB,CAACK,SAAS,CAAC,CAAC/E,SAAS,CAAC;MACzDC,IAAI,EAAEA,CAAA,KAAK;QACTf,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE4F,SAAS,CAAC;MACrD,CAAC;MACD3E,KAAK,EAAGA,KAAK,IAAI;QACflB,OAAO,CAACkB,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MAC1D;KACD,CAAC;EACJ;EAEA;EACA4E,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAACzG,WAAW,CAAC0G,KAAK,IAAI,CAAC,IAAI,CAAC7J,YAAY,EAAEtI,EAAE,EAAE;IAEvD,MAAMS,OAAO,GAAG,IAAI,CAACgL,WAAW,CAACO,GAAG,CAAC,SAAS,CAAC,EAAEoG,KAAK,EAAEC,IAAI,EAAE;IAC9D,IAAI,CAAC5R,OAAO,EAAE;IAEd,MAAMmQ,UAAU,GAAG,IAAI,CAACrS,gBAAgB,EAAEyB,EAAE,IAAI,IAAI,CAACzB,gBAAgB,EAAE2P,GAAG;IAE1E,IAAI,CAAC0C,UAAU,EAAE;MACf,IAAI,CAACxI,YAAY,CAACmG,SAAS,CAAC,0BAA0B,CAAC;MACvD;;IAGF;IACA,IAAI,CAACtF,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAAC6C,gBAAgB,EAAE;IAEvBM,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE;MACjC5L,OAAO;MACPmQ,UAAU;MACVxC,cAAc,EAAE,IAAI,CAAC9F,YAAY,CAACtI;KACnC,CAAC;IAEF,IAAI,CAACkI,cAAc,CAACgK,WAAW,CAC7BtB,UAAU,EACVnQ,OAAO,EACP6R,SAAS,EACT,MAAa,EACb,IAAI,CAAChK,YAAY,CAACtI,EAAE,CACrB,CAACkN,SAAS,CAAC;MACVC,IAAI,EAAGoF,OAAY,IAAI;QACrBnG,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEkG,OAAO,CAAC;QAEpD;QACA,MAAMlB,aAAa,GAAG,IAAI,CAACjM,QAAQ,CAACkM,IAAI,CACrCC,GAAG,IAAKA,GAAG,CAACvR,EAAE,KAAKuS,OAAO,CAACvS,EAAE,CAC/B;QACD,IAAI,CAACqR,aAAa,EAAE;UAClB,IAAI,CAACjM,QAAQ,CAACoM,IAAI,CAACe,OAAO,CAAC;UAC3BnG,OAAO,CAACC,GAAG,CACT,wCAAwC,EACxC,IAAI,CAACjH,QAAQ,CAACuJ,MAAM,CACrB;;QAGH;QACA,IAAI,CAAClD,WAAW,CAAC+G,KAAK,EAAE;QACxB,IAAI,CAACvJ,gBAAgB,GAAG,KAAK;QAC7B,IAAI,CAAC6C,gBAAgB,EAAE;QAEvB;QACA,IAAI,CAACzD,GAAG,CAACoJ,aAAa,EAAE;QACxBC,UAAU,CAAC,MAAK;UACd,IAAI,CAACxB,cAAc,EAAE;QACvB,CAAC,EAAE,EAAE,CAAC;MACR,CAAC;MACD5C,KAAK,EAAGA,KAAU,IAAI;QACpBlB,OAAO,CAACkB,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;QAC5D,IAAI,CAAClF,YAAY,CAACmG,SAAS,CAAC,mCAAmC,CAAC;QAChE,IAAI,CAACtF,gBAAgB,GAAG,KAAK;QAC7B,IAAI,CAAC6C,gBAAgB,EAAE;MACzB;KACD,CAAC;EACJ;EAEAoE,cAAcA,CAAA;IACZwB,UAAU,CAAC,MAAK;MACd,IAAI,IAAI,CAACe,iBAAiB,EAAE;QAC1B,MAAMC,OAAO,GAAG,IAAI,CAACD,iBAAiB,CAACE,aAAa;QACpDD,OAAO,CAACE,SAAS,GAAGF,OAAO,CAACG,YAAY;;IAE5C,CAAC,EAAE,GAAG,CAAC;EACT;EAEA;EACApU,gBAAgBA,CAACC,UAAgC;IAC/C,IAAI,CAACA,UAAU,EAAE,OAAO,YAAY;IAEpC,MAAMoU,QAAQ,GAAGC,IAAI,CAACC,KAAK,CACzB,CAACrD,IAAI,CAACsD,GAAG,EAAE,GAAG,IAAItD,IAAI,CAACjR,UAAU,CAAC,CAACmR,OAAO,EAAE,IAAI,KAAK,CACtD;IAED,IAAIiD,QAAQ,GAAG,CAAC,EAAE,OAAO,aAAa;IACtC,IAAIA,QAAQ,GAAG,EAAE,EAAE,OAAO,UAAUA,QAAQ,MAAM;IAClD,IAAIA,QAAQ,GAAG,IAAI,EAAE,OAAO,UAAUC,IAAI,CAACC,KAAK,CAACF,QAAQ,GAAG,EAAE,CAAC,GAAG;IAClE,OAAO,UAAUC,IAAI,CAACC,KAAK,CAACF,QAAQ,GAAG,IAAI,CAAC,GAAG;EACjD;EAEA;EACAI,oBAAoBA,CAACjB,SAAiB;IACpC,OACE,IAAI,CAAC5H,aAAa,CAAC4H,SAAS,CAAC,IAAI;MAC/BkB,QAAQ,EAAE,CAAC;MACXC,QAAQ,EAAE,CAAC;MACXC,WAAW,EAAE,CAAC;MACdC,KAAK,EAAE;KACR;EAEL;EAEQC,oBAAoBA,CAC1BtB,SAAiB,EACjBuB,IAAkD;IAElD,IAAI,CAACnJ,aAAa,CAAC4H,SAAS,CAAC,GAAG;MAC9B,GAAG,IAAI,CAACiB,oBAAoB,CAACjB,SAAS,CAAC;MACvC,GAAGuB;KACJ;EACH;EAEA;EAEA;EAEAC,cAAcA,CAAA;IACZ,IAAI,CAAC,IAAI,CAAClV,gBAAgB,EAAEyB,EAAE,EAAE;MAC9B,IAAI,CAACoI,YAAY,CAACmG,SAAS,CAAC,gCAAgC,CAAC;MAC7D;;IAGFnC,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE,IAAI,CAAC9N,gBAAgB,CAACc,QAAQ,CAAC;IAC3E,IAAI,CAACqU,YAAY,CAAC5V,QAAQ,CAAC6V,KAAK,CAAC;EACnC;EAEAC,cAAcA,CAAA;IACZ,IAAI,CAAC,IAAI,CAACrV,gBAAgB,EAAEyB,EAAE,EAAE;MAC9B,IAAI,CAACoI,YAAY,CAACmG,SAAS,CAAC,gCAAgC,CAAC;MAC7D;;IAGFnC,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE,IAAI,CAAC9N,gBAAgB,CAACc,QAAQ,CAAC;IAC3E,IAAI,CAACqU,YAAY,CAAC5V,QAAQ,CAAC+V,KAAK,CAAC;EACnC;EAEA;EACA;EAEA;EAEAC,cAAcA,CAACC,KAAa;IAC1B,IAAIA,KAAK,GAAG,IAAI,EAAE,OAAOA,KAAK,GAAG,IAAI;IACrC,IAAIA,KAAK,GAAG,OAAO,EAAE,OAAOhB,IAAI,CAACiB,KAAK,CAACD,KAAK,GAAG,IAAI,CAAC,GAAG,KAAK;IAC5D,OAAOhB,IAAI,CAACiB,KAAK,CAACD,KAAK,GAAG,OAAO,CAAC,GAAG,KAAK;EAC5C;EAEAE,YAAYA,CAAC1B,OAAY;IACvB,MAAM2B,cAAc,GAAG3B,OAAO,CAAC1B,WAAW,EAAE3B,IAAI,CAC7C8B,GAAQ,IAAK,CAACA,GAAG,CAACN,IAAI,EAAEyD,UAAU,CAAC,QAAQ,CAAC,CAC9C;IACD,IAAID,cAAc,EAAEhD,GAAG,EAAE;MACvB,MAAMkD,IAAI,GAAG3H,QAAQ,CAAC4H,aAAa,CAAC,GAAG,CAAC;MACxCD,IAAI,CAACE,IAAI,GAAGJ,cAAc,CAAChD,GAAG;MAC9BkD,IAAI,CAACG,QAAQ,GAAGL,cAAc,CAACtO,IAAI,IAAI,MAAM;MAC7CwO,IAAI,CAACI,MAAM,GAAG,QAAQ;MACtB/H,QAAQ,CAACgI,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC;MAC/BA,IAAI,CAACO,KAAK,EAAE;MACZlI,QAAQ,CAACgI,IAAI,CAACG,WAAW,CAACR,IAAI,CAAC;MAC/B,IAAI,CAAChM,YAAY,CAACyM,WAAW,CAAC,wBAAwB,CAAC;;EAE3D;EAEA;EAEA5V,YAAYA,CAAA;IACV,IAAI,CAAC+J,UAAU,GAAG,CAAC,IAAI,CAACA,UAAU;IAClC,IAAI,CAACH,UAAU,GAAG,IAAI,CAACG,UAAU;EACnC;EAEA8L,cAAcA,CAAA;IACZ,IAAI,CAAC3V,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;EACxC;EAEA4V,qBAAqBA,CAAA;IACnB3I,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;IAC7C;IACA,IAAI,CAACpE,MAAM,CACR+M,QAAQ,CAAC,CAAC,+BAA+B,CAAC,CAAC,CAC3CC,IAAI,CAAC,MAAK;MACT7I,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;IACzD,CAAC,CAAC,CACD6I,KAAK,CAAE5H,KAAK,IAAI;MACflB,OAAO,CAACkB,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3C;MACA,IAAI,CAACrF,MAAM,CAAC+M,QAAQ,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAACE,KAAK,CAAC,MAAK;QACnD;QACAC,MAAM,CAACC,QAAQ,CAACd,IAAI,GAAG,+BAA+B;MACxD,CAAC,CAAC;IACJ,CAAC,CAAC;EACN;EAEA;EAEAxN,aAAaA,CAAA;IACX,IAAI,CAAC6B,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACC,kBAAkB,GAAG,KAAK;IAC/B,IAAI,CAACzJ,YAAY,GAAG,KAAK;IACzB,IAAI,CAAC+J,sBAAsB,GAAG,KAAK;IACnC,IAAI,CAACK,kBAAkB,GAAG,KAAK;EACjC;EAEAzF,oBAAoBA,CAACyO,OAAY,EAAE8C,KAAiB;IAClDA,KAAK,CAACC,cAAc,EAAE;IACtB,IAAI,CAACnM,eAAe,GAAGoJ,OAAO;IAC9B,IAAI,CAACnJ,mBAAmB,GAAG;MAAEC,CAAC,EAAEgM,KAAK,CAACE,OAAO;MAAEjM,CAAC,EAAE+L,KAAK,CAACG;IAAO,CAAE;IACjE,IAAI,CAACtM,sBAAsB,GAAG,IAAI;EACpC;EAEAuM,kBAAkBA,CAAClD,OAAY,EAAE8C,KAAiB;IAChDA,KAAK,CAACK,eAAe,EAAE;IACvB,IAAI,CAAClM,qBAAqB,GAAG+I,OAAO;IACpC,IAAI,CAACnJ,mBAAmB,GAAG;MAAEC,CAAC,EAAEgM,KAAK,CAACE,OAAO;MAAEjM,CAAC,EAAE+L,KAAK,CAACG;IAAO,CAAE;IACjE,IAAI,CAACjM,kBAAkB,GAAG,IAAI;EAChC;EAEAoM,UAAUA,CAAC9P,KAAa;IACtB,IAAI,IAAI,CAAC2D,qBAAqB,EAAE;MAC9B,IAAI,CAACoM,cAAc,CAAC,IAAI,CAACpM,qBAAqB,CAACxJ,EAAE,EAAE6F,KAAK,CAAC;;IAE3D,IAAI,CAAC0D,kBAAkB,GAAG,KAAK;EACjC;EAEAqM,cAAcA,CAAC3D,SAAiB,EAAEpM,KAAa;IAC7CuG,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAExG,KAAK,EAAE,cAAc,EAAEoM,SAAS,CAAC;IAEtE,IAAI,CAACA,SAAS,IAAI,CAACpM,KAAK,EAAE;MACxBuG,OAAO,CAACkB,KAAK,CAAC,2CAA2C,CAAC;MAC1D;;IAGF;IACA,IAAI,CAACpF,cAAc,CAAC2N,cAAc,CAAC5D,SAAS,EAAEpM,KAAK,CAAC,CAACqH,SAAS,CAAC;MAC7DC,IAAI,EAAG2I,MAAM,IAAI;QACf1J,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEyJ,MAAM,CAAC;QAEvD;QACA,MAAMC,YAAY,GAAG,IAAI,CAAC3Q,QAAQ,CAAC4Q,SAAS,CACzCzE,GAAG,IAAKA,GAAG,CAACvR,EAAE,KAAKiS,SAAS,CAC9B;QACD,IAAI8D,YAAY,KAAK,CAAC,CAAC,EAAE;UACvB,IAAI,CAAC3Q,QAAQ,CAAC2Q,YAAY,CAAC,GAAGD,MAAM;UACpC,IAAI,CAACzN,GAAG,CAACoJ,aAAa,EAAE;;QAG1B,IAAI,CAACrJ,YAAY,CAACyM,WAAW,CAAC,YAAYhP,KAAK,UAAU,CAAC;MAC5D,CAAC;MACDyH,KAAK,EAAGA,KAAK,IAAI;QACflB,OAAO,CAACkB,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QAClD,IAAI,CAAClF,YAAY,CAACmG,SAAS,CAAC,uCAAuC,CAAC;MACtE;KACD,CAAC;EACJ;EAEA0H,cAAcA,CAACC,QAAa,EAAEjI,MAAc;IAC1C,OAAOiI,QAAQ,CAACjI,MAAM,KAAKA,MAAM;EACnC;EAEAkI,cAAcA,CAAC5D,OAAY;IACzBnG,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEkG,OAAO,CAACvS,EAAE,CAAC;IAClD,IAAI,CAAC8G,aAAa,EAAE;EACtB;EAEAsP,cAAcA,CAAC7D,OAAY;IACzBnG,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEkG,OAAO,CAACvS,EAAE,CAAC;IACjD,IAAI,CAAC8G,aAAa,EAAE;EACtB;EAEAuP,aAAaA,CAAC9D,OAAY;IACxBnG,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEkG,OAAO,CAACvS,EAAE,CAAC;IAEhD,IAAI,CAACuS,OAAO,CAACvS,EAAE,EAAE;MACfoM,OAAO,CAACkB,KAAK,CAAC,uCAAuC,CAAC;MACtD,IAAI,CAAClF,YAAY,CAACmG,SAAS,CAAC,gCAAgC,CAAC;MAC7D;;IAGF;IACA,MAAM+H,SAAS,GACb/D,OAAO,CAACxS,MAAM,EAAEC,EAAE,KAAK,IAAI,CAACY,aAAa,IACzC2R,OAAO,CAAC5B,QAAQ,KAAK,IAAI,CAAC/P,aAAa;IAEzC,IAAI,CAAC0V,SAAS,EAAE;MACd,IAAI,CAAClO,YAAY,CAACmG,SAAS,CACzB,mDAAmD,CACpD;MACD,IAAI,CAACzH,aAAa,EAAE;MACpB;;IAGF;IACA,IAAI,CAACyP,OAAO,CAAC,iDAAiD,CAAC,EAAE;MAC/D,IAAI,CAACzP,aAAa,EAAE;MACpB;;IAGF;IACA,IAAI,CAACoB,cAAc,CAACmO,aAAa,CAAC9D,OAAO,CAACvS,EAAE,CAAC,CAACkN,SAAS,CAAC;MACtDC,IAAI,EAAG2I,MAAM,IAAI;QACf1J,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEyJ,MAAM,CAAC;QAEtD;QACA,IAAI,CAAC1Q,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACoR,MAAM,CAAEjF,GAAG,IAAKA,GAAG,CAACvR,EAAE,KAAKuS,OAAO,CAACvS,EAAE,CAAC;QAEpE,IAAI,CAACoI,YAAY,CAACyM,WAAW,CAAC,kBAAkB,CAAC;QACjD,IAAI,CAACxM,GAAG,CAACoJ,aAAa,EAAE;MAC1B,CAAC;MACDnE,KAAK,EAAGA,KAAK,IAAI;QACflB,OAAO,CAACkB,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjD,IAAI,CAAClF,YAAY,CAACmG,SAAS,CAAC,0CAA0C,CAAC;MACzE;KACD,CAAC;IAEF,IAAI,CAACzH,aAAa,EAAE;EACtB;EAEA;EAEA2P,iBAAiBA,CAAA;IACf,IAAI,CAAC9N,eAAe,GAAG,CAAC,IAAI,CAACA,eAAe;EAC9C;EAEA+N,mBAAmBA,CAACC,QAAa;IAC/B,IAAI,CAAC1Q,qBAAqB,GAAG0Q,QAAQ;EACvC;EAEA3Q,oBAAoBA,CAAC2Q,QAAa;IAChC,OAAOA,QAAQ,EAAEzL,MAAM,IAAI,EAAE;EAC/B;EAEAvF,WAAWA,CAACE,KAAU;IACpB,MAAM+Q,cAAc,GAAG,IAAI,CAACnL,WAAW,CAACO,GAAG,CAAC,SAAS,CAAC,EAAEoG,KAAK,IAAI,EAAE;IACnE,MAAMyE,UAAU,GAAGD,cAAc,GAAG/Q,KAAK,CAACA,KAAK;IAC/C,IAAI,CAAC4F,WAAW,CAACqL,UAAU,CAAC;MAAErW,OAAO,EAAEoW;IAAU,CAAE,CAAC;IACpD,IAAI,CAAClO,eAAe,GAAG,KAAK;EAC9B;EAEAoO,oBAAoBA,CAAA;IAClB,IAAI,CAACnO,kBAAkB,GAAG,CAAC,IAAI,CAACA,kBAAkB;EACpD;EAEA;EACA;EAEA;EACA;EAEA;EACA;EAEA;EAEAvD,gBAAgBA,CAAC4L,KAAa,EAAEsB,OAAY;IAC1C,OAAOA,OAAO,CAACvS,EAAE,IAAIuS,OAAO,CAACrE,GAAG,IAAI+C,KAAK,CAAC+F,QAAQ,EAAE;EACtD;EAEA;EAEAC,cAAcA,CAAA;IACZ7K,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;IACtC,MAAM6K,WAAW,GAAG;MAClBlX,EAAE,EAAE,QAAQ2P,IAAI,CAACsD,GAAG,EAAE,EAAE;MACxBxS,OAAO,EAAE,mBAAmB,IAAIkP,IAAI,EAAE,CAACwH,kBAAkB,EAAE,EAAE;MAC7D1X,SAAS,EAAE,IAAIkQ,IAAI,EAAE,CAACyH,WAAW,EAAE;MACnCrX,MAAM,EAAE;QACNC,EAAE,EAAE,IAAI,CAACzB,gBAAgB,EAAEyB,EAAE,IAAI,WAAW;QAC5CX,QAAQ,EAAE,IAAI,CAACd,gBAAgB,EAAEc,QAAQ,IAAI,WAAW;QACxDa,KAAK,EACH,IAAI,CAAC3B,gBAAgB,EAAE2B,KAAK,IAAI;OACnC;MACDwQ,IAAI,EAAE,MAAM;MACZ2G,MAAM,EAAE;KACT;IACD,IAAI,CAACjS,QAAQ,CAACoM,IAAI,CAAC0F,WAAW,CAAC;IAC/B,IAAI,CAAC7O,GAAG,CAACoJ,aAAa,EAAE;IACxBC,UAAU,CAAC,MAAM,IAAI,CAACxB,cAAc,EAAE,EAAE,EAAE,CAAC;EAC7C;EAEAxL,mBAAmBA,CAAA;IACjB,OACE,IAAI,CAAC4D,YAAY,EAAEsG,OAAO,IAC1B,IAAI,CAACtG,YAAY,EAAEmG,YAAY,EAAEE,MAAM,GAAG,CAAC,IAC3C,KAAK;EAET;EAEAjI,UAAUA,CAAA;IACR0F,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;IAChC,IAAI,CAACzD,kBAAkB,GAAG,KAAK;IAC/B;EACF;;EAEA0O,SAASA,CAACC,MAAc;IACtB,MAAMC,YAAY,GAAG/K,QAAQ,CAACgL,aAAa,CACzC,oBAAoB,CACN;IAChB,IAAID,YAAY,EAAE;MAChB,MAAME,gBAAgB,GAAGF,YAAY,CAACG,KAAK,CAACC,SAAS,IAAI,UAAU;MACnE,MAAMC,YAAY,GAAGC,UAAU,CAC7BJ,gBAAgB,CAACK,KAAK,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,IAAI,GAAG,CACvD;MACD,MAAMC,QAAQ,GAAGjF,IAAI,CAACkF,GAAG,CAAC,GAAG,EAAElF,IAAI,CAACmF,GAAG,CAAC,CAAC,EAAEL,YAAY,GAAGN,MAAM,CAAC,CAAC;MAClEC,YAAY,CAACG,KAAK,CAACC,SAAS,GAAG,SAASI,QAAQ,GAAG;MACnD,IAAIA,QAAQ,GAAG,CAAC,EAAE;QAChBR,YAAY,CAACW,SAAS,CAACnL,GAAG,CAAC,QAAQ,CAAC;OACrC,MAAM;QACLwK,YAAY,CAACW,SAAS,CAACC,MAAM,CAAC,QAAQ,CAAC;;;EAG7C;EAEAC,SAASA,CAAA;IACP,MAAMb,YAAY,GAAG/K,QAAQ,CAACgL,aAAa,CACzC,oBAAoB,CACN;IAChB,IAAID,YAAY,EAAE;MAChBA,YAAY,CAACG,KAAK,CAACC,SAAS,GAAG,UAAU;MACzCJ,YAAY,CAACW,SAAS,CAACC,MAAM,CAAC,QAAQ,CAAC;;EAE3C;EAEA;EACA;EACA;EACA;EAEA/R,gBAAgBA,CAACqK,IAAa;IAC5B,MAAM4H,KAAK,GAAG,IAAI,CAACC,SAAS,EAAE5F,aAAa;IAC3C,IAAI,CAAC2F,KAAK,EAAE;MACVlM,OAAO,CAACkB,KAAK,CAAC,8BAA8B,CAAC;MAC7C;;IAGF;IACA,IAAIoD,IAAI,KAAK,OAAO,EAAE;MACpB4H,KAAK,CAACE,MAAM,GAAG,SAAS;KACzB,MAAM,IAAI9H,IAAI,KAAK,OAAO,EAAE;MAC3B4H,KAAK,CAACE,MAAM,GAAG,SAAS;KACzB,MAAM,IAAI9H,IAAI,KAAK,UAAU,EAAE;MAC9B4H,KAAK,CAACE,MAAM,GAAG,iCAAiC;KACjD,MAAM;MACLF,KAAK,CAACE,MAAM,GAAG,KAAK;;IAGtB;IACAF,KAAK,CAAClG,KAAK,GAAG,EAAE;IAEhB;IACAkG,KAAK,CAAC3D,KAAK,EAAE;IACb,IAAI,CAAC/L,kBAAkB,GAAG,KAAK;EACjC;EAEA7D,iBAAiBA,CAACtF,SAAwB;IACxC,IAAI,CAACA,SAAS,EAAE,OAAO,EAAE;IAEzB,MAAMgZ,IAAI,GAAG,IAAI9I,IAAI,CAAClQ,SAAS,CAAC;IAChC,OAAOgZ,IAAI,CAACtB,kBAAkB,CAAC,OAAO,EAAE;MACtCuB,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;KACT,CAAC;EACJ;EAEApZ,mBAAmBA,CAACE,SAAwB;IAC1C,IAAI,CAACA,SAAS,EAAE,OAAO,EAAE;IAEzB,MAAMgZ,IAAI,GAAG,IAAI9I,IAAI,CAAClQ,SAAS,CAAC;IAChC,MAAMmZ,KAAK,GAAG,IAAIjJ,IAAI,EAAE;IACxB,MAAMkJ,SAAS,GAAG,IAAIlJ,IAAI,CAACiJ,KAAK,CAAC;IACjCC,SAAS,CAACC,OAAO,CAACD,SAAS,CAACE,OAAO,EAAE,GAAG,CAAC,CAAC;IAE1C,IAAIN,IAAI,CAACO,YAAY,EAAE,KAAKJ,KAAK,CAACI,YAAY,EAAE,EAAE;MAChD,OAAO,aAAa;KACrB,MAAM,IAAIP,IAAI,CAACO,YAAY,EAAE,KAAKH,SAAS,CAACG,YAAY,EAAE,EAAE;MAC3D,OAAO,MAAM;KACd,MAAM;MACL,OAAOP,IAAI,CAACQ,kBAAkB,CAAC,OAAO,CAAC;;EAE3C;EAEAzY,oBAAoBA,CAACC,OAAe;IAClC,IAAI,CAACA,OAAO,EAAE,OAAO,EAAE;IAEvB;IACA,MAAMyY,QAAQ,GAAG,sBAAsB;IACvC,OAAOzY,OAAO,CAAC0Y,OAAO,CACpBD,QAAQ,EACR,qEAAqE,CACtE;EACH;EAEA3U,uBAAuBA,CAAC0M,KAAa;IACnC,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO,IAAI;IAE5B,MAAMmI,cAAc,GAAG,IAAI,CAAChU,QAAQ,CAAC6L,KAAK,CAAC;IAC3C,MAAMoI,eAAe,GAAG,IAAI,CAACjU,QAAQ,CAAC6L,KAAK,GAAG,CAAC,CAAC;IAEhD,IAAI,CAACmI,cAAc,EAAE3Z,SAAS,IAAI,CAAC4Z,eAAe,EAAE5Z,SAAS,EAAE,OAAO,KAAK;IAE3E,MAAM6Z,WAAW,GAAG,IAAI3J,IAAI,CAACyJ,cAAc,CAAC3Z,SAAS,CAAC,CAACuZ,YAAY,EAAE;IACrE,MAAMO,YAAY,GAAG,IAAI5J,IAAI,CAAC0J,eAAe,CAAC5Z,SAAS,CAAC,CAACuZ,YAAY,EAAE;IAEvE,OAAOM,WAAW,KAAKC,YAAY;EACrC;EAEA9U,gBAAgBA,CAACwM,KAAa;IAC5B,MAAMmI,cAAc,GAAG,IAAI,CAAChU,QAAQ,CAAC6L,KAAK,CAAC;IAC3C,MAAMuI,WAAW,GAAG,IAAI,CAACpU,QAAQ,CAAC6L,KAAK,GAAG,CAAC,CAAC;IAE5C,IAAI,CAACuI,WAAW,EAAE,OAAO,IAAI;IAE7B,OAAOJ,cAAc,CAACrZ,MAAM,EAAEC,EAAE,KAAKwZ,WAAW,CAACzZ,MAAM,EAAEC,EAAE;EAC7D;EAEA2E,oBAAoBA,CAACsM,KAAa;IAChC,MAAMmI,cAAc,GAAG,IAAI,CAAChU,QAAQ,CAAC6L,KAAK,CAAC;IAC3C,MAAMoI,eAAe,GAAG,IAAI,CAACjU,QAAQ,CAAC6L,KAAK,GAAG,CAAC,CAAC;IAEhD,IAAI,CAACoI,eAAe,EAAE,OAAO,IAAI;IAEjC,OAAOD,cAAc,CAACrZ,MAAM,EAAEC,EAAE,KAAKqZ,eAAe,CAACtZ,MAAM,EAAEC,EAAE;EACjE;EAEA4E,cAAcA,CAAC2N,OAAY;IACzB;IACA,IAAIA,OAAO,CAAC7B,IAAI,EAAE;MAChB,IAAI6B,OAAO,CAAC7B,IAAI,KAAK,OAAO,IAAI6B,OAAO,CAAC7B,IAAI,KAAK,OAAO,EAAE,OAAO,OAAO;MACxE,IAAI6B,OAAO,CAAC7B,IAAI,KAAK,OAAO,IAAI6B,OAAO,CAAC7B,IAAI,KAAK,OAAO,EAAE,OAAO,OAAO;MACxE,IAAI6B,OAAO,CAAC7B,IAAI,KAAK,OAAO,IAAI6B,OAAO,CAAC7B,IAAI,KAAK,OAAO,EAAE,OAAO,OAAO;MACxE,IAAI6B,OAAO,CAAC7B,IAAI,KAAK,eAAe,EAAE,OAAO,OAAO;MACpD,IAAI6B,OAAO,CAAC7B,IAAI,KAAK,MAAM,IAAI6B,OAAO,CAAC7B,IAAI,KAAK,MAAM,EAAE,OAAO,MAAM;;IAGvE;IACA,IAAI6B,OAAO,CAAC1B,WAAW,IAAI0B,OAAO,CAAC1B,WAAW,CAAClC,MAAM,GAAG,CAAC,EAAE;MACzD,MAAM8K,UAAU,GAAGlH,OAAO,CAAC1B,WAAW,CAAC,CAAC,CAAC;MACzC,IAAI4I,UAAU,CAAC/I,IAAI,EAAEyD,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,OAAO;MACzD,IAAIsF,UAAU,CAAC/I,IAAI,EAAEyD,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,OAAO;MACzD,IAAIsF,UAAU,CAAC/I,IAAI,EAAEyD,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,OAAO;MACzD,OAAO,MAAM;;IAGf;IACA,IAAI5B,OAAO,CAACmH,QAAQ,IAAInH,OAAO,CAACoH,QAAQ,IAAIpH,OAAO,CAACqH,KAAK,EAAE,OAAO,OAAO;IAEzE,OAAO,MAAM;EACf;EAEA/U,QAAQA,CAAC0N,OAAY;IACnB;IACA,IAAIA,OAAO,CAAC7B,IAAI,KAAK,OAAO,IAAI6B,OAAO,CAAC7B,IAAI,KAAK,OAAO,EAAE;MACxD,OAAO,IAAI;;IAGb;IACA,MAAMmJ,kBAAkB,GACtBtH,OAAO,CAAC1B,WAAW,EAAES,IAAI,CAAEN,GAAQ,IAAI;MACrC,OAAOA,GAAG,CAACN,IAAI,EAAEyD,UAAU,CAAC,QAAQ,CAAC,IAAInD,GAAG,CAACN,IAAI,KAAK,OAAO;IAC/D,CAAC,CAAC,IAAI,KAAK;IAEb;IACA,MAAMoJ,WAAW,GAAG,CAAC,EAAEvH,OAAO,CAACwH,QAAQ,IAAIxH,OAAO,CAACrS,KAAK,CAAC;IAEzD,OAAO2Z,kBAAkB,IAAIC,WAAW;EAC1C;EAEAhJ,OAAOA,CAACyB,OAAY;IAClB;IACA,IAAIA,OAAO,CAAC7B,IAAI,KAAK,MAAM,IAAI6B,OAAO,CAAC7B,IAAI,KAAK,MAAM,EAAE;MACtD,OAAO,IAAI;;IAGb;IACA,MAAMsJ,iBAAiB,GACrBzH,OAAO,CAAC1B,WAAW,EAAES,IAAI,CAAEN,GAAQ,IAAI;MACrC,OAAO,CAACA,GAAG,CAACN,IAAI,EAAEyD,UAAU,CAAC,QAAQ,CAAC,IAAInD,GAAG,CAACN,IAAI,KAAK,OAAO;IAChE,CAAC,CAAC,IAAI,KAAK;IAEb,OAAOsJ,iBAAiB;EAC1B;EAEArY,WAAWA,CAAC4Q,OAAY;IACtB;IACA,IAAIA,OAAO,CAACwH,QAAQ,EAAE;MACpB,OAAOxH,OAAO,CAACwH,QAAQ;;IAEzB,IAAIxH,OAAO,CAACrS,KAAK,EAAE;MACjB,OAAOqS,OAAO,CAACrS,KAAK;;IAGtB;IACA,MAAM+Z,eAAe,GAAG1H,OAAO,CAAC1B,WAAW,EAAE3B,IAAI,CAC9C8B,GAAQ,IAAKA,GAAG,CAACN,IAAI,EAAEyD,UAAU,CAAC,QAAQ,CAAC,IAAInD,GAAG,CAACN,IAAI,KAAK,OAAO,CACrE;IAED,IAAIuJ,eAAe,EAAE;MACnB,OAAOA,eAAe,CAAC/I,GAAG,IAAI+I,eAAe,CAAC9I,IAAI,IAAI,EAAE;;IAG1D,OAAO,EAAE;EACX;EAEA+I,WAAWA,CAAC3H,OAAY;IACtB,MAAM2B,cAAc,GAAG3B,OAAO,CAAC1B,WAAW,EAAE3B,IAAI,CAC7C8B,GAAQ,IAAK,CAACA,GAAG,CAACN,IAAI,EAAEyD,UAAU,CAAC,QAAQ,CAAC,CAC9C;IACD,OAAOD,cAAc,EAAEtO,IAAI,IAAI,SAAS;EAC1C;EAEAuU,WAAWA,CAAC5H,OAAY;IACtB,MAAM2B,cAAc,GAAG3B,OAAO,CAAC1B,WAAW,EAAE3B,IAAI,CAC7C8B,GAAQ,IAAK,CAACA,GAAG,CAACN,IAAI,EAAEyD,UAAU,CAAC,QAAQ,CAAC,CAC9C;IACD,IAAI,CAACD,cAAc,EAAE9C,IAAI,EAAE,OAAO,EAAE;IAEpC,MAAM2C,KAAK,GAAGG,cAAc,CAAC9C,IAAI;IACjC,IAAI2C,KAAK,GAAG,IAAI,EAAE,OAAOA,KAAK,GAAG,IAAI;IACrC,IAAIA,KAAK,GAAG,OAAO,EAAE,OAAOhB,IAAI,CAACiB,KAAK,CAACD,KAAK,GAAG,IAAI,CAAC,GAAG,KAAK;IAC5D,OAAOhB,IAAI,CAACiB,KAAK,CAACD,KAAK,GAAG,OAAO,CAAC,GAAG,KAAK;EAC5C;EAEAqG,WAAWA,CAAC7H,OAAY;IACtB,MAAM2B,cAAc,GAAG3B,OAAO,CAAC1B,WAAW,EAAE3B,IAAI,CAC7C8B,GAAQ,IAAK,CAACA,GAAG,CAACN,IAAI,EAAEyD,UAAU,CAAC,QAAQ,CAAC,CAC9C;IACD,IAAI,CAACD,cAAc,EAAExD,IAAI,EAAE,OAAO,aAAa;IAE/C,IAAIwD,cAAc,CAACxD,IAAI,CAACyD,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,mBAAmB;IACxE,IAAID,cAAc,CAACxD,IAAI,CAACyD,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,mBAAmB;IACxE,IAAID,cAAc,CAACxD,IAAI,CAAC2J,QAAQ,CAAC,KAAK,CAAC,EAAE,OAAO,iBAAiB;IACjE,IAAInG,cAAc,CAACxD,IAAI,CAAC2J,QAAQ,CAAC,MAAM,CAAC,EAAE,OAAO,kBAAkB;IACnE,IAAInG,cAAc,CAACxD,IAAI,CAAC2J,QAAQ,CAAC,OAAO,CAAC,EAAE,OAAO,mBAAmB;IACrE,OAAO,aAAa;EACtB;EAEA/Z,YAAYA,CAAC2N,MAAc;IACzB;IACA,MAAMqM,MAAM,GAAG,CACb,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,CACV;IACD,MAAMrJ,KAAK,GAAGhD,MAAM,CAACsM,UAAU,CAAC,CAAC,CAAC,GAAGD,MAAM,CAAC3L,MAAM;IAClD,OAAO2L,MAAM,CAACrJ,KAAK,CAAC;EACtB;EAEA;EACAtN,cAAcA,CAAC4O,OAAY,EAAE8C,KAAU;IACrCjJ,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEkG,OAAO,CAAC;EAC1C;EAEAiI,aAAaA,CAACnF,KAAU;IACtB;IACA,IAAI,CAACoF,qBAAqB,EAAE;EAC9B;EAEAC,cAAcA,CAACrF,KAAoB;IACjC,IAAIA,KAAK,CAACsF,GAAG,KAAK,OAAO,IAAI,CAACtF,KAAK,CAACuF,QAAQ,EAAE;MAC5CvF,KAAK,CAACC,cAAc,EAAE;MACtB,IAAI,CAACpD,WAAW,EAAE;;EAEtB;EAEA2I,YAAYA,CAAA;IACV;EAAA;EAGFC,WAAWA,CAAA;IACT;EAAA;EAGFC,QAAQA,CAAC1F,KAAU;IACjB;IACA,MAAM3C,OAAO,GAAG2C,KAAK,CAACb,MAAM;IAC5B,IACE9B,OAAO,CAACE,SAAS,KAAK,CAAC,IACvB,IAAI,CAAClK,eAAe,IACpB,CAAC,IAAI,CAACD,aAAa,EACnB;MACA,IAAI,CAAC0H,gBAAgB,EAAE;;EAE3B;EAEArQ,eAAeA,CAACmO,MAAc;IAC5B7B,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE4B,MAAM,CAAC;EAClD;EAEA7M,WAAWA,CAACiU,KAAU,EAAE9C,OAAY;IAClCnG,OAAO,CAACC,GAAG,CACT,oDAAoD,EACpDkG,OAAO,CAACvS,EAAE,EACVqV,KAAK,CAACb,MAAM,CAACwG,GAAG,CACjB;EACH;EAEAzZ,YAAYA,CAAC8T,KAAU,EAAE9C,OAAY;IACnCnG,OAAO,CAACkB,KAAK,CAAC,+CAA+C,EAAEiF,OAAO,CAACvS,EAAE,EAAE;MACzEgb,GAAG,EAAE3F,KAAK,CAACb,MAAM,CAACwG,GAAG;MACrB1N,KAAK,EAAE+H;KACR,CAAC;IACF;IACAA,KAAK,CAACb,MAAM,CAACwG,GAAG,GACd,4WAA4W;EAChX;EAEAha,eAAeA,CAACuR,OAAY;IAC1B,MAAM0H,eAAe,GAAG1H,OAAO,CAAC1B,WAAW,EAAE3B,IAAI,CAAE8B,GAAQ,IACzDA,GAAG,CAACN,IAAI,EAAEyD,UAAU,CAAC,QAAQ,CAAC,CAC/B;IACD,IAAI8F,eAAe,EAAE/I,GAAG,EAAE;MACxB,IAAI,CAACxH,aAAa,GAAG;QACnBwH,GAAG,EAAE+I,eAAe,CAAC/I,GAAG;QACxBtL,IAAI,EAAEqU,eAAe,CAACrU,IAAI,IAAI,OAAO;QACrCwL,IAAI,EAAE,IAAI,CAAC0C,cAAc,CAACmG,eAAe,CAAC7I,IAAI,IAAI,CAAC,CAAC;QACpDmB,OAAO,EAAEA;OACV;MACD,IAAI,CAAC9I,eAAe,GAAG,IAAI;MAC3B2C,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE,IAAI,CAAC3C,aAAa,CAAC;;EAEvE;EAEAuR,gBAAgBA,CAAA;IACd,IAAI,CAACxR,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACC,aAAa,GAAG,IAAI;IACzB0C,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;EACzC;EAEA6O,aAAaA,CAAA;IACX,IAAI,IAAI,CAACxR,aAAa,EAAEwH,GAAG,EAAE;MAC3B,MAAMkD,IAAI,GAAG3H,QAAQ,CAAC4H,aAAa,CAAC,GAAG,CAAC;MACxCD,IAAI,CAACE,IAAI,GAAG,IAAI,CAAC5K,aAAa,CAACwH,GAAG;MAClCkD,IAAI,CAACG,QAAQ,GAAG,IAAI,CAAC7K,aAAa,CAAC9D,IAAI,IAAI,OAAO;MAClDwO,IAAI,CAACI,MAAM,GAAG,QAAQ;MACtB/H,QAAQ,CAACgI,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC;MAC/BA,IAAI,CAACO,KAAK,EAAE;MACZlI,QAAQ,CAACgI,IAAI,CAACG,WAAW,CAACR,IAAI,CAAC;MAC/B,IAAI,CAAChM,YAAY,CAACyM,WAAW,CAAC,wBAAwB,CAAC;MACvDzI,OAAO,CAACC,GAAG,CACT,qCAAqC,EACrC,IAAI,CAAC3C,aAAa,CAAC9D,IAAI,CACxB;;EAEL;EAEA;EACA;EACA;EAEAuV,cAAcA,CAAA;IACZ,IAAI,CAAC,IAAI,CAACrS,WAAW,CAACuJ,IAAI,EAAE,EAAE;MAC5B,IAAI,CAACtJ,aAAa,GAAG,EAAE;MACvB;;IAGF,IAAI,CAACA,aAAa,GAAG,IAAI,CAAC3D,QAAQ,CAACoR,MAAM,CACtCjE,OAAO,IACNA,OAAO,CAAC9R,OAAO,EACX2a,WAAW,EAAE,CACdf,QAAQ,CAAC,IAAI,CAACvR,WAAW,CAACsS,WAAW,EAAE,CAAC,IAC3C7I,OAAO,CAACxS,MAAM,EAAEV,QAAQ,EACpB+b,WAAW,EAAE,CACdf,QAAQ,CAAC,IAAI,CAACvR,WAAW,CAACsS,WAAW,EAAE,CAAC,CAC9C;EACH;EAEAC,mBAAmBA,CAAA;IACjB,IAAI,CAACF,cAAc,EAAE;EACvB;EAEAG,WAAWA,CAAA;IACT,IAAI,CAACxS,WAAW,GAAG,EAAE;IACrB,IAAI,CAACC,aAAa,GAAG,EAAE;EACzB;EAEAwS,aAAaA,CAACtJ,SAAiB;IAC7B,MAAMuJ,cAAc,GAAG/O,QAAQ,CAACgP,cAAc,CAAC,WAAWxJ,SAAS,EAAE,CAAC;IACtE,IAAIuJ,cAAc,EAAE;MAClBA,cAAc,CAACE,cAAc,CAAC;QAAEC,QAAQ,EAAE,QAAQ;QAAEC,KAAK,EAAE;MAAQ,CAAE,CAAC;MACtE;MACAJ,cAAc,CAACrD,SAAS,CAACnL,GAAG,CAAC,WAAW,CAAC;MACzC0E,UAAU,CAAC,MAAK;QACd8J,cAAc,CAACrD,SAAS,CAACC,MAAM,CAAC,WAAW,CAAC;MAC9C,CAAC,EAAE,IAAI,CAAC;;EAEZ;EAEA;EACA;EACA;EAEAyD,gBAAgBA,CAAA;IACd,IAAI,CAAC3S,sBAAsB,GAAG,KAAK;IACnC,IAAI,CAACC,eAAe,GAAG,IAAI;EAC7B;EAEA;EACA;EACA;EACA;EAEA;EACA;EAEA;EACA;EACA;EACA;EAEQuK,YAAYA,CAACnJ,QAAkB;IACrC6B,OAAO,CAACC,GAAG,CAAC,sDAAsD,CAAC;IACnED,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE;MAC5C9B,QAAQ;MACRhM,gBAAgB,EAAE,IAAI,CAACA,gBAAgB;MACvC+J,YAAY,EAAE,IAAI,CAACA,YAAY,EAAEtI,EAAE;MACnCY,aAAa,EAAE,IAAI,CAACA;KACrB,CAAC;IAEF,IAAI,CAAC,IAAI,CAACrC,gBAAgB,EAAE;MAC1B6N,OAAO,CAACkB,KAAK,CAAC,uCAAuC,CAAC;MACtD,IAAI,CAAClF,YAAY,CAACmG,SAAS,CAAC,gCAAgC,CAAC;MAC7D;;IAGF,MAAMuN,WAAW,GAAG,IAAI,CAACvd,gBAAgB,CAACyB,EAAE,IAAI,IAAI,CAACzB,gBAAgB,CAAC2P,GAAG;IACzE,IAAI,CAAC4N,WAAW,EAAE;MAChB1P,OAAO,CAACkB,KAAK,CAAC,wCAAwC,CAAC;MACvD,IAAI,CAAClF,YAAY,CAACmG,SAAS,CAAC,gCAAgC,CAAC;MAC7D;;IAGFnC,OAAO,CAACC,GAAG,CAAC,+BAA+B9B,QAAQ,gBAAgB,EAAE;MACnEuR,WAAW;MACXC,aAAa,EACX,IAAI,CAACxd,gBAAgB,CAACc,QAAQ,IAAI,IAAI,CAACd,gBAAgB,CAACqH,IAAI;MAC9DwI,cAAc,EAAE,IAAI,CAAC9F,YAAY,EAAEtI;KACpC,CAAC;IAEF,IAAI,CAACsK,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,QAAQ,GAAGA,QAAQ,KAAKzM,QAAQ,CAAC6V,KAAK,GAAG,OAAO,GAAG,OAAO;IAC/D,IAAI,CAACnJ,YAAY,GAAG,CAAC;IAErB;IACA,IAAI,CAACwR,cAAc,EAAE;IAErB5P,OAAO,CAACC,GAAG,CAAC,sDAAsD,CAAC;IAEnE;IACA,IAAI,CAAClE,WAAW,CACbuL,YAAY,CAACoI,WAAW,EAAEvR,QAAQ,EAAE,IAAI,CAACjC,YAAY,EAAEtI,EAAE,CAAC,CAC1DkN,SAAS,CAAC;MACTC,IAAI,EAAGK,IAAU,IAAI;QACnBpB,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAE;UAC1D4P,MAAM,EAAEzO,IAAI,CAACxN,EAAE;UACfuK,QAAQ,EAAEiD,IAAI,CAACkD,IAAI;UACnBwL,UAAU,EAAE1O,IAAI,CAACpK,MAAM;UACvBqK,MAAM,EAAED,IAAI,CAACC,MAAM,EAAEpO,QAAQ;UAC7B8c,SAAS,EAAE3O,IAAI,CAAC2O,SAAS,EAAE9c,QAAQ;UACnC+O,cAAc,EAAEZ,IAAI,CAACY;SACtB,CAAC;QAEF,IAAI,CAAC1D,UAAU,GAAG8C,IAAI;QACtB,IAAI,CAAC7C,eAAe,GAAG,KAAK;QAC5B,IAAI,CAACvC,YAAY,CAACyM,WAAW,CAC3B,SAAStK,QAAQ,KAAKzM,QAAQ,CAAC6V,KAAK,GAAG,OAAO,GAAG,OAAO,SAAS,CAClE;QAEDvH,OAAO,CAACC,GAAG,CACT,qEAAqE,CACtE;MACH,CAAC;MACDiB,KAAK,EAAGA,KAAK,IAAI;QACflB,OAAO,CAACkB,KAAK,CAAC,wCAAwC,EAAE;UACtDA,KAAK,EAAEA,KAAK,CAACiF,OAAO,IAAIjF,KAAK;UAC7BwO,WAAW;UACXvR,QAAQ;UACR6D,cAAc,EAAE,IAAI,CAAC9F,YAAY,EAAEtI;SACpC,CAAC;QAEF,IAAI,CAACoc,OAAO,EAAE;QACd,IAAI,CAAChU,YAAY,CAACmG,SAAS,CAAC,wCAAwC,CAAC;MACvE;KACD,CAAC;EACN;EAEA8N,UAAUA,CAACjP,YAA0B;IACnChB,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEe,YAAY,CAAC;IAExD,IAAI,CAACjF,WAAW,CAACkU,UAAU,CAACjP,YAAY,CAAC,CAACF,SAAS,CAAC;MAClDC,IAAI,EAAGK,IAAU,IAAI;QACnBpB,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEmB,IAAI,CAAC;QAClD,IAAI,CAAC9C,UAAU,GAAG8C,IAAI;QACtB,IAAI,CAAClD,QAAQ,GAAG,IAAI;QACpB,IAAI,CAACK,eAAe,GAAG,IAAI;QAC3B,IAAI,CAACJ,QAAQ,GAAGiD,IAAI,CAACkD,IAAI,KAAK5S,QAAQ,CAAC6V,KAAK,GAAG,OAAO,GAAG,OAAO;QAChE,IAAI,CAACqI,cAAc,EAAE;QACrB,IAAI,CAAC5T,YAAY,CAACyM,WAAW,CAAC,eAAe,CAAC;MAChD,CAAC;MACDvH,KAAK,EAAGA,KAAK,IAAI;QACflB,OAAO,CAACkB,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,IAAI,CAAClF,YAAY,CAACmG,SAAS,CAAC,yCAAyC,CAAC;MACxE;KACD,CAAC;EACJ;EAEA+N,UAAUA,CAAClP,YAA0B;IACnChB,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEe,YAAY,CAAC;IAExD,IAAI,CAACjF,WAAW,CAACmU,UAAU,CAAClP,YAAY,CAACpN,EAAE,EAAE,eAAe,CAAC,CAACkN,SAAS,CAAC;MACtEC,IAAI,EAAEA,CAAA,KAAK;QACTf,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;QAC3C,IAAI,CAACjE,YAAY,CAACyM,WAAW,CAAC,cAAc,CAAC;MAC/C,CAAC;MACDvH,KAAK,EAAGA,KAAK,IAAI;QACflB,OAAO,CAACkB,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,IAAI,CAAClF,YAAY,CAACmG,SAAS,CAAC,iCAAiC,CAAC;MAChE;KACD,CAAC;EACJ;EAEA;EACA6N,OAAOA,CAAA;IACLhQ,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;IAE9C,IAAI,IAAI,CAAC3B,UAAU,EAAE1K,EAAE,EAAE;MACvB,IAAI,CAACmI,WAAW,CAACiU,OAAO,CAAC,IAAI,CAAC1R,UAAU,CAAC1K,EAAE,CAAC,CAACkN,SAAS,CAAC;QACrDC,IAAI,EAAEA,CAAA,KAAK;UACTf,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;UACtD,IAAI,CAACkQ,cAAc,EAAE;QACvB,CAAC;QACDjP,KAAK,EAAGA,KAAK,IAAI;UACflB,OAAO,CAACkB,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;UAC1D,IAAI,CAACiP,cAAc,EAAE,CAAC,CAAC;QACzB;OACD,CAAC;KACH,MAAM;MACLnQ,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC;MACtE,IAAI,CAACkQ,cAAc,EAAE;;EAEzB;EAEQP,cAAcA,CAAA;IACpB,IAAI,CAACxR,YAAY,GAAG,CAAC;IACrB,IAAI,CAACC,SAAS,GAAG+R,WAAW,CAAC,MAAK;MAChC,IAAI,CAAChS,YAAY,EAAE;MACnB,IAAI,CAACnC,GAAG,CAACoJ,aAAa,EAAE;IAC1B,CAAC,EAAE,IAAI,CAAC;EACV;EAEQ8K,cAAcA,CAAA;IACpB,IAAI,IAAI,CAAC9R,SAAS,EAAE;MAClBgS,aAAa,CAAC,IAAI,CAAChS,SAAS,CAAC;MAC7B,IAAI,CAACA,SAAS,GAAG,IAAI;;IAGvB,IAAI,CAACH,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,YAAY,GAAG,CAAC;IACrB,IAAI,CAACE,UAAU,GAAG,IAAI;IACtB,IAAI,CAACC,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACC,OAAO,GAAG,KAAK;IACpB,IAAI,CAACC,cAAc,GAAG,IAAI;EAC5B;EAEA;EACA6R,UAAUA,CAAA;IACR,IAAI,CAAC,IAAI,CAAChS,UAAU,EAAE;IAEtB,IAAI,CAACE,OAAO,GAAG,CAAC,IAAI,CAACA,OAAO;IAE5B;IACA,IAAI,CAACzC,WAAW,CACbwU,WAAW,CACV,IAAI,CAACjS,UAAU,CAAC1K,EAAE,EAClBsS,SAAS;IAAE;IACX,CAAC,IAAI,CAAC1H,OAAO,CAAC;KACf,CACAsC,SAAS,CAAC;MACTC,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAAC/E,YAAY,CAACyM,WAAW,CAC3B,IAAI,CAACjK,OAAO,GAAG,aAAa,GAAG,cAAc,CAC9C;MACH,CAAC;MACD0C,KAAK,EAAGA,KAAK,IAAI;QACflB,OAAO,CAACkB,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C;QACA,IAAI,CAAC1C,OAAO,GAAG,CAAC,IAAI,CAACA,OAAO;QAC5B,IAAI,CAACxC,YAAY,CAACmG,SAAS,CAAC,oCAAoC,CAAC;MACnE;KACD,CAAC;EACN;EAEAqO,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAAClS,UAAU,EAAE;IAEtB,IAAI,CAACG,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;IAE1C;IACA,IAAI,CAAC1C,WAAW,CACbwU,WAAW,CACV,IAAI,CAACjS,UAAU,CAAC1K,EAAE,EAClB,IAAI,CAAC6K,cAAc;IAAE;IACrByH,SAAS,CAAC;KACX,CACApF,SAAS,CAAC;MACTC,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAAC/E,YAAY,CAACyM,WAAW,CAC3B,IAAI,CAAChK,cAAc,GAAG,gBAAgB,GAAG,mBAAmB,CAC7D;MACH,CAAC;MACDyC,KAAK,EAAGA,KAAK,IAAI;QACflB,OAAO,CAACkB,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C;QACA,IAAI,CAACzC,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;QAC1C,IAAI,CAACzC,YAAY,CAACmG,SAAS,CAAC,wCAAwC,CAAC;MACvE;KACD,CAAC;EACN;EAEAsO,kBAAkBA,CAACzJ,QAAgB;IACjC,MAAM0J,KAAK,GAAG/J,IAAI,CAACC,KAAK,CAACI,QAAQ,GAAG,IAAI,CAAC;IACzC,MAAM2J,OAAO,GAAGhK,IAAI,CAACC,KAAK,CAAEI,QAAQ,GAAG,IAAI,GAAI,EAAE,CAAC;IAClD,MAAM4J,OAAO,GAAG5J,QAAQ,GAAG,EAAE;IAE7B,IAAI0J,KAAK,GAAG,CAAC,EAAE;MACb,OAAO,GAAGA,KAAK,IAAIC,OAAO,CAAC/F,QAAQ,EAAE,CAACiG,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAID,OAAO,CAC9DhG,QAAQ,EAAE,CACViG,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;;IAEvB,OAAO,GAAGF,OAAO,IAAIC,OAAO,CAAChG,QAAQ,EAAE,CAACiG,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EAC5D;EAEA;EACA;EAEMC,mBAAmBA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACvBhR,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;MAErD,IAAI;QACF;QACA,IAAI,CAACgR,SAAS,CAACC,YAAY,IAAI,CAACD,SAAS,CAACC,YAAY,CAACC,YAAY,EAAE;UACnE,MAAM,IAAIC,KAAK,CACb,yDAAyD,CAC1D;;QAGH;QACA,IAAI,CAACrI,MAAM,CAACsI,aAAa,EAAE;UACzB,MAAM,IAAID,KAAK,CACb,uDAAuD,CACxD;;QAGHpR,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;QAEzD;QACA,MAAMqR,MAAM,SAASL,SAAS,CAACC,YAAY,CAACC,YAAY,CAAC;UACvDI,KAAK,EAAE;YACLC,gBAAgB,EAAE,IAAI;YACtBC,gBAAgB,EAAE,IAAI;YACtBC,eAAe,EAAE,IAAI;YACrBC,UAAU,EAAE,KAAK;YACjBC,YAAY,EAAE;;SAEjB,CAAC;QAEF5R,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;QAEnD;QACA,IAAI4R,QAAQ,GAAG,wBAAwB;QACvC,IAAI,CAACR,aAAa,CAACS,eAAe,CAACD,QAAQ,CAAC,EAAE;UAC5CA,QAAQ,GAAG,YAAY;UACvB,IAAI,CAACR,aAAa,CAACS,eAAe,CAACD,QAAQ,CAAC,EAAE;YAC5CA,QAAQ,GAAG,WAAW;YACtB,IAAI,CAACR,aAAa,CAACS,eAAe,CAACD,QAAQ,CAAC,EAAE;cAC5CA,QAAQ,GAAG,EAAE,CAAC,CAAC;;;;;QAKrB7R,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE4R,QAAQ,CAAC;QAEpD;QACAd,KAAI,CAACnT,aAAa,GAAG,IAAIyT,aAAa,CAACC,MAAM,EAAE;UAC7CO,QAAQ,EAAEA,QAAQ,IAAI3L;SACvB,CAAC;QAEF;QACA6K,KAAI,CAAClT,WAAW,GAAG,EAAE;QACrBkT,KAAI,CAACrT,gBAAgB,GAAG,IAAI;QAC5BqT,KAAI,CAACxV,sBAAsB,GAAG,CAAC;QAC/BwV,KAAI,CAACpT,mBAAmB,GAAG,WAAW;QAEtCqC,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;QAElE;QACA8Q,KAAI,CAACjT,cAAc,GAAGsS,WAAW,CAAC,MAAK;UACrCW,KAAI,CAACxV,sBAAsB,EAAE;UAC7B;UACAwV,KAAI,CAACgB,iBAAiB,EAAE;UACxBhB,KAAI,CAAC9U,GAAG,CAACoJ,aAAa,EAAE;QAC1B,CAAC,EAAE,IAAI,CAAC;QAER;QACA0L,KAAI,CAACnT,aAAa,CAACoU,eAAe,GAAI/I,KAAK,IAAI;UAC7CjJ,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEgJ,KAAK,CAAC7B,IAAI,CAACpC,IAAI,EAAE,OAAO,CAAC;UACnE,IAAIiE,KAAK,CAAC7B,IAAI,CAACpC,IAAI,GAAG,CAAC,EAAE;YACvB+L,KAAI,CAAClT,WAAW,CAACuH,IAAI,CAAC6D,KAAK,CAAC7B,IAAI,CAAC;;QAErC,CAAC;QAED2J,KAAI,CAACnT,aAAa,CAACqU,MAAM,GAAG,MAAK;UAC/BjS,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;UACpE8Q,KAAI,CAACmB,oBAAoB,EAAE;QAC7B,CAAC;QAEDnB,KAAI,CAACnT,aAAa,CAACuU,OAAO,GAAIlJ,KAAU,IAAI;UAC1CjJ,OAAO,CAACkB,KAAK,CAAC,iCAAiC,EAAE+H,KAAK,CAAC/H,KAAK,CAAC;UAC7D6P,KAAI,CAAC/U,YAAY,CAACmG,SAAS,CAAC,iCAAiC,CAAC;UAC9D4O,KAAI,CAACqB,oBAAoB,EAAE;QAC7B,CAAC;QAED;QACArB,KAAI,CAACnT,aAAa,CAACyU,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;QAC/BrS,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;QAExD8Q,KAAI,CAAC/U,YAAY,CAACyM,WAAW,CAAC,iCAAiC,CAAC;OACjE,CAAC,OAAOvH,KAAU,EAAE;QACnBlB,OAAO,CAACkB,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;QAE5D,IAAIoR,YAAY,GAAG,+CAA+C;QAElE,IAAIpR,KAAK,CAAC1H,IAAI,KAAK,iBAAiB,EAAE;UACpC8Y,YAAY,GACV,iGAAiG;SACpG,MAAM,IAAIpR,KAAK,CAAC1H,IAAI,KAAK,eAAe,EAAE;UACzC8Y,YAAY,GACV,6DAA6D;SAChE,MAAM,IAAIpR,KAAK,CAAC1H,IAAI,KAAK,mBAAmB,EAAE;UAC7C8Y,YAAY,GACV,0DAA0D;SAC7D,MAAM,IAAIpR,KAAK,CAACiF,OAAO,EAAE;UACxBmM,YAAY,GAAGpR,KAAK,CAACiF,OAAO;;QAG9B4K,KAAI,CAAC/U,YAAY,CAACmG,SAAS,CAACmQ,YAAY,CAAC;QACzCvB,KAAI,CAACqB,oBAAoB,EAAE;;IAC5B;EACH;EAEAG,kBAAkBA,CAAA;IAChB,IAAI,IAAI,CAAC3U,aAAa,IAAI,IAAI,CAACA,aAAa,CAAC4U,KAAK,KAAK,WAAW,EAAE;MAClE,IAAI,CAAC5U,aAAa,CAAC6U,IAAI,EAAE;MACzB,IAAI,CAAC7U,aAAa,CAAC0T,MAAM,CAACoB,SAAS,EAAE,CAAC/N,OAAO,CAAEgO,KAAK,IAAKA,KAAK,CAACF,IAAI,EAAE,CAAC;;IAGxE,IAAI,IAAI,CAAC3U,cAAc,EAAE;MACvBuS,aAAa,CAAC,IAAI,CAACvS,cAAc,CAAC;MAClC,IAAI,CAACA,cAAc,GAAG,IAAI;;IAG5B,IAAI,CAACJ,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACC,mBAAmB,GAAG,YAAY;EACzC;EAEAyU,oBAAoBA,CAAA;IAClB,IAAI,IAAI,CAACxU,aAAa,EAAE;MACtB,IAAI,IAAI,CAACA,aAAa,CAAC4U,KAAK,KAAK,WAAW,EAAE;QAC5C,IAAI,CAAC5U,aAAa,CAAC6U,IAAI,EAAE;;MAE3B,IAAI,CAAC7U,aAAa,CAAC0T,MAAM,CAACoB,SAAS,EAAE,CAAC/N,OAAO,CAAEgO,KAAK,IAAKA,KAAK,CAACF,IAAI,EAAE,CAAC;MACtE,IAAI,CAAC7U,aAAa,GAAG,IAAI;;IAG3B,IAAI,IAAI,CAACE,cAAc,EAAE;MACvBuS,aAAa,CAAC,IAAI,CAACvS,cAAc,CAAC;MAClC,IAAI,CAACA,cAAc,GAAG,IAAI;;IAG5B,IAAI,CAACJ,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACnC,sBAAsB,GAAG,CAAC;IAC/B,IAAI,CAACoC,mBAAmB,GAAG,MAAM;IACjC,IAAI,CAACE,WAAW,GAAG,EAAE;EACvB;EAEcqU,oBAAoBA,CAAA;IAAA,IAAAU,MAAA;IAAA,OAAA5B,iBAAA;MAChChR,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;MAEtD,IAAI;QACF;QACA,IAAI2S,MAAI,CAAC/U,WAAW,CAAC0E,MAAM,KAAK,CAAC,EAAE;UACjCvC,OAAO,CAACkB,KAAK,CAAC,sCAAsC,CAAC;UACrD0R,MAAI,CAAC5W,YAAY,CAACmG,SAAS,CAAC,wBAAwB,CAAC;UACrDyQ,MAAI,CAACR,oBAAoB,EAAE;UAC3B;;QAGFpS,OAAO,CAACC,GAAG,CACT,0BAA0B,EAC1B2S,MAAI,CAAC/U,WAAW,CAAC0E,MAAM,EACvB,WAAW,EACXqQ,MAAI,CAACrX,sBAAsB,CAC5B;QAED;QACA,IAAIqX,MAAI,CAACrX,sBAAsB,GAAG,CAAC,EAAE;UACnCyE,OAAO,CAACkB,KAAK,CACX,iCAAiC,EACjC0R,MAAI,CAACrX,sBAAsB,CAC5B;UACDqX,MAAI,CAAC5W,YAAY,CAACmG,SAAS,CACzB,+CAA+C,CAChD;UACDyQ,MAAI,CAACR,oBAAoB,EAAE;UAC3B;;QAGF;QACA,IAAIP,QAAQ,GAAG,wBAAwB;QACvC,IAAIe,MAAI,CAAChV,aAAa,EAAEiU,QAAQ,EAAE;UAChCA,QAAQ,GAAGe,MAAI,CAAChV,aAAa,CAACiU,QAAQ;;QAGxC7R,OAAO,CAACC,GAAG,CAAC,gDAAgD,EAAE4R,QAAQ,CAAC;QAEvE;QACA,MAAMgB,SAAS,GAAG,IAAIC,IAAI,CAACF,MAAI,CAAC/U,WAAW,EAAE;UAC3CyG,IAAI,EAAEuN;SACP,CAAC;QAEF7R,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE;UAC5C+E,IAAI,EAAE6N,SAAS,CAAC7N,IAAI;UACpBV,IAAI,EAAEuO,SAAS,CAACvO;SACjB,CAAC;QAEF;QACA,IAAIyO,SAAS,GAAG,OAAO;QACvB,IAAIlB,QAAQ,CAAC5D,QAAQ,CAAC,KAAK,CAAC,EAAE;UAC5B8E,SAAS,GAAG,MAAM;SACnB,MAAM,IAAIlB,QAAQ,CAAC5D,QAAQ,CAAC,KAAK,CAAC,EAAE;UACnC8E,SAAS,GAAG,MAAM;SACnB,MAAM,IAAIlB,QAAQ,CAAC5D,QAAQ,CAAC,KAAK,CAAC,EAAE;UACnC8E,SAAS,GAAG,MAAM;;QAGpB;QACA,MAAMC,SAAS,GAAG,IAAIC,IAAI,CACxB,CAACJ,SAAS,CAAC,EACX,SAAStP,IAAI,CAACsD,GAAG,EAAE,GAAGkM,SAAS,EAAE,EACjC;UACEzO,IAAI,EAAEuN;SACP,CACF;QAED7R,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE;UAC5CzG,IAAI,EAAEwZ,SAAS,CAACxZ,IAAI;UACpBwL,IAAI,EAAEgO,SAAS,CAAChO,IAAI;UACpBV,IAAI,EAAE0O,SAAS,CAAC1O;SACjB,CAAC;QAEF;QACAsO,MAAI,CAACjV,mBAAmB,GAAG,YAAY;QACvC,MAAMiV,MAAI,CAACM,gBAAgB,CAACF,SAAS,CAAC;QAEtChT,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;QACzD2S,MAAI,CAAC5W,YAAY,CAACyM,WAAW,CAAC,yBAAyB,CAAC;OACzD,CAAC,OAAOvH,KAAU,EAAE;QACnBlB,OAAO,CAACkB,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;QAC1D0R,MAAI,CAAC5W,YAAY,CAACmG,SAAS,CACzB,2CAA2C,IACxCjB,KAAK,CAACiF,OAAO,IAAI,iBAAiB,CAAC,CACvC;OACF,SAAS;QACR;QACAyM,MAAI,CAACjV,mBAAmB,GAAG,MAAM;QACjCiV,MAAI,CAACrX,sBAAsB,GAAG,CAAC;QAC/BqX,MAAI,CAAC/U,WAAW,GAAG,EAAE;QACrB+U,MAAI,CAAClV,gBAAgB,GAAG,KAAK;QAE7BsC,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;;IAClE;EACH;EAEciT,gBAAgBA,CAACF,SAAe;IAAA,IAAAG,MAAA;IAAA,OAAAnC,iBAAA;MAC5C,MAAMxM,UAAU,GAAG2O,MAAI,CAAChhB,gBAAgB,EAAEyB,EAAE,IAAIuf,MAAI,CAAChhB,gBAAgB,EAAE2P,GAAG;MAE1E,IAAI,CAAC0C,UAAU,EAAE;QACf,MAAM,IAAI4M,KAAK,CAAC,0BAA0B,CAAC;;MAG7C,OAAO,IAAIgC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;QACrCH,MAAI,CAACrX,cAAc,CAACgK,WAAW,CAC7BtB,UAAU,EACV,EAAE,EACFwO,SAAS,EACT,OAAc,EACdG,MAAI,CAACjX,YAAY,CAACtI,EAAE,CACrB,CAACkN,SAAS,CAAC;UACVC,IAAI,EAAGoF,OAAY,IAAI;YACrBgN,MAAI,CAACna,QAAQ,CAACoM,IAAI,CAACe,OAAO,CAAC;YAC3BgN,MAAI,CAACrP,cAAc,EAAE;YACrBuP,OAAO,EAAE;UACX,CAAC;UACDnS,KAAK,EAAGA,KAAU,IAAI;YACpBlB,OAAO,CAACkB,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;YAChEoS,MAAM,CAACpS,KAAK,CAAC;UACf;SACD,CAAC;MACJ,CAAC,CAAC;IAAC;EACL;EAEA5F,uBAAuBA,CAAC0L,QAAgB;IACtC,MAAM2J,OAAO,GAAGhK,IAAI,CAACC,KAAK,CAACI,QAAQ,GAAG,EAAE,CAAC;IACzC,MAAM4J,OAAO,GAAG5J,QAAQ,GAAG,EAAE;IAC7B,OAAO,GAAG2J,OAAO,IAAIC,OAAO,CAAChG,QAAQ,EAAE,CAACiG,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EAC5D;EAEA;EAEA0C,aAAaA,CAACtK,KAAY;IACxBA,KAAK,CAACC,cAAc,EAAE;IACtBlJ,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;IAChDD,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE;MACvCvC,gBAAgB,EAAE,IAAI,CAACA,gBAAgB;MACvCC,mBAAmB,EAAE,IAAI,CAACA,mBAAmB;MAC7CpC,sBAAsB,EAAE,IAAI,CAACA,sBAAsB;MACnDqC,aAAa,EAAE,CAAC,CAAC,IAAI,CAACA;KACvB,CAAC;IAEF;IACA,IAAI,IAAI,CAACD,mBAAmB,KAAK,YAAY,EAAE;MAC7CqC,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;MAC5D,IAAI,CAACjE,YAAY,CAACwX,WAAW,CAAC,wBAAwB,CAAC;MACvD;;IAGF,IAAI,IAAI,CAAC9V,gBAAgB,EAAE;MACzBsC,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;MAC3D,IAAI,CAACjE,YAAY,CAACwX,WAAW,CAAC,iCAAiC,CAAC;MAChE;;IAGF;IACA,IAAI,CAACxX,YAAY,CAACyX,QAAQ,CAAC,2CAA2C,CAAC;IAEvE;IACA,IAAI,CAAC3C,mBAAmB,EAAE,CAAChI,KAAK,CAAE5H,KAAK,IAAI;MACzClB,OAAO,CAACkB,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;MAC7D,IAAI,CAAClF,YAAY,CAACmG,SAAS,CACzB,iDAAiD,IAC9CjB,KAAK,CAACiF,OAAO,IAAI,iBAAiB,CAAC,CACvC;IACH,CAAC,CAAC;EACJ;EAEA/K,WAAWA,CAAC6N,KAAY;IACtBA,KAAK,CAACC,cAAc,EAAE;IACtBlJ,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;IAE9C,IAAI,CAAC,IAAI,CAACvC,gBAAgB,EAAE;MAC1BsC,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;MACrD;;IAGF;IACA,IAAI,CAACsS,kBAAkB,EAAE;EAC3B;EAEAtX,cAAcA,CAACgO,KAAY;IACzBA,KAAK,CAACC,cAAc,EAAE;IACtBlJ,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;IAEjD,IAAI,CAAC,IAAI,CAACvC,gBAAgB,EAAE;MAC1BsC,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;MACxD;;IAGF;IACA,IAAI,CAACmS,oBAAoB,EAAE;EAC7B;EAEA5W,kBAAkBA,CAAA;IAChB,IAAI,IAAI,CAACoC,aAAa,EAAEiU,QAAQ,EAAE;MAChC,IAAI,IAAI,CAACjU,aAAa,CAACiU,QAAQ,CAAC5D,QAAQ,CAAC,MAAM,CAAC,EAAE,OAAO,MAAM;MAC/D,IAAI,IAAI,CAACrQ,aAAa,CAACiU,QAAQ,CAAC5D,QAAQ,CAAC,KAAK,CAAC,EAAE,OAAO,KAAK;MAC7D,IAAI,IAAI,CAACrQ,aAAa,CAACiU,QAAQ,CAAC5D,QAAQ,CAAC,KAAK,CAAC,EAAE,OAAO,KAAK;MAC7D,IAAI,IAAI,CAACrQ,aAAa,CAACiU,QAAQ,CAAC5D,QAAQ,CAAC,KAAK,CAAC,EAAE,OAAO,KAAK;;IAE/D,OAAO,MAAM;EACf;EAEA;EAEQ8D,iBAAiBA,CAAA;IACvB;IACA,IAAI,CAACrb,UAAU,GAAG,IAAI,CAACA,UAAU,CAACgd,GAAG,CAAC,MAAK;MACzC,OAAO/M,IAAI,CAACC,KAAK,CAACD,IAAI,CAACgN,MAAM,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;IAC7C,CAAC,CAAC;EACJ;;EAEAC,cAAcA,CAAC3K,KAAU;IACvBjJ,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;IACnD,MAAM4T,KAAK,GAAG5K,KAAK,CAACb,MAAM,CAACyL,KAAK;IAEhC,IAAI,CAACA,KAAK,IAAIA,KAAK,CAACtR,MAAM,KAAK,CAAC,EAAE;MAChCvC,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;MAC5C;;IAGFD,OAAO,CAACC,GAAG,CAAC,eAAe4T,KAAK,CAACtR,MAAM,oBAAoB,EAAEsR,KAAK,CAAC;IAEnE,KAAK,IAAIC,IAAI,IAAID,KAAK,EAAE;MACtB7T,OAAO,CAACC,GAAG,CACT,gCAAgC6T,IAAI,CAACta,IAAI,WAAWsa,IAAI,CAAC9O,IAAI,WAAW8O,IAAI,CAACxP,IAAI,EAAE,CACpF;MACD,IAAI,CAACyP,UAAU,CAACD,IAAI,CAAC;;EAEzB;EAEQC,UAAUA,CAACD,IAAU;IAC3B9T,OAAO,CAACC,GAAG,CAAC,yCAAyC6T,IAAI,CAACta,IAAI,EAAE,CAAC;IAEjE,MAAMgL,UAAU,GAAG,IAAI,CAACrS,gBAAgB,EAAEyB,EAAE,IAAI,IAAI,CAACzB,gBAAgB,EAAE2P,GAAG;IAE1E,IAAI,CAAC0C,UAAU,EAAE;MACfxE,OAAO,CAACkB,KAAK,CAAC,kCAAkC,CAAC;MACjD,IAAI,CAAClF,YAAY,CAACmG,SAAS,CAAC,0BAA0B,CAAC;MACvD;;IAGFnC,OAAO,CAACC,GAAG,CAAC,4BAA4BuE,UAAU,EAAE,CAAC;IAErD;IACA,MAAMwP,OAAO,GAAG,EAAE,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC;IAClC,IAAIF,IAAI,CAAC9O,IAAI,GAAGgP,OAAO,EAAE;MACvBhU,OAAO,CAACkB,KAAK,CAAC,+BAA+B4S,IAAI,CAAC9O,IAAI,QAAQ,CAAC;MAC/D,IAAI,CAAChJ,YAAY,CAACmG,SAAS,CAAC,oCAAoC,CAAC;MACjE;;IAGF;IACA,IAAI2R,IAAI,CAACxP,IAAI,CAACyD,UAAU,CAAC,QAAQ,CAAC,IAAI+L,IAAI,CAAC9O,IAAI,GAAG,IAAI,GAAG,IAAI,EAAE;MAC7D;MACAhF,OAAO,CAACC,GAAG,CACT,sCAAsC,EACtC6T,IAAI,CAACta,IAAI,EACT,gBAAgB,EAChBsa,IAAI,CAAC9O,IAAI,CACV;MACD,IAAI,CAACiP,aAAa,CAACH,IAAI,CAAC,CACrBjL,IAAI,CAAEqL,cAAc,IAAI;QACvBlU,OAAO,CAACC,GAAG,CACT,8DAA8D,EAC9DiU,cAAc,CAAClP,IAAI,CACpB;QACD,IAAI,CAACmP,gBAAgB,CAACD,cAAc,EAAE1P,UAAU,CAAC;MACnD,CAAC,CAAC,CACDsE,KAAK,CAAE5H,KAAK,IAAI;QACflB,OAAO,CAACkB,KAAK,CAAC,8CAA8C,EAAEA,KAAK,CAAC;QACpE;QACA,IAAI,CAACiT,gBAAgB,CAACL,IAAI,EAAEtP,UAAU,CAAC;MACzC,CAAC,CAAC;MACJ;;IAGF;IACA,IAAI,CAAC2P,gBAAgB,CAACL,IAAI,EAAEtP,UAAU,CAAC;EACzC;EAEQ2P,gBAAgBA,CAACL,IAAU,EAAEtP,UAAkB;IACrD,MAAM4P,WAAW,GAAG,IAAI,CAACC,kBAAkB,CAACP,IAAI,CAAC;IACjD9T,OAAO,CAACC,GAAG,CAAC,wCAAwCmU,WAAW,EAAE,CAAC;IAClEpU,OAAO,CAACC,GAAG,CAAC,gCAAgC,IAAI,CAAC/D,YAAY,CAACtI,EAAE,EAAE,CAAC;IAEnE,IAAI,CAACiJ,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACW,WAAW,GAAG,IAAI;IACvB,IAAI,CAACD,cAAc,GAAG,CAAC;IACvByC,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC;IAEhE;IACA,MAAMqU,gBAAgB,GAAGlE,WAAW,CAAC,MAAK;MACxC,IAAI,CAAC7S,cAAc,IAAIoJ,IAAI,CAACgN,MAAM,EAAE,GAAG,EAAE;MACzC,IAAI,IAAI,CAACpW,cAAc,IAAI,EAAE,EAAE;QAC7B8S,aAAa,CAACiE,gBAAgB,CAAC;;MAEjC,IAAI,CAACrY,GAAG,CAACoJ,aAAa,EAAE;IAC1B,CAAC,EAAE,GAAG,CAAC;IAEP,IAAI,CAACvJ,cAAc,CAACgK,WAAW,CAC7BtB,UAAU,EACV,EAAE,EACFsP,IAAI,EACJM,WAAW,EACX,IAAI,CAAClY,YAAY,CAACtI,EAAE,CACrB,CAACkN,SAAS,CAAC;MACVC,IAAI,EAAGoF,OAAY,IAAI;QACrBnG,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEkG,OAAO,CAAC;QAC7DnG,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAE;UAChDrM,EAAE,EAAEuS,OAAO,CAACvS,EAAE;UACd0Q,IAAI,EAAE6B,OAAO,CAAC7B,IAAI;UAClBG,WAAW,EAAE0B,OAAO,CAAC1B,WAAW;UAChChM,QAAQ,EAAE,IAAI,CAACA,QAAQ,CAAC0N,OAAO,CAAC;UAChCzB,OAAO,EAAE,IAAI,CAACA,OAAO,CAACyB,OAAO,CAAC;UAC9BwH,QAAQ,EAAE,IAAI,CAACpY,WAAW,CAAC4Q,OAAO;SACnC,CAAC;QAEFkK,aAAa,CAACiE,gBAAgB,CAAC;QAC/B,IAAI,CAAC/W,cAAc,GAAG,GAAG;QAEzB+H,UAAU,CAAC,MAAK;UACd,IAAI,CAACtM,QAAQ,CAACoM,IAAI,CAACe,OAAO,CAAC;UAC3B,IAAI,CAACrC,cAAc,EAAE;UACrB,IAAI,CAAC9H,YAAY,CAACyM,WAAW,CAAC,4BAA4B,CAAC;UAC3D,IAAI,CAAC8L,gBAAgB,EAAE;QACzB,CAAC,EAAE,GAAG,CAAC;MACT,CAAC;MACDrT,KAAK,EAAGA,KAAU,IAAI;QACpBlB,OAAO,CAACkB,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;QACzDmP,aAAa,CAACiE,gBAAgB,CAAC;QAC/B,IAAI,CAACtY,YAAY,CAACmG,SAAS,CAAC,mCAAmC,CAAC;QAChE,IAAI,CAACoS,gBAAgB,EAAE;MACzB;KACD,CAAC;EACJ;EAEQF,kBAAkBA,CAACP,IAAU;IACnC,IAAIA,IAAI,CAACxP,IAAI,CAACyD,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,OAAc;IACzD,IAAI+L,IAAI,CAACxP,IAAI,CAACyD,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,OAAc;IACzD,IAAI+L,IAAI,CAACxP,IAAI,CAACyD,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,OAAc;IACzD,OAAO,MAAa;EACtB;EAEAyM,kBAAkBA,CAAA;IAChB,OAAO,KAAK;EACd;EAEAD,gBAAgBA,CAAA;IACd,IAAI,CAAC1X,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACW,WAAW,GAAG,KAAK;IACxB,IAAI,CAACD,cAAc,GAAG,CAAC;EACzB;EAEA;EAEAkX,UAAUA,CAACxL,KAAgB;IACzBA,KAAK,CAACC,cAAc,EAAE;IACtBD,KAAK,CAACK,eAAe,EAAE;IACvB,IAAI,CAAC7L,UAAU,GAAG,IAAI;EACxB;EAEAiX,WAAWA,CAACzL,KAAgB;IAC1BA,KAAK,CAACC,cAAc,EAAE;IACtBD,KAAK,CAACK,eAAe,EAAE;IACvB;IACA,MAAMqL,IAAI,GAAI1L,KAAK,CAAC2L,aAA6B,CAACC,qBAAqB,EAAE;IACzE,MAAM5X,CAAC,GAAGgM,KAAK,CAACE,OAAO;IACvB,MAAMjM,CAAC,GAAG+L,KAAK,CAACG,OAAO;IAEvB,IAAInM,CAAC,GAAG0X,IAAI,CAACG,IAAI,IAAI7X,CAAC,GAAG0X,IAAI,CAACI,KAAK,IAAI7X,CAAC,GAAGyX,IAAI,CAACK,GAAG,IAAI9X,CAAC,GAAGyX,IAAI,CAACM,MAAM,EAAE;MACtE,IAAI,CAACxX,UAAU,GAAG,KAAK;;EAE3B;EAEAyX,MAAMA,CAACjM,KAAgB;IACrBA,KAAK,CAACC,cAAc,EAAE;IACtBD,KAAK,CAACK,eAAe,EAAE;IACvB,IAAI,CAAC7L,UAAU,GAAG,KAAK;IAEvB,MAAMoW,KAAK,GAAG5K,KAAK,CAACkM,YAAY,EAAEtB,KAAK;IACvC,IAAIA,KAAK,IAAIA,KAAK,CAACtR,MAAM,GAAG,CAAC,EAAE;MAC7BvC,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE4T,KAAK,CAACtR,MAAM,CAAC;MAE1D;MACA6S,KAAK,CAACC,IAAI,CAACxB,KAAK,CAAC,CAAClP,OAAO,CAAEmP,IAAI,IAAI;QACjC9T,OAAO,CAACC,GAAG,CACT,iCAAiC,EACjC6T,IAAI,CAACta,IAAI,EACTsa,IAAI,CAACxP,IAAI,EACTwP,IAAI,CAAC9O,IAAI,CACV;QACD,IAAI,CAAC+O,UAAU,CAACD,IAAI,CAAC;MACvB,CAAC,CAAC;MAEF,IAAI,CAAC9X,YAAY,CAACyM,WAAW,CAC3B,GAAGoL,KAAK,CAACtR,MAAM,8BAA8B,CAC9C;;EAEL;EAEA;EAEQ0R,aAAaA,CAACH,IAAU,EAAEwB,OAAA,GAAkB,GAAG;IACrD,OAAO,IAAIlC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;MACrC,MAAMiC,MAAM,GAAGlV,QAAQ,CAAC4H,aAAa,CAAC,QAAQ,CAAC;MAC/C,MAAMuN,GAAG,GAAGD,MAAM,CAACE,UAAU,CAAC,IAAI,CAAC;MACnC,MAAMC,GAAG,GAAG,IAAIC,KAAK,EAAE;MAEvBD,GAAG,CAACE,MAAM,GAAG,MAAK;QAChB;QACA,MAAMC,QAAQ,GAAG,IAAI;QACrB,MAAMC,SAAS,GAAG,IAAI;QACtB,IAAI;UAAEC,KAAK;UAAEC;QAAM,CAAE,GAAGN,GAAG;QAE3B,IAAIK,KAAK,GAAGF,QAAQ,IAAIG,MAAM,GAAGF,SAAS,EAAE;UAC1C,MAAMG,KAAK,GAAGtP,IAAI,CAACmF,GAAG,CAAC+J,QAAQ,GAAGE,KAAK,EAAED,SAAS,GAAGE,MAAM,CAAC;UAC5DD,KAAK,IAAIE,KAAK;UACdD,MAAM,IAAIC,KAAK;;QAGjBV,MAAM,CAACQ,KAAK,GAAGA,KAAK;QACpBR,MAAM,CAACS,MAAM,GAAGA,MAAM;QAEtB;QACAR,GAAG,EAAEU,SAAS,CAACR,GAAG,EAAE,CAAC,EAAE,CAAC,EAAEK,KAAK,EAAEC,MAAM,CAAC;QAExC;QACAT,MAAM,CAACY,MAAM,CACVC,IAAI,IAAI;UACP,IAAIA,IAAI,EAAE;YACR,MAAMlC,cAAc,GAAG,IAAIjB,IAAI,CAAC,CAACmD,IAAI,CAAC,EAAEtC,IAAI,CAACta,IAAI,EAAE;cACjD8K,IAAI,EAAEwP,IAAI,CAACxP,IAAI;cACf+R,YAAY,EAAE9S,IAAI,CAACsD,GAAG;aACvB,CAAC;YACFwM,OAAO,CAACa,cAAc,CAAC;WACxB,MAAM;YACLZ,MAAM,CAAC,IAAIlC,KAAK,CAAC,0BAA0B,CAAC,CAAC;;QAEjD,CAAC,EACD0C,IAAI,CAACxP,IAAI,EACTgR,OAAO,CACR;MACH,CAAC;MAEDI,GAAG,CAACvD,OAAO,GAAG,MAAMmB,MAAM,CAAC,IAAIlC,KAAK,CAAC,sBAAsB,CAAC,CAAC;MAC7DsE,GAAG,CAAC9G,GAAG,GAAG0H,GAAG,CAACC,eAAe,CAACzC,IAAI,CAAC;IACrC,CAAC,CAAC;EACJ;EAEA;EAEQzF,qBAAqBA,CAAA;IAC3B,IAAI,CAAC,IAAI,CAACpP,QAAQ,EAAE;MAClB,IAAI,CAACA,QAAQ,GAAG,IAAI;MACpB;MACA,IAAI,CAACuX,mBAAmB,CAAC,IAAI,CAAC;;IAGhC;IACA,IAAI,IAAI,CAACrX,aAAa,EAAE;MACtBsX,YAAY,CAAC,IAAI,CAACtX,aAAa,CAAC;;IAGlC,IAAI,CAACA,aAAa,GAAGmG,UAAU,CAAC,MAAK;MACnC,IAAI,CAACrG,QAAQ,GAAG,KAAK;MACrB;MACA,IAAI,CAACuX,mBAAmB,CAAC,KAAK,CAAC;IACjC,CAAC,EAAE,IAAI,CAAC;EACV;EAEQA,mBAAmBA,CAACvX,QAAiB;IAC3C;IACA,MAAMuF,UAAU,GAAG,IAAI,CAACrS,gBAAgB,EAAEyB,EAAE,IAAI,IAAI,CAACzB,gBAAgB,EAAE2P,GAAG;IAC1E,IAAI0C,UAAU,IAAI,IAAI,CAACtI,YAAY,EAAEtI,EAAE,EAAE;MACvCoM,OAAO,CAACC,GAAG,CACT,gCAAgChB,QAAQ,YAAYuF,UAAU,EAAE,CACjE;MACD;MACA;;EAEJ;EAEA;EAEAkS,cAAcA,CAACtV,IAAU;IACvBpB,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEmB,IAAI,CAAC;IACrD,IAAI,CAAC9C,UAAU,GAAG8C,IAAI;IACtB,IAAI,CAAClD,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACK,eAAe,GAAG,IAAI;IAC3B,IAAI,CAACqR,cAAc,EAAE;IACrB,IAAI,CAAC5T,YAAY,CAACyM,WAAW,CAAC,eAAe,CAAC;EAChD;EAEAkO,cAAcA,CAAA;IACZ3W,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;IAC9C,IAAI,CAAC+P,OAAO,EAAE;IACd,IAAI,CAAChU,YAAY,CAACyX,QAAQ,CAAC,cAAc,CAAC;EAC5C;EAEA;EAEAmD,gBAAgBA,CAACzQ,OAAY;IAC3BnG,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEkG,OAAO,CAACvS,EAAE,CAAC;IAC5D,IAAI,CAACyC,mBAAmB,CAAC8P,OAAO,CAAC;EACnC;EAEA1Q,cAAcA,CAACoQ,SAAiB;IAC9B,OAAO,IAAI,CAAC7H,gBAAgB,KAAK6H,SAAS;EAC5C;EAEAxP,mBAAmBA,CAAC8P,OAAY;IAC9B,MAAMN,SAAS,GAAGM,OAAO,CAACvS,EAAE;IAC5B,MAAM2Z,QAAQ,GAAG,IAAI,CAACsJ,WAAW,CAAC1Q,OAAO,CAAC;IAE1C,IAAI,CAACoH,QAAQ,EAAE;MACbvN,OAAO,CAACkB,KAAK,CAAC,4CAA4C,EAAE2E,SAAS,CAAC;MACtE,IAAI,CAAC7J,YAAY,CAACmG,SAAS,CAAC,2BAA2B,CAAC;MACxD;;IAGF;IACA,IAAI,IAAI,CAAC1M,cAAc,CAACoQ,SAAS,CAAC,EAAE;MAClC,IAAI,CAACiR,iBAAiB,EAAE;MACxB;;IAGF;IACA,IAAI,CAACA,iBAAiB,EAAE;IAExB;IACA,IAAI,CAACC,kBAAkB,CAAC5Q,OAAO,EAAEoH,QAAQ,CAAC;EAC5C;EAEQwJ,kBAAkBA,CAAC5Q,OAAY,EAAEoH,QAAgB;IACvD,MAAM1H,SAAS,GAAGM,OAAO,CAACvS,EAAE;IAE5B,IAAI;MACFoM,OAAO,CAACC,GAAG,CACT,mCAAmC,EACnC4F,SAAS,EACT,MAAM,EACN0H,QAAQ,CACT;MAED,IAAI,CAACxP,YAAY,GAAG,IAAIiZ,KAAK,CAACzJ,QAAQ,CAAC;MACvC,IAAI,CAACvP,gBAAgB,GAAG6H,SAAS;MAEjC;MACA,MAAMoR,WAAW,GAAG,IAAI,CAACnQ,oBAAoB,CAACjB,SAAS,CAAC;MACxD,IAAI,CAACsB,oBAAoB,CAACtB,SAAS,EAAE;QACnCkB,QAAQ,EAAE,CAAC;QACXE,WAAW,EAAE,CAAC;QACdC,KAAK,EAAE+P,WAAW,CAAC/P,KAAK,IAAI,CAAC;QAC7BF,QAAQ,EAAEiQ,WAAW,CAACjQ,QAAQ,IAAI;OACnC,CAAC;MAEF;MACA,IAAI,CAACjJ,YAAY,CAACmZ,YAAY,GAAGD,WAAW,CAAC/P,KAAK,IAAI,CAAC;MAEvD;MACA,IAAI,CAACnJ,YAAY,CAACwC,gBAAgB,CAAC,gBAAgB,EAAE,MAAK;QACxD,IAAI,IAAI,CAACxC,YAAY,EAAE;UACrB,IAAI,CAACoJ,oBAAoB,CAACtB,SAAS,EAAE;YACnCmB,QAAQ,EAAE,IAAI,CAACjJ,YAAY,CAACiJ;WAC7B,CAAC;UACFhH,OAAO,CAACC,GAAG,CACT,oCAAoC,EACpC,IAAI,CAAClC,YAAY,CAACiJ,QAAQ,CAC3B;;MAEL,CAAC,CAAC;MAEF,IAAI,CAACjJ,YAAY,CAACwC,gBAAgB,CAAC,YAAY,EAAE,MAAK;QACpD,IAAI,IAAI,CAACxC,YAAY,IAAI,IAAI,CAACC,gBAAgB,KAAK6H,SAAS,EAAE;UAC5D,MAAMoB,WAAW,GAAG,IAAI,CAAClJ,YAAY,CAACkJ,WAAW;UACjD,MAAMF,QAAQ,GAAIE,WAAW,GAAG,IAAI,CAAClJ,YAAY,CAACiJ,QAAQ,GAAI,GAAG;UACjE,IAAI,CAACG,oBAAoB,CAACtB,SAAS,EAAE;YAAEoB,WAAW;YAAEF;UAAQ,CAAE,CAAC;UAC/D,IAAI,CAAC9K,GAAG,CAACoJ,aAAa,EAAE;;MAE5B,CAAC,CAAC;MAEF,IAAI,CAACtH,YAAY,CAACwC,gBAAgB,CAAC,OAAO,EAAE,MAAK;QAC/CP,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE4F,SAAS,CAAC;QACxD,IAAI,CAACiR,iBAAiB,EAAE;MAC1B,CAAC,CAAC;MAEF,IAAI,CAAC/Y,YAAY,CAACwC,gBAAgB,CAAC,OAAO,EAAGW,KAAK,IAAI;QACpDlB,OAAO,CAACkB,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,IAAI,CAAClF,YAAY,CAACmG,SAAS,CAAC,iCAAiC,CAAC;QAC9D,IAAI,CAAC2U,iBAAiB,EAAE;MAC1B,CAAC,CAAC;MAEF;MACA,IAAI,CAAC/Y,YAAY,CACduD,IAAI,EAAE,CACNuH,IAAI,CAAC,MAAK;QACT7I,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;QACvD,IAAI,CAACjE,YAAY,CAACyM,WAAW,CAAC,6BAA6B,CAAC;MAC9D,CAAC,CAAC,CACDK,KAAK,CAAE5H,KAAK,IAAI;QACflB,OAAO,CAACkB,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;QAC3D,IAAI,CAAClF,YAAY,CAACmG,SAAS,CAAC,qCAAqC,CAAC;QAClE,IAAI,CAAC2U,iBAAiB,EAAE;MAC1B,CAAC,CAAC;KACL,CAAC,OAAO5V,KAAK,EAAE;MACdlB,OAAO,CAACkB,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxD,IAAI,CAAClF,YAAY,CAACmG,SAAS,CAAC,iCAAiC,CAAC;MAC9D,IAAI,CAAC2U,iBAAiB,EAAE;;EAE5B;EAEQA,iBAAiBA,CAAA;IACvB,IAAI,IAAI,CAAC/Y,YAAY,EAAE;MACrBiC,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAE,IAAI,CAACjC,gBAAgB,CAAC;MACvE,IAAI,CAACD,YAAY,CAACoZ,KAAK,EAAE;MACzB,IAAI,CAACpZ,YAAY,CAACkJ,WAAW,GAAG,CAAC;MACjC,IAAI,CAAClJ,YAAY,GAAG,IAAI;;IAE1B,IAAI,CAACC,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAAC/B,GAAG,CAACoJ,aAAa,EAAE;EAC1B;EAEAwR,WAAWA,CAAC1Q,OAAY;IACtB;IACA,IAAIA,OAAO,CAACmH,QAAQ,EAAE,OAAOnH,OAAO,CAACmH,QAAQ;IAC7C,IAAInH,OAAO,CAACoH,QAAQ,EAAE,OAAOpH,OAAO,CAACoH,QAAQ;IAC7C,IAAIpH,OAAO,CAACqH,KAAK,EAAE,OAAOrH,OAAO,CAACqH,KAAK;IAEvC;IACA,MAAM4J,eAAe,GAAGjR,OAAO,CAAC1B,WAAW,EAAE3B,IAAI,CAC9C8B,GAAQ,IAAKA,GAAG,CAACN,IAAI,EAAEyD,UAAU,CAAC,QAAQ,CAAC,IAAInD,GAAG,CAACN,IAAI,KAAK,OAAO,CACrE;IAED,IAAI8S,eAAe,EAAE;MACnB,OAAOA,eAAe,CAACtS,GAAG,IAAIsS,eAAe,CAACrS,IAAI,IAAI,EAAE;;IAG1D,OAAO,EAAE;EACX;EAEAsS,aAAaA,CAAClR,OAAY;IACxB;IACA,MAAMN,SAAS,GAAGM,OAAO,CAACvS,EAAE,IAAI,EAAE;IAClC,MAAM0jB,IAAI,GAAGzR,SAAS,CACnB0R,KAAK,CAAC,EAAE,CAAC,CACTC,MAAM,CAAC,CAACC,GAAW,EAAEC,IAAY,KAAKD,GAAG,GAAGC,IAAI,CAACvJ,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACrE,MAAMwJ,KAAK,GAAa,EAAE;IAE1B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;MAC3B,MAAM5B,MAAM,GAAG,CAAC,GAAI,CAACsB,IAAI,GAAGM,CAAC,GAAG,CAAC,IAAI,EAAG;MACxCD,KAAK,CAACvS,IAAI,CAAC4Q,MAAM,CAAC;;IAGpB,OAAO2B,KAAK;EACd;EAEAE,gBAAgBA,CAAC1R,OAAY;IAC3B,MAAMiB,IAAI,GAAG,IAAI,CAACN,oBAAoB,CAACX,OAAO,CAACvS,EAAE,CAAC;IAClD,MAAMkkB,UAAU,GAAG,EAAE;IACrB,OAAOnR,IAAI,CAACC,KAAK,CAAEQ,IAAI,CAACL,QAAQ,GAAG,GAAG,GAAI+Q,UAAU,CAAC;EACvD;EAEAC,mBAAmBA,CAAC5R,OAAY;IAC9B,MAAMiB,IAAI,GAAG,IAAI,CAACN,oBAAoB,CAACX,OAAO,CAACvS,EAAE,CAAC;IAClD,OAAO,IAAI,CAACokB,eAAe,CAAC5Q,IAAI,CAACH,WAAW,CAAC;EAC/C;EAEAtQ,gBAAgBA,CAACwP,OAAY;IAC3B,MAAMiB,IAAI,GAAG,IAAI,CAACN,oBAAoB,CAACX,OAAO,CAACvS,EAAE,CAAC;IAClD,MAAMoT,QAAQ,GAAGI,IAAI,CAACJ,QAAQ,IAAIb,OAAO,CAAC8R,QAAQ,EAAEjR,QAAQ,IAAI,CAAC;IAEjE,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;MAChC,OAAOA,QAAQ,CAAC,CAAC;;;IAGnB,OAAO,IAAI,CAACgR,eAAe,CAAChR,QAAQ,CAAC;EACvC;EAEQgR,eAAeA,CAACpH,OAAe;IACrC,MAAMD,OAAO,GAAGhK,IAAI,CAACC,KAAK,CAACgK,OAAO,GAAG,EAAE,CAAC;IACxC,MAAMsH,gBAAgB,GAAGvR,IAAI,CAACC,KAAK,CAACgK,OAAO,GAAG,EAAE,CAAC;IACjD,OAAO,GAAGD,OAAO,IAAIuH,gBAAgB,CAACtN,QAAQ,EAAE,CAACiG,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EACrE;EAEAsH,gBAAgBA,CAAChS,OAAY,EAAEiS,SAAiB;IAC9C,MAAMvS,SAAS,GAAGM,OAAO,CAACvS,EAAE;IAE5B,IAAI,CAAC,IAAI,CAACmK,YAAY,IAAI,IAAI,CAACC,gBAAgB,KAAK6H,SAAS,EAAE;MAC7D;;IAGF,MAAMiS,UAAU,GAAG,EAAE;IACrB,MAAMO,cAAc,GAAID,SAAS,GAAGN,UAAU,GAAI,GAAG;IACrD,MAAMQ,QAAQ,GAAID,cAAc,GAAG,GAAG,GAAI,IAAI,CAACta,YAAY,CAACiJ,QAAQ;IAEpE,IAAI,CAACjJ,YAAY,CAACkJ,WAAW,GAAGqR,QAAQ;IACxCtY,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEqY,QAAQ,EAAE,SAAS,CAAC;EAC5D;EAEAC,gBAAgBA,CAACpS,OAAY;IAC3B,MAAMN,SAAS,GAAGM,OAAO,CAACvS,EAAE;IAC5B,MAAMwT,IAAI,GAAG,IAAI,CAACN,oBAAoB,CAACjB,SAAS,CAAC;IAEjD;IACA,MAAM2S,QAAQ,GAAGpR,IAAI,CAACF,KAAK,KAAK,CAAC,GAAG,GAAG,GAAGE,IAAI,CAACF,KAAK,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC;IAEpE,IAAI,CAACC,oBAAoB,CAACtB,SAAS,EAAE;MAAEqB,KAAK,EAAEsR;IAAQ,CAAE,CAAC;IAEzD,IAAI,IAAI,CAACza,YAAY,IAAI,IAAI,CAACC,gBAAgB,KAAK6H,SAAS,EAAE;MAC5D,IAAI,CAAC9H,YAAY,CAACmZ,YAAY,GAAGsB,QAAQ;;IAG3C,IAAI,CAACxc,YAAY,CAACyM,WAAW,CAAC,YAAY+P,QAAQ,GAAG,CAAC;EACxD;EAEA;EAEAziB,gBAAgBA,CAACoQ,OAAY;IAC3B,IAAI,CAACoS,gBAAgB,CAACpS,OAAO,CAAC;EAChC;EAEAlQ,aAAaA,CAACkQ,OAAY;IACxB,MAAMiB,IAAI,GAAG,IAAI,CAACN,oBAAoB,CAACX,OAAO,CAACvS,EAAE,CAAC;IAClD,OAAOwT,IAAI,CAACF,KAAK,IAAI,CAAC;EACxB;EAEAuR,WAAWA,CAAA;IACT,IAAI,CAACrZ,aAAa,CAACsZ,WAAW,EAAE;IAEhC;IACA,IAAI,IAAI,CAACra,SAAS,EAAE;MAClBgS,aAAa,CAAC,IAAI,CAAChS,SAAS,CAAC;;IAE/B,IAAI,IAAI,CAACP,cAAc,EAAE;MACvBuS,aAAa,CAAC,IAAI,CAACvS,cAAc,CAAC;;IAEpC,IAAI,IAAI,CAACqB,aAAa,EAAE;MACtBsX,YAAY,CAAC,IAAI,CAACtX,aAAa,CAAC;;IAGlC;IACA,IAAI,IAAI,CAACvB,aAAa,EAAE;MACtB,IAAI,IAAI,CAACA,aAAa,CAAC4U,KAAK,KAAK,WAAW,EAAE;QAC5C,IAAI,CAAC5U,aAAa,CAAC6U,IAAI,EAAE;;MAE3B,IAAI,CAAC7U,aAAa,CAAC0T,MAAM,EAAEoB,SAAS,EAAE,CAAC/N,OAAO,CAAEgO,KAAK,IAAKA,KAAK,CAACF,IAAI,EAAE,CAAC;;IAGzE;IACA,IAAI,CAACqE,iBAAiB,EAAE;EAC1B;;;uBApgFWrb,oBAAoB,EAAA9J,EAAA,CAAAgnB,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAlnB,EAAA,CAAAgnB,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAApnB,EAAA,CAAAgnB,iBAAA,CAAAG,EAAA,CAAAE,MAAA,GAAArnB,EAAA,CAAAgnB,iBAAA,CAAAM,EAAA,CAAAnd,cAAA,GAAAnK,EAAA,CAAAgnB,iBAAA,CAAAO,EAAA,CAAAC,WAAA,GAAAxnB,EAAA,CAAAgnB,iBAAA,CAAAS,EAAA,CAAAC,YAAA,GAAA1nB,EAAA,CAAAgnB,iBAAA,CAAAhnB,EAAA,CAAA2nB,iBAAA;IAAA;EAAA;;;YAApB7d,oBAAoB;MAAA8d,SAAA;MAAAC,SAAA,WAAAC,2BAAAC,EAAA,EAAAlE,GAAA;QAAA,IAAAkE,EAAA;;;;;;;;;;;;;;;;;;;UClBjC/nB,EAAA,CAAAC,SAAA,kBAA4E;UAG5ED,EAAA,CAAAE,cAAA,aASC;UAsEKF,EAAA,CAAAY,UAAA,mBAAAonB,sDAAA;YAAA,OAASnE,GAAA,CAAA7M,qBAAA,EAAuB;UAAA,EAAC;UAmBjChX,EAAA,CAAAC,SAAA,WAGK;UACPD,EAAA,CAAAI,YAAA,EAAS;UAGTJ,EAAA,CAAAE,cAAA,aAAuE;UAejEF,EAAA,CAAAY,UAAA,mBAAAqnB,oDAAA;YAAA,OAASpE,GAAA,CAAA9hB,eAAA,CAAA8hB,GAAA,CAAArjB,gBAAA,kBAAAqjB,GAAA,CAAArjB,gBAAA,CAAAyB,EAAA,CAAsC;UAAA,EAAC;UAZlDjC,EAAA,CAAAI,YAAA,EAgBE;UAEFJ,EAAA,CAAAyD,UAAA,KAAAykB,oCAAA,kBAaO;UACTloB,EAAA,CAAAI,YAAA,EAAM;UAGNJ,EAAA,CAAAE,cAAA,eAAmC;UAY/BF,EAAA,CAAAG,MAAA,IACF;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACLJ,EAAA,CAAAE,cAAA,eAA8D;UAE5DF,EAAA,CAAAyD,UAAA,KAAA0kB,oCAAA,kBAkCM;UAENnoB,EAAA,CAAAyD,UAAA,KAAA2kB,qCAAA,mBAMO;UACTpoB,EAAA,CAAAI,YAAA,EAAM;UAKVJ,EAAA,CAAAE,cAAA,eAA0D;UAGtDF,EAAA,CAAAY,UAAA,mBAAAynB,uDAAA;YAAA,OAASxE,GAAA,CAAAnO,cAAA,EAAgB;UAAA,EAAC;UAoB1B1V,EAAA,CAAAC,SAAA,aAAoD;UACtDD,EAAA,CAAAI,YAAA,EAAS;UAGTJ,EAAA,CAAAE,cAAA,kBAoBC;UAnBCF,EAAA,CAAAY,UAAA,mBAAA0nB,uDAAA;YAAA,OAASzE,GAAA,CAAAhO,cAAA,EAAgB;UAAA,EAAC;UAoB1B7V,EAAA,CAAAC,SAAA,aAAoD;UACtDD,EAAA,CAAAI,YAAA,EAAS;UAGTJ,EAAA,CAAAE,cAAA,kBAcC;UAbCF,EAAA,CAAAY,UAAA,mBAAA2nB,uDAAA;YAAA,OAAS1E,GAAA,CAAA3iB,YAAA,EAAc;UAAA,EAAC;UAcxBlB,EAAA,CAAAC,SAAA,aAA6B;UAC/BD,EAAA,CAAAI,YAAA,EAAS;UAGTJ,EAAA,CAAAE,cAAA,kBAeC;UAdCF,EAAA,CAAAY,UAAA,mBAAA4nB,uDAAA;YAAA,OAAS3E,GAAA,CAAA9M,cAAA,EAAgB;UAAA,EAAC;UAe1B/W,EAAA,CAAAC,SAAA,aAAiC;UACnCD,EAAA,CAAAI,YAAA,EAAS;UAIXJ,EAAA,CAAAyD,UAAA,KAAAglB,oCAAA,mBA8EM;UACRzoB,EAAA,CAAAI,YAAA,EAAS;UAGTJ,EAAA,CAAAE,cAAA,oBAQC;UALCF,EAAA,CAAAY,UAAA,oBAAA8nB,sDAAAvlB,MAAA;YAAA,OAAU0gB,GAAA,CAAA7G,QAAA,CAAA7Z,MAAA,CAAgB;UAAA,EAAC,sBAAAwlB,wDAAAxlB,MAAA;YAAA,OACf0gB,GAAA,CAAAf,UAAA,CAAA3f,MAAA,CAAkB;UAAA,EADH,uBAAAylB,yDAAAzlB,MAAA;YAAA,OAEd0gB,GAAA,CAAAd,WAAA,CAAA5f,MAAA,CAAmB;UAAA,EAFL,kBAAA0lB,oDAAA1lB,MAAA;YAAA,OAGnB0gB,GAAA,CAAAN,MAAA,CAAApgB,MAAA,CAAc;UAAA,EAHK;UAO3BnD,EAAA,CAAAyD,UAAA,KAAAqlB,oCAAA,kBAoDM;UAGN9oB,EAAA,CAAAyD,UAAA,KAAAslB,oCAAA,kBAsBM;UAGN/oB,EAAA,CAAAyD,UAAA,KAAAulB,oCAAA,kBA0BM;UAGNhpB,EAAA,CAAAyD,UAAA,KAAAwlB,oCAAA,kBAgWM;UACRjpB,EAAA,CAAAI,YAAA,EAAO;UAGPJ,EAAA,CAAAE,cAAA,kBAEC;UAGGF,EAAA,CAAAY,UAAA,sBAAAsoB,wDAAA;YAAA,OAAYrF,GAAA,CAAA1P,WAAA,EAAa;UAAA,EAAC;UAI1BnU,EAAA,CAAAE,cAAA,eAAqC;UAIjCF,EAAA,CAAAY,UAAA,mBAAAuoB,uDAAA;YAAA,OAAStF,GAAA,CAAAnL,iBAAA,EAAmB;UAAA,EAAC;UAgB7B1Y,EAAA,CAAAC,SAAA,aAA4B;UAC9BD,EAAA,CAAAI,YAAA,EAAS;UAGTJ,EAAA,CAAAE,cAAA,kBAiBC;UAfCF,EAAA,CAAAY,UAAA,mBAAAwoB,uDAAA;YAAA,OAASvF,GAAA,CAAA7K,oBAAA,EAAsB;UAAA,EAAC;UAgBhChZ,EAAA,CAAAC,SAAA,aAAgC;UAClCD,EAAA,CAAAI,YAAA,EAAS;UAGTJ,EAAA,CAAAE,cAAA,kBAwBC;UAtBCF,EAAA,CAAAY,UAAA,uBAAAyoB,2DAAAlmB,MAAA;YAAA,OAAa0gB,GAAA,CAAAjC,aAAA,CAAAze,MAAA,CAAqB;UAAA,EAAC,qBAAAmmB,yDAAAnmB,MAAA;YAAA,OACxB0gB,GAAA,CAAApa,WAAA,CAAAtG,MAAA,CAAmB;UAAA,EADK,wBAAAomB,4DAAApmB,MAAA;YAAA,OAErB0gB,GAAA,CAAAva,cAAA,CAAAnG,MAAA,CAAsB;UAAA,EAFD,wBAAAqmB,4DAAArmB,MAAA;YAAA,OAGrB0gB,GAAA,CAAAjC,aAAA,CAAAze,MAAA,CAAqB;UAAA,EAHA,sBAAAsmB,0DAAAtmB,MAAA;YAAA,OAIvB0gB,GAAA,CAAApa,WAAA,CAAAtG,MAAA,CAAmB;UAAA,EAJI,yBAAAumB,6DAAAvmB,MAAA;YAAA,OAKpB0gB,GAAA,CAAAva,cAAA,CAAAnG,MAAA,CAAsB;UAAA,EALF;UAuBnCnD,EAAA,CAAAC,SAAA,SAGK;UAGLD,EAAA,CAAAyD,UAAA,KAAAkmB,oCAAA,kBAYO;UACT3pB,EAAA,CAAAI,YAAA,EAAS;UAIXJ,EAAA,CAAAE,cAAA,eAAyC;UAIrCF,EAAA,CAAAY,UAAA,qBAAAgpB,2DAAAzmB,MAAA;YAAA,OAAW0gB,GAAA,CAAAlH,cAAA,CAAAxZ,MAAA,CAAsB;UAAA,EAAC,mBAAA0mB,yDAAA1mB,MAAA;YAAA,OACzB0gB,GAAA,CAAApH,aAAA,CAAAtZ,MAAA,CAAqB;UAAA,EADI,mBAAA2mB,yDAAA;YAAA,OAEzBjG,GAAA,CAAA/G,YAAA,EAAc;UAAA,EAFW;UAoBnC9c,EAAA,CAAAI,YAAA,EAAW;UAIdJ,EAAA,CAAAE,cAAA,kBA0BC;UACCF,EAAA,CAAAyD,UAAA,KAAAsmB,kCAAA,gBAA4D;UAC5D/pB,EAAA,CAAAyD,UAAA,KAAAumB,oCAAA,kBAUO;UACThqB,EAAA,CAAAI,YAAA,EAAS;UAIXJ,EAAA,CAAAyD,UAAA,KAAAwmB,oCAAA,kBAkDM;UAGNjqB,EAAA,CAAAyD,UAAA,KAAAymB,oCAAA,mBAsJM;UAGNlqB,EAAA,CAAAE,cAAA,qBAOE;UAHAF,EAAA,CAAAY,UAAA,oBAAAupB,uDAAAhnB,MAAA;YAAA,OAAU0gB,GAAA,CAAA5B,cAAA,CAAA9e,MAAA,CAAsB;UAAA,EAAC;UAJnCnD,EAAA,CAAAI,YAAA,EAOE;UAIJJ,EAAA,CAAAyD,UAAA,KAAA2mB,oCAAA,kBAYO;UAGPpqB,EAAA,CAAAyD,UAAA,KAAA4mB,oCAAA,mBAiHM;UACRrqB,EAAA,CAAAI,YAAA,EAAM;;;UAnvCIJ,EAAA,CAAAK,SAAA,IAAqE;UAArEL,EAAA,CAAAkC,UAAA,SAAA2hB,GAAA,CAAArjB,gBAAA,kBAAAqjB,GAAA,CAAArjB,gBAAA,CAAA2B,KAAA,yCAAAnC,EAAA,CAAAoC,aAAA,CAAqE,QAAAyhB,GAAA,CAAArjB,gBAAA,kBAAAqjB,GAAA,CAAArjB,gBAAA,CAAAc,QAAA;UAkBpEtB,EAAA,CAAAK,SAAA,GAAgC;UAAhCL,EAAA,CAAAkC,UAAA,SAAA2hB,GAAA,CAAArjB,gBAAA,kBAAAqjB,GAAA,CAAArjB,gBAAA,CAAAC,QAAA,CAAgC;UA4BjCT,EAAA,CAAAK,SAAA,GACF;UADEL,EAAA,CAAAM,kBAAA,OAAAujB,GAAA,CAAArjB,gBAAA,kBAAAqjB,GAAA,CAAArjB,gBAAA,CAAAc,QAAA,wBACF;UAIKtB,EAAA,CAAAK,SAAA,GAAkB;UAAlBL,EAAA,CAAAkC,UAAA,SAAA2hB,GAAA,CAAAtW,YAAA,CAAkB;UAmCdvN,EAAA,CAAAK,SAAA,GAAmB;UAAnBL,EAAA,CAAAkC,UAAA,UAAA2hB,GAAA,CAAAtW,YAAA,CAAmB;UA2E5BvN,EAAA,CAAAK,SAAA,GAA2D;UAA3DL,EAAA,CAAAqC,WAAA,eAAAwhB,GAAA,CAAA5Y,UAAA,6BAA2D,UAAA4Y,GAAA,CAAA5Y,UAAA;UAoB3DjL,EAAA,CAAAK,SAAA,GAA6D;UAA7DL,EAAA,CAAAqC,WAAA,eAAAwhB,GAAA,CAAAziB,YAAA,6BAA6D,UAAAyiB,GAAA,CAAAziB,YAAA;UAU9DpB,EAAA,CAAAK,SAAA,GAAkB;UAAlBL,EAAA,CAAAkC,UAAA,SAAA2hB,GAAA,CAAAziB,YAAA,CAAkB;UAwFrBpB,EAAA,CAAAK,SAAA,GAA0E;UAA1EL,EAAA,CAAAqC,WAAA,eAAAwhB,GAAA,CAAA/X,UAAA,4CAA0E;UAIvE9L,EAAA,CAAAK,SAAA,GAAgB;UAAhBL,EAAA,CAAAkC,UAAA,SAAA2hB,GAAA,CAAA/X,UAAA,CAAgB;UAuDhB9L,EAAA,CAAAK,SAAA,GAAe;UAAfL,EAAA,CAAAkC,UAAA,SAAA2hB,GAAA,CAAApZ,SAAA,CAAe;UAyBfzK,EAAA,CAAAK,SAAA,GAAyC;UAAzCL,EAAA,CAAAkC,UAAA,UAAA2hB,GAAA,CAAApZ,SAAA,IAAAoZ,GAAA,CAAAxc,QAAA,CAAAuJ,MAAA,OAAyC;UA6BzC5Q,EAAA,CAAAK,SAAA,GAAuC;UAAvCL,EAAA,CAAAkC,UAAA,UAAA2hB,GAAA,CAAApZ,SAAA,IAAAoZ,GAAA,CAAAxc,QAAA,CAAAuJ,MAAA,KAAuC;UAuWxC5Q,EAAA,CAAAK,SAAA,GAAyB;UAAzBL,EAAA,CAAAkC,UAAA,cAAA2hB,GAAA,CAAAnW,WAAA,CAAyB;UAmBrB1N,EAAA,CAAAK,SAAA,GAAgE;UAAhEL,EAAA,CAAAqC,WAAA,eAAAwhB,GAAA,CAAAjZ,eAAA,6BAAgE,UAAAiZ,GAAA,CAAAjZ,eAAA;UAsBhE5K,EAAA,CAAAK,SAAA,GAAmE;UAAnEL,EAAA,CAAAqC,WAAA,eAAAwhB,GAAA,CAAAhZ,kBAAA,6BAAmE,UAAAgZ,GAAA,CAAAhZ,kBAAA;UA4BnE7K,EAAA,CAAAK,SAAA,GAAiE;UAAjEL,EAAA,CAAAqC,WAAA,eAAAwhB,GAAA,CAAA9X,gBAAA,6BAAiE,UAAA8X,GAAA,CAAA9X,gBAAA,uCAAA8X,GAAA,CAAA9X,gBAAA;UAQ/D/L,EAAA,CAAAK,SAAA,GAAgE;UAAhEL,EAAA,CAAA6E,UAAA,CAAAgf,GAAA,CAAA9X,gBAAA,uCAAgE;UAChE/L,EAAA,CAAAqC,WAAA,cAAAwhB,GAAA,CAAA9X,gBAAA,gCAAmE;UAKlE/L,EAAA,CAAAK,SAAA,GAAsB;UAAtBL,EAAA,CAAAkC,UAAA,SAAA2hB,GAAA,CAAA9X,gBAAA,CAAsB;UAuCzB/L,EAAA,CAAAK,SAAA,GAA8B;UAA9BL,EAAA,CAAAkC,UAAA,aAAA2hB,GAAA,CAAA/V,eAAA,GAA8B;UAsBhC9N,EAAA,CAAAK,SAAA,GAEC;UAFDL,EAAA,CAAAqC,WAAA,gBAAAwhB,GAAA,CAAAnW,WAAA,CAAA0G,KAAA,IAAAyP,GAAA,CAAA3Y,gBAAA,yBAEC,YAAA2Y,GAAA,CAAAnW,WAAA,CAAA0G,KAAA,IAAAyP,GAAA,CAAA3Y,gBAAA;UAjBDlL,EAAA,CAAAkC,UAAA,cAAA2hB,GAAA,CAAAnW,WAAA,CAAA0G,KAAA,IAAAyP,GAAA,CAAA3Y,gBAAA,CAAmD;UAyBpBlL,EAAA,CAAAK,SAAA,GAAuB;UAAvBL,EAAA,CAAAkC,UAAA,UAAA2hB,GAAA,CAAA3Y,gBAAA,CAAuB;UAEnDlL,EAAA,CAAAK,SAAA,GAAsB;UAAtBL,EAAA,CAAAkC,UAAA,SAAA2hB,GAAA,CAAA3Y,gBAAA,CAAsB;UAe1BlL,EAAA,CAAAK,SAAA,GAAqB;UAArBL,EAAA,CAAAkC,UAAA,SAAA2hB,GAAA,CAAAjZ,eAAA,CAAqB;UAqDrB5K,EAAA,CAAAK,SAAA,GAAwB;UAAxBL,EAAA,CAAAkC,UAAA,SAAA2hB,GAAA,CAAAhZ,kBAAA,CAAwB;UA6JzB7K,EAAA,CAAAK,SAAA,GAA+B;UAA/BL,EAAA,CAAAkC,UAAA,WAAA2hB,GAAA,CAAAhB,kBAAA,GAA+B;UAOhC7iB,EAAA,CAAAK,SAAA,GAA2D;UAA3DL,EAAA,CAAAkC,UAAA,SAAA2hB,GAAA,CAAAjZ,eAAA,IAAAiZ,GAAA,CAAAhZ,kBAAA,IAAAgZ,GAAA,CAAAziB,YAAA,CAA2D;UAe3DpB,EAAA,CAAAK,SAAA,GAAsB;UAAtBL,EAAA,CAAAkC,UAAA,SAAA2hB,GAAA,CAAA9X,gBAAA,CAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}