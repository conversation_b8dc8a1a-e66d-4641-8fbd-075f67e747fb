"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[784],{8397:(w,b,u)=>{u.d(b,{f:()=>_});var f=u(4412),p=u(7705);let _=(()=>{class l{constructor(){this.toastsSubject=new f.t([]),this.toasts$=this.toastsSubject.asObservable(),this.currentId=0}generateId(){return Math.random().toString(36).substr(2,9)}addToast(c){const t={...c,id:this.generateId(),duration:c.duration||5e3};this.toastsSubject.next([...this.toastsSubject.value,t]),t.duration&&t.duration>0&&setTimeout(()=>{this.removeToast(t.id)},t.duration)}show(c,t="info",g=5e3){const C=this.generateId();this.toastsSubject.next([...this.toastsSubject.value,{id:C,type:t,title:"",message:c,duration:g}]),g>0&&setTimeout(()=>this.dismiss(C),g)}showSuccess(c,t=3e3){this.show(c,"success",t)}showError(c,t=5e3){this.show(c,"error",t)}showWarning(c,t=4e3){this.show(c,"warning",t)}showInfo(c,t=3e3){this.show(c,"info",t)}dismiss(c){const t=this.toastsSubject.value.filter(g=>g.id!==c);this.toastsSubject.next(t)}success(c,t,g){this.addToast({type:"success",title:c,message:t,duration:g,icon:"check-circle"})}error(c,t,g,C){this.addToast({type:"error",title:c,message:t,duration:g||8e3,icon:"x-circle",action:C})}warning(c,t,g){this.addToast({type:"warning",title:c,message:t,duration:g,icon:"exclamation-triangle"})}accessDenied(c="effectuer cette action",t){this.error("Acc\xe8s refus\xe9",`Vous n'avez pas les permissions n\xe9cessaires pour ${c}${t?` (Code: ${t})`:""}`,8e3,{label:"Comprendre les r\xf4les",handler:()=>{console.log("Redirection vers l'aide sur les r\xf4les")}})}ownershipRequired(c="cette ressource"){this.error("Propri\xe9taire requis",`Seul le propri\xe9taire ou un administrateur peut modifier ${c}`,8e3)}removeToast(c){this.toastsSubject.next(this.toastsSubject.value.filter(g=>g.id!==c))}clear(){this.toastsSubject.next([])}static{this.\u0275fac=function(t){return new(t||l)}}static{this.\u0275prov=p.jDH({token:l,factory:l.\u0275fac,providedIn:"root"})}}return l})()},784:(w,b,u)=>{u.r(b),u.d(b,{MessagesModule:()=>Kt});var f=u(177),p=u(6647),_=u(467),l=u(4341),v=u(8359),c=u(5293),t=u(7705),g=u(7455),C=u(9454),M=u(8397);const O=["messagesContainer"],T=["fileInput"],R=["localVideo"],V=["remoteVideo"],D=["localVideoHidden"],F=["remoteVideoHidden"];function U(r,a){1&r&&t.nrm(0,"div",58)}function A(r,a){1&r&&(t.j41(0,"div",59)(1,"span"),t.EFF(2,"En train d'\xe9crire"),t.k0s(),t.j41(3,"div",60),t.nrm(4,"div",61)(5,"div",62)(6,"div",63),t.k0s()())}function $(r,a){if(1&r&&(t.j41(0,"span"),t.EFF(1),t.k0s()),2&r){const e=t.XpG();t.R7$(1),t.SpI(" ",null!=e.otherParticipant&&e.otherParticipant.isOnline?"En ligne":e.formatLastActive(null==e.otherParticipant?null:e.otherParticipant.lastActive)," ")}}function G(r,a){if(1&r){const e=t.RV6();t.j41(0,"div",64)(1,"div",65)(2,"button",66),t.bIt("click",function(){t.eBV(e);const n=t.XpG();return n.toggleSearch(),t.Njj(n.showMainMenu=!1)}),t.nrm(3,"i",67),t.j41(4,"span",68),t.EFF(5,"Rechercher"),t.k0s()(),t.j41(6,"button",69),t.nrm(7,"i",70),t.j41(8,"span",68),t.EFF(9,"Voir le profil"),t.k0s()(),t.nrm(10,"hr",71),t.j41(11,"button",69),t.nrm(12,"i",72),t.j41(13,"span",68),t.EFF(14,"Param\xe8tres"),t.k0s()()()()}}function z(r,a){1&r&&(t.j41(0,"div",73)(1,"div",74),t.nrm(2,"i",75),t.j41(3,"p",76),t.EFF(4," D\xe9posez vos fichiers ici "),t.k0s(),t.j41(5,"p",77),t.EFF(6," Images, vid\xe9os, documents... "),t.k0s()()())}function N(r,a){1&r&&(t.j41(0,"div",78),t.nrm(1,"div",79),t.j41(2,"span",80),t.EFF(3,"Chargement des messages..."),t.k0s()())}function L(r,a){if(1&r&&(t.j41(0,"div",81)(1,"div",82),t.nrm(2,"i",83),t.k0s(),t.j41(3,"h3",84),t.EFF(4," Aucun message "),t.k0s(),t.j41(5,"p",85),t.EFF(6),t.k0s()()),2&r){const e=t.XpG();t.R7$(6),t.SpI(" Commencez votre conversation avec ",null==e.otherParticipant?null:e.otherParticipant.username," ")}}function B(r,a){if(1&r&&(t.j41(0,"div",99)(1,"div",100)(2,"span",101),t.EFF(3),t.k0s()()()),2&r){const e=t.XpG().$implicit,i=t.XpG(2);t.R7$(3),t.SpI(" ",i.formatDateSeparator(e.timestamp)," ")}}function Y(r,a){if(1&r){const e=t.RV6();t.j41(0,"div",102)(1,"img",103),t.bIt("click",function(){t.eBV(e);const n=t.XpG().$implicit,o=t.XpG(2);return t.Njj(o.openUserProfile(null==n.sender?null:n.sender.id))}),t.k0s()()}if(2&r){const e=t.XpG().$implicit;t.R7$(1),t.Y8G("src",(null==e.sender?null:e.sender.image)||"assets/images/default-avatar.png",t.B4B)("alt",null==e.sender?null:e.sender.username)}}function X(r,a){if(1&r&&(t.j41(0,"div",104),t.EFF(1),t.k0s()),2&r){const e=t.XpG().$implicit,i=t.XpG(2);t.xc7("color",i.getUserColor(null==e.sender?null:e.sender.id)),t.R7$(1),t.SpI(" ",null==e.sender?null:e.sender.username," ")}}function W(r,a){if(1&r&&(t.j41(0,"div",105),t.nrm(1,"div",106),t.k0s()),2&r){const e=t.XpG().$implicit,i=t.XpG(2);t.R7$(1),t.Y8G("innerHTML",i.formatMessageContent(e.content),t.npT)}}function H(r,a){if(1&r&&t.nrm(0,"div",110),2&r){const e=t.XpG(2).$implicit,i=t.XpG(2);t.xc7("color",(null==e.sender?null:e.sender.id)===i.currentUserId?"#ffffff":"#111827"),t.Y8G("innerHTML",i.formatMessageContent(e.content),t.npT)}}function Q(r,a){if(1&r){const e=t.RV6();t.j41(0,"div",107)(1,"img",108),t.bIt("click",function(){t.eBV(e);const n=t.XpG().$implicit,o=t.XpG(2);return t.Njj(o.openImageViewer(n))})("load",function(n){t.eBV(e);const o=t.XpG().$implicit,s=t.XpG(2);return t.Njj(s.onImageLoad(n,o))})("error",function(n){t.eBV(e);const o=t.XpG().$implicit,s=t.XpG(2);return t.Njj(s.onImageError(n,o))}),t.k0s(),t.DNE(2,H,1,3,"div",109),t.k0s()}if(2&r){const e=t.XpG().$implicit,i=t.XpG(2);t.R7$(1),t.Y8G("src",i.getImageUrl(e),t.B4B)("alt",e.content||"Image"),t.R7$(1),t.Y8G("ngIf",e.content)}}function Z(r,a){if(1&r&&t.nrm(0,"div",119),2&r){const e=a.$implicit,i=a.index,n=t.XpG(2).$implicit,o=t.XpG(2);t.xc7("height",o.isVoicePlaying(n.id)?e:8,"px")("animation",o.isVoicePlaying(n.id)?"pulse 1s infinite":"none")("animation-delay",.1*i+"s")}}function J(r,a){if(1&r){const e=t.RV6();t.j41(0,"button",120),t.bIt("click",function(){t.eBV(e);const n=t.XpG(2).$implicit,o=t.XpG(2);return t.Njj(o.changeVoiceSpeed(n))}),t.EFF(1),t.k0s()}if(2&r){const e=t.XpG(2).$implicit,i=t.XpG(2);t.R7$(1),t.SpI(" ",i.getVoiceSpeed(e),"x ")}}function K(r,a){if(1&r){const e=t.RV6();t.j41(0,"div",111)(1,"button",112),t.bIt("click",function(){t.eBV(e);const n=t.XpG().$implicit,o=t.XpG(2);return t.Njj(o.toggleVoicePlayback(n))}),t.nrm(2,"i",113),t.k0s(),t.j41(3,"div",114),t.DNE(4,Z,1,6,"div",115),t.k0s(),t.j41(5,"div",116)(6,"div",117),t.EFF(7),t.k0s(),t.DNE(8,J,2,1,"button",118),t.k0s()()}if(2&r){const e=t.XpG().$implicit,i=t.XpG(2);t.R7$(2),t.HbH(i.isVoicePlaying(e.id)?"fas fa-pause":"fas fa-play"),t.R7$(2),t.Y8G("ngForOf",i.voiceWaves),t.R7$(3),t.SpI(" ",i.getVoiceDuration(e)," "),t.R7$(1),t.Y8G("ngIf",i.isVoicePlaying(e.id))}}function q(r,a){1&r&&t.nrm(0,"i",126)}function tt(r,a){1&r&&t.nrm(0,"i",127)}function et(r,a){1&r&&t.nrm(0,"i",128)}function nt(r,a){1&r&&t.nrm(0,"i",129)}function it(r,a){if(1&r&&(t.j41(0,"div",121),t.DNE(1,q,1,0,"i",122),t.DNE(2,tt,1,0,"i",123),t.DNE(3,et,1,0,"i",124),t.DNE(4,nt,1,0,"i",125),t.k0s()),2&r){const e=t.XpG().$implicit;t.R7$(1),t.Y8G("ngIf","SENDING"===e.status),t.R7$(1),t.Y8G("ngIf","SENT"===e.status),t.R7$(1),t.Y8G("ngIf","DELIVERED"===e.status),t.R7$(1),t.Y8G("ngIf","READ"===e.status)}}function ot(r,a){if(1&r){const e=t.RV6();t.qex(0),t.DNE(1,B,4,1,"div",89),t.j41(2,"div",90),t.bIt("click",function(n){const s=t.eBV(e).$implicit,d=t.XpG(2);return t.Njj(d.onMessageClick(s,n))})("contextmenu",function(n){const s=t.eBV(e).$implicit,d=t.XpG(2);return t.Njj(d.onMessageContextMenu(s,n))}),t.DNE(3,Y,2,2,"div",91),t.j41(4,"div",92),t.DNE(5,X,2,3,"div",93),t.DNE(6,W,2,1,"div",94),t.DNE(7,Q,3,3,"div",95),t.DNE(8,K,9,5,"div",96),t.j41(9,"div",97)(10,"span"),t.EFF(11),t.k0s(),t.DNE(12,it,5,4,"div",98),t.k0s()()(),t.bVm()}if(2&r){const e=a.$implicit,i=a.index,n=t.XpG(2);t.R7$(1),t.Y8G("ngIf",n.shouldShowDateSeparator(i)),t.R7$(1),t.xc7("justify-content",(null==e.sender?null:e.sender.id)===n.currentUserId?"flex-end":"flex-start"),t.Y8G("id","message-"+e.id),t.R7$(1),t.Y8G("ngIf",(null==e.sender?null:e.sender.id)!==n.currentUserId&&n.shouldShowAvatar(i)),t.R7$(1),t.xc7("background-color",(null==e.sender?null:e.sender.id)===n.currentUserId?"#3b82f6":"#ffffff")("color",(null==e.sender?null:e.sender.id)===n.currentUserId?"#ffffff":"#111827"),t.R7$(1),t.Y8G("ngIf",n.isGroupConversation()&&(null==e.sender?null:e.sender.id)!==n.currentUserId&&n.shouldShowSenderName(i)),t.R7$(1),t.Y8G("ngIf","text"===n.getMessageType(e)),t.R7$(1),t.Y8G("ngIf",n.hasImage(e)),t.R7$(1),t.Y8G("ngIf","audio"===n.getMessageType(e)),t.R7$(3),t.JRh(n.formatMessageTime(e.timestamp)),t.R7$(1),t.Y8G("ngIf",(null==e.sender?null:e.sender.id)===n.currentUserId)}}function rt(r,a){if(1&r&&(t.j41(0,"div",130),t.nrm(1,"img",131),t.j41(2,"div",132)(3,"div",133),t.nrm(4,"div",134)(5,"div",135)(6,"div",136),t.k0s()()()),2&r){const e=t.XpG(2);t.R7$(1),t.Y8G("src",(null==e.otherParticipant?null:e.otherParticipant.image)||"assets/images/default-avatar.png",t.B4B)("alt",null==e.otherParticipant?null:e.otherParticipant.username)}}function st(r,a){if(1&r&&(t.j41(0,"div",86),t.DNE(1,ot,13,15,"ng-container",87),t.DNE(2,rt,7,2,"div",88),t.k0s()),2&r){const e=t.XpG();t.R7$(1),t.Y8G("ngForOf",e.messages)("ngForTrackBy",e.trackByMessageId),t.R7$(1),t.Y8G("ngIf",e.otherUserIsTyping)}}function at(r,a){1&r&&t.nrm(0,"div",137)}function ct(r,a){1&r&&t.nrm(0,"i",138)}function lt(r,a){1&r&&t.nrm(0,"div",139)}function dt(r,a){if(1&r){const e=t.RV6();t.j41(0,"button",145),t.bIt("click",function(){const o=t.eBV(e).$implicit,s=t.XpG(2);return t.Njj(s.insertEmoji(o))}),t.EFF(1),t.k0s()}if(2&r){const e=a.$implicit;t.Y8G("title",e.name),t.R7$(1),t.SpI(" ",e.emoji," ")}}function ut(r,a){if(1&r&&(t.j41(0,"div",140)(1,"div",141)(2,"h4",142),t.EFF(3," \xc9mojis "),t.k0s(),t.j41(4,"div",143),t.DNE(5,dt,2,2,"button",144),t.k0s()()()),2&r){const e=t.XpG();t.R7$(5),t.Y8G("ngForOf",e.getEmojisForCategory(e.selectedEmojiCategory))}}function gt(r,a){if(1&r){const e=t.RV6();t.j41(0,"div",146)(1,"div",141)(2,"h4",142),t.EFF(3," Pi\xe8ces jointes "),t.k0s(),t.j41(4,"div",147)(5,"button",148),t.bIt("click",function(){t.eBV(e);const n=t.XpG();return t.Njj(n.triggerFileInput("image"))}),t.j41(6,"div",149),t.nrm(7,"i",150),t.k0s(),t.j41(8,"span",151),t.EFF(9,"Images"),t.k0s()(),t.j41(10,"button",148),t.bIt("click",function(){t.eBV(e);const n=t.XpG();return t.Njj(n.triggerFileInput("document"))}),t.j41(11,"div",152),t.nrm(12,"i",153),t.k0s(),t.j41(13,"span",151),t.EFF(14,"Documents"),t.k0s()(),t.j41(15,"button",148),t.bIt("click",function(){t.eBV(e);const n=t.XpG();return t.Njj(n.openCamera())}),t.j41(16,"div",154),t.nrm(17,"i",155),t.k0s(),t.j41(18,"span",151),t.EFF(19,"Cam\xe9ra"),t.k0s()()()()()}}function ht(r,a){if(1&r){const e=t.RV6();t.j41(0,"div",156),t.bIt("click",function(){t.eBV(e);const n=t.XpG();return t.Njj(n.closeAllMenus())}),t.k0s()}}function ft(r,a){if(1&r&&t.nrm(0,"div",169),2&r){const i=a.index;t.xc7("height",a.$implicit,"px")("animation","bounce 1s infinite")("animation-delay",.1*i+"s")}}function pt(r,a){if(1&r){const e=t.RV6();t.j41(0,"div",157)(1,"div",158),t.nrm(2,"i",159),t.k0s(),t.j41(3,"div",160)(4,"div",161),t.EFF(5),t.k0s(),t.j41(6,"div",162),t.DNE(7,ft,1,6,"div",163),t.k0s(),t.j41(8,"div",164),t.EFF(9),t.k0s()(),t.j41(10,"div",37)(11,"button",165),t.bIt("click",function(n){t.eBV(e);const o=t.XpG();return t.Njj(o.onRecordCancel(n))}),t.nrm(12,"i",166),t.k0s(),t.j41(13,"button",167),t.bIt("click",function(n){t.eBV(e);const o=t.XpG();return t.Njj(o.onRecordEnd(n))}),t.nrm(14,"i",168),t.k0s()()()}if(2&r){const e=t.XpG();t.R7$(5),t.SpI(" ",e.formatRecordingDuration(e.voiceRecordingDuration)," "),t.R7$(2),t.Y8G("ngForOf",e.voiceWaves),t.R7$(2),t.SpI(" Format: ",e.getRecordingFormat()," ")}}function mt(r,a){if(1&r&&(t.j41(0,"div",182)(1,"div",183),t.EFF(2," \u{1f464} "),t.k0s(),t.j41(3,"h3"),t.EFF(4),t.k0s(),t.j41(5,"p",184),t.EFF(6,"En attente de connexion..."),t.k0s()()),2&r){const e=t.XpG(2);t.R7$(4),t.JRh(null==e.otherParticipant?null:e.otherParticipant.username)}}function _t(r,a){if(1&r){const e=t.RV6();t.j41(0,"div",170)(1,"div",171)(2,"h3",172),t.EFF(3),t.k0s(),t.j41(4,"p",173),t.EFF(5),t.k0s()(),t.j41(6,"div",174),t.nrm(7,"video",175,3)(9,"video",176,1),t.DNE(11,mt,7,1,"div",177),t.k0s(),t.j41(12,"div",178)(13,"button",179),t.bIt("click",function(){t.eBV(e);const n=t.XpG();return t.Njj(n.toggleMicrophone())}),t.nrm(14,"i"),t.k0s(),t.j41(15,"button",179),t.bIt("click",function(){t.eBV(e);const n=t.XpG();return t.Njj(n.toggleCamera())}),t.nrm(16,"i"),t.k0s(),t.j41(17,"button",180),t.bIt("click",function(){t.eBV(e);const n=t.XpG();return t.Njj(n.endCall())}),t.nrm(18,"i",181),t.k0s()()()}if(2&r){const e=t.XpG();t.R7$(3),t.SpI(" \u{1f4f9} Appel vid\xe9o avec ",null==e.otherParticipant?null:e.otherParticipant.username," "),t.R7$(2),t.SpI(" ","connected"===e.activeCall.status?"Connect\xe9":"Connexion..."," "),t.R7$(6),t.Y8G("ngIf",!e.activeCall||"connected"!==e.activeCall.status),t.R7$(2),t.xc7("background",e.isMuted?"#ff4757":"#2ed573"),t.FS9("title",e.isMuted?"Activer le micro":"Couper le micro"),t.R7$(1),t.ZvI("fas ",e.isMuted?"fa-microphone-slash":"fa-microphone",""),t.R7$(1),t.xc7("background",e.isVideoEnabled?"#2ed573":"#ff4757"),t.FS9("title",e.isVideoEnabled?"D\xe9sactiver la cam\xe9ra":"Activer la cam\xe9ra"),t.R7$(1),t.ZvI("fas ",e.isVideoEnabled?"fa-video":"fa-video-slash","")}}function bt(r,a){1&r&&t.nrm(0,"video",185,186)}function Ct(r,a){1&r&&t.nrm(0,"video",187,188)}let I=(()=>{class r{constructor(e,i,n,o,s,d,h){this.fb=e,this.route=i,this.router=n,this.MessageService=o,this.callService=s,this.toastService=d,this.cdr=h,this.conversation=null,this.messages=[],this.currentUserId=null,this.currentUsername="You",this.otherParticipant=null,this.isLoading=!1,this.isLoadingMore=!1,this.hasMoreMessages=!0,this.showEmojiPicker=!1,this.showAttachmentMenu=!1,this.showSearch=!1,this.searchQuery="",this.searchResults=[],this.searchMode=!1,this.isSendingMessage=!1,this.otherUserIsTyping=!1,this.showMainMenu=!1,this.showMessageContextMenu=!1,this.selectedMessage=null,this.contextMenuPosition={x:0,y:0},this.showReactionPicker=!1,this.reactionPickerMessage=null,this.showImageViewer=!1,this.selectedImage=null,this.uploadProgress=0,this.isUploading=!1,this.isDragOver=!1,this.isRecordingVoice=!1,this.voiceRecordingDuration=0,this.voiceRecordingState="idle",this.mediaRecorder=null,this.audioChunks=[],this.recordingTimer=null,this.voiceWaves=[4,8,12,16,20,16,12,8,4,8,12,16,20,16,12,8],this.currentAudio=null,this.playingMessageId=null,this.voicePlayback={},this.isInCall=!1,this.callType=null,this.callDuration=0,this.callTimer=null,this.activeCall=null,this.isCallConnected=!1,this.isMuted=!1,this.isVideoEnabled=!0,this.localVideoElement=null,this.remoteVideoElement=null,this.emojiCategories=[{id:"smileys",name:"Smileys",icon:"\u{1f600}",emojis:[{emoji:"\u{1f600}",name:"grinning face"},{emoji:"\u{1f603}",name:"grinning face with big eyes"},{emoji:"\u{1f604}",name:"grinning face with smiling eyes"},{emoji:"\u{1f601}",name:"beaming face with smiling eyes"},{emoji:"\u{1f606}",name:"grinning squinting face"},{emoji:"\u{1f605}",name:"grinning face with sweat"},{emoji:"\u{1f602}",name:"face with tears of joy"},{emoji:"\u{1f923}",name:"rolling on the floor laughing"},{emoji:"\u{1f60a}",name:"smiling face with smiling eyes"},{emoji:"\u{1f607}",name:"smiling face with halo"}]},{id:"people",name:"People",icon:"\u{1f464}",emojis:[{emoji:"\u{1f476}",name:"baby"},{emoji:"\u{1f9d2}",name:"child"},{emoji:"\u{1f466}",name:"boy"},{emoji:"\u{1f467}",name:"girl"},{emoji:"\u{1f9d1}",name:"person"},{emoji:"\u{1f468}",name:"man"},{emoji:"\u{1f469}",name:"woman"},{emoji:"\u{1f474}",name:"old man"},{emoji:"\u{1f475}",name:"old woman"}]},{id:"nature",name:"Nature",icon:"\u{1f33f}",emojis:[{emoji:"\u{1f436}",name:"dog face"},{emoji:"\u{1f431}",name:"cat face"},{emoji:"\u{1f42d}",name:"mouse face"},{emoji:"\u{1f439}",name:"hamster"},{emoji:"\u{1f430}",name:"rabbit face"},{emoji:"\u{1f98a}",name:"fox"},{emoji:"\u{1f43b}",name:"bear"},{emoji:"\u{1f43c}",name:"panda"}]}],this.selectedEmojiCategory=this.emojiCategories[0],this.MAX_MESSAGES_TO_LOAD=10,this.currentPage=1,this.isTyping=!1,this.isUserTyping=!1,this.typingTimeout=null,this.subscriptions=new v.yU,this.messageForm=this.fb.group({content:["",[l.k0.required,l.k0.minLength(1)]]})}isInputDisabled(){return!this.otherParticipant||this.isRecordingVoice||this.isSendingMessage}updateInputState(){const e=this.messageForm.get("content");this.isInputDisabled()?e?.disable():e?.enable()}ngOnInit(){console.log("\u{1f680} MessageChatComponent initialized"),this.initializeComponent(),this.enableSoundsOnFirstInteraction()}ngAfterViewInit(){setTimeout(()=>{this.setupVideoElements()},100),setTimeout(()=>{this.setupVideoElements()},500),setTimeout(()=>{this.setupVideoElements()},1e3),setTimeout(()=>{this.setupVideoElements()},2e3)}setupVideoElements(){console.log("\u{1f50d} [MessageChat] Checking video elements:",{localVideo:!!this.localVideo,remoteVideo:!!this.remoteVideo,localVideoHidden:!!this.localVideoHidden,remoteVideoHidden:!!this.remoteVideoHidden}),this.localVideo&&this.remoteVideo?(console.log("\u{1f3a5} [MessageChat] Setting up visible video elements for WebRTC"),this.callService.setVideoElements(this.localVideo.nativeElement,this.remoteVideo.nativeElement),console.log("\u2705 [MessageChat] Visible video elements configured successfully")):this.localVideoHidden&&this.remoteVideoHidden?(console.log("\u{1f3a5} [MessageChat] Setting up hidden video elements for audio calls"),this.callService.setVideoElements(this.localVideoHidden.nativeElement,this.remoteVideoHidden.nativeElement),console.log("\u2705 [MessageChat] Hidden video elements configured successfully")):(console.warn("\u26a0\ufe0f [MessageChat] No video elements found, creating manually..."),this.createVideoElementsManually(),setTimeout(()=>{this.setupVideoElements()},500),setTimeout(()=>{this.setupVideoElements()},1500))}createVideoElementsManually(){console.log("\u{1f527} [MessageChat] Creating video elements manually...");const e=document.getElementById("localVideo"),i=document.getElementById("remoteVideo");if(e&&i)console.log("\u2705 [MessageChat] Found video elements in DOM, configuring..."),this.callService.setVideoElements(e,i);else{console.warn("\u26a0\ufe0f [MessageChat] Video elements not found in DOM either");const n=document.createElement("video");n.id="localVideo",n.autoplay=!0,n.muted=!0,n.playsInline=!0,n.style.cssText="position: absolute; top: -9999px; left: -9999px; width: 1px; height: 1px;";const o=document.createElement("video");o.id="remoteVideo",o.autoplay=!0,o.playsInline=!0,o.style.cssText="position: absolute; top: -9999px; left: -9999px; width: 1px; height: 1px;",document.body.appendChild(n),document.body.appendChild(o),console.log("\u2705 [MessageChat] Created video elements dynamically"),this.callService.setVideoElements(n,o)}}enableSoundsOnFirstInteraction(){const e=()=>{this.callService.enableSounds(),document.removeEventListener("click",e),document.removeEventListener("keydown",e),document.removeEventListener("touchstart",e)};document.addEventListener("click",e,{once:!0}),document.addEventListener("keydown",e,{once:!0}),document.addEventListener("touchstart",e,{once:!0})}initializeComponent(){this.loadCurrentUser(),this.loadConversation(),this.setupCallSubscriptions()}setupCallSubscriptions(){this.subscriptions.add(this.callService.incomingCall$.subscribe({next:e=>{e&&(console.log("\u{1f4de} Incoming call received:",e),this.handleIncomingCall(e))},error:e=>{console.error("\u274c Error in incoming call subscription:",e)}})),this.subscriptions.add(this.callService.activeCall$.subscribe({next:e=>{e&&(console.log("\u{1f4de} Active call updated:",e),this.activeCall=e)},error:e=>{console.error("\u274c Error in active call subscription:",e)}}))}handleIncomingCall(e){console.log("\u{1f514} Handling incoming call from:",e.caller.username),this.MessageService.play("ringtone")}loadCurrentUser(){try{const e=localStorage.getItem("user");if(console.log("\u{1f50d} Raw user from localStorage:",e),!e||"null"===e||"undefined"===e)return console.error("\u274c No user data in localStorage"),this.currentUserId=null,void(this.currentUsername="You");const i=JSON.parse(e);console.log("\u{1f50d} Parsed user object:",i);const n=i._id||i.id||i.userId;console.log("\u{1f50d} Trying to extract user ID:",{_id:i._id,id:i.id,userId:i.userId,extracted:n}),n?(this.currentUserId=n,this.currentUsername=i.username||i.name||"You",console.log("\u2705 Current user loaded successfully:",{id:this.currentUserId,username:this.currentUsername})):(console.error("\u274c No valid user ID found in user object:",i),this.currentUserId=null,this.currentUsername="You")}catch(e){console.error("\u274c Error parsing user from localStorage:",e),this.currentUserId=null,this.currentUsername="You"}}loadConversation(){const e=this.route.snapshot.paramMap.get("id");console.log("Loading conversation with ID:",e),e?(this.isLoading=!0,this.cleanupSubscriptions(),this.MessageService.getConversation(e).subscribe({next:i=>{console.log("\u{1f50d} Conversation loaded successfully:",i),console.log("\u{1f50d} Conversation structure:",{id:i?.id,participants:i?.participants,participantsCount:i?.participants?.length,isGroup:i?.isGroup,messages:i?.messages,messagesCount:i?.messages?.length}),this.conversation=i,this.setOtherParticipant(),this.loadMessages(),this.setupSubscriptions(),this.isLoading=!1},error:i=>{console.error("Erreur lors du chargement de la conversation:",i),this.toastService.showError("Erreur lors du chargement de la conversation"),this.isLoading=!1,setTimeout(()=>{console.log("\u{1f504} Retrying conversation load..."),this.loadConversation()},2e3)}})):this.toastService.showError("ID de conversation manquant")}setOtherParticipant(){if(!this.conversation?.participants||0===this.conversation.participants.length)return console.warn("No participants found in conversation"),void(this.otherParticipant=null);console.log("Setting other participant..."),console.log("Current user ID:",this.currentUserId),console.log("All participants:",this.conversation.participants),this.otherParticipant=this.conversation.participants.find(this.conversation.isGroup?e=>String(e.id||e._id)!==String(this.currentUserId):e=>{const i=e.id||e._id;return console.log("Comparing participant ID:",i,"with current user ID:",this.currentUserId),String(i)!==String(this.currentUserId)}),!this.otherParticipant&&this.conversation.participants.length>0&&(console.log("Fallback: using first participant"),this.otherParticipant=this.conversation.participants[0],this.conversation.participants.length>1)&&String(this.otherParticipant.id||this.otherParticipant._id)===String(this.currentUserId)&&(console.log("First participant is current user, using second participant"),this.otherParticipant=this.conversation.participants[1]),this.otherParticipant?(console.log("\u2705 Other participant set successfully:",{id:this.otherParticipant.id||this.otherParticipant._id,username:this.otherParticipant.username,image:this.otherParticipant.image,isOnline:this.otherParticipant.isOnline}),console.log("\u{1f3af} FINAL RESULT: otherParticipant =",this.otherParticipant.username),console.log("\u{1f3af} Should display in sidebar:",this.otherParticipant.username)):(console.error("\u274c No other participant found! This should not happen."),console.log("Conversation participants:",this.conversation.participants),console.log("Current user ID:",this.currentUserId),console.log("\u{1f6a8} ERROR: No otherParticipant found!")),this.updateInputState()}loadMessages(){this.conversation?.id&&(this.messages=(this.conversation.messages||[]).sort((i,n)=>new Date(i.timestamp||i.createdAt).getTime()-new Date(n.timestamp||n.createdAt).getTime()),console.log("\u{1f4cb} Messages loaded and sorted:",{total:this.messages.length,first:this.messages[0]?.content,last:this.messages[this.messages.length-1]?.content}),this.hasMoreMessages=this.messages.length===this.MAX_MESSAGES_TO_LOAD,this.isLoading=!1,this.scrollToBottom())}loadMoreMessages(){!this.isLoadingMore&&this.hasMoreMessages&&this.conversation?.id&&(this.isLoadingMore=!0,this.currentPage++,this.MessageService.getMessages(this.currentUserId,this.otherParticipant?.id||this.otherParticipant?._id,this.conversation.id,this.currentPage,this.MAX_MESSAGES_TO_LOAD).subscribe({next:i=>{i&&i.length>0?(this.messages=[...i.reverse(),...this.messages],this.hasMoreMessages=i.length===this.MAX_MESSAGES_TO_LOAD):this.hasMoreMessages=!1,this.isLoadingMore=!1},error:i=>{console.error("Erreur lors du chargement des messages:",i),this.toastService.showError("Erreur lors du chargement des messages"),this.isLoadingMore=!1,this.currentPage--}}))}cleanupSubscriptions(){console.log("\u{1f9f9} Cleaning up existing subscriptions..."),this.subscriptions.unsubscribe(),this.subscriptions=new v.yU}reloadConversation(){console.log("\u{1f504} Reloading conversation..."),this.conversation?.id&&(this.messages=[],this.currentPage=1,this.hasMoreMessages=!0,this.loadConversation())}setupSubscriptions(){this.conversation?.id?(console.log("\u{1f504} Setting up real-time subscriptions for conversation:",this.conversation.id),console.log("\u{1f4e8} Setting up message subscription..."),this.subscriptions.add(this.MessageService.subscribeToNewMessages(this.conversation.id).subscribe({next:e=>{if(console.log("\u{1f4e8} New message received via subscription:",e),console.log("\u{1f4e8} Message structure:",{id:e.id,type:e.type,content:e.content,sender:e.sender,senderId:e.senderId,receiverId:e.receiverId,attachments:e.attachments}),console.log("\u{1f4e8} [Debug] Message type detected:",this.getMessageType(e)),console.log("\u{1f4e8} [Debug] Has image:",this.hasImage(e)),console.log("\u{1f4e8} [Debug] Has file:",this.hasFile(e)),console.log("\u{1f4e8} [Debug] Image URL:",this.getImageUrl(e)),e.attachments&&e.attachments.forEach((n,o)=>{console.log(`\u{1f4e8} [Debug] Attachment ${o}:`,{type:n.type,url:n.url,path:n.path,name:n.name,size:n.size})}),!this.messages.some(n=>n.id===e.id)){this.messages.push(e),console.log("\u2705 Message added to list, total messages:",this.messages.length),this.cdr.detectChanges(),setTimeout(()=>{this.scrollToBottom()},50);const n=e.sender?.id||e.senderId;console.log("\u{1f4e8} Checking if message should be marked as read:",{senderId:n,currentUserId:this.currentUserId,shouldMarkAsRead:n!==this.currentUserId}),n&&n!==this.currentUserId&&this.markMessageAsRead(e.id)}},error:e=>{console.error("\u274c Error in message subscription:",e),this.toastService.showError("Connexion temps r\xe9el interrompue"),setTimeout(()=>{console.log("\u{1f504} Retrying message subscription..."),this.setupSubscriptions()},5e3)}})),console.log("\u{1f4dd} Setting up typing indicator subscription..."),this.subscriptions.add(this.MessageService.subscribeToTypingIndicator(this.conversation.id).subscribe({next:e=>{console.log("\u{1f4dd} Typing indicator received:",e),e.userId!==this.currentUserId&&(this.otherUserIsTyping=e.isTyping,this.isUserTyping=e.isTyping,console.log("\u{1f4dd} Other user typing status updated:",this.otherUserIsTyping),this.cdr.detectChanges())},error:e=>{console.error("\u274c Error in typing subscription:",e)}})),this.subscriptions.add(this.MessageService.subscribeToConversationUpdates(this.conversation.id).subscribe({next:e=>{console.log("\u{1f4cb} Conversation update:",e),e.id===this.conversation.id&&(this.conversation={...this.conversation,...e},this.cdr.detectChanges())},error:e=>{console.error("\u274c Error in conversation subscription:",e)}}))):console.warn("\u274c Cannot setup subscriptions: no conversation ID")}markMessageAsRead(e){this.MessageService.markMessageAsRead(e).subscribe({next:()=>{console.log("\u2705 Message marked as read:",e)},error:i=>{console.error("\u274c Error marking message as read:",i)}})}sendMessage(){if(!this.messageForm.valid||!this.conversation?.id)return;const e=this.messageForm.get("content")?.value?.trim();if(!e)return;const i=this.otherParticipant?.id||this.otherParticipant?._id;i?(this.isSendingMessage=!0,this.updateInputState(),console.log("\u{1f4e4} Sending message:",{content:e,receiverId:i,conversationId:this.conversation.id}),this.MessageService.sendMessage(i,e,void 0,"TEXT",this.conversation.id).subscribe({next:n=>{console.log("\u2705 Message sent successfully:",n),this.messages.some(s=>s.id===n.id)||(this.messages.push(n),console.log("\u{1f4cb} Message added to local list, total:",this.messages.length)),this.messageForm.reset(),this.isSendingMessage=!1,this.updateInputState(),this.cdr.detectChanges(),setTimeout(()=>{this.scrollToBottom()},50)},error:n=>{console.error("\u274c Erreur lors de l'envoi du message:",n),this.toastService.showError("Erreur lors de l'envoi du message"),this.isSendingMessage=!1,this.updateInputState()}})):this.toastService.showError("Destinataire introuvable")}scrollToBottom(){setTimeout(()=>{if(this.messagesContainer){const e=this.messagesContainer.nativeElement;e.scrollTop=e.scrollHeight}},100)}formatLastActive(e){if(!e)return"Hors ligne";const i=Math.floor((Date.now()-new Date(e).getTime())/6e4);return i<1?"\xc0 l'instant":i<60?`Il y a ${i} min`:i<1440?`Il y a ${Math.floor(i/60)}h`:`Il y a ${Math.floor(i/1440)}j`}getVoicePlaybackData(e){return this.voicePlayback[e]||{progress:0,duration:0,currentTime:0,speed:1}}setVoicePlaybackData(e,i){this.voicePlayback[e]={...this.getVoicePlaybackData(e),...i}}startVideoCall(){this.otherParticipant?.id?(console.log("\u{1f4f9} Starting video call with:",this.otherParticipant.username),this.initiateCall(c.JG.VIDEO)):this.toastService.showError("Impossible de d\xe9marrer l'appel")}startVoiceCall(){this.otherParticipant?.id?(console.log("\u{1f4de} Starting voice call with:",this.otherParticipant.username),this.setupVideoElements(),this.initiateCall(c.JG.AUDIO)):this.toastService.showError("Impossible de d\xe9marrer l'appel")}toggleMicrophone(){this.isMuted=!this.isMuted,console.log("\u{1f3a4} Microphone toggled:",this.isMuted?"MUTED":"UNMUTED"),this.callService.toggleAudio&&this.callService.toggleAudio(),this.toastService.showSuccess(this.isMuted?"Microphone coup\xe9":"Microphone activ\xe9")}toggleCamera(){this.isVideoEnabled=!this.isVideoEnabled,console.log("\u{1f4f9} Camera toggled:",this.isVideoEnabled?"ENABLED":"DISABLED"),this.callService.toggleVideo&&this.callService.toggleVideo(),this.toastService.showSuccess(this.isVideoEnabled?"Cam\xe9ra activ\xe9e":"Cam\xe9ra d\xe9sactiv\xe9e")}endCall(){this.activeCall&&(console.log("\u{1f4de} Ending call:",this.activeCall.id),this.callService.endCall?this.callService.endCall(this.activeCall.id).subscribe({next:()=>{console.log("\u2705 Call ended successfully"),this.activeCall=null,this.isCallConnected=!1},error:e=>{console.error("\u274c Error ending call:",e),this.toastService.showError("Erreur lors de la fin de l'appel")}}):(this.activeCall=null,this.isCallConnected=!1,this.toastService.showSuccess("Appel termin\xe9")))}formatFileSize(e){return e<1024?e+" B":e<1048576?Math.round(e/1024)+" KB":Math.round(e/1048576)+" MB"}downloadFile(e){const i=e.attachments?.find(n=>!n.type?.startsWith("image/"));if(i?.url){const n=document.createElement("a");n.href=i.url,n.download=i.name||"file",n.target="_blank",document.body.appendChild(n),n.click(),document.body.removeChild(n),this.toastService.showSuccess("T\xe9l\xe9chargement d\xe9marr\xe9")}}toggleSearch(){this.searchMode=!this.searchMode,this.showSearch=this.searchMode}toggleMainMenu(){this.showMainMenu=!this.showMainMenu}goBackToConversations(){console.log("\u{1f519} Going back to conversations"),this.router.navigate(["/front/messages/conversations"]).then(()=>{console.log("\u2705 Navigation to conversations successful")}).catch(e=>{console.error("\u274c Navigation error:",e),this.router.navigate(["/front/messages"]).catch(()=>{window.location.href="/front/messages/conversations"})})}closeAllMenus(){this.showEmojiPicker=!1,this.showAttachmentMenu=!1,this.showMainMenu=!1,this.showMessageContextMenu=!1,this.showReactionPicker=!1}onMessageContextMenu(e,i){i.preventDefault(),this.selectedMessage=e,this.contextMenuPosition={x:i.clientX,y:i.clientY},this.showMessageContextMenu=!0}showQuickReactions(e,i){i.stopPropagation(),this.reactionPickerMessage=e,this.contextMenuPosition={x:i.clientX,y:i.clientY},this.showReactionPicker=!0}quickReact(e){this.reactionPickerMessage&&this.toggleReaction(this.reactionPickerMessage.id,e),this.showReactionPicker=!1}toggleReaction(e,i){console.log("\u{1f44d} Toggling reaction:",i,"for message:",e),e&&i?this.MessageService.reactToMessage(e,i).subscribe({next:n=>{console.log("\u2705 Reaction toggled successfully:",n);const o=this.messages.findIndex(s=>s.id===e);-1!==o&&(this.messages[o]=n,this.cdr.detectChanges()),this.toastService.showSuccess(`R\xe9action ${i} ajout\xe9e`)},error:n=>{console.error("\u274c Error toggling reaction:",n),this.toastService.showError("Erreur lors de l'ajout de la r\xe9action")}}):console.error("\u274c Missing messageId or emoji for reaction")}hasUserReacted(e,i){return e.userId===i}replyToMessage(e){console.log("\u21a9\ufe0f Replying to message:",e.id),this.closeAllMenus()}forwardMessage(e){console.log("\u27a1\ufe0f Forwarding message:",e.id),this.closeAllMenus()}deleteMessage(e){return console.log("\u{1f5d1}\ufe0f Deleting message:",e.id),e.id?e.sender?.id!==this.currentUserId&&e.senderId!==this.currentUserId?(this.toastService.showError("Vous ne pouvez supprimer que vos propres messages"),void this.closeAllMenus()):void(confirm("\xcates-vous s\xfbr de vouloir supprimer ce message ?")?(this.MessageService.deleteMessage(e.id).subscribe({next:n=>{console.log("\u2705 Message deleted successfully:",n),this.messages=this.messages.filter(o=>o.id!==e.id),this.toastService.showSuccess("Message supprim\xe9"),this.cdr.detectChanges()},error:n=>{console.error("\u274c Error deleting message:",n),this.toastService.showError("Erreur lors de la suppression du message")}}),this.closeAllMenus()):this.closeAllMenus()):(console.error("\u274c No message ID provided for deletion"),void this.toastService.showError("Erreur: ID du message manquant"))}toggleEmojiPicker(){this.showEmojiPicker=!this.showEmojiPicker}selectEmojiCategory(e){this.selectedEmojiCategory=e}getEmojisForCategory(e){return e?.emojis||[]}insertEmoji(e){const n=(this.messageForm.get("content")?.value||"")+e.emoji;this.messageForm.patchValue({content:n}),this.showEmojiPicker=!1}toggleAttachmentMenu(){this.showAttachmentMenu=!this.showAttachmentMenu}trackByMessageId(e,i){return i.id||i._id||e.toString()}testAddMessage(){console.log("\u{1f9ea} Test: Adding message");const e={id:`test-${Date.now()}`,content:`Message de test ${(new Date).toLocaleTimeString()}`,timestamp:(new Date).toISOString(),sender:{id:this.otherParticipant?.id||"test-user",username:this.otherParticipant?.username||"Test User",image:this.otherParticipant?.image||"assets/images/default-avatar.png"},type:"TEXT",isRead:!1};this.messages.push(e),this.cdr.detectChanges(),setTimeout(()=>this.scrollToBottom(),50)}isGroupConversation(){return this.conversation?.isGroup||this.conversation?.participants?.length>2||!1}openCamera(){console.log("\u{1f4f7} Opening camera"),this.showAttachmentMenu=!1}zoomImage(e){const i=document.querySelector(".image-viewer-zoom");if(i){const n=i.style.transform||"scale(1)",o=parseFloat(n.match(/scale\(([^)]+)\)/)?.[1]||"1"),s=Math.max(.5,Math.min(3,o*e));i.style.transform=`scale(${s})`,s>1?i.classList.add("zoomed"):i.classList.remove("zoomed")}}resetZoom(){const e=document.querySelector(".image-viewer-zoom");e&&(e.style.transform="scale(1)",e.classList.remove("zoomed"))}triggerFileInput(e){const i=this.fileInput?.nativeElement;i?(i.accept="image"===e?"image/*":"video"===e?"video/*":"document"===e?".pdf,.doc,.docx,.xls,.xlsx,.txt":"*/*",i.value="",i.click(),this.showAttachmentMenu=!1):console.error("File input element not found")}formatMessageTime(e){return e?new Date(e).toLocaleTimeString("fr-FR",{hour:"2-digit",minute:"2-digit"}):""}formatDateSeparator(e){if(!e)return"";const i=new Date(e),n=new Date,o=new Date(n);return o.setDate(o.getDate()-1),i.toDateString()===n.toDateString()?"Aujourd'hui":i.toDateString()===o.toDateString()?"Hier":i.toLocaleDateString("fr-FR")}formatMessageContent(e){return e?e.replace(/(https?:\/\/[^\s]+)/g,'<a href="$1" target="_blank" class="text-blue-500 underline">$1</a>'):""}shouldShowDateSeparator(e){if(0===e)return!0;const i=this.messages[e],n=this.messages[e-1];return!(!i?.timestamp||!n?.timestamp)&&new Date(i.timestamp).toDateString()!==new Date(n.timestamp).toDateString()}shouldShowAvatar(e){const i=this.messages[e],n=this.messages[e+1];return!n||i.sender?.id!==n.sender?.id}shouldShowSenderName(e){const i=this.messages[e],n=this.messages[e-1];return!n||i.sender?.id!==n.sender?.id}getMessageType(e){if(e.type){if("IMAGE"===e.type||"image"===e.type)return"image";if("VIDEO"===e.type||"video"===e.type)return"video";if("AUDIO"===e.type||"audio"===e.type||"VOICE_MESSAGE"===e.type)return"audio";if("FILE"===e.type||"file"===e.type)return"file"}if(e.attachments&&e.attachments.length>0){const i=e.attachments[0];return i.type?.startsWith("image/")?"image":i.type?.startsWith("video/")?"video":i.type?.startsWith("audio/")?"audio":"file"}return e.voiceUrl||e.audioUrl||e.voice?"audio":"text"}hasImage(e){return"IMAGE"===e.type||"image"===e.type||(e.attachments?.some(o=>o.type?.startsWith("image/")||"IMAGE"===o.type)||!(!e.imageUrl&&!e.image))}hasFile(e){return"FILE"===e.type||"file"===e.type||e.attachments?.some(n=>!n.type?.startsWith("image/")&&"IMAGE"!==n.type)||!1}getImageUrl(e){if(e.imageUrl)return e.imageUrl;if(e.image)return e.image;const i=e.attachments?.find(n=>n.type?.startsWith("image/")||"IMAGE"===n.type);return i&&(i.url||i.path)||""}getFileName(e){return e.attachments?.find(n=>!n.type?.startsWith("image/"))?.name||"Fichier"}getFileSize(e){const i=e.attachments?.find(o=>!o.type?.startsWith("image/"));if(!i?.size)return"";const n=i.size;return n<1024?n+" B":n<1048576?Math.round(n/1024)+" KB":Math.round(n/1048576)+" MB"}getFileIcon(e){const i=e.attachments?.find(n=>!n.type?.startsWith("image/"));return i?.type?i.type.startsWith("audio/")?"fas fa-file-audio":i.type.startsWith("video/")?"fas fa-file-video":i.type.includes("pdf")?"fas fa-file-pdf":i.type.includes("word")?"fas fa-file-word":i.type.includes("excel")?"fas fa-file-excel":"fas fa-file":"fas fa-file"}getUserColor(e){const i=["#FF6B6B","#4ECDC4","#45B7D1","#96CEB4","#FFEAA7","#DDA0DD","#98D8C8"];return i[e.charCodeAt(0)%i.length]}onMessageClick(e,i){console.log("Message clicked:",e)}onInputChange(e){this.handleTypingIndicator()}onInputKeyDown(e){"Enter"===e.key&&!e.shiftKey&&(e.preventDefault(),this.sendMessage())}onInputFocus(){}onInputBlur(){}onScroll(e){0===e.target.scrollTop&&this.hasMoreMessages&&!this.isLoadingMore&&this.loadMoreMessages()}openUserProfile(e){console.log("Opening user profile for:",e)}onImageLoad(e,i){console.log("\u{1f5bc}\ufe0f [Debug] Image loaded successfully for message:",i.id,e.target.src)}onImageError(e,i){console.error("\u{1f5bc}\ufe0f [Debug] Image failed to load for message:",i.id,{src:e.target.src,error:e}),e.target.src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjNmNGY2Ii8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzZiNzI4MCIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkltYWdlIG5vbiBkaXNwb25pYmxlPC90ZXh0Pjwvc3ZnPg=="}openImageViewer(e){const i=e.attachments?.find(n=>n.type?.startsWith("image/"));i?.url&&(this.selectedImage={url:i.url,name:i.name||"Image",size:this.formatFileSize(i.size||0),message:e},this.showImageViewer=!0,console.log("\u{1f5bc}\ufe0f [ImageViewer] Opening image:",this.selectedImage))}closeImageViewer(){this.showImageViewer=!1,this.selectedImage=null,console.log("\u{1f5bc}\ufe0f [ImageViewer] Closed")}downloadImage(){if(this.selectedImage?.url){const e=document.createElement("a");e.href=this.selectedImage.url,e.download=this.selectedImage.name||"image",e.target="_blank",document.body.appendChild(e),e.click(),document.body.removeChild(e),this.toastService.showSuccess("T\xe9l\xe9chargement d\xe9marr\xe9"),console.log("\u{1f5bc}\ufe0f [ImageViewer] Download started:",this.selectedImage.name)}}searchMessages(){this.searchResults=this.searchQuery.trim()?this.messages.filter(e=>e.content?.toLowerCase().includes(this.searchQuery.toLowerCase())||e.sender?.username?.toLowerCase().includes(this.searchQuery.toLowerCase())):[]}onSearchQueryChange(){this.searchMessages()}clearSearch(){this.searchQuery="",this.searchResults=[]}jumpToMessage(e){const i=document.getElementById(`message-${e}`);i&&(i.scrollIntoView({behavior:"smooth",block:"center"}),i.classList.add("highlight"),setTimeout(()=>{i.classList.remove("highlight")},2e3))}closeContextMenu(){this.showMessageContextMenu=!1,this.selectedMessage=null}initiateCall(e){if(console.log("\u{1f680} [MessageChat] Starting call initiation process..."),console.log("\u{1f4cb} [MessageChat] Call details:",{callType:e,otherParticipant:this.otherParticipant,conversation:this.conversation?.id,currentUserId:this.currentUserId}),!this.otherParticipant)return console.error("\u274c [MessageChat] No recipient selected"),void this.toastService.showError("Aucun destinataire s\xe9lectionn\xe9");const i=this.otherParticipant.id||this.otherParticipant._id;if(!i)return console.error("\u274c [MessageChat] Recipient ID not found"),void this.toastService.showError("ID du destinataire introuvable");console.log(`\u{1f4de} [MessageChat] Initiating ${e} call to user:`,{recipientId:i,recipientName:this.otherParticipant.username||this.otherParticipant.name,conversationId:this.conversation?.id}),this.isInCall=!0,this.callType=e===c.JG.VIDEO?"VIDEO":"AUDIO",this.callDuration=0,this.startCallTimer(),console.log("\u{1f504} [MessageChat] Calling CallService.initiateCall..."),this.callService.initiateCall(i,e,this.conversation?.id).subscribe({next:n=>{console.log("\u2705 [MessageChat] Call initiated successfully:",{callId:n.id,callType:n.type,callStatus:n.status,caller:n.caller?.username,recipient:n.recipient?.username,conversationId:n.conversationId}),this.activeCall=n,this.isCallConnected=!1,this.toastService.showSuccess(`Appel ${e===c.JG.VIDEO?"vid\xe9o":"audio"} initi\xe9`),console.log("\u{1f4e1} [MessageChat] Call should now be sent to recipient via WebSocket")},error:n=>{console.error("\u274c [MessageChat] Error initiating call:",{error:n.message||n,recipientId:i,callType:e,conversationId:this.conversation?.id}),this.endCall(),this.toastService.showError("Erreur lors de l'initiation de l'appel")}})}acceptCall(e){console.log("\u{1f504} Accepting incoming call:",e),this.callService.acceptCall(e).subscribe({next:i=>{console.log("\u2705 Call accepted successfully:",i),this.activeCall=i,this.isInCall=!0,this.isCallConnected=!0,this.callType=i.type===c.JG.VIDEO?"VIDEO":"AUDIO",this.startCallTimer(),this.toastService.showSuccess("Appel accept\xe9")},error:i=>{console.error("\u274c Error accepting call:",i),this.toastService.showError("Erreur lors de l'acceptation de l'appel")}})}rejectCall(e){console.log("\u{1f504} Rejecting incoming call:",e),this.callService.rejectCall(e.id,"User rejected").subscribe({next:()=>{console.log("\u2705 Call rejected successfully"),this.toastService.showSuccess("Appel rejet\xe9")},error:i=>{console.error("\u274c Error rejecting call:",i),this.toastService.showError("Erreur lors du rejet de l'appel")}})}startCallTimer(){this.callDuration=0,this.callTimer=setInterval(()=>{this.callDuration++,this.cdr.detectChanges()},1e3)}resetCallState(){this.callTimer&&(clearInterval(this.callTimer),this.callTimer=null),this.isInCall=!1,this.callType=null,this.callDuration=0,this.activeCall=null,this.isCallConnected=!1,this.isMuted=!1,this.isVideoEnabled=!0}toggleMute(){this.activeCall&&(this.isMuted=!this.isMuted,this.callService.toggleMedia(this.activeCall.id,void 0,!this.isMuted).subscribe({next:()=>{this.toastService.showSuccess(this.isMuted?"Micro coup\xe9":"Micro activ\xe9")},error:e=>{console.error("\u274c Error toggling mute:",e),this.isMuted=!this.isMuted,this.toastService.showError("Erreur lors du changement du micro")}}))}toggleVideo(){this.activeCall&&(this.isVideoEnabled=!this.isVideoEnabled,this.callService.toggleMedia(this.activeCall.id,this.isVideoEnabled,void 0).subscribe({next:()=>{this.toastService.showSuccess(this.isVideoEnabled?"Cam\xe9ra activ\xe9e":"Cam\xe9ra d\xe9sactiv\xe9e")},error:e=>{console.error("\u274c Error toggling video:",e),this.isVideoEnabled=!this.isVideoEnabled,this.toastService.showError("Erreur lors du changement de la cam\xe9ra")}}))}formatCallDuration(e){const i=Math.floor(e/3600),n=Math.floor(e%3600/60),o=e%60;return i>0?`${i}:${n.toString().padStart(2,"0")}:${o.toString().padStart(2,"0")}`:`${n}:${o.toString().padStart(2,"0")}`}startVoiceRecording(){var e=this;return(0,_.A)(function*(){console.log("\u{1f3a4} [Voice] Starting voice recording...");try{if(!navigator.mediaDevices||!navigator.mediaDevices.getUserMedia)throw new Error("Votre navigateur ne supporte pas l'enregistrement audio");if(!window.MediaRecorder)throw new Error("MediaRecorder n'est pas support\xe9 par votre navigateur");console.log("\u{1f3a4} [Voice] Requesting microphone access...");const i=yield navigator.mediaDevices.getUserMedia({audio:{echoCancellation:!0,noiseSuppression:!0,autoGainControl:!0,sampleRate:44100,channelCount:1}});console.log("\u{1f3a4} [Voice] Microphone access granted");let n="audio/webm;codecs=opus";MediaRecorder.isTypeSupported(n)||(n="audio/webm",MediaRecorder.isTypeSupported(n)||(n="audio/mp4",MediaRecorder.isTypeSupported(n)||(n=""))),console.log("\u{1f3a4} [Voice] Using MIME type:",n),e.mediaRecorder=new MediaRecorder(i,{mimeType:n||void 0}),e.audioChunks=[],e.isRecordingVoice=!0,e.voiceRecordingDuration=0,e.voiceRecordingState="recording",console.log("\u{1f3a4} [Voice] MediaRecorder created, starting timer..."),e.recordingTimer=setInterval(()=>{e.voiceRecordingDuration++,e.animateVoiceWaves(),e.cdr.detectChanges()},1e3),e.mediaRecorder.ondataavailable=o=>{console.log("\u{1f3a4} [Voice] Data available:",o.data.size,"bytes"),o.data.size>0&&e.audioChunks.push(o.data)},e.mediaRecorder.onstop=()=>{console.log("\u{1f3a4} [Voice] MediaRecorder stopped, processing audio..."),e.processRecordedAudio()},e.mediaRecorder.onerror=o=>{console.error("\u{1f3a4} [Voice] MediaRecorder error:",o.error),e.toastService.showError("Erreur lors de l'enregistrement"),e.cancelVoiceRecording()},e.mediaRecorder.start(100),console.log("\u{1f3a4} [Voice] Recording started successfully"),e.toastService.showSuccess("\u{1f3a4} Enregistrement vocal d\xe9marr\xe9")}catch(i){console.error("\u{1f3a4} [Voice] Error starting recording:",i);let n="Impossible de d\xe9marrer l'enregistrement vocal";"NotAllowedError"===i.name?n="Acc\xe8s au microphone refus\xe9. Veuillez autoriser l'acc\xe8s dans les param\xe8tres de votre navigateur.":"NotFoundError"===i.name?n="Aucun microphone d\xe9tect\xe9. Veuillez connecter un microphone.":"NotSupportedError"===i.name?n="Votre navigateur ne supporte pas l'enregistrement audio.":i.message&&(n=i.message),e.toastService.showError(n),e.cancelVoiceRecording()}})()}stopVoiceRecording(){this.mediaRecorder&&"recording"===this.mediaRecorder.state&&(this.mediaRecorder.stop(),this.mediaRecorder.stream.getTracks().forEach(e=>e.stop())),this.recordingTimer&&(clearInterval(this.recordingTimer),this.recordingTimer=null),this.isRecordingVoice=!1,this.voiceRecordingState="processing"}cancelVoiceRecording(){this.mediaRecorder&&("recording"===this.mediaRecorder.state&&this.mediaRecorder.stop(),this.mediaRecorder.stream.getTracks().forEach(e=>e.stop()),this.mediaRecorder=null),this.recordingTimer&&(clearInterval(this.recordingTimer),this.recordingTimer=null),this.isRecordingVoice=!1,this.voiceRecordingDuration=0,this.voiceRecordingState="idle",this.audioChunks=[]}processRecordedAudio(){var e=this;return(0,_.A)(function*(){console.log("\u{1f3a4} [Voice] Processing recorded audio...");try{if(0===e.audioChunks.length)return console.error("\u{1f3a4} [Voice] No audio chunks available"),e.toastService.showError("Aucun audio enregistr\xe9"),void e.cancelVoiceRecording();if(console.log("\u{1f3a4} [Voice] Audio chunks:",e.audioChunks.length,"Duration:",e.voiceRecordingDuration),e.voiceRecordingDuration<1)return console.error("\u{1f3a4} [Voice] Recording too short:",e.voiceRecordingDuration),e.toastService.showError("Enregistrement trop court (minimum 1 seconde)"),void e.cancelVoiceRecording();let i="audio/webm;codecs=opus";e.mediaRecorder?.mimeType&&(i=e.mediaRecorder.mimeType),console.log("\u{1f3a4} [Voice] Creating audio blob with MIME type:",i);const n=new Blob(e.audioChunks,{type:i});console.log("\u{1f3a4} [Voice] Audio blob created:",{size:n.size,type:n.type});let o=".webm";i.includes("mp4")?o=".mp4":i.includes("wav")?o=".wav":i.includes("ogg")&&(o=".ogg");const s=new File([n],`voice_${Date.now()}${o}`,{type:i});console.log("\u{1f3a4} [Voice] Audio file created:",{name:s.name,size:s.size,type:s.type}),e.voiceRecordingState="processing",yield e.sendVoiceMessage(s),console.log("\u{1f3a4} [Voice] Voice message sent successfully"),e.toastService.showSuccess("\u{1f3a4} Message vocal envoy\xe9")}catch(i){console.error("\u{1f3a4} [Voice] Error processing audio:",i),e.toastService.showError("Erreur lors de l'envoi du message vocal: "+(i.message||"Erreur inconnue"))}finally{e.voiceRecordingState="idle",e.voiceRecordingDuration=0,e.audioChunks=[],e.isRecordingVoice=!1,console.log("\u{1f3a4} [Voice] Audio processing completed, state reset")}})()}sendVoiceMessage(e){var i=this;return(0,_.A)(function*(){const n=i.otherParticipant?.id||i.otherParticipant?._id;if(!n)throw new Error("Destinataire introuvable");return new Promise((o,s)=>{i.MessageService.sendMessage(n,"",e,"AUDIO",i.conversation.id).subscribe({next:d=>{i.messages.push(d),i.scrollToBottom(),o()},error:d=>{console.error("Erreur lors de l'envoi du message vocal:",d),s(d)}})})})()}formatRecordingDuration(e){return`${Math.floor(e/60)}:${(e%60).toString().padStart(2,"0")}`}onRecordStart(e){return e.preventDefault(),console.log("\u{1f3a4} [Voice] Record start triggered"),console.log("\u{1f3a4} [Voice] Current state:",{isRecordingVoice:this.isRecordingVoice,voiceRecordingState:this.voiceRecordingState,voiceRecordingDuration:this.voiceRecordingDuration,mediaRecorder:!!this.mediaRecorder}),"processing"===this.voiceRecordingState?(console.log("\u{1f3a4} [Voice] Already processing, ignoring start"),void this.toastService.showWarning("Traitement en cours...")):this.isRecordingVoice?(console.log("\u{1f3a4} [Voice] Already recording, ignoring start"),void this.toastService.showWarning("Enregistrement d\xe9j\xe0 en cours...")):(this.toastService.showInfo("\u{1f3a4} D\xe9marrage de l'enregistrement vocal..."),void this.startVoiceRecording().catch(i=>{console.error("\u{1f3a4} [Voice] Failed to start recording:",i),this.toastService.showError("Impossible de d\xe9marrer l'enregistrement vocal: "+(i.message||"Erreur inconnue"))}))}onRecordEnd(e){e.preventDefault(),console.log("\u{1f3a4} [Voice] Record end triggered"),this.isRecordingVoice?this.stopVoiceRecording():console.log("\u{1f3a4} [Voice] Not recording, ignoring end")}onRecordCancel(e){e.preventDefault(),console.log("\u{1f3a4} [Voice] Record cancel triggered"),this.isRecordingVoice?this.cancelVoiceRecording():console.log("\u{1f3a4} [Voice] Not recording, ignoring cancel")}getRecordingFormat(){if(this.mediaRecorder?.mimeType){if(this.mediaRecorder.mimeType.includes("webm"))return"WebM";if(this.mediaRecorder.mimeType.includes("mp4"))return"MP4";if(this.mediaRecorder.mimeType.includes("wav"))return"WAV";if(this.mediaRecorder.mimeType.includes("ogg"))return"OGG"}return"Auto"}animateVoiceWaves(){this.voiceWaves=this.voiceWaves.map(()=>Math.floor(20*Math.random())+4)}onFileSelected(e){console.log("\u{1f4c1} [Upload] File selection triggered");const i=e.target.files;if(i&&0!==i.length){console.log(`\u{1f4c1} [Upload] ${i.length} file(s) selected:`,i);for(let n of i)console.log(`\u{1f4c1} [Upload] Processing file: ${n.name}, size: ${n.size}, type: ${n.type}`),this.uploadFile(n)}else console.log("\u{1f4c1} [Upload] No files selected")}uploadFile(e){console.log(`\u{1f4c1} [Upload] Starting upload for file: ${e.name}`);const i=this.otherParticipant?.id||this.otherParticipant?._id;return i?(console.log(`\u{1f4c1} [Upload] Receiver ID: ${i}`),e.size>52428800?(console.error(`\u{1f4c1} [Upload] File too large: ${e.size} bytes`),void this.toastService.showError("Fichier trop volumineux (max 50MB)")):e.type.startsWith("image/")&&e.size>1048576?(console.log("\u{1f5bc}\ufe0f [Compression] Compressing image:",e.name,"Original size:",e.size),void this.compressImage(e).then(o=>{console.log("\u{1f5bc}\ufe0f [Compression] \u2705 Image compressed successfully. New size:",o.size),this.sendFileToServer(o,i)}).catch(o=>{console.error("\u{1f5bc}\ufe0f [Compression] \u274c Error compressing image:",o),this.sendFileToServer(e,i)})):void this.sendFileToServer(e,i)):(console.error("\u{1f4c1} [Upload] No receiver ID found"),void this.toastService.showError("Destinataire introuvable"))}sendFileToServer(e,i){const n=this.getFileMessageType(e);console.log(`\u{1f4c1} [Upload] Message type determined: ${n}`),console.log(`\u{1f4c1} [Upload] Conversation ID: ${this.conversation.id}`),this.isSendingMessage=!0,this.isUploading=!0,this.uploadProgress=0,console.log("\u{1f4c1} [Upload] Calling MessageService.sendMessage...");const o=setInterval(()=>{this.uploadProgress+=15*Math.random(),this.uploadProgress>=90&&clearInterval(o),this.cdr.detectChanges()},300);this.MessageService.sendMessage(i,"",e,n,this.conversation.id).subscribe({next:s=>{console.log("\u{1f4c1} [Upload] \u2705 File sent successfully:",s),console.log("\u{1f4c1} [Debug] Sent message structure:",{id:s.id,type:s.type,attachments:s.attachments,hasImage:this.hasImage(s),hasFile:this.hasFile(s),imageUrl:this.getImageUrl(s)}),clearInterval(o),this.uploadProgress=100,setTimeout(()=>{this.messages.push(s),this.scrollToBottom(),this.toastService.showSuccess("Fichier envoy\xe9 avec succ\xe8s"),this.resetUploadState()},500)},error:s=>{console.error("\u{1f4c1} [Upload] \u274c Error sending file:",s),clearInterval(o),this.toastService.showError("Erreur lors de l'envoi du fichier"),this.resetUploadState()}})}getFileMessageType(e){return e.type.startsWith("image/")?"IMAGE":e.type.startsWith("video/")?"VIDEO":e.type.startsWith("audio/")?"AUDIO":"FILE"}getFileAcceptTypes(){return"*/*"}resetUploadState(){this.isSendingMessage=!1,this.isUploading=!1,this.uploadProgress=0}onDragOver(e){e.preventDefault(),e.stopPropagation(),this.isDragOver=!0}onDragLeave(e){e.preventDefault(),e.stopPropagation();const i=e.currentTarget.getBoundingClientRect(),n=e.clientX,o=e.clientY;(n<i.left||n>i.right||o<i.top||o>i.bottom)&&(this.isDragOver=!1)}onDrop(e){e.preventDefault(),e.stopPropagation(),this.isDragOver=!1;const i=e.dataTransfer?.files;i&&i.length>0&&(console.log("\u{1f4c1} [Drag&Drop] Files dropped:",i.length),Array.from(i).forEach(n=>{console.log("\u{1f4c1} [Drag&Drop] Processing file:",n.name,n.type,n.size),this.uploadFile(n)}),this.toastService.showSuccess(`${i.length} fichier(s) en cours d'envoi`))}compressImage(e,i=.8){return new Promise((n,o)=>{const s=document.createElement("canvas"),d=s.getContext("2d"),h=new Image;h.onload=()=>{let{width:m,height:x}=h;if(m>1920||x>1080){const P=Math.min(1920/m,1080/x);m*=P,x*=P}s.width=m,s.height=x,d?.drawImage(h,0,0,m,x),s.toBlob(P=>{if(P){const qt=new File([P],e.name,{type:e.type,lastModified:Date.now()});n(qt)}else o(new Error("Failed to compress image"))},e.type,i)},h.onerror=()=>o(new Error("Failed to load image")),h.src=URL.createObjectURL(e)})}handleTypingIndicator(){this.isTyping||(this.isTyping=!0,this.sendTypingIndicator(!0)),this.typingTimeout&&clearTimeout(this.typingTimeout),this.typingTimeout=setTimeout(()=>{this.isTyping=!1,this.sendTypingIndicator(!1)},2e3)}sendTypingIndicator(e){const i=this.otherParticipant?.id||this.otherParticipant?._id;i&&this.conversation?.id&&console.log(`\u{1f4dd} Sending typing indicator: ${e} to user ${i}`)}onCallAccepted(e){console.log("\u{1f504} Call accepted from interface:",e),this.activeCall=e,this.isInCall=!0,this.isCallConnected=!0,this.startCallTimer(),this.toastService.showSuccess("Appel accept\xe9")}onCallRejected(){console.log("\u{1f504} Call rejected from interface"),this.endCall(),this.toastService.showInfo("Appel rejet\xe9")}playVoiceMessage(e){console.log("\u{1f3b5} [Voice] Playing voice message:",e.id),this.toggleVoicePlayback(e)}isVoicePlaying(e){return this.playingMessageId===e}toggleVoicePlayback(e){const i=e.id,n=this.getVoiceUrl(e);if(!n)return console.error("\u{1f3b5} [Voice] No audio URL found for message:",i),void this.toastService.showError("Fichier audio introuvable");this.isVoicePlaying(i)?this.stopVoicePlayback():(this.stopVoicePlayback(),this.startVoicePlayback(e,n))}startVoicePlayback(e,i){const n=e.id;try{console.log("\u{1f3b5} [Voice] Starting playback for:",n,"URL:",i),this.currentAudio=new Audio(i),this.playingMessageId=n;const o=this.getVoicePlaybackData(n);this.setVoicePlaybackData(n,{progress:0,currentTime:0,speed:o.speed||1,duration:o.duration||0}),this.currentAudio.playbackRate=o.speed||1,this.currentAudio.addEventListener("loadedmetadata",()=>{this.currentAudio&&(this.setVoicePlaybackData(n,{duration:this.currentAudio.duration}),console.log("\u{1f3b5} [Voice] Audio loaded, duration:",this.currentAudio.duration))}),this.currentAudio.addEventListener("timeupdate",()=>{if(this.currentAudio&&this.playingMessageId===n){const s=this.currentAudio.currentTime;this.setVoicePlaybackData(n,{currentTime:s,progress:s/this.currentAudio.duration*100}),this.cdr.detectChanges()}}),this.currentAudio.addEventListener("ended",()=>{console.log("\u{1f3b5} [Voice] Playback ended for:",n),this.stopVoicePlayback()}),this.currentAudio.addEventListener("error",s=>{console.error("\u{1f3b5} [Voice] Audio error:",s),this.toastService.showError("Erreur lors de la lecture audio"),this.stopVoicePlayback()}),this.currentAudio.play().then(()=>{console.log("\u{1f3b5} [Voice] Playback started successfully"),this.toastService.showSuccess("\u{1f3b5} Lecture du message vocal")}).catch(s=>{console.error("\u{1f3b5} [Voice] Error starting playback:",s),this.toastService.showError("Impossible de lire le message vocal"),this.stopVoicePlayback()})}catch(o){console.error("\u{1f3b5} [Voice] Error creating audio:",o),this.toastService.showError("Erreur lors de la lecture audio"),this.stopVoicePlayback()}}stopVoicePlayback(){this.currentAudio&&(console.log("\u{1f3b5} [Voice] Stopping playback for:",this.playingMessageId),this.currentAudio.pause(),this.currentAudio.currentTime=0,this.currentAudio=null),this.playingMessageId=null,this.cdr.detectChanges()}getVoiceUrl(e){if(e.voiceUrl)return e.voiceUrl;if(e.audioUrl)return e.audioUrl;if(e.voice)return e.voice;const i=e.attachments?.find(n=>n.type?.startsWith("audio/")||"AUDIO"===n.type);return i&&(i.url||i.path)||""}getVoiceWaves(e){const n=(e.id||"").split("").reduce((s,d)=>s+d.charCodeAt(0),0),o=[];for(let s=0;s<16;s++)o.push(4+(n+7*s)%20);return o}getVoiceProgress(e){const i=this.getVoicePlaybackData(e.id);return Math.floor(i.progress/100*16)}getVoiceCurrentTime(e){const i=this.getVoicePlaybackData(e.id);return this.formatAudioTime(i.currentTime)}getVoiceDuration(e){const n=this.getVoicePlaybackData(e.id).duration||e.metadata?.duration||0;return"string"==typeof n?n:this.formatAudioTime(n)}formatAudioTime(e){return`${Math.floor(e/60)}:${Math.floor(e%60).toString().padStart(2,"0")}`}seekVoiceMessage(e,i){if(!this.currentAudio||this.playingMessageId!==e.id)return;const d=i/16*100/100*this.currentAudio.duration;this.currentAudio.currentTime=d,console.log("\u{1f3b5} [Voice] Seeking to:",d,"seconds")}toggleVoiceSpeed(e){const i=e.id,n=this.getVoicePlaybackData(i),o=1===n.speed?1.5:1.5===n.speed?2:1;this.setVoicePlaybackData(i,{speed:o}),this.currentAudio&&this.playingMessageId===i&&(this.currentAudio.playbackRate=o),this.toastService.showSuccess(`Vitesse: ${o}x`)}changeVoiceSpeed(e){this.toggleVoiceSpeed(e)}getVoiceSpeed(e){return this.getVoicePlaybackData(e.id).speed||1}ngOnDestroy(){this.subscriptions.unsubscribe(),this.callTimer&&clearInterval(this.callTimer),this.recordingTimer&&clearInterval(this.recordingTimer),this.typingTimeout&&clearTimeout(this.typingTimeout),this.mediaRecorder&&("recording"===this.mediaRecorder.state&&this.mediaRecorder.stop(),this.mediaRecorder.stream?.getTracks().forEach(e=>e.stop())),this.stopVoicePlayback()}static{this.\u0275fac=function(i){return new(i||r)(t.rXU(l.ok),t.rXU(p.nX),t.rXU(p.Ix),t.rXU(g.b),t.rXU(C.U),t.rXU(M.f),t.rXU(t.gRc))}}static{this.\u0275cmp=t.VBU({type:r,selectors:[["app-message-chat"]],viewQuery:function(i,n){if(1&i&&(t.GBs(O,5),t.GBs(T,5),t.GBs(R,5),t.GBs(V,5),t.GBs(D,5),t.GBs(F,5)),2&i){let o;t.mGM(o=t.lsd())&&(n.messagesContainer=o.first),t.mGM(o=t.lsd())&&(n.fileInput=o.first),t.mGM(o=t.lsd())&&(n.localVideo=o.first),t.mGM(o=t.lsd())&&(n.remoteVideo=o.first),t.mGM(o=t.lsd())&&(n.localVideoHidden=o.first),t.mGM(o=t.lsd())&&(n.remoteVideoHidden=o.first)}},decls:60,vars:62,consts:[["autoplay","","muted","","playsinline","",2,"position","absolute","top","-9999px","left","-9999px","width","1px","height","1px"],["localVideo",""],["autoplay","","playsinline","",2,"position","absolute","top","-9999px","left","-9999px","width","1px","height","1px"],["remoteVideo",""],[2,"display","flex","flex-direction","column","height","100vh","background","linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)","color","#1f2937","font-family","'Inter', -apple-system, BlinkMacSystemFont, sans-serif"],[2,"display","flex","align-items","center","padding","12px 16px","background","#ffffff","border-bottom","1px solid #e5e7eb","box-shadow","0 1px 3px rgba(0, 0, 0, 0.1)","position","relative","z-index","10"],["onmouseover","this.style.background='#f3f4f6'; this.style.transform='scale(1.05)'","onmouseout","this.style.background='transparent'; this.style.transform='scale(1)'","title","Retour aux conversations",2,"padding","10px","margin-right","12px","border-radius","50%","border","none","background","transparent","cursor","pointer","transition","all 0.2s ease","display","flex","align-items","center","justify-content","center","min-width","40px","min-height","40px",3,"click"],[1,"fas","fa-arrow-left",2,"color","#374151","font-size","18px","font-weight","bold"],[2,"display","flex","align-items","center","flex","1","min-width","0"],[2,"position","relative","margin-right","12px"],["onmouseover","this.style.transform='scale(1.05)'","onmouseout","this.style.transform='scale(1)'","title","Voir le profil",2,"width","40px","height","40px","border-radius","50%","object-fit","cover","border","2px solid transparent","cursor","pointer","transition","transform 0.2s ease",3,"src","alt","click"],["style","\n            position: absolute;\n            bottom: 0;\n            right: 0;\n            width: 12px;\n            height: 12px;\n            background: #10b981;\n            border: 2px solid transparent;\n            border-radius: 50%;\n            animation: pulse 2s infinite;\n          ",4,"ngIf"],[2,"flex","1","min-width","0"],[2,"font-weight","600","color","#111827","margin","0","font-size","16px","white-space","nowrap","overflow","hidden","text-overflow","ellipsis"],[2,"font-size","14px","color","#6b7280","margin-top","2px"],["style","display: flex; align-items: center; gap: 4px; color: #10b981",4,"ngIf"],[4,"ngIf"],[2,"display","flex","align-items","center","gap","8px"],["title","Appel vid\xe9o","onmouseover","this.style.transform='scale(1.1)'; this.style.boxShadow='0 6px 20px rgba(59, 130, 246, 0.4)'","onmouseout","this.style.transform='scale(1)'; this.style.boxShadow='0 4px 12px rgba(59, 130, 246, 0.3)'",2,"padding","10px","border-radius","50%","border","none","background","linear-gradient(135deg, #3b82f6, #1d4ed8)","color","white","cursor","pointer","transition","all 0.3s","box-shadow","0 4px 12px rgba(59, 130, 246, 0.3)","width","40px","height","40px","display","flex","align-items","center","justify-content","center",3,"click"],[1,"fas","fa-video",2,"font-size","14px"],["title","Appel vocal","onmouseover","this.style.transform='scale(1.1)'; this.style.boxShadow='0 6px 20px rgba(16, 185, 129, 0.4)'","onmouseout","this.style.transform='scale(1)'; this.style.boxShadow='0 4px 12px rgba(16, 185, 129, 0.3)'",2,"padding","10px","border-radius","50%","border","none","background","linear-gradient(135deg, #10b981, #047857)","color","white","cursor","pointer","transition","all 0.3s","box-shadow","0 4px 12px rgba(16, 185, 129, 0.3)","width","40px","height","40px","display","flex","align-items","center","justify-content","center",3,"click"],[1,"fas","fa-phone",2,"font-size","14px"],["title","Rechercher",2,"padding","8px","border-radius","50%","border","none","background","transparent","color","#6b7280","cursor","pointer","transition","all 0.2s",3,"click"],[1,"fas","fa-search"],["title","Recharger la conversation","onmouseover","this.style.background='#f3f4f6'; this.style.color='#374151'","onmouseout","this.style.background='transparent'; this.style.color='#6b7280'",2,"padding","8px","border-radius","50%","border","none","background","transparent","color","#6b7280","cursor","pointer","transition","all 0.2s",3,"disabled","click"],[1,"fas","fa-sync-alt"],["title","Menu",2,"padding","8px","border-radius","50%","border","none","background","transparent","color","#6b7280","cursor","pointer","transition","all 0.2s","position","relative",3,"click"],[1,"fas","fa-ellipsis-v"],["style","\n        position: absolute;\n        top: 64px;\n        right: 16px;\n        background: #ffffff;\n        border-radius: 16px;\n        box-shadow: 0 20px 25px rgba(0, 0, 0, 0.1);\n        border: 1px solid #e5e7eb;\n        z-index: 50;\n        min-width: 192px;\n      ",4,"ngIf"],[2,"flex","1","overflow-y","auto","padding","16px","position","relative",3,"scroll","dragover","dragleave","drop"],["messagesContainer",""],["style","\n        position: absolute;\n        top: 0;\n        left: 0;\n        right: 0;\n        bottom: 0;\n        background: rgba(34, 197, 94, 0.2);\n        border: 2px dashed transparent;\n        border-radius: 8px;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        z-index: 50;\n        backdrop-filter: blur(2px);\n        animation: pulse 2s infinite;\n      ",4,"ngIf"],["style","\n        display: flex;\n        flex-direction: column;\n        align-items: center;\n        justify-content: center;\n        padding: 32px 0;\n      ",4,"ngIf"],["style","\n        display: flex;\n        flex-direction: column;\n        align-items: center;\n        justify-content: center;\n        padding: 64px 0;\n      ",4,"ngIf"],["style","display: flex; flex-direction: column; gap: 8px",4,"ngIf"],[2,"background","#ffffff","border-top","1px solid #e5e7eb","padding","16px"],[2,"display","flex","align-items","end","gap","12px",3,"formGroup","ngSubmit"],[2,"display","flex","gap","8px"],["type","button","title","\xc9mojis","onmouseover","this.style.background=this.style.background === 'transparent' ? '#f3f4f6' : this.style.background","onmouseout","this.style.background=this.style.background === '#f3f4f6' ? 'transparent' : this.style.background",2,"padding","8px","border-radius","50%","border","none","background","transparent","color","#6b7280","cursor","pointer","transition","all 0.2s",3,"click"],[1,"fas","fa-smile"],["type","button","title","Pi\xe8ces jointes","onmouseover","this.style.background=this.style.background === 'transparent' ? '#f3f4f6' : this.style.background","onmouseout","this.style.background=this.style.background === '#f3f4f6' ? 'transparent' : this.style.background",2,"padding","8px","border-radius","50%","border","none","background","transparent","color","#6b7280","cursor","pointer","transition","all 0.2s",3,"click"],[1,"fas","fa-paperclip"],["type","button","title","Maintenir pour enregistrer un message vocal","onmouseover","if(!this.style.background || this.style.background === 'transparent') this.style.background='#f3f4f6'","onmouseout","if(this.style.background === '#f3f4f6') this.style.background='transparent'",2,"padding","8px","border-radius","50%","border","none","background","transparent","color","#6b7280","cursor","pointer","transition","all 0.2s","position","relative",3,"mousedown","mouseup","mouseleave","touchstart","touchend","touchcancel"],["style","\n              position: absolute;\n              top: -2px;\n              right: -2px;\n              width: 8px;\n              height: 8px;\n              background: #ef4444;\n              border-radius: 50%;\n              animation: ping 1s infinite;\n            ",4,"ngIf"],[2,"flex","1","position","relative"],["formControlName","content","placeholder","Tapez votre message...",2,"width","100%","min-height","44px","max-height","120px","padding","12px 16px","border","1px solid #e5e7eb","border-radius","22px","resize","none","outline","none","font-family","inherit","font-size","14px","line-height","1.4","background","#ffffff","color","#111827","transition","all 0.2s",3,"disabled","keydown","input","focus"],["type","submit","title","Envoyer","onmouseover","if(!this.disabled) this.style.background='#2563eb'","onmouseout","if(!this.disabled) this.style.background='#3b82f6'",2,"padding","12px","border-radius","50%","border","none","background","#3b82f6","color","#ffffff","cursor","pointer","transition","all 0.2s","display","flex","align-items","center","justify-content","center","min-width","44px","min-height","44px",3,"disabled"],["class","fas fa-paper-plane",4,"ngIf"],["style","\n            width: 16px;\n            height: 16px;\n            border: 2px solid #ffffff;\n            border-top-color: transparent;\n            border-radius: 50%;\n            animation: spin 1s linear infinite;\n          ",4,"ngIf"],["style","\n        position: absolute;\n        bottom: 80px;\n        left: 16px;\n        background: #ffffff;\n        border-radius: 16px;\n        box-shadow: 0 20px 25px rgba(0, 0, 0, 0.1);\n        border: 1px solid #e5e7eb;\n        z-index: 50;\n        width: 320px;\n        max-height: 300px;\n        overflow-y: auto;\n      ",4,"ngIf"],["style","\n        position: absolute;\n        bottom: 80px;\n        left: 60px;\n        background: #ffffff;\n        border-radius: 16px;\n        box-shadow: 0 20px 25px rgba(0, 0, 0, 0.1);\n        border: 1px solid #e5e7eb;\n        z-index: 50;\n        min-width: 200px;\n      ",4,"ngIf"],["type","file","multiple","",2,"display","none",3,"accept","change"],["fileInput",""],["style","\n      position: fixed;\n      top: 0;\n      left: 0;\n      right: 0;\n      bottom: 0;\n      background: rgba(0, 0, 0, 0.25);\n      z-index: 40;\n    ",3,"click",4,"ngIf"],["style","\n      position: fixed;\n      bottom: 100px;\n      left: 50%;\n      transform: translateX(-50%);\n      background: linear-gradient(135deg, #f59e0b, #d97706);\n      color: white;\n      padding: 20px 24px;\n      border-radius: 20px;\n      box-shadow: 0 20px 25px rgba(0, 0, 0, 0.2);\n      z-index: 60;\n      display: flex;\n      align-items: center;\n      gap: 16px;\n      min-width: 280px;\n      animation: slideInUp 0.3s ease-out;\n    ",4,"ngIf"],["class","video-call-overlay","style","\n    position: fixed;\n    top: 0;\n    left: 0;\n    width: 100vw;\n    height: 100vh;\n    background: linear-gradient(135deg, #1a1a2e, #16213e);\n    z-index: 9999;\n    display: flex;\n    flex-direction: column;\n  ",4,"ngIf"],["id","localVideoHidden","autoplay","","muted","","playsinline","","style","\n    position: absolute;\n    top: -9999px;\n    left: -9999px;\n    width: 1px;\n    height: 1px;\n  ",4,"ngIf"],["id","remoteVideoHidden","autoplay","","playsinline","","style","\n    position: absolute;\n    top: -9999px;\n    left: -9999px;\n    width: 1px;\n    height: 1px;\n  ",4,"ngIf"],[2,"position","absolute","bottom","0","right","0","width","12px","height","12px","background","#10b981","border","2px solid transparent","border-radius","50%","animation","pulse 2s infinite"],[2,"display","flex","align-items","center","gap","4px","color","#10b981"],[2,"display","flex","gap","2px"],[2,"width","4px","height","4px","background","#10b981","border-radius","50%","animation","bounce 1s infinite"],[2,"width","4px","height","4px","background","#10b981","border-radius","50%","animation","bounce 1s infinite 0.1s"],[2,"width","4px","height","4px","background","#10b981","border-radius","50%","animation","bounce 1s infinite 0.2s"],[2,"position","absolute","top","64px","right","16px","background","#ffffff","border-radius","16px","box-shadow","0 20px 25px rgba(0, 0, 0, 0.1)","border","1px solid #e5e7eb","z-index","50","min-width","192px"],[2,"padding","8px"],["onmouseover","this.style.background='#f3f4f6'","onmouseout","this.style.background='transparent'",2,"width","100%","display","flex","align-items","center","gap","12px","padding","8px 12px","border-radius","8px","border","none","background","transparent","cursor","pointer","transition","all 0.2s","text-align","left",3,"click"],[1,"fas","fa-search",2,"color","#3b82f6"],[2,"color","#374151"],["onmouseover","this.style.background='#f3f4f6'","onmouseout","this.style.background='transparent'",2,"width","100%","display","flex","align-items","center","gap","12px","padding","8px 12px","border-radius","8px","border","none","background","transparent","cursor","pointer","transition","all 0.2s","text-align","left"],[1,"fas","fa-user",2,"color","#10b981"],[2,"margin","8px 0","border-color","#e5e7eb"],[1,"fas","fa-cog",2,"color","#6b7280"],[2,"position","absolute","top","0","left","0","right","0","bottom","0","background","rgba(34, 197, 94, 0.2)","border","2px dashed transparent","border-radius","8px","display","flex","align-items","center","justify-content","center","z-index","50","backdrop-filter","blur(2px)","animation","pulse 2s infinite"],[2,"text-align","center","background","#ffffff","padding","24px","border-radius","12px","box-shadow","0 10px 15px rgba(0, 0, 0, 0.1)","border","1px solid transparent"],[1,"fas","fa-cloud-upload-alt",2,"font-size","48px","color","#10b981","margin-bottom","12px","animation","bounce 1s infinite"],[2,"font-size","20px","font-weight","bold","color","#047857","margin-bottom","8px"],[2,"font-size","14px","color","#10b981"],[2,"display","flex","flex-direction","column","align-items","center","justify-content","center","padding","32px 0"],[2,"width","32px","height","32px","border","2px solid #e5e7eb","border-bottom-color","#10b981","border-radius","50%","animation","spin 1s linear infinite","margin-bottom","16px"],[2,"color","#6b7280"],[2,"display","flex","flex-direction","column","align-items","center","justify-content","center","padding","64px 0"],[2,"font-size","64px","color","#d1d5db","margin-bottom","16px"],[1,"fas","fa-comments"],[2,"font-size","20px","font-weight","600","color","#374151","margin-bottom","8px"],[2,"color","#6b7280","text-align","center"],[2,"display","flex","flex-direction","column","gap","8px"],[4,"ngFor","ngForOf","ngForTrackBy"],["style","display: flex; align-items: start; gap: 8px",4,"ngIf"],["style","display: flex; justify-content: center; margin: 16px 0",4,"ngIf"],[2,"display","flex",3,"id","click","contextmenu"],["style","margin-right: 8px; flex-shrink: 0",4,"ngIf"],[2,"max-width","320px","padding","12px 16px","border-radius","18px","box-shadow","0 1px 3px rgba(0, 0, 0, 0.1)","position","relative","word-wrap","break-word","overflow-wrap","break-word","border","none"],["style","\n                font-size: 12px;\n                font-weight: 600;\n                margin-bottom: 4px;\n                opacity: 0.75;\n              ",3,"color",4,"ngIf"],["style","word-wrap: break-word; overflow-wrap: break-word",4,"ngIf"],["style","margin: 8px 0",4,"ngIf"],["style","\n                display: flex;\n                align-items: center;\n                gap: 12px;\n                padding: 12px;\n                background: rgba(255, 255, 255, 0.1);\n                border-radius: 12px;\n                margin: 8px 0;\n                min-width: 200px;\n                max-width: 280px;\n              ",4,"ngIf"],[2,"display","flex","align-items","center","justify-content","flex-end","gap","4px","margin-top","4px","font-size","12px","opacity","0.75"],["style","display: flex; align-items: center",4,"ngIf"],[2,"display","flex","justify-content","center","margin","16px 0"],[2,"background","#ffffff","padding","4px 12px","border-radius","20px","box-shadow","0 1px 3px rgba(0, 0, 0, 0.1)"],[2,"font-size","12px","color","#6b7280"],[2,"margin-right","8px","flex-shrink","0"],["onmouseover","this.style.transform='scale(1.05)'","onmouseout","this.style.transform='scale(1)'",2,"width","32px","height","32px","border-radius","50%","object-fit","cover","cursor","pointer","transition","transform 0.2s",3,"src","alt","click"],[2,"font-size","12px","font-weight","600","margin-bottom","4px","opacity","0.75"],[2,"word-wrap","break-word","overflow-wrap","break-word"],[3,"innerHTML"],[2,"margin","8px 0"],["onmouseover","this.style.transform='scale(1.02)'","onmouseout","this.style.transform='scale(1)'",2,"max-width","280px","height","auto","border-radius","12px","cursor","pointer","transition","transform 0.2s",3,"src","alt","click","load","error"],["style","font-size: 14px; margin-top: 8px; line-height: 1.4",3,"color","innerHTML",4,"ngIf"],[2,"font-size","14px","margin-top","8px","line-height","1.4",3,"innerHTML"],[2,"display","flex","align-items","center","gap","12px","padding","12px","background","rgba(255, 255, 255, 0.1)","border-radius","12px","margin","8px 0","min-width","200px","max-width","280px"],["onmouseover","this.style.background='rgba(255, 255, 255, 0.3)'","onmouseout","this.style.background='rgba(255, 255, 255, 0.2)'","title","Lire/Pause",2,"width","40px","height","40px","border-radius","50%","border","none","background","rgba(255, 255, 255, 0.2)","color","inherit","cursor","pointer","display","flex","align-items","center","justify-content","center","transition","all 0.2s","flex-shrink","0",3,"click"],[2,"font-size","14px"],[2,"flex","1","display","flex","align-items","center","gap","2px","height","24px","overflow","hidden"],["style","\n                    width: 3px;\n                    background: currentColor;\n                    border-radius: 2px;\n                    opacity: 0.7;\n                    transition: height 0.3s ease;\n                  ",3,"height","animation","animation-delay",4,"ngFor","ngForOf"],[2,"display","flex","align-items","center","gap","8px","flex-shrink","0"],[2,"font-size","12px","opacity","0.8","min-width","40px","text-align","right"],["style","\n                    padding: 4px 8px;\n                    border-radius: 12px;\n                    border: none;\n                    background: rgba(255, 255, 255, 0.2);\n                    color: inherit;\n                    cursor: pointer;\n                    font-size: 11px;\n                    transition: all 0.2s;\n                  ","onmouseover","this.style.background='rgba(255, 255, 255, 0.3)'","onmouseout","this.style.background='rgba(255, 255, 255, 0.2)'","title","Changer la vitesse",3,"click",4,"ngIf"],[2,"width","3px","background","currentColor","border-radius","2px","opacity","0.7","transition","height 0.3s ease"],["onmouseover","this.style.background='rgba(255, 255, 255, 0.3)'","onmouseout","this.style.background='rgba(255, 255, 255, 0.2)'","title","Changer la vitesse",2,"padding","4px 8px","border-radius","12px","border","none","background","rgba(255, 255, 255, 0.2)","color","inherit","cursor","pointer","font-size","11px","transition","all 0.2s",3,"click"],[2,"display","flex","align-items","center"],["class","fas fa-clock","title","Envoi en cours",4,"ngIf"],["class","fas fa-check","title","Envoy\xe9",4,"ngIf"],["class","fas fa-check-double","title","Livr\xe9",4,"ngIf"],["class","fas fa-check-double","style","color: #3b82f6","title","Lu",4,"ngIf"],["title","Envoi en cours",1,"fas","fa-clock"],["title","Envoy\xe9",1,"fas","fa-check"],["title","Livr\xe9",1,"fas","fa-check-double"],["title","Lu",1,"fas","fa-check-double",2,"color","#3b82f6"],[2,"display","flex","align-items","start","gap","8px"],[2,"width","32px","height","32px","border-radius","50%","object-fit","cover",3,"src","alt"],[2,"background","#ffffff","padding","12px 16px","border-radius","18px","box-shadow","0 1px 3px rgba(0, 0, 0, 0.1)"],[2,"display","flex","gap","4px"],[2,"width","8px","height","8px","background","#6b7280","border-radius","50%","animation","bounce 1s infinite"],[2,"width","8px","height","8px","background","#6b7280","border-radius","50%","animation","bounce 1s infinite 0.1s"],[2,"width","8px","height","8px","background","#6b7280","border-radius","50%","animation","bounce 1s infinite 0.2s"],[2,"position","absolute","top","-2px","right","-2px","width","8px","height","8px","background","#ef4444","border-radius","50%","animation","ping 1s infinite"],[1,"fas","fa-paper-plane"],[2,"width","16px","height","16px","border","2px solid #ffffff","border-top-color","transparent","border-radius","50%","animation","spin 1s linear infinite"],[2,"position","absolute","bottom","80px","left","16px","background","#ffffff","border-radius","16px","box-shadow","0 20px 25px rgba(0, 0, 0, 0.1)","border","1px solid #e5e7eb","z-index","50","width","320px","max-height","300px","overflow-y","auto"],[2,"padding","16px"],[2,"margin","0 0 12px 0","font-size","14px","font-weight","600","color","#374151"],[2,"display","grid","grid-template-columns","repeat(8, 1fr)","gap","8px"],["style","\n              padding: 8px;\n              border: none;\n              background: transparent;\n              border-radius: 8px;\n              cursor: pointer;\n              font-size: 20px;\n              transition: all 0.2s;\n            ","onmouseover","this.style.background='#f3f4f6'","onmouseout","this.style.background='transparent'",3,"title","click",4,"ngFor","ngForOf"],["onmouseover","this.style.background='#f3f4f6'","onmouseout","this.style.background='transparent'",2,"padding","8px","border","none","background","transparent","border-radius","8px","cursor","pointer","font-size","20px","transition","all 0.2s",3,"title","click"],[2,"position","absolute","bottom","80px","left","60px","background","#ffffff","border-radius","16px","box-shadow","0 20px 25px rgba(0, 0, 0, 0.1)","border","1px solid #e5e7eb","z-index","50","min-width","200px"],[2,"display","grid","grid-template-columns","repeat(2, 1fr)","gap","12px"],["onmouseover","this.style.background='#f3f4f6'","onmouseout","this.style.background='transparent'",2,"display","flex","flex-direction","column","align-items","center","gap","8px","padding","16px","border","none","background","transparent","border-radius","12px","cursor","pointer","transition","all 0.2s",3,"click"],[2,"width","48px","height","48px","background","#dbeafe","border-radius","50%","display","flex","align-items","center","justify-content","center"],[1,"fas","fa-image",2,"color","#3b82f6","font-size","20px"],[2,"font-size","14px","font-weight","500","color","#374151"],[2,"width","48px","height","48px","background","#fef3c7","border-radius","50%","display","flex","align-items","center","justify-content","center"],[1,"fas","fa-file-alt",2,"color","#f59e0b","font-size","20px"],[2,"width","48px","height","48px","background","#dcfce7","border-radius","50%","display","flex","align-items","center","justify-content","center"],[1,"fas","fa-camera",2,"color","#10b981","font-size","20px"],[2,"position","fixed","top","0","left","0","right","0","bottom","0","background","rgba(0, 0, 0, 0.25)","z-index","40",3,"click"],[2,"position","fixed","bottom","100px","left","50%","transform","translateX(-50%)","background","linear-gradient(135deg, #f59e0b, #d97706)","color","white","padding","20px 24px","border-radius","20px","box-shadow","0 20px 25px rgba(0, 0, 0, 0.2)","z-index","60","display","flex","align-items","center","gap","16px","min-width","280px","animation","slideInUp 0.3s ease-out"],[2,"width","48px","height","48px","background","rgba(255, 255, 255, 0.2)","border-radius","50%","display","flex","align-items","center","justify-content","center","animation","pulse 1s infinite"],[1,"fas","fa-microphone",2,"font-size","20px"],[2,"flex","1"],[2,"font-size","18px","font-weight","bold","margin-bottom","4px"],[2,"display","flex","align-items","center","gap","2px","height","20px"],["style","\n            width: 3px;\n            background: rgba(255, 255, 255, 0.8);\n            border-radius: 2px;\n            transition: height 0.3s ease;\n          ",3,"height","animation","animation-delay",4,"ngFor","ngForOf"],[2,"font-size","12px","opacity","0.8","margin-top","4px"],["onmouseover","this.style.background='rgba(239, 68, 68, 1)'","onmouseout","this.style.background='rgba(239, 68, 68, 0.8)'","title","Annuler l'enregistrement",2,"width","40px","height","40px","border-radius","50%","border","none","background","rgba(239, 68, 68, 0.8)","color","white","cursor","pointer","display","flex","align-items","center","justify-content","center","transition","all 0.2s",3,"click"],[1,"fas","fa-times",2,"font-size","16px"],["onmouseover","this.style.background='rgba(34, 197, 94, 1)'","onmouseout","this.style.background='rgba(34, 197, 94, 0.8)'","title","Envoyer le message vocal",2,"width","40px","height","40px","border-radius","50%","border","none","background","rgba(34, 197, 94, 0.8)","color","white","cursor","pointer","display","flex","align-items","center","justify-content","center","transition","all 0.2s",3,"click"],[1,"fas","fa-paper-plane",2,"font-size","16px"],[2,"width","3px","background","rgba(255, 255, 255, 0.8)","border-radius","2px","transition","height 0.3s ease"],[1,"video-call-overlay",2,"position","fixed","top","0","left","0","width","100vw","height","100vh","background","linear-gradient(135deg, #1a1a2e, #16213e)","z-index","9999","display","flex","flex-direction","column"],[2,"padding","20px","text-align","center","color","white","background","rgba(0, 0, 0, 0.3)"],[2,"margin","0","font-size","18px"],[2,"margin","5px 0 0 0","opacity","0.8"],[2,"flex","1","position","relative","display","flex","align-items","center","justify-content","center"],["id","remoteVideo","autoplay","","playsinline","",2,"width","100%","height","100%","object-fit","cover","background","#000"],["id","localVideo","autoplay","","muted","","playsinline","",2,"position","absolute","top","20px","right","20px","width","200px","height","150px","border-radius","10px","border","2px solid #00ff88","object-fit","cover","background","#000","z-index","10"],["style","\n        position: absolute;\n        top: 50%;\n        left: 50%;\n        transform: translate(-50%, -50%);\n        text-align: center;\n        color: white;\n      ",4,"ngIf"],[2,"padding","30px","display","flex","justify-content","center","gap","20px","background","rgba(0, 0, 0, 0.3)"],[2,"width","60px","height","60px","border-radius","50%","border","none","color","white","font-size","20px","cursor","pointer","transition","all 0.3s","box-shadow","0 4px 15px rgba(0, 0, 0, 0.3)",3,"title","click"],["title","Raccrocher",2,"width","60px","height","60px","border-radius","50%","border","none","background","#ff4757","color","white","font-size","20px","cursor","pointer","transition","all 0.3s","box-shadow","0 4px 15px rgba(0, 0, 0, 0.3)",3,"click"],[1,"fas","fa-phone-slash"],[2,"position","absolute","top","50%","left","50%","transform","translate(-50%, -50%)","text-align","center","color","white"],[2,"width","120px","height","120px","border-radius","50%","background","linear-gradient(135deg, #667eea, #764ba2)","margin","0 auto 20px","display","flex","align-items","center","justify-content","center","font-size","48px"],[2,"opacity","0.8"],["id","localVideoHidden","autoplay","","muted","","playsinline","",2,"position","absolute","top","-9999px","left","-9999px","width","1px","height","1px"],["localVideoHidden",""],["id","remoteVideoHidden","autoplay","","playsinline","",2,"position","absolute","top","-9999px","left","-9999px","width","1px","height","1px"],["remoteVideoHidden",""]],template:function(i,n){1&i&&(t.nrm(0,"video",0,1)(2,"video",2,3),t.j41(4,"div",4)(5,"header",5)(6,"button",6),t.bIt("click",function(){return n.goBackToConversations()}),t.nrm(7,"i",7),t.k0s(),t.j41(8,"div",8)(9,"div",9)(10,"img",10),t.bIt("click",function(){return n.openUserProfile(null==n.otherParticipant?null:n.otherParticipant.id)}),t.k0s(),t.DNE(11,U,1,0,"div",11),t.k0s(),t.j41(12,"div",12)(13,"h3",13),t.EFF(14),t.k0s(),t.j41(15,"div",14),t.DNE(16,A,7,0,"div",15),t.DNE(17,$,2,1,"span",16),t.k0s()()(),t.j41(18,"div",17)(19,"button",18),t.bIt("click",function(){return n.startVideoCall()}),t.nrm(20,"i",19),t.k0s(),t.j41(21,"button",20),t.bIt("click",function(){return n.startVoiceCall()}),t.nrm(22,"i",21),t.k0s(),t.j41(23,"button",22),t.bIt("click",function(){return n.toggleSearch()}),t.nrm(24,"i",23),t.k0s(),t.j41(25,"button",24),t.bIt("click",function(){return n.reloadConversation()}),t.nrm(26,"i",25),t.k0s(),t.j41(27,"button",26),t.bIt("click",function(){return n.toggleMainMenu()}),t.nrm(28,"i",27),t.k0s()(),t.DNE(29,G,15,0,"div",28),t.k0s(),t.j41(30,"main",29,30),t.bIt("scroll",function(s){return n.onScroll(s)})("dragover",function(s){return n.onDragOver(s)})("dragleave",function(s){return n.onDragLeave(s)})("drop",function(s){return n.onDrop(s)}),t.DNE(32,z,7,0,"div",31),t.DNE(33,N,4,0,"div",32),t.DNE(34,L,7,1,"div",33),t.DNE(35,st,3,3,"div",34),t.k0s(),t.j41(36,"footer",35)(37,"form",36),t.bIt("ngSubmit",function(){return n.sendMessage()}),t.j41(38,"div",37)(39,"button",38),t.bIt("click",function(){return n.toggleEmojiPicker()}),t.nrm(40,"i",39),t.k0s(),t.j41(41,"button",40),t.bIt("click",function(){return n.toggleAttachmentMenu()}),t.nrm(42,"i",41),t.k0s(),t.j41(43,"button",42),t.bIt("mousedown",function(s){return n.onRecordStart(s)})("mouseup",function(s){return n.onRecordEnd(s)})("mouseleave",function(s){return n.onRecordCancel(s)})("touchstart",function(s){return n.onRecordStart(s)})("touchend",function(s){return n.onRecordEnd(s)})("touchcancel",function(s){return n.onRecordCancel(s)}),t.nrm(44,"i"),t.DNE(45,at,1,0,"div",43),t.k0s()(),t.j41(46,"div",44)(47,"textarea",45),t.bIt("keydown",function(s){return n.onInputKeyDown(s)})("input",function(s){return n.onInputChange(s)})("focus",function(){return n.onInputFocus()}),t.k0s()(),t.j41(48,"button",46),t.DNE(49,ct,1,0,"i",47),t.DNE(50,lt,1,0,"div",48),t.k0s()(),t.DNE(51,ut,6,1,"div",49),t.DNE(52,gt,20,0,"div",50),t.j41(53,"input",51,52),t.bIt("change",function(s){return n.onFileSelected(s)}),t.k0s()(),t.DNE(55,ht,1,0,"div",53),t.DNE(56,pt,15,3,"div",54),t.k0s(),t.DNE(57,_t,19,15,"div",55),t.DNE(58,bt,2,0,"video",56),t.DNE(59,Ct,2,0,"video",57)),2&i&&(t.R7$(10),t.Y8G("src",(null==n.otherParticipant?null:n.otherParticipant.image)||"assets/images/default-avatar.png",t.B4B)("alt",null==n.otherParticipant?null:n.otherParticipant.username),t.R7$(1),t.Y8G("ngIf",null==n.otherParticipant?null:n.otherParticipant.isOnline),t.R7$(3),t.SpI(" ",(null==n.otherParticipant?null:n.otherParticipant.username)||"Utilisateur"," "),t.R7$(2),t.Y8G("ngIf",n.isUserTyping),t.R7$(1),t.Y8G("ngIf",!n.isUserTyping),t.R7$(6),t.xc7("background",n.searchMode?"#dcfce7":"transparent")("color",n.searchMode?"#16a34a":"#6b7280"),t.R7$(2),t.xc7("opacity",n.isLoading?"0.5":"1"),t.Y8G("disabled",n.isLoading),t.R7$(1),t.xc7("animation",n.isLoading?"spin 1s linear infinite":"none"),t.R7$(1),t.xc7("background",n.showMainMenu?"#dcfce7":"transparent")("color",n.showMainMenu?"#16a34a":"#6b7280"),t.R7$(2),t.Y8G("ngIf",n.showMainMenu),t.R7$(1),t.xc7("background",n.isDragOver?"rgba(34, 197, 94, 0.1)":"transparent"),t.R7$(2),t.Y8G("ngIf",n.isDragOver),t.R7$(1),t.Y8G("ngIf",n.isLoading),t.R7$(1),t.Y8G("ngIf",!n.isLoading&&0===n.messages.length),t.R7$(1),t.Y8G("ngIf",!n.isLoading&&n.messages.length>0),t.R7$(2),t.Y8G("formGroup",n.messageForm),t.R7$(2),t.xc7("background",n.showEmojiPicker?"#dcfce7":"transparent")("color",n.showEmojiPicker?"#16a34a":"#6b7280"),t.R7$(2),t.xc7("background",n.showAttachmentMenu?"#dcfce7":"transparent")("color",n.showAttachmentMenu?"#16a34a":"#6b7280"),t.R7$(2),t.xc7("background",n.isRecordingVoice?"#fef3c7":"transparent")("color",n.isRecordingVoice?"#f59e0b":"#6b7280")("transform",n.isRecordingVoice?"scale(1.1)":"scale(1)"),t.R7$(1),t.HbH(n.isRecordingVoice?"fas fa-stop":"fas fa-microphone"),t.xc7("animation",n.isRecordingVoice?"pulse 1s infinite":"none"),t.R7$(1),t.Y8G("ngIf",n.isRecordingVoice),t.R7$(2),t.Y8G("disabled",n.isInputDisabled()),t.R7$(1),t.xc7("background",!n.messageForm.valid||n.isSendingMessage?"#9ca3af":"#3b82f6")("cursor",!n.messageForm.valid||n.isSendingMessage?"not-allowed":"pointer"),t.Y8G("disabled",!n.messageForm.valid||n.isSendingMessage),t.R7$(1),t.Y8G("ngIf",!n.isSendingMessage),t.R7$(1),t.Y8G("ngIf",n.isSendingMessage),t.R7$(1),t.Y8G("ngIf",n.showEmojiPicker),t.R7$(1),t.Y8G("ngIf",n.showAttachmentMenu),t.R7$(1),t.Y8G("accept",n.getFileAcceptTypes()),t.R7$(2),t.Y8G("ngIf",n.showEmojiPicker||n.showAttachmentMenu||n.showMainMenu),t.R7$(1),t.Y8G("ngIf",n.isRecordingVoice),t.R7$(1),t.Y8G("ngIf",n.activeCall&&"VIDEO"===n.activeCall.type),t.R7$(1),t.Y8G("ngIf",!n.activeCall||"VIDEO"!==n.activeCall.type),t.R7$(1),t.Y8G("ngIf",!n.activeCall||"VIDEO"!==n.activeCall.type))},dependencies:[f.Sq,f.bT,l.qT,l.me,l.BC,l.cb,l.j4,l.JD],styles:["@keyframes _ngcontent-%COMP%_pulse {\n      0%,\n      100% {\n        opacity: 1;\n      }\n      50% {\n        opacity: 0.5;\n      }\n    }\n    @keyframes _ngcontent-%COMP%_bounce {\n      0%,\n      20%,\n      53%,\n      80%,\n      100% {\n        transform: translateY(0);\n      }\n      40%,\n      43% {\n        transform: translateY(-8px);\n      }\n      70% {\n        transform: translateY(-4px);\n      }\n    }\n    @keyframes _ngcontent-%COMP%_spin {\n      from {\n        transform: rotate(0deg);\n      }\n      to {\n        transform: rotate(360deg);\n      }\n    }\n    @keyframes _ngcontent-%COMP%_ping {\n      75%,\n      100% {\n        transform: scale(2);\n        opacity: 0;\n      }\n    }\n    @keyframes _ngcontent-%COMP%_slideInUp {\n      from {\n        transform: translateX(-50%) translateY(20px);\n        opacity: 0;\n      }\n      to {\n        transform: translateX(-50%) translateY(0);\n        opacity: 1;\n      }\n    }"]})}}return r})();var vt=u(4412),Mt=u(6354),E=u(9271),S=u(4798),j=u(487);function kt(r,a){if(1&r&&(t.j41(0,"div",31)(1,"span",32),t.EFF(2),t.k0s(),t.nrm(3,"div",33),t.k0s()),2&r){const e=a.ngIf;t.R7$(2),t.SpI(" ",e," ")}}function xt(r,a){1&r&&(t.j41(0,"div",34),t.nrm(1,"div",35),t.j41(2,"p",36),t.EFF(3,"Chargement des conversations..."),t.k0s()())}function Pt(r,a){if(1&r){const e=t.RV6();t.j41(0,"div",37)(1,"div",38),t.nrm(2,"i",39),t.k0s(),t.j41(3,"div",40)(4,"h3",41),t.EFF(5," Erreur de chargement des conversations "),t.k0s(),t.j41(6,"p",42),t.EFF(7),t.k0s(),t.j41(8,"button",43),t.bIt("click",function(){t.eBV(e);const n=t.XpG();return t.Njj(n.loadConversations())}),t.nrm(9,"i",44),t.EFF(10," R\xe9essayer "),t.k0s()()()}if(2&r){const e=t.XpG();t.R7$(7),t.SpI(" ",e.error," ")}}function Ot(r,a){if(1&r){const e=t.RV6();t.j41(0,"div",45)(1,"div",46),t.nrm(2,"i",47),t.k0s(),t.j41(3,"h3",48),t.EFF(4,"Aucune conversation"),t.k0s(),t.j41(5,"p",49),t.EFF(6," D\xe9marrez une nouvelle conversation pour communiquer "),t.k0s(),t.j41(7,"button",50),t.bIt("click",function(){t.eBV(e);const n=t.XpG();return t.Njj(n.startNewConversation())}),t.nrm(8,"i",51),t.EFF(9," Nouvelle Conversation "),t.k0s()()}}function yt(r,a){1&r&&(t.j41(0,"div",52)(1,"div",46),t.nrm(2,"i",53),t.k0s(),t.j41(3,"h3",48),t.EFF(4,"Aucun r\xe9sultat trouv\xe9"),t.k0s(),t.j41(5,"p",49),t.EFF(6,"Essayez un autre terme de recherche"),t.k0s()())}function wt(r,a){1&r&&t.nrm(0,"div",69)}function It(r,a){1&r&&(t.j41(0,"span",70),t.EFF(1,"Vous: "),t.k0s())}function Et(r,a){if(1&r&&(t.j41(0,"div",71),t.EFF(1),t.k0s()),2&r){const e=t.XpG().$implicit;t.R7$(1),t.SpI(" ",e.unreadCount," ")}}const St=function(r){return{"futuristic-conversation-selected":r}};function jt(r,a){if(1&r){const e=t.RV6();t.j41(0,"li",56),t.bIt("click",function(){const o=t.eBV(e).$implicit,s=t.XpG(2);return t.Njj(s.openConversation(o.id))}),t.j41(1,"div",57)(2,"div",58),t.nrm(3,"img",59),t.DNE(4,wt,1,0,"div",60),t.k0s(),t.j41(5,"div",61)(6,"div",62)(7,"h3",63),t.EFF(8),t.k0s(),t.j41(9,"span",64),t.EFF(10),t.nI1(11,"date"),t.k0s()(),t.j41(12,"div",65)(13,"p",66),t.DNE(14,It,2,0,"span",67),t.EFF(15),t.k0s(),t.DNE(16,Et,2,1,"div",68),t.k0s()()()()}if(2&r){const e=a.$implicit,i=t.XpG(2);let n,o,s;t.Y8G("ngClass",t.eq3(11,St,i.selectedConversationId===e.id)),t.R7$(3),t.Y8G("src",(e.participants?null==(n=i.getOtherParticipant(e.participants))?null:n.image:null)||"assets/images/default-avatar.png",t.B4B),t.R7$(1),t.Y8G("ngIf",e.participants&&(null==(o=i.getOtherParticipant(e.participants))?null:o.isOnline)),t.R7$(4),t.SpI(" ",(e.participants?null==(s=i.getOtherParticipant(e.participants))?null:s.username:null)||"Utilisateur inconnu"," "),t.R7$(2),t.SpI(" ",t.i5U(11,8,null==e.lastMessage?null:e.lastMessage.timestamp,"shortTime")||""," "),t.R7$(4),t.Y8G("ngIf",(null==e.lastMessage||null==e.lastMessage.sender?null:e.lastMessage.sender.id)===i.currentUserId),t.R7$(1),t.SpI(" ",(null==e.lastMessage?null:e.lastMessage.content)||"Pas encore de messages"," "),t.R7$(1),t.Y8G("ngIf",e.unreadCount&&e.unreadCount>0)}}function Tt(r,a){if(1&r&&(t.j41(0,"ul",54),t.DNE(1,jt,17,13,"li",55),t.k0s()),2&r){const e=t.XpG();t.R7$(1),t.Y8G("ngForOf",e.filteredConversations)}}let Rt=(()=>{class r{constructor(e,i,n,o,s,d,h){this.MessageService=e,this.authService=i,this.router=n,this.route=o,this.toastService=s,this.logger=d,this.themeService=h,this.conversations=[],this.filteredConversations=[],this.loading=!0,this.currentUserId=null,this.searchQuery="",this.selectedConversationId=null,this.unreadCount=new vt.t(0),this.unreadCount$=this.unreadCount.asObservable(),this.subscriptions=[],this.isDarkMode$=this.themeService.darkMode$}ngOnInit(){this.currentUserId=this.authService.getCurrentUserId(),this.currentUserId?(this.loadConversations(),this.subscribeToUserStatus(),this.subscribeToConversationUpdates(),this.route.firstChild?.params.subscribe(e=>{this.selectedConversationId=e.conversationId||null})):this.handleError("User not authenticated")}loadConversations(){this.loading=!0,this.error=null;const e=this.MessageService.getConversations().subscribe({next:i=>{this.conversations=Array.isArray(i)?[...i]:[],this.filterConversations(),this.updateUnreadCount(),this.sortConversations(),this.loading=!1},error:i=>{this.error=i,this.loading=!1,this.toastService.showError("Failed to load conversations")}});this.subscriptions.push(e)}filterConversations(){if(!this.searchQuery)return void(this.filteredConversations=[...this.conversations]);const e=this.searchQuery.toLowerCase();this.filteredConversations=this.conversations.filter(i=>(i.participants?this.getOtherParticipant(i.participants):void 0)?.username.toLowerCase().includes(e)||i.lastMessage?.content?.toLowerCase().includes(e)||!1)}updateUnreadCount(){const e=this.conversations.reduce((i,n)=>i+(n.unreadCount||0),0);this.unreadCount.next(e)}sortConversations(){this.conversations.sort((e,i)=>{const n=this.getConversationDate(e);return this.getConversationDate(i).getTime()-n.getTime()}),this.filterConversations()}getConversationDate(e){const i=new Date(0);return e.lastMessage?.timestamp?"string"==typeof e.lastMessage.timestamp?new Date(e.lastMessage.timestamp):e.lastMessage.timestamp:e.updatedAt?"string"==typeof e.updatedAt?new Date(e.updatedAt):e.updatedAt:e.createdAt?"string"==typeof e.createdAt?new Date(e.createdAt):e.createdAt:i}getOtherParticipant(e){if(e&&Array.isArray(e))return e.find(i=>i._id!==this.currentUserId&&i.id!==this.currentUserId)}subscribeToUserStatus(){const e=this.MessageService.subscribeToUserStatus().pipe((0,Mt.T)(i=>this.MessageService.normalizeUser(i))).subscribe({next:i=>{i&&this.updateUserStatus(i)},error:i=>{this.toastService.showError("Connection to status updates lost")}});this.subscriptions.push(e)}subscribeToConversationUpdates(){const e=this.MessageService.subscribeToConversationUpdates("global").subscribe({next:i=>{const n=this.conversations.findIndex(o=>o.id===i.id);n>=0?this.conversations[n]=i:this.conversations.unshift(i),this.sortConversations()},error:i=>{}});this.subscriptions.push(e)}updateUserStatus(e){this.conversations=this.conversations.map(i=>{if(!i.participants)return i;const n=i.participants.map(o=>o._id===e._id||o.id===e._id?{...o,isOnline:e.isOnline,lastActive:e.lastActive}:o);return{...i,participants:n}}),this.filterConversations()}openConversation(e){e&&(this.selectedConversationId=e,this.router.navigate(["chat",e],{relativeTo:this.route}))}startNewConversation(){this.router.navigate(["/messages/users"])}formatLastActive(e){return this.MessageService.formatLastActive(e)}handleError(e,i){this.logger.error("MessagesListComponent",e,i),this.error=e,this.loading=!1,this.toastService.showError(e)}ngOnDestroy(){this.subscriptions.forEach(e=>e.unsubscribe())}static{this.\u0275fac=function(i){return new(i||r)(t.rXU(g.b),t.rXU(E.V),t.rXU(p.Ix),t.rXU(p.nX),t.rXU(M.f),t.rXU(S.g),t.rXU(j.F))}}static{this.\u0275cmp=t.VBU({type:r,selectors:[["app-messages-list"]],decls:45,vars:13,consts:[[1,"flex","h-screen","futuristic-messages-page","relative"],[1,"absolute","inset-0","overflow-hidden","pointer-events-none"],[1,"absolute","top-[15%]","left-[10%]","w-64","h-64","rounded-full","bg-gradient-to-br","from-[#4f5fad]/5","to-transparent","dark:from-[#6d78c9]/3","dark:to-transparent","blur-3xl"],[1,"absolute","bottom-[20%]","right-[10%]","w-80","h-80","rounded-full","bg-gradient-to-tl","from-[#4f5fad]/5","to-transparent","dark:from-[#6d78c9]/3","dark:to-transparent","blur-3xl"],[1,"absolute","inset-0","opacity-5","dark:opacity-[0.03]"],[1,"h-full","grid","grid-cols-12"],[1,"border-r","border-[#4f5fad]","dark:border-[#6d78c9]"],[1,"w-full","md:w-80","lg:w-96","futuristic-sidebar","flex","flex-col","relative","z-10","backdrop-blur-sm"],[1,"futuristic-header","sticky","top-0","z-10","backdrop-blur-sm"],[1,"absolute","top-0","left-0","right-0","h-1","bg-gradient-to-r","from-[#3d4a85]","to-[#4f5fad]","dark:from-[#6d78c9]","dark:to-[#4f5fad]"],[1,"absolute","top-0","left-0","right-0","h-1","bg-gradient-to-r","from-[#3d4a85]","to-[#4f5fad]","dark:from-[#6d78c9]","dark:to-[#4f5fad]","blur-md"],[1,"flex","justify-between","items-center","mb-4"],[1,"futuristic-title","text-xl","font-bold"],[1,"flex","items-center","space-x-2"],["title","Nouvelle conversation",1,"p-2","rounded-full","text-[#4f5fad]","dark:text-[#6d78c9]","hover:bg-[#4f5fad]/10","dark:hover:bg-[#6d78c9]/10","transition-all","relative","group","overflow-hidden",3,"click"],[1,"absolute","inset-0","bg-[#4f5fad]/10","dark:bg-[#6d78c9]/10","opacity-0","group-hover:opacity-100","transition-opacity","rounded-full","blur-md"],[1,"fas","fa-edit","relative","z-10","group-hover:scale-110","transition-transform"],["class","relative",4,"ngIf"],[1,"relative","group"],["type","text","placeholder","Rechercher des conversations...",1,"w-full","pl-10","pr-4","py-2.5","text-sm","rounded-lg","border","border-[#bdc6cc]","dark:border-[#2a2a2a]","bg-white","dark:bg-[#1e1e1e]","text-[#6d6870]","dark:text-[#e0e0e0]","focus:outline-none","focus:border-[#4f5fad]","dark:focus:border-[#6d78c9]","focus:ring-2","focus:ring-[#4f5fad]/20","dark:focus:ring-[#6d78c9]/20","transition-all",3,"ngModel","ngModelChange"],[1,"absolute","inset-y-0","left-0","pl-3","flex","items-center","pointer-events-none"],[1,"fas","fa-search","text-[#bdc6cc]","dark:text-[#6d6870]","group-focus-within:text-[#4f5fad]","dark:group-focus-within:text-[#6d78c9]","transition-colors"],[1,"absolute","inset-y-0","left-0","pl-3","flex","items-center","pointer-events-none","opacity-0","group-focus-within:opacity-100","transition-opacity"],[1,"w-0.5","h-4","bg-gradient-to-b","from-[#3d4a85]","to-[#4f5fad]","dark:from-[#6d78c9]","dark:to-[#4f5fad]","rounded-full"],[1,"flex-1","overflow-y-auto","futuristic-conversations-list"],["class","flex flex-col items-center justify-center h-full p-4",4,"ngIf"],["class","futuristic-error-container",4,"ngIf"],["class","futuristic-empty-state",4,"ngIf"],["class","futuristic-no-results",4,"ngIf"],["class","futuristic-conversations",4,"ngIf"],[1,"flex-1","hidden","md:flex","flex-col","futuristic-main-area"],[1,"relative"],[1,"futuristic-badge"],[1,"absolute","inset-0","bg-[#4f5fad]/30","dark:bg-[#6d78c9]/30","rounded-full","blur-md","transform","scale-150","-z-10"],[1,"flex","flex-col","items-center","justify-center","h-full","p-4"],[1,"futuristic-loading-circle"],[1,"futuristic-loading-text"],[1,"futuristic-error-container"],[1,"futuristic-error-icon"],[1,"fas","fa-exclamation-triangle"],[1,"flex-1"],[1,"futuristic-error-title"],[1,"futuristic-error-message"],[1,"futuristic-retry-button",3,"click"],[1,"fas","fa-sync-alt","mr-1.5"],[1,"futuristic-empty-state"],[1,"futuristic-empty-icon"],[1,"fas","fa-comments"],[1,"futuristic-empty-title"],[1,"futuristic-empty-text"],[1,"futuristic-start-button",3,"click"],[1,"fas","fa-plus-circle","mr-2"],[1,"futuristic-no-results"],[1,"fas","fa-search"],[1,"futuristic-conversations"],["class","futuristic-conversation-item",3,"ngClass","click",4,"ngFor","ngForOf"],[1,"futuristic-conversation-item",3,"ngClass","click"],[1,"flex","items-center"],[1,"futuristic-avatar"],["alt","User avatar",3,"src"],["class","futuristic-online-indicator",4,"ngIf"],[1,"futuristic-conversation-details"],[1,"futuristic-conversation-header"],[1,"futuristic-conversation-name"],[1,"futuristic-conversation-time"],[1,"futuristic-conversation-preview"],[1,"futuristic-conversation-message"],["class","futuristic-you-prefix",4,"ngIf"],["class","futuristic-unread-badge",4,"ngIf"],[1,"futuristic-online-indicator"],[1,"futuristic-you-prefix"],[1,"futuristic-unread-badge"]],template:function(i,n){1&i&&(t.j41(0,"div",0),t.nI1(1,"async"),t.j41(2,"div",1),t.nrm(3,"div",2)(4,"div",3),t.j41(5,"div",4)(6,"div",5),t.nrm(7,"div",6)(8,"div",6)(9,"div",6)(10,"div",6)(11,"div",6)(12,"div",6)(13,"div",6)(14,"div",6)(15,"div",6)(16,"div",6)(17,"div",6),t.k0s()()(),t.j41(18,"div",7)(19,"div",8),t.nrm(20,"div",9)(21,"div",10),t.j41(22,"div",11)(23,"h1",12),t.EFF(24,"Messages"),t.k0s(),t.j41(25,"div",13)(26,"button",14),t.bIt("click",function(){return n.startNewConversation()}),t.nrm(27,"div",15)(28,"i",16),t.k0s(),t.DNE(29,kt,4,1,"div",17),t.nI1(30,"async"),t.k0s()(),t.j41(31,"div",18)(32,"input",19),t.bIt("ngModelChange",function(s){return n.searchQuery=s})("ngModelChange",function(){return n.filterConversations()}),t.k0s(),t.j41(33,"div",20),t.nrm(34,"i",21),t.k0s(),t.j41(35,"div",22),t.nrm(36,"div",23),t.k0s()()(),t.j41(37,"div",24),t.DNE(38,xt,4,0,"div",25),t.DNE(39,Pt,11,1,"div",26),t.DNE(40,Ot,10,0,"div",27),t.DNE(41,yt,7,0,"div",28),t.DNE(42,Tt,2,1,"ul",29),t.k0s()(),t.j41(43,"div",30),t.nrm(44,"router-outlet"),t.k0s()()),2&i&&(t.AVh("dark",t.bMT(1,9,n.isDarkMode$)),t.R7$(29),t.Y8G("ngIf",t.bMT(30,11,n.unreadCount$)),t.R7$(3),t.Y8G("ngModel",n.searchQuery),t.R7$(6),t.Y8G("ngIf",n.loading),t.R7$(1),t.Y8G("ngIf",n.error),t.R7$(1),t.Y8G("ngIf",!n.loading&&0===n.filteredConversations.length&&!n.searchQuery),t.R7$(1),t.Y8G("ngIf",!n.loading&&0===n.filteredConversations.length&&n.searchQuery),t.R7$(1),t.Y8G("ngIf",!n.loading&&n.filteredConversations.length>0))},dependencies:[f.YU,f.Sq,f.bT,p.n3,l.me,l.BC,l.vS,f.Jj,f.vh],styles:[':not(.dark)[_nghost-%COMP%]   .futuristic-messages-page[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-messages-page[_ngcontent-%COMP%]{background-color:#f0f4f8;color:#6d6870}.dark[_nghost-%COMP%]   .futuristic-messages-page[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-messages-page[_ngcontent-%COMP%]{background-color:var(--dark-bg);color:var(--text-light)}:not(.dark)[_nghost-%COMP%]   .futuristic-sidebar[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-sidebar[_ngcontent-%COMP%]{background-color:#fff;border-color:#4f5fad33}.dark[_nghost-%COMP%]   .futuristic-sidebar[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-sidebar[_ngcontent-%COMP%]{background-color:var(--medium-bg);border-color:#00f7ff33}:not(.dark)[_nghost-%COMP%]   .futuristic-header[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-header[_ngcontent-%COMP%]{background-color:#fff;border-bottom:1px solid rgba(79,95,173,.2);padding:15px}.dark[_nghost-%COMP%]   .futuristic-header[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-header[_ngcontent-%COMP%]{background-color:var(--medium-bg);border-bottom:1px solid rgba(0,247,255,.2);padding:15px}:not(.dark)[_nghost-%COMP%]   .futuristic-title[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-title[_ngcontent-%COMP%]{color:#4f5fad;font-weight:600;letter-spacing:.5px}.dark[_nghost-%COMP%]   .futuristic-title[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-title[_ngcontent-%COMP%]{color:var(--text-light);font-weight:600;letter-spacing:.5px}:not(.dark)[_nghost-%COMP%]   .futuristic-badge[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-badge[_ngcontent-%COMP%]{background:linear-gradient(135deg,#4f5fad,#7826b5);color:#fff;font-size:.75rem;font-weight:500;border-radius:9999px;padding:2px 8px;min-width:1.5rem;text-align:center;box-shadow:0 0 15px #4f5fad66}.dark[_nghost-%COMP%]   .futuristic-badge[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-badge[_ngcontent-%COMP%]{background:var(--primary-gradient);color:#fff;font-size:.75rem;font-weight:500;border-radius:9999px;padding:2px 8px;min-width:1.5rem;text-align:center;box-shadow:var(--glow-effect)}:not(.dark)[_nghost-%COMP%]   .futuristic-conversations-list[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-conversations-list[_ngcontent-%COMP%]{scrollbar-width:thin;scrollbar-color:#4f5fad #f0f4f8}:not(.dark)[_nghost-%COMP%]   .futuristic-conversations-list[_ngcontent-%COMP%]::-webkit-scrollbar, :not(.dark)   [_nghost-%COMP%]   .futuristic-conversations-list[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}:not(.dark)[_nghost-%COMP%]   .futuristic-conversations-list[_ngcontent-%COMP%]::-webkit-scrollbar-track, :not(.dark)   [_nghost-%COMP%]   .futuristic-conversations-list[_ngcontent-%COMP%]::-webkit-scrollbar-track{background:#f0f4f8}:not(.dark)[_nghost-%COMP%]   .futuristic-conversations-list[_ngcontent-%COMP%]::-webkit-scrollbar-thumb, :not(.dark)   [_nghost-%COMP%]   .futuristic-conversations-list[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background-color:#4f5fad;border-radius:10px}.dark[_nghost-%COMP%]   .futuristic-conversations-list[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-conversations-list[_ngcontent-%COMP%]{scrollbar-width:thin;scrollbar-color:var(--accent-color) var(--medium-bg)}.dark[_nghost-%COMP%]   .futuristic-conversations-list[_ngcontent-%COMP%]::-webkit-scrollbar, .dark   [_nghost-%COMP%]   .futuristic-conversations-list[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.dark[_nghost-%COMP%]   .futuristic-conversations-list[_ngcontent-%COMP%]::-webkit-scrollbar-track, .dark   [_nghost-%COMP%]   .futuristic-conversations-list[_ngcontent-%COMP%]::-webkit-scrollbar-track{background:var(--medium-bg)}.dark[_nghost-%COMP%]   .futuristic-conversations-list[_ngcontent-%COMP%]::-webkit-scrollbar-thumb, .dark   [_nghost-%COMP%]   .futuristic-conversations-list[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background-color:var(--accent-color);border-radius:10px}:not(.dark)[_nghost-%COMP%]   .futuristic-loading-circle[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-loading-circle[_ngcontent-%COMP%]{width:40px;height:40px;border-radius:50%;border:3px solid rgba(79,95,173,.1);border-top-color:#4f5fad;animation:_ngcontent-%COMP%_spin-light 1.5s linear infinite}@keyframes _ngcontent-%COMP%_spin-light{to{transform:rotate(360deg)}}:not(.dark)[_nghost-%COMP%]   .futuristic-loading-text[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-loading-text[_ngcontent-%COMP%]{color:#4f5fad;font-size:.875rem;letter-spacing:.5px}.dark[_nghost-%COMP%]   .futuristic-loading-circle[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-loading-circle[_ngcontent-%COMP%]{width:40px;height:40px;border-radius:50%;border:3px solid rgba(0,247,255,.1);border-top-color:var(--accent-color);animation:_ngcontent-%COMP%_spin-dark 1.5s linear infinite}@keyframes _ngcontent-%COMP%_spin-dark{to{transform:rotate(360deg)}}.dark[_nghost-%COMP%]   .futuristic-loading-text[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-loading-text[_ngcontent-%COMP%]{color:var(--accent-color);font-size:.875rem;letter-spacing:.5px}:not(.dark)[_nghost-%COMP%]   .futuristic-error-container[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-error-container[_ngcontent-%COMP%]{margin:15px;padding:15px;background:rgba(255,107,105,.1);border-left:3px solid #ff6b69;border-radius:5px;display:flex;align-items:flex-start}:not(.dark)[_nghost-%COMP%]   .futuristic-error-icon[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-error-icon[_ngcontent-%COMP%]{color:#ff6b69;font-size:1.25rem;margin-right:15px}:not(.dark)[_nghost-%COMP%]   .futuristic-error-title[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-error-title[_ngcontent-%COMP%]{color:#ff6b69;font-size:.875rem;font-weight:600;margin-bottom:5px}:not(.dark)[_nghost-%COMP%]   .futuristic-error-message[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-error-message[_ngcontent-%COMP%]{color:#6d6870;font-size:.8125rem;margin-bottom:10px}:not(.dark)[_nghost-%COMP%]   .futuristic-retry-button[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-retry-button[_ngcontent-%COMP%]{background:rgba(255,107,105,.2);color:#ff6b69;border:none;border-radius:5px;padding:5px 10px;font-size:.75rem;cursor:pointer;transition:all var(--transition-fast)}:not(.dark)[_nghost-%COMP%]   .futuristic-retry-button[_ngcontent-%COMP%]:hover, :not(.dark)   [_nghost-%COMP%]   .futuristic-retry-button[_ngcontent-%COMP%]:hover{background:rgba(255,107,105,.3)}.dark[_nghost-%COMP%]   .futuristic-error-container[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-error-container[_ngcontent-%COMP%]{margin:15px;padding:15px;background:rgba(255,0,0,.1);border-left:3px solid #ff3b30;border-radius:5px;display:flex;align-items:flex-start}.dark[_nghost-%COMP%]   .futuristic-error-icon[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-error-icon[_ngcontent-%COMP%]{color:#ff3b30;font-size:1.25rem;margin-right:15px}.dark[_nghost-%COMP%]   .futuristic-error-title[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-error-title[_ngcontent-%COMP%]{color:#ff3b30;font-size:.875rem;font-weight:600;margin-bottom:5px}.dark[_nghost-%COMP%]   .futuristic-error-message[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-error-message[_ngcontent-%COMP%]{color:var(--text-dim);font-size:.8125rem;margin-bottom:10px}.dark[_nghost-%COMP%]   .futuristic-retry-button[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-retry-button[_ngcontent-%COMP%]{background:rgba(255,0,0,.2);color:#ff3b30;border:none;border-radius:5px;padding:5px 10px;font-size:.75rem;cursor:pointer;transition:all var(--transition-fast)}.dark[_nghost-%COMP%]   .futuristic-retry-button[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .futuristic-retry-button[_ngcontent-%COMP%]:hover{background:rgba(255,0,0,.3)}:not(.dark)[_nghost-%COMP%]   .futuristic-empty-state[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-empty-state[_ngcontent-%COMP%], :not(.dark)[_nghost-%COMP%]   .futuristic-no-results[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-no-results[_ngcontent-%COMP%]{color:#6d6870}:not(.dark)[_nghost-%COMP%]   .futuristic-empty-icon[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-empty-icon[_ngcontent-%COMP%]{font-size:2.5rem;color:#4f5fad;margin-bottom:20px;opacity:.7}:not(.dark)[_nghost-%COMP%]   .futuristic-empty-title[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-empty-title[_ngcontent-%COMP%]{font-size:1.125rem;font-weight:600;color:#4f5fad;margin-bottom:5px}:not(.dark)[_nghost-%COMP%]   .futuristic-empty-text[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-empty-text[_ngcontent-%COMP%]{color:#6d6870;font-size:.875rem;margin-bottom:20px}:not(.dark)[_nghost-%COMP%]   .futuristic-start-button[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-start-button[_ngcontent-%COMP%]{background:linear-gradient(135deg,#4f5fad,#7826b5);color:#fff;border:none;border-radius:20px;padding:8px 16px;font-size:.875rem;cursor:pointer;display:flex;align-items:center;transition:all var(--transition-fast);box-shadow:0 2px 10px #0003}:not(.dark)[_nghost-%COMP%]   .futuristic-start-button[_ngcontent-%COMP%]:hover, :not(.dark)   [_nghost-%COMP%]   .futuristic-start-button[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 0 15px #4f5fad66}.dark[_nghost-%COMP%]   .futuristic-empty-state[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-empty-state[_ngcontent-%COMP%], .dark[_nghost-%COMP%]   .futuristic-no-results[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-no-results[_ngcontent-%COMP%]{color:var(--text-dim)}.dark[_nghost-%COMP%]   .futuristic-empty-icon[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-empty-icon[_ngcontent-%COMP%]{font-size:2.5rem;color:var(--accent-color);margin-bottom:20px;opacity:.7}.dark[_nghost-%COMP%]   .futuristic-empty-title[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-empty-title[_ngcontent-%COMP%]{font-size:1.125rem;font-weight:600;color:var(--text-light);margin-bottom:5px}.dark[_nghost-%COMP%]   .futuristic-empty-text[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-empty-text[_ngcontent-%COMP%]{color:var(--text-dim);font-size:.875rem;margin-bottom:20px}.dark[_nghost-%COMP%]   .futuristic-start-button[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-start-button[_ngcontent-%COMP%]{background:var(--primary-gradient);color:#fff;border:none;border-radius:20px;padding:8px 16px;font-size:.875rem;cursor:pointer;display:flex;align-items:center;transition:all var(--transition-fast);box-shadow:0 2px 10px #0003}.dark[_nghost-%COMP%]   .futuristic-start-button[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .futuristic-start-button[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:var(--glow-effect)}:not(.dark)[_nghost-%COMP%]   .futuristic-conversations[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-conversations[_ngcontent-%COMP%]{list-style:none;padding:0;margin:0}:not(.dark)[_nghost-%COMP%]   .futuristic-conversation-item[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-conversation-item[_ngcontent-%COMP%]{display:flex;align-items:center;padding:12px 15px;cursor:pointer;transition:background-color var(--transition-fast);border-bottom:1px solid rgba(79,95,173,.05)}:not(.dark)[_nghost-%COMP%]   .futuristic-conversation-item[_ngcontent-%COMP%]:hover, :not(.dark)   [_nghost-%COMP%]   .futuristic-conversation-item[_ngcontent-%COMP%]:hover{background-color:#4f5fad0d}:not(.dark)[_nghost-%COMP%]   .futuristic-conversation-selected[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-conversation-selected[_ngcontent-%COMP%]{background-color:#4f5fad1a!important;border-left:3px solid #4f5fad}:not(.dark)[_nghost-%COMP%]   .futuristic-avatar[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-avatar[_ngcontent-%COMP%]{position:relative;width:48px;height:48px;border-radius:50%;overflow:hidden;margin-right:12px;flex-shrink:0;border:2px solid rgba(79,95,173,.3);box-shadow:0 0 15px #4f5fad33}:not(.dark)[_nghost-%COMP%]   .futuristic-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:100%;height:100%;object-fit:cover}:not(.dark)[_nghost-%COMP%]   .futuristic-online-indicator[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-online-indicator[_ngcontent-%COMP%]{position:absolute;bottom:2px;right:2px;width:10px;height:10px;border-radius:50%;background:#4caf50;border:2px solid #ffffff;box-shadow:0 0 5px #4caf50cc}.dark[_nghost-%COMP%]   .futuristic-conversations[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-conversations[_ngcontent-%COMP%]{list-style:none;padding:0;margin:0}.dark[_nghost-%COMP%]   .futuristic-conversation-item[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-conversation-item[_ngcontent-%COMP%]{display:flex;align-items:center;padding:12px 15px;cursor:pointer;transition:background-color var(--transition-fast);border-bottom:1px solid rgba(0,247,255,.05)}.dark[_nghost-%COMP%]   .futuristic-conversation-item[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .futuristic-conversation-item[_ngcontent-%COMP%]:hover{background-color:#00f7ff0d}.dark[_nghost-%COMP%]   .futuristic-conversation-selected[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-conversation-selected[_ngcontent-%COMP%]{background-color:#00f7ff1a!important;border-left:3px solid var(--accent-color)}.dark[_nghost-%COMP%]   .futuristic-avatar[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-avatar[_ngcontent-%COMP%]{position:relative;width:48px;height:48px;border-radius:50%;overflow:hidden;margin-right:12px;flex-shrink:0;border:2px solid rgba(0,247,255,.3);box-shadow:var(--glow-effect)}.dark[_nghost-%COMP%]   .futuristic-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:100%;height:100%;object-fit:cover}.dark[_nghost-%COMP%]   .futuristic-online-indicator[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-online-indicator[_ngcontent-%COMP%]{position:absolute;bottom:2px;right:2px;width:10px;height:10px;border-radius:50%;background:#00ff9d;border:2px solid var(--medium-bg);box-shadow:0 0 5px #00ff9dcc}:not(.dark)[_nghost-%COMP%]   .futuristic-conversation-details[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-conversation-details[_ngcontent-%COMP%]{flex:1;min-width:0}:not(.dark)[_nghost-%COMP%]   .futuristic-conversation-header[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-conversation-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:baseline;margin-bottom:4px}:not(.dark)[_nghost-%COMP%]   .futuristic-conversation-name[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-conversation-name[_ngcontent-%COMP%]{font-size:.9375rem;font-weight:600;color:#4f5fad;margin:0;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}:not(.dark)[_nghost-%COMP%]   .futuristic-conversation-time[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-conversation-time[_ngcontent-%COMP%]{font-size:.75rem;color:#6d6870;white-space:nowrap;margin-left:8px}:not(.dark)[_nghost-%COMP%]   .futuristic-conversation-preview[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-conversation-preview[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center}:not(.dark)[_nghost-%COMP%]   .futuristic-conversation-message[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-conversation-message[_ngcontent-%COMP%]{font-size:.8125rem;color:#6d6870;margin:0;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}:not(.dark)[_nghost-%COMP%]   .futuristic-you-prefix[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-you-prefix[_ngcontent-%COMP%]{color:#4f5fad;font-weight:500}:not(.dark)[_nghost-%COMP%]   .futuristic-unread-badge[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-unread-badge[_ngcontent-%COMP%]{background:linear-gradient(135deg,#4f5fad,#7826b5);color:#fff;font-size:.75rem;font-weight:600;border-radius:50%;width:20px;height:20px;display:flex;align-items:center;justify-content:center;margin-left:8px;box-shadow:0 0 15px #4f5fad66}.dark[_nghost-%COMP%]   .futuristic-conversation-details[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-conversation-details[_ngcontent-%COMP%]{flex:1;min-width:0}.dark[_nghost-%COMP%]   .futuristic-conversation-header[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-conversation-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:baseline;margin-bottom:4px}.dark[_nghost-%COMP%]   .futuristic-conversation-name[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-conversation-name[_ngcontent-%COMP%]{font-size:.9375rem;font-weight:600;color:var(--text-light);margin:0;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.dark[_nghost-%COMP%]   .futuristic-conversation-time[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-conversation-time[_ngcontent-%COMP%]{font-size:.75rem;color:var(--text-dim);white-space:nowrap;margin-left:8px}.dark[_nghost-%COMP%]   .futuristic-conversation-preview[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-conversation-preview[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center}.dark[_nghost-%COMP%]   .futuristic-conversation-message[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-conversation-message[_ngcontent-%COMP%]{font-size:.8125rem;color:var(--text-dim);margin:0;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.dark[_nghost-%COMP%]   .futuristic-you-prefix[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-you-prefix[_ngcontent-%COMP%]{color:#00f7ffb3;font-weight:500}.dark[_nghost-%COMP%]   .futuristic-unread-badge[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-unread-badge[_ngcontent-%COMP%]{background:var(--accent-color);color:var(--dark-bg);font-size:.75rem;font-weight:600;border-radius:50%;width:20px;height:20px;display:flex;align-items:center;justify-content:center;margin-left:8px;box-shadow:var(--glow-effect)}:not(.dark)[_nghost-%COMP%]   .futuristic-main-area[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-main-area[_ngcontent-%COMP%]{background-color:#f0f4f8;position:relative}:not(.dark)[_nghost-%COMP%]   .futuristic-main-area[_ngcontent-%COMP%]:before, :not(.dark)   [_nghost-%COMP%]   .futuristic-main-area[_ngcontent-%COMP%]:before{content:"";position:absolute;inset:0;background-image:linear-gradient(rgba(79,95,173,.03) 1px,transparent 1px),linear-gradient(90deg,rgba(79,95,173,.03) 1px,transparent 1px);background-size:20px 20px;pointer-events:none;z-index:0}.dark[_nghost-%COMP%]   .futuristic-main-area[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-main-area[_ngcontent-%COMP%]{background-color:var(--dark-bg);position:relative}.dark[_nghost-%COMP%]   .futuristic-main-area[_ngcontent-%COMP%]:before, .dark   [_nghost-%COMP%]   .futuristic-main-area[_ngcontent-%COMP%]:before{content:"";position:absolute;inset:0;background-image:linear-gradient(rgba(0,247,255,.03) 1px,transparent 1px),linear-gradient(90deg,rgba(0,247,255,.03) 1px,transparent 1px);background-size:20px 20px;pointer-events:none;z-index:0}']})}}return r})();var Vt=u(605);function Dt(r,a){if(1&r&&(t.j41(0,"div",44)(1,"span"),t.EFF(2),t.k0s(),t.j41(3,"span"),t.EFF(4),t.k0s()()),2&r){const e=t.XpG();t.R7$(2),t.Lme("Affichage de ",e.users.length," sur ",e.totalUsers," utilisateurs"),t.R7$(2),t.Lme("Page ",e.currentPage," sur ",e.totalPages,"")}}function Ft(r,a){1&r&&(t.j41(0,"div",45),t.nrm(1,"div",46),t.j41(2,"div",47),t.EFF(3,"Chargement des utilisateurs..."),t.k0s()())}function Ut(r,a){1&r&&(t.j41(0,"div",48)(1,"div",49),t.nrm(2,"i",50),t.k0s(),t.j41(3,"h3",51),t.EFF(4,"Aucun utilisateur trouv\xe9"),t.k0s(),t.j41(5,"p",52),t.EFF(6," Essayez un autre terme de recherche ou effacez les filtres "),t.k0s()())}function At(r,a){1&r&&t.nrm(0,"span",66)}function $t(r,a){if(1&r){const e=t.RV6();t.j41(0,"button",67),t.bIt("click",function(){t.eBV(e);const n=t.XpG().$implicit,o=t.XpG(2);return t.Njj(o.startAudioCall(n.id||n._id))}),t.nrm(1,"i",68),t.k0s()}}function Gt(r,a){if(1&r){const e=t.RV6();t.j41(0,"button",69),t.bIt("click",function(){t.eBV(e);const n=t.XpG().$implicit,o=t.XpG(2);return t.Njj(o.startVideoCall(n.id||n._id))}),t.nrm(1,"i",70),t.k0s()}}function zt(r,a){if(1&r){const e=t.RV6();t.j41(0,"li",55)(1,"div",56),t.bIt("click",function(){const o=t.eBV(e).$implicit,s=t.XpG(2);return t.Njj(s.startConversation(o.id||o._id))}),t.j41(2,"div",57),t.nrm(3,"img",58),t.DNE(4,At,1,0,"span",59),t.k0s(),t.j41(5,"div",60)(6,"h3",61),t.EFF(7),t.k0s(),t.j41(8,"p",62),t.EFF(9),t.k0s()()(),t.j41(10,"div",63),t.DNE(11,$t,2,0,"button",64),t.DNE(12,Gt,2,0,"button",65),t.k0s()()}if(2&r){const e=a.$implicit;t.R7$(3),t.Y8G("src",e.image||"assets/images/default-avatar.png",t.B4B),t.R7$(1),t.Y8G("ngIf",e.isOnline),t.R7$(3),t.SpI(" ",e.username," "),t.R7$(2),t.JRh(e.email),t.R7$(2),t.Y8G("ngIf",e.isOnline),t.R7$(1),t.Y8G("ngIf",e.isOnline)}}function Nt(r,a){if(1&r&&(t.j41(0,"ul",53),t.DNE(1,zt,13,6,"li",54),t.k0s()),2&r){const e=t.XpG();t.R7$(1),t.Y8G("ngForOf",e.users)}}function Lt(r,a){1&r&&(t.j41(0,"div",71)(1,"div",72),t.nrm(2,"div",73)(3,"div",74)(4,"div",75),t.k0s(),t.j41(5,"div",47),t.EFF(6," Chargement de plus d'utilisateurs... "),t.k0s()())}function Bt(r,a){if(1&r){const e=t.RV6();t.j41(0,"div",76)(1,"button",77),t.bIt("click",function(){t.eBV(e);const n=t.XpG();return t.Njj(n.loadNextPage())}),t.nrm(2,"i",78),t.EFF(3," Charger plus d'utilisateurs "),t.k0s()()}}let Yt=(()=>{class r{constructor(e,i,n,o,s,d,h){this.MessageService=e,this.router=i,this.route=n,this.authService=o,this.toastService=s,this.logger=d,this.themeService=h,this.users=[],this.loading=!0,this.currentUserId=null,this.currentPage=1,this.pageSize=10,this.totalUsers=0,this.totalPages=0,this.hasNextPage=!1,this.hasPreviousPage=!1,this.sortBy="username",this.sortOrder="asc",this.filterForm=new l.gE({searchQuery:new l.MJ(""),isOnline:new l.MJ(null)}),this.autoRefreshEnabled=!0,this.autoRefreshInterval=3e4,this.loadingMore=!1,this.subscriptions=new v.yU,this.isDarkMode$=this.themeService.darkMode$}ngOnInit(){this.currentUserId=this.authService.getCurrentUserId(),this.setupFilterListeners(),this.setupAutoRefresh(),this.loadUsers()}setupFilterListeners(){const e=this.filterForm.get("searchQuery").valueChanges.subscribe(()=>{this.resetPagination(),this.loadUsers()});this.subscriptions.add(e);const i=this.filterForm.get("isOnline").valueChanges.subscribe(()=>{this.resetPagination(),this.loadUsers()});this.subscriptions.add(i)}setupAutoRefresh(){this.autoRefreshEnabled&&(this.autoRefreshSubscription=(0,Vt.Y)(this.autoRefreshInterval).subscribe(()=>{!this.loading&&!this.filterForm.get("searchQuery")?.value&&this.loadUsers(!0)}))}toggleAutoRefresh(){this.autoRefreshEnabled=!this.autoRefreshEnabled,this.autoRefreshEnabled?this.setupAutoRefresh():this.autoRefreshSubscription&&(this.autoRefreshSubscription.unsubscribe(),this.autoRefreshSubscription=void 0)}resetPagination(){this.currentPage=1}get searchQuery(){return this.filterForm.get("searchQuery")?.value||""}set searchQuery(e){this.filterForm.get("searchQuery")?.setValue(e)}$any(e){return e}loadUsers(e=!1){if(this.loadingMore)return;this.loading=!0;const i=this.filterForm.get("searchQuery")?.value||"",n=this.filterForm.get("isOnline")?.value,o=this.MessageService.getAllUsers(e,i,this.currentPage,this.pageSize,this.sortBy,this.sortOrder,!0===n||void 0).subscribe({next:s=>{if(!Array.isArray(s))return this.users=[],this.loading=!1,this.loadingMore=!1,void this.toastService.showError("Failed to load users: Invalid data");if(1===this.currentPage)this.users=s.filter(h=>!!h&&(h.id||h._id)!==this.currentUserId);else{const h=s.filter(k=>{if(!k)return!1;const y=k.id||k._id;return y!==this.currentUserId&&!this.users.some(m=>(m.id||m._id)===y)});this.users=[...this.users,...h]}const d=this.MessageService.currentUserPagination;this.totalUsers=d.totalCount,this.totalPages=d.totalPages,this.hasNextPage=d.hasNextPage,this.hasPreviousPage=d.hasPreviousPage,this.loading=!1,this.loadingMore=!1},error:s=>{this.loading=!1,this.loadingMore=!1,this.toastService.showError(`Failed to load users: ${s.message||"Unknown error"}`),1===this.currentPage&&(this.users=[])},complete:()=>{this.loading=!1,this.loadingMore=!1}});this.subscriptions.add(o)}startConversation(e){e?(this.toastService.showInfo("Creating conversation..."),this.MessageService.createConversation(e).subscribe({next:i=>{i&&i.id?this.router.navigate(["/messages/conversations/chat",i.id]).then(n=>{n||this.toastService.showError("Failed to open conversation")}):this.toastService.showError("Failed to create conversation: Invalid response")},error:i=>{this.toastService.showError(`Failed to create conversation: ${i.message||"Unknown error"}`)}})):this.toastService.showError("Cannot start conversation with undefined user")}startAudioCall(e){e&&this.MessageService.initiateCall(e,c.JG.AUDIO).subscribe({next:i=>{this.toastService.showSuccess("Audio call initiated")},error:i=>{this.toastService.showError("Failed to initiate audio call")}})}startVideoCall(e){e&&this.MessageService.initiateCall(e,c.JG.VIDEO).subscribe({next:i=>{this.toastService.showSuccess("Video call initiated")},error:i=>{this.toastService.showError("Failed to initiate video call")}})}loadNextPage(){this.hasNextPage&&!this.loading&&(this.loadingMore=!0,this.currentPage++,this.loadUsers())}loadPreviousPage(){this.hasPreviousPage&&!this.loading&&(this.loadingMore=!0,this.currentPage--,this.loadUsers())}refreshUsers(){this.resetPagination(),this.loadUsers(!0)}clearFilters(){this.filterForm.reset({searchQuery:"",isOnline:null}),this.resetPagination(),this.loadUsers(!0)}changeSortOrder(e){this.sortBy===e?this.sortOrder="asc"===this.sortOrder?"desc":"asc":(this.sortBy=e,this.sortOrder="asc"),this.resetPagination(),this.loadUsers(!0)}goBackToConversations(){this.router.navigate(["/messages/conversations"])}ngOnDestroy(){this.subscriptions.unsubscribe(),this.autoRefreshSubscription&&this.autoRefreshSubscription.unsubscribe()}static{this.\u0275fac=function(i){return new(i||r)(t.rXU(g.b),t.rXU(p.Ix),t.rXU(p.nX),t.rXU(E.V),t.rXU(M.f),t.rXU(S.g),t.rXU(j.F))}}static{this.\u0275cmp=t.VBU({type:r,selectors:[["app-user-list"]],decls:64,vars:18,consts:[[1,"flex","flex-col","h-full","futuristic-users-container"],[1,"absolute","inset-0","overflow-hidden","pointer-events-none"],[1,"absolute","top-[15%]","left-[10%]","w-64","h-64","rounded-full","bg-gradient-to-br","from-[#4f5fad]/5","to-transparent","dark:from-[#00f7ff]/10","dark:to-transparent","blur-3xl"],[1,"absolute","bottom-[20%]","right-[10%]","w-80","h-80","rounded-full","bg-gradient-to-tl","from-[#4f5fad]/5","to-transparent","dark:from-[#00f7ff]/10","dark:to-transparent","blur-3xl"],[1,"absolute","top-[40%]","right-[30%]","w-40","h-40","rounded-full","bg-gradient-to-br","from-transparent","to-transparent","dark:from-[#00f7ff]/5","dark:to-transparent","blur-3xl","opacity-0","dark:opacity-100"],[1,"absolute","bottom-[60%]","left-[25%]","w-32","h-32","rounded-full","bg-gradient-to-tl","from-transparent","to-transparent","dark:from-[#00f7ff]/5","dark:to-transparent","blur-3xl","opacity-0","dark:opacity-100"],[1,"absolute","inset-0","opacity-5","dark:opacity-0"],[1,"h-full","grid","grid-cols-12"],[1,"border-r","border-[#4f5fad]"],[1,"absolute","inset-0","opacity-0","dark:opacity-100","overflow-hidden"],[1,"h-px","w-full","bg-[#00f7ff]/20","absolute","animate-scan"],[1,"futuristic-users-header"],[1,"flex","justify-between","items-center","mb-4"],[1,"futuristic-title"],[1,"flex","space-x-2"],["title","Rafra\xeechir la liste",1,"futuristic-action-button",3,"click"],[1,"fas","fa-sync-alt"],[1,"futuristic-action-button",3,"click"],[1,"fas","fa-arrow-left"],[1,"space-y-3"],[1,"relative"],["type","text","placeholder","Rechercher des utilisateurs...",1,"w-full","pl-10","pr-4","py-2","rounded-lg","futuristic-input-field",3,"ngModel","ngModelChange"],[1,"fas","fa-search","absolute","left-3","top-3","text-[#6d6870]","dark:text-[#a0a0a0]"],[1,"flex","items-center","justify-between"],[1,"flex","items-center","space-x-4"],[1,"flex","items-center","space-x-2"],[1,"futuristic-checkbox-container"],["type","checkbox","id","onlineFilter",1,"futuristic-checkbox",3,"checked","change"],[1,"futuristic-checkbox-checkmark"],["for","onlineFilter",1,"futuristic-label"],[1,"futuristic-label"],[1,"futuristic-select",3,"change"],["value","username",3,"selected"],["value","email",3,"selected"],["value","lastActive",3,"selected"],[1,"futuristic-sort-button",3,"title","click"],[1,"futuristic-clear-button",3,"click"],["class","flex justify-between items-center futuristic-pagination-info",4,"ngIf"],[1,"futuristic-users-list",3,"scroll"],["class","futuristic-loading-container",4,"ngIf"],["class","futuristic-empty-state",4,"ngIf"],["class","futuristic-users-grid",4,"ngIf"],["class","futuristic-loading-more",4,"ngIf"],["class","futuristic-load-more-container",4,"ngIf"],[1,"flex","justify-between","items-center","futuristic-pagination-info"],[1,"futuristic-loading-container"],[1,"futuristic-loading-circle"],[1,"futuristic-loading-text"],[1,"futuristic-empty-state"],[1,"futuristic-empty-icon"],[1,"fas","fa-users"],[1,"futuristic-empty-title"],[1,"futuristic-empty-text"],[1,"futuristic-users-grid"],["class","futuristic-user-card",4,"ngFor","ngForOf"],[1,"futuristic-user-card"],[1,"futuristic-user-content",3,"click"],[1,"futuristic-avatar"],["alt","User avatar",3,"src"],["class","futuristic-online-indicator",4,"ngIf"],[1,"futuristic-user-info"],[1,"futuristic-username"],[1,"futuristic-user-email"],[1,"futuristic-call-buttons"],["class","futuristic-call-button","title","Appel audio",3,"click",4,"ngIf"],["class","futuristic-call-button","title","Appel vid\xe9o",3,"click",4,"ngIf"],[1,"futuristic-online-indicator"],["title","Appel audio",1,"futuristic-call-button",3,"click"],[1,"fas","fa-phone"],["title","Appel vid\xe9o",1,"futuristic-call-button",3,"click"],[1,"fas","fa-video"],[1,"futuristic-loading-more"],[1,"futuristic-loading-dots"],[1,"futuristic-loading-dot",2,"animation-delay","0s"],[1,"futuristic-loading-dot",2,"animation-delay","0.2s"],[1,"futuristic-loading-dot",2,"animation-delay","0.4s"],[1,"futuristic-load-more-container"],[1,"futuristic-load-more-button",3,"click"],[1,"fas","fa-chevron-down","mr-2"]],template:function(i,n){if(1&i&&(t.j41(0,"div",0),t.nI1(1,"async"),t.j41(2,"div",1),t.nrm(3,"div",2)(4,"div",3)(5,"div",4)(6,"div",5),t.j41(7,"div",6)(8,"div",7),t.nrm(9,"div",8)(10,"div",8)(11,"div",8)(12,"div",8)(13,"div",8)(14,"div",8)(15,"div",8)(16,"div",8)(17,"div",8)(18,"div",8)(19,"div",8),t.k0s()(),t.j41(20,"div",9),t.nrm(21,"div",10),t.k0s()(),t.j41(22,"div",11)(23,"div",12)(24,"h1",13),t.EFF(25,"Nouvelle Conversation"),t.k0s(),t.j41(26,"div",14)(27,"button",15),t.bIt("click",function(){return n.refreshUsers()}),t.nrm(28,"i",16),t.k0s(),t.j41(29,"button",17),t.bIt("click",function(){return n.goBackToConversations()}),t.nrm(30,"i",18),t.k0s()()(),t.j41(31,"div",19)(32,"div",20)(33,"input",21),t.bIt("ngModelChange",function(s){return n.searchQuery=s}),t.k0s(),t.nrm(34,"i",22),t.k0s(),t.j41(35,"div",23)(36,"div",24)(37,"div",25)(38,"label",26)(39,"input",27),t.bIt("change",function(s){let d;return null==(d=n.filterForm.get("isOnline"))?null:d.setValue(!!s.target.checked||null)}),t.k0s(),t.nrm(40,"span",28),t.k0s(),t.j41(41,"label",29),t.EFF(42,"En ligne uniquement"),t.k0s()(),t.j41(43,"div",25)(44,"span",30),t.EFF(45,"Trier par:"),t.k0s(),t.j41(46,"select",31),t.bIt("change",function(s){return n.changeSortOrder(s.target.value)}),t.j41(47,"option",32),t.EFF(48," Nom "),t.k0s(),t.j41(49,"option",33),t.EFF(50," Email "),t.k0s(),t.j41(51,"option",34),t.EFF(52," Derni\xe8re activit\xe9 "),t.k0s()(),t.j41(53,"button",35),t.bIt("click",function(){return n.sortOrder="asc"===n.sortOrder?"desc":"asc",n.loadUsers(!0)}),t.nrm(54,"i"),t.k0s()()(),t.j41(55,"button",36),t.bIt("click",function(){return n.clearFilters()}),t.EFF(56," Effacer les filtres "),t.k0s()(),t.DNE(57,Dt,5,4,"div",37),t.k0s()(),t.j41(58,"div",38),t.bIt("scroll",function(s){return s.target.scrollTop+s.target.clientHeight>=s.target.scrollHeight-200&&n.loadNextPage()}),t.DNE(59,Ft,4,0,"div",39),t.DNE(60,Ut,7,0,"div",40),t.DNE(61,Nt,2,1,"ul",41),t.DNE(62,Lt,7,0,"div",42),t.DNE(63,Bt,4,0,"div",43),t.k0s()()),2&i){let o;t.AVh("dark",t.bMT(1,16,n.isDarkMode$)),t.R7$(33),t.Y8G("ngModel",n.searchQuery),t.R7$(6),t.Y8G("checked",!0===(null==(o=n.filterForm.get("isOnline"))?null:o.value)),t.R7$(8),t.Y8G("selected","username"===n.sortBy),t.R7$(2),t.Y8G("selected","email"===n.sortBy),t.R7$(2),t.Y8G("selected","lastActive"===n.sortBy),t.R7$(2),t.Y8G("title","asc"===n.sortOrder?"Ordre croissant":"Ordre d\xe9croissant"),t.R7$(1),t.HbH("asc"===n.sortOrder?"fas fa-sort-up":"fas fa-sort-down"),t.R7$(3),t.Y8G("ngIf",n.totalUsers>0),t.R7$(2),t.Y8G("ngIf",n.loading&&!n.users.length),t.R7$(1),t.Y8G("ngIf",!n.loading&&0===n.users.length),t.R7$(1),t.Y8G("ngIf",n.users.length>0),t.R7$(1),t.Y8G("ngIf",n.loading&&n.users.length>0),t.R7$(1),t.Y8G("ngIf",n.hasNextPage&&!n.loading)}},dependencies:[f.Sq,f.bT,l.xH,l.y7,l.me,l.BC,l.vS,f.Jj],styles:[":not(.dark)[_nghost-%COMP%]   .futuristic-users-container[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-users-container[_ngcontent-%COMP%]{background-color:#f0f4f8;color:#6d6870;position:relative;overflow:hidden}.dark[_nghost-%COMP%]   .futuristic-users-container[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-users-container[_ngcontent-%COMP%]{background-color:var(--dark-bg);color:var(--text-light);position:relative;overflow:hidden}@keyframes _ngcontent-%COMP%_grid-pulse{0%{opacity:.3}50%{opacity:.5}to{opacity:.3}}@keyframes _ngcontent-%COMP%_scan{0%{top:-10%;opacity:.5}50%{opacity:.8}to{top:110%;opacity:.5}}.animate-scan[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_scan 8s linear infinite;box-shadow:0 0 10px #00f7ff80}:not(.dark)[_nghost-%COMP%]   .futuristic-users-container[_ngcontent-%COMP%]:before, :not(.dark)   [_nghost-%COMP%]   .futuristic-users-container[_ngcontent-%COMP%]:before{content:\"\";position:absolute;inset:0;background-image:linear-gradient(rgba(79,95,173,.03) 1px,transparent 1px),linear-gradient(90deg,rgba(79,95,173,.03) 1px,transparent 1px);background-size:20px 20px;pointer-events:none;z-index:0}.dark[_nghost-%COMP%]   .futuristic-users-container[_ngcontent-%COMP%]:before, .dark   [_nghost-%COMP%]   .futuristic-users-container[_ngcontent-%COMP%]:before{content:\"\";position:absolute;inset:0;background-image:linear-gradient(rgba(0,247,255,.07) 1px,transparent 1px),linear-gradient(90deg,rgba(0,247,255,.07) 1px,transparent 1px),linear-gradient(rgba(0,247,255,.03) 1px,transparent 1px),linear-gradient(90deg,rgba(0,247,255,.03) 1px,transparent 1px);background-size:100px 100px,100px 100px,20px 20px,20px 20px;pointer-events:none;z-index:0;animation:_ngcontent-%COMP%_grid-pulse 4s infinite ease-in-out}.dark[_nghost-%COMP%]   .futuristic-users-container[_ngcontent-%COMP%]:after, .dark   [_nghost-%COMP%]   .futuristic-users-container[_ngcontent-%COMP%]:after{content:\"\";position:absolute;inset:0;background:repeating-linear-gradient(to bottom,transparent,transparent 50px,rgba(0,247,255,.03) 50px,rgba(0,247,255,.03) 51px);pointer-events:none;z-index:0}:not(.dark)[_nghost-%COMP%]   .futuristic-users-header[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-users-header[_ngcontent-%COMP%]{padding:1rem;border-bottom:1px solid rgba(79,95,173,.2);background-color:#ffffffe6;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);position:sticky;top:0;z-index:10;box-shadow:0 4px 20px #0000001a;position:relative}.dark[_nghost-%COMP%]   .futuristic-users-header[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-users-header[_ngcontent-%COMP%]{padding:1rem;border-bottom:1px solid rgba(0,247,255,.2);background-color:#1e1e1ee6;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);position:sticky;top:0;z-index:10;box-shadow:0 4px 20px #0000004d;position:relative}:not(.dark)[_nghost-%COMP%]   .futuristic-title[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-title[_ngcontent-%COMP%]{font-size:1.25rem;font-weight:600;background:linear-gradient(135deg,#4f5fad,#7826b5);-webkit-background-clip:text;background-clip:text;color:transparent;text-shadow:0 0 10px rgba(79,95,173,.5)}.dark[_nghost-%COMP%]   .futuristic-title[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-title[_ngcontent-%COMP%]{font-size:1.25rem;font-weight:600;background:linear-gradient(135deg,var(--accent-color),var(--secondary-color));-webkit-background-clip:text;background-clip:text;color:transparent;text-shadow:0 0 10px rgba(0,247,255,.5)}:not(.dark)[_nghost-%COMP%]   .futuristic-action-button[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-action-button[_ngcontent-%COMP%]{width:36px;height:36px;display:flex;align-items:center;justify-content:center;background-color:#4f5fad1a;color:#4f5fad;border:none;border-radius:50%;cursor:pointer;transition:all var(--transition-fast)}:not(.dark)[_nghost-%COMP%]   .futuristic-action-button[_ngcontent-%COMP%]:hover, :not(.dark)   [_nghost-%COMP%]   .futuristic-action-button[_ngcontent-%COMP%]:hover{background-color:#4f5fad33;transform:translateY(-2px);box-shadow:0 0 15px #4f5fad66}.dark[_nghost-%COMP%]   .futuristic-action-button[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-action-button[_ngcontent-%COMP%]{width:36px;height:36px;display:flex;align-items:center;justify-content:center;background-color:#00f7ff1a;color:var(--accent-color);border:none;border-radius:50%;cursor:pointer;transition:all var(--transition-fast)}.dark[_nghost-%COMP%]   .futuristic-action-button[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .futuristic-action-button[_ngcontent-%COMP%]:hover{background-color:#00f7ff33;transform:translateY(-2px);box-shadow:var(--glow-effect)}:not(.dark)[_nghost-%COMP%]   .futuristic-input-field[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-input-field[_ngcontent-%COMP%]{background-color:#4f5fad0d;border:1px solid rgba(79,95,173,.2);border-radius:var(--border-radius-md);color:#6d6870;transition:all var(--transition-fast)}:not(.dark)[_nghost-%COMP%]   .futuristic-input-field[_ngcontent-%COMP%]:focus, :not(.dark)   [_nghost-%COMP%]   .futuristic-input-field[_ngcontent-%COMP%]:focus{background-color:#4f5fad1a;border-color:#4f5fad;box-shadow:0 0 15px #4f5fad66;outline:none}:not(.dark)[_nghost-%COMP%]   .futuristic-input-field[_ngcontent-%COMP%]::placeholder, :not(.dark)   [_nghost-%COMP%]   .futuristic-input-field[_ngcontent-%COMP%]::placeholder{color:#6d6870}.dark[_nghost-%COMP%]   .futuristic-input-field[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-input-field[_ngcontent-%COMP%]{background-color:#00f7ff0d;border:1px solid rgba(0,247,255,.2);border-radius:var(--border-radius-md);color:var(--text-light);transition:all var(--transition-fast)}.dark[_nghost-%COMP%]   .futuristic-input-field[_ngcontent-%COMP%]:focus, .dark   [_nghost-%COMP%]   .futuristic-input-field[_ngcontent-%COMP%]:focus{background-color:#00f7ff1a;border-color:var(--accent-color);box-shadow:var(--glow-effect);outline:none}.dark[_nghost-%COMP%]   .futuristic-input-field[_ngcontent-%COMP%]::placeholder, .dark   [_nghost-%COMP%]   .futuristic-input-field[_ngcontent-%COMP%]::placeholder{color:var(--text-dim)}:not(.dark)[_nghost-%COMP%]   .futuristic-checkbox-container[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-checkbox-container[_ngcontent-%COMP%]{position:relative;display:inline-block;width:18px;height:18px;cursor:pointer}:not(.dark)[_nghost-%COMP%]   .futuristic-checkbox[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-checkbox[_ngcontent-%COMP%]{position:absolute;opacity:0;cursor:pointer;height:0;width:0}:not(.dark)[_nghost-%COMP%]   .futuristic-checkbox-checkmark[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-checkbox-checkmark[_ngcontent-%COMP%]{position:absolute;top:0;left:0;height:18px;width:18px;background-color:#4f5fad0d;border:1px solid rgba(79,95,173,.2);border-radius:4px;transition:all var(--transition-fast)}:not(.dark)[_nghost-%COMP%]   .futuristic-checkbox[_ngcontent-%COMP%]:checked ~ .futuristic-checkbox-checkmark[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-checkbox[_ngcontent-%COMP%]:checked ~ .futuristic-checkbox-checkmark[_ngcontent-%COMP%]{background-color:#4f5fad;box-shadow:0 0 15px #4f5fad66}:not(.dark)[_nghost-%COMP%]   .futuristic-checkbox-checkmark[_ngcontent-%COMP%]:after, :not(.dark)   [_nghost-%COMP%]   .futuristic-checkbox-checkmark[_ngcontent-%COMP%]:after{content:\"\";position:absolute;display:none}:not(.dark)[_nghost-%COMP%]   .futuristic-checkbox[_ngcontent-%COMP%]:checked ~ .futuristic-checkbox-checkmark[_ngcontent-%COMP%]:after, :not(.dark)   [_nghost-%COMP%]   .futuristic-checkbox[_ngcontent-%COMP%]:checked ~ .futuristic-checkbox-checkmark[_ngcontent-%COMP%]:after{display:block;left:6px;top:2px;width:5px;height:10px;border:solid white;border-width:0 2px 2px 0;transform:rotate(45deg)}:not(.dark)[_nghost-%COMP%]   .futuristic-label[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-label[_ngcontent-%COMP%]{color:#6d6870;font-size:.875rem;transition:color var(--transition-fast)}.dark[_nghost-%COMP%]   .futuristic-checkbox-container[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-checkbox-container[_ngcontent-%COMP%]{position:relative;display:inline-block;width:18px;height:18px;cursor:pointer}.dark[_nghost-%COMP%]   .futuristic-checkbox[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-checkbox[_ngcontent-%COMP%]{position:absolute;opacity:0;cursor:pointer;height:0;width:0}.dark[_nghost-%COMP%]   .futuristic-checkbox-checkmark[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-checkbox-checkmark[_ngcontent-%COMP%]{position:absolute;top:0;left:0;height:18px;width:18px;background-color:#00f7ff0d;border:1px solid rgba(0,247,255,.2);border-radius:4px;transition:all var(--transition-fast)}.dark[_nghost-%COMP%]   .futuristic-checkbox[_ngcontent-%COMP%]:checked ~ .futuristic-checkbox-checkmark[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-checkbox[_ngcontent-%COMP%]:checked ~ .futuristic-checkbox-checkmark[_ngcontent-%COMP%]{background-color:var(--accent-color);box-shadow:var(--glow-effect)}.dark[_nghost-%COMP%]   .futuristic-checkbox-checkmark[_ngcontent-%COMP%]:after, .dark   [_nghost-%COMP%]   .futuristic-checkbox-checkmark[_ngcontent-%COMP%]:after{content:\"\";position:absolute;display:none}.dark[_nghost-%COMP%]   .futuristic-checkbox[_ngcontent-%COMP%]:checked ~ .futuristic-checkbox-checkmark[_ngcontent-%COMP%]:after, .dark   [_nghost-%COMP%]   .futuristic-checkbox[_ngcontent-%COMP%]:checked ~ .futuristic-checkbox-checkmark[_ngcontent-%COMP%]:after{display:block;left:6px;top:2px;width:5px;height:10px;border:solid white;border-width:0 2px 2px 0;transform:rotate(45deg)}.dark[_nghost-%COMP%]   .futuristic-label[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-label[_ngcontent-%COMP%]{color:var(--text-dim);font-size:.875rem;transition:color var(--transition-fast)}:not(.dark)[_nghost-%COMP%]   .futuristic-select[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-select[_ngcontent-%COMP%]{background-color:#4f5fad0d;border:1px solid rgba(79,95,173,.2);border-radius:var(--border-radius-md);color:#6d6870;padding:.25rem 2rem .25rem .5rem;font-size:.875rem;appearance:none;background-image:url(\"data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%234f5fad' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e\");background-repeat:no-repeat;background-position:right .5rem center;background-size:1em;transition:all var(--transition-fast)}:not(.dark)[_nghost-%COMP%]   .futuristic-select[_ngcontent-%COMP%]:focus, :not(.dark)   [_nghost-%COMP%]   .futuristic-select[_ngcontent-%COMP%]:focus{background-color:#4f5fad1a;border-color:#4f5fad;box-shadow:0 0 15px #4f5fad66;outline:none}:not(.dark)[_nghost-%COMP%]   .futuristic-select[_ngcontent-%COMP%]   option[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-select[_ngcontent-%COMP%]   option[_ngcontent-%COMP%]{background-color:#fff;color:#6d6870}.dark[_nghost-%COMP%]   .futuristic-select[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-select[_ngcontent-%COMP%]{background-color:#00f7ff0d;border:1px solid rgba(0,247,255,.2);border-radius:var(--border-radius-md);color:var(--text-light);padding:.25rem 2rem .25rem .5rem;font-size:.875rem;appearance:none;background-image:url(\"data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%2300f7ff' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e\");background-repeat:no-repeat;background-position:right .5rem center;background-size:1em;transition:all var(--transition-fast)}.dark[_nghost-%COMP%]   .futuristic-select[_ngcontent-%COMP%]:focus, .dark   [_nghost-%COMP%]   .futuristic-select[_ngcontent-%COMP%]:focus{background-color:#00f7ff1a;border-color:var(--accent-color);box-shadow:var(--glow-effect);outline:none}.dark[_nghost-%COMP%]   .futuristic-select[_ngcontent-%COMP%]   option[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-select[_ngcontent-%COMP%]   option[_ngcontent-%COMP%]{background-color:var(--dark-bg);color:var(--text-light)}:not(.dark)[_nghost-%COMP%]   .futuristic-sort-button[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-sort-button[_ngcontent-%COMP%]{width:28px;height:28px;display:flex;align-items:center;justify-content:center;background-color:#4f5fad0d;color:#4f5fad;border:none;border-radius:var(--border-radius-sm);cursor:pointer;transition:all var(--transition-fast)}:not(.dark)[_nghost-%COMP%]   .futuristic-sort-button[_ngcontent-%COMP%]:hover, :not(.dark)   [_nghost-%COMP%]   .futuristic-sort-button[_ngcontent-%COMP%]:hover{background-color:#4f5fad1a;box-shadow:0 0 15px #4f5fad66}.dark[_nghost-%COMP%]   .futuristic-sort-button[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-sort-button[_ngcontent-%COMP%]{width:28px;height:28px;display:flex;align-items:center;justify-content:center;background-color:#00f7ff0d;color:var(--accent-color);border:none;border-radius:var(--border-radius-sm);cursor:pointer;transition:all var(--transition-fast)}.dark[_nghost-%COMP%]   .futuristic-sort-button[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .futuristic-sort-button[_ngcontent-%COMP%]:hover{background-color:#00f7ff1a;box-shadow:var(--glow-effect)}:not(.dark)[_nghost-%COMP%]   .futuristic-clear-button[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-clear-button[_ngcontent-%COMP%]{font-size:.75rem;color:#4f5fad;background:none;border:none;cursor:pointer;transition:all var(--transition-fast);padding:.25rem .5rem;border-radius:var(--border-radius-sm)}:not(.dark)[_nghost-%COMP%]   .futuristic-clear-button[_ngcontent-%COMP%]:hover, :not(.dark)   [_nghost-%COMP%]   .futuristic-clear-button[_ngcontent-%COMP%]:hover{color:#7826b5;background-color:#4f5fad0d}.dark[_nghost-%COMP%]   .futuristic-clear-button[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-clear-button[_ngcontent-%COMP%]{font-size:.75rem;color:var(--accent-color);background:none;border:none;cursor:pointer;transition:all var(--transition-fast);padding:.25rem .5rem;border-radius:var(--border-radius-sm)}.dark[_nghost-%COMP%]   .futuristic-clear-button[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .futuristic-clear-button[_ngcontent-%COMP%]:hover{color:var(--secondary-color);background-color:#00f7ff0d}:not(.dark)[_nghost-%COMP%]   .futuristic-pagination-info[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-pagination-info[_ngcontent-%COMP%]{font-size:.75rem;color:#6d6870}.dark[_nghost-%COMP%]   .futuristic-pagination-info[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-pagination-info[_ngcontent-%COMP%]{font-size:.75rem;color:var(--text-dim)}:not(.dark)[_nghost-%COMP%]   .futuristic-users-list[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-users-list[_ngcontent-%COMP%]{flex:1;overflow-y:auto;padding:1rem;scrollbar-width:thin;scrollbar-color:#4f5fad transparent;position:relative;z-index:1}:not(.dark)[_nghost-%COMP%]   .futuristic-users-list[_ngcontent-%COMP%]::-webkit-scrollbar, :not(.dark)   [_nghost-%COMP%]   .futuristic-users-list[_ngcontent-%COMP%]::-webkit-scrollbar{width:4px}:not(.dark)[_nghost-%COMP%]   .futuristic-users-list[_ngcontent-%COMP%]::-webkit-scrollbar-track, :not(.dark)   [_nghost-%COMP%]   .futuristic-users-list[_ngcontent-%COMP%]::-webkit-scrollbar-track{background:transparent}:not(.dark)[_nghost-%COMP%]   .futuristic-users-list[_ngcontent-%COMP%]::-webkit-scrollbar-thumb, :not(.dark)   [_nghost-%COMP%]   .futuristic-users-list[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background-color:#4f5fad;border-radius:10px}.dark[_nghost-%COMP%]   .futuristic-users-list[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-users-list[_ngcontent-%COMP%]{flex:1;overflow-y:auto;padding:1rem;scrollbar-width:thin;scrollbar-color:var(--accent-color) transparent;position:relative;z-index:1}.dark[_nghost-%COMP%]   .futuristic-users-list[_ngcontent-%COMP%]::-webkit-scrollbar, .dark   [_nghost-%COMP%]   .futuristic-users-list[_ngcontent-%COMP%]::-webkit-scrollbar{width:4px}.dark[_nghost-%COMP%]   .futuristic-users-list[_ngcontent-%COMP%]::-webkit-scrollbar-track, .dark   [_nghost-%COMP%]   .futuristic-users-list[_ngcontent-%COMP%]::-webkit-scrollbar-track{background:transparent}.dark[_nghost-%COMP%]   .futuristic-users-list[_ngcontent-%COMP%]::-webkit-scrollbar-thumb, .dark   [_nghost-%COMP%]   .futuristic-users-list[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background-color:var(--accent-color);border-radius:10px}:not(.dark)[_nghost-%COMP%]   .futuristic-loading-container[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-loading-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;height:100%;padding:2rem}:not(.dark)[_nghost-%COMP%]   .futuristic-loading-circle[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-loading-circle[_ngcontent-%COMP%]{width:60px;height:60px;border-radius:50%;border:2px solid transparent;border-top-color:#4f5fad;border-bottom-color:#7826b5;animation:_ngcontent-%COMP%_futuristic-spin-light 1.2s linear infinite;box-shadow:0 0 15px #4f5fad4d}@keyframes _ngcontent-%COMP%_futuristic-spin-light{0%{transform:rotate(0)}to{transform:rotate(360deg)}}:not(.dark)[_nghost-%COMP%]   .futuristic-loading-text[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-loading-text[_ngcontent-%COMP%]{margin-top:1rem;color:#6d6870;font-size:.875rem;text-align:center}.dark[_nghost-%COMP%]   .futuristic-loading-container[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-loading-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;height:100%;padding:2rem}.dark[_nghost-%COMP%]   .futuristic-loading-circle[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-loading-circle[_ngcontent-%COMP%]{width:60px;height:60px;border-radius:50%;border:2px solid transparent;border-top-color:var(--accent-color);border-bottom-color:var(--secondary-color);animation:_ngcontent-%COMP%_futuristic-spin-dark 1.2s linear infinite;box-shadow:0 0 15px #00f7ff4d}@keyframes _ngcontent-%COMP%_futuristic-spin-dark{0%{transform:rotate(0)}to{transform:rotate(360deg)}}.dark[_nghost-%COMP%]   .futuristic-loading-text[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-loading-text[_ngcontent-%COMP%]{margin-top:1rem;color:var(--text-dim);font-size:.875rem;text-align:center}:not(.dark)[_nghost-%COMP%]   .futuristic-empty-state[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-empty-state[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;height:100%;padding:2rem;text-align:center}:not(.dark)[_nghost-%COMP%]   .futuristic-empty-icon[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-empty-icon[_ngcontent-%COMP%]{font-size:3rem;color:#4f5fad;margin-bottom:1rem;opacity:.5}:not(.dark)[_nghost-%COMP%]   .futuristic-empty-title[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-empty-title[_ngcontent-%COMP%]{font-size:1.25rem;font-weight:600;color:#4f5fad;margin-bottom:.5rem}:not(.dark)[_nghost-%COMP%]   .futuristic-empty-text[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-empty-text[_ngcontent-%COMP%]{color:#6d6870;font-size:.875rem}.dark[_nghost-%COMP%]   .futuristic-empty-state[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-empty-state[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;height:100%;padding:2rem;text-align:center}.dark[_nghost-%COMP%]   .futuristic-empty-icon[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-empty-icon[_ngcontent-%COMP%]{font-size:3rem;color:var(--accent-color);margin-bottom:1rem;opacity:.5}.dark[_nghost-%COMP%]   .futuristic-empty-title[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-empty-title[_ngcontent-%COMP%]{font-size:1.25rem;font-weight:600;color:var(--text-light);margin-bottom:.5rem}.dark[_nghost-%COMP%]   .futuristic-empty-text[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-empty-text[_ngcontent-%COMP%]{color:var(--text-dim);font-size:.875rem}:not(.dark)[_nghost-%COMP%]   .futuristic-users-grid[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-users-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fill,minmax(280px,1fr));gap:1rem;list-style:none;padding:0;margin:0}:not(.dark)[_nghost-%COMP%]   .futuristic-user-card[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-user-card[_ngcontent-%COMP%]{background-color:#4f5fad0d;border:1px solid rgba(79,95,173,.1);border-radius:var(--border-radius-md);overflow:hidden;transition:all var(--transition-fast);display:flex;flex-direction:column}:not(.dark)[_nghost-%COMP%]   .futuristic-user-card[_ngcontent-%COMP%]:hover, :not(.dark)   [_nghost-%COMP%]   .futuristic-user-card[_ngcontent-%COMP%]:hover{transform:translateY(-4px);box-shadow:0 0 15px #4f5fad66;background-color:#4f5fad1a;border-color:#4f5fad4d}:not(.dark)[_nghost-%COMP%]   .futuristic-user-content[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-user-content[_ngcontent-%COMP%]{display:flex;align-items:center;padding:1rem;cursor:pointer;flex:1}:not(.dark)[_nghost-%COMP%]   .futuristic-avatar[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-avatar[_ngcontent-%COMP%]{position:relative;width:48px;height:48px;flex-shrink:0;margin-right:1rem}:not(.dark)[_nghost-%COMP%]   .futuristic-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:100%;height:100%;object-fit:cover;border-radius:50%;border:2px solid rgba(79,95,173,.3);transition:all var(--transition-fast)}:not(.dark)[_nghost-%COMP%]   .futuristic-user-card[_ngcontent-%COMP%]:hover   .futuristic-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-user-card[_ngcontent-%COMP%]:hover   .futuristic-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{border-color:#4f5fad;box-shadow:0 0 15px #4f5fad80}:not(.dark)[_nghost-%COMP%]   .futuristic-online-indicator[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-online-indicator[_ngcontent-%COMP%]{position:absolute;bottom:0;right:0;width:12px;height:12px;background-color:#4caf50;border-radius:50%;border:2px solid #ffffff;box-shadow:0 0 8px #4caf50cc;animation:_ngcontent-%COMP%_pulse-light 2s infinite}@keyframes _ngcontent-%COMP%_pulse-light{0%{box-shadow:0 0 #4caf5066}70%{box-shadow:0 0 0 6px #4caf5000}to{box-shadow:0 0 #4caf5000}}:not(.dark)[_nghost-%COMP%]   .futuristic-user-info[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-user-info[_ngcontent-%COMP%]{flex:1;min-width:0}:not(.dark)[_nghost-%COMP%]   .futuristic-username[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-username[_ngcontent-%COMP%]{font-size:.875rem;font-weight:600;color:#4f5fad;margin-bottom:.25rem;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}:not(.dark)[_nghost-%COMP%]   .futuristic-user-email[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-user-email[_ngcontent-%COMP%]{font-size:.75rem;color:#6d6870;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}:not(.dark)[_nghost-%COMP%]   .futuristic-call-buttons[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-call-buttons[_ngcontent-%COMP%]{display:flex;justify-content:flex-end;padding:.5rem 1rem;background-color:#4f5fad0d;border-top:1px solid rgba(79,95,173,.1)}:not(.dark)[_nghost-%COMP%]   .futuristic-call-button[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-call-button[_ngcontent-%COMP%]{width:32px;height:32px;display:flex;align-items:center;justify-content:center;background-color:#4f5fad1a;color:#4f5fad;border:none;border-radius:50%;cursor:pointer;transition:all var(--transition-fast);margin-left:.5rem}:not(.dark)[_nghost-%COMP%]   .futuristic-call-button[_ngcontent-%COMP%]:hover, :not(.dark)   [_nghost-%COMP%]   .futuristic-call-button[_ngcontent-%COMP%]:hover{background-color:#4f5fad33;transform:translateY(-2px);box-shadow:0 0 15px #4f5fad66}.dark[_nghost-%COMP%]   .futuristic-users-grid[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-users-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fill,minmax(280px,1fr));gap:1rem;list-style:none;padding:0;margin:0}.dark[_nghost-%COMP%]   .futuristic-user-card[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-user-card[_ngcontent-%COMP%]{background-color:#00f7ff0d;border:1px solid rgba(0,247,255,.1);border-radius:var(--border-radius-md);overflow:hidden;transition:all var(--transition-fast);display:flex;flex-direction:column}.dark[_nghost-%COMP%]   .futuristic-user-card[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .futuristic-user-card[_ngcontent-%COMP%]:hover{transform:translateY(-4px);box-shadow:var(--glow-effect);background-color:#00f7ff1a;border-color:#00f7ff4d}.dark[_nghost-%COMP%]   .futuristic-user-content[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-user-content[_ngcontent-%COMP%]{display:flex;align-items:center;padding:1rem;cursor:pointer;flex:1}.dark[_nghost-%COMP%]   .futuristic-avatar[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-avatar[_ngcontent-%COMP%]{position:relative;width:48px;height:48px;flex-shrink:0;margin-right:1rem}.dark[_nghost-%COMP%]   .futuristic-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:100%;height:100%;object-fit:cover;border-radius:50%;border:2px solid rgba(0,247,255,.3);transition:all var(--transition-fast)}.dark[_nghost-%COMP%]   .futuristic-user-card[_ngcontent-%COMP%]:hover   .futuristic-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-user-card[_ngcontent-%COMP%]:hover   .futuristic-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{border-color:var(--accent-color);box-shadow:0 0 15px #00f7ff80}.dark[_nghost-%COMP%]   .futuristic-online-indicator[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-online-indicator[_ngcontent-%COMP%]{position:absolute;bottom:0;right:0;width:12px;height:12px;background-color:var(--success-color);border-radius:50%;border:2px solid var(--dark-bg);box-shadow:0 0 8px #00ff80cc;animation:_ngcontent-%COMP%_pulse-dark 2s infinite}@keyframes _ngcontent-%COMP%_pulse-dark{0%{box-shadow:0 0 #00ff8066}70%{box-shadow:0 0 0 6px #00ff8000}to{box-shadow:0 0 #00ff8000}}.dark[_nghost-%COMP%]   .futuristic-user-info[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-user-info[_ngcontent-%COMP%]{flex:1;min-width:0}.dark[_nghost-%COMP%]   .futuristic-username[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-username[_ngcontent-%COMP%]{font-size:.875rem;font-weight:600;color:var(--text-light);margin-bottom:.25rem;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.dark[_nghost-%COMP%]   .futuristic-user-email[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-user-email[_ngcontent-%COMP%]{font-size:.75rem;color:var(--text-dim);white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.dark[_nghost-%COMP%]   .futuristic-call-buttons[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-call-buttons[_ngcontent-%COMP%]{display:flex;justify-content:flex-end;padding:.5rem 1rem;background-color:#0003;border-top:1px solid rgba(0,247,255,.1)}.dark[_nghost-%COMP%]   .futuristic-call-button[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-call-button[_ngcontent-%COMP%]{width:32px;height:32px;display:flex;align-items:center;justify-content:center;background-color:#00f7ff1a;color:var(--accent-color);border:none;border-radius:50%;cursor:pointer;transition:all var(--transition-fast);margin-left:.5rem}.dark[_nghost-%COMP%]   .futuristic-call-button[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .futuristic-call-button[_ngcontent-%COMP%]:hover{background-color:#00f7ff33;transform:translateY(-2px);box-shadow:var(--glow-effect)}:not(.dark)[_nghost-%COMP%]   .futuristic-loading-more[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-loading-more[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;padding:1rem}:not(.dark)[_nghost-%COMP%]   .futuristic-loading-dots[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-loading-dots[_ngcontent-%COMP%]{display:flex;justify-content:center;margin-bottom:.5rem}:not(.dark)[_nghost-%COMP%]   .futuristic-loading-dot[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-loading-dot[_ngcontent-%COMP%]{width:8px;height:8px;margin:0 4px;background-color:#4f5fad;border-radius:50%;animation:_ngcontent-%COMP%_dot-pulse-light 1.4s infinite ease-in-out;box-shadow:0 0 8px #4f5fad80}@keyframes _ngcontent-%COMP%_dot-pulse-light{0%,to{transform:scale(.5);opacity:.5}50%{transform:scale(1);opacity:1}}:not(.dark)[_nghost-%COMP%]   .futuristic-load-more-container[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-load-more-container[_ngcontent-%COMP%]{display:flex;justify-content:center;padding:1rem}:not(.dark)[_nghost-%COMP%]   .futuristic-load-more-button[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-load-more-button[_ngcontent-%COMP%]{display:flex;align-items:center;padding:.5rem 1rem;background:linear-gradient(135deg,rgba(79,95,173,.1),rgba(79,95,173,.2));color:#4f5fad;border:1px solid rgba(79,95,173,.3);border-radius:var(--border-radius-md);font-size:.875rem;cursor:pointer;transition:all var(--transition-fast)}:not(.dark)[_nghost-%COMP%]   .futuristic-load-more-button[_ngcontent-%COMP%]:hover, :not(.dark)   [_nghost-%COMP%]   .futuristic-load-more-button[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,rgba(79,95,173,.2),rgba(79,95,173,.3));transform:translateY(-2px);box-shadow:0 0 15px #4f5fad66}.dark[_nghost-%COMP%]   .futuristic-loading-more[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-loading-more[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;padding:1rem}.dark[_nghost-%COMP%]   .futuristic-loading-dots[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-loading-dots[_ngcontent-%COMP%]{display:flex;justify-content:center;margin-bottom:.5rem}.dark[_nghost-%COMP%]   .futuristic-loading-dot[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-loading-dot[_ngcontent-%COMP%]{width:8px;height:8px;margin:0 4px;background-color:var(--accent-color);border-radius:50%;animation:_ngcontent-%COMP%_dot-pulse-dark 1.4s infinite ease-in-out;box-shadow:0 0 8px #00f7ff80}@keyframes _ngcontent-%COMP%_dot-pulse-dark{0%,to{transform:scale(.5);opacity:.5}50%{transform:scale(1);opacity:1}}.dark[_nghost-%COMP%]   .futuristic-load-more-container[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-load-more-container[_ngcontent-%COMP%]{display:flex;justify-content:center;padding:1rem}.dark[_nghost-%COMP%]   .futuristic-load-more-button[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-load-more-button[_ngcontent-%COMP%]{display:flex;align-items:center;padding:.5rem 1rem;background:linear-gradient(135deg,rgba(0,247,255,.1),rgba(0,247,255,.2));color:var(--accent-color);border:1px solid rgba(0,247,255,.3);border-radius:var(--border-radius-md);font-size:.875rem;cursor:pointer;transition:all var(--transition-fast)}.dark[_nghost-%COMP%]   .futuristic-load-more-button[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .futuristic-load-more-button[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,rgba(0,247,255,.2),rgba(0,247,255,.3));transform:translateY(-2px);box-shadow:var(--glow-effect)}"]})}}return r})();const Wt=[{path:"",component:u(8076).Z,children:[{path:"",redirectTo:"conversations",pathMatch:"full"},{path:"conversations",component:Rt,data:{title:"Conversations"}},{path:"conversations/chat/:id",component:I,data:{title:"Chat"}},{path:"chat/:id",component:I,data:{title:"Chat"}},{path:"users",component:Yt,data:{title:"Utilisateurs"}},{path:"new",redirectTo:"users",pathMatch:"full"}]}];let Ht=(()=>{class r{static{this.\u0275fac=function(i){return new(i||r)}}static{this.\u0275mod=t.$C({type:r})}static{this.\u0275inj=t.G2t({imports:[p.iI.forChild(Wt),p.iI]})}}return r})();var Qt=u(499),Zt=u(9722),Jt=u(356);let Kt=(()=>{class r{static{this.\u0275fac=function(i){return new(i||r)}}static{this.\u0275mod=t.$C({type:r})}static{this.\u0275inj=t.G2t({providers:[Zt.x,g.b],imports:[f.MD,Ht,l.YN,l.X1,Qt._9,p.iI,Jt.S]})}}return r})()},605:(w,b,u)=>{u.d(b,{Y:()=>_});var f=u(3236),p=u(1584);function _(l=0,v=f.E){return l<0&&(l=0),(0,p.O)(l,l,v)}}}]);