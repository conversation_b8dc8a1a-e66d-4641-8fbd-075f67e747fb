{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/OneDrive/Bureau/Project PI/devBridge/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { BehaviorSubject, Observable, throwError } from 'rxjs';\nimport { map, catchError } from 'rxjs/operators';\nimport { CallType, CallStatus } from '../models/message.model';\nimport { TOGGLE_CALL_MEDIA_MUTATION, INCOMING_CALL_SUBSCRIPTION, CALL_STATUS_CHANGED_SUBSCRIPTION, CALL_SIGNAL_SUBSCRIPTION } from '../graphql/message.graphql';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"apollo-angular\";\nimport * as i2 from \"./logger.service\";\nexport class CallService {\n  constructor(apollo, logger) {\n    this.apollo = apollo;\n    this.logger = logger;\n    // État des appels\n    this.activeCall = new BehaviorSubject(null);\n    this.incomingCall = new BehaviorSubject(null);\n    // Observables publics\n    this.activeCall$ = this.activeCall.asObservable();\n    this.incomingCall$ = this.incomingCall.asObservable();\n    // Propriétés pour la gestion des sons\n    this.sounds = {};\n    this.isPlaying = {};\n    // États simples pour les médias\n    this.isVideoEnabled = true;\n    this.isAudioEnabled = true;\n    // WebRTC\n    this.peerConnection = null;\n    this.localStream = null;\n    this.remoteStream = null;\n    this.localVideoElement = null;\n    this.remoteVideoElement = null;\n    this.currentCallId = null;\n    this.webrtcExchangeStarted = false;\n    this.initializeSounds();\n    this.initializeSubscriptions();\n    this.initializeWebRTC();\n  }\n  /**\n   * Initialise les sons\n   */\n  initializeSounds() {\n    this.createSyntheticSounds();\n  }\n  /**\n   * Crée des sons synthétiques\n   */\n  createSyntheticSounds() {\n    try {\n      const audioContext = new (window.AudioContext || window.webkitAudioContext)();\n      this.sounds['ringtone'] = this.createRingtoneSound(audioContext);\n      this.sounds['call-connected'] = this.createConnectedSound(audioContext);\n      this.sounds['call-end'] = this.createEndSound(audioContext);\n      this.sounds['notification'] = this.createNotificationSound(audioContext);\n    } catch (error) {\n      console.warn('Could not create synthetic sounds:', error);\n    }\n  }\n  /**\n   * Crée une sonnerie\n   */\n  createRingtoneSound(audioContext) {\n    const audio = new Audio();\n    audio.volume = 0.5;\n    let isPlaying = false;\n    let timeoutIds = [];\n    audio.playSynthetic = () => {\n      if (isPlaying) return Promise.resolve();\n      isPlaying = true;\n      const playMelody = () => {\n        if (!isPlaying) return;\n        const melody = [{\n          freq: 659.25,\n          duration: 0.125\n        }, {\n          freq: 587.33,\n          duration: 0.125\n        }, {\n          freq: 739.99,\n          duration: 0.25\n        }, {\n          freq: 783.99,\n          duration: 0.25\n        }];\n        let currentTime = audioContext.currentTime;\n        melody.forEach(note => {\n          if (!isPlaying) return;\n          const oscillator = audioContext.createOscillator();\n          const gainNode = audioContext.createGain();\n          oscillator.connect(gainNode);\n          gainNode.connect(audioContext.destination);\n          oscillator.frequency.value = note.freq;\n          oscillator.type = 'square';\n          gainNode.gain.setValueAtTime(0, currentTime);\n          gainNode.gain.linearRampToValueAtTime(0.3, currentTime + 0.02);\n          gainNode.gain.exponentialRampToValueAtTime(0.01, currentTime + note.duration);\n          oscillator.start(currentTime);\n          oscillator.stop(currentTime + note.duration);\n          currentTime += note.duration + 0.05;\n        });\n        const timeoutId = setTimeout(() => {\n          if (isPlaying) playMelody();\n        }, (currentTime - audioContext.currentTime + 0.8) * 1000);\n        timeoutIds.push(timeoutId);\n      };\n      playMelody();\n      return Promise.resolve();\n    };\n    audio.stopSynthetic = () => {\n      isPlaying = false;\n      timeoutIds.forEach(id => clearTimeout(id));\n      timeoutIds = [];\n    };\n    return audio;\n  }\n  /**\n   * Crée un son de connexion\n   */\n  createConnectedSound(audioContext) {\n    const audio = new Audio();\n    audio.volume = 0.5;\n    audio.playSynthetic = () => {\n      const melody = [{\n        freq: 523.25,\n        duration: 0.15\n      }, {\n        freq: 659.25,\n        duration: 0.15\n      }, {\n        freq: 783.99,\n        duration: 0.15\n      }, {\n        freq: 1046.5,\n        duration: 0.4\n      }];\n      let currentTime = audioContext.currentTime;\n      melody.forEach(note => {\n        const oscillator = audioContext.createOscillator();\n        const gainNode = audioContext.createGain();\n        oscillator.connect(gainNode);\n        gainNode.connect(audioContext.destination);\n        oscillator.frequency.value = note.freq;\n        oscillator.type = 'triangle';\n        gainNode.gain.setValueAtTime(0, currentTime);\n        gainNode.gain.linearRampToValueAtTime(0.25, currentTime + 0.02);\n        gainNode.gain.exponentialRampToValueAtTime(0.01, currentTime + note.duration);\n        oscillator.start(currentTime);\n        oscillator.stop(currentTime + note.duration);\n        currentTime += note.duration * 0.8;\n      });\n      return Promise.resolve();\n    };\n    return audio;\n  }\n  /**\n   * Crée un son de fin d'appel\n   */\n  createEndSound(audioContext) {\n    const audio = new Audio();\n    audio.volume = 0.4;\n    audio.playSynthetic = () => {\n      const melody = [{\n        freq: 783.99,\n        duration: 0.2\n      }, {\n        freq: 659.25,\n        duration: 0.2\n      }, {\n        freq: 523.25,\n        duration: 0.2\n      }, {\n        freq: 392.0,\n        duration: 0.4\n      }];\n      let currentTime = audioContext.currentTime;\n      melody.forEach(note => {\n        const oscillator = audioContext.createOscillator();\n        const gainNode = audioContext.createGain();\n        oscillator.connect(gainNode);\n        gainNode.connect(audioContext.destination);\n        oscillator.frequency.value = note.freq;\n        oscillator.type = 'sine';\n        gainNode.gain.setValueAtTime(0, currentTime);\n        gainNode.gain.linearRampToValueAtTime(0.2, currentTime + 0.05);\n        gainNode.gain.exponentialRampToValueAtTime(0.01, currentTime + note.duration);\n        oscillator.start(currentTime);\n        oscillator.stop(currentTime + note.duration);\n        currentTime += note.duration * 0.9;\n      });\n      return Promise.resolve();\n    };\n    return audio;\n  }\n  /**\n   * Crée un son de notification\n   */\n  createNotificationSound(audioContext) {\n    const audio = new Audio();\n    audio.volume = 0.6;\n    audio.playSynthetic = () => {\n      const notes = [{\n        freq: 523.25,\n        duration: 0.15,\n        delay: 0\n      }, {\n        freq: 783.99,\n        duration: 0.25,\n        delay: 0.2\n      }];\n      notes.forEach(note => {\n        setTimeout(() => {\n          const oscillator = audioContext.createOscillator();\n          const gainNode = audioContext.createGain();\n          oscillator.connect(gainNode);\n          gainNode.connect(audioContext.destination);\n          oscillator.frequency.value = note.freq;\n          oscillator.type = 'triangle';\n          const startTime = audioContext.currentTime;\n          gainNode.gain.setValueAtTime(0, startTime);\n          gainNode.gain.linearRampToValueAtTime(0.4, startTime + 0.02);\n          gainNode.gain.exponentialRampToValueAtTime(0.01, startTime + note.duration);\n          oscillator.start(startTime);\n          oscillator.stop(startTime + note.duration);\n        }, note.delay * 1000);\n      });\n      return Promise.resolve();\n    };\n    return audio;\n  }\n  /**\n   * Joue un son\n   */\n  play(soundName, loop = false) {\n    const sound = this.sounds[soundName];\n    if (!sound) return;\n    this.isPlaying[soundName] = true;\n    if (sound.playSynthetic) {\n      sound.playSynthetic();\n      if (loop) {\n        const interval = setInterval(() => {\n          if (this.isPlaying[soundName]) {\n            sound.playSynthetic();\n          } else {\n            clearInterval(interval);\n          }\n        }, 3000);\n      }\n    }\n  }\n  /**\n   * Arrête un son\n   */\n  stop(soundName) {\n    this.isPlaying[soundName] = false;\n    const sound = this.sounds[soundName];\n    if (sound && sound.stopSynthetic) {\n      sound.stopSynthetic();\n    }\n  }\n  /**\n   * Arrête tous les sons\n   */\n  stopAllSounds() {\n    Object.keys(this.sounds).forEach(name => {\n      this.stop(name);\n    });\n  }\n  /**\n   * Initialise les subscriptions\n   */\n  initializeSubscriptions() {\n    setTimeout(() => {\n      this.subscribeToIncomingCalls();\n      this.subscribeToCallStatusChanges();\n      this.subscribeToCallSignals();\n    }, 1000);\n  }\n  /**\n   * S'abonne aux appels entrants\n   */\n  subscribeToIncomingCalls() {\n    this.apollo.subscribe({\n      query: INCOMING_CALL_SUBSCRIPTION,\n      errorPolicy: 'all'\n    }).subscribe({\n      next: ({\n        data,\n        errors\n      }) => {\n        if (data?.incomingCall) {\n          this.handleIncomingCall(data.incomingCall);\n        }\n      },\n      error: error => {\n        console.error('Error in incoming call subscription:', error);\n        setTimeout(() => this.subscribeToIncomingCalls(), 5000);\n      }\n    });\n  }\n  /**\n   * S'abonne aux changements de statut d'appel\n   */\n  subscribeToCallStatusChanges() {\n    this.apollo.subscribe({\n      query: CALL_STATUS_CHANGED_SUBSCRIPTION,\n      errorPolicy: 'all'\n    }).subscribe({\n      next: ({\n        data,\n        errors\n      }) => {\n        if (data?.callStatusChanged) {\n          this.handleCallStatusChange(data.callStatusChanged);\n        }\n      },\n      error: error => {\n        console.error('Error in call status subscription:', error);\n        setTimeout(() => this.subscribeToCallStatusChanges(), 5000);\n      }\n    });\n  }\n  /**\n   * S'abonne aux signaux d'appel\n   */\n  subscribeToCallSignals() {\n    this.apollo.subscribe({\n      query: CALL_SIGNAL_SUBSCRIPTION,\n      errorPolicy: 'all'\n    }).subscribe({\n      next: ({\n        data,\n        errors\n      }) => {\n        if (data?.callSignal) {\n          this.handleCallSignal(data.callSignal);\n        }\n      },\n      error: error => {\n        console.error('Error in call signal subscription:', error);\n        setTimeout(() => this.subscribeToCallSignals(), 5000);\n      }\n    });\n  }\n  /**\n   * Gère un appel entrant\n   */\n  handleIncomingCall(call) {\n    this.incomingCall.next(call);\n    this.play('ringtone', true);\n  }\n  /**\n   * Gère les changements de statut d'appel\n   */\n  handleCallStatusChange(call) {\n    switch (call.status) {\n      case CallStatus.CONNECTED:\n        this.stop('ringtone');\n        this.play('call-connected');\n        this.activeCall.next(call);\n        this.incomingCall.next(null);\n        break;\n      case CallStatus.ENDED:\n        this.play('call-end');\n        this.activeCall.next(null);\n        this.incomingCall.next(null);\n        this.cleanupWebRTC();\n        break;\n      case CallStatus.REJECTED:\n        this.stop('ringtone');\n        this.play('call-end');\n        this.activeCall.next(null);\n        this.incomingCall.next(null);\n        break;\n    }\n  }\n  /**\n   * Gère les signaux d'appel\n   */\n  handleCallSignal(signal) {\n    if (!this.peerConnection) return;\n    switch (signal.type) {\n      case 'offer':\n        this.handleRemoteOffer(signal);\n        break;\n      case 'answer':\n        this.handleRemoteAnswer(signal);\n        break;\n      case 'ice-candidate':\n        this.handleRemoteICECandidate(signal);\n        break;\n    }\n  }\n  /**\n   * Initialise WebRTC\n   */\n  initializeWebRTC() {\n    const configuration = {\n      iceServers: [{\n        urls: 'stun:stun.l.google.com:19302'\n      }, {\n        urls: 'stun:stun1.l.google.com:19302'\n      }]\n    };\n    try {\n      this.peerConnection = new RTCPeerConnection(configuration);\n      this.setupPeerConnectionEvents();\n    } catch (error) {\n      console.error('Failed to initialize WebRTC:', error);\n    }\n  }\n  /**\n   * Configure les événements de la PeerConnection\n   */\n  setupPeerConnectionEvents() {\n    if (!this.peerConnection) return;\n    this.peerConnection.ontrack = event => {\n      this.remoteStream = event.streams[0];\n      this.attachRemoteStream();\n    };\n    this.peerConnection.onicecandidate = event => {\n      if (event.candidate) {\n        this.sendSignal('ice-candidate', JSON.stringify(event.candidate));\n      }\n    };\n  }\n  /**\n   * Attache le stream distant\n   */\n  attachRemoteStream() {\n    if (this.remoteStream && this.remoteVideoElement) {\n      this.remoteVideoElement.srcObject = this.remoteStream;\n      this.remoteVideoElement.muted = false;\n      this.remoteVideoElement.volume = 1;\n      this.remoteVideoElement.play();\n    }\n    // Attacher à tous les éléments vidéo disponibles\n    const videos = document.querySelectorAll('video');\n    for (let i = 1; i < videos.length; i++) {\n      if (this.remoteStream && !videos[i].srcObject) {\n        videos[i].srcObject = this.remoteStream;\n        videos[i].muted = false;\n        videos[i].volume = 1;\n        videos[i].play();\n        break;\n      }\n    }\n  }\n  /**\n   * Initie un appel - VERSION TEMPORAIRE SANS BACKEND\n   */\n  initiateCall(recipientId, callType, conversationId) {\n    console.log('🔄 [CallService] Initiating call (temporary version):', {\n      recipientId,\n      callType,\n      conversationId\n    });\n    // Créer un appel factice pour les tests\n    const fakeCall = {\n      id: 'call_' + Date.now(),\n      caller: {\n        _id: 'current-user',\n        id: 'current-user',\n        username: 'You',\n        email: '<EMAIL>',\n        role: 'user',\n        isActive: true,\n        image: '/assets/images/default-avatar.png'\n      },\n      recipient: {\n        _id: recipientId,\n        id: recipientId,\n        username: 'Other User',\n        email: '<EMAIL>',\n        role: 'user',\n        isActive: true,\n        image: '/assets/images/default-avatar.png'\n      },\n      type: callType,\n      status: CallStatus.RINGING,\n      startTime: new Date().toISOString(),\n      conversationId: conversationId || ''\n    };\n    // Simuler un délai réseau\n    return new Observable(observer => {\n      setTimeout(() => {\n        console.log('✅ [CallService] Fake call created:', fakeCall);\n        this.activeCall.next(fakeCall);\n        this.currentCallId = fakeCall.id;\n        // Jouer la sonnerie\n        this.play('ringtone', true);\n        // Démarrer les médias pour l'appel sortant\n        this.startOutgoingCallMedia(callType);\n        // Simuler un appel entrant après 3 secondes pour tester l'acceptation\n        setTimeout(() => {\n          this.simulateIncomingCall(fakeCall);\n        }, 3000);\n        observer.next(fakeCall);\n        observer.complete();\n      }, 500);\n    });\n    // VERSION AVEC BACKEND (à réactiver quand le backend sera prêt)\n    /*\n    const callId = 'call_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);\n         return this.apollo\n      .mutate<{ initiateCall: Call }>({\n        mutation: INITIATE_CALL_MUTATION,\n        variables: {\n          recipientId,\n          callType,\n          callId,\n          offer: '',\n          conversationId,\n        },\n      })\n      .pipe(\n        map((result) => {\n          if (!result.data?.initiateCall) {\n            throw new Error('No call data received from server');\n          }\n          const call = result.data.initiateCall;\n          this.activeCall.next(call);\n          this.currentCallId = call.id;\n          return call;\n        }),\n        catchError((error) => {\n          console.error('Error initiating call:', error);\n          return throwError(() => new Error(\"Erreur lors de l'initiation de l'appel: \" + error.message));\n        })\n      );\n    */\n  }\n  /**\n   * Accepte un appel - VERSION TEMPORAIRE\n   */\n  acceptCall(incomingCall) {\n    console.log('🔄 [CallService] Accepting call (temporary version):', incomingCall);\n    // Créer un appel accepté factice\n    const acceptedCall = {\n      id: incomingCall.id,\n      caller: incomingCall.caller,\n      recipient: {\n        _id: 'current-user',\n        id: 'current-user',\n        username: 'You',\n        email: '<EMAIL>',\n        role: 'user',\n        isActive: true,\n        image: '/assets/images/default-avatar.png'\n      },\n      type: incomingCall.type,\n      status: CallStatus.CONNECTED,\n      startTime: new Date().toISOString(),\n      conversationId: incomingCall.conversationId || ''\n    };\n    return new Observable(observer => {\n      setTimeout(() => {\n        console.log('✅ [CallService] Call accepted (fake):', acceptedCall);\n        this.stop('ringtone');\n        this.play('call-connected');\n        // Démarrer les médias pour l'appel\n        const callWithMedia = this.startMediaForCall(incomingCall, acceptedCall);\n        this.activeCall.next(callWithMedia);\n        this.incomingCall.next(null);\n        observer.next(callWithMedia);\n        observer.complete();\n      }, 300);\n    });\n  }\n  /**\n   * Rejette un appel - VERSION TEMPORAIRE\n   */\n  rejectCall(callId, reason) {\n    console.log('🔄 [CallService] Rejecting call (temporary version):', {\n      callId,\n      reason\n    });\n    const fakeSuccess = {\n      success: true,\n      message: 'Call rejected successfully'\n    };\n    return new Observable(observer => {\n      setTimeout(() => {\n        console.log('✅ [CallService] Call rejected (fake):', fakeSuccess);\n        this.stop('ringtone');\n        this.incomingCall.next(null);\n        this.activeCall.next(null);\n        this.play('call-end');\n        observer.next(fakeSuccess);\n        observer.complete();\n      }, 200);\n    });\n  }\n  /**\n   * Termine un appel - VERSION TEMPORAIRE\n   */\n  endCall(callId) {\n    console.log('🔄 [CallService] Ending call (temporary version):', callId);\n    const fakeSuccess = {\n      success: true,\n      message: 'Call ended successfully'\n    };\n    return new Observable(observer => {\n      setTimeout(() => {\n        console.log('✅ [CallService] Call ended (fake):', fakeSuccess);\n        this.play('call-end');\n        this.activeCall.next(null);\n        this.incomingCall.next(null);\n        this.cleanupWebRTC();\n        observer.next(fakeSuccess);\n        observer.complete();\n      }, 200);\n    });\n  }\n  /**\n   * Configure les éléments vidéo\n   */\n  setVideoElements(localVideo, remoteVideo) {\n    this.localVideoElement = localVideo;\n    this.remoteVideoElement = remoteVideo;\n  }\n  /**\n   * Envoie un signal\n   */\n  sendSignal(type, data) {\n    // Simulation pour les tests\n    setTimeout(() => {\n      this.handleCallSignal({\n        callId: this.currentCallId,\n        senderId: 'other-user',\n        type,\n        data\n      });\n    }, 100);\n  }\n  /**\n   * Obtient les médias utilisateur (caméra/micro)\n   */\n  getUserMedia(callType) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      console.log('🎥 [CallService] Getting user media for:', callType);\n      const constraints = {\n        audio: true,\n        video: callType === CallType.VIDEO\n      };\n      try {\n        const stream = yield navigator.mediaDevices.getUserMedia(constraints);\n        console.log('✅ [CallService] User media obtained');\n        _this.localStream = stream;\n        return stream;\n      } catch (error) {\n        console.error('❌ [CallService] Error getting user media:', error);\n        throw new Error(\"Impossible d'accéder au microphone/caméra\");\n      }\n    })();\n  }\n  /**\n   * Crée une offre WebRTC\n   */\n  createOffer() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this2.peerConnection) {\n        throw new Error('PeerConnection not initialized');\n      }\n      try {\n        const offer = yield _this2.peerConnection.createOffer({\n          offerToReceiveAudio: true,\n          offerToReceiveVideo: true\n        });\n        yield _this2.peerConnection.setLocalDescription(offer);\n        console.log('✅ [CallService] Offer created and set as local description');\n        return offer;\n      } catch (error) {\n        console.error('❌ [CallService] Error creating offer:', error);\n        throw error;\n      }\n    })();\n  }\n  /**\n   * Crée une réponse WebRTC\n   */\n  createAnswer(offer) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this3.peerConnection) {\n        throw new Error('PeerConnection not initialized');\n      }\n      try {\n        yield _this3.peerConnection.setRemoteDescription(offer);\n        console.log('✅ [CallService] Remote description set');\n        const answer = yield _this3.peerConnection.createAnswer();\n        yield _this3.peerConnection.setLocalDescription(answer);\n        console.log('✅ [CallService] Answer created and set as local description');\n        return answer;\n      } catch (error) {\n        console.error('❌ [CallService] Error creating answer:', error);\n        throw error;\n      }\n    })();\n  }\n  /**\n   * Gère une offre distante\n   */\n  handleRemoteOffer(signal) {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const offer = JSON.parse(signal.data);\n        console.log('📥 [CallService] Handling remote offer:', offer);\n        // Obtenir les médias utilisateur\n        const stream = yield _this4.getUserMedia(CallType.AUDIO);\n        _this4.addLocalStreamToPeerConnection(stream);\n        // Créer et envoyer la réponse\n        const answer = yield _this4.createAnswer(offer);\n        _this4.sendSignal('answer', JSON.stringify(answer));\n        console.log('✅ [CallService] Remote offer handled successfully');\n      } catch (error) {\n        console.error('❌ [CallService] Error handling remote offer:', error);\n      }\n    })();\n  }\n  /**\n   * Gère une réponse distante\n   */\n  handleRemoteAnswer(signal) {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const answer = JSON.parse(signal.data);\n        console.log('📥 [CallService] Handling remote answer:', answer);\n        if (_this5.peerConnection) {\n          yield _this5.peerConnection.setRemoteDescription(answer);\n          console.log('✅ [CallService] Remote answer set successfully');\n        }\n      } catch (error) {\n        console.error('❌ [CallService] Error handling remote answer:', error);\n      }\n    })();\n  }\n  /**\n   * Gère un candidat ICE distant\n   */\n  handleRemoteICECandidate(signal) {\n    var _this6 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const candidate = JSON.parse(signal.data);\n        console.log('📥 [CallService] Handling remote ICE candidate:', candidate);\n        if (_this6.peerConnection && candidate) {\n          yield _this6.peerConnection.addIceCandidate(new RTCIceCandidate(candidate));\n          console.log('✅ [CallService] Remote ICE candidate added successfully');\n        }\n      } catch (error) {\n        console.error('❌ [CallService] Error handling ICE candidate:', error);\n      }\n    })();\n  }\n  /**\n   * Ajoute le stream local à la PeerConnection\n   */\n  addLocalStreamToPeerConnection(stream) {\n    if (!this.peerConnection) return;\n    stream.getTracks().forEach(track => {\n      this.peerConnection.addTrack(track, stream);\n      console.log('✅ [CallService] Track added to peer connection:', track.kind);\n    });\n  }\n  /**\n   * Démarre les médias pour un appel accepté\n   */\n  startMediaForCall(incomingCall, call) {\n    console.log('🎥 [CallService] Starting media for accepted call');\n    // Démarrer les médias en arrière-plan\n    this.getUserMedia(incomingCall.type).then(stream => {\n      this.addLocalStreamToPeerConnection(stream);\n      this.attachLocalStream();\n      console.log('✅ [CallService] Media started for call');\n    }).catch(error => {\n      console.error('❌ [CallService] Error starting media:', error);\n    });\n    return call;\n  }\n  /**\n   * Attache le stream local aux éléments vidéo\n   */\n  attachLocalStream() {\n    if (this.localStream && this.localVideoElement) {\n      this.localVideoElement.srcObject = this.localStream;\n      this.localVideoElement.muted = true; // Éviter l'écho\n      this.localVideoElement.play();\n      console.log('✅ [CallService] Local stream attached');\n    }\n    // Attacher aussi au premier élément vidéo disponible\n    const videos = document.querySelectorAll('video');\n    if (this.localStream && videos.length > 0 && !videos[0].srcObject) {\n      videos[0].srcObject = this.localStream;\n      videos[0].muted = true;\n      videos[0].play();\n    }\n  }\n  /**\n   * Démarre les médias pour un appel sortant\n   */\n  startOutgoingCallMedia(callType) {\n    console.log('🎥 [CallService] Starting outgoing call media');\n    this.getUserMedia(callType).then(stream => {\n      this.addLocalStreamToPeerConnection(stream);\n      this.attachLocalStream();\n      // Créer une offre WebRTC\n      return this.createOffer();\n    }).then(offer => {\n      console.log('✅ [CallService] Offer created for outgoing call');\n      // Dans un vrai scénario, on enverrait cette offre au destinataire\n    }).catch(error => {\n      console.error('❌ [CallService] Error starting outgoing call media:', error);\n    });\n  }\n  /**\n   * Nettoie les ressources WebRTC\n   */\n  cleanupWebRTC() {\n    if (this.localStream) {\n      this.localStream.getTracks().forEach(track => track.stop());\n      this.localStream = null;\n    }\n    if (this.peerConnection) {\n      this.peerConnection.close();\n      this.peerConnection = null;\n    }\n    this.remoteStream = null;\n  }\n  /**\n   * Active les sons après interaction utilisateur\n   */\n  enableSounds() {\n    console.log('Sounds enabled after user interaction');\n  }\n  /**\n   * Bascule l'audio\n   */\n  toggleAudio() {\n    this.isAudioEnabled = !this.isAudioEnabled;\n    return this.isAudioEnabled;\n  }\n  /**\n   * Bascule la vidéo\n   */\n  toggleVideo() {\n    this.isVideoEnabled = !this.isVideoEnabled;\n    return this.isVideoEnabled;\n  }\n  /**\n   * Bascule les médias\n   */\n  toggleMedia(callId, enableVideo, enableAudio) {\n    return this.apollo.mutate({\n      mutation: TOGGLE_CALL_MEDIA_MUTATION,\n      variables: {\n        callId,\n        video: enableVideo,\n        audio: enableAudio\n      }\n    }).pipe(map(result => {\n      return result.data.toggleCallMedia;\n    }), catchError(error => {\n      console.error('Error toggling media:', error);\n      return throwError(() => new Error('Erreur lors du changement des médias'));\n    }));\n  }\n  /**\n   * Simule un appel entrant (pour les tests)\n   */\n  simulateIncomingCall(originalCall) {\n    const incomingCall = {\n      id: originalCall.id,\n      caller: originalCall.recipient,\n      type: originalCall.type,\n      conversationId: originalCall.conversationId,\n      offer: '{\"type\":\"offer\",\"sdp\":\"fake-offer\"}',\n      timestamp: new Date().toISOString()\n    };\n    console.log('📞 [CallService] Simulating incoming call:', incomingCall);\n    this.stop('ringtone'); // Arrêter la sonnerie sortante\n    this.incomingCall.next(incomingCall);\n    this.activeCall.next(null); // Nettoyer l'appel sortant\n    // Jouer la sonnerie d'appel entrant\n    this.play('ringtone', true);\n  }\n  ngOnDestroy() {\n    this.stopAllSounds();\n    this.cleanupWebRTC();\n  }\n  static {\n    this.ɵfac = function CallService_Factory(t) {\n      return new (t || CallService)(i0.ɵɵinject(i1.Apollo), i0.ɵɵinject(i2.LoggerService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: CallService,\n      factory: CallService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["BehaviorSubject", "Observable", "throwError", "map", "catchError", "CallType", "CallStatus", "TOGGLE_CALL_MEDIA_MUTATION", "INCOMING_CALL_SUBSCRIPTION", "CALL_STATUS_CHANGED_SUBSCRIPTION", "CALL_SIGNAL_SUBSCRIPTION", "CallService", "constructor", "apollo", "logger", "activeCall", "incomingCall", "activeCall$", "asObservable", "incomingCall$", "sounds", "isPlaying", "isVideoEnabled", "isAudioEnabled", "peerConnection", "localStream", "remoteStream", "localVideoElement", "remoteVideoElement", "currentCallId", "webrtcExchangeStarted", "initializeSounds", "initializeSubscriptions", "initializeWebRTC", "createSyntheticSounds", "audioContext", "window", "AudioContext", "webkitAudioContext", "createRingtoneSound", "createConnectedSound", "createEndSound", "createNotificationSound", "error", "console", "warn", "audio", "Audio", "volume", "timeoutIds", "playSynthetic", "Promise", "resolve", "playMelody", "melody", "freq", "duration", "currentTime", "for<PERSON>ach", "note", "oscillator", "createOscillator", "gainNode", "createGain", "connect", "destination", "frequency", "value", "type", "gain", "setValueAtTime", "linearRampToValueAtTime", "exponentialRampToValueAtTime", "start", "stop", "timeoutId", "setTimeout", "push", "stopSynthetic", "id", "clearTimeout", "notes", "delay", "startTime", "play", "soundName", "loop", "sound", "interval", "setInterval", "clearInterval", "stopAllSounds", "Object", "keys", "name", "subscribeToIncomingCalls", "subscribeToCallStatusChanges", "subscribeToCallSignals", "subscribe", "query", "errorPolicy", "next", "data", "errors", "handleIncomingCall", "callStatusChanged", "handleCallStatusChange", "callSignal", "handleCallSignal", "call", "status", "CONNECTED", "ENDED", "cleanupWebRTC", "REJECTED", "signal", "handleRemoteOffer", "handleRemoteAnswer", "handleRemoteICECandidate", "configuration", "iceServers", "urls", "RTCPeerConnection", "setupPeerConnectionEvents", "ontrack", "event", "streams", "attachRemoteStream", "onicecandidate", "candidate", "sendSignal", "JSON", "stringify", "srcObject", "muted", "videos", "document", "querySelectorAll", "i", "length", "initiateCall", "recipientId", "callType", "conversationId", "log", "fakeCall", "Date", "now", "caller", "_id", "username", "email", "role", "isActive", "image", "recipient", "RINGING", "toISOString", "observer", "startOutgoingCallMedia", "simulateIncomingCall", "complete", "acceptCall", "acceptedCall", "callWithMedia", "startMediaForCall", "rejectCall", "callId", "reason", "fakeSuccess", "success", "message", "endCall", "setVideoElements", "localVideo", "remoteVideo", "senderId", "getUserMedia", "_this", "_asyncToGenerator", "constraints", "video", "VIDEO", "stream", "navigator", "mediaDevices", "Error", "createOffer", "_this2", "offer", "offerToReceiveAudio", "offerToReceiveVideo", "setLocalDescription", "createAnswer", "_this3", "setRemoteDescription", "answer", "_this4", "parse", "AUDIO", "addLocalStreamToPeerConnection", "_this5", "_this6", "addIceCandidate", "RTCIceCandidate", "getTracks", "track", "addTrack", "kind", "then", "attachLocalStream", "catch", "close", "enableSounds", "toggleAudio", "toggleVideo", "toggleMedia", "enableVideo", "enableAudio", "mutate", "mutation", "variables", "pipe", "result", "toggleCallMedia", "originalCall", "timestamp", "ngOnDestroy", "i0", "ɵɵinject", "i1", "Apollo", "i2", "LoggerService", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\services\\call.service.ts"], "sourcesContent": ["import { Injectable, <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';\nimport { Apollo } from 'apollo-angular';\nimport { BehaviorSubject, Observable, throwError, from } from 'rxjs';\nimport { map, catchError, switchMap } from 'rxjs/operators';\nimport {\n  Call,\n  CallType,\n  CallStatus,\n  IncomingCall,\n  CallSuccess,\n} from '../models/message.model';\nimport {\n  INITIATE_CALL_MUTATION,\n  ACCEPT_CALL_MUTATION,\n  REJECT_CALL_MUTATION,\n  END_CALL_MUTATION,\n  TOGGLE_CALL_MEDIA_MUTATION,\n  INCOMING_CALL_SUBSCRIPTION,\n  CALL_STATUS_CHANGED_SUBSCRIPTION,\n  CALL_SIGNAL_SUBSCRIPTION,\n} from '../graphql/message.graphql';\nimport { LoggerService } from './logger.service';\n\n@Injectable({\n  providedIn: 'root',\n})\nexport class CallService implements OnDestroy {\n  // État des appels\n  private activeCall = new BehaviorSubject<Call | null>(null);\n  private incomingCall = new BehaviorSubject<IncomingCall | null>(null);\n\n  // Observables publics\n  public activeCall$ = this.activeCall.asObservable();\n  public incomingCall$ = this.incomingCall.asObservable();\n\n  // Propriétés pour la gestion des sons\n  private sounds: { [key: string]: HTMLAudioElement } = {};\n  private isPlaying: { [key: string]: boolean } = {};\n\n  // États simples pour les médias\n  private isVideoEnabled = true;\n  private isAudioEnabled = true;\n\n  // WebRTC\n  private peerConnection: RTCPeerConnection | null = null;\n  private localStream: MediaStream | null = null;\n  private remoteStream: MediaStream | null = null;\n  private localVideoElement: HTMLVideoElement | null = null;\n  private remoteVideoElement: HTMLVideoElement | null = null;\n  private currentCallId: string | null = null;\n  private webrtcExchangeStarted: boolean = false;\n\n  constructor(private apollo: Apollo, private logger: LoggerService) {\n    this.initializeSounds();\n    this.initializeSubscriptions();\n    this.initializeWebRTC();\n  }\n\n  /**\n   * Initialise les sons\n   */\n  private initializeSounds(): void {\n    this.createSyntheticSounds();\n  }\n\n  /**\n   * Crée des sons synthétiques\n   */\n  private createSyntheticSounds(): void {\n    try {\n      const audioContext = new (window.AudioContext ||\n        (window as any).webkitAudioContext)();\n      this.sounds['ringtone'] = this.createRingtoneSound(audioContext);\n      this.sounds['call-connected'] = this.createConnectedSound(audioContext);\n      this.sounds['call-end'] = this.createEndSound(audioContext);\n      this.sounds['notification'] = this.createNotificationSound(audioContext);\n    } catch (error) {\n      console.warn('Could not create synthetic sounds:', error);\n    }\n  }\n\n  /**\n   * Crée une sonnerie\n   */\n  private createRingtoneSound(audioContext: AudioContext): HTMLAudioElement {\n    const audio = new Audio();\n    audio.volume = 0.5;\n    let isPlaying = false;\n    let timeoutIds: any[] = [];\n\n    (audio as any).playSynthetic = () => {\n      if (isPlaying) return Promise.resolve();\n      isPlaying = true;\n\n      const playMelody = () => {\n        if (!isPlaying) return;\n        const melody = [\n          { freq: 659.25, duration: 0.125 },\n          { freq: 587.33, duration: 0.125 },\n          { freq: 739.99, duration: 0.25 },\n          { freq: 783.99, duration: 0.25 },\n        ];\n\n        let currentTime = audioContext.currentTime;\n        melody.forEach((note) => {\n          if (!isPlaying) return;\n          const oscillator = audioContext.createOscillator();\n          const gainNode = audioContext.createGain();\n          oscillator.connect(gainNode);\n          gainNode.connect(audioContext.destination);\n          oscillator.frequency.value = note.freq;\n          oscillator.type = 'square';\n          gainNode.gain.setValueAtTime(0, currentTime);\n          gainNode.gain.linearRampToValueAtTime(0.3, currentTime + 0.02);\n          gainNode.gain.exponentialRampToValueAtTime(\n            0.01,\n            currentTime + note.duration\n          );\n          oscillator.start(currentTime);\n          oscillator.stop(currentTime + note.duration);\n          currentTime += note.duration + 0.05;\n        });\n\n        const timeoutId = setTimeout(() => {\n          if (isPlaying) playMelody();\n        }, (currentTime - audioContext.currentTime + 0.8) * 1000);\n        timeoutIds.push(timeoutId);\n      };\n\n      playMelody();\n      return Promise.resolve();\n    };\n\n    (audio as any).stopSynthetic = () => {\n      isPlaying = false;\n      timeoutIds.forEach((id) => clearTimeout(id));\n      timeoutIds = [];\n    };\n\n    return audio;\n  }\n\n  /**\n   * Crée un son de connexion\n   */\n  private createConnectedSound(audioContext: AudioContext): HTMLAudioElement {\n    const audio = new Audio();\n    audio.volume = 0.5;\n\n    (audio as any).playSynthetic = () => {\n      const melody = [\n        { freq: 523.25, duration: 0.15 },\n        { freq: 659.25, duration: 0.15 },\n        { freq: 783.99, duration: 0.15 },\n        { freq: 1046.5, duration: 0.4 },\n      ];\n\n      let currentTime = audioContext.currentTime;\n      melody.forEach((note) => {\n        const oscillator = audioContext.createOscillator();\n        const gainNode = audioContext.createGain();\n        oscillator.connect(gainNode);\n        gainNode.connect(audioContext.destination);\n        oscillator.frequency.value = note.freq;\n        oscillator.type = 'triangle';\n        gainNode.gain.setValueAtTime(0, currentTime);\n        gainNode.gain.linearRampToValueAtTime(0.25, currentTime + 0.02);\n        gainNode.gain.exponentialRampToValueAtTime(\n          0.01,\n          currentTime + note.duration\n        );\n        oscillator.start(currentTime);\n        oscillator.stop(currentTime + note.duration);\n        currentTime += note.duration * 0.8;\n      });\n\n      return Promise.resolve();\n    };\n\n    return audio;\n  }\n\n  /**\n   * Crée un son de fin d'appel\n   */\n  private createEndSound(audioContext: AudioContext): HTMLAudioElement {\n    const audio = new Audio();\n    audio.volume = 0.4;\n\n    (audio as any).playSynthetic = () => {\n      const melody = [\n        { freq: 783.99, duration: 0.2 },\n        { freq: 659.25, duration: 0.2 },\n        { freq: 523.25, duration: 0.2 },\n        { freq: 392.0, duration: 0.4 },\n      ];\n\n      let currentTime = audioContext.currentTime;\n      melody.forEach((note) => {\n        const oscillator = audioContext.createOscillator();\n        const gainNode = audioContext.createGain();\n        oscillator.connect(gainNode);\n        gainNode.connect(audioContext.destination);\n        oscillator.frequency.value = note.freq;\n        oscillator.type = 'sine';\n        gainNode.gain.setValueAtTime(0, currentTime);\n        gainNode.gain.linearRampToValueAtTime(0.2, currentTime + 0.05);\n        gainNode.gain.exponentialRampToValueAtTime(\n          0.01,\n          currentTime + note.duration\n        );\n        oscillator.start(currentTime);\n        oscillator.stop(currentTime + note.duration);\n        currentTime += note.duration * 0.9;\n      });\n\n      return Promise.resolve();\n    };\n\n    return audio;\n  }\n\n  /**\n   * Crée un son de notification\n   */\n  private createNotificationSound(\n    audioContext: AudioContext\n  ): HTMLAudioElement {\n    const audio = new Audio();\n    audio.volume = 0.6;\n\n    (audio as any).playSynthetic = () => {\n      const notes = [\n        { freq: 523.25, duration: 0.15, delay: 0 },\n        { freq: 783.99, duration: 0.25, delay: 0.2 },\n      ];\n\n      notes.forEach((note) => {\n        setTimeout(() => {\n          const oscillator = audioContext.createOscillator();\n          const gainNode = audioContext.createGain();\n          oscillator.connect(gainNode);\n          gainNode.connect(audioContext.destination);\n          oscillator.frequency.value = note.freq;\n          oscillator.type = 'triangle';\n          const startTime = audioContext.currentTime;\n          gainNode.gain.setValueAtTime(0, startTime);\n          gainNode.gain.linearRampToValueAtTime(0.4, startTime + 0.02);\n          gainNode.gain.exponentialRampToValueAtTime(\n            0.01,\n            startTime + note.duration\n          );\n          oscillator.start(startTime);\n          oscillator.stop(startTime + note.duration);\n        }, note.delay * 1000);\n      });\n\n      return Promise.resolve();\n    };\n\n    return audio;\n  }\n\n  /**\n   * Joue un son\n   */\n  private play(soundName: string, loop: boolean = false): void {\n    const sound = this.sounds[soundName];\n    if (!sound) return;\n\n    this.isPlaying[soundName] = true;\n\n    if ((sound as any).playSynthetic) {\n      (sound as any).playSynthetic();\n      if (loop) {\n        const interval = setInterval(() => {\n          if (this.isPlaying[soundName]) {\n            (sound as any).playSynthetic();\n          } else {\n            clearInterval(interval);\n          }\n        }, 3000);\n      }\n    }\n  }\n\n  /**\n   * Arrête un son\n   */\n  private stop(soundName: string): void {\n    this.isPlaying[soundName] = false;\n    const sound = this.sounds[soundName];\n    if (sound && (sound as any).stopSynthetic) {\n      (sound as any).stopSynthetic();\n    }\n  }\n\n  /**\n   * Arrête tous les sons\n   */\n  private stopAllSounds(): void {\n    Object.keys(this.sounds).forEach((name) => {\n      this.stop(name);\n    });\n  }\n\n  /**\n   * Initialise les subscriptions\n   */\n  private initializeSubscriptions(): void {\n    setTimeout(() => {\n      this.subscribeToIncomingCalls();\n      this.subscribeToCallStatusChanges();\n      this.subscribeToCallSignals();\n    }, 1000);\n  }\n\n  /**\n   * S'abonne aux appels entrants\n   */\n  private subscribeToIncomingCalls(): void {\n    this.apollo\n      .subscribe<{ incomingCall: IncomingCall }>({\n        query: INCOMING_CALL_SUBSCRIPTION,\n        errorPolicy: 'all',\n      })\n      .subscribe({\n        next: ({ data, errors }) => {\n          if (data?.incomingCall) {\n            this.handleIncomingCall(data.incomingCall);\n          }\n        },\n        error: (error) => {\n          console.error('Error in incoming call subscription:', error);\n          setTimeout(() => this.subscribeToIncomingCalls(), 5000);\n        },\n      });\n  }\n\n  /**\n   * S'abonne aux changements de statut d'appel\n   */\n  private subscribeToCallStatusChanges(): void {\n    this.apollo\n      .subscribe<{ callStatusChanged: Call }>({\n        query: CALL_STATUS_CHANGED_SUBSCRIPTION,\n        errorPolicy: 'all',\n      })\n      .subscribe({\n        next: ({ data, errors }) => {\n          if (data?.callStatusChanged) {\n            this.handleCallStatusChange(data.callStatusChanged);\n          }\n        },\n        error: (error) => {\n          console.error('Error in call status subscription:', error);\n          setTimeout(() => this.subscribeToCallStatusChanges(), 5000);\n        },\n      });\n  }\n\n  /**\n   * S'abonne aux signaux d'appel\n   */\n  private subscribeToCallSignals(): void {\n    this.apollo\n      .subscribe<{ callSignal: any }>({\n        query: CALL_SIGNAL_SUBSCRIPTION,\n        errorPolicy: 'all',\n      })\n      .subscribe({\n        next: ({ data, errors }) => {\n          if (data?.callSignal) {\n            this.handleCallSignal(data.callSignal);\n          }\n        },\n        error: (error) => {\n          console.error('Error in call signal subscription:', error);\n          setTimeout(() => this.subscribeToCallSignals(), 5000);\n        },\n      });\n  }\n\n  /**\n   * Gère un appel entrant\n   */\n  private handleIncomingCall(call: IncomingCall): void {\n    this.incomingCall.next(call);\n    this.play('ringtone', true);\n  }\n\n  /**\n   * Gère les changements de statut d'appel\n   */\n  private handleCallStatusChange(call: Call): void {\n    switch (call.status) {\n      case CallStatus.CONNECTED:\n        this.stop('ringtone');\n        this.play('call-connected');\n        this.activeCall.next(call);\n        this.incomingCall.next(null);\n        break;\n      case CallStatus.ENDED:\n        this.play('call-end');\n        this.activeCall.next(null);\n        this.incomingCall.next(null);\n        this.cleanupWebRTC();\n        break;\n      case CallStatus.REJECTED:\n        this.stop('ringtone');\n        this.play('call-end');\n        this.activeCall.next(null);\n        this.incomingCall.next(null);\n        break;\n    }\n  }\n\n  /**\n   * Gère les signaux d'appel\n   */\n  private handleCallSignal(signal: any): void {\n    if (!this.peerConnection) return;\n\n    switch (signal.type) {\n      case 'offer':\n        this.handleRemoteOffer(signal);\n        break;\n      case 'answer':\n        this.handleRemoteAnswer(signal);\n        break;\n      case 'ice-candidate':\n        this.handleRemoteICECandidate(signal);\n        break;\n    }\n  }\n\n  /**\n   * Initialise WebRTC\n   */\n  private initializeWebRTC(): void {\n    const configuration: RTCConfiguration = {\n      iceServers: [\n        { urls: 'stun:stun.l.google.com:19302' },\n        { urls: 'stun:stun1.l.google.com:19302' },\n      ],\n    };\n\n    try {\n      this.peerConnection = new RTCPeerConnection(configuration);\n      this.setupPeerConnectionEvents();\n    } catch (error) {\n      console.error('Failed to initialize WebRTC:', error);\n    }\n  }\n\n  /**\n   * Configure les événements de la PeerConnection\n   */\n  private setupPeerConnectionEvents(): void {\n    if (!this.peerConnection) return;\n\n    this.peerConnection.ontrack = (event) => {\n      this.remoteStream = event.streams[0];\n      this.attachRemoteStream();\n    };\n\n    this.peerConnection.onicecandidate = (event) => {\n      if (event.candidate) {\n        this.sendSignal('ice-candidate', JSON.stringify(event.candidate));\n      }\n    };\n  }\n\n  /**\n   * Attache le stream distant\n   */\n  private attachRemoteStream(): void {\n    if (this.remoteStream && this.remoteVideoElement) {\n      this.remoteVideoElement.srcObject = this.remoteStream;\n      this.remoteVideoElement.muted = false;\n      this.remoteVideoElement.volume = 1;\n      this.remoteVideoElement.play();\n    }\n\n    // Attacher à tous les éléments vidéo disponibles\n    const videos = document.querySelectorAll('video');\n    for (let i = 1; i < videos.length; i++) {\n      if (this.remoteStream && !videos[i].srcObject) {\n        videos[i].srcObject = this.remoteStream;\n        videos[i].muted = false;\n        videos[i].volume = 1;\n        videos[i].play();\n        break;\n      }\n    }\n  }\n\n  /**\n   * Initie un appel - VERSION TEMPORAIRE SANS BACKEND\n   */\n  initiateCall(\n    recipientId: string,\n    callType: CallType,\n    conversationId?: string\n  ): Observable<Call> {\n    console.log('🔄 [CallService] Initiating call (temporary version):', {\n      recipientId,\n      callType,\n      conversationId,\n    });\n\n    // Créer un appel factice pour les tests\n    const fakeCall: Call = {\n      id: 'call_' + Date.now(),\n      caller: {\n        _id: 'current-user',\n        id: 'current-user',\n        username: 'You',\n        email: '<EMAIL>',\n        role: 'user',\n        isActive: true,\n        image: '/assets/images/default-avatar.png',\n      },\n      recipient: {\n        _id: recipientId,\n        id: recipientId,\n        username: 'Other User',\n        email: '<EMAIL>',\n        role: 'user',\n        isActive: true,\n        image: '/assets/images/default-avatar.png',\n      },\n      type: callType,\n      status: CallStatus.RINGING,\n      startTime: new Date().toISOString(),\n      conversationId: conversationId || '',\n    };\n\n    // Simuler un délai réseau\n    return new Observable<Call>((observer) => {\n      setTimeout(() => {\n        console.log('✅ [CallService] Fake call created:', fakeCall);\n\n        this.activeCall.next(fakeCall);\n        this.currentCallId = fakeCall.id;\n\n        // Jouer la sonnerie\n        this.play('ringtone', true);\n\n        // Démarrer les médias pour l'appel sortant\n        this.startOutgoingCallMedia(callType);\n\n        // Simuler un appel entrant après 3 secondes pour tester l'acceptation\n        setTimeout(() => {\n          this.simulateIncomingCall(fakeCall);\n        }, 3000);\n\n        observer.next(fakeCall);\n        observer.complete();\n      }, 500);\n    });\n\n    // VERSION AVEC BACKEND (à réactiver quand le backend sera prêt)\n    /*\n    const callId = 'call_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);\n\n    return this.apollo\n      .mutate<{ initiateCall: Call }>({\n        mutation: INITIATE_CALL_MUTATION,\n        variables: {\n          recipientId,\n          callType,\n          callId,\n          offer: '',\n          conversationId,\n        },\n      })\n      .pipe(\n        map((result) => {\n          if (!result.data?.initiateCall) {\n            throw new Error('No call data received from server');\n          }\n          const call = result.data.initiateCall;\n          this.activeCall.next(call);\n          this.currentCallId = call.id;\n          return call;\n        }),\n        catchError((error) => {\n          console.error('Error initiating call:', error);\n          return throwError(() => new Error(\"Erreur lors de l'initiation de l'appel: \" + error.message));\n        })\n      );\n    */\n  }\n\n  /**\n   * Accepte un appel - VERSION TEMPORAIRE\n   */\n  acceptCall(incomingCall: IncomingCall): Observable<Call> {\n    console.log(\n      '🔄 [CallService] Accepting call (temporary version):',\n      incomingCall\n    );\n\n    // Créer un appel accepté factice\n    const acceptedCall: Call = {\n      id: incomingCall.id,\n      caller: incomingCall.caller,\n      recipient: {\n        _id: 'current-user',\n        id: 'current-user',\n        username: 'You',\n        email: '<EMAIL>',\n        role: 'user',\n        isActive: true,\n        image: '/assets/images/default-avatar.png',\n      },\n      type: incomingCall.type,\n      status: CallStatus.CONNECTED,\n      startTime: new Date().toISOString(),\n      conversationId: incomingCall.conversationId || '',\n    };\n\n    return new Observable<Call>((observer) => {\n      setTimeout(() => {\n        console.log('✅ [CallService] Call accepted (fake):', acceptedCall);\n\n        this.stop('ringtone');\n        this.play('call-connected');\n\n        // Démarrer les médias pour l'appel\n        const callWithMedia = this.startMediaForCall(\n          incomingCall,\n          acceptedCall\n        );\n\n        this.activeCall.next(callWithMedia);\n        this.incomingCall.next(null);\n\n        observer.next(callWithMedia);\n        observer.complete();\n      }, 300);\n    });\n  }\n\n  /**\n   * Rejette un appel - VERSION TEMPORAIRE\n   */\n  rejectCall(callId: string, reason?: string): Observable<CallSuccess> {\n    console.log('🔄 [CallService] Rejecting call (temporary version):', {\n      callId,\n      reason,\n    });\n\n    const fakeSuccess: CallSuccess = {\n      success: true,\n      message: 'Call rejected successfully',\n    };\n\n    return new Observable<CallSuccess>((observer) => {\n      setTimeout(() => {\n        console.log('✅ [CallService] Call rejected (fake):', fakeSuccess);\n\n        this.stop('ringtone');\n        this.incomingCall.next(null);\n        this.activeCall.next(null);\n        this.play('call-end');\n\n        observer.next(fakeSuccess);\n        observer.complete();\n      }, 200);\n    });\n  }\n\n  /**\n   * Termine un appel - VERSION TEMPORAIRE\n   */\n  endCall(callId: string): Observable<CallSuccess> {\n    console.log('🔄 [CallService] Ending call (temporary version):', callId);\n\n    const fakeSuccess: CallSuccess = {\n      success: true,\n      message: 'Call ended successfully',\n    };\n\n    return new Observable<CallSuccess>((observer) => {\n      setTimeout(() => {\n        console.log('✅ [CallService] Call ended (fake):', fakeSuccess);\n\n        this.play('call-end');\n        this.activeCall.next(null);\n        this.incomingCall.next(null);\n        this.cleanupWebRTC();\n\n        observer.next(fakeSuccess);\n        observer.complete();\n      }, 200);\n    });\n  }\n\n  /**\n   * Configure les éléments vidéo\n   */\n  setVideoElements(\n    localVideo: HTMLVideoElement,\n    remoteVideo: HTMLVideoElement\n  ): void {\n    this.localVideoElement = localVideo;\n    this.remoteVideoElement = remoteVideo;\n  }\n\n  /**\n   * Envoie un signal\n   */\n  private sendSignal(type: string, data: string): void {\n    // Simulation pour les tests\n    setTimeout(() => {\n      this.handleCallSignal({\n        callId: this.currentCallId,\n        senderId: 'other-user',\n        type,\n        data,\n      });\n    }, 100);\n  }\n\n  /**\n   * Obtient les médias utilisateur (caméra/micro)\n   */\n  private async getUserMedia(callType: CallType): Promise<MediaStream> {\n    console.log('🎥 [CallService] Getting user media for:', callType);\n\n    const constraints: MediaStreamConstraints = {\n      audio: true,\n      video: callType === CallType.VIDEO,\n    };\n\n    try {\n      const stream = await navigator.mediaDevices.getUserMedia(constraints);\n      console.log('✅ [CallService] User media obtained');\n\n      this.localStream = stream;\n      return stream;\n    } catch (error) {\n      console.error('❌ [CallService] Error getting user media:', error);\n      throw new Error(\"Impossible d'accéder au microphone/caméra\");\n    }\n  }\n\n  /**\n   * Crée une offre WebRTC\n   */\n  private async createOffer(): Promise<RTCSessionDescriptionInit> {\n    if (!this.peerConnection) {\n      throw new Error('PeerConnection not initialized');\n    }\n\n    try {\n      const offer = await this.peerConnection.createOffer({\n        offerToReceiveAudio: true,\n        offerToReceiveVideo: true,\n      });\n\n      await this.peerConnection.setLocalDescription(offer);\n      console.log(\n        '✅ [CallService] Offer created and set as local description'\n      );\n\n      return offer;\n    } catch (error) {\n      console.error('❌ [CallService] Error creating offer:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Crée une réponse WebRTC\n   */\n  private async createAnswer(\n    offer: RTCSessionDescriptionInit\n  ): Promise<RTCSessionDescriptionInit> {\n    if (!this.peerConnection) {\n      throw new Error('PeerConnection not initialized');\n    }\n\n    try {\n      await this.peerConnection.setRemoteDescription(offer);\n      console.log('✅ [CallService] Remote description set');\n\n      const answer = await this.peerConnection.createAnswer();\n      await this.peerConnection.setLocalDescription(answer);\n      console.log(\n        '✅ [CallService] Answer created and set as local description'\n      );\n\n      return answer;\n    } catch (error) {\n      console.error('❌ [CallService] Error creating answer:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Gère une offre distante\n   */\n  private async handleRemoteOffer(signal: any): Promise<void> {\n    try {\n      const offer = JSON.parse(signal.data);\n      console.log('📥 [CallService] Handling remote offer:', offer);\n\n      // Obtenir les médias utilisateur\n      const stream = await this.getUserMedia(CallType.AUDIO);\n      this.addLocalStreamToPeerConnection(stream);\n\n      // Créer et envoyer la réponse\n      const answer = await this.createAnswer(offer);\n      this.sendSignal('answer', JSON.stringify(answer));\n\n      console.log('✅ [CallService] Remote offer handled successfully');\n    } catch (error) {\n      console.error('❌ [CallService] Error handling remote offer:', error);\n    }\n  }\n\n  /**\n   * Gère une réponse distante\n   */\n  private async handleRemoteAnswer(signal: any): Promise<void> {\n    try {\n      const answer = JSON.parse(signal.data);\n      console.log('📥 [CallService] Handling remote answer:', answer);\n\n      if (this.peerConnection) {\n        await this.peerConnection.setRemoteDescription(answer);\n        console.log('✅ [CallService] Remote answer set successfully');\n      }\n    } catch (error) {\n      console.error('❌ [CallService] Error handling remote answer:', error);\n    }\n  }\n\n  /**\n   * Gère un candidat ICE distant\n   */\n  private async handleRemoteICECandidate(signal: any): Promise<void> {\n    try {\n      const candidate = JSON.parse(signal.data);\n      console.log('📥 [CallService] Handling remote ICE candidate:', candidate);\n\n      if (this.peerConnection && candidate) {\n        await this.peerConnection.addIceCandidate(\n          new RTCIceCandidate(candidate)\n        );\n        console.log('✅ [CallService] Remote ICE candidate added successfully');\n      }\n    } catch (error) {\n      console.error('❌ [CallService] Error handling ICE candidate:', error);\n    }\n  }\n\n  /**\n   * Ajoute le stream local à la PeerConnection\n   */\n  private addLocalStreamToPeerConnection(stream: MediaStream): void {\n    if (!this.peerConnection) return;\n\n    stream.getTracks().forEach((track) => {\n      this.peerConnection!.addTrack(track, stream);\n      console.log(\n        '✅ [CallService] Track added to peer connection:',\n        track.kind\n      );\n    });\n  }\n\n  /**\n   * Démarre les médias pour un appel accepté\n   */\n  private startMediaForCall(incomingCall: IncomingCall, call: Call): Call {\n    console.log('🎥 [CallService] Starting media for accepted call');\n\n    // Démarrer les médias en arrière-plan\n    this.getUserMedia(incomingCall.type)\n      .then((stream) => {\n        this.addLocalStreamToPeerConnection(stream);\n        this.attachLocalStream();\n        console.log('✅ [CallService] Media started for call');\n      })\n      .catch((error) => {\n        console.error('❌ [CallService] Error starting media:', error);\n      });\n\n    return call;\n  }\n\n  /**\n   * Attache le stream local aux éléments vidéo\n   */\n  private attachLocalStream(): void {\n    if (this.localStream && this.localVideoElement) {\n      this.localVideoElement.srcObject = this.localStream;\n      this.localVideoElement.muted = true; // Éviter l'écho\n      this.localVideoElement.play();\n      console.log('✅ [CallService] Local stream attached');\n    }\n\n    // Attacher aussi au premier élément vidéo disponible\n    const videos = document.querySelectorAll('video');\n    if (this.localStream && videos.length > 0 && !videos[0].srcObject) {\n      videos[0].srcObject = this.localStream;\n      videos[0].muted = true;\n      videos[0].play();\n    }\n  }\n\n  /**\n   * Démarre les médias pour un appel sortant\n   */\n  private startOutgoingCallMedia(callType: CallType): void {\n    console.log('🎥 [CallService] Starting outgoing call media');\n\n    this.getUserMedia(callType)\n      .then((stream) => {\n        this.addLocalStreamToPeerConnection(stream);\n        this.attachLocalStream();\n\n        // Créer une offre WebRTC\n        return this.createOffer();\n      })\n      .then((offer) => {\n        console.log('✅ [CallService] Offer created for outgoing call');\n        // Dans un vrai scénario, on enverrait cette offre au destinataire\n      })\n      .catch((error) => {\n        console.error(\n          '❌ [CallService] Error starting outgoing call media:',\n          error\n        );\n      });\n  }\n\n  /**\n   * Nettoie les ressources WebRTC\n   */\n  private cleanupWebRTC(): void {\n    if (this.localStream) {\n      this.localStream.getTracks().forEach((track) => track.stop());\n      this.localStream = null;\n    }\n\n    if (this.peerConnection) {\n      this.peerConnection.close();\n      this.peerConnection = null;\n    }\n\n    this.remoteStream = null;\n  }\n\n  /**\n   * Active les sons après interaction utilisateur\n   */\n  enableSounds(): void {\n    console.log('Sounds enabled after user interaction');\n  }\n\n  /**\n   * Bascule l'audio\n   */\n  toggleAudio(): boolean {\n    this.isAudioEnabled = !this.isAudioEnabled;\n    return this.isAudioEnabled;\n  }\n\n  /**\n   * Bascule la vidéo\n   */\n  toggleVideo(): boolean {\n    this.isVideoEnabled = !this.isVideoEnabled;\n    return this.isVideoEnabled;\n  }\n\n  /**\n   * Bascule les médias\n   */\n  toggleMedia(\n    callId: string,\n    enableVideo?: boolean,\n    enableAudio?: boolean\n  ): Observable<CallSuccess> {\n    return this.apollo\n      .mutate<{ toggleCallMedia: CallSuccess }>({\n        mutation: TOGGLE_CALL_MEDIA_MUTATION,\n        variables: { callId, video: enableVideo, audio: enableAudio },\n      })\n      .pipe(\n        map((result) => {\n          return result.data!.toggleCallMedia;\n        }),\n        catchError((error) => {\n          console.error('Error toggling media:', error);\n          return throwError(\n            () => new Error('Erreur lors du changement des médias')\n          );\n        })\n      );\n  }\n\n  /**\n   * Simule un appel entrant (pour les tests)\n   */\n  private simulateIncomingCall(originalCall: Call): void {\n    const incomingCall: IncomingCall = {\n      id: originalCall.id,\n      caller: originalCall.recipient, // Inverser caller/recipient\n      type: originalCall.type,\n      conversationId: originalCall.conversationId,\n      offer: '{\"type\":\"offer\",\"sdp\":\"fake-offer\"}',\n      timestamp: new Date().toISOString(),\n    };\n\n    console.log('📞 [CallService] Simulating incoming call:', incomingCall);\n\n    this.stop('ringtone'); // Arrêter la sonnerie sortante\n    this.incomingCall.next(incomingCall);\n    this.activeCall.next(null); // Nettoyer l'appel sortant\n\n    // Jouer la sonnerie d'appel entrant\n    this.play('ringtone', true);\n  }\n\n  ngOnDestroy(): void {\n    this.stopAllSounds();\n    this.cleanupWebRTC();\n  }\n}\n"], "mappings": ";AAEA,SAASA,eAAe,EAAEC,UAAU,EAAEC,UAAU,QAAc,MAAM;AACpE,SAASC,GAAG,EAAEC,UAAU,QAAmB,gBAAgB;AAC3D,SAEEC,QAAQ,EACRC,UAAU,QAGL,yBAAyB;AAChC,SAKEC,0BAA0B,EAC1BC,0BAA0B,EAC1BC,gCAAgC,EAChCC,wBAAwB,QACnB,4BAA4B;;;;AAMnC,OAAM,MAAOC,WAAW;EA0BtBC,YAAoBC,MAAc,EAAUC,MAAqB;IAA7C,KAAAD,MAAM,GAANA,MAAM;IAAkB,KAAAC,MAAM,GAANA,MAAM;IAzBlD;IACQ,KAAAC,UAAU,GAAG,IAAIf,eAAe,CAAc,IAAI,CAAC;IACnD,KAAAgB,YAAY,GAAG,IAAIhB,eAAe,CAAsB,IAAI,CAAC;IAErE;IACO,KAAAiB,WAAW,GAAG,IAAI,CAACF,UAAU,CAACG,YAAY,EAAE;IAC5C,KAAAC,aAAa,GAAG,IAAI,CAACH,YAAY,CAACE,YAAY,EAAE;IAEvD;IACQ,KAAAE,MAAM,GAAwC,EAAE;IAChD,KAAAC,SAAS,GAA+B,EAAE;IAElD;IACQ,KAAAC,cAAc,GAAG,IAAI;IACrB,KAAAC,cAAc,GAAG,IAAI;IAE7B;IACQ,KAAAC,cAAc,GAA6B,IAAI;IAC/C,KAAAC,WAAW,GAAuB,IAAI;IACtC,KAAAC,YAAY,GAAuB,IAAI;IACvC,KAAAC,iBAAiB,GAA4B,IAAI;IACjD,KAAAC,kBAAkB,GAA4B,IAAI;IAClD,KAAAC,aAAa,GAAkB,IAAI;IACnC,KAAAC,qBAAqB,GAAY,KAAK;IAG5C,IAAI,CAACC,gBAAgB,EAAE;IACvB,IAAI,CAACC,uBAAuB,EAAE;IAC9B,IAAI,CAACC,gBAAgB,EAAE;EACzB;EAEA;;;EAGQF,gBAAgBA,CAAA;IACtB,IAAI,CAACG,qBAAqB,EAAE;EAC9B;EAEA;;;EAGQA,qBAAqBA,CAAA;IAC3B,IAAI;MACF,MAAMC,YAAY,GAAG,KAAKC,MAAM,CAACC,YAAY,IAC1CD,MAAc,CAACE,kBAAkB,EAAC,CAAE;MACvC,IAAI,CAAClB,MAAM,CAAC,UAAU,CAAC,GAAG,IAAI,CAACmB,mBAAmB,CAACJ,YAAY,CAAC;MAChE,IAAI,CAACf,MAAM,CAAC,gBAAgB,CAAC,GAAG,IAAI,CAACoB,oBAAoB,CAACL,YAAY,CAAC;MACvE,IAAI,CAACf,MAAM,CAAC,UAAU,CAAC,GAAG,IAAI,CAACqB,cAAc,CAACN,YAAY,CAAC;MAC3D,IAAI,CAACf,MAAM,CAAC,cAAc,CAAC,GAAG,IAAI,CAACsB,uBAAuB,CAACP,YAAY,CAAC;KACzE,CAAC,OAAOQ,KAAK,EAAE;MACdC,OAAO,CAACC,IAAI,CAAC,oCAAoC,EAAEF,KAAK,CAAC;;EAE7D;EAEA;;;EAGQJ,mBAAmBA,CAACJ,YAA0B;IACpD,MAAMW,KAAK,GAAG,IAAIC,KAAK,EAAE;IACzBD,KAAK,CAACE,MAAM,GAAG,GAAG;IAClB,IAAI3B,SAAS,GAAG,KAAK;IACrB,IAAI4B,UAAU,GAAU,EAAE;IAEzBH,KAAa,CAACI,aAAa,GAAG,MAAK;MAClC,IAAI7B,SAAS,EAAE,OAAO8B,OAAO,CAACC,OAAO,EAAE;MACvC/B,SAAS,GAAG,IAAI;MAEhB,MAAMgC,UAAU,GAAGA,CAAA,KAAK;QACtB,IAAI,CAAChC,SAAS,EAAE;QAChB,MAAMiC,MAAM,GAAG,CACb;UAAEC,IAAI,EAAE,MAAM;UAAEC,QAAQ,EAAE;QAAK,CAAE,EACjC;UAAED,IAAI,EAAE,MAAM;UAAEC,QAAQ,EAAE;QAAK,CAAE,EACjC;UAAED,IAAI,EAAE,MAAM;UAAEC,QAAQ,EAAE;QAAI,CAAE,EAChC;UAAED,IAAI,EAAE,MAAM;UAAEC,QAAQ,EAAE;QAAI,CAAE,CACjC;QAED,IAAIC,WAAW,GAAGtB,YAAY,CAACsB,WAAW;QAC1CH,MAAM,CAACI,OAAO,CAAEC,IAAI,IAAI;UACtB,IAAI,CAACtC,SAAS,EAAE;UAChB,MAAMuC,UAAU,GAAGzB,YAAY,CAAC0B,gBAAgB,EAAE;UAClD,MAAMC,QAAQ,GAAG3B,YAAY,CAAC4B,UAAU,EAAE;UAC1CH,UAAU,CAACI,OAAO,CAACF,QAAQ,CAAC;UAC5BA,QAAQ,CAACE,OAAO,CAAC7B,YAAY,CAAC8B,WAAW,CAAC;UAC1CL,UAAU,CAACM,SAAS,CAACC,KAAK,GAAGR,IAAI,CAACJ,IAAI;UACtCK,UAAU,CAACQ,IAAI,GAAG,QAAQ;UAC1BN,QAAQ,CAACO,IAAI,CAACC,cAAc,CAAC,CAAC,EAAEb,WAAW,CAAC;UAC5CK,QAAQ,CAACO,IAAI,CAACE,uBAAuB,CAAC,GAAG,EAAEd,WAAW,GAAG,IAAI,CAAC;UAC9DK,QAAQ,CAACO,IAAI,CAACG,4BAA4B,CACxC,IAAI,EACJf,WAAW,GAAGE,IAAI,CAACH,QAAQ,CAC5B;UACDI,UAAU,CAACa,KAAK,CAAChB,WAAW,CAAC;UAC7BG,UAAU,CAACc,IAAI,CAACjB,WAAW,GAAGE,IAAI,CAACH,QAAQ,CAAC;UAC5CC,WAAW,IAAIE,IAAI,CAACH,QAAQ,GAAG,IAAI;QACrC,CAAC,CAAC;QAEF,MAAMmB,SAAS,GAAGC,UAAU,CAAC,MAAK;UAChC,IAAIvD,SAAS,EAAEgC,UAAU,EAAE;QAC7B,CAAC,EAAE,CAACI,WAAW,GAAGtB,YAAY,CAACsB,WAAW,GAAG,GAAG,IAAI,IAAI,CAAC;QACzDR,UAAU,CAAC4B,IAAI,CAACF,SAAS,CAAC;MAC5B,CAAC;MAEDtB,UAAU,EAAE;MACZ,OAAOF,OAAO,CAACC,OAAO,EAAE;IAC1B,CAAC;IAEAN,KAAa,CAACgC,aAAa,GAAG,MAAK;MAClCzD,SAAS,GAAG,KAAK;MACjB4B,UAAU,CAACS,OAAO,CAAEqB,EAAE,IAAKC,YAAY,CAACD,EAAE,CAAC,CAAC;MAC5C9B,UAAU,GAAG,EAAE;IACjB,CAAC;IAED,OAAOH,KAAK;EACd;EAEA;;;EAGQN,oBAAoBA,CAACL,YAA0B;IACrD,MAAMW,KAAK,GAAG,IAAIC,KAAK,EAAE;IACzBD,KAAK,CAACE,MAAM,GAAG,GAAG;IAEjBF,KAAa,CAACI,aAAa,GAAG,MAAK;MAClC,MAAMI,MAAM,GAAG,CACb;QAAEC,IAAI,EAAE,MAAM;QAAEC,QAAQ,EAAE;MAAI,CAAE,EAChC;QAAED,IAAI,EAAE,MAAM;QAAEC,QAAQ,EAAE;MAAI,CAAE,EAChC;QAAED,IAAI,EAAE,MAAM;QAAEC,QAAQ,EAAE;MAAI,CAAE,EAChC;QAAED,IAAI,EAAE,MAAM;QAAEC,QAAQ,EAAE;MAAG,CAAE,CAChC;MAED,IAAIC,WAAW,GAAGtB,YAAY,CAACsB,WAAW;MAC1CH,MAAM,CAACI,OAAO,CAAEC,IAAI,IAAI;QACtB,MAAMC,UAAU,GAAGzB,YAAY,CAAC0B,gBAAgB,EAAE;QAClD,MAAMC,QAAQ,GAAG3B,YAAY,CAAC4B,UAAU,EAAE;QAC1CH,UAAU,CAACI,OAAO,CAACF,QAAQ,CAAC;QAC5BA,QAAQ,CAACE,OAAO,CAAC7B,YAAY,CAAC8B,WAAW,CAAC;QAC1CL,UAAU,CAACM,SAAS,CAACC,KAAK,GAAGR,IAAI,CAACJ,IAAI;QACtCK,UAAU,CAACQ,IAAI,GAAG,UAAU;QAC5BN,QAAQ,CAACO,IAAI,CAACC,cAAc,CAAC,CAAC,EAAEb,WAAW,CAAC;QAC5CK,QAAQ,CAACO,IAAI,CAACE,uBAAuB,CAAC,IAAI,EAAEd,WAAW,GAAG,IAAI,CAAC;QAC/DK,QAAQ,CAACO,IAAI,CAACG,4BAA4B,CACxC,IAAI,EACJf,WAAW,GAAGE,IAAI,CAACH,QAAQ,CAC5B;QACDI,UAAU,CAACa,KAAK,CAAChB,WAAW,CAAC;QAC7BG,UAAU,CAACc,IAAI,CAACjB,WAAW,GAAGE,IAAI,CAACH,QAAQ,CAAC;QAC5CC,WAAW,IAAIE,IAAI,CAACH,QAAQ,GAAG,GAAG;MACpC,CAAC,CAAC;MAEF,OAAOL,OAAO,CAACC,OAAO,EAAE;IAC1B,CAAC;IAED,OAAON,KAAK;EACd;EAEA;;;EAGQL,cAAcA,CAACN,YAA0B;IAC/C,MAAMW,KAAK,GAAG,IAAIC,KAAK,EAAE;IACzBD,KAAK,CAACE,MAAM,GAAG,GAAG;IAEjBF,KAAa,CAACI,aAAa,GAAG,MAAK;MAClC,MAAMI,MAAM,GAAG,CACb;QAAEC,IAAI,EAAE,MAAM;QAAEC,QAAQ,EAAE;MAAG,CAAE,EAC/B;QAAED,IAAI,EAAE,MAAM;QAAEC,QAAQ,EAAE;MAAG,CAAE,EAC/B;QAAED,IAAI,EAAE,MAAM;QAAEC,QAAQ,EAAE;MAAG,CAAE,EAC/B;QAAED,IAAI,EAAE,KAAK;QAAEC,QAAQ,EAAE;MAAG,CAAE,CAC/B;MAED,IAAIC,WAAW,GAAGtB,YAAY,CAACsB,WAAW;MAC1CH,MAAM,CAACI,OAAO,CAAEC,IAAI,IAAI;QACtB,MAAMC,UAAU,GAAGzB,YAAY,CAAC0B,gBAAgB,EAAE;QAClD,MAAMC,QAAQ,GAAG3B,YAAY,CAAC4B,UAAU,EAAE;QAC1CH,UAAU,CAACI,OAAO,CAACF,QAAQ,CAAC;QAC5BA,QAAQ,CAACE,OAAO,CAAC7B,YAAY,CAAC8B,WAAW,CAAC;QAC1CL,UAAU,CAACM,SAAS,CAACC,KAAK,GAAGR,IAAI,CAACJ,IAAI;QACtCK,UAAU,CAACQ,IAAI,GAAG,MAAM;QACxBN,QAAQ,CAACO,IAAI,CAACC,cAAc,CAAC,CAAC,EAAEb,WAAW,CAAC;QAC5CK,QAAQ,CAACO,IAAI,CAACE,uBAAuB,CAAC,GAAG,EAAEd,WAAW,GAAG,IAAI,CAAC;QAC9DK,QAAQ,CAACO,IAAI,CAACG,4BAA4B,CACxC,IAAI,EACJf,WAAW,GAAGE,IAAI,CAACH,QAAQ,CAC5B;QACDI,UAAU,CAACa,KAAK,CAAChB,WAAW,CAAC;QAC7BG,UAAU,CAACc,IAAI,CAACjB,WAAW,GAAGE,IAAI,CAACH,QAAQ,CAAC;QAC5CC,WAAW,IAAIE,IAAI,CAACH,QAAQ,GAAG,GAAG;MACpC,CAAC,CAAC;MAEF,OAAOL,OAAO,CAACC,OAAO,EAAE;IAC1B,CAAC;IAED,OAAON,KAAK;EACd;EAEA;;;EAGQJ,uBAAuBA,CAC7BP,YAA0B;IAE1B,MAAMW,KAAK,GAAG,IAAIC,KAAK,EAAE;IACzBD,KAAK,CAACE,MAAM,GAAG,GAAG;IAEjBF,KAAa,CAACI,aAAa,GAAG,MAAK;MAClC,MAAM+B,KAAK,GAAG,CACZ;QAAE1B,IAAI,EAAE,MAAM;QAAEC,QAAQ,EAAE,IAAI;QAAE0B,KAAK,EAAE;MAAC,CAAE,EAC1C;QAAE3B,IAAI,EAAE,MAAM;QAAEC,QAAQ,EAAE,IAAI;QAAE0B,KAAK,EAAE;MAAG,CAAE,CAC7C;MAEDD,KAAK,CAACvB,OAAO,CAAEC,IAAI,IAAI;QACrBiB,UAAU,CAAC,MAAK;UACd,MAAMhB,UAAU,GAAGzB,YAAY,CAAC0B,gBAAgB,EAAE;UAClD,MAAMC,QAAQ,GAAG3B,YAAY,CAAC4B,UAAU,EAAE;UAC1CH,UAAU,CAACI,OAAO,CAACF,QAAQ,CAAC;UAC5BA,QAAQ,CAACE,OAAO,CAAC7B,YAAY,CAAC8B,WAAW,CAAC;UAC1CL,UAAU,CAACM,SAAS,CAACC,KAAK,GAAGR,IAAI,CAACJ,IAAI;UACtCK,UAAU,CAACQ,IAAI,GAAG,UAAU;UAC5B,MAAMe,SAAS,GAAGhD,YAAY,CAACsB,WAAW;UAC1CK,QAAQ,CAACO,IAAI,CAACC,cAAc,CAAC,CAAC,EAAEa,SAAS,CAAC;UAC1CrB,QAAQ,CAACO,IAAI,CAACE,uBAAuB,CAAC,GAAG,EAAEY,SAAS,GAAG,IAAI,CAAC;UAC5DrB,QAAQ,CAACO,IAAI,CAACG,4BAA4B,CACxC,IAAI,EACJW,SAAS,GAAGxB,IAAI,CAACH,QAAQ,CAC1B;UACDI,UAAU,CAACa,KAAK,CAACU,SAAS,CAAC;UAC3BvB,UAAU,CAACc,IAAI,CAACS,SAAS,GAAGxB,IAAI,CAACH,QAAQ,CAAC;QAC5C,CAAC,EAAEG,IAAI,CAACuB,KAAK,GAAG,IAAI,CAAC;MACvB,CAAC,CAAC;MAEF,OAAO/B,OAAO,CAACC,OAAO,EAAE;IAC1B,CAAC;IAED,OAAON,KAAK;EACd;EAEA;;;EAGQsC,IAAIA,CAACC,SAAiB,EAAEC,IAAA,GAAgB,KAAK;IACnD,MAAMC,KAAK,GAAG,IAAI,CAACnE,MAAM,CAACiE,SAAS,CAAC;IACpC,IAAI,CAACE,KAAK,EAAE;IAEZ,IAAI,CAAClE,SAAS,CAACgE,SAAS,CAAC,GAAG,IAAI;IAEhC,IAAKE,KAAa,CAACrC,aAAa,EAAE;MAC/BqC,KAAa,CAACrC,aAAa,EAAE;MAC9B,IAAIoC,IAAI,EAAE;QACR,MAAME,QAAQ,GAAGC,WAAW,CAAC,MAAK;UAChC,IAAI,IAAI,CAACpE,SAAS,CAACgE,SAAS,CAAC,EAAE;YAC5BE,KAAa,CAACrC,aAAa,EAAE;WAC/B,MAAM;YACLwC,aAAa,CAACF,QAAQ,CAAC;;QAE3B,CAAC,EAAE,IAAI,CAAC;;;EAGd;EAEA;;;EAGQd,IAAIA,CAACW,SAAiB;IAC5B,IAAI,CAAChE,SAAS,CAACgE,SAAS,CAAC,GAAG,KAAK;IACjC,MAAME,KAAK,GAAG,IAAI,CAACnE,MAAM,CAACiE,SAAS,CAAC;IACpC,IAAIE,KAAK,IAAKA,KAAa,CAACT,aAAa,EAAE;MACxCS,KAAa,CAACT,aAAa,EAAE;;EAElC;EAEA;;;EAGQa,aAAaA,CAAA;IACnBC,MAAM,CAACC,IAAI,CAAC,IAAI,CAACzE,MAAM,CAAC,CAACsC,OAAO,CAAEoC,IAAI,IAAI;MACxC,IAAI,CAACpB,IAAI,CAACoB,IAAI,CAAC;IACjB,CAAC,CAAC;EACJ;EAEA;;;EAGQ9D,uBAAuBA,CAAA;IAC7B4C,UAAU,CAAC,MAAK;MACd,IAAI,CAACmB,wBAAwB,EAAE;MAC/B,IAAI,CAACC,4BAA4B,EAAE;MACnC,IAAI,CAACC,sBAAsB,EAAE;IAC/B,CAAC,EAAE,IAAI,CAAC;EACV;EAEA;;;EAGQF,wBAAwBA,CAAA;IAC9B,IAAI,CAAClF,MAAM,CACRqF,SAAS,CAAiC;MACzCC,KAAK,EAAE3F,0BAA0B;MACjC4F,WAAW,EAAE;KACd,CAAC,CACDF,SAAS,CAAC;MACTG,IAAI,EAAEA,CAAC;QAAEC,IAAI;QAAEC;MAAM,CAAE,KAAI;QACzB,IAAID,IAAI,EAAEtF,YAAY,EAAE;UACtB,IAAI,CAACwF,kBAAkB,CAACF,IAAI,CAACtF,YAAY,CAAC;;MAE9C,CAAC;MACD2B,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;QAC5DiC,UAAU,CAAC,MAAM,IAAI,CAACmB,wBAAwB,EAAE,EAAE,IAAI,CAAC;MACzD;KACD,CAAC;EACN;EAEA;;;EAGQC,4BAA4BA,CAAA;IAClC,IAAI,CAACnF,MAAM,CACRqF,SAAS,CAA8B;MACtCC,KAAK,EAAE1F,gCAAgC;MACvC2F,WAAW,EAAE;KACd,CAAC,CACDF,SAAS,CAAC;MACTG,IAAI,EAAEA,CAAC;QAAEC,IAAI;QAAEC;MAAM,CAAE,KAAI;QACzB,IAAID,IAAI,EAAEG,iBAAiB,EAAE;UAC3B,IAAI,CAACC,sBAAsB,CAACJ,IAAI,CAACG,iBAAiB,CAAC;;MAEvD,CAAC;MACD9D,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;QAC1DiC,UAAU,CAAC,MAAM,IAAI,CAACoB,4BAA4B,EAAE,EAAE,IAAI,CAAC;MAC7D;KACD,CAAC;EACN;EAEA;;;EAGQC,sBAAsBA,CAAA;IAC5B,IAAI,CAACpF,MAAM,CACRqF,SAAS,CAAsB;MAC9BC,KAAK,EAAEzF,wBAAwB;MAC/B0F,WAAW,EAAE;KACd,CAAC,CACDF,SAAS,CAAC;MACTG,IAAI,EAAEA,CAAC;QAAEC,IAAI;QAAEC;MAAM,CAAE,KAAI;QACzB,IAAID,IAAI,EAAEK,UAAU,EAAE;UACpB,IAAI,CAACC,gBAAgB,CAACN,IAAI,CAACK,UAAU,CAAC;;MAE1C,CAAC;MACDhE,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;QAC1DiC,UAAU,CAAC,MAAM,IAAI,CAACqB,sBAAsB,EAAE,EAAE,IAAI,CAAC;MACvD;KACD,CAAC;EACN;EAEA;;;EAGQO,kBAAkBA,CAACK,IAAkB;IAC3C,IAAI,CAAC7F,YAAY,CAACqF,IAAI,CAACQ,IAAI,CAAC;IAC5B,IAAI,CAACzB,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC;EAC7B;EAEA;;;EAGQsB,sBAAsBA,CAACG,IAAU;IACvC,QAAQA,IAAI,CAACC,MAAM;MACjB,KAAKxG,UAAU,CAACyG,SAAS;QACvB,IAAI,CAACrC,IAAI,CAAC,UAAU,CAAC;QACrB,IAAI,CAACU,IAAI,CAAC,gBAAgB,CAAC;QAC3B,IAAI,CAACrE,UAAU,CAACsF,IAAI,CAACQ,IAAI,CAAC;QAC1B,IAAI,CAAC7F,YAAY,CAACqF,IAAI,CAAC,IAAI,CAAC;QAC5B;MACF,KAAK/F,UAAU,CAAC0G,KAAK;QACnB,IAAI,CAAC5B,IAAI,CAAC,UAAU,CAAC;QACrB,IAAI,CAACrE,UAAU,CAACsF,IAAI,CAAC,IAAI,CAAC;QAC1B,IAAI,CAACrF,YAAY,CAACqF,IAAI,CAAC,IAAI,CAAC;QAC5B,IAAI,CAACY,aAAa,EAAE;QACpB;MACF,KAAK3G,UAAU,CAAC4G,QAAQ;QACtB,IAAI,CAACxC,IAAI,CAAC,UAAU,CAAC;QACrB,IAAI,CAACU,IAAI,CAAC,UAAU,CAAC;QACrB,IAAI,CAACrE,UAAU,CAACsF,IAAI,CAAC,IAAI,CAAC;QAC1B,IAAI,CAACrF,YAAY,CAACqF,IAAI,CAAC,IAAI,CAAC;QAC5B;;EAEN;EAEA;;;EAGQO,gBAAgBA,CAACO,MAAW;IAClC,IAAI,CAAC,IAAI,CAAC3F,cAAc,EAAE;IAE1B,QAAQ2F,MAAM,CAAC/C,IAAI;MACjB,KAAK,OAAO;QACV,IAAI,CAACgD,iBAAiB,CAACD,MAAM,CAAC;QAC9B;MACF,KAAK,QAAQ;QACX,IAAI,CAACE,kBAAkB,CAACF,MAAM,CAAC;QAC/B;MACF,KAAK,eAAe;QAClB,IAAI,CAACG,wBAAwB,CAACH,MAAM,CAAC;QACrC;;EAEN;EAEA;;;EAGQlF,gBAAgBA,CAAA;IACtB,MAAMsF,aAAa,GAAqB;MACtCC,UAAU,EAAE,CACV;QAAEC,IAAI,EAAE;MAA8B,CAAE,EACxC;QAAEA,IAAI,EAAE;MAA+B,CAAE;KAE5C;IAED,IAAI;MACF,IAAI,CAACjG,cAAc,GAAG,IAAIkG,iBAAiB,CAACH,aAAa,CAAC;MAC1D,IAAI,CAACI,yBAAyB,EAAE;KACjC,CAAC,OAAOhF,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;;EAExD;EAEA;;;EAGQgF,yBAAyBA,CAAA;IAC/B,IAAI,CAAC,IAAI,CAACnG,cAAc,EAAE;IAE1B,IAAI,CAACA,cAAc,CAACoG,OAAO,GAAIC,KAAK,IAAI;MACtC,IAAI,CAACnG,YAAY,GAAGmG,KAAK,CAACC,OAAO,CAAC,CAAC,CAAC;MACpC,IAAI,CAACC,kBAAkB,EAAE;IAC3B,CAAC;IAED,IAAI,CAACvG,cAAc,CAACwG,cAAc,GAAIH,KAAK,IAAI;MAC7C,IAAIA,KAAK,CAACI,SAAS,EAAE;QACnB,IAAI,CAACC,UAAU,CAAC,eAAe,EAAEC,IAAI,CAACC,SAAS,CAACP,KAAK,CAACI,SAAS,CAAC,CAAC;;IAErE,CAAC;EACH;EAEA;;;EAGQF,kBAAkBA,CAAA;IACxB,IAAI,IAAI,CAACrG,YAAY,IAAI,IAAI,CAACE,kBAAkB,EAAE;MAChD,IAAI,CAACA,kBAAkB,CAACyG,SAAS,GAAG,IAAI,CAAC3G,YAAY;MACrD,IAAI,CAACE,kBAAkB,CAAC0G,KAAK,GAAG,KAAK;MACrC,IAAI,CAAC1G,kBAAkB,CAACoB,MAAM,GAAG,CAAC;MAClC,IAAI,CAACpB,kBAAkB,CAACwD,IAAI,EAAE;;IAGhC;IACA,MAAMmD,MAAM,GAAGC,QAAQ,CAACC,gBAAgB,CAAC,OAAO,CAAC;IACjD,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,MAAM,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;MACtC,IAAI,IAAI,CAAChH,YAAY,IAAI,CAAC6G,MAAM,CAACG,CAAC,CAAC,CAACL,SAAS,EAAE;QAC7CE,MAAM,CAACG,CAAC,CAAC,CAACL,SAAS,GAAG,IAAI,CAAC3G,YAAY;QACvC6G,MAAM,CAACG,CAAC,CAAC,CAACJ,KAAK,GAAG,KAAK;QACvBC,MAAM,CAACG,CAAC,CAAC,CAAC1F,MAAM,GAAG,CAAC;QACpBuF,MAAM,CAACG,CAAC,CAAC,CAACtD,IAAI,EAAE;QAChB;;;EAGN;EAEA;;;EAGAwD,YAAYA,CACVC,WAAmB,EACnBC,QAAkB,EAClBC,cAAuB;IAEvBnG,OAAO,CAACoG,GAAG,CAAC,uDAAuD,EAAE;MACnEH,WAAW;MACXC,QAAQ;MACRC;KACD,CAAC;IAEF;IACA,MAAME,QAAQ,GAAS;MACrBlE,EAAE,EAAE,OAAO,GAAGmE,IAAI,CAACC,GAAG,EAAE;MACxBC,MAAM,EAAE;QACNC,GAAG,EAAE,cAAc;QACnBtE,EAAE,EAAE,cAAc;QAClBuE,QAAQ,EAAE,KAAK;QACfC,KAAK,EAAE,qBAAqB;QAC5BC,IAAI,EAAE,MAAM;QACZC,QAAQ,EAAE,IAAI;QACdC,KAAK,EAAE;OACR;MACDC,SAAS,EAAE;QACTN,GAAG,EAAER,WAAW;QAChB9D,EAAE,EAAE8D,WAAW;QACfS,QAAQ,EAAE,YAAY;QACtBC,KAAK,EAAE,mBAAmB;QAC1BC,IAAI,EAAE,MAAM;QACZC,QAAQ,EAAE,IAAI;QACdC,KAAK,EAAE;OACR;MACDtF,IAAI,EAAE0E,QAAQ;MACdhC,MAAM,EAAExG,UAAU,CAACsJ,OAAO;MAC1BzE,SAAS,EAAE,IAAI+D,IAAI,EAAE,CAACW,WAAW,EAAE;MACnCd,cAAc,EAAEA,cAAc,IAAI;KACnC;IAED;IACA,OAAO,IAAI9I,UAAU,CAAQ6J,QAAQ,IAAI;MACvClF,UAAU,CAAC,MAAK;QACdhC,OAAO,CAACoG,GAAG,CAAC,oCAAoC,EAAEC,QAAQ,CAAC;QAE3D,IAAI,CAAClI,UAAU,CAACsF,IAAI,CAAC4C,QAAQ,CAAC;QAC9B,IAAI,CAACpH,aAAa,GAAGoH,QAAQ,CAAClE,EAAE;QAEhC;QACA,IAAI,CAACK,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC;QAE3B;QACA,IAAI,CAAC2E,sBAAsB,CAACjB,QAAQ,CAAC;QAErC;QACAlE,UAAU,CAAC,MAAK;UACd,IAAI,CAACoF,oBAAoB,CAACf,QAAQ,CAAC;QACrC,CAAC,EAAE,IAAI,CAAC;QAERa,QAAQ,CAACzD,IAAI,CAAC4C,QAAQ,CAAC;QACvBa,QAAQ,CAACG,QAAQ,EAAE;MACrB,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC;IAEF;IACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA8BF;EAEA;;;EAGAC,UAAUA,CAAClJ,YAA0B;IACnC4B,OAAO,CAACoG,GAAG,CACT,sDAAsD,EACtDhI,YAAY,CACb;IAED;IACA,MAAMmJ,YAAY,GAAS;MACzBpF,EAAE,EAAE/D,YAAY,CAAC+D,EAAE;MACnBqE,MAAM,EAAEpI,YAAY,CAACoI,MAAM;MAC3BO,SAAS,EAAE;QACTN,GAAG,EAAE,cAAc;QACnBtE,EAAE,EAAE,cAAc;QAClBuE,QAAQ,EAAE,KAAK;QACfC,KAAK,EAAE,qBAAqB;QAC5BC,IAAI,EAAE,MAAM;QACZC,QAAQ,EAAE,IAAI;QACdC,KAAK,EAAE;OACR;MACDtF,IAAI,EAAEpD,YAAY,CAACoD,IAAI;MACvB0C,MAAM,EAAExG,UAAU,CAACyG,SAAS;MAC5B5B,SAAS,EAAE,IAAI+D,IAAI,EAAE,CAACW,WAAW,EAAE;MACnCd,cAAc,EAAE/H,YAAY,CAAC+H,cAAc,IAAI;KAChD;IAED,OAAO,IAAI9I,UAAU,CAAQ6J,QAAQ,IAAI;MACvClF,UAAU,CAAC,MAAK;QACdhC,OAAO,CAACoG,GAAG,CAAC,uCAAuC,EAAEmB,YAAY,CAAC;QAElE,IAAI,CAACzF,IAAI,CAAC,UAAU,CAAC;QACrB,IAAI,CAACU,IAAI,CAAC,gBAAgB,CAAC;QAE3B;QACA,MAAMgF,aAAa,GAAG,IAAI,CAACC,iBAAiB,CAC1CrJ,YAAY,EACZmJ,YAAY,CACb;QAED,IAAI,CAACpJ,UAAU,CAACsF,IAAI,CAAC+D,aAAa,CAAC;QACnC,IAAI,CAACpJ,YAAY,CAACqF,IAAI,CAAC,IAAI,CAAC;QAE5ByD,QAAQ,CAACzD,IAAI,CAAC+D,aAAa,CAAC;QAC5BN,QAAQ,CAACG,QAAQ,EAAE;MACrB,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC;EACJ;EAEA;;;EAGAK,UAAUA,CAACC,MAAc,EAAEC,MAAe;IACxC5H,OAAO,CAACoG,GAAG,CAAC,sDAAsD,EAAE;MAClEuB,MAAM;MACNC;KACD,CAAC;IAEF,MAAMC,WAAW,GAAgB;MAC/BC,OAAO,EAAE,IAAI;MACbC,OAAO,EAAE;KACV;IAED,OAAO,IAAI1K,UAAU,CAAe6J,QAAQ,IAAI;MAC9ClF,UAAU,CAAC,MAAK;QACdhC,OAAO,CAACoG,GAAG,CAAC,uCAAuC,EAAEyB,WAAW,CAAC;QAEjE,IAAI,CAAC/F,IAAI,CAAC,UAAU,CAAC;QACrB,IAAI,CAAC1D,YAAY,CAACqF,IAAI,CAAC,IAAI,CAAC;QAC5B,IAAI,CAACtF,UAAU,CAACsF,IAAI,CAAC,IAAI,CAAC;QAC1B,IAAI,CAACjB,IAAI,CAAC,UAAU,CAAC;QAErB0E,QAAQ,CAACzD,IAAI,CAACoE,WAAW,CAAC;QAC1BX,QAAQ,CAACG,QAAQ,EAAE;MACrB,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC;EACJ;EAEA;;;EAGAW,OAAOA,CAACL,MAAc;IACpB3H,OAAO,CAACoG,GAAG,CAAC,mDAAmD,EAAEuB,MAAM,CAAC;IAExE,MAAME,WAAW,GAAgB;MAC/BC,OAAO,EAAE,IAAI;MACbC,OAAO,EAAE;KACV;IAED,OAAO,IAAI1K,UAAU,CAAe6J,QAAQ,IAAI;MAC9ClF,UAAU,CAAC,MAAK;QACdhC,OAAO,CAACoG,GAAG,CAAC,oCAAoC,EAAEyB,WAAW,CAAC;QAE9D,IAAI,CAACrF,IAAI,CAAC,UAAU,CAAC;QACrB,IAAI,CAACrE,UAAU,CAACsF,IAAI,CAAC,IAAI,CAAC;QAC1B,IAAI,CAACrF,YAAY,CAACqF,IAAI,CAAC,IAAI,CAAC;QAC5B,IAAI,CAACY,aAAa,EAAE;QAEpB6C,QAAQ,CAACzD,IAAI,CAACoE,WAAW,CAAC;QAC1BX,QAAQ,CAACG,QAAQ,EAAE;MACrB,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC;EACJ;EAEA;;;EAGAY,gBAAgBA,CACdC,UAA4B,EAC5BC,WAA6B;IAE7B,IAAI,CAACpJ,iBAAiB,GAAGmJ,UAAU;IACnC,IAAI,CAAClJ,kBAAkB,GAAGmJ,WAAW;EACvC;EAEA;;;EAGQ7C,UAAUA,CAAC9D,IAAY,EAAEkC,IAAY;IAC3C;IACA1B,UAAU,CAAC,MAAK;MACd,IAAI,CAACgC,gBAAgB,CAAC;QACpB2D,MAAM,EAAE,IAAI,CAAC1I,aAAa;QAC1BmJ,QAAQ,EAAE,YAAY;QACtB5G,IAAI;QACJkC;OACD,CAAC;IACJ,CAAC,EAAE,GAAG,CAAC;EACT;EAEA;;;EAGc2E,YAAYA,CAACnC,QAAkB;IAAA,IAAAoC,KAAA;IAAA,OAAAC,iBAAA;MAC3CvI,OAAO,CAACoG,GAAG,CAAC,0CAA0C,EAAEF,QAAQ,CAAC;MAEjE,MAAMsC,WAAW,GAA2B;QAC1CtI,KAAK,EAAE,IAAI;QACXuI,KAAK,EAAEvC,QAAQ,KAAKzI,QAAQ,CAACiL;OAC9B;MAED,IAAI;QACF,MAAMC,MAAM,SAASC,SAAS,CAACC,YAAY,CAACR,YAAY,CAACG,WAAW,CAAC;QACrExI,OAAO,CAACoG,GAAG,CAAC,qCAAqC,CAAC;QAElDkC,KAAI,CAACzJ,WAAW,GAAG8J,MAAM;QACzB,OAAOA,MAAM;OACd,CAAC,OAAO5I,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;QACjE,MAAM,IAAI+I,KAAK,CAAC,2CAA2C,CAAC;;IAC7D;EACH;EAEA;;;EAGcC,WAAWA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAT,iBAAA;MACvB,IAAI,CAACS,MAAI,CAACpK,cAAc,EAAE;QACxB,MAAM,IAAIkK,KAAK,CAAC,gCAAgC,CAAC;;MAGnD,IAAI;QACF,MAAMG,KAAK,SAASD,MAAI,CAACpK,cAAc,CAACmK,WAAW,CAAC;UAClDG,mBAAmB,EAAE,IAAI;UACzBC,mBAAmB,EAAE;SACtB,CAAC;QAEF,MAAMH,MAAI,CAACpK,cAAc,CAACwK,mBAAmB,CAACH,KAAK,CAAC;QACpDjJ,OAAO,CAACoG,GAAG,CACT,4DAA4D,CAC7D;QAED,OAAO6C,KAAK;OACb,CAAC,OAAOlJ,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;QAC7D,MAAMA,KAAK;;IACZ;EACH;EAEA;;;EAGcsJ,YAAYA,CACxBJ,KAAgC;IAAA,IAAAK,MAAA;IAAA,OAAAf,iBAAA;MAEhC,IAAI,CAACe,MAAI,CAAC1K,cAAc,EAAE;QACxB,MAAM,IAAIkK,KAAK,CAAC,gCAAgC,CAAC;;MAGnD,IAAI;QACF,MAAMQ,MAAI,CAAC1K,cAAc,CAAC2K,oBAAoB,CAACN,KAAK,CAAC;QACrDjJ,OAAO,CAACoG,GAAG,CAAC,wCAAwC,CAAC;QAErD,MAAMoD,MAAM,SAASF,MAAI,CAAC1K,cAAc,CAACyK,YAAY,EAAE;QACvD,MAAMC,MAAI,CAAC1K,cAAc,CAACwK,mBAAmB,CAACI,MAAM,CAAC;QACrDxJ,OAAO,CAACoG,GAAG,CACT,6DAA6D,CAC9D;QAED,OAAOoD,MAAM;OACd,CAAC,OAAOzJ,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;QAC9D,MAAMA,KAAK;;IACZ;EACH;EAEA;;;EAGcyE,iBAAiBA,CAACD,MAAW;IAAA,IAAAkF,MAAA;IAAA,OAAAlB,iBAAA;MACzC,IAAI;QACF,MAAMU,KAAK,GAAG1D,IAAI,CAACmE,KAAK,CAACnF,MAAM,CAACb,IAAI,CAAC;QACrC1D,OAAO,CAACoG,GAAG,CAAC,yCAAyC,EAAE6C,KAAK,CAAC;QAE7D;QACA,MAAMN,MAAM,SAASc,MAAI,CAACpB,YAAY,CAAC5K,QAAQ,CAACkM,KAAK,CAAC;QACtDF,MAAI,CAACG,8BAA8B,CAACjB,MAAM,CAAC;QAE3C;QACA,MAAMa,MAAM,SAASC,MAAI,CAACJ,YAAY,CAACJ,KAAK,CAAC;QAC7CQ,MAAI,CAACnE,UAAU,CAAC,QAAQ,EAAEC,IAAI,CAACC,SAAS,CAACgE,MAAM,CAAC,CAAC;QAEjDxJ,OAAO,CAACoG,GAAG,CAAC,mDAAmD,CAAC;OACjE,CAAC,OAAOrG,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,8CAA8C,EAAEA,KAAK,CAAC;;IACrE;EACH;EAEA;;;EAGc0E,kBAAkBA,CAACF,MAAW;IAAA,IAAAsF,MAAA;IAAA,OAAAtB,iBAAA;MAC1C,IAAI;QACF,MAAMiB,MAAM,GAAGjE,IAAI,CAACmE,KAAK,CAACnF,MAAM,CAACb,IAAI,CAAC;QACtC1D,OAAO,CAACoG,GAAG,CAAC,0CAA0C,EAAEoD,MAAM,CAAC;QAE/D,IAAIK,MAAI,CAACjL,cAAc,EAAE;UACvB,MAAMiL,MAAI,CAACjL,cAAc,CAAC2K,oBAAoB,CAACC,MAAM,CAAC;UACtDxJ,OAAO,CAACoG,GAAG,CAAC,gDAAgD,CAAC;;OAEhE,CAAC,OAAOrG,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,+CAA+C,EAAEA,KAAK,CAAC;;IACtE;EACH;EAEA;;;EAGc2E,wBAAwBA,CAACH,MAAW;IAAA,IAAAuF,MAAA;IAAA,OAAAvB,iBAAA;MAChD,IAAI;QACF,MAAMlD,SAAS,GAAGE,IAAI,CAACmE,KAAK,CAACnF,MAAM,CAACb,IAAI,CAAC;QACzC1D,OAAO,CAACoG,GAAG,CAAC,iDAAiD,EAAEf,SAAS,CAAC;QAEzE,IAAIyE,MAAI,CAAClL,cAAc,IAAIyG,SAAS,EAAE;UACpC,MAAMyE,MAAI,CAAClL,cAAc,CAACmL,eAAe,CACvC,IAAIC,eAAe,CAAC3E,SAAS,CAAC,CAC/B;UACDrF,OAAO,CAACoG,GAAG,CAAC,yDAAyD,CAAC;;OAEzE,CAAC,OAAOrG,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,+CAA+C,EAAEA,KAAK,CAAC;;IACtE;EACH;EAEA;;;EAGQ6J,8BAA8BA,CAACjB,MAAmB;IACxD,IAAI,CAAC,IAAI,CAAC/J,cAAc,EAAE;IAE1B+J,MAAM,CAACsB,SAAS,EAAE,CAACnJ,OAAO,CAAEoJ,KAAK,IAAI;MACnC,IAAI,CAACtL,cAAe,CAACuL,QAAQ,CAACD,KAAK,EAAEvB,MAAM,CAAC;MAC5C3I,OAAO,CAACoG,GAAG,CACT,iDAAiD,EACjD8D,KAAK,CAACE,IAAI,CACX;IACH,CAAC,CAAC;EACJ;EAEA;;;EAGQ3C,iBAAiBA,CAACrJ,YAA0B,EAAE6F,IAAU;IAC9DjE,OAAO,CAACoG,GAAG,CAAC,mDAAmD,CAAC;IAEhE;IACA,IAAI,CAACiC,YAAY,CAACjK,YAAY,CAACoD,IAAI,CAAC,CACjC6I,IAAI,CAAE1B,MAAM,IAAI;MACf,IAAI,CAACiB,8BAA8B,CAACjB,MAAM,CAAC;MAC3C,IAAI,CAAC2B,iBAAiB,EAAE;MACxBtK,OAAO,CAACoG,GAAG,CAAC,wCAAwC,CAAC;IACvD,CAAC,CAAC,CACDmE,KAAK,CAAExK,KAAK,IAAI;MACfC,OAAO,CAACD,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;IAC/D,CAAC,CAAC;IAEJ,OAAOkE,IAAI;EACb;EAEA;;;EAGQqG,iBAAiBA,CAAA;IACvB,IAAI,IAAI,CAACzL,WAAW,IAAI,IAAI,CAACE,iBAAiB,EAAE;MAC9C,IAAI,CAACA,iBAAiB,CAAC0G,SAAS,GAAG,IAAI,CAAC5G,WAAW;MACnD,IAAI,CAACE,iBAAiB,CAAC2G,KAAK,GAAG,IAAI,CAAC,CAAC;MACrC,IAAI,CAAC3G,iBAAiB,CAACyD,IAAI,EAAE;MAC7BxC,OAAO,CAACoG,GAAG,CAAC,uCAAuC,CAAC;;IAGtD;IACA,MAAMT,MAAM,GAAGC,QAAQ,CAACC,gBAAgB,CAAC,OAAO,CAAC;IACjD,IAAI,IAAI,CAAChH,WAAW,IAAI8G,MAAM,CAACI,MAAM,GAAG,CAAC,IAAI,CAACJ,MAAM,CAAC,CAAC,CAAC,CAACF,SAAS,EAAE;MACjEE,MAAM,CAAC,CAAC,CAAC,CAACF,SAAS,GAAG,IAAI,CAAC5G,WAAW;MACtC8G,MAAM,CAAC,CAAC,CAAC,CAACD,KAAK,GAAG,IAAI;MACtBC,MAAM,CAAC,CAAC,CAAC,CAACnD,IAAI,EAAE;;EAEpB;EAEA;;;EAGQ2E,sBAAsBA,CAACjB,QAAkB;IAC/ClG,OAAO,CAACoG,GAAG,CAAC,+CAA+C,CAAC;IAE5D,IAAI,CAACiC,YAAY,CAACnC,QAAQ,CAAC,CACxBmE,IAAI,CAAE1B,MAAM,IAAI;MACf,IAAI,CAACiB,8BAA8B,CAACjB,MAAM,CAAC;MAC3C,IAAI,CAAC2B,iBAAiB,EAAE;MAExB;MACA,OAAO,IAAI,CAACvB,WAAW,EAAE;IAC3B,CAAC,CAAC,CACDsB,IAAI,CAAEpB,KAAK,IAAI;MACdjJ,OAAO,CAACoG,GAAG,CAAC,iDAAiD,CAAC;MAC9D;IACF,CAAC,CAAC,CACDmE,KAAK,CAAExK,KAAK,IAAI;MACfC,OAAO,CAACD,KAAK,CACX,qDAAqD,EACrDA,KAAK,CACN;IACH,CAAC,CAAC;EACN;EAEA;;;EAGQsE,aAAaA,CAAA;IACnB,IAAI,IAAI,CAACxF,WAAW,EAAE;MACpB,IAAI,CAACA,WAAW,CAACoL,SAAS,EAAE,CAACnJ,OAAO,CAAEoJ,KAAK,IAAKA,KAAK,CAACpI,IAAI,EAAE,CAAC;MAC7D,IAAI,CAACjD,WAAW,GAAG,IAAI;;IAGzB,IAAI,IAAI,CAACD,cAAc,EAAE;MACvB,IAAI,CAACA,cAAc,CAAC4L,KAAK,EAAE;MAC3B,IAAI,CAAC5L,cAAc,GAAG,IAAI;;IAG5B,IAAI,CAACE,YAAY,GAAG,IAAI;EAC1B;EAEA;;;EAGA2L,YAAYA,CAAA;IACVzK,OAAO,CAACoG,GAAG,CAAC,uCAAuC,CAAC;EACtD;EAEA;;;EAGAsE,WAAWA,CAAA;IACT,IAAI,CAAC/L,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;IAC1C,OAAO,IAAI,CAACA,cAAc;EAC5B;EAEA;;;EAGAgM,WAAWA,CAAA;IACT,IAAI,CAACjM,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;IAC1C,OAAO,IAAI,CAACA,cAAc;EAC5B;EAEA;;;EAGAkM,WAAWA,CACTjD,MAAc,EACdkD,WAAqB,EACrBC,WAAqB;IAErB,OAAO,IAAI,CAAC7M,MAAM,CACf8M,MAAM,CAAmC;MACxCC,QAAQ,EAAErN,0BAA0B;MACpCsN,SAAS,EAAE;QAAEtD,MAAM;QAAEc,KAAK,EAAEoC,WAAW;QAAE3K,KAAK,EAAE4K;MAAW;KAC5D,CAAC,CACDI,IAAI,CACH3N,GAAG,CAAE4N,MAAM,IAAI;MACb,OAAOA,MAAM,CAACzH,IAAK,CAAC0H,eAAe;IACrC,CAAC,CAAC,EACF5N,UAAU,CAAEuC,KAAK,IAAI;MACnBC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,OAAOzC,UAAU,CACf,MAAM,IAAIwL,KAAK,CAAC,sCAAsC,CAAC,CACxD;IACH,CAAC,CAAC,CACH;EACL;EAEA;;;EAGQ1B,oBAAoBA,CAACiE,YAAkB;IAC7C,MAAMjN,YAAY,GAAiB;MACjC+D,EAAE,EAAEkJ,YAAY,CAAClJ,EAAE;MACnBqE,MAAM,EAAE6E,YAAY,CAACtE,SAAS;MAC9BvF,IAAI,EAAE6J,YAAY,CAAC7J,IAAI;MACvB2E,cAAc,EAAEkF,YAAY,CAAClF,cAAc;MAC3C8C,KAAK,EAAE,qCAAqC;MAC5CqC,SAAS,EAAE,IAAIhF,IAAI,EAAE,CAACW,WAAW;KAClC;IAEDjH,OAAO,CAACoG,GAAG,CAAC,4CAA4C,EAAEhI,YAAY,CAAC;IAEvE,IAAI,CAAC0D,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;IACvB,IAAI,CAAC1D,YAAY,CAACqF,IAAI,CAACrF,YAAY,CAAC;IACpC,IAAI,CAACD,UAAU,CAACsF,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IAE5B;IACA,IAAI,CAACjB,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC;EAC7B;EAEA+I,WAAWA,CAAA;IACT,IAAI,CAACxI,aAAa,EAAE;IACpB,IAAI,CAACsB,aAAa,EAAE;EACtB;;;uBA/+BWtG,WAAW,EAAAyN,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,aAAA;IAAA;EAAA;;;aAAX9N,WAAW;MAAA+N,OAAA,EAAX/N,WAAW,CAAAgO,IAAA;MAAAC,UAAA,EAFV;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}