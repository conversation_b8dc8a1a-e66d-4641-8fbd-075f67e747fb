{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/OneDrive/Bureau/Project PI/devBridge/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { BehaviorSubject, throwError, from } from 'rxjs';\nimport { map, catchError, switchMap } from 'rxjs/operators';\nimport { CallStatus } from '../models/message.model';\nimport { INITIATE_CALL_MUTATION, ACCEPT_CALL_MUTATION, REJECT_CALL_MUTATION, END_CALL_MUTATION, TOGGLE_CALL_MEDIA_MUTATION, INCOMING_CALL_SUBSCRIPTION, CALL_STATUS_CHANGED_SUBSCRIPTION } from '../graphql/message.graphql';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"apollo-angular\";\nimport * as i2 from \"./logger.service\";\nexport class CallService {\n  constructor(apollo, logger) {\n    this.apollo = apollo;\n    this.logger = logger;\n    // État des appels\n    this.activeCall = new BehaviorSubject(null);\n    this.incomingCall = new BehaviorSubject(null);\n    // Observables publics\n    this.activeCall$ = this.activeCall.asObservable();\n    this.incomingCall$ = this.incomingCall.asObservable();\n    // Propriétés pour la gestion des sons\n    this.sounds = {};\n    this.isPlaying = {};\n    this.muted = false;\n    // États simples pour les médias\n    this.isVideoEnabled = true;\n    this.isAudioEnabled = true;\n    this.preloadSounds();\n    this.initializeSubscriptions();\n  }\n  /**\n   * Initialise les subscriptions avec un délai pour s'assurer que l'authentification est prête\n   */\n  initializeSubscriptions() {\n    // Attendre un peu pour s'assurer que l'authentification est prête\n    setTimeout(() => {\n      this.subscribeToIncomingCalls();\n      this.subscribeToCallStatusChanges();\n    }, 1000);\n    // Réessayer après 5 secondes si la première tentative échoue\n    setTimeout(() => {\n      if (!this.incomingCall.value) {\n        console.log('🔄 [CallService] Retrying subscription initialization...');\n        this.subscribeToIncomingCalls();\n        this.subscribeToCallStatusChanges();\n      }\n    }, 5000);\n  }\n  /**\n   * Précharge les sons utilisés dans l'application\n   */\n  preloadSounds() {\n    // Créer des sons synthétiques si les fichiers ne sont pas disponibles\n    this.createSyntheticSounds();\n    // Essayer de charger les vrais fichiers audio\n    this.loadSound('ringtone', 'assets/sounds/ringtone.mp3');\n    this.loadSound('call-end', 'assets/sounds/call-end.mp3');\n    this.loadSound('call-connected', 'assets/sounds/call-connected.mp3');\n  }\n  /**\n   * Crée des sons synthétiques comme fallback\n   */\n  createSyntheticSounds() {\n    try {\n      // Créer un contexte audio pour les sons synthétiques\n      const audioContext = new (window.AudioContext || window.webkitAudioContext)();\n      // Son de sonnerie (bip répétitif)\n      this.sounds['ringtone-synthetic'] = this.createBeepSound(audioContext, 800, 0.3, true);\n      // Son de connexion (bip court)\n      this.sounds['call-connected-synthetic'] = this.createBeepSound(audioContext, 1000, 0.2, false);\n      // Son de fin d'appel (bip grave)\n      this.sounds['call-end-synthetic'] = this.createBeepSound(audioContext, 400, 0.5, false);\n      console.log('🔊 [CallService] Synthetic sounds created as fallback');\n    } catch (error) {\n      console.warn('⚠️ [CallService] Could not create synthetic sounds:', error);\n    }\n  }\n  /**\n   * Crée un son de bip synthétique\n   */\n  createBeepSound(audioContext, frequency, duration, loop) {\n    // Créer un buffer audio\n    const sampleRate = audioContext.sampleRate;\n    const numSamples = sampleRate * duration;\n    const buffer = audioContext.createBuffer(1, numSamples, sampleRate);\n    const channelData = buffer.getChannelData(0);\n    // Générer une onde sinusoïdale\n    for (let i = 0; i < numSamples; i++) {\n      channelData[i] = Math.sin(2 * Math.PI * frequency * i / sampleRate) * 0.3;\n    }\n    // Créer un élément audio factice qui utilise le son synthétique\n    const audio = new Audio();\n    audio.loop = loop;\n    audio.volume = 0.3;\n    // Simuler la lecture avec Web Audio API\n    audio.playSynthetic = () => {\n      const source = audioContext.createBufferSource();\n      source.buffer = buffer;\n      source.connect(audioContext.destination);\n      source.start();\n      return Promise.resolve();\n    };\n    return audio;\n  }\n  /**\n   * Charge un fichier audio\n   */\n  loadSound(name, path) {\n    try {\n      const audio = new Audio();\n      // Configuration de l'audio\n      audio.preload = 'auto';\n      audio.volume = 0.7;\n      // Gérer les événements de chargement\n      audio.addEventListener('canplaythrough', () => {\n        console.log(`✅ [CallService] Sound ${name} loaded successfully from ${path}`);\n      });\n      audio.addEventListener('error', e => {\n        console.error(`❌ [CallService] Error loading sound ${name} from ${path}:`, e);\n        console.log(`🔄 [CallService] Trying to load ${name} with different approach...`);\n        // Essayer de charger avec un chemin relatif\n        const altPath = path.startsWith('/') ? path.substring(1) : path;\n        if (altPath !== path) {\n          setTimeout(() => {\n            audio.src = altPath;\n            audio.load();\n          }, 100);\n        }\n      });\n      audio.addEventListener('ended', () => {\n        this.isPlaying[name] = false;\n        console.log(`🔇 [CallService] Sound ${name} ended`);\n      });\n      // Définir la source et charger\n      audio.src = path;\n      audio.load();\n      this.sounds[name] = audio;\n      this.isPlaying[name] = false;\n      console.log(`🔊 [CallService] Loading sound ${name} from ${path}`);\n    } catch (error) {\n      console.error(`❌ [CallService] Error creating audio element for ${name}:`, error);\n    }\n  }\n  /**\n   * Joue un son\n   */\n  play(name, loop = false) {\n    if (this.muted) {\n      console.log(`🔇 [CallService] Sound ${name} muted`);\n      return;\n    }\n    try {\n      let sound = this.sounds[name];\n      // Si le son principal n'est pas disponible, essayer la version synthétique\n      if (!sound || sound.error) {\n        const syntheticName = `${name}-synthetic`;\n        sound = this.sounds[syntheticName];\n        if (sound) {\n          console.log(`🔊 [CallService] Using synthetic sound for ${name}`);\n        }\n      }\n      if (!sound) {\n        console.warn(`🔊 [CallService] Sound ${name} not found (neither original nor synthetic)`);\n        // Créer un bip simple comme dernier recours\n        this.playSimpleBeep(name === 'ringtone' ? 800 : name === 'call-connected' ? 1000 : 400);\n        return;\n      }\n      sound.loop = loop;\n      sound.volume = 0.7;\n      if (!this.isPlaying[name]) {\n        console.log(`🔊 [CallService] Playing sound: ${name} (loop: ${loop})`);\n        // Vérifier si c'est un son synthétique\n        if (sound.playSynthetic) {\n          sound.playSynthetic().then(() => {\n            console.log(`✅ [CallService] Synthetic sound ${name} started successfully`);\n            this.isPlaying[name] = true;\n            // Gérer la boucle pour les sons synthétiques\n            if (loop) {\n              setTimeout(() => {\n                if (this.isPlaying[name]) {\n                  this.play(name, loop);\n                }\n              }, 1000);\n            } else {\n              setTimeout(() => {\n                this.isPlaying[name] = false;\n              }, 500);\n            }\n          }).catch(error => {\n            console.error(`❌ [CallService] Error playing synthetic sound ${name}:`, error);\n          });\n        } else {\n          // Son normal\n          sound.currentTime = 0;\n          sound.play().then(() => {\n            console.log(`✅ [CallService] Sound ${name} started successfully`);\n            this.isPlaying[name] = true;\n          }).catch(error => {\n            console.error(`❌ [CallService] Error playing sound ${name}:`, error);\n            // Essayer le son synthétique en cas d'échec\n            const syntheticName = `${name}-synthetic`;\n            const syntheticSound = this.sounds[syntheticName];\n            if (syntheticSound && syntheticSound.playSynthetic) {\n              console.log(`🔄 [CallService] Falling back to synthetic sound for ${name}`);\n              this.play(name, loop);\n            } else {\n              // Dernier recours : bip simple\n              this.playSimpleBeep(name === 'ringtone' ? 800 : name === 'call-connected' ? 1000 : 400);\n            }\n          });\n        }\n      } else {\n        console.log(`🔊 [CallService] Sound ${name} already playing`);\n      }\n    } catch (error) {\n      console.error(`❌ [CallService] Error in play method for ${name}:`, error);\n      // Dernier recours\n      this.playSimpleBeep(name === 'ringtone' ? 800 : name === 'call-connected' ? 1000 : 400);\n    }\n  }\n  /**\n   * Joue un bip simple comme dernier recours\n   */\n  playSimpleBeep(frequency) {\n    try {\n      const audioContext = new (window.AudioContext || window.webkitAudioContext)();\n      const oscillator = audioContext.createOscillator();\n      const gainNode = audioContext.createGain();\n      oscillator.connect(gainNode);\n      gainNode.connect(audioContext.destination);\n      oscillator.frequency.value = frequency;\n      oscillator.type = 'sine';\n      gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);\n      gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);\n      oscillator.start(audioContext.currentTime);\n      oscillator.stop(audioContext.currentTime + 0.3);\n      console.log(`🔊 [CallService] Playing simple beep at ${frequency}Hz`);\n    } catch (error) {\n      console.error('❌ [CallService] Could not play simple beep:', error);\n    }\n  }\n  /**\n   * Arrête un son\n   */\n  stop(name) {\n    try {\n      const sound = this.sounds[name];\n      if (!sound) {\n        console.warn(`🔊 [CallService] Sound ${name} not found for stopping`);\n        return;\n      }\n      if (this.isPlaying[name]) {\n        console.log(`🔇 [CallService] Stopping sound: ${name}`);\n        sound.pause();\n        sound.currentTime = 0;\n        this.isPlaying[name] = false;\n      } else {\n        console.log(`🔇 [CallService] Sound ${name} was not playing`);\n      }\n    } catch (error) {\n      console.error(`❌ [CallService] Error stopping sound ${name}:`, error);\n    }\n  }\n  /**\n   * S'abonne aux appels entrants\n   */\n  subscribeToIncomingCalls() {\n    console.log('🔔 [CallService] Setting up incoming call subscription...');\n    try {\n      this.apollo.subscribe({\n        query: INCOMING_CALL_SUBSCRIPTION,\n        errorPolicy: 'all' // Continuer même en cas d'erreur\n      }).subscribe({\n        next: ({\n          data,\n          errors\n        }) => {\n          if (errors) {\n            console.warn('⚠️ [CallService] GraphQL errors in subscription:', errors);\n          }\n          if (data?.incomingCall) {\n            console.log('📞 [CallService] Incoming call received:', {\n              callId: data.incomingCall.id,\n              callType: data.incomingCall.type,\n              caller: data.incomingCall.caller?.username,\n              conversationId: data.incomingCall.conversationId\n            });\n            this.handleIncomingCall(data.incomingCall);\n          }\n        },\n        error: error => {\n          console.error('❌ [CallService] Error in incoming call subscription:', error);\n          // Réessayer après 5 secondes en cas d'erreur\n          setTimeout(() => {\n            console.log('🔄 [CallService] Retrying incoming call subscription...');\n            this.subscribeToIncomingCalls();\n          }, 5000);\n        },\n        complete: () => {\n          console.log('🔚 [CallService] Incoming call subscription completed');\n          // Réessayer si la subscription se ferme de manière inattendue\n          setTimeout(() => {\n            console.log('🔄 [CallService] Restarting subscription after completion...');\n            this.subscribeToIncomingCalls();\n          }, 2000);\n        }\n      });\n    } catch (error) {\n      console.error('❌ [CallService] Failed to create subscription:', error);\n      // Réessayer après 3 secondes\n      setTimeout(() => {\n        this.subscribeToIncomingCalls();\n      }, 3000);\n    }\n  }\n  /**\n   * Méthode publique pour forcer la réinitialisation de la subscription\n   */\n  reinitializeSubscription() {\n    console.log('🔄 [CallService] Manually reinitializing subscription...');\n    this.subscribeToIncomingCalls();\n    this.subscribeToCallStatusChanges();\n  }\n  /**\n   * Méthode de test pour vérifier les sons\n   */\n  testSounds() {\n    console.log('🧪 [CallService] Testing sounds...');\n    // Test de la sonnerie\n    console.log('🔊 Testing ringtone...');\n    this.play('ringtone', true);\n    setTimeout(() => {\n      this.stop('ringtone');\n      console.log('🔊 Testing call-connected...');\n      this.play('call-connected');\n    }, 3000);\n    setTimeout(() => {\n      console.log('🔊 Testing call-end...');\n      this.play('call-end');\n    }, 5000);\n  }\n  /**\n   * S'abonne aux changements de statut d'appel\n   */\n  subscribeToCallStatusChanges() {\n    console.log('📞 [CallService] Setting up call status change subscription...');\n    try {\n      this.apollo.subscribe({\n        query: CALL_STATUS_CHANGED_SUBSCRIPTION,\n        errorPolicy: 'all'\n      }).subscribe({\n        next: ({\n          data,\n          errors\n        }) => {\n          if (errors) {\n            console.warn('⚠️ [CallService] GraphQL errors in call status subscription:', errors);\n          }\n          if (data?.callStatusChanged) {\n            console.log('📞 [CallService] Call status changed:', data.callStatusChanged);\n            this.handleCallStatusChange(data.callStatusChanged);\n          }\n        },\n        error: error => {\n          console.error('❌ [CallService] Error in call status subscription:', error);\n          // Réessayer après 5 secondes\n          setTimeout(() => {\n            this.subscribeToCallStatusChanges();\n          }, 5000);\n        },\n        complete: () => {\n          console.log('🔚 [CallService] Call status subscription completed');\n          // Réessayer si la subscription se ferme\n          setTimeout(() => {\n            this.subscribeToCallStatusChanges();\n          }, 2000);\n        }\n      });\n    } catch (error) {\n      console.error('❌ [CallService] Failed to create call status subscription:', error);\n      setTimeout(() => {\n        this.subscribeToCallStatusChanges();\n      }, 3000);\n    }\n  }\n  /**\n   * Gère un appel entrant\n   */\n  handleIncomingCall(call) {\n    console.log('🔔 [CallService] Handling incoming call:', {\n      callId: call.id,\n      callType: call.type,\n      caller: call.caller?.username,\n      conversationId: call.conversationId\n    });\n    this.incomingCall.next(call);\n    this.play('ringtone', true);\n    console.log('🔊 [CallService] Ringtone started, call notification sent to UI');\n  }\n  /**\n   * Gère les changements de statut d'appel\n   */\n  handleCallStatusChange(call) {\n    console.log('📞 [CallService] Call status changed:', call.status);\n    switch (call.status) {\n      case CallStatus.REJECTED:\n        console.log('❌ [CallService] Call was rejected');\n        this.stop('ringtone');\n        this.play('call-end');\n        this.activeCall.next(null);\n        this.incomingCall.next(null);\n        break;\n      case CallStatus.ENDED:\n        console.log('📞 [CallService] Call ended');\n        this.stopAllSounds();\n        this.play('call-end');\n        this.activeCall.next(null);\n        this.incomingCall.next(null);\n        break;\n      case CallStatus.CONNECTED:\n        console.log('✅ [CallService] Call connected');\n        this.stop('ringtone');\n        this.play('call-connected');\n        this.activeCall.next(call);\n        this.incomingCall.next(null);\n        break;\n      case CallStatus.RINGING:\n        console.log('📞 [CallService] Call is ringing');\n        this.play('ringtone', true);\n        break;\n      default:\n        console.log('📞 [CallService] Unknown call status:', call.status);\n        break;\n    }\n  }\n  /**\n   * Initie un appel WebRTC\n   */\n  initiateCall(recipientId, callType, conversationId) {\n    console.log('🔄 [CallService] Initiating call:', {\n      recipientId,\n      callType,\n      conversationId\n    });\n    if (!recipientId) {\n      const error = new Error('Recipient ID is required');\n      console.error('❌ [CallService] initiateCall error:', error);\n      return throwError(() => error);\n    }\n    // Générer un ID unique pour l'appel\n    const callId = `call_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n    // Pour l'instant, utiliser une offre WebRTC factice\n    const offer = JSON.stringify({\n      type: 'offer',\n      sdp: 'v=0\\r\\no=- 0 0 IN IP4 127.0.0.1\\r\\ns=-\\r\\nt=0 0\\r\\n'\n    });\n    const variables = {\n      recipientId,\n      callType: callType,\n      callId,\n      offer,\n      conversationId\n    };\n    console.log('📤 [CallService] Sending initiate call mutation:', variables);\n    return this.apollo.mutate({\n      mutation: INITIATE_CALL_MUTATION,\n      variables\n    }).pipe(map(result => {\n      console.log('✅ [CallService] Call initiated successfully:', result);\n      if (!result.data?.initiateCall) {\n        throw new Error('No call data received from server');\n      }\n      const call = result.data.initiateCall;\n      console.log('📞 [CallService] Call details:', {\n        id: call.id,\n        type: call.type,\n        status: call.status,\n        caller: call.caller?.username,\n        recipient: call.recipient?.username\n      });\n      // Mettre à jour l'état local\n      this.activeCall.next(call);\n      return call;\n    }), catchError(error => {\n      console.error('❌ [CallService] initiateCall error:', error);\n      this.logger.error('Error initiating call:', error);\n      let errorMessage = \"Erreur lors de l'initiation de l'appel\";\n      if (error.networkError) {\n        errorMessage = 'Erreur de connexion réseau';\n      } else if (error.graphQLErrors?.length > 0) {\n        errorMessage = error.graphQLErrors[0].message || errorMessage;\n      }\n      return throwError(() => new Error(errorMessage));\n    }));\n  }\n  /**\n   * Accepte un appel entrant\n   */\n  acceptCall(incomingCall) {\n    console.log('🔄 [CallService] Accepting call:', incomingCall.id);\n    // Générer une réponse WebRTC factice\n    const answer = JSON.stringify({\n      type: 'answer',\n      sdp: 'v=0\\r\\no=- 0 0 IN IP4 127.0.0.1\\r\\ns=-\\r\\nt=0 0\\r\\n'\n    });\n    return this.apollo.mutate({\n      mutation: ACCEPT_CALL_MUTATION,\n      variables: {\n        callId: incomingCall.id,\n        answer\n      }\n    }).pipe(switchMap(result => {\n      console.log('✅ [CallService] Call accepted successfully:', result);\n      if (!result.data?.acceptCall) {\n        throw new Error('No call data received from server');\n      }\n      const call = result.data.acceptCall;\n      // Arrêter la sonnerie\n      this.stop('ringtone');\n      this.play('call-connected');\n      // Démarrer les médias pour l'appel de manière asynchrone\n      return from(this.startMediaForCall(incomingCall, call));\n    }), catchError(error => {\n      console.error('❌ [CallService] acceptCall error:', error);\n      this.logger.error('Error accepting call:', error);\n      return throwError(() => new Error(\"Erreur lors de l'acceptation de l'appel\"));\n    }));\n  }\n  /**\n   * Rejette un appel entrant\n   */\n  rejectCall(callId, reason) {\n    console.log('🔄 [CallService] Rejecting call:', callId, reason);\n    return this.apollo.mutate({\n      mutation: REJECT_CALL_MUTATION,\n      variables: {\n        callId,\n        reason: reason || 'User rejected'\n      }\n    }).pipe(map(result => {\n      console.log('✅ [CallService] Call rejected successfully:', result);\n      if (!result.data?.rejectCall) {\n        throw new Error('No response received from server');\n      }\n      // Mettre à jour l'état local\n      this.incomingCall.next(null);\n      this.activeCall.next(null);\n      // Arrêter la sonnerie\n      this.stop('ringtone');\n      return result.data.rejectCall;\n    }), catchError(error => {\n      console.error('❌ [CallService] rejectCall error:', error);\n      this.logger.error('Error rejecting call:', error);\n      return throwError(() => new Error(\"Erreur lors du rejet de l'appel\"));\n    }));\n  }\n  /**\n   * Termine un appel en cours\n   */\n  endCall(callId) {\n    console.log('🔄 [CallService] Ending call:', callId);\n    return this.apollo.mutate({\n      mutation: END_CALL_MUTATION,\n      variables: {\n        callId,\n        feedback: null // Pas de feedback pour l'instant\n      }\n    }).pipe(map(result => {\n      console.log('✅ [CallService] Call ended successfully:', result);\n      if (!result.data?.endCall) {\n        throw new Error('No response received from server');\n      }\n      // Mettre à jour l'état local\n      this.activeCall.next(null);\n      this.incomingCall.next(null);\n      // Arrêter tous les sons\n      this.stopAllSounds();\n      this.play('call-end');\n      return result.data.endCall;\n    }), catchError(error => {\n      console.error('❌ [CallService] endCall error:', error);\n      this.logger.error('Error ending call:', error);\n      return throwError(() => new Error(\"Erreur lors de la fin de l'appel\"));\n    }));\n  }\n  /**\n   * Bascule l'état des médias (audio/vidéo) pendant un appel\n   */\n  toggleMedia(callId, enableVideo, enableAudio) {\n    console.log('🔄 [CallService] Toggling media:', {\n      callId,\n      enableVideo,\n      enableAudio\n    });\n    return this.apollo.mutate({\n      mutation: TOGGLE_CALL_MEDIA_MUTATION,\n      variables: {\n        callId,\n        video: enableVideo,\n        audio: enableAudio\n      }\n    }).pipe(map(result => {\n      console.log('✅ [CallService] Media toggled successfully:', result);\n      if (!result.data?.toggleCallMedia) {\n        throw new Error('No response received from server');\n      }\n      return result.data.toggleCallMedia;\n    }), catchError(error => {\n      console.error('❌ [CallService] toggleMedia error:', error);\n      this.logger.error('Error toggling media:', error);\n      return throwError(() => new Error('Erreur lors du changement des médias'));\n    }));\n  }\n  /**\n   * Démarre les médias pour un appel accepté\n   */\n  startMediaForCall(incomingCall, call) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      console.log('🎥 [CallService] Call connected - playing connection sound');\n      // Jouer le son de connexion\n      _this.play('call-connected');\n      // Mettre à jour l'état local\n      _this.activeCall.next(call);\n      _this.incomingCall.next(null); // Supprimer l'appel entrant\n      return call;\n    })();\n  }\n  /**\n   * Active/désactive la vidéo\n   */\n  toggleVideo() {\n    this.isVideoEnabled = !this.isVideoEnabled;\n    console.log('📹 [CallService] Video toggled:', this.isVideoEnabled);\n    return this.isVideoEnabled;\n  }\n  /**\n   * Active/désactive l'audio\n   */\n  toggleAudio() {\n    this.isAudioEnabled = !this.isAudioEnabled;\n    console.log('🎤 [CallService] Audio toggled:', this.isAudioEnabled);\n    return this.isAudioEnabled;\n  }\n  /**\n   * Obtient l'état de la vidéo\n   */\n  getVideoEnabled() {\n    return this.isVideoEnabled;\n  }\n  /**\n   * Obtient l'état de l'audio\n   */\n  getAudioEnabled() {\n    return this.isAudioEnabled;\n  }\n  /**\n   * Arrête tous les sons\n   */\n  stopAllSounds() {\n    console.log('🔇 [CallService] Stopping all sounds');\n    Object.keys(this.sounds).forEach(name => {\n      this.stop(name);\n    });\n  }\n  /**\n   * Active les sons après interaction utilisateur (pour contourner les restrictions du navigateur)\n   */\n  enableSounds() {\n    console.log('🔊 [CallService] Enabling sounds after user interaction');\n    Object.values(this.sounds).forEach(sound => {\n      if (sound) {\n        sound.muted = false;\n        sound.volume = 0.7;\n        // Jouer et arrêter immédiatement pour \"débloquer\" l'audio\n        sound.play().then(() => {\n          sound.pause();\n          sound.currentTime = 0;\n        }).catch(() => {\n          // Ignorer les erreurs ici\n        });\n      }\n    });\n  }\n  ngOnDestroy() {\n    this.stopAllSounds();\n  }\n  static {\n    this.ɵfac = function CallService_Factory(t) {\n      return new (t || CallService)(i0.ɵɵinject(i1.Apollo), i0.ɵɵinject(i2.LoggerService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: CallService,\n      factory: CallService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["BehaviorSubject", "throwError", "from", "map", "catchError", "switchMap", "CallStatus", "INITIATE_CALL_MUTATION", "ACCEPT_CALL_MUTATION", "REJECT_CALL_MUTATION", "END_CALL_MUTATION", "TOGGLE_CALL_MEDIA_MUTATION", "INCOMING_CALL_SUBSCRIPTION", "CALL_STATUS_CHANGED_SUBSCRIPTION", "CallService", "constructor", "apollo", "logger", "activeCall", "incomingCall", "activeCall$", "asObservable", "incomingCall$", "sounds", "isPlaying", "muted", "isVideoEnabled", "isAudioEnabled", "preloadSounds", "initializeSubscriptions", "setTimeout", "subscribeToIncomingCalls", "subscribeToCallStatusChanges", "value", "console", "log", "createSyntheticSounds", "loadSound", "audioContext", "window", "AudioContext", "webkitAudioContext", "createBeepSound", "error", "warn", "frequency", "duration", "loop", "sampleRate", "numSamples", "buffer", "createBuffer", "channelData", "getChannelData", "i", "Math", "sin", "PI", "audio", "Audio", "volume", "playSynthetic", "source", "createBufferSource", "connect", "destination", "start", "Promise", "resolve", "name", "path", "preload", "addEventListener", "e", "altPath", "startsWith", "substring", "src", "load", "play", "sound", "syntheticName", "playSimpleBeep", "then", "catch", "currentTime", "syntheticSound", "oscillator", "createOscillator", "gainNode", "createGain", "type", "gain", "setValueAtTime", "exponentialRampToValueAtTime", "stop", "pause", "subscribe", "query", "errorPolicy", "next", "data", "errors", "callId", "id", "callType", "caller", "username", "conversationId", "handleIncomingCall", "complete", "reinitializeSubscription", "testSounds", "callStatusChanged", "handleCallStatusChange", "call", "status", "REJECTED", "ENDED", "stopAllSounds", "CONNECTED", "RINGING", "initiateCall", "recipientId", "Error", "Date", "now", "random", "toString", "substr", "offer", "JSON", "stringify", "sdp", "variables", "mutate", "mutation", "pipe", "result", "recipient", "errorMessage", "networkError", "graphQLErrors", "length", "message", "acceptCall", "answer", "startMediaForCall", "rejectCall", "reason", "endCall", "feedback", "toggleMedia", "enableVideo", "enableAudio", "video", "toggleCallMedia", "_this", "_asyncToGenerator", "toggleVideo", "toggleAudio", "getVideoEnabled", "getAudioEnabled", "Object", "keys", "for<PERSON>ach", "enableSounds", "values", "ngOnDestroy", "i0", "ɵɵinject", "i1", "Apollo", "i2", "LoggerService", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\services\\call.service.ts"], "sourcesContent": ["import { Injectable, <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';\nimport { Apollo } from 'apollo-angular';\nimport { BehaviorSubject, Observable, throwError, of, from } from 'rxjs';\nimport { map, catchError, switchMap } from 'rxjs/operators';\nimport {\n  Call,\n  CallType,\n  CallStatus,\n  IncomingCall,\n  CallSuccess,\n} from '../models/message.model';\nimport {\n  INITIATE_CALL_MUTATION,\n  ACCEPT_CALL_MUTATION,\n  REJECT_CALL_MUTATION,\n  END_CALL_MUTATION,\n  TOGGLE_CALL_MEDIA_MUTATION,\n  INCOMING_CALL_SUBSCRIPTION,\n  CALL_STATUS_CHANGED_SUBSCRIPTION,\n} from '../graphql/message.graphql';\nimport { LoggerService } from './logger.service';\n\n@Injectable({\n  providedIn: 'root',\n})\nexport class CallService implements OnDestroy {\n  // État des appels\n  private activeCall = new BehaviorSubject<Call | null>(null);\n  private incomingCall = new BehaviorSubject<IncomingCall | null>(null);\n\n  // Observables publics\n  public activeCall$ = this.activeCall.asObservable();\n  public incomingCall$ = this.incomingCall.asObservable();\n\n  // Propriétés pour la gestion des sons\n  private sounds: { [key: string]: HTMLAudioElement } = {};\n  private isPlaying: { [key: string]: boolean } = {};\n  private muted = false;\n\n  // États simples pour les médias\n  private isVideoEnabled = true;\n  private isAudioEnabled = true;\n\n  constructor(private apollo: Apollo, private logger: LoggerService) {\n    this.preloadSounds();\n    this.initializeSubscriptions();\n  }\n\n  /**\n   * Initialise les subscriptions avec un délai pour s'assurer que l'authentification est prête\n   */\n  private initializeSubscriptions(): void {\n    // Attendre un peu pour s'assurer que l'authentification est prête\n    setTimeout(() => {\n      this.subscribeToIncomingCalls();\n      this.subscribeToCallStatusChanges();\n    }, 1000);\n\n    // Réessayer après 5 secondes si la première tentative échoue\n    setTimeout(() => {\n      if (!this.incomingCall.value) {\n        console.log('🔄 [CallService] Retrying subscription initialization...');\n        this.subscribeToIncomingCalls();\n        this.subscribeToCallStatusChanges();\n      }\n    }, 5000);\n  }\n\n  /**\n   * Précharge les sons utilisés dans l'application\n   */\n  private preloadSounds(): void {\n    // Créer des sons synthétiques si les fichiers ne sont pas disponibles\n    this.createSyntheticSounds();\n\n    // Essayer de charger les vrais fichiers audio\n    this.loadSound('ringtone', 'assets/sounds/ringtone.mp3');\n    this.loadSound('call-end', 'assets/sounds/call-end.mp3');\n    this.loadSound('call-connected', 'assets/sounds/call-connected.mp3');\n  }\n\n  /**\n   * Crée des sons synthétiques comme fallback\n   */\n  private createSyntheticSounds(): void {\n    try {\n      // Créer un contexte audio pour les sons synthétiques\n      const audioContext = new (window.AudioContext ||\n        (window as any).webkitAudioContext)();\n\n      // Son de sonnerie (bip répétitif)\n      this.sounds['ringtone-synthetic'] = this.createBeepSound(\n        audioContext,\n        800,\n        0.3,\n        true\n      );\n\n      // Son de connexion (bip court)\n      this.sounds['call-connected-synthetic'] = this.createBeepSound(\n        audioContext,\n        1000,\n        0.2,\n        false\n      );\n\n      // Son de fin d'appel (bip grave)\n      this.sounds['call-end-synthetic'] = this.createBeepSound(\n        audioContext,\n        400,\n        0.5,\n        false\n      );\n\n      console.log('🔊 [CallService] Synthetic sounds created as fallback');\n    } catch (error) {\n      console.warn(\n        '⚠️ [CallService] Could not create synthetic sounds:',\n        error\n      );\n    }\n  }\n\n  /**\n   * Crée un son de bip synthétique\n   */\n  private createBeepSound(\n    audioContext: AudioContext,\n    frequency: number,\n    duration: number,\n    loop: boolean\n  ): HTMLAudioElement {\n    // Créer un buffer audio\n    const sampleRate = audioContext.sampleRate;\n    const numSamples = sampleRate * duration;\n    const buffer = audioContext.createBuffer(1, numSamples, sampleRate);\n    const channelData = buffer.getChannelData(0);\n\n    // Générer une onde sinusoïdale\n    for (let i = 0; i < numSamples; i++) {\n      channelData[i] =\n        Math.sin((2 * Math.PI * frequency * i) / sampleRate) * 0.3;\n    }\n\n    // Créer un élément audio factice qui utilise le son synthétique\n    const audio = new Audio();\n    audio.loop = loop;\n    audio.volume = 0.3;\n\n    // Simuler la lecture avec Web Audio API\n    (audio as any).playSynthetic = () => {\n      const source = audioContext.createBufferSource();\n      source.buffer = buffer;\n      source.connect(audioContext.destination);\n      source.start();\n      return Promise.resolve();\n    };\n\n    return audio;\n  }\n\n  /**\n   * Charge un fichier audio\n   */\n  private loadSound(name: string, path: string): void {\n    try {\n      const audio = new Audio();\n\n      // Configuration de l'audio\n      audio.preload = 'auto';\n      audio.volume = 0.7;\n\n      // Gérer les événements de chargement\n      audio.addEventListener('canplaythrough', () => {\n        console.log(\n          `✅ [CallService] Sound ${name} loaded successfully from ${path}`\n        );\n      });\n\n      audio.addEventListener('error', (e) => {\n        console.error(\n          `❌ [CallService] Error loading sound ${name} from ${path}:`,\n          e\n        );\n        console.log(\n          `🔄 [CallService] Trying to load ${name} with different approach...`\n        );\n\n        // Essayer de charger avec un chemin relatif\n        const altPath = path.startsWith('/') ? path.substring(1) : path;\n        if (altPath !== path) {\n          setTimeout(() => {\n            audio.src = altPath;\n            audio.load();\n          }, 100);\n        }\n      });\n\n      audio.addEventListener('ended', () => {\n        this.isPlaying[name] = false;\n        console.log(`🔇 [CallService] Sound ${name} ended`);\n      });\n\n      // Définir la source et charger\n      audio.src = path;\n      audio.load();\n\n      this.sounds[name] = audio;\n      this.isPlaying[name] = false;\n\n      console.log(`🔊 [CallService] Loading sound ${name} from ${path}`);\n    } catch (error) {\n      console.error(\n        `❌ [CallService] Error creating audio element for ${name}:`,\n        error\n      );\n    }\n  }\n\n  /**\n   * Joue un son\n   */\n  private play(name: string, loop: boolean = false): void {\n    if (this.muted) {\n      console.log(`🔇 [CallService] Sound ${name} muted`);\n      return;\n    }\n\n    try {\n      let sound = this.sounds[name];\n\n      // Si le son principal n'est pas disponible, essayer la version synthétique\n      if (!sound || sound.error) {\n        const syntheticName = `${name}-synthetic`;\n        sound = this.sounds[syntheticName];\n        if (sound) {\n          console.log(`🔊 [CallService] Using synthetic sound for ${name}`);\n        }\n      }\n\n      if (!sound) {\n        console.warn(\n          `🔊 [CallService] Sound ${name} not found (neither original nor synthetic)`\n        );\n        // Créer un bip simple comme dernier recours\n        this.playSimpleBeep(\n          name === 'ringtone' ? 800 : name === 'call-connected' ? 1000 : 400\n        );\n        return;\n      }\n\n      sound.loop = loop;\n      sound.volume = 0.7;\n\n      if (!this.isPlaying[name]) {\n        console.log(`🔊 [CallService] Playing sound: ${name} (loop: ${loop})`);\n\n        // Vérifier si c'est un son synthétique\n        if ((sound as any).playSynthetic) {\n          (sound as any)\n            .playSynthetic()\n            .then(() => {\n              console.log(\n                `✅ [CallService] Synthetic sound ${name} started successfully`\n              );\n              this.isPlaying[name] = true;\n\n              // Gérer la boucle pour les sons synthétiques\n              if (loop) {\n                setTimeout(() => {\n                  if (this.isPlaying[name]) {\n                    this.play(name, loop);\n                  }\n                }, 1000);\n              } else {\n                setTimeout(() => {\n                  this.isPlaying[name] = false;\n                }, 500);\n              }\n            })\n            .catch((error: any) => {\n              console.error(\n                `❌ [CallService] Error playing synthetic sound ${name}:`,\n                error\n              );\n            });\n        } else {\n          // Son normal\n          sound.currentTime = 0;\n          sound\n            .play()\n            .then(() => {\n              console.log(\n                `✅ [CallService] Sound ${name} started successfully`\n              );\n              this.isPlaying[name] = true;\n            })\n            .catch((error) => {\n              console.error(\n                `❌ [CallService] Error playing sound ${name}:`,\n                error\n              );\n\n              // Essayer le son synthétique en cas d'échec\n              const syntheticName = `${name}-synthetic`;\n              const syntheticSound = this.sounds[syntheticName];\n              if (syntheticSound && (syntheticSound as any).playSynthetic) {\n                console.log(\n                  `🔄 [CallService] Falling back to synthetic sound for ${name}`\n                );\n                this.play(name, loop);\n              } else {\n                // Dernier recours : bip simple\n                this.playSimpleBeep(\n                  name === 'ringtone'\n                    ? 800\n                    : name === 'call-connected'\n                    ? 1000\n                    : 400\n                );\n              }\n            });\n        }\n      } else {\n        console.log(`🔊 [CallService] Sound ${name} already playing`);\n      }\n    } catch (error) {\n      console.error(\n        `❌ [CallService] Error in play method for ${name}:`,\n        error\n      );\n      // Dernier recours\n      this.playSimpleBeep(\n        name === 'ringtone' ? 800 : name === 'call-connected' ? 1000 : 400\n      );\n    }\n  }\n\n  /**\n   * Joue un bip simple comme dernier recours\n   */\n  private playSimpleBeep(frequency: number): void {\n    try {\n      const audioContext = new (window.AudioContext ||\n        (window as any).webkitAudioContext)();\n      const oscillator = audioContext.createOscillator();\n      const gainNode = audioContext.createGain();\n\n      oscillator.connect(gainNode);\n      gainNode.connect(audioContext.destination);\n\n      oscillator.frequency.value = frequency;\n      oscillator.type = 'sine';\n\n      gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);\n      gainNode.gain.exponentialRampToValueAtTime(\n        0.01,\n        audioContext.currentTime + 0.3\n      );\n\n      oscillator.start(audioContext.currentTime);\n      oscillator.stop(audioContext.currentTime + 0.3);\n\n      console.log(`🔊 [CallService] Playing simple beep at ${frequency}Hz`);\n    } catch (error) {\n      console.error('❌ [CallService] Could not play simple beep:', error);\n    }\n  }\n\n  /**\n   * Arrête un son\n   */\n  private stop(name: string): void {\n    try {\n      const sound = this.sounds[name];\n      if (!sound) {\n        console.warn(`🔊 [CallService] Sound ${name} not found for stopping`);\n        return;\n      }\n\n      if (this.isPlaying[name]) {\n        console.log(`🔇 [CallService] Stopping sound: ${name}`);\n        sound.pause();\n        sound.currentTime = 0;\n        this.isPlaying[name] = false;\n      } else {\n        console.log(`🔇 [CallService] Sound ${name} was not playing`);\n      }\n    } catch (error) {\n      console.error(`❌ [CallService] Error stopping sound ${name}:`, error);\n    }\n  }\n\n  /**\n   * S'abonne aux appels entrants\n   */\n  private subscribeToIncomingCalls(): void {\n    console.log('🔔 [CallService] Setting up incoming call subscription...');\n\n    try {\n      this.apollo\n        .subscribe<{ incomingCall: IncomingCall }>({\n          query: INCOMING_CALL_SUBSCRIPTION,\n          errorPolicy: 'all', // Continuer même en cas d'erreur\n        })\n        .subscribe({\n          next: ({ data, errors }) => {\n            if (errors) {\n              console.warn(\n                '⚠️ [CallService] GraphQL errors in subscription:',\n                errors\n              );\n            }\n\n            if (data?.incomingCall) {\n              console.log('📞 [CallService] Incoming call received:', {\n                callId: data.incomingCall.id,\n                callType: data.incomingCall.type,\n                caller: data.incomingCall.caller?.username,\n                conversationId: data.incomingCall.conversationId,\n              });\n              this.handleIncomingCall(data.incomingCall);\n            }\n          },\n          error: (error) => {\n            console.error(\n              '❌ [CallService] Error in incoming call subscription:',\n              error\n            );\n\n            // Réessayer après 5 secondes en cas d'erreur\n            setTimeout(() => {\n              console.log(\n                '🔄 [CallService] Retrying incoming call subscription...'\n              );\n              this.subscribeToIncomingCalls();\n            }, 5000);\n          },\n          complete: () => {\n            console.log(\n              '🔚 [CallService] Incoming call subscription completed'\n            );\n            // Réessayer si la subscription se ferme de manière inattendue\n            setTimeout(() => {\n              console.log(\n                '🔄 [CallService] Restarting subscription after completion...'\n              );\n              this.subscribeToIncomingCalls();\n            }, 2000);\n          },\n        });\n    } catch (error) {\n      console.error('❌ [CallService] Failed to create subscription:', error);\n      // Réessayer après 3 secondes\n      setTimeout(() => {\n        this.subscribeToIncomingCalls();\n      }, 3000);\n    }\n  }\n\n  /**\n   * Méthode publique pour forcer la réinitialisation de la subscription\n   */\n  public reinitializeSubscription(): void {\n    console.log('🔄 [CallService] Manually reinitializing subscription...');\n    this.subscribeToIncomingCalls();\n    this.subscribeToCallStatusChanges();\n  }\n\n  /**\n   * Méthode de test pour vérifier les sons\n   */\n  public testSounds(): void {\n    console.log('🧪 [CallService] Testing sounds...');\n\n    // Test de la sonnerie\n    console.log('🔊 Testing ringtone...');\n    this.play('ringtone', true);\n\n    setTimeout(() => {\n      this.stop('ringtone');\n      console.log('🔊 Testing call-connected...');\n      this.play('call-connected');\n    }, 3000);\n\n    setTimeout(() => {\n      console.log('🔊 Testing call-end...');\n      this.play('call-end');\n    }, 5000);\n  }\n\n  /**\n   * S'abonne aux changements de statut d'appel\n   */\n  private subscribeToCallStatusChanges(): void {\n    console.log(\n      '📞 [CallService] Setting up call status change subscription...'\n    );\n\n    try {\n      this.apollo\n        .subscribe<{ callStatusChanged: Call }>({\n          query: CALL_STATUS_CHANGED_SUBSCRIPTION,\n          errorPolicy: 'all',\n        })\n        .subscribe({\n          next: ({ data, errors }) => {\n            if (errors) {\n              console.warn(\n                '⚠️ [CallService] GraphQL errors in call status subscription:',\n                errors\n              );\n            }\n\n            if (data?.callStatusChanged) {\n              console.log(\n                '📞 [CallService] Call status changed:',\n                data.callStatusChanged\n              );\n              this.handleCallStatusChange(data.callStatusChanged);\n            }\n          },\n          error: (error) => {\n            console.error(\n              '❌ [CallService] Error in call status subscription:',\n              error\n            );\n            // Réessayer après 5 secondes\n            setTimeout(() => {\n              this.subscribeToCallStatusChanges();\n            }, 5000);\n          },\n          complete: () => {\n            console.log('🔚 [CallService] Call status subscription completed');\n            // Réessayer si la subscription se ferme\n            setTimeout(() => {\n              this.subscribeToCallStatusChanges();\n            }, 2000);\n          },\n        });\n    } catch (error) {\n      console.error(\n        '❌ [CallService] Failed to create call status subscription:',\n        error\n      );\n      setTimeout(() => {\n        this.subscribeToCallStatusChanges();\n      }, 3000);\n    }\n  }\n\n  /**\n   * Gère un appel entrant\n   */\n  private handleIncomingCall(call: IncomingCall): void {\n    console.log('🔔 [CallService] Handling incoming call:', {\n      callId: call.id,\n      callType: call.type,\n      caller: call.caller?.username,\n      conversationId: call.conversationId,\n    });\n\n    this.incomingCall.next(call);\n    this.play('ringtone', true);\n\n    console.log(\n      '🔊 [CallService] Ringtone started, call notification sent to UI'\n    );\n  }\n\n  /**\n   * Gère les changements de statut d'appel\n   */\n  private handleCallStatusChange(call: Call): void {\n    console.log('📞 [CallService] Call status changed:', call.status);\n\n    switch (call.status) {\n      case CallStatus.REJECTED:\n        console.log('❌ [CallService] Call was rejected');\n        this.stop('ringtone');\n        this.play('call-end');\n        this.activeCall.next(null);\n        this.incomingCall.next(null);\n        break;\n\n      case CallStatus.ENDED:\n        console.log('📞 [CallService] Call ended');\n        this.stopAllSounds();\n        this.play('call-end');\n        this.activeCall.next(null);\n        this.incomingCall.next(null);\n        break;\n\n      case CallStatus.CONNECTED:\n        console.log('✅ [CallService] Call connected');\n        this.stop('ringtone');\n        this.play('call-connected');\n        this.activeCall.next(call);\n        this.incomingCall.next(null);\n        break;\n\n      case CallStatus.RINGING:\n        console.log('📞 [CallService] Call is ringing');\n        this.play('ringtone', true);\n        break;\n\n      default:\n        console.log('📞 [CallService] Unknown call status:', call.status);\n        break;\n    }\n  }\n\n  /**\n   * Initie un appel WebRTC\n   */\n  initiateCall(\n    recipientId: string,\n    callType: CallType,\n    conversationId?: string\n  ): Observable<Call> {\n    console.log('🔄 [CallService] Initiating call:', {\n      recipientId,\n      callType,\n      conversationId,\n    });\n\n    if (!recipientId) {\n      const error = new Error('Recipient ID is required');\n      console.error('❌ [CallService] initiateCall error:', error);\n      return throwError(() => error);\n    }\n\n    // Générer un ID unique pour l'appel\n    const callId = `call_${Date.now()}_${Math.random()\n      .toString(36)\n      .substr(2, 9)}`;\n\n    // Pour l'instant, utiliser une offre WebRTC factice\n    const offer = JSON.stringify({\n      type: 'offer',\n      sdp: 'v=0\\r\\no=- 0 0 IN IP4 127.0.0.1\\r\\ns=-\\r\\nt=0 0\\r\\n',\n    });\n\n    const variables = {\n      recipientId,\n      callType: callType,\n      callId,\n      offer,\n      conversationId,\n    };\n\n    console.log('📤 [CallService] Sending initiate call mutation:', variables);\n\n    return this.apollo\n      .mutate<{ initiateCall: Call }>({\n        mutation: INITIATE_CALL_MUTATION,\n        variables,\n      })\n      .pipe(\n        map((result) => {\n          console.log('✅ [CallService] Call initiated successfully:', result);\n\n          if (!result.data?.initiateCall) {\n            throw new Error('No call data received from server');\n          }\n\n          const call = result.data.initiateCall;\n          console.log('📞 [CallService] Call details:', {\n            id: call.id,\n            type: call.type,\n            status: call.status,\n            caller: call.caller?.username,\n            recipient: call.recipient?.username,\n          });\n\n          // Mettre à jour l'état local\n          this.activeCall.next(call);\n\n          return call;\n        }),\n        catchError((error) => {\n          console.error('❌ [CallService] initiateCall error:', error);\n          this.logger.error('Error initiating call:', error);\n\n          let errorMessage = \"Erreur lors de l'initiation de l'appel\";\n          if (error.networkError) {\n            errorMessage = 'Erreur de connexion réseau';\n          } else if (error.graphQLErrors?.length > 0) {\n            errorMessage = error.graphQLErrors[0].message || errorMessage;\n          }\n\n          return throwError(() => new Error(errorMessage));\n        })\n      );\n  }\n\n  /**\n   * Accepte un appel entrant\n   */\n  acceptCall(incomingCall: IncomingCall): Observable<Call> {\n    console.log('🔄 [CallService] Accepting call:', incomingCall.id);\n\n    // Générer une réponse WebRTC factice\n    const answer = JSON.stringify({\n      type: 'answer',\n      sdp: 'v=0\\r\\no=- 0 0 IN IP4 127.0.0.1\\r\\ns=-\\r\\nt=0 0\\r\\n',\n    });\n\n    return this.apollo\n      .mutate<{ acceptCall: Call }>({\n        mutation: ACCEPT_CALL_MUTATION,\n        variables: {\n          callId: incomingCall.id,\n          answer,\n        },\n      })\n      .pipe(\n        switchMap((result) => {\n          console.log('✅ [CallService] Call accepted successfully:', result);\n\n          if (!result.data?.acceptCall) {\n            throw new Error('No call data received from server');\n          }\n\n          const call = result.data.acceptCall;\n\n          // Arrêter la sonnerie\n          this.stop('ringtone');\n          this.play('call-connected');\n\n          // Démarrer les médias pour l'appel de manière asynchrone\n          return from(this.startMediaForCall(incomingCall, call));\n        }),\n        catchError((error) => {\n          console.error('❌ [CallService] acceptCall error:', error);\n          this.logger.error('Error accepting call:', error);\n          return throwError(\n            () => new Error(\"Erreur lors de l'acceptation de l'appel\")\n          );\n        })\n      );\n  }\n\n  /**\n   * Rejette un appel entrant\n   */\n  rejectCall(callId: string, reason?: string): Observable<CallSuccess> {\n    console.log('🔄 [CallService] Rejecting call:', callId, reason);\n\n    return this.apollo\n      .mutate<{ rejectCall: CallSuccess }>({\n        mutation: REJECT_CALL_MUTATION,\n        variables: {\n          callId,\n          reason: reason || 'User rejected',\n        },\n      })\n      .pipe(\n        map((result) => {\n          console.log('✅ [CallService] Call rejected successfully:', result);\n\n          if (!result.data?.rejectCall) {\n            throw new Error('No response received from server');\n          }\n\n          // Mettre à jour l'état local\n          this.incomingCall.next(null);\n          this.activeCall.next(null);\n\n          // Arrêter la sonnerie\n          this.stop('ringtone');\n\n          return result.data.rejectCall;\n        }),\n        catchError((error) => {\n          console.error('❌ [CallService] rejectCall error:', error);\n          this.logger.error('Error rejecting call:', error);\n          return throwError(() => new Error(\"Erreur lors du rejet de l'appel\"));\n        })\n      );\n  }\n\n  /**\n   * Termine un appel en cours\n   */\n  endCall(callId: string): Observable<CallSuccess> {\n    console.log('🔄 [CallService] Ending call:', callId);\n\n    return this.apollo\n      .mutate<{ endCall: CallSuccess }>({\n        mutation: END_CALL_MUTATION,\n        variables: {\n          callId,\n          feedback: null, // Pas de feedback pour l'instant\n        },\n      })\n      .pipe(\n        map((result) => {\n          console.log('✅ [CallService] Call ended successfully:', result);\n\n          if (!result.data?.endCall) {\n            throw new Error('No response received from server');\n          }\n\n          // Mettre à jour l'état local\n          this.activeCall.next(null);\n          this.incomingCall.next(null);\n\n          // Arrêter tous les sons\n          this.stopAllSounds();\n          this.play('call-end');\n\n          return result.data.endCall;\n        }),\n        catchError((error) => {\n          console.error('❌ [CallService] endCall error:', error);\n          this.logger.error('Error ending call:', error);\n          return throwError(\n            () => new Error(\"Erreur lors de la fin de l'appel\")\n          );\n        })\n      );\n  }\n\n  /**\n   * Bascule l'état des médias (audio/vidéo) pendant un appel\n   */\n  toggleMedia(\n    callId: string,\n    enableVideo?: boolean,\n    enableAudio?: boolean\n  ): Observable<CallSuccess> {\n    console.log('🔄 [CallService] Toggling media:', {\n      callId,\n      enableVideo,\n      enableAudio,\n    });\n\n    return this.apollo\n      .mutate<{ toggleCallMedia: CallSuccess }>({\n        mutation: TOGGLE_CALL_MEDIA_MUTATION,\n        variables: {\n          callId,\n          video: enableVideo,\n          audio: enableAudio,\n        },\n      })\n      .pipe(\n        map((result) => {\n          console.log('✅ [CallService] Media toggled successfully:', result);\n\n          if (!result.data?.toggleCallMedia) {\n            throw new Error('No response received from server');\n          }\n\n          return result.data.toggleCallMedia;\n        }),\n        catchError((error) => {\n          console.error('❌ [CallService] toggleMedia error:', error);\n          this.logger.error('Error toggling media:', error);\n          return throwError(\n            () => new Error('Erreur lors du changement des médias')\n          );\n        })\n      );\n  }\n\n  /**\n   * Démarre les médias pour un appel accepté\n   */\n  private async startMediaForCall(\n    incomingCall: IncomingCall,\n    call: Call\n  ): Promise<Call> {\n    console.log('🎥 [CallService] Call connected - playing connection sound');\n\n    // Jouer le son de connexion\n    this.play('call-connected');\n\n    // Mettre à jour l'état local\n    this.activeCall.next(call);\n    this.incomingCall.next(null); // Supprimer l'appel entrant\n\n    return call;\n  }\n\n  /**\n   * Active/désactive la vidéo\n   */\n  toggleVideo(): boolean {\n    this.isVideoEnabled = !this.isVideoEnabled;\n    console.log('📹 [CallService] Video toggled:', this.isVideoEnabled);\n    return this.isVideoEnabled;\n  }\n\n  /**\n   * Active/désactive l'audio\n   */\n  toggleAudio(): boolean {\n    this.isAudioEnabled = !this.isAudioEnabled;\n    console.log('🎤 [CallService] Audio toggled:', this.isAudioEnabled);\n    return this.isAudioEnabled;\n  }\n\n  /**\n   * Obtient l'état de la vidéo\n   */\n  getVideoEnabled(): boolean {\n    return this.isVideoEnabled;\n  }\n\n  /**\n   * Obtient l'état de l'audio\n   */\n  getAudioEnabled(): boolean {\n    return this.isAudioEnabled;\n  }\n\n  /**\n   * Arrête tous les sons\n   */\n  private stopAllSounds(): void {\n    console.log('🔇 [CallService] Stopping all sounds');\n    Object.keys(this.sounds).forEach((name) => {\n      this.stop(name);\n    });\n  }\n\n  /**\n   * Active les sons après interaction utilisateur (pour contourner les restrictions du navigateur)\n   */\n  public enableSounds(): void {\n    console.log('🔊 [CallService] Enabling sounds after user interaction');\n    Object.values(this.sounds).forEach((sound) => {\n      if (sound) {\n        sound.muted = false;\n        sound.volume = 0.7;\n        // Jouer et arrêter immédiatement pour \"débloquer\" l'audio\n        sound\n          .play()\n          .then(() => {\n            sound.pause();\n            sound.currentTime = 0;\n          })\n          .catch(() => {\n            // Ignorer les erreurs ici\n          });\n      }\n    });\n  }\n\n  ngOnDestroy(): void {\n    this.stopAllSounds();\n  }\n}\n"], "mappings": ";AAEA,SAASA,eAAe,EAAcC,UAAU,EAAMC,IAAI,QAAQ,MAAM;AACxE,SAASC,GAAG,EAAEC,UAAU,EAAEC,SAAS,QAAQ,gBAAgB;AAC3D,SAGEC,UAAU,QAGL,yBAAyB;AAChC,SACEC,sBAAsB,EACtBC,oBAAoB,EACpBC,oBAAoB,EACpBC,iBAAiB,EACjBC,0BAA0B,EAC1BC,0BAA0B,EAC1BC,gCAAgC,QAC3B,4BAA4B;;;;AAMnC,OAAM,MAAOC,WAAW;EAkBtBC,YAAoBC,MAAc,EAAUC,MAAqB;IAA7C,KAAAD,MAAM,GAANA,MAAM;IAAkB,KAAAC,MAAM,GAANA,MAAM;IAjBlD;IACQ,KAAAC,UAAU,GAAG,IAAIlB,eAAe,CAAc,IAAI,CAAC;IACnD,KAAAmB,YAAY,GAAG,IAAInB,eAAe,CAAsB,IAAI,CAAC;IAErE;IACO,KAAAoB,WAAW,GAAG,IAAI,CAACF,UAAU,CAACG,YAAY,EAAE;IAC5C,KAAAC,aAAa,GAAG,IAAI,CAACH,YAAY,CAACE,YAAY,EAAE;IAEvD;IACQ,KAAAE,MAAM,GAAwC,EAAE;IAChD,KAAAC,SAAS,GAA+B,EAAE;IAC1C,KAAAC,KAAK,GAAG,KAAK;IAErB;IACQ,KAAAC,cAAc,GAAG,IAAI;IACrB,KAAAC,cAAc,GAAG,IAAI;IAG3B,IAAI,CAACC,aAAa,EAAE;IACpB,IAAI,CAACC,uBAAuB,EAAE;EAChC;EAEA;;;EAGQA,uBAAuBA,CAAA;IAC7B;IACAC,UAAU,CAAC,MAAK;MACd,IAAI,CAACC,wBAAwB,EAAE;MAC/B,IAAI,CAACC,4BAA4B,EAAE;IACrC,CAAC,EAAE,IAAI,CAAC;IAER;IACAF,UAAU,CAAC,MAAK;MACd,IAAI,CAAC,IAAI,CAACX,YAAY,CAACc,KAAK,EAAE;QAC5BC,OAAO,CAACC,GAAG,CAAC,0DAA0D,CAAC;QACvE,IAAI,CAACJ,wBAAwB,EAAE;QAC/B,IAAI,CAACC,4BAA4B,EAAE;;IAEvC,CAAC,EAAE,IAAI,CAAC;EACV;EAEA;;;EAGQJ,aAAaA,CAAA;IACnB;IACA,IAAI,CAACQ,qBAAqB,EAAE;IAE5B;IACA,IAAI,CAACC,SAAS,CAAC,UAAU,EAAE,4BAA4B,CAAC;IACxD,IAAI,CAACA,SAAS,CAAC,UAAU,EAAE,4BAA4B,CAAC;IACxD,IAAI,CAACA,SAAS,CAAC,gBAAgB,EAAE,kCAAkC,CAAC;EACtE;EAEA;;;EAGQD,qBAAqBA,CAAA;IAC3B,IAAI;MACF;MACA,MAAME,YAAY,GAAG,KAAKC,MAAM,CAACC,YAAY,IAC1CD,MAAc,CAACE,kBAAkB,EAAC,CAAE;MAEvC;MACA,IAAI,CAAClB,MAAM,CAAC,oBAAoB,CAAC,GAAG,IAAI,CAACmB,eAAe,CACtDJ,YAAY,EACZ,GAAG,EACH,GAAG,EACH,IAAI,CACL;MAED;MACA,IAAI,CAACf,MAAM,CAAC,0BAA0B,CAAC,GAAG,IAAI,CAACmB,eAAe,CAC5DJ,YAAY,EACZ,IAAI,EACJ,GAAG,EACH,KAAK,CACN;MAED;MACA,IAAI,CAACf,MAAM,CAAC,oBAAoB,CAAC,GAAG,IAAI,CAACmB,eAAe,CACtDJ,YAAY,EACZ,GAAG,EACH,GAAG,EACH,KAAK,CACN;MAEDJ,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;KACrE,CAAC,OAAOQ,KAAK,EAAE;MACdT,OAAO,CAACU,IAAI,CACV,qDAAqD,EACrDD,KAAK,CACN;;EAEL;EAEA;;;EAGQD,eAAeA,CACrBJ,YAA0B,EAC1BO,SAAiB,EACjBC,QAAgB,EAChBC,IAAa;IAEb;IACA,MAAMC,UAAU,GAAGV,YAAY,CAACU,UAAU;IAC1C,MAAMC,UAAU,GAAGD,UAAU,GAAGF,QAAQ;IACxC,MAAMI,MAAM,GAAGZ,YAAY,CAACa,YAAY,CAAC,CAAC,EAAEF,UAAU,EAAED,UAAU,CAAC;IACnE,MAAMI,WAAW,GAAGF,MAAM,CAACG,cAAc,CAAC,CAAC,CAAC;IAE5C;IACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,UAAU,EAAEK,CAAC,EAAE,EAAE;MACnCF,WAAW,CAACE,CAAC,CAAC,GACZC,IAAI,CAACC,GAAG,CAAE,CAAC,GAAGD,IAAI,CAACE,EAAE,GAAGZ,SAAS,GAAGS,CAAC,GAAIN,UAAU,CAAC,GAAG,GAAG;;IAG9D;IACA,MAAMU,KAAK,GAAG,IAAIC,KAAK,EAAE;IACzBD,KAAK,CAACX,IAAI,GAAGA,IAAI;IACjBW,KAAK,CAACE,MAAM,GAAG,GAAG;IAElB;IACCF,KAAa,CAACG,aAAa,GAAG,MAAK;MAClC,MAAMC,MAAM,GAAGxB,YAAY,CAACyB,kBAAkB,EAAE;MAChDD,MAAM,CAACZ,MAAM,GAAGA,MAAM;MACtBY,MAAM,CAACE,OAAO,CAAC1B,YAAY,CAAC2B,WAAW,CAAC;MACxCH,MAAM,CAACI,KAAK,EAAE;MACd,OAAOC,OAAO,CAACC,OAAO,EAAE;IAC1B,CAAC;IAED,OAAOV,KAAK;EACd;EAEA;;;EAGQrB,SAASA,CAACgC,IAAY,EAAEC,IAAY;IAC1C,IAAI;MACF,MAAMZ,KAAK,GAAG,IAAIC,KAAK,EAAE;MAEzB;MACAD,KAAK,CAACa,OAAO,GAAG,MAAM;MACtBb,KAAK,CAACE,MAAM,GAAG,GAAG;MAElB;MACAF,KAAK,CAACc,gBAAgB,CAAC,gBAAgB,EAAE,MAAK;QAC5CtC,OAAO,CAACC,GAAG,CACT,yBAAyBkC,IAAI,6BAA6BC,IAAI,EAAE,CACjE;MACH,CAAC,CAAC;MAEFZ,KAAK,CAACc,gBAAgB,CAAC,OAAO,EAAGC,CAAC,IAAI;QACpCvC,OAAO,CAACS,KAAK,CACX,uCAAuC0B,IAAI,SAASC,IAAI,GAAG,EAC3DG,CAAC,CACF;QACDvC,OAAO,CAACC,GAAG,CACT,mCAAmCkC,IAAI,6BAA6B,CACrE;QAED;QACA,MAAMK,OAAO,GAAGJ,IAAI,CAACK,UAAU,CAAC,GAAG,CAAC,GAAGL,IAAI,CAACM,SAAS,CAAC,CAAC,CAAC,GAAGN,IAAI;QAC/D,IAAII,OAAO,KAAKJ,IAAI,EAAE;UACpBxC,UAAU,CAAC,MAAK;YACd4B,KAAK,CAACmB,GAAG,GAAGH,OAAO;YACnBhB,KAAK,CAACoB,IAAI,EAAE;UACd,CAAC,EAAE,GAAG,CAAC;;MAEX,CAAC,CAAC;MAEFpB,KAAK,CAACc,gBAAgB,CAAC,OAAO,EAAE,MAAK;QACnC,IAAI,CAAChD,SAAS,CAAC6C,IAAI,CAAC,GAAG,KAAK;QAC5BnC,OAAO,CAACC,GAAG,CAAC,0BAA0BkC,IAAI,QAAQ,CAAC;MACrD,CAAC,CAAC;MAEF;MACAX,KAAK,CAACmB,GAAG,GAAGP,IAAI;MAChBZ,KAAK,CAACoB,IAAI,EAAE;MAEZ,IAAI,CAACvD,MAAM,CAAC8C,IAAI,CAAC,GAAGX,KAAK;MACzB,IAAI,CAAClC,SAAS,CAAC6C,IAAI,CAAC,GAAG,KAAK;MAE5BnC,OAAO,CAACC,GAAG,CAAC,kCAAkCkC,IAAI,SAASC,IAAI,EAAE,CAAC;KACnE,CAAC,OAAO3B,KAAK,EAAE;MACdT,OAAO,CAACS,KAAK,CACX,oDAAoD0B,IAAI,GAAG,EAC3D1B,KAAK,CACN;;EAEL;EAEA;;;EAGQoC,IAAIA,CAACV,IAAY,EAAEtB,IAAA,GAAgB,KAAK;IAC9C,IAAI,IAAI,CAACtB,KAAK,EAAE;MACdS,OAAO,CAACC,GAAG,CAAC,0BAA0BkC,IAAI,QAAQ,CAAC;MACnD;;IAGF,IAAI;MACF,IAAIW,KAAK,GAAG,IAAI,CAACzD,MAAM,CAAC8C,IAAI,CAAC;MAE7B;MACA,IAAI,CAACW,KAAK,IAAIA,KAAK,CAACrC,KAAK,EAAE;QACzB,MAAMsC,aAAa,GAAG,GAAGZ,IAAI,YAAY;QACzCW,KAAK,GAAG,IAAI,CAACzD,MAAM,CAAC0D,aAAa,CAAC;QAClC,IAAID,KAAK,EAAE;UACT9C,OAAO,CAACC,GAAG,CAAC,8CAA8CkC,IAAI,EAAE,CAAC;;;MAIrE,IAAI,CAACW,KAAK,EAAE;QACV9C,OAAO,CAACU,IAAI,CACV,0BAA0ByB,IAAI,6CAA6C,CAC5E;QACD;QACA,IAAI,CAACa,cAAc,CACjBb,IAAI,KAAK,UAAU,GAAG,GAAG,GAAGA,IAAI,KAAK,gBAAgB,GAAG,IAAI,GAAG,GAAG,CACnE;QACD;;MAGFW,KAAK,CAACjC,IAAI,GAAGA,IAAI;MACjBiC,KAAK,CAACpB,MAAM,GAAG,GAAG;MAElB,IAAI,CAAC,IAAI,CAACpC,SAAS,CAAC6C,IAAI,CAAC,EAAE;QACzBnC,OAAO,CAACC,GAAG,CAAC,mCAAmCkC,IAAI,WAAWtB,IAAI,GAAG,CAAC;QAEtE;QACA,IAAKiC,KAAa,CAACnB,aAAa,EAAE;UAC/BmB,KAAa,CACXnB,aAAa,EAAE,CACfsB,IAAI,CAAC,MAAK;YACTjD,OAAO,CAACC,GAAG,CACT,mCAAmCkC,IAAI,uBAAuB,CAC/D;YACD,IAAI,CAAC7C,SAAS,CAAC6C,IAAI,CAAC,GAAG,IAAI;YAE3B;YACA,IAAItB,IAAI,EAAE;cACRjB,UAAU,CAAC,MAAK;gBACd,IAAI,IAAI,CAACN,SAAS,CAAC6C,IAAI,CAAC,EAAE;kBACxB,IAAI,CAACU,IAAI,CAACV,IAAI,EAAEtB,IAAI,CAAC;;cAEzB,CAAC,EAAE,IAAI,CAAC;aACT,MAAM;cACLjB,UAAU,CAAC,MAAK;gBACd,IAAI,CAACN,SAAS,CAAC6C,IAAI,CAAC,GAAG,KAAK;cAC9B,CAAC,EAAE,GAAG,CAAC;;UAEX,CAAC,CAAC,CACDe,KAAK,CAAEzC,KAAU,IAAI;YACpBT,OAAO,CAACS,KAAK,CACX,iDAAiD0B,IAAI,GAAG,EACxD1B,KAAK,CACN;UACH,CAAC,CAAC;SACL,MAAM;UACL;UACAqC,KAAK,CAACK,WAAW,GAAG,CAAC;UACrBL,KAAK,CACFD,IAAI,EAAE,CACNI,IAAI,CAAC,MAAK;YACTjD,OAAO,CAACC,GAAG,CACT,yBAAyBkC,IAAI,uBAAuB,CACrD;YACD,IAAI,CAAC7C,SAAS,CAAC6C,IAAI,CAAC,GAAG,IAAI;UAC7B,CAAC,CAAC,CACDe,KAAK,CAAEzC,KAAK,IAAI;YACfT,OAAO,CAACS,KAAK,CACX,uCAAuC0B,IAAI,GAAG,EAC9C1B,KAAK,CACN;YAED;YACA,MAAMsC,aAAa,GAAG,GAAGZ,IAAI,YAAY;YACzC,MAAMiB,cAAc,GAAG,IAAI,CAAC/D,MAAM,CAAC0D,aAAa,CAAC;YACjD,IAAIK,cAAc,IAAKA,cAAsB,CAACzB,aAAa,EAAE;cAC3D3B,OAAO,CAACC,GAAG,CACT,wDAAwDkC,IAAI,EAAE,CAC/D;cACD,IAAI,CAACU,IAAI,CAACV,IAAI,EAAEtB,IAAI,CAAC;aACtB,MAAM;cACL;cACA,IAAI,CAACmC,cAAc,CACjBb,IAAI,KAAK,UAAU,GACf,GAAG,GACHA,IAAI,KAAK,gBAAgB,GACzB,IAAI,GACJ,GAAG,CACR;;UAEL,CAAC,CAAC;;OAEP,MAAM;QACLnC,OAAO,CAACC,GAAG,CAAC,0BAA0BkC,IAAI,kBAAkB,CAAC;;KAEhE,CAAC,OAAO1B,KAAK,EAAE;MACdT,OAAO,CAACS,KAAK,CACX,4CAA4C0B,IAAI,GAAG,EACnD1B,KAAK,CACN;MACD;MACA,IAAI,CAACuC,cAAc,CACjBb,IAAI,KAAK,UAAU,GAAG,GAAG,GAAGA,IAAI,KAAK,gBAAgB,GAAG,IAAI,GAAG,GAAG,CACnE;;EAEL;EAEA;;;EAGQa,cAAcA,CAACrC,SAAiB;IACtC,IAAI;MACF,MAAMP,YAAY,GAAG,KAAKC,MAAM,CAACC,YAAY,IAC1CD,MAAc,CAACE,kBAAkB,EAAC,CAAE;MACvC,MAAM8C,UAAU,GAAGjD,YAAY,CAACkD,gBAAgB,EAAE;MAClD,MAAMC,QAAQ,GAAGnD,YAAY,CAACoD,UAAU,EAAE;MAE1CH,UAAU,CAACvB,OAAO,CAACyB,QAAQ,CAAC;MAC5BA,QAAQ,CAACzB,OAAO,CAAC1B,YAAY,CAAC2B,WAAW,CAAC;MAE1CsB,UAAU,CAAC1C,SAAS,CAACZ,KAAK,GAAGY,SAAS;MACtC0C,UAAU,CAACI,IAAI,GAAG,MAAM;MAExBF,QAAQ,CAACG,IAAI,CAACC,cAAc,CAAC,GAAG,EAAEvD,YAAY,CAAC+C,WAAW,CAAC;MAC3DI,QAAQ,CAACG,IAAI,CAACE,4BAA4B,CACxC,IAAI,EACJxD,YAAY,CAAC+C,WAAW,GAAG,GAAG,CAC/B;MAEDE,UAAU,CAACrB,KAAK,CAAC5B,YAAY,CAAC+C,WAAW,CAAC;MAC1CE,UAAU,CAACQ,IAAI,CAACzD,YAAY,CAAC+C,WAAW,GAAG,GAAG,CAAC;MAE/CnD,OAAO,CAACC,GAAG,CAAC,2CAA2CU,SAAS,IAAI,CAAC;KACtE,CAAC,OAAOF,KAAK,EAAE;MACdT,OAAO,CAACS,KAAK,CAAC,6CAA6C,EAAEA,KAAK,CAAC;;EAEvE;EAEA;;;EAGQoD,IAAIA,CAAC1B,IAAY;IACvB,IAAI;MACF,MAAMW,KAAK,GAAG,IAAI,CAACzD,MAAM,CAAC8C,IAAI,CAAC;MAC/B,IAAI,CAACW,KAAK,EAAE;QACV9C,OAAO,CAACU,IAAI,CAAC,0BAA0ByB,IAAI,yBAAyB,CAAC;QACrE;;MAGF,IAAI,IAAI,CAAC7C,SAAS,CAAC6C,IAAI,CAAC,EAAE;QACxBnC,OAAO,CAACC,GAAG,CAAC,oCAAoCkC,IAAI,EAAE,CAAC;QACvDW,KAAK,CAACgB,KAAK,EAAE;QACbhB,KAAK,CAACK,WAAW,GAAG,CAAC;QACrB,IAAI,CAAC7D,SAAS,CAAC6C,IAAI,CAAC,GAAG,KAAK;OAC7B,MAAM;QACLnC,OAAO,CAACC,GAAG,CAAC,0BAA0BkC,IAAI,kBAAkB,CAAC;;KAEhE,CAAC,OAAO1B,KAAK,EAAE;MACdT,OAAO,CAACS,KAAK,CAAC,wCAAwC0B,IAAI,GAAG,EAAE1B,KAAK,CAAC;;EAEzE;EAEA;;;EAGQZ,wBAAwBA,CAAA;IAC9BG,OAAO,CAACC,GAAG,CAAC,2DAA2D,CAAC;IAExE,IAAI;MACF,IAAI,CAACnB,MAAM,CACRiF,SAAS,CAAiC;QACzCC,KAAK,EAAEtF,0BAA0B;QACjCuF,WAAW,EAAE,KAAK,CAAE;OACrB,CAAC,CACDF,SAAS,CAAC;QACTG,IAAI,EAAEA,CAAC;UAAEC,IAAI;UAAEC;QAAM,CAAE,KAAI;UACzB,IAAIA,MAAM,EAAE;YACVpE,OAAO,CAACU,IAAI,CACV,kDAAkD,EAClD0D,MAAM,CACP;;UAGH,IAAID,IAAI,EAAElF,YAAY,EAAE;YACtBe,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAE;cACtDoE,MAAM,EAAEF,IAAI,CAAClF,YAAY,CAACqF,EAAE;cAC5BC,QAAQ,EAAEJ,IAAI,CAAClF,YAAY,CAACwE,IAAI;cAChCe,MAAM,EAAEL,IAAI,CAAClF,YAAY,CAACuF,MAAM,EAAEC,QAAQ;cAC1CC,cAAc,EAAEP,IAAI,CAAClF,YAAY,CAACyF;aACnC,CAAC;YACF,IAAI,CAACC,kBAAkB,CAACR,IAAI,CAAClF,YAAY,CAAC;;QAE9C,CAAC;QACDwB,KAAK,EAAGA,KAAK,IAAI;UACfT,OAAO,CAACS,KAAK,CACX,sDAAsD,EACtDA,KAAK,CACN;UAED;UACAb,UAAU,CAAC,MAAK;YACdI,OAAO,CAACC,GAAG,CACT,yDAAyD,CAC1D;YACD,IAAI,CAACJ,wBAAwB,EAAE;UACjC,CAAC,EAAE,IAAI,CAAC;QACV,CAAC;QACD+E,QAAQ,EAAEA,CAAA,KAAK;UACb5E,OAAO,CAACC,GAAG,CACT,uDAAuD,CACxD;UACD;UACAL,UAAU,CAAC,MAAK;YACdI,OAAO,CAACC,GAAG,CACT,8DAA8D,CAC/D;YACD,IAAI,CAACJ,wBAAwB,EAAE;UACjC,CAAC,EAAE,IAAI,CAAC;QACV;OACD,CAAC;KACL,CAAC,OAAOY,KAAK,EAAE;MACdT,OAAO,CAACS,KAAK,CAAC,gDAAgD,EAAEA,KAAK,CAAC;MACtE;MACAb,UAAU,CAAC,MAAK;QACd,IAAI,CAACC,wBAAwB,EAAE;MACjC,CAAC,EAAE,IAAI,CAAC;;EAEZ;EAEA;;;EAGOgF,wBAAwBA,CAAA;IAC7B7E,OAAO,CAACC,GAAG,CAAC,0DAA0D,CAAC;IACvE,IAAI,CAACJ,wBAAwB,EAAE;IAC/B,IAAI,CAACC,4BAA4B,EAAE;EACrC;EAEA;;;EAGOgF,UAAUA,CAAA;IACf9E,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;IAEjD;IACAD,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;IACrC,IAAI,CAAC4C,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC;IAE3BjD,UAAU,CAAC,MAAK;MACd,IAAI,CAACiE,IAAI,CAAC,UAAU,CAAC;MACrB7D,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;MAC3C,IAAI,CAAC4C,IAAI,CAAC,gBAAgB,CAAC;IAC7B,CAAC,EAAE,IAAI,CAAC;IAERjD,UAAU,CAAC,MAAK;MACdI,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;MACrC,IAAI,CAAC4C,IAAI,CAAC,UAAU,CAAC;IACvB,CAAC,EAAE,IAAI,CAAC;EACV;EAEA;;;EAGQ/C,4BAA4BA,CAAA;IAClCE,OAAO,CAACC,GAAG,CACT,gEAAgE,CACjE;IAED,IAAI;MACF,IAAI,CAACnB,MAAM,CACRiF,SAAS,CAA8B;QACtCC,KAAK,EAAErF,gCAAgC;QACvCsF,WAAW,EAAE;OACd,CAAC,CACDF,SAAS,CAAC;QACTG,IAAI,EAAEA,CAAC;UAAEC,IAAI;UAAEC;QAAM,CAAE,KAAI;UACzB,IAAIA,MAAM,EAAE;YACVpE,OAAO,CAACU,IAAI,CACV,8DAA8D,EAC9D0D,MAAM,CACP;;UAGH,IAAID,IAAI,EAAEY,iBAAiB,EAAE;YAC3B/E,OAAO,CAACC,GAAG,CACT,uCAAuC,EACvCkE,IAAI,CAACY,iBAAiB,CACvB;YACD,IAAI,CAACC,sBAAsB,CAACb,IAAI,CAACY,iBAAiB,CAAC;;QAEvD,CAAC;QACDtE,KAAK,EAAGA,KAAK,IAAI;UACfT,OAAO,CAACS,KAAK,CACX,oDAAoD,EACpDA,KAAK,CACN;UACD;UACAb,UAAU,CAAC,MAAK;YACd,IAAI,CAACE,4BAA4B,EAAE;UACrC,CAAC,EAAE,IAAI,CAAC;QACV,CAAC;QACD8E,QAAQ,EAAEA,CAAA,KAAK;UACb5E,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;UAClE;UACAL,UAAU,CAAC,MAAK;YACd,IAAI,CAACE,4BAA4B,EAAE;UACrC,CAAC,EAAE,IAAI,CAAC;QACV;OACD,CAAC;KACL,CAAC,OAAOW,KAAK,EAAE;MACdT,OAAO,CAACS,KAAK,CACX,4DAA4D,EAC5DA,KAAK,CACN;MACDb,UAAU,CAAC,MAAK;QACd,IAAI,CAACE,4BAA4B,EAAE;MACrC,CAAC,EAAE,IAAI,CAAC;;EAEZ;EAEA;;;EAGQ6E,kBAAkBA,CAACM,IAAkB;IAC3CjF,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAE;MACtDoE,MAAM,EAAEY,IAAI,CAACX,EAAE;MACfC,QAAQ,EAAEU,IAAI,CAACxB,IAAI;MACnBe,MAAM,EAAES,IAAI,CAACT,MAAM,EAAEC,QAAQ;MAC7BC,cAAc,EAAEO,IAAI,CAACP;KACtB,CAAC;IAEF,IAAI,CAACzF,YAAY,CAACiF,IAAI,CAACe,IAAI,CAAC;IAC5B,IAAI,CAACpC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC;IAE3B7C,OAAO,CAACC,GAAG,CACT,iEAAiE,CAClE;EACH;EAEA;;;EAGQ+E,sBAAsBA,CAACC,IAAU;IACvCjF,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEgF,IAAI,CAACC,MAAM,CAAC;IAEjE,QAAQD,IAAI,CAACC,MAAM;MACjB,KAAK9G,UAAU,CAAC+G,QAAQ;QACtBnF,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;QAChD,IAAI,CAAC4D,IAAI,CAAC,UAAU,CAAC;QACrB,IAAI,CAAChB,IAAI,CAAC,UAAU,CAAC;QACrB,IAAI,CAAC7D,UAAU,CAACkF,IAAI,CAAC,IAAI,CAAC;QAC1B,IAAI,CAACjF,YAAY,CAACiF,IAAI,CAAC,IAAI,CAAC;QAC5B;MAEF,KAAK9F,UAAU,CAACgH,KAAK;QACnBpF,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;QAC1C,IAAI,CAACoF,aAAa,EAAE;QACpB,IAAI,CAACxC,IAAI,CAAC,UAAU,CAAC;QACrB,IAAI,CAAC7D,UAAU,CAACkF,IAAI,CAAC,IAAI,CAAC;QAC1B,IAAI,CAACjF,YAAY,CAACiF,IAAI,CAAC,IAAI,CAAC;QAC5B;MAEF,KAAK9F,UAAU,CAACkH,SAAS;QACvBtF,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;QAC7C,IAAI,CAAC4D,IAAI,CAAC,UAAU,CAAC;QACrB,IAAI,CAAChB,IAAI,CAAC,gBAAgB,CAAC;QAC3B,IAAI,CAAC7D,UAAU,CAACkF,IAAI,CAACe,IAAI,CAAC;QAC1B,IAAI,CAAChG,YAAY,CAACiF,IAAI,CAAC,IAAI,CAAC;QAC5B;MAEF,KAAK9F,UAAU,CAACmH,OAAO;QACrBvF,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;QAC/C,IAAI,CAAC4C,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC;QAC3B;MAEF;QACE7C,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEgF,IAAI,CAACC,MAAM,CAAC;QACjE;;EAEN;EAEA;;;EAGAM,YAAYA,CACVC,WAAmB,EACnBlB,QAAkB,EAClBG,cAAuB;IAEvB1E,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAE;MAC/CwF,WAAW;MACXlB,QAAQ;MACRG;KACD,CAAC;IAEF,IAAI,CAACe,WAAW,EAAE;MAChB,MAAMhF,KAAK,GAAG,IAAIiF,KAAK,CAAC,0BAA0B,CAAC;MACnD1F,OAAO,CAACS,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3D,OAAO1C,UAAU,CAAC,MAAM0C,KAAK,CAAC;;IAGhC;IACA,MAAM4D,MAAM,GAAG,QAAQsB,IAAI,CAACC,GAAG,EAAE,IAAIvE,IAAI,CAACwE,MAAM,EAAE,CAC/CC,QAAQ,CAAC,EAAE,CAAC,CACZC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;IAEjB;IACA,MAAMC,KAAK,GAAGC,IAAI,CAACC,SAAS,CAAC;MAC3BzC,IAAI,EAAE,OAAO;MACb0C,GAAG,EAAE;KACN,CAAC;IAEF,MAAMC,SAAS,GAAG;MAChBX,WAAW;MACXlB,QAAQ,EAAEA,QAAQ;MAClBF,MAAM;MACN2B,KAAK;MACLtB;KACD;IAED1E,OAAO,CAACC,GAAG,CAAC,kDAAkD,EAAEmG,SAAS,CAAC;IAE1E,OAAO,IAAI,CAACtH,MAAM,CACfuH,MAAM,CAAyB;MAC9BC,QAAQ,EAAEjI,sBAAsB;MAChC+H;KACD,CAAC,CACDG,IAAI,CACHtI,GAAG,CAAEuI,MAAM,IAAI;MACbxG,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAEuG,MAAM,CAAC;MAEnE,IAAI,CAACA,MAAM,CAACrC,IAAI,EAAEqB,YAAY,EAAE;QAC9B,MAAM,IAAIE,KAAK,CAAC,mCAAmC,CAAC;;MAGtD,MAAMT,IAAI,GAAGuB,MAAM,CAACrC,IAAI,CAACqB,YAAY;MACrCxF,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE;QAC5CqE,EAAE,EAAEW,IAAI,CAACX,EAAE;QACXb,IAAI,EAAEwB,IAAI,CAACxB,IAAI;QACfyB,MAAM,EAAED,IAAI,CAACC,MAAM;QACnBV,MAAM,EAAES,IAAI,CAACT,MAAM,EAAEC,QAAQ;QAC7BgC,SAAS,EAAExB,IAAI,CAACwB,SAAS,EAAEhC;OAC5B,CAAC;MAEF;MACA,IAAI,CAACzF,UAAU,CAACkF,IAAI,CAACe,IAAI,CAAC;MAE1B,OAAOA,IAAI;IACb,CAAC,CAAC,EACF/G,UAAU,CAAEuC,KAAK,IAAI;MACnBT,OAAO,CAACS,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3D,IAAI,CAAC1B,MAAM,CAAC0B,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAElD,IAAIiG,YAAY,GAAG,wCAAwC;MAC3D,IAAIjG,KAAK,CAACkG,YAAY,EAAE;QACtBD,YAAY,GAAG,4BAA4B;OAC5C,MAAM,IAAIjG,KAAK,CAACmG,aAAa,EAAEC,MAAM,GAAG,CAAC,EAAE;QAC1CH,YAAY,GAAGjG,KAAK,CAACmG,aAAa,CAAC,CAAC,CAAC,CAACE,OAAO,IAAIJ,YAAY;;MAG/D,OAAO3I,UAAU,CAAC,MAAM,IAAI2H,KAAK,CAACgB,YAAY,CAAC,CAAC;IAClD,CAAC,CAAC,CACH;EACL;EAEA;;;EAGAK,UAAUA,CAAC9H,YAA0B;IACnCe,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEhB,YAAY,CAACqF,EAAE,CAAC;IAEhE;IACA,MAAM0C,MAAM,GAAGf,IAAI,CAACC,SAAS,CAAC;MAC5BzC,IAAI,EAAE,QAAQ;MACd0C,GAAG,EAAE;KACN,CAAC;IAEF,OAAO,IAAI,CAACrH,MAAM,CACfuH,MAAM,CAAuB;MAC5BC,QAAQ,EAAEhI,oBAAoB;MAC9B8H,SAAS,EAAE;QACT/B,MAAM,EAAEpF,YAAY,CAACqF,EAAE;QACvB0C;;KAEH,CAAC,CACDT,IAAI,CACHpI,SAAS,CAAEqI,MAAM,IAAI;MACnBxG,OAAO,CAACC,GAAG,CAAC,6CAA6C,EAAEuG,MAAM,CAAC;MAElE,IAAI,CAACA,MAAM,CAACrC,IAAI,EAAE4C,UAAU,EAAE;QAC5B,MAAM,IAAIrB,KAAK,CAAC,mCAAmC,CAAC;;MAGtD,MAAMT,IAAI,GAAGuB,MAAM,CAACrC,IAAI,CAAC4C,UAAU;MAEnC;MACA,IAAI,CAAClD,IAAI,CAAC,UAAU,CAAC;MACrB,IAAI,CAAChB,IAAI,CAAC,gBAAgB,CAAC;MAE3B;MACA,OAAO7E,IAAI,CAAC,IAAI,CAACiJ,iBAAiB,CAAChI,YAAY,EAAEgG,IAAI,CAAC,CAAC;IACzD,CAAC,CAAC,EACF/G,UAAU,CAAEuC,KAAK,IAAI;MACnBT,OAAO,CAACS,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzD,IAAI,CAAC1B,MAAM,CAAC0B,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MACjD,OAAO1C,UAAU,CACf,MAAM,IAAI2H,KAAK,CAAC,yCAAyC,CAAC,CAC3D;IACH,CAAC,CAAC,CACH;EACL;EAEA;;;EAGAwB,UAAUA,CAAC7C,MAAc,EAAE8C,MAAe;IACxCnH,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEoE,MAAM,EAAE8C,MAAM,CAAC;IAE/D,OAAO,IAAI,CAACrI,MAAM,CACfuH,MAAM,CAA8B;MACnCC,QAAQ,EAAE/H,oBAAoB;MAC9B6H,SAAS,EAAE;QACT/B,MAAM;QACN8C,MAAM,EAAEA,MAAM,IAAI;;KAErB,CAAC,CACDZ,IAAI,CACHtI,GAAG,CAAEuI,MAAM,IAAI;MACbxG,OAAO,CAACC,GAAG,CAAC,6CAA6C,EAAEuG,MAAM,CAAC;MAElE,IAAI,CAACA,MAAM,CAACrC,IAAI,EAAE+C,UAAU,EAAE;QAC5B,MAAM,IAAIxB,KAAK,CAAC,kCAAkC,CAAC;;MAGrD;MACA,IAAI,CAACzG,YAAY,CAACiF,IAAI,CAAC,IAAI,CAAC;MAC5B,IAAI,CAAClF,UAAU,CAACkF,IAAI,CAAC,IAAI,CAAC;MAE1B;MACA,IAAI,CAACL,IAAI,CAAC,UAAU,CAAC;MAErB,OAAO2C,MAAM,CAACrC,IAAI,CAAC+C,UAAU;IAC/B,CAAC,CAAC,EACFhJ,UAAU,CAAEuC,KAAK,IAAI;MACnBT,OAAO,CAACS,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzD,IAAI,CAAC1B,MAAM,CAAC0B,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MACjD,OAAO1C,UAAU,CAAC,MAAM,IAAI2H,KAAK,CAAC,iCAAiC,CAAC,CAAC;IACvE,CAAC,CAAC,CACH;EACL;EAEA;;;EAGA0B,OAAOA,CAAC/C,MAAc;IACpBrE,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEoE,MAAM,CAAC;IAEpD,OAAO,IAAI,CAACvF,MAAM,CACfuH,MAAM,CAA2B;MAChCC,QAAQ,EAAE9H,iBAAiB;MAC3B4H,SAAS,EAAE;QACT/B,MAAM;QACNgD,QAAQ,EAAE,IAAI,CAAE;;KAEnB,CAAC,CACDd,IAAI,CACHtI,GAAG,CAAEuI,MAAM,IAAI;MACbxG,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAEuG,MAAM,CAAC;MAE/D,IAAI,CAACA,MAAM,CAACrC,IAAI,EAAEiD,OAAO,EAAE;QACzB,MAAM,IAAI1B,KAAK,CAAC,kCAAkC,CAAC;;MAGrD;MACA,IAAI,CAAC1G,UAAU,CAACkF,IAAI,CAAC,IAAI,CAAC;MAC1B,IAAI,CAACjF,YAAY,CAACiF,IAAI,CAAC,IAAI,CAAC;MAE5B;MACA,IAAI,CAACmB,aAAa,EAAE;MACpB,IAAI,CAACxC,IAAI,CAAC,UAAU,CAAC;MAErB,OAAO2D,MAAM,CAACrC,IAAI,CAACiD,OAAO;IAC5B,CAAC,CAAC,EACFlJ,UAAU,CAAEuC,KAAK,IAAI;MACnBT,OAAO,CAACS,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD,IAAI,CAAC1B,MAAM,CAAC0B,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC9C,OAAO1C,UAAU,CACf,MAAM,IAAI2H,KAAK,CAAC,kCAAkC,CAAC,CACpD;IACH,CAAC,CAAC,CACH;EACL;EAEA;;;EAGA4B,WAAWA,CACTjD,MAAc,EACdkD,WAAqB,EACrBC,WAAqB;IAErBxH,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE;MAC9CoE,MAAM;MACNkD,WAAW;MACXC;KACD,CAAC;IAEF,OAAO,IAAI,CAAC1I,MAAM,CACfuH,MAAM,CAAmC;MACxCC,QAAQ,EAAE7H,0BAA0B;MACpC2H,SAAS,EAAE;QACT/B,MAAM;QACNoD,KAAK,EAAEF,WAAW;QAClB/F,KAAK,EAAEgG;;KAEV,CAAC,CACDjB,IAAI,CACHtI,GAAG,CAAEuI,MAAM,IAAI;MACbxG,OAAO,CAACC,GAAG,CAAC,6CAA6C,EAAEuG,MAAM,CAAC;MAElE,IAAI,CAACA,MAAM,CAACrC,IAAI,EAAEuD,eAAe,EAAE;QACjC,MAAM,IAAIhC,KAAK,CAAC,kCAAkC,CAAC;;MAGrD,OAAOc,MAAM,CAACrC,IAAI,CAACuD,eAAe;IACpC,CAAC,CAAC,EACFxJ,UAAU,CAAEuC,KAAK,IAAI;MACnBT,OAAO,CAACS,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;MAC1D,IAAI,CAAC1B,MAAM,CAAC0B,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MACjD,OAAO1C,UAAU,CACf,MAAM,IAAI2H,KAAK,CAAC,sCAAsC,CAAC,CACxD;IACH,CAAC,CAAC,CACH;EACL;EAEA;;;EAGcuB,iBAAiBA,CAC7BhI,YAA0B,EAC1BgG,IAAU;IAAA,IAAA0C,KAAA;IAAA,OAAAC,iBAAA;MAEV5H,OAAO,CAACC,GAAG,CAAC,4DAA4D,CAAC;MAEzE;MACA0H,KAAI,CAAC9E,IAAI,CAAC,gBAAgB,CAAC;MAE3B;MACA8E,KAAI,CAAC3I,UAAU,CAACkF,IAAI,CAACe,IAAI,CAAC;MAC1B0C,KAAI,CAAC1I,YAAY,CAACiF,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;MAE9B,OAAOe,IAAI;IAAC;EACd;EAEA;;;EAGA4C,WAAWA,CAAA;IACT,IAAI,CAACrI,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;IAC1CQ,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE,IAAI,CAACT,cAAc,CAAC;IACnE,OAAO,IAAI,CAACA,cAAc;EAC5B;EAEA;;;EAGAsI,WAAWA,CAAA;IACT,IAAI,CAACrI,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;IAC1CO,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE,IAAI,CAACR,cAAc,CAAC;IACnE,OAAO,IAAI,CAACA,cAAc;EAC5B;EAEA;;;EAGAsI,eAAeA,CAAA;IACb,OAAO,IAAI,CAACvI,cAAc;EAC5B;EAEA;;;EAGAwI,eAAeA,CAAA;IACb,OAAO,IAAI,CAACvI,cAAc;EAC5B;EAEA;;;EAGQ4F,aAAaA,CAAA;IACnBrF,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;IACnDgI,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC7I,MAAM,CAAC,CAAC8I,OAAO,CAAEhG,IAAI,IAAI;MACxC,IAAI,CAAC0B,IAAI,CAAC1B,IAAI,CAAC;IACjB,CAAC,CAAC;EACJ;EAEA;;;EAGOiG,YAAYA,CAAA;IACjBpI,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC;IACtEgI,MAAM,CAACI,MAAM,CAAC,IAAI,CAAChJ,MAAM,CAAC,CAAC8I,OAAO,CAAErF,KAAK,IAAI;MAC3C,IAAIA,KAAK,EAAE;QACTA,KAAK,CAACvD,KAAK,GAAG,KAAK;QACnBuD,KAAK,CAACpB,MAAM,GAAG,GAAG;QAClB;QACAoB,KAAK,CACFD,IAAI,EAAE,CACNI,IAAI,CAAC,MAAK;UACTH,KAAK,CAACgB,KAAK,EAAE;UACbhB,KAAK,CAACK,WAAW,GAAG,CAAC;QACvB,CAAC,CAAC,CACDD,KAAK,CAAC,MAAK;UACV;QAAA,CACD,CAAC;;IAER,CAAC,CAAC;EACJ;EAEAoF,WAAWA,CAAA;IACT,IAAI,CAACjD,aAAa,EAAE;EACtB;;;uBAh6BWzG,WAAW,EAAA2J,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,aAAA;IAAA;EAAA;;;aAAXhK,WAAW;MAAAiK,OAAA,EAAXjK,WAAW,CAAAkK,IAAA;MAAAC,UAAA,EAFV;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}