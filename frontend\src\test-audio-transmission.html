<!DOCTYPE html>
<html>
<head>
    <title>Test Transmission Audio WebRTC</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #1a1a1a;
            color: white;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .test-section {
            background: #333;
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            border: 2px solid #00ff88;
        }
        button {
            background: #00ff88;
            color: #1a1a1a;
            border: none;
            padding: 15px 25px;
            margin: 10px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
            font-size: 16px;
        }
        button:hover {
            background: #00cc66;
        }
        button:disabled {
            background: #666;
            cursor: not-allowed;
        }
        .status {
            background: #444;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
            font-size: 14px;
        }
        .success {
            background: #00cc66;
            color: white;
        }
        .error {
            background: #cc0000;
            color: white;
        }
        .audio-level {
            height: 20px;
            background: #333;
            border-radius: 10px;
            margin: 10px 0;
            overflow: hidden;
        }
        .audio-bar {
            height: 100%;
            background: linear-gradient(90deg, #00ff88, #ffff00, #ff0000);
            width: 0%;
            transition: width 0.1s;
        }
        .peer-section {
            display: flex;
            gap: 20px;
        }
        .peer {
            flex: 1;
            background: #444;
            padding: 15px;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔊 Test Transmission Audio WebRTC</h1>
        
        <div class="test-section">
            <h2>📋 Test de transmission audio entre deux PeerConnections</h2>
            <div id="mainStatus" class="status">
                Prêt à tester la transmission audio WebRTC
            </div>
            
            <div>
                <button onclick="startTransmissionTest()" id="startBtn">🚀 Démarrer Test Transmission</button>
                <button onclick="stopTransmissionTest()" id="stopBtn" disabled>⏹️ Arrêter Test</button>
            </div>
        </div>

        <div class="test-section">
            <h2>🎤 Niveaux Audio</h2>
            <div>
                <h4>Microphone Local:</h4>
                <div class="audio-level">
                    <div id="localAudioBar" class="audio-bar"></div>
                </div>
                <span id="localLevel">0%</span>
            </div>
            
            <div>
                <h4>Audio Reçu (WebRTC):</h4>
                <div class="audio-level">
                    <div id="remoteAudioBar" class="audio-bar"></div>
                </div>
                <span id="remoteLevel">0%</span>
            </div>
        </div>

        <div class="test-section">
            <h2>🔗 État des PeerConnections</h2>
            <div class="peer-section">
                <div class="peer">
                    <h4>👤 Peer 1 (Émetteur)</h4>
                    <div id="peer1Status">Non connecté</div>
                    <div id="peer1Ice">ICE: Nouveau</div>
                </div>
                <div class="peer">
                    <h4>👥 Peer 2 (Récepteur)</h4>
                    <div id="peer2Status">Non connecté</div>
                    <div id="peer2Ice">ICE: Nouveau</div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>📊 Statistiques WebRTC</h2>
            <div id="stats">
                <div>Packets envoyés: <span id="packetsSent">0</span></div>
                <div>Packets reçus: <span id="packetsReceived">0</span></div>
                <div>Bytes envoyés: <span id="bytesSent">0</span></div>
                <div>Bytes reçus: <span id="bytesReceived">0</span></div>
                <div>Qualité audio: <span id="audioQuality">-</span></div>
            </div>
        </div>

        <!-- Éléments audio cachés -->
        <audio id="localAudio" autoplay muted style="display: none;"></audio>
        <audio id="remoteAudio" autoplay style="display: none;"></audio>
    </div>

    <script>
        let localStream = null;
        let peerConnection1 = null;
        let peerConnection2 = null;
        let localAudioContext = null;
        let remoteAudioContext = null;
        let localAnalyser = null;
        let remoteAnalyser = null;
        let isTestRunning = false;
        let statsInterval = null;

        function updateStatus(message, type = 'normal') {
            const statusDiv = document.getElementById('mainStatus');
            statusDiv.textContent = message;
            statusDiv.className = 'status';
            if (type === 'error') statusDiv.className += ' error';
            if (type === 'success') statusDiv.className += ' success';
            console.log('🔍 [Test]', message);
        }

        async function startTransmissionTest() {
            try {
                updateStatus('🚀 Démarrage du test de transmission audio...');
                
                // 1. Obtenir le microphone
                localStream = await navigator.mediaDevices.getUserMedia({
                    audio: {
                        echoCancellation: false,
                        noiseSuppression: false,
                        autoGainControl: false
                    },
                    video: false
                });
                
                updateStatus('🎤 Microphone obtenu, configuration WebRTC...');
                
                // 2. Configurer les PeerConnections
                await setupPeerConnections();
                
                // 3. Configurer l'analyse audio
                setupAudioAnalysis();
                
                // 4. Démarrer les statistiques
                startStats();
                
                isTestRunning = true;
                document.getElementById('startBtn').disabled = true;
                document.getElementById('stopBtn').disabled = false;
                
                updateStatus('✅ Test de transmission démarré - Parlez dans votre micro!', 'success');
                
            } catch (error) {
                updateStatus('❌ Erreur: ' + error.message, 'error');
                console.error('❌ [Test] Erreur:', error);
            }
        }

        async function setupPeerConnections() {
            const configuration = {
                iceServers: [
                    { urls: 'stun:stun.l.google.com:19302' },
                    { urls: 'stun:stun1.l.google.com:19302' }
                ]
            };

            // Créer les PeerConnections
            peerConnection1 = new RTCPeerConnection(configuration);
            peerConnection2 = new RTCPeerConnection(configuration);

            // Ajouter le stream local à PC1
            localStream.getTracks().forEach(track => {
                peerConnection1.addTrack(track, localStream);
                console.log('🎵 Track ajouté à PC1:', track.kind, track.enabled);
            });

            // Gérer la réception du stream sur PC2
            peerConnection2.ontrack = (event) => {
                console.log('📺 Stream reçu sur PC2:', event.streams[0]);
                const remoteAudio = document.getElementById('remoteAudio');
                remoteAudio.srcObject = event.streams[0];
                
                // Forcer la lecture
                remoteAudio.play().then(() => {
                    console.log('✅ Audio distant en lecture');
                    setupRemoteAudioAnalysis(event.streams[0]);
                }).catch(err => {
                    console.error('❌ Erreur lecture audio distant:', err);
                });
            };

            // Gérer les états de connexion
            peerConnection1.onconnectionstatechange = () => {
                document.getElementById('peer1Status').textContent = peerConnection1.connectionState;
                console.log('🔗 PC1 état:', peerConnection1.connectionState);
            };

            peerConnection2.onconnectionstatechange = () => {
                document.getElementById('peer2Status').textContent = peerConnection2.connectionState;
                console.log('🔗 PC2 état:', peerConnection2.connectionState);
            };

            // Gérer ICE
            peerConnection1.onicecandidate = (event) => {
                if (event.candidate) {
                    peerConnection2.addIceCandidate(event.candidate);
                }
            };

            peerConnection2.onicecandidate = (event) => {
                if (event.candidate) {
                    peerConnection1.addIceCandidate(event.candidate);
                }
            };

            // Gérer les états ICE
            peerConnection1.onicegatheringstatechange = () => {
                document.getElementById('peer1Ice').textContent = 'ICE: ' + peerConnection1.iceGatheringState;
            };

            peerConnection2.onicegatheringstatechange = () => {
                document.getElementById('peer2Ice').textContent = 'ICE: ' + peerConnection2.iceGatheringState;
            };

            // Négociation
            const offer = await peerConnection1.createOffer();
            await peerConnection1.setLocalDescription(offer);
            await peerConnection2.setRemoteDescription(offer);

            const answer = await peerConnection2.createAnswer();
            await peerConnection2.setLocalDescription(answer);
            await peerConnection1.setRemoteDescription(answer);

            console.log('✅ Négociation WebRTC terminée');
        }

        function setupAudioAnalysis() {
            try {
                // Analyse du microphone local
                localAudioContext = new (window.AudioContext || window.webkitAudioContext)();
                localAnalyser = localAudioContext.createAnalyser();
                const localSource = localAudioContext.createMediaStreamSource(localStream);
                
                localAnalyser.fftSize = 256;
                localSource.connect(localAnalyser);
                
                // Démarrer l'analyse locale
                analyzeLocalAudio();
                
                console.log('✅ Analyse audio locale configurée');
                
            } catch (error) {
                console.error('❌ Erreur configuration analyse audio:', error);
            }
        }

        function setupRemoteAudioAnalysis(remoteStream) {
            try {
                // Analyse de l'audio reçu
                remoteAudioContext = new (window.AudioContext || window.webkitAudioContext)();
                remoteAnalyser = remoteAudioContext.createAnalyser();
                const remoteSource = remoteAudioContext.createMediaStreamSource(remoteStream);
                
                remoteAnalyser.fftSize = 256;
                remoteSource.connect(remoteAnalyser);
                
                // Démarrer l'analyse distante
                analyzeRemoteAudio();
                
                console.log('✅ Analyse audio distante configurée');
                
            } catch (error) {
                console.error('❌ Erreur configuration analyse audio distante:', error);
            }
        }

        function analyzeLocalAudio() {
            if (!localAnalyser || !isTestRunning) return;
            
            const bufferLength = localAnalyser.frequencyBinCount;
            const dataArray = new Uint8Array(bufferLength);
            
            function analyze() {
                if (!isTestRunning) return;
                
                localAnalyser.getByteFrequencyData(dataArray);
                
                let sum = 0;
                for (let i = 0; i < bufferLength; i++) {
                    sum += dataArray[i];
                }
                const average = sum / bufferLength;
                const percentage = Math.round((average / 255) * 100);
                
                document.getElementById('localAudioBar').style.width = percentage + '%';
                document.getElementById('localLevel').textContent = percentage + '%';
                
                requestAnimationFrame(analyze);
            }
            
            analyze();
        }

        function analyzeRemoteAudio() {
            if (!remoteAnalyser || !isTestRunning) return;
            
            const bufferLength = remoteAnalyser.frequencyBinCount;
            const dataArray = new Uint8Array(bufferLength);
            
            function analyze() {
                if (!isTestRunning) return;
                
                remoteAnalyser.getByteFrequencyData(dataArray);
                
                let sum = 0;
                for (let i = 0; i < bufferLength; i++) {
                    sum += dataArray[i];
                }
                const average = sum / bufferLength;
                const percentage = Math.round((average / 255) * 100);
                
                document.getElementById('remoteAudioBar').style.width = percentage + '%';
                document.getElementById('remoteLevel').textContent = percentage + '%';
                
                // Si on détecte de l'audio distant, c'est que la transmission fonctionne!
                if (percentage > 5) {
                    updateStatus('🎉 TRANSMISSION AUDIO DÉTECTÉE! Le son passe via WebRTC!', 'success');
                }
                
                requestAnimationFrame(analyze);
            }
            
            analyze();
        }

        function startStats() {
            statsInterval = setInterval(async () => {
                if (!peerConnection1 || !isTestRunning) return;
                
                try {
                    const stats = await peerConnection1.getStats();
                    
                    stats.forEach(report => {
                        if (report.type === 'outbound-rtp' && report.kind === 'audio') {
                            document.getElementById('packetsSent').textContent = report.packetsSent || 0;
                            document.getElementById('bytesSent').textContent = report.bytesSent || 0;
                        }
                    });
                    
                    const stats2 = await peerConnection2.getStats();
                    
                    stats2.forEach(report => {
                        if (report.type === 'inbound-rtp' && report.kind === 'audio') {
                            document.getElementById('packetsReceived').textContent = report.packetsReceived || 0;
                            document.getElementById('bytesReceived').textContent = report.bytesReceived || 0;
                            
                            // Calculer la qualité
                            const packetsLost = report.packetsLost || 0;
                            const packetsReceived = report.packetsReceived || 0;
                            const total = packetsLost + packetsReceived;
                            const quality = total > 0 ? Math.round(((packetsReceived / total) * 100)) : 0;
                            document.getElementById('audioQuality').textContent = quality + '%';
                        }
                    });
                    
                } catch (error) {
                    console.error('❌ Erreur stats:', error);
                }
            }, 1000);
        }

        function stopTransmissionTest() {
            isTestRunning = false;
            
            if (localStream) {
                localStream.getTracks().forEach(track => track.stop());
                localStream = null;
            }
            
            if (peerConnection1) {
                peerConnection1.close();
                peerConnection1 = null;
            }
            
            if (peerConnection2) {
                peerConnection2.close();
                peerConnection2 = null;
            }
            
            if (localAudioContext) {
                localAudioContext.close();
                localAudioContext = null;
            }
            
            if (remoteAudioContext) {
                remoteAudioContext.close();
                remoteAudioContext = null;
            }
            
            if (statsInterval) {
                clearInterval(statsInterval);
                statsInterval = null;
            }
            
            document.getElementById('startBtn').disabled = false;
            document.getElementById('stopBtn').disabled = true;
            
            // Reset UI
            document.getElementById('localAudioBar').style.width = '0%';
            document.getElementById('remoteAudioBar').style.width = '0%';
            document.getElementById('localLevel').textContent = '0%';
            document.getElementById('remoteLevel').textContent = '0%';
            document.getElementById('peer1Status').textContent = 'Non connecté';
            document.getElementById('peer2Status').textContent = 'Non connecté';
            
            updateStatus('⏹️ Test arrêté');
        }

        // Vérifications initiales
        window.onload = function() {
            if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
                updateStatus('❌ WebRTC non supporté par ce navigateur', 'error');
                return;
            }
            
            if (!window.RTCPeerConnection) {
                updateStatus('❌ RTCPeerConnection non supporté', 'error');
                return;
            }
            
            updateStatus('✅ Navigateur compatible WebRTC - Prêt à tester!');
        };
    </script>
</body>
</html>
