{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/OneDrive/Bureau/Project PI/devBridge/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { BehaviorSubject, throwError, from } from 'rxjs';\nimport { map, catchError, switchMap } from 'rxjs/operators';\nimport { CallType, CallStatus } from '../models/message.model';\nimport { INITIATE_CALL_MUTATION, ACCEPT_CALL_MUTATION, REJECT_CALL_MUTATION, END_CALL_MUTATION, TOGGLE_CALL_MEDIA_MUTATION, INCOMING_CALL_SUBSCRIPTION, CALL_STATUS_CHANGED_SUBSCRIPTION } from '../graphql/message.graphql';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"apollo-angular\";\nimport * as i2 from \"./logger.service\";\nexport class CallService {\n  constructor(apollo, logger) {\n    this.apollo = apollo;\n    this.logger = logger;\n    // État des appels\n    this.activeCall = new BehaviorSubject(null);\n    this.incomingCall = new BehaviorSubject(null);\n    // Observables publics\n    this.activeCall$ = this.activeCall.asObservable();\n    this.incomingCall$ = this.incomingCall.asObservable();\n    // Propriétés pour la gestion des sons\n    this.sounds = {};\n    this.isPlaying = {};\n    this.muted = false;\n    // États simples pour les médias\n    this.isVideoEnabled = true;\n    this.isAudioEnabled = true;\n    // WebRTC\n    this.peerConnection = null;\n    this.localStream = null;\n    this.remoteStream = null;\n    this.localVideoElement = null;\n    this.remoteVideoElement = null;\n    this.preloadSounds();\n    this.initializeSubscriptions();\n    this.initializeWebRTC();\n  }\n  /**\n   * Initialise les subscriptions avec un délai pour s'assurer que l'authentification est prête\n   */\n  initializeSubscriptions() {\n    // Attendre un peu pour s'assurer que l'authentification est prête\n    setTimeout(() => {\n      this.subscribeToIncomingCalls();\n      this.subscribeToCallStatusChanges();\n    }, 1000);\n    // Réessayer UNE SEULE FOIS après 10 secondes si nécessaire\n    setTimeout(() => {\n      if (!this.incomingCall.value) {\n        // console.log('🔄 [CallService] Retrying subscription initialization...');\n        this.subscribeToIncomingCalls();\n        this.subscribeToCallStatusChanges();\n      }\n    }, 10000);\n  }\n  /**\n   * Initialise WebRTC\n   */\n  initializeWebRTC() {\n    console.log('🔧 [CallService] Initializing WebRTC...');\n    // Configuration des serveurs STUN/TURN\n    const configuration = {\n      iceServers: [{\n        urls: 'stun:stun.l.google.com:19302'\n      }, {\n        urls: 'stun:stun1.l.google.com:19302'\n      }]\n    };\n    try {\n      this.peerConnection = new RTCPeerConnection(configuration);\n      this.setupPeerConnectionEvents();\n      console.log('✅ [CallService] WebRTC initialized successfully');\n    } catch (error) {\n      console.error('❌ [CallService] Failed to initialize WebRTC:', error);\n    }\n  }\n  /**\n   * Configure les événements de la PeerConnection\n   */\n  setupPeerConnectionEvents() {\n    if (!this.peerConnection) return;\n    // Quand on reçoit un stream distant\n    this.peerConnection.ontrack = event => {\n      console.log('📺 [CallService] Remote stream received');\n      this.remoteStream = event.streams[0];\n      if (this.remoteVideoElement) {\n        this.remoteVideoElement.srcObject = this.remoteStream;\n        // Forcer la lecture audio immédiatement\n        this.ensureAudioPlayback(this.remoteVideoElement);\n      }\n    };\n    // Gestion des candidats ICE\n    this.peerConnection.onicecandidate = event => {\n      if (event.candidate) {\n        console.log('🧊 [CallService] ICE candidate generated');\n        // TODO: Envoyer le candidat via WebSocket\n      }\n    };\n    // État de la connexion\n    this.peerConnection.onconnectionstatechange = () => {\n      console.log('🔗 [CallService] Connection state:', this.peerConnection?.connectionState);\n    };\n  }\n  /**\n   * Précharge les sons utilisés dans l'application\n   */\n  preloadSounds() {\n    // Créer des sons synthétiques de haute qualité\n    this.createSyntheticSounds();\n    // Charger le son de notification qui existe encore\n    this.loadSound('notification', 'assets/sounds/notification.mp3');\n    console.log('🎵 [CallService] Beautiful synthetic melodies created for calls');\n  }\n  /**\n   * Crée des sons synthétiques comme fallback\n   */\n  createSyntheticSounds() {\n    try {\n      // Créer un contexte audio pour les sons synthétiques\n      const audioContext = new (window.AudioContext || window.webkitAudioContext)();\n      // Son de sonnerie (mélodie agréable)\n      this.sounds['ringtone-synthetic'] = this.createRingtoneSound(audioContext);\n      // Son de connexion (accord agréable)\n      this.sounds['call-connected-synthetic'] = this.createConnectedSound(audioContext);\n      // Son de fin d'appel (ton descendant)\n      this.sounds['call-end-synthetic'] = this.createEndSound(audioContext);\n      // Son de notification (bip agréable)\n      this.sounds['notification-synthetic'] = this.createNotificationSound(audioContext);\n      console.log('🔊 [CallService] Synthetic sounds created as fallback');\n    } catch (error) {\n      console.warn('⚠️ [CallService] Could not create synthetic sounds:', error);\n    }\n  }\n  /**\n   * Crée une sonnerie agréable (mélodie)\n   */\n  createRingtoneSound(audioContext) {\n    const audio = new Audio();\n    audio.volume = 0.5;\n    let isPlaying = false;\n    let timeoutIds = [];\n    audio.playSynthetic = () => {\n      if (isPlaying) return Promise.resolve();\n      isPlaying = true;\n      const playMelody = () => {\n        if (!isPlaying) return;\n        // Mélodie inspirée de Nokia mais plus moderne : Mi-Ré-Fa#-Sol-Do#-Si-Ré-Do\n        const melody = [{\n          freq: 659.25,\n          duration: 0.125\n        }, {\n          freq: 587.33,\n          duration: 0.125\n        }, {\n          freq: 739.99,\n          duration: 0.25\n        }, {\n          freq: 783.99,\n          duration: 0.25\n        }, {\n          freq: 554.37,\n          duration: 0.125\n        }, {\n          freq: 493.88,\n          duration: 0.125\n        }, {\n          freq: 587.33,\n          duration: 0.25\n        }, {\n          freq: 523.25,\n          duration: 0.25\n        } // Do\n        ];\n\n        let currentTime = audioContext.currentTime;\n        melody.forEach((note, index) => {\n          if (!isPlaying) return;\n          const oscillator = audioContext.createOscillator();\n          const gainNode = audioContext.createGain();\n          oscillator.connect(gainNode);\n          gainNode.connect(audioContext.destination);\n          oscillator.frequency.value = note.freq;\n          oscillator.type = 'square'; // Son plus moderne\n          // Enveloppe ADSR pour un son plus naturel\n          gainNode.gain.setValueAtTime(0, currentTime);\n          gainNode.gain.linearRampToValueAtTime(0.3, currentTime + 0.02);\n          gainNode.gain.linearRampToValueAtTime(0.2, currentTime + note.duration * 0.7);\n          gainNode.gain.exponentialRampToValueAtTime(0.01, currentTime + note.duration);\n          oscillator.start(currentTime);\n          oscillator.stop(currentTime + note.duration);\n          currentTime += note.duration + 0.05; // Petite pause entre les notes\n        });\n        // Répéter la mélodie après une pause\n        const timeoutId = setTimeout(() => {\n          if (isPlaying) {\n            playMelody();\n          }\n        }, (currentTime - audioContext.currentTime + 0.8) * 1000);\n        timeoutIds.push(timeoutId);\n      };\n      playMelody();\n      return Promise.resolve();\n    };\n    audio.stopSynthetic = () => {\n      isPlaying = false;\n      timeoutIds.forEach(id => clearTimeout(id));\n      timeoutIds = [];\n    };\n    return audio;\n  }\n  /**\n   * Crée un son de connexion agréable (mélodie ascendante)\n   */\n  createConnectedSound(audioContext) {\n    const audio = new Audio();\n    audio.volume = 0.5;\n    audio.playSynthetic = () => {\n      // Mélodie ascendante positive : Do-Mi-Sol-Do (octave supérieure)\n      const melody = [{\n        freq: 523.25,\n        duration: 0.15\n      }, {\n        freq: 659.25,\n        duration: 0.15\n      }, {\n        freq: 783.99,\n        duration: 0.15\n      }, {\n        freq: 1046.5,\n        duration: 0.4\n      } // Do (octave supérieure)\n      ];\n\n      let currentTime = audioContext.currentTime;\n      melody.forEach((note, index) => {\n        const oscillator = audioContext.createOscillator();\n        const gainNode = audioContext.createGain();\n        oscillator.connect(gainNode);\n        gainNode.connect(audioContext.destination);\n        oscillator.frequency.value = note.freq;\n        oscillator.type = 'triangle'; // Son plus doux\n        // Enveloppe pour un son naturel\n        gainNode.gain.setValueAtTime(0, currentTime);\n        gainNode.gain.linearRampToValueAtTime(0.25, currentTime + 0.02);\n        gainNode.gain.linearRampToValueAtTime(0.15, currentTime + note.duration * 0.8);\n        gainNode.gain.exponentialRampToValueAtTime(0.01, currentTime + note.duration);\n        oscillator.start(currentTime);\n        oscillator.stop(currentTime + note.duration);\n        currentTime += note.duration * 0.8; // Chevauchement léger des notes\n      });\n\n      return Promise.resolve();\n    };\n    return audio;\n  }\n  /**\n   * Crée un son de fin d'appel (mélodie descendante douce)\n   */\n  createEndSound(audioContext) {\n    const audio = new Audio();\n    audio.volume = 0.4;\n    audio.playSynthetic = () => {\n      // Mélodie descendante douce : Sol-Mi-Do-Sol(grave)\n      const melody = [{\n        freq: 783.99,\n        duration: 0.2\n      }, {\n        freq: 659.25,\n        duration: 0.2\n      }, {\n        freq: 523.25,\n        duration: 0.2\n      }, {\n        freq: 392.0,\n        duration: 0.4\n      } // Sol (octave inférieure)\n      ];\n\n      let currentTime = audioContext.currentTime;\n      melody.forEach((note, index) => {\n        const oscillator = audioContext.createOscillator();\n        const gainNode = audioContext.createGain();\n        oscillator.connect(gainNode);\n        gainNode.connect(audioContext.destination);\n        oscillator.frequency.value = note.freq;\n        oscillator.type = 'sine'; // Son doux pour la fin\n        // Enveloppe douce pour un son apaisant\n        gainNode.gain.setValueAtTime(0, currentTime);\n        gainNode.gain.linearRampToValueAtTime(0.2, currentTime + 0.05);\n        gainNode.gain.linearRampToValueAtTime(0.1, currentTime + note.duration * 0.7);\n        gainNode.gain.exponentialRampToValueAtTime(0.01, currentTime + note.duration);\n        oscillator.start(currentTime);\n        oscillator.stop(currentTime + note.duration);\n        currentTime += note.duration * 0.9; // Léger chevauchement\n      });\n\n      return Promise.resolve();\n    };\n    return audio;\n  }\n  /**\n   * Crée un son de notification agréable (double bip)\n   */\n  createNotificationSound(audioContext) {\n    const audio = new Audio();\n    audio.volume = 0.6;\n    audio.playSynthetic = () => {\n      // Double bip agréable : Do-Sol\n      const notes = [{\n        freq: 523.25,\n        duration: 0.15,\n        delay: 0\n      }, {\n        freq: 783.99,\n        duration: 0.25,\n        delay: 0.2\n      } // Sol\n      ];\n\n      notes.forEach(note => {\n        setTimeout(() => {\n          const oscillator = audioContext.createOscillator();\n          const gainNode = audioContext.createGain();\n          oscillator.connect(gainNode);\n          gainNode.connect(audioContext.destination);\n          oscillator.frequency.value = note.freq;\n          oscillator.type = 'triangle'; // Son doux et agréable\n          const startTime = audioContext.currentTime;\n          // Enveloppe pour un son naturel\n          gainNode.gain.setValueAtTime(0, startTime);\n          gainNode.gain.linearRampToValueAtTime(0.4, startTime + 0.02);\n          gainNode.gain.linearRampToValueAtTime(0.2, startTime + note.duration * 0.7);\n          gainNode.gain.exponentialRampToValueAtTime(0.01, startTime + note.duration);\n          oscillator.start(startTime);\n          oscillator.stop(startTime + note.duration);\n        }, note.delay * 1000);\n      });\n      return Promise.resolve();\n    };\n    return audio;\n  }\n  /**\n   * Charge un fichier audio\n   */\n  loadSound(name, path) {\n    try {\n      const audio = new Audio();\n      // Configuration de l'audio\n      audio.preload = 'auto';\n      audio.volume = 0.7;\n      // Gérer les événements de chargement\n      audio.addEventListener('canplaythrough', () => {\n        console.log(`✅ [CallService] Sound ${name} loaded successfully from ${path}`);\n      });\n      audio.addEventListener('error', e => {\n        console.error(`❌ [CallService] Error loading sound ${name} from ${path}:`, e);\n        console.log(`🔄 [CallService] Trying to load ${name} with different approach...`);\n        // Essayer de charger avec un chemin relatif\n        const altPath = path.startsWith('/') ? path.substring(1) : path;\n        if (altPath !== path) {\n          setTimeout(() => {\n            audio.src = altPath;\n            audio.load();\n          }, 100);\n        }\n      });\n      audio.addEventListener('ended', () => {\n        this.isPlaying[name] = false;\n        console.log(`🔇 [CallService] Sound ${name} ended`);\n      });\n      // Définir la source et charger\n      audio.src = path;\n      audio.load();\n      this.sounds[name] = audio;\n      this.isPlaying[name] = false;\n      console.log(`🔊 [CallService] Loading sound ${name} from ${path}`);\n    } catch (error) {\n      console.error(`❌ [CallService] Error creating audio element for ${name}:`, error);\n    }\n  }\n  /**\n   * Joue un son\n   */\n  play(name, loop = false) {\n    if (this.muted) {\n      console.log(`🔇 [CallService] Sound ${name} muted`);\n      return;\n    }\n    try {\n      // Pour les sons d'appel, utiliser directement les versions synthétiques\n      let sound;\n      if (name === 'ringtone' || name === 'call-connected' || name === 'call-end') {\n        const syntheticName = `${name}-synthetic`;\n        sound = this.sounds[syntheticName];\n        if (sound) {\n          console.log(`🎵 [CallService] Using beautiful synthetic melody for ${name}`);\n        }\n      } else {\n        // Pour les autres sons (comme notification), essayer d'abord le fichier\n        sound = this.sounds[name];\n        if (!sound || sound.error) {\n          const syntheticName = `${name}-synthetic`;\n          sound = this.sounds[syntheticName];\n          if (sound) {\n            console.log(`🔊 [CallService] Using synthetic sound for ${name}`);\n          }\n        }\n      }\n      if (!sound) {\n        console.warn(`🔊 [CallService] Sound ${name} not found (neither original nor synthetic)`);\n        // Créer un bip simple comme dernier recours\n        this.playSimpleBeep(name === 'ringtone' ? 800 : name === 'call-connected' ? 1000 : 400);\n        return;\n      }\n      sound.loop = loop;\n      sound.volume = 0.7;\n      if (!this.isPlaying[name]) {\n        console.log(`🔊 [CallService] Playing sound: ${name} (loop: ${loop})`);\n        // Vérifier si c'est un son synthétique\n        if (sound.playSynthetic) {\n          sound.playSynthetic().then(() => {\n            console.log(`✅ [CallService] Synthetic sound ${name} started successfully`);\n            this.isPlaying[name] = true;\n            // Pour la sonnerie synthétique, elle gère sa propre boucle\n            if (name === 'ringtone' && !loop) {\n              // Si ce n'est pas en boucle, arrêter après un certain temps\n              setTimeout(() => {\n                this.isPlaying[name] = false;\n              }, 3000);\n            } else if (name !== 'ringtone') {\n              // Pour les autres sons, arrêter après leur durée\n              setTimeout(() => {\n                this.isPlaying[name] = false;\n              }, name === 'call-connected' ? 1200 : 1000);\n            }\n          }).catch(error => {\n            console.error(`❌ [CallService] Error playing synthetic sound ${name}:`, error);\n          });\n        } else {\n          // Son normal\n          sound.currentTime = 0;\n          sound.play().then(() => {\n            console.log(`✅ [CallService] Sound ${name} started successfully`);\n            this.isPlaying[name] = true;\n          }).catch(error => {\n            console.error(`❌ [CallService] Error playing sound ${name}:`, error);\n            // Essayer le son synthétique en cas d'échec\n            const syntheticName = `${name}-synthetic`;\n            const syntheticSound = this.sounds[syntheticName];\n            if (syntheticSound && syntheticSound.playSynthetic) {\n              console.log(`🔄 [CallService] Falling back to synthetic sound for ${name}`);\n              this.play(name, loop);\n            } else {\n              // Dernier recours : bip simple\n              this.playSimpleBeep(name === 'ringtone' ? 800 : name === 'call-connected' ? 1000 : 400);\n            }\n          });\n        }\n      } else {\n        console.log(`🔊 [CallService] Sound ${name} already playing`);\n      }\n    } catch (error) {\n      console.error(`❌ [CallService] Error in play method for ${name}:`, error);\n      // Dernier recours\n      this.playSimpleBeep(name === 'ringtone' ? 800 : name === 'call-connected' ? 1000 : 400);\n    }\n  }\n  /**\n   * Joue un bip simple comme dernier recours\n   */\n  playSimpleBeep(frequency) {\n    try {\n      const audioContext = new (window.AudioContext || window.webkitAudioContext)();\n      const oscillator = audioContext.createOscillator();\n      const gainNode = audioContext.createGain();\n      oscillator.connect(gainNode);\n      gainNode.connect(audioContext.destination);\n      oscillator.frequency.value = frequency;\n      oscillator.type = 'sine';\n      gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);\n      gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);\n      oscillator.start(audioContext.currentTime);\n      oscillator.stop(audioContext.currentTime + 0.3);\n      console.log(`🔊 [CallService] Playing simple beep at ${frequency}Hz`);\n    } catch (error) {\n      console.error('❌ [CallService] Could not play simple beep:', error);\n    }\n  }\n  /**\n   * Arrête un son\n   */\n  stop(name) {\n    try {\n      let sound = this.sounds[name];\n      // Essayer aussi la version synthétique\n      if (!sound) {\n        sound = this.sounds[`${name}-synthetic`];\n      }\n      if (!sound) {\n        console.warn(`🔊 [CallService] Sound ${name} not found for stopping`);\n        return;\n      }\n      if (this.isPlaying[name]) {\n        console.log(`🔇 [CallService] Stopping sound: ${name}`);\n        // Arrêter le son synthétique si c'est le cas\n        if (sound.stopSynthetic) {\n          sound.stopSynthetic();\n        } else {\n          sound.pause();\n          sound.currentTime = 0;\n        }\n        this.isPlaying[name] = false;\n      } else {\n        console.log(`🔇 [CallService] Sound ${name} was not playing`);\n      }\n    } catch (error) {\n      console.error(`❌ [CallService] Error stopping sound ${name}:`, error);\n    }\n  }\n  /**\n   * S'abonne aux appels entrants\n   */\n  subscribeToIncomingCalls() {\n    // console.log('🔔 [CallService] Setting up incoming call subscription...');\n    try {\n      this.apollo.subscribe({\n        query: INCOMING_CALL_SUBSCRIPTION,\n        errorPolicy: 'all' // Continuer même en cas d'erreur\n      }).subscribe({\n        next: ({\n          data,\n          errors\n        }) => {\n          if (errors) {\n            console.warn('⚠️ [CallService] GraphQL errors in subscription:', errors);\n          }\n          if (data?.incomingCall) {\n            // console.log('📞 [CallService] Incoming call received:', data.incomingCall.id);\n            this.handleIncomingCall(data.incomingCall);\n          }\n        },\n        error: error => {\n          console.error('❌ [CallService] Error in incoming call subscription:', error);\n          // Réessayer UNE SEULE FOIS après 10 secondes\n          setTimeout(() => {\n            this.subscribeToIncomingCalls();\n          }, 10000);\n        },\n        complete: () => {\n          // console.log('🔚 [CallService] Incoming call subscription completed');\n          // Réessayer si la subscription se ferme de manière inattendue\n          setTimeout(() => {\n            this.subscribeToIncomingCalls();\n          }, 5000);\n        }\n      });\n    } catch (error) {\n      console.error('❌ [CallService] Failed to create subscription:', error);\n      // Réessayer après 10 secondes\n      setTimeout(() => {\n        this.subscribeToIncomingCalls();\n      }, 10000);\n    }\n  }\n  /**\n   * Méthode publique pour forcer la réinitialisation de la subscription\n   */\n  reinitializeSubscription() {\n    // console.log('🔄 [CallService] Manually reinitializing subscription...');\n    this.subscribeToIncomingCalls();\n    this.subscribeToCallStatusChanges();\n  }\n  /**\n   * Méthode de test pour vérifier les sons\n   */\n  testSounds() {\n    console.log('🧪 [CallService] Testing sounds...');\n    // Test de la sonnerie\n    console.log('🔊 Testing ringtone...');\n    this.play('ringtone', true);\n    setTimeout(() => {\n      this.stop('ringtone');\n      console.log('🔊 Testing call-connected...');\n      this.play('call-connected');\n    }, 3000);\n    setTimeout(() => {\n      console.log('🔊 Testing call-end...');\n      this.play('call-end');\n    }, 5000);\n  }\n  /**\n   * Test spécifique pour la sonnerie\n   */\n  testRingtone() {\n    console.log('🧪 [CallService] Testing ringtone specifically...');\n    console.log('🔊 [CallService] Available sounds:', Object.keys(this.sounds));\n    // Vérifier si le son est chargé\n    const ringtone = this.sounds['ringtone'];\n    const ringtoneSynthetic = this.sounds['ringtone-synthetic'];\n    if (ringtone) {\n      console.log('✅ [CallService] Ringtone MP3 found:', {\n        src: ringtone.src,\n        readyState: ringtone.readyState,\n        error: ringtone.error,\n        duration: ringtone.duration\n      });\n    } else {\n      console.log('❌ [CallService] Ringtone MP3 not found');\n    }\n    if (ringtoneSynthetic) {\n      console.log('✅ [CallService] Ringtone synthetic found');\n    }\n    // Jouer la sonnerie (elle va automatiquement utiliser le fallback si nécessaire)\n    console.log('🎵 [CallService] Playing beautiful ringtone melody...');\n    this.play('ringtone', true);\n    // Arrêter après 8 secondes pour entendre plusieurs cycles de la mélodie\n    setTimeout(() => {\n      this.stop('ringtone');\n      console.log('🔇 [CallService] Ringtone test stopped');\n    }, 8000);\n  }\n  /**\n   * Test tous les nouveaux sons améliorés\n   */\n  testBeautifulSounds() {\n    console.log('🧪 [CallService] Testing all beautiful sounds...');\n    // Test de la sonnerie (mélodie)\n    // console.log('🎵 Testing beautiful ringtone melody...');\n    this.play('ringtone', true);\n    setTimeout(() => {\n      this.stop('ringtone');\n      // console.log('🎵 Testing beautiful connection sound...');\n      this.play('call-connected');\n    }, 4000);\n    setTimeout(() => {\n      // console.log('🎵 Testing beautiful end sound...');\n      this.play('call-end');\n    }, 6000);\n    setTimeout(() => {\n      // console.log('🎵 Testing beautiful notification sound...');\n      this.play('notification');\n    }, 8000);\n    setTimeout(() => {\n      console.log('🎵 All sound tests completed!');\n    }, 10000);\n  }\n  /**\n   * Joue le son de notification (méthode publique)\n   */\n  playNotification() {\n    console.log('🔔 [CallService] Playing notification sound...');\n    this.play('notification');\n  }\n  /**\n   * S'abonne aux changements de statut d'appel\n   */\n  subscribeToCallStatusChanges() {\n    console.log('📞 [CallService] Setting up call status change subscription...');\n    try {\n      this.apollo.subscribe({\n        query: CALL_STATUS_CHANGED_SUBSCRIPTION,\n        errorPolicy: 'all'\n      }).subscribe({\n        next: ({\n          data,\n          errors\n        }) => {\n          if (errors) {\n            console.warn('⚠️ [CallService] GraphQL errors in call status subscription:', errors);\n          }\n          if (data?.callStatusChanged) {\n            console.log('📞 [CallService] Call status changed:', data.callStatusChanged);\n            this.handleCallStatusChange(data.callStatusChanged);\n          }\n        },\n        error: error => {\n          console.error('❌ [CallService] Error in call status subscription:', error);\n          // Réessayer après 5 secondes\n          setTimeout(() => {\n            this.subscribeToCallStatusChanges();\n          }, 5000);\n        },\n        complete: () => {\n          console.log('🔚 [CallService] Call status subscription completed');\n          // Réessayer si la subscription se ferme\n          setTimeout(() => {\n            this.subscribeToCallStatusChanges();\n          }, 2000);\n        }\n      });\n    } catch (error) {\n      console.error('❌ [CallService] Failed to create call status subscription:', error);\n      setTimeout(() => {\n        this.subscribeToCallStatusChanges();\n      }, 3000);\n    }\n  }\n  /**\n   * Gère un appel entrant\n   */\n  handleIncomingCall(call) {\n    console.log('🔔 [CallService] Handling incoming call:', {\n      callId: call.id,\n      callType: call.type,\n      caller: call.caller?.username,\n      conversationId: call.conversationId\n    });\n    this.incomingCall.next(call);\n    this.play('ringtone', true);\n    console.log('🔊 [CallService] Ringtone started, call notification sent to UI');\n  }\n  /**\n   * Gère les changements de statut d'appel\n   */\n  handleCallStatusChange(call) {\n    console.log('📞 [CallService] Call status changed:', call.status);\n    switch (call.status) {\n      case CallStatus.REJECTED:\n        console.log('❌ [CallService] Call was rejected');\n        this.stop('ringtone');\n        this.play('call-end');\n        this.activeCall.next(null);\n        this.incomingCall.next(null);\n        break;\n      case CallStatus.ENDED:\n        console.log('📞 [CallService] Call ended');\n        this.stopAllSounds();\n        this.play('call-end');\n        this.activeCall.next(null);\n        this.incomingCall.next(null);\n        break;\n      case CallStatus.CONNECTED:\n        console.log('✅ [CallService] Call connected');\n        this.stop('ringtone');\n        this.play('call-connected');\n        this.activeCall.next(call);\n        this.incomingCall.next(null);\n        break;\n      case CallStatus.RINGING:\n        console.log('📞 [CallService] Call is ringing');\n        this.play('ringtone', true);\n        break;\n      default:\n        console.log('📞 [CallService] Unknown call status:', call.status);\n        break;\n    }\n  }\n  /**\n   * Obtient les médias utilisateur (caméra/micro)\n   */\n  getUserMedia(callType) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      console.log('🎥 [CallService] Getting user media for:', callType);\n      const constraints = {\n        audio: true,\n        video: callType === CallType.VIDEO\n      };\n      try {\n        const stream = yield navigator.mediaDevices.getUserMedia(constraints);\n        console.log('✅ [CallService] User media obtained');\n        _this.localStream = stream;\n        // Afficher le stream local si on a un élément vidéo\n        if (_this.localVideoElement && callType === CallType.VIDEO) {\n          _this.localVideoElement.srcObject = stream;\n        }\n        return stream;\n      } catch (error) {\n        console.error('❌ [CallService] Failed to get user media:', error);\n        throw new Error(\"Impossible d'accéder à la caméra/microphone\");\n      }\n    })();\n  }\n  /**\n   * Ajoute le stream local à la PeerConnection\n   */\n  addLocalStreamToPeerConnection(stream) {\n    if (!this.peerConnection) {\n      console.error('❌ [CallService] No peer connection available');\n      return;\n    }\n    console.log('📤 [CallService] Adding local stream to peer connection');\n    stream.getTracks().forEach(track => {\n      this.peerConnection.addTrack(track, stream);\n    });\n  }\n  /**\n   * Crée une offre WebRTC\n   */\n  createOffer() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this2.peerConnection) {\n        throw new Error('No peer connection available');\n      }\n      console.log('📝 [CallService] Creating WebRTC offer');\n      const offer = yield _this2.peerConnection.createOffer();\n      yield _this2.peerConnection.setLocalDescription(offer);\n      return offer;\n    })();\n  }\n  /**\n   * Crée une réponse WebRTC\n   */\n  createAnswer(offer) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this3.peerConnection) {\n        throw new Error('No peer connection available');\n      }\n      console.log('📝 [CallService] Creating WebRTC answer');\n      yield _this3.peerConnection.setRemoteDescription(offer);\n      const answer = yield _this3.peerConnection.createAnswer();\n      yield _this3.peerConnection.setLocalDescription(answer);\n      return answer;\n    })();\n  }\n  /**\n   * Configure les éléments vidéo\n   */\n  setVideoElements(localVideo, remoteVideo) {\n    console.log('📺 [CallService] Setting video elements');\n    this.localVideoElement = localVideo;\n    this.remoteVideoElement = remoteVideo;\n    // Configurer les éléments pour la lecture audio\n    try {\n      this.setupAudioPlayback(localVideo, remoteVideo);\n    } catch (error) {\n      console.warn('⚠️ [CallService] setupAudioPlayback error:', error);\n    }\n    // Si on a déjà des streams, les connecter\n    if (this.localStream && localVideo) {\n      localVideo.srcObject = this.localStream;\n      try {\n        this.ensureAudioPlayback(localVideo);\n      } catch (error) {\n        console.warn('⚠️ [CallService] ensureAudioPlayback error for local:', error);\n      }\n    }\n    if (this.remoteStream && remoteVideo) {\n      remoteVideo.srcObject = this.remoteStream;\n      try {\n        this.ensureAudioPlayback(remoteVideo);\n      } catch (error) {\n        console.warn('⚠️ [CallService] ensureAudioPlayback error for remote:', error);\n      }\n    }\n  }\n  /**\n   * Configure la lecture audio pour les éléments vidéo\n   */\n  setupAudioPlayback(localVideo, remoteVideo) {\n    console.log('🔊 [CallService] Setting up audio playback');\n    // Configurer les propriétés audio\n    localVideo.volume = 0; // Local toujours muet pour éviter l'écho\n    remoteVideo.volume = 1; // Remote avec volume maximum\n    // Forcer l'autoplay\n    localVideo.autoplay = true;\n    remoteVideo.autoplay = true;\n    // Événements pour débugger\n    remoteVideo.addEventListener('loadedmetadata', () => {\n      console.log('🎵 [CallService] Remote video metadata loaded');\n    });\n    remoteVideo.addEventListener('canplay', () => {\n      console.log('🎵 [CallService] Remote video can play');\n      this.ensureAudioPlayback(remoteVideo);\n    });\n    remoteVideo.addEventListener('play', () => {\n      console.log('🎵 [CallService] Remote video started playing');\n    });\n    remoteVideo.addEventListener('pause', () => {\n      console.log('⏸️ [CallService] Remote video paused');\n    });\n  }\n  /**\n   * Force la lecture audio\n   */\n  ensureAudioPlayback(videoElement) {\n    console.log('🔊 [CallService] Ensuring audio playback for element:', videoElement === this.localVideoElement ? 'local' : 'remote');\n    // Forcer la lecture\n    videoElement.play().then(() => {\n      console.log('✅ [CallService] Video/audio playback started successfully');\n    }).catch(error => {\n      console.warn('⚠️ [CallService] Autoplay prevented, user interaction required:', error);\n      // Essayer de jouer après interaction utilisateur\n      const playOnInteraction = () => {\n        videoElement.play().then(() => {\n          console.log('✅ [CallService] Video/audio playback started after user interaction');\n          document.removeEventListener('click', playOnInteraction);\n          document.removeEventListener('keydown', playOnInteraction);\n        }).catch(err => {\n          console.error('❌ [CallService] Failed to play after interaction:', err);\n        });\n      };\n      document.addEventListener('click', playOnInteraction);\n      document.addEventListener('keydown', playOnInteraction);\n    });\n  }\n  /**\n   * Initie un appel WebRTC\n   */\n  initiateCall(recipientId, callType, conversationId) {\n    console.log('🔄 [CallService] Initiating call:', {\n      recipientId,\n      callType,\n      conversationId\n    });\n    if (!recipientId) {\n      const error = new Error('Recipient ID is required');\n      console.error('❌ [CallService] initiateCall error:', error);\n      return throwError(() => error);\n    }\n    // Générer un ID unique pour l'appel\n    const callId = `call_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n    // Créer une vraie offre WebRTC\n    return from(this.createWebRTCOffer(callType)).pipe(switchMap(offer => {\n      const variables = {\n        recipientId,\n        callType: callType,\n        callId,\n        offer: JSON.stringify(offer),\n        conversationId\n      };\n      console.log('📤 [CallService] Sending initiate call mutation:', variables);\n      return this.apollo.mutate({\n        mutation: INITIATE_CALL_MUTATION,\n        variables\n      }).pipe(map(result => {\n        console.log('✅ [CallService] Call initiated successfully:', result);\n        if (!result.data?.initiateCall) {\n          throw new Error('No call data received from server');\n        }\n        const call = result.data.initiateCall;\n        console.log('📞 [CallService] Call details:', {\n          id: call.id,\n          type: call.type,\n          status: call.status,\n          caller: call.caller?.username,\n          recipient: call.recipient?.username\n        });\n        // Mettre à jour l'état local\n        this.activeCall.next(call);\n        return call;\n      }), catchError(error => {\n        console.error('❌ [CallService] initiateCall error:', error);\n        this.logger.error('Error initiating call:', error);\n        let errorMessage = \"Erreur lors de l'initiation de l'appel\";\n        if (error.networkError) {\n          errorMessage = 'Erreur de connexion réseau';\n        } else if (error.graphQLErrors?.length > 0) {\n          errorMessage = error.graphQLErrors[0].message || errorMessage;\n        }\n        return throwError(() => new Error(errorMessage));\n      }));\n    }), catchError(error => {\n      console.error('❌ [CallService] WebRTC error:', error);\n      return throwError(() => new Error('Erreur WebRTC: ' + error.message));\n    }));\n  }\n  /**\n   * Crée une offre WebRTC complète avec médias\n   */\n  createWebRTCOffer(callType) {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        // Réinitialiser la PeerConnection si nécessaire\n        if (!_this4.peerConnection) {\n          _this4.initializeWebRTC();\n        }\n        // Obtenir les médias utilisateur\n        const stream = yield _this4.getUserMedia(callType);\n        // Ajouter le stream à la PeerConnection\n        _this4.addLocalStreamToPeerConnection(stream);\n        // Créer l'offre\n        const offer = yield _this4.createOffer();\n        console.log('✅ [CallService] WebRTC offer created successfully');\n        return offer;\n      } catch (error) {\n        console.error('❌ [CallService] Failed to create WebRTC offer:', error);\n        throw error;\n      }\n    })();\n  }\n  /**\n   * Accepte un appel entrant\n   */\n  acceptCall(incomingCall) {\n    console.log('🔄 [CallService] Accepting call:', incomingCall.id);\n    // Créer une vraie réponse WebRTC\n    return from(this.createWebRTCAnswer(incomingCall)).pipe(switchMap(answer => {\n      return this.apollo.mutate({\n        mutation: ACCEPT_CALL_MUTATION,\n        variables: {\n          callId: incomingCall.id,\n          answer: JSON.stringify(answer)\n        }\n      }).pipe(switchMap(result => {\n        console.log('✅ [CallService] Call accepted successfully:', result);\n        if (!result.data?.acceptCall) {\n          throw new Error('No call data received from server');\n        }\n        const call = result.data.acceptCall;\n        // Arrêter la sonnerie\n        this.stop('ringtone');\n        this.play('call-connected');\n        // Démarrer les médias pour l'appel de manière asynchrone\n        return from(this.startMediaForCall(incomingCall, call));\n      }), catchError(error => {\n        console.error('❌ [CallService] acceptCall error:', error);\n        this.logger.error('Error accepting call:', error);\n        return throwError(() => new Error(\"Erreur lors de l'acceptation de l'appel\"));\n      }));\n    }), catchError(error => {\n      console.error('❌ [CallService] WebRTC answer error:', error);\n      return throwError(() => new Error('Erreur WebRTC: ' + error.message));\n    }));\n  }\n  /**\n   * Crée une réponse WebRTC complète avec médias\n   */\n  createWebRTCAnswer(incomingCall) {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        console.log('🔄 [CallService] Creating WebRTC answer for incoming call:', {\n          callId: incomingCall.id,\n          callType: incomingCall.type,\n          hasOffer: !!incomingCall.offer,\n          offerLength: incomingCall.offer?.length || 0\n        });\n        // Réinitialiser la PeerConnection si nécessaire\n        if (!_this5.peerConnection) {\n          console.log('🔧 [CallService] Initializing new PeerConnection for answer');\n          _this5.initializeWebRTC();\n        }\n        // Obtenir les médias utilisateur\n        console.log('🎥 [CallService] Getting user media for answer...');\n        const stream = yield _this5.getUserMedia(incomingCall.type);\n        // Ajouter le stream à la PeerConnection\n        console.log('📤 [CallService] Adding local stream to PeerConnection for answer');\n        _this5.addLocalStreamToPeerConnection(stream);\n        // Récupérer l'offre depuis l'appel entrant\n        if (!incomingCall.offer) {\n          throw new Error('No offer received in incoming call');\n        }\n        const offer = JSON.parse(incomingCall.offer);\n        if (!offer || !offer.type || !offer.sdp) {\n          throw new Error('Invalid offer format received');\n        }\n        // Créer la réponse\n        const answer = yield _this5.createAnswer(offer);\n        console.log('✅ [CallService] WebRTC answer created successfully');\n        return answer;\n      } catch (error) {\n        console.error('❌ [CallService] Failed to create WebRTC answer:', error);\n        throw error;\n      }\n    })();\n  }\n  /**\n   * Rejette un appel entrant\n   */\n  rejectCall(callId, reason) {\n    console.log('🔄 [CallService] Rejecting call:', callId, reason);\n    return this.apollo.mutate({\n      mutation: REJECT_CALL_MUTATION,\n      variables: {\n        callId,\n        reason: reason || 'User rejected'\n      }\n    }).pipe(map(result => {\n      console.log('✅ [CallService] Call rejected successfully:', result);\n      if (!result.data?.rejectCall) {\n        throw new Error('No response received from server');\n      }\n      // Mettre à jour l'état local\n      this.incomingCall.next(null);\n      this.activeCall.next(null);\n      // Arrêter la sonnerie\n      this.stop('ringtone');\n      return result.data.rejectCall;\n    }), catchError(error => {\n      console.error('❌ [CallService] rejectCall error:', error);\n      this.logger.error('Error rejecting call:', error);\n      return throwError(() => new Error(\"Erreur lors du rejet de l'appel\"));\n    }));\n  }\n  /**\n   * Termine un appel en cours\n   */\n  endCall(callId) {\n    console.log('🔄 [CallService] Ending call:', callId);\n    return this.apollo.mutate({\n      mutation: END_CALL_MUTATION,\n      variables: {\n        callId,\n        feedback: null // Pas de feedback pour l'instant\n      }\n    }).pipe(map(result => {\n      console.log('✅ [CallService] Call ended successfully:', result);\n      if (!result.data?.endCall) {\n        throw new Error('No response received from server');\n      }\n      // Mettre à jour l'état local\n      this.activeCall.next(null);\n      this.incomingCall.next(null);\n      // Arrêter tous les sons\n      this.stopAllSounds();\n      this.play('call-end');\n      // Nettoyer les ressources WebRTC\n      this.cleanupWebRTC();\n      return result.data.endCall;\n    }), catchError(error => {\n      console.error('❌ [CallService] endCall error:', error);\n      this.logger.error('Error ending call:', error);\n      return throwError(() => new Error(\"Erreur lors de la fin de l'appel\"));\n    }));\n  }\n  /**\n   * Bascule l'état des médias (audio/vidéo) pendant un appel\n   */\n  toggleMedia(callId, enableVideo, enableAudio) {\n    console.log('🔄 [CallService] Toggling media:', {\n      callId,\n      enableVideo,\n      enableAudio\n    });\n    return this.apollo.mutate({\n      mutation: TOGGLE_CALL_MEDIA_MUTATION,\n      variables: {\n        callId,\n        video: enableVideo,\n        audio: enableAudio\n      }\n    }).pipe(map(result => {\n      console.log('✅ [CallService] Media toggled successfully:', result);\n      if (!result.data?.toggleCallMedia) {\n        throw new Error('No response received from server');\n      }\n      return result.data.toggleCallMedia;\n    }), catchError(error => {\n      console.error('❌ [CallService] toggleMedia error:', error);\n      this.logger.error('Error toggling media:', error);\n      return throwError(() => new Error('Erreur lors du changement des médias'));\n    }));\n  }\n  /**\n   * Démarre les médias pour un appel accepté\n   */\n  startMediaForCall(incomingCall, call) {\n    var _this6 = this;\n    return _asyncToGenerator(function* () {\n      console.log('🎥 [CallService] Call connected - playing connection sound');\n      // Jouer le son de connexion\n      _this6.play('call-connected');\n      // Mettre à jour l'état local\n      _this6.activeCall.next(call);\n      _this6.incomingCall.next(null); // Supprimer l'appel entrant\n      return call;\n    })();\n  }\n  /**\n   * Active/désactive la vidéo\n   */\n  toggleVideo() {\n    this.isVideoEnabled = !this.isVideoEnabled;\n    console.log('📹 [CallService] Video toggled:', this.isVideoEnabled);\n    return this.isVideoEnabled;\n  }\n  /**\n   * Active/désactive l'audio\n   */\n  toggleAudio() {\n    this.isAudioEnabled = !this.isAudioEnabled;\n    console.log('🎤 [CallService] Audio toggled:', this.isAudioEnabled);\n    return this.isAudioEnabled;\n  }\n  /**\n   * Obtient l'état de la vidéo\n   */\n  getVideoEnabled() {\n    return this.isVideoEnabled;\n  }\n  /**\n   * Obtient l'état de l'audio\n   */\n  getAudioEnabled() {\n    return this.isAudioEnabled;\n  }\n  /**\n   * Arrête tous les sons\n   */\n  stopAllSounds() {\n    console.log('🔇 [CallService] Stopping all sounds');\n    Object.keys(this.sounds).forEach(name => {\n      this.stop(name);\n    });\n  }\n  /**\n   * Active les sons après interaction utilisateur (pour contourner les restrictions du navigateur)\n   */\n  enableSounds() {\n    console.log('🔊 [CallService] Enabling sounds after user interaction');\n    Object.values(this.sounds).forEach(sound => {\n      if (sound) {\n        sound.muted = false;\n        sound.volume = 0.7;\n        // Jouer et arrêter immédiatement pour \"débloquer\" l'audio\n        sound.play().then(() => {\n          sound.pause();\n          sound.currentTime = 0;\n        }).catch(() => {\n          // Ignorer les erreurs ici\n        });\n      }\n    });\n  }\n  /**\n   * Nettoie les ressources WebRTC\n   */\n  cleanupWebRTC() {\n    console.log('🧹 [CallService] Cleaning up WebRTC resources');\n    // Arrêter les tracks du stream local\n    if (this.localStream) {\n      this.localStream.getTracks().forEach(track => {\n        track.stop();\n      });\n      this.localStream = null;\n    }\n    // Nettoyer les éléments vidéo\n    if (this.localVideoElement) {\n      this.localVideoElement.srcObject = null;\n    }\n    if (this.remoteVideoElement) {\n      this.remoteVideoElement.srcObject = null;\n    }\n    // Fermer la PeerConnection\n    if (this.peerConnection) {\n      this.peerConnection.close();\n      this.peerConnection = null;\n    }\n    this.remoteStream = null;\n  }\n  ngOnDestroy() {\n    this.stopAllSounds();\n    this.cleanupWebRTC();\n  }\n  static {\n    this.ɵfac = function CallService_Factory(t) {\n      return new (t || CallService)(i0.ɵɵinject(i1.Apollo), i0.ɵɵinject(i2.LoggerService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: CallService,\n      factory: CallService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["BehaviorSubject", "throwError", "from", "map", "catchError", "switchMap", "CallType", "CallStatus", "INITIATE_CALL_MUTATION", "ACCEPT_CALL_MUTATION", "REJECT_CALL_MUTATION", "END_CALL_MUTATION", "TOGGLE_CALL_MEDIA_MUTATION", "INCOMING_CALL_SUBSCRIPTION", "CALL_STATUS_CHANGED_SUBSCRIPTION", "CallService", "constructor", "apollo", "logger", "activeCall", "incomingCall", "activeCall$", "asObservable", "incomingCall$", "sounds", "isPlaying", "muted", "isVideoEnabled", "isAudioEnabled", "peerConnection", "localStream", "remoteStream", "localVideoElement", "remoteVideoElement", "preloadSounds", "initializeSubscriptions", "initializeWebRTC", "setTimeout", "subscribeToIncomingCalls", "subscribeToCallStatusChanges", "value", "console", "log", "configuration", "iceServers", "urls", "RTCPeerConnection", "setupPeerConnectionEvents", "error", "ontrack", "event", "streams", "srcObject", "ensureAudioPlayback", "onicecandidate", "candidate", "onconnectionstatechange", "connectionState", "createSyntheticSounds", "loadSound", "audioContext", "window", "AudioContext", "webkitAudioContext", "createRingtoneSound", "createConnectedSound", "createEndSound", "createNotificationSound", "warn", "audio", "Audio", "volume", "timeoutIds", "playSynthetic", "Promise", "resolve", "playMelody", "melody", "freq", "duration", "currentTime", "for<PERSON>ach", "note", "index", "oscillator", "createOscillator", "gainNode", "createGain", "connect", "destination", "frequency", "type", "gain", "setValueAtTime", "linearRampToValueAtTime", "exponentialRampToValueAtTime", "start", "stop", "timeoutId", "push", "stopSynthetic", "id", "clearTimeout", "notes", "delay", "startTime", "name", "path", "preload", "addEventListener", "e", "altPath", "startsWith", "substring", "src", "load", "play", "loop", "sound", "syntheticName", "playSimpleBeep", "then", "catch", "syntheticSound", "pause", "subscribe", "query", "errorPolicy", "next", "data", "errors", "handleIncomingCall", "complete", "reinitializeSubscription", "testSounds", "testRingtone", "Object", "keys", "ringtone", "ringtoneSynthetic", "readyState", "testBeautifulSounds", "playNotification", "callStatusChanged", "handleCallStatusChange", "call", "callId", "callType", "caller", "username", "conversationId", "status", "REJECTED", "ENDED", "stopAllSounds", "CONNECTED", "RINGING", "getUserMedia", "_this", "_asyncToGenerator", "constraints", "video", "VIDEO", "stream", "navigator", "mediaDevices", "Error", "addLocalStreamToPeerConnection", "getTracks", "track", "addTrack", "createOffer", "_this2", "offer", "setLocalDescription", "createAnswer", "_this3", "setRemoteDescription", "answer", "setVideoElements", "localVideo", "remoteVideo", "setupAudioPlayback", "autoplay", "videoElement", "playOnInteraction", "document", "removeEventListener", "err", "initiateCall", "recipientId", "Date", "now", "Math", "random", "toString", "substr", "createWebRTCOffer", "pipe", "variables", "JSON", "stringify", "mutate", "mutation", "result", "recipient", "errorMessage", "networkError", "graphQLErrors", "length", "message", "_this4", "acceptCall", "createWebRTCAnswer", "startMediaForCall", "_this5", "<PERSON><PERSON><PERSON>", "offerLength", "parse", "sdp", "rejectCall", "reason", "endCall", "feedback", "cleanupWebRTC", "toggleMedia", "enableVideo", "enableAudio", "toggleCallMedia", "_this6", "toggleVideo", "toggleAudio", "getVideoEnabled", "getAudioEnabled", "enableSounds", "values", "close", "ngOnDestroy", "i0", "ɵɵinject", "i1", "Apollo", "i2", "LoggerService", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\services\\call.service.ts"], "sourcesContent": ["import { Injectable, <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';\nimport { Apollo } from 'apollo-angular';\nimport { BehaviorSubject, Observable, throwError, of, from } from 'rxjs';\nimport { map, catchError, switchMap } from 'rxjs/operators';\nimport {\n  Call,\n  CallType,\n  CallStatus,\n  IncomingCall,\n  CallSuccess,\n} from '../models/message.model';\nimport {\n  INITIATE_CALL_MUTATION,\n  ACCEPT_CALL_MUTATION,\n  REJECT_CALL_MUTATION,\n  END_CALL_MUTATION,\n  TOGGLE_CALL_MEDIA_MUTATION,\n  INCOMING_CALL_SUBSCRIPTION,\n  CALL_STATUS_CHANGED_SUBSCRIPTION,\n} from '../graphql/message.graphql';\nimport { LoggerService } from './logger.service';\n\n@Injectable({\n  providedIn: 'root',\n})\nexport class CallService implements OnDestroy {\n  // État des appels\n  private activeCall = new BehaviorSubject<Call | null>(null);\n  private incomingCall = new BehaviorSubject<IncomingCall | null>(null);\n\n  // Observables publics\n  public activeCall$ = this.activeCall.asObservable();\n  public incomingCall$ = this.incomingCall.asObservable();\n\n  // Propriétés pour la gestion des sons\n  private sounds: { [key: string]: HTMLAudioElement } = {};\n  private isPlaying: { [key: string]: boolean } = {};\n  private muted = false;\n\n  // États simples pour les médias\n  private isVideoEnabled = true;\n  private isAudioEnabled = true;\n\n  // WebRTC\n  private peerConnection: RTCPeerConnection | null = null;\n  private localStream: MediaStream | null = null;\n  private remoteStream: MediaStream | null = null;\n  private localVideoElement: HTMLVideoElement | null = null;\n  private remoteVideoElement: HTMLVideoElement | null = null;\n\n  constructor(private apollo: Apollo, private logger: LoggerService) {\n    this.preloadSounds();\n    this.initializeSubscriptions();\n    this.initializeWebRTC();\n  }\n\n  /**\n   * Initialise les subscriptions avec un délai pour s'assurer que l'authentification est prête\n   */\n  private initializeSubscriptions(): void {\n    // Attendre un peu pour s'assurer que l'authentification est prête\n    setTimeout(() => {\n      this.subscribeToIncomingCalls();\n      this.subscribeToCallStatusChanges();\n    }, 1000);\n\n    // Réessayer UNE SEULE FOIS après 10 secondes si nécessaire\n    setTimeout(() => {\n      if (!this.incomingCall.value) {\n        // console.log('🔄 [CallService] Retrying subscription initialization...');\n        this.subscribeToIncomingCalls();\n        this.subscribeToCallStatusChanges();\n      }\n    }, 10000);\n  }\n\n  /**\n   * Initialise WebRTC\n   */\n  private initializeWebRTC(): void {\n    console.log('🔧 [CallService] Initializing WebRTC...');\n\n    // Configuration des serveurs STUN/TURN\n    const configuration: RTCConfiguration = {\n      iceServers: [\n        { urls: 'stun:stun.l.google.com:19302' },\n        { urls: 'stun:stun1.l.google.com:19302' },\n      ],\n    };\n\n    try {\n      this.peerConnection = new RTCPeerConnection(configuration);\n      this.setupPeerConnectionEvents();\n      console.log('✅ [CallService] WebRTC initialized successfully');\n    } catch (error) {\n      console.error('❌ [CallService] Failed to initialize WebRTC:', error);\n    }\n  }\n\n  /**\n   * Configure les événements de la PeerConnection\n   */\n  private setupPeerConnectionEvents(): void {\n    if (!this.peerConnection) return;\n\n    // Quand on reçoit un stream distant\n    this.peerConnection.ontrack = (event) => {\n      console.log('📺 [CallService] Remote stream received');\n      this.remoteStream = event.streams[0];\n      if (this.remoteVideoElement) {\n        this.remoteVideoElement.srcObject = this.remoteStream;\n        // Forcer la lecture audio immédiatement\n        this.ensureAudioPlayback(this.remoteVideoElement);\n      }\n    };\n\n    // Gestion des candidats ICE\n    this.peerConnection.onicecandidate = (event) => {\n      if (event.candidate) {\n        console.log('🧊 [CallService] ICE candidate generated');\n        // TODO: Envoyer le candidat via WebSocket\n      }\n    };\n\n    // État de la connexion\n    this.peerConnection.onconnectionstatechange = () => {\n      console.log(\n        '🔗 [CallService] Connection state:',\n        this.peerConnection?.connectionState\n      );\n    };\n  }\n\n  /**\n   * Précharge les sons utilisés dans l'application\n   */\n  private preloadSounds(): void {\n    // Créer des sons synthétiques de haute qualité\n    this.createSyntheticSounds();\n\n    // Charger le son de notification qui existe encore\n    this.loadSound('notification', 'assets/sounds/notification.mp3');\n\n    console.log(\n      '🎵 [CallService] Beautiful synthetic melodies created for calls'\n    );\n  }\n\n  /**\n   * Crée des sons synthétiques comme fallback\n   */\n  private createSyntheticSounds(): void {\n    try {\n      // Créer un contexte audio pour les sons synthétiques\n      const audioContext = new (window.AudioContext ||\n        (window as any).webkitAudioContext)();\n\n      // Son de sonnerie (mélodie agréable)\n      this.sounds['ringtone-synthetic'] =\n        this.createRingtoneSound(audioContext);\n\n      // Son de connexion (accord agréable)\n      this.sounds['call-connected-synthetic'] =\n        this.createConnectedSound(audioContext);\n\n      // Son de fin d'appel (ton descendant)\n      this.sounds['call-end-synthetic'] = this.createEndSound(audioContext);\n\n      // Son de notification (bip agréable)\n      this.sounds['notification-synthetic'] =\n        this.createNotificationSound(audioContext);\n\n      console.log('🔊 [CallService] Synthetic sounds created as fallback');\n    } catch (error) {\n      console.warn(\n        '⚠️ [CallService] Could not create synthetic sounds:',\n        error\n      );\n    }\n  }\n\n  /**\n   * Crée une sonnerie agréable (mélodie)\n   */\n  private createRingtoneSound(audioContext: AudioContext): HTMLAudioElement {\n    const audio = new Audio();\n    audio.volume = 0.5;\n\n    let isPlaying = false;\n    let timeoutIds: any[] = [];\n\n    (audio as any).playSynthetic = () => {\n      if (isPlaying) return Promise.resolve();\n\n      isPlaying = true;\n\n      const playMelody = () => {\n        if (!isPlaying) return;\n\n        // Mélodie inspirée de Nokia mais plus moderne : Mi-Ré-Fa#-Sol-Do#-Si-Ré-Do\n        const melody = [\n          { freq: 659.25, duration: 0.125 }, // Mi\n          { freq: 587.33, duration: 0.125 }, // Ré\n          { freq: 739.99, duration: 0.25 }, // Fa#\n          { freq: 783.99, duration: 0.25 }, // Sol\n          { freq: 554.37, duration: 0.125 }, // Do#\n          { freq: 493.88, duration: 0.125 }, // Si\n          { freq: 587.33, duration: 0.25 }, // Ré\n          { freq: 523.25, duration: 0.25 }, // Do\n        ];\n\n        let currentTime = audioContext.currentTime;\n\n        melody.forEach((note, index) => {\n          if (!isPlaying) return;\n\n          const oscillator = audioContext.createOscillator();\n          const gainNode = audioContext.createGain();\n\n          oscillator.connect(gainNode);\n          gainNode.connect(audioContext.destination);\n\n          oscillator.frequency.value = note.freq;\n          oscillator.type = 'square'; // Son plus moderne\n\n          // Enveloppe ADSR pour un son plus naturel\n          gainNode.gain.setValueAtTime(0, currentTime);\n          gainNode.gain.linearRampToValueAtTime(0.3, currentTime + 0.02);\n          gainNode.gain.linearRampToValueAtTime(\n            0.2,\n            currentTime + note.duration * 0.7\n          );\n          gainNode.gain.exponentialRampToValueAtTime(\n            0.01,\n            currentTime + note.duration\n          );\n\n          oscillator.start(currentTime);\n          oscillator.stop(currentTime + note.duration);\n\n          currentTime += note.duration + 0.05; // Petite pause entre les notes\n        });\n\n        // Répéter la mélodie après une pause\n        const timeoutId = setTimeout(() => {\n          if (isPlaying) {\n            playMelody();\n          }\n        }, (currentTime - audioContext.currentTime + 0.8) * 1000);\n\n        timeoutIds.push(timeoutId);\n      };\n\n      playMelody();\n      return Promise.resolve();\n    };\n\n    (audio as any).stopSynthetic = () => {\n      isPlaying = false;\n      timeoutIds.forEach((id) => clearTimeout(id));\n      timeoutIds = [];\n    };\n\n    return audio;\n  }\n\n  /**\n   * Crée un son de connexion agréable (mélodie ascendante)\n   */\n  private createConnectedSound(audioContext: AudioContext): HTMLAudioElement {\n    const audio = new Audio();\n    audio.volume = 0.5;\n\n    (audio as any).playSynthetic = () => {\n      // Mélodie ascendante positive : Do-Mi-Sol-Do (octave supérieure)\n      const melody = [\n        { freq: 523.25, duration: 0.15 }, // Do\n        { freq: 659.25, duration: 0.15 }, // Mi\n        { freq: 783.99, duration: 0.15 }, // Sol\n        { freq: 1046.5, duration: 0.4 }, // Do (octave supérieure)\n      ];\n\n      let currentTime = audioContext.currentTime;\n\n      melody.forEach((note, index) => {\n        const oscillator = audioContext.createOscillator();\n        const gainNode = audioContext.createGain();\n\n        oscillator.connect(gainNode);\n        gainNode.connect(audioContext.destination);\n\n        oscillator.frequency.value = note.freq;\n        oscillator.type = 'triangle'; // Son plus doux\n\n        // Enveloppe pour un son naturel\n        gainNode.gain.setValueAtTime(0, currentTime);\n        gainNode.gain.linearRampToValueAtTime(0.25, currentTime + 0.02);\n        gainNode.gain.linearRampToValueAtTime(\n          0.15,\n          currentTime + note.duration * 0.8\n        );\n        gainNode.gain.exponentialRampToValueAtTime(\n          0.01,\n          currentTime + note.duration\n        );\n\n        oscillator.start(currentTime);\n        oscillator.stop(currentTime + note.duration);\n\n        currentTime += note.duration * 0.8; // Chevauchement léger des notes\n      });\n\n      return Promise.resolve();\n    };\n\n    return audio;\n  }\n\n  /**\n   * Crée un son de fin d'appel (mélodie descendante douce)\n   */\n  private createEndSound(audioContext: AudioContext): HTMLAudioElement {\n    const audio = new Audio();\n    audio.volume = 0.4;\n\n    (audio as any).playSynthetic = () => {\n      // Mélodie descendante douce : Sol-Mi-Do-Sol(grave)\n      const melody = [\n        { freq: 783.99, duration: 0.2 }, // Sol\n        { freq: 659.25, duration: 0.2 }, // Mi\n        { freq: 523.25, duration: 0.2 }, // Do\n        { freq: 392.0, duration: 0.4 }, // Sol (octave inférieure)\n      ];\n\n      let currentTime = audioContext.currentTime;\n\n      melody.forEach((note, index) => {\n        const oscillator = audioContext.createOscillator();\n        const gainNode = audioContext.createGain();\n\n        oscillator.connect(gainNode);\n        gainNode.connect(audioContext.destination);\n\n        oscillator.frequency.value = note.freq;\n        oscillator.type = 'sine'; // Son doux pour la fin\n\n        // Enveloppe douce pour un son apaisant\n        gainNode.gain.setValueAtTime(0, currentTime);\n        gainNode.gain.linearRampToValueAtTime(0.2, currentTime + 0.05);\n        gainNode.gain.linearRampToValueAtTime(\n          0.1,\n          currentTime + note.duration * 0.7\n        );\n        gainNode.gain.exponentialRampToValueAtTime(\n          0.01,\n          currentTime + note.duration\n        );\n\n        oscillator.start(currentTime);\n        oscillator.stop(currentTime + note.duration);\n\n        currentTime += note.duration * 0.9; // Léger chevauchement\n      });\n\n      return Promise.resolve();\n    };\n\n    return audio;\n  }\n\n  /**\n   * Crée un son de notification agréable (double bip)\n   */\n  private createNotificationSound(\n    audioContext: AudioContext\n  ): HTMLAudioElement {\n    const audio = new Audio();\n    audio.volume = 0.6;\n\n    (audio as any).playSynthetic = () => {\n      // Double bip agréable : Do-Sol\n      const notes = [\n        { freq: 523.25, duration: 0.15, delay: 0 }, // Do\n        { freq: 783.99, duration: 0.25, delay: 0.2 }, // Sol\n      ];\n\n      notes.forEach((note) => {\n        setTimeout(() => {\n          const oscillator = audioContext.createOscillator();\n          const gainNode = audioContext.createGain();\n\n          oscillator.connect(gainNode);\n          gainNode.connect(audioContext.destination);\n\n          oscillator.frequency.value = note.freq;\n          oscillator.type = 'triangle'; // Son doux et agréable\n\n          const startTime = audioContext.currentTime;\n\n          // Enveloppe pour un son naturel\n          gainNode.gain.setValueAtTime(0, startTime);\n          gainNode.gain.linearRampToValueAtTime(0.4, startTime + 0.02);\n          gainNode.gain.linearRampToValueAtTime(\n            0.2,\n            startTime + note.duration * 0.7\n          );\n          gainNode.gain.exponentialRampToValueAtTime(\n            0.01,\n            startTime + note.duration\n          );\n\n          oscillator.start(startTime);\n          oscillator.stop(startTime + note.duration);\n        }, note.delay * 1000);\n      });\n\n      return Promise.resolve();\n    };\n\n    return audio;\n  }\n\n  /**\n   * Charge un fichier audio\n   */\n  private loadSound(name: string, path: string): void {\n    try {\n      const audio = new Audio();\n\n      // Configuration de l'audio\n      audio.preload = 'auto';\n      audio.volume = 0.7;\n\n      // Gérer les événements de chargement\n      audio.addEventListener('canplaythrough', () => {\n        console.log(\n          `✅ [CallService] Sound ${name} loaded successfully from ${path}`\n        );\n      });\n\n      audio.addEventListener('error', (e) => {\n        console.error(\n          `❌ [CallService] Error loading sound ${name} from ${path}:`,\n          e\n        );\n        console.log(\n          `🔄 [CallService] Trying to load ${name} with different approach...`\n        );\n\n        // Essayer de charger avec un chemin relatif\n        const altPath = path.startsWith('/') ? path.substring(1) : path;\n        if (altPath !== path) {\n          setTimeout(() => {\n            audio.src = altPath;\n            audio.load();\n          }, 100);\n        }\n      });\n\n      audio.addEventListener('ended', () => {\n        this.isPlaying[name] = false;\n        console.log(`🔇 [CallService] Sound ${name} ended`);\n      });\n\n      // Définir la source et charger\n      audio.src = path;\n      audio.load();\n\n      this.sounds[name] = audio;\n      this.isPlaying[name] = false;\n\n      console.log(`🔊 [CallService] Loading sound ${name} from ${path}`);\n    } catch (error) {\n      console.error(\n        `❌ [CallService] Error creating audio element for ${name}:`,\n        error\n      );\n    }\n  }\n\n  /**\n   * Joue un son\n   */\n  private play(name: string, loop: boolean = false): void {\n    if (this.muted) {\n      console.log(`🔇 [CallService] Sound ${name} muted`);\n      return;\n    }\n\n    try {\n      // Pour les sons d'appel, utiliser directement les versions synthétiques\n      let sound;\n      if (\n        name === 'ringtone' ||\n        name === 'call-connected' ||\n        name === 'call-end'\n      ) {\n        const syntheticName = `${name}-synthetic`;\n        sound = this.sounds[syntheticName];\n        if (sound) {\n          console.log(\n            `🎵 [CallService] Using beautiful synthetic melody for ${name}`\n          );\n        }\n      } else {\n        // Pour les autres sons (comme notification), essayer d'abord le fichier\n        sound = this.sounds[name];\n        if (!sound || sound.error) {\n          const syntheticName = `${name}-synthetic`;\n          sound = this.sounds[syntheticName];\n          if (sound) {\n            console.log(`🔊 [CallService] Using synthetic sound for ${name}`);\n          }\n        }\n      }\n\n      if (!sound) {\n        console.warn(\n          `🔊 [CallService] Sound ${name} not found (neither original nor synthetic)`\n        );\n        // Créer un bip simple comme dernier recours\n        this.playSimpleBeep(\n          name === 'ringtone' ? 800 : name === 'call-connected' ? 1000 : 400\n        );\n        return;\n      }\n\n      sound.loop = loop;\n      sound.volume = 0.7;\n\n      if (!this.isPlaying[name]) {\n        console.log(`🔊 [CallService] Playing sound: ${name} (loop: ${loop})`);\n\n        // Vérifier si c'est un son synthétique\n        if ((sound as any).playSynthetic) {\n          (sound as any)\n            .playSynthetic()\n            .then(() => {\n              console.log(\n                `✅ [CallService] Synthetic sound ${name} started successfully`\n              );\n              this.isPlaying[name] = true;\n\n              // Pour la sonnerie synthétique, elle gère sa propre boucle\n              if (name === 'ringtone' && !loop) {\n                // Si ce n'est pas en boucle, arrêter après un certain temps\n                setTimeout(() => {\n                  this.isPlaying[name] = false;\n                }, 3000);\n              } else if (name !== 'ringtone') {\n                // Pour les autres sons, arrêter après leur durée\n                setTimeout(\n                  () => {\n                    this.isPlaying[name] = false;\n                  },\n                  name === 'call-connected' ? 1200 : 1000\n                );\n              }\n            })\n            .catch((error: any) => {\n              console.error(\n                `❌ [CallService] Error playing synthetic sound ${name}:`,\n                error\n              );\n            });\n        } else {\n          // Son normal\n          sound.currentTime = 0;\n          sound\n            .play()\n            .then(() => {\n              console.log(\n                `✅ [CallService] Sound ${name} started successfully`\n              );\n              this.isPlaying[name] = true;\n            })\n            .catch((error) => {\n              console.error(\n                `❌ [CallService] Error playing sound ${name}:`,\n                error\n              );\n\n              // Essayer le son synthétique en cas d'échec\n              const syntheticName = `${name}-synthetic`;\n              const syntheticSound = this.sounds[syntheticName];\n              if (syntheticSound && (syntheticSound as any).playSynthetic) {\n                console.log(\n                  `🔄 [CallService] Falling back to synthetic sound for ${name}`\n                );\n                this.play(name, loop);\n              } else {\n                // Dernier recours : bip simple\n                this.playSimpleBeep(\n                  name === 'ringtone'\n                    ? 800\n                    : name === 'call-connected'\n                    ? 1000\n                    : 400\n                );\n              }\n            });\n        }\n      } else {\n        console.log(`🔊 [CallService] Sound ${name} already playing`);\n      }\n    } catch (error) {\n      console.error(\n        `❌ [CallService] Error in play method for ${name}:`,\n        error\n      );\n      // Dernier recours\n      this.playSimpleBeep(\n        name === 'ringtone' ? 800 : name === 'call-connected' ? 1000 : 400\n      );\n    }\n  }\n\n  /**\n   * Joue un bip simple comme dernier recours\n   */\n  private playSimpleBeep(frequency: number): void {\n    try {\n      const audioContext = new (window.AudioContext ||\n        (window as any).webkitAudioContext)();\n      const oscillator = audioContext.createOscillator();\n      const gainNode = audioContext.createGain();\n\n      oscillator.connect(gainNode);\n      gainNode.connect(audioContext.destination);\n\n      oscillator.frequency.value = frequency;\n      oscillator.type = 'sine';\n\n      gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);\n      gainNode.gain.exponentialRampToValueAtTime(\n        0.01,\n        audioContext.currentTime + 0.3\n      );\n\n      oscillator.start(audioContext.currentTime);\n      oscillator.stop(audioContext.currentTime + 0.3);\n\n      console.log(`🔊 [CallService] Playing simple beep at ${frequency}Hz`);\n    } catch (error) {\n      console.error('❌ [CallService] Could not play simple beep:', error);\n    }\n  }\n\n  /**\n   * Arrête un son\n   */\n  private stop(name: string): void {\n    try {\n      let sound = this.sounds[name];\n\n      // Essayer aussi la version synthétique\n      if (!sound) {\n        sound = this.sounds[`${name}-synthetic`];\n      }\n\n      if (!sound) {\n        console.warn(`🔊 [CallService] Sound ${name} not found for stopping`);\n        return;\n      }\n\n      if (this.isPlaying[name]) {\n        console.log(`🔇 [CallService] Stopping sound: ${name}`);\n\n        // Arrêter le son synthétique si c'est le cas\n        if ((sound as any).stopSynthetic) {\n          (sound as any).stopSynthetic();\n        } else {\n          sound.pause();\n          sound.currentTime = 0;\n        }\n\n        this.isPlaying[name] = false;\n      } else {\n        console.log(`🔇 [CallService] Sound ${name} was not playing`);\n      }\n    } catch (error) {\n      console.error(`❌ [CallService] Error stopping sound ${name}:`, error);\n    }\n  }\n\n  /**\n   * S'abonne aux appels entrants\n   */\n  private subscribeToIncomingCalls(): void {\n    // console.log('🔔 [CallService] Setting up incoming call subscription...');\n\n    try {\n      this.apollo\n        .subscribe<{ incomingCall: IncomingCall }>({\n          query: INCOMING_CALL_SUBSCRIPTION,\n          errorPolicy: 'all', // Continuer même en cas d'erreur\n        })\n        .subscribe({\n          next: ({ data, errors }) => {\n            if (errors) {\n              console.warn(\n                '⚠️ [CallService] GraphQL errors in subscription:',\n                errors\n              );\n            }\n\n            if (data?.incomingCall) {\n              // console.log('📞 [CallService] Incoming call received:', data.incomingCall.id);\n              this.handleIncomingCall(data.incomingCall);\n            }\n          },\n          error: (error) => {\n            console.error(\n              '❌ [CallService] Error in incoming call subscription:',\n              error\n            );\n\n            // Réessayer UNE SEULE FOIS après 10 secondes\n            setTimeout(() => {\n              this.subscribeToIncomingCalls();\n            }, 10000);\n          },\n          complete: () => {\n            // console.log('🔚 [CallService] Incoming call subscription completed');\n            // Réessayer si la subscription se ferme de manière inattendue\n            setTimeout(() => {\n              this.subscribeToIncomingCalls();\n            }, 5000);\n          },\n        });\n    } catch (error) {\n      console.error('❌ [CallService] Failed to create subscription:', error);\n      // Réessayer après 10 secondes\n      setTimeout(() => {\n        this.subscribeToIncomingCalls();\n      }, 10000);\n    }\n  }\n\n  /**\n   * Méthode publique pour forcer la réinitialisation de la subscription\n   */\n  public reinitializeSubscription(): void {\n    // console.log('🔄 [CallService] Manually reinitializing subscription...');\n    this.subscribeToIncomingCalls();\n    this.subscribeToCallStatusChanges();\n  }\n\n  /**\n   * Méthode de test pour vérifier les sons\n   */\n  public testSounds(): void {\n    console.log('🧪 [CallService] Testing sounds...');\n\n    // Test de la sonnerie\n    console.log('🔊 Testing ringtone...');\n    this.play('ringtone', true);\n\n    setTimeout(() => {\n      this.stop('ringtone');\n      console.log('🔊 Testing call-connected...');\n      this.play('call-connected');\n    }, 3000);\n\n    setTimeout(() => {\n      console.log('🔊 Testing call-end...');\n      this.play('call-end');\n    }, 5000);\n  }\n\n  /**\n   * Test spécifique pour la sonnerie\n   */\n  public testRingtone(): void {\n    console.log('🧪 [CallService] Testing ringtone specifically...');\n    console.log('🔊 [CallService] Available sounds:', Object.keys(this.sounds));\n\n    // Vérifier si le son est chargé\n    const ringtone = this.sounds['ringtone'];\n    const ringtoneSynthetic = this.sounds['ringtone-synthetic'];\n\n    if (ringtone) {\n      console.log('✅ [CallService] Ringtone MP3 found:', {\n        src: ringtone.src,\n        readyState: ringtone.readyState,\n        error: ringtone.error,\n        duration: ringtone.duration,\n      });\n    } else {\n      console.log('❌ [CallService] Ringtone MP3 not found');\n    }\n\n    if (ringtoneSynthetic) {\n      console.log('✅ [CallService] Ringtone synthetic found');\n    }\n\n    // Jouer la sonnerie (elle va automatiquement utiliser le fallback si nécessaire)\n    console.log('🎵 [CallService] Playing beautiful ringtone melody...');\n    this.play('ringtone', true);\n\n    // Arrêter après 8 secondes pour entendre plusieurs cycles de la mélodie\n    setTimeout(() => {\n      this.stop('ringtone');\n      console.log('🔇 [CallService] Ringtone test stopped');\n    }, 8000);\n  }\n\n  /**\n   * Test tous les nouveaux sons améliorés\n   */\n  public testBeautifulSounds(): void {\n    console.log('🧪 [CallService] Testing all beautiful sounds...');\n\n    // Test de la sonnerie (mélodie)\n    // console.log('🎵 Testing beautiful ringtone melody...');\n    this.play('ringtone', true);\n\n    setTimeout(() => {\n      this.stop('ringtone');\n      // console.log('🎵 Testing beautiful connection sound...');\n      this.play('call-connected');\n    }, 4000);\n\n    setTimeout(() => {\n      // console.log('🎵 Testing beautiful end sound...');\n      this.play('call-end');\n    }, 6000);\n\n    setTimeout(() => {\n      // console.log('🎵 Testing beautiful notification sound...');\n      this.play('notification');\n    }, 8000);\n\n    setTimeout(() => {\n      console.log('🎵 All sound tests completed!');\n    }, 10000);\n  }\n\n  /**\n   * Joue le son de notification (méthode publique)\n   */\n  public playNotification(): void {\n    console.log('🔔 [CallService] Playing notification sound...');\n    this.play('notification');\n  }\n\n  /**\n   * S'abonne aux changements de statut d'appel\n   */\n  private subscribeToCallStatusChanges(): void {\n    console.log(\n      '📞 [CallService] Setting up call status change subscription...'\n    );\n\n    try {\n      this.apollo\n        .subscribe<{ callStatusChanged: Call }>({\n          query: CALL_STATUS_CHANGED_SUBSCRIPTION,\n          errorPolicy: 'all',\n        })\n        .subscribe({\n          next: ({ data, errors }) => {\n            if (errors) {\n              console.warn(\n                '⚠️ [CallService] GraphQL errors in call status subscription:',\n                errors\n              );\n            }\n\n            if (data?.callStatusChanged) {\n              console.log(\n                '📞 [CallService] Call status changed:',\n                data.callStatusChanged\n              );\n              this.handleCallStatusChange(data.callStatusChanged);\n            }\n          },\n          error: (error) => {\n            console.error(\n              '❌ [CallService] Error in call status subscription:',\n              error\n            );\n            // Réessayer après 5 secondes\n            setTimeout(() => {\n              this.subscribeToCallStatusChanges();\n            }, 5000);\n          },\n          complete: () => {\n            console.log('🔚 [CallService] Call status subscription completed');\n            // Réessayer si la subscription se ferme\n            setTimeout(() => {\n              this.subscribeToCallStatusChanges();\n            }, 2000);\n          },\n        });\n    } catch (error) {\n      console.error(\n        '❌ [CallService] Failed to create call status subscription:',\n        error\n      );\n      setTimeout(() => {\n        this.subscribeToCallStatusChanges();\n      }, 3000);\n    }\n  }\n\n  /**\n   * Gère un appel entrant\n   */\n  private handleIncomingCall(call: IncomingCall): void {\n    console.log('🔔 [CallService] Handling incoming call:', {\n      callId: call.id,\n      callType: call.type,\n      caller: call.caller?.username,\n      conversationId: call.conversationId,\n    });\n\n    this.incomingCall.next(call);\n    this.play('ringtone', true);\n\n    console.log(\n      '🔊 [CallService] Ringtone started, call notification sent to UI'\n    );\n  }\n\n  /**\n   * Gère les changements de statut d'appel\n   */\n  private handleCallStatusChange(call: Call): void {\n    console.log('📞 [CallService] Call status changed:', call.status);\n\n    switch (call.status) {\n      case CallStatus.REJECTED:\n        console.log('❌ [CallService] Call was rejected');\n        this.stop('ringtone');\n        this.play('call-end');\n        this.activeCall.next(null);\n        this.incomingCall.next(null);\n        break;\n\n      case CallStatus.ENDED:\n        console.log('📞 [CallService] Call ended');\n        this.stopAllSounds();\n        this.play('call-end');\n        this.activeCall.next(null);\n        this.incomingCall.next(null);\n        break;\n\n      case CallStatus.CONNECTED:\n        console.log('✅ [CallService] Call connected');\n        this.stop('ringtone');\n        this.play('call-connected');\n        this.activeCall.next(call);\n        this.incomingCall.next(null);\n        break;\n\n      case CallStatus.RINGING:\n        console.log('📞 [CallService] Call is ringing');\n        this.play('ringtone', true);\n        break;\n\n      default:\n        console.log('📞 [CallService] Unknown call status:', call.status);\n        break;\n    }\n  }\n\n  /**\n   * Obtient les médias utilisateur (caméra/micro)\n   */\n  private async getUserMedia(callType: CallType): Promise<MediaStream> {\n    console.log('🎥 [CallService] Getting user media for:', callType);\n\n    const constraints: MediaStreamConstraints = {\n      audio: true,\n      video: callType === CallType.VIDEO,\n    };\n\n    try {\n      const stream = await navigator.mediaDevices.getUserMedia(constraints);\n      console.log('✅ [CallService] User media obtained');\n      this.localStream = stream;\n\n      // Afficher le stream local si on a un élément vidéo\n      if (this.localVideoElement && callType === CallType.VIDEO) {\n        this.localVideoElement.srcObject = stream;\n      }\n\n      return stream;\n    } catch (error) {\n      console.error('❌ [CallService] Failed to get user media:', error);\n      throw new Error(\"Impossible d'accéder à la caméra/microphone\");\n    }\n  }\n\n  /**\n   * Ajoute le stream local à la PeerConnection\n   */\n  private addLocalStreamToPeerConnection(stream: MediaStream): void {\n    if (!this.peerConnection) {\n      console.error('❌ [CallService] No peer connection available');\n      return;\n    }\n\n    console.log('📤 [CallService] Adding local stream to peer connection');\n    stream.getTracks().forEach((track) => {\n      this.peerConnection!.addTrack(track, stream);\n    });\n  }\n\n  /**\n   * Crée une offre WebRTC\n   */\n  private async createOffer(): Promise<RTCSessionDescriptionInit> {\n    if (!this.peerConnection) {\n      throw new Error('No peer connection available');\n    }\n\n    console.log('📝 [CallService] Creating WebRTC offer');\n    const offer = await this.peerConnection.createOffer();\n    await this.peerConnection.setLocalDescription(offer);\n    return offer;\n  }\n\n  /**\n   * Crée une réponse WebRTC\n   */\n  private async createAnswer(\n    offer: RTCSessionDescriptionInit\n  ): Promise<RTCSessionDescriptionInit> {\n    if (!this.peerConnection) {\n      throw new Error('No peer connection available');\n    }\n\n    console.log('📝 [CallService] Creating WebRTC answer');\n    await this.peerConnection.setRemoteDescription(offer);\n    const answer = await this.peerConnection.createAnswer();\n    await this.peerConnection.setLocalDescription(answer);\n    return answer;\n  }\n\n  /**\n   * Configure les éléments vidéo\n   */\n  public setVideoElements(\n    localVideo: HTMLVideoElement,\n    remoteVideo: HTMLVideoElement\n  ): void {\n    console.log('📺 [CallService] Setting video elements');\n    this.localVideoElement = localVideo;\n    this.remoteVideoElement = remoteVideo;\n\n    // Configurer les éléments pour la lecture audio\n    try {\n      this.setupAudioPlayback(localVideo, remoteVideo);\n    } catch (error) {\n      console.warn('⚠️ [CallService] setupAudioPlayback error:', error);\n    }\n\n    // Si on a déjà des streams, les connecter\n    if (this.localStream && localVideo) {\n      localVideo.srcObject = this.localStream;\n      try {\n        this.ensureAudioPlayback(localVideo);\n      } catch (error) {\n        console.warn(\n          '⚠️ [CallService] ensureAudioPlayback error for local:',\n          error\n        );\n      }\n    }\n    if (this.remoteStream && remoteVideo) {\n      remoteVideo.srcObject = this.remoteStream;\n      try {\n        this.ensureAudioPlayback(remoteVideo);\n      } catch (error) {\n        console.warn(\n          '⚠️ [CallService] ensureAudioPlayback error for remote:',\n          error\n        );\n      }\n    }\n  }\n\n  /**\n   * Configure la lecture audio pour les éléments vidéo\n   */\n  private setupAudioPlayback(\n    localVideo: HTMLVideoElement,\n    remoteVideo: HTMLVideoElement\n  ): void {\n    console.log('🔊 [CallService] Setting up audio playback');\n\n    // Configurer les propriétés audio\n    localVideo.volume = 0; // Local toujours muet pour éviter l'écho\n    remoteVideo.volume = 1; // Remote avec volume maximum\n\n    // Forcer l'autoplay\n    localVideo.autoplay = true;\n    remoteVideo.autoplay = true;\n\n    // Événements pour débugger\n    remoteVideo.addEventListener('loadedmetadata', () => {\n      console.log('🎵 [CallService] Remote video metadata loaded');\n    });\n\n    remoteVideo.addEventListener('canplay', () => {\n      console.log('🎵 [CallService] Remote video can play');\n      this.ensureAudioPlayback(remoteVideo);\n    });\n\n    remoteVideo.addEventListener('play', () => {\n      console.log('🎵 [CallService] Remote video started playing');\n    });\n\n    remoteVideo.addEventListener('pause', () => {\n      console.log('⏸️ [CallService] Remote video paused');\n    });\n  }\n\n  /**\n   * Force la lecture audio\n   */\n  private ensureAudioPlayback(videoElement: HTMLVideoElement): void {\n    console.log(\n      '🔊 [CallService] Ensuring audio playback for element:',\n      videoElement === this.localVideoElement ? 'local' : 'remote'\n    );\n\n    // Forcer la lecture\n    videoElement\n      .play()\n      .then(() => {\n        console.log(\n          '✅ [CallService] Video/audio playback started successfully'\n        );\n      })\n      .catch((error) => {\n        console.warn(\n          '⚠️ [CallService] Autoplay prevented, user interaction required:',\n          error\n        );\n\n        // Essayer de jouer après interaction utilisateur\n        const playOnInteraction = () => {\n          videoElement\n            .play()\n            .then(() => {\n              console.log(\n                '✅ [CallService] Video/audio playback started after user interaction'\n              );\n              document.removeEventListener('click', playOnInteraction);\n              document.removeEventListener('keydown', playOnInteraction);\n            })\n            .catch((err) => {\n              console.error(\n                '❌ [CallService] Failed to play after interaction:',\n                err\n              );\n            });\n        };\n\n        document.addEventListener('click', playOnInteraction);\n        document.addEventListener('keydown', playOnInteraction);\n      });\n  }\n\n  /**\n   * Initie un appel WebRTC\n   */\n  initiateCall(\n    recipientId: string,\n    callType: CallType,\n    conversationId?: string\n  ): Observable<Call> {\n    console.log('🔄 [CallService] Initiating call:', {\n      recipientId,\n      callType,\n      conversationId,\n    });\n\n    if (!recipientId) {\n      const error = new Error('Recipient ID is required');\n      console.error('❌ [CallService] initiateCall error:', error);\n      return throwError(() => error);\n    }\n\n    // Générer un ID unique pour l'appel\n    const callId = `call_${Date.now()}_${Math.random()\n      .toString(36)\n      .substr(2, 9)}`;\n\n    // Créer une vraie offre WebRTC\n    return from(this.createWebRTCOffer(callType)).pipe(\n      switchMap((offer) => {\n        const variables = {\n          recipientId,\n          callType: callType,\n          callId,\n          offer: JSON.stringify(offer),\n          conversationId,\n        };\n\n        console.log(\n          '📤 [CallService] Sending initiate call mutation:',\n          variables\n        );\n\n        return this.apollo\n          .mutate<{ initiateCall: Call }>({\n            mutation: INITIATE_CALL_MUTATION,\n            variables,\n          })\n          .pipe(\n            map((result) => {\n              console.log(\n                '✅ [CallService] Call initiated successfully:',\n                result\n              );\n\n              if (!result.data?.initiateCall) {\n                throw new Error('No call data received from server');\n              }\n\n              const call = result.data.initiateCall;\n              console.log('📞 [CallService] Call details:', {\n                id: call.id,\n                type: call.type,\n                status: call.status,\n                caller: call.caller?.username,\n                recipient: call.recipient?.username,\n              });\n\n              // Mettre à jour l'état local\n              this.activeCall.next(call);\n\n              return call;\n            }),\n            catchError((error) => {\n              console.error('❌ [CallService] initiateCall error:', error);\n              this.logger.error('Error initiating call:', error);\n\n              let errorMessage = \"Erreur lors de l'initiation de l'appel\";\n              if (error.networkError) {\n                errorMessage = 'Erreur de connexion réseau';\n              } else if (error.graphQLErrors?.length > 0) {\n                errorMessage = error.graphQLErrors[0].message || errorMessage;\n              }\n\n              return throwError(() => new Error(errorMessage));\n            })\n          );\n      }),\n      catchError((error) => {\n        console.error('❌ [CallService] WebRTC error:', error);\n        return throwError(() => new Error('Erreur WebRTC: ' + error.message));\n      })\n    );\n  }\n\n  /**\n   * Crée une offre WebRTC complète avec médias\n   */\n  private async createWebRTCOffer(\n    callType: CallType\n  ): Promise<RTCSessionDescriptionInit> {\n    try {\n      // Réinitialiser la PeerConnection si nécessaire\n      if (!this.peerConnection) {\n        this.initializeWebRTC();\n      }\n\n      // Obtenir les médias utilisateur\n      const stream = await this.getUserMedia(callType);\n\n      // Ajouter le stream à la PeerConnection\n      this.addLocalStreamToPeerConnection(stream);\n\n      // Créer l'offre\n      const offer = await this.createOffer();\n\n      console.log('✅ [CallService] WebRTC offer created successfully');\n      return offer;\n    } catch (error) {\n      console.error('❌ [CallService] Failed to create WebRTC offer:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Accepte un appel entrant\n   */\n  acceptCall(incomingCall: IncomingCall): Observable<Call> {\n    console.log('🔄 [CallService] Accepting call:', incomingCall.id);\n\n    // Créer une vraie réponse WebRTC\n    return from(this.createWebRTCAnswer(incomingCall)).pipe(\n      switchMap((answer) => {\n        return this.apollo\n          .mutate<{ acceptCall: Call }>({\n            mutation: ACCEPT_CALL_MUTATION,\n            variables: {\n              callId: incomingCall.id,\n              answer: JSON.stringify(answer),\n            },\n          })\n          .pipe(\n            switchMap((result) => {\n              console.log(\n                '✅ [CallService] Call accepted successfully:',\n                result\n              );\n\n              if (!result.data?.acceptCall) {\n                throw new Error('No call data received from server');\n              }\n\n              const call = result.data.acceptCall;\n\n              // Arrêter la sonnerie\n              this.stop('ringtone');\n              this.play('call-connected');\n\n              // Démarrer les médias pour l'appel de manière asynchrone\n              return from(this.startMediaForCall(incomingCall, call));\n            }),\n            catchError((error) => {\n              console.error('❌ [CallService] acceptCall error:', error);\n              this.logger.error('Error accepting call:', error);\n              return throwError(\n                () => new Error(\"Erreur lors de l'acceptation de l'appel\")\n              );\n            })\n          );\n      }),\n      catchError((error) => {\n        console.error('❌ [CallService] WebRTC answer error:', error);\n        return throwError(() => new Error('Erreur WebRTC: ' + error.message));\n      })\n    );\n  }\n\n  /**\n   * Crée une réponse WebRTC complète avec médias\n   */\n  private async createWebRTCAnswer(\n    incomingCall: IncomingCall\n  ): Promise<RTCSessionDescriptionInit> {\n    try {\n      console.log(\n        '🔄 [CallService] Creating WebRTC answer for incoming call:',\n        {\n          callId: incomingCall.id,\n          callType: incomingCall.type,\n          hasOffer: !!incomingCall.offer,\n          offerLength: incomingCall.offer?.length || 0,\n        }\n      );\n\n      // Réinitialiser la PeerConnection si nécessaire\n      if (!this.peerConnection) {\n        console.log(\n          '🔧 [CallService] Initializing new PeerConnection for answer'\n        );\n        this.initializeWebRTC();\n      }\n\n      // Obtenir les médias utilisateur\n      console.log('🎥 [CallService] Getting user media for answer...');\n      const stream = await this.getUserMedia(incomingCall.type);\n\n      // Ajouter le stream à la PeerConnection\n      console.log(\n        '📤 [CallService] Adding local stream to PeerConnection for answer'\n      );\n      this.addLocalStreamToPeerConnection(stream);\n\n      // Récupérer l'offre depuis l'appel entrant\n      if (!incomingCall.offer) {\n        throw new Error('No offer received in incoming call');\n      }\n\n      const offer = JSON.parse(incomingCall.offer);\n\n      if (!offer || !offer.type || !offer.sdp) {\n        throw new Error('Invalid offer format received');\n      }\n\n      // Créer la réponse\n      const answer = await this.createAnswer(offer);\n\n      console.log('✅ [CallService] WebRTC answer created successfully');\n      return answer;\n    } catch (error) {\n      console.error('❌ [CallService] Failed to create WebRTC answer:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Rejette un appel entrant\n   */\n  rejectCall(callId: string, reason?: string): Observable<CallSuccess> {\n    console.log('🔄 [CallService] Rejecting call:', callId, reason);\n\n    return this.apollo\n      .mutate<{ rejectCall: CallSuccess }>({\n        mutation: REJECT_CALL_MUTATION,\n        variables: {\n          callId,\n          reason: reason || 'User rejected',\n        },\n      })\n      .pipe(\n        map((result) => {\n          console.log('✅ [CallService] Call rejected successfully:', result);\n\n          if (!result.data?.rejectCall) {\n            throw new Error('No response received from server');\n          }\n\n          // Mettre à jour l'état local\n          this.incomingCall.next(null);\n          this.activeCall.next(null);\n\n          // Arrêter la sonnerie\n          this.stop('ringtone');\n\n          return result.data.rejectCall;\n        }),\n        catchError((error) => {\n          console.error('❌ [CallService] rejectCall error:', error);\n          this.logger.error('Error rejecting call:', error);\n          return throwError(() => new Error(\"Erreur lors du rejet de l'appel\"));\n        })\n      );\n  }\n\n  /**\n   * Termine un appel en cours\n   */\n  endCall(callId: string): Observable<CallSuccess> {\n    console.log('🔄 [CallService] Ending call:', callId);\n\n    return this.apollo\n      .mutate<{ endCall: CallSuccess }>({\n        mutation: END_CALL_MUTATION,\n        variables: {\n          callId,\n          feedback: null, // Pas de feedback pour l'instant\n        },\n      })\n      .pipe(\n        map((result) => {\n          console.log('✅ [CallService] Call ended successfully:', result);\n\n          if (!result.data?.endCall) {\n            throw new Error('No response received from server');\n          }\n\n          // Mettre à jour l'état local\n          this.activeCall.next(null);\n          this.incomingCall.next(null);\n\n          // Arrêter tous les sons\n          this.stopAllSounds();\n          this.play('call-end');\n\n          // Nettoyer les ressources WebRTC\n          this.cleanupWebRTC();\n\n          return result.data.endCall;\n        }),\n        catchError((error) => {\n          console.error('❌ [CallService] endCall error:', error);\n          this.logger.error('Error ending call:', error);\n          return throwError(\n            () => new Error(\"Erreur lors de la fin de l'appel\")\n          );\n        })\n      );\n  }\n\n  /**\n   * Bascule l'état des médias (audio/vidéo) pendant un appel\n   */\n  toggleMedia(\n    callId: string,\n    enableVideo?: boolean,\n    enableAudio?: boolean\n  ): Observable<CallSuccess> {\n    console.log('🔄 [CallService] Toggling media:', {\n      callId,\n      enableVideo,\n      enableAudio,\n    });\n\n    return this.apollo\n      .mutate<{ toggleCallMedia: CallSuccess }>({\n        mutation: TOGGLE_CALL_MEDIA_MUTATION,\n        variables: {\n          callId,\n          video: enableVideo,\n          audio: enableAudio,\n        },\n      })\n      .pipe(\n        map((result) => {\n          console.log('✅ [CallService] Media toggled successfully:', result);\n\n          if (!result.data?.toggleCallMedia) {\n            throw new Error('No response received from server');\n          }\n\n          return result.data.toggleCallMedia;\n        }),\n        catchError((error) => {\n          console.error('❌ [CallService] toggleMedia error:', error);\n          this.logger.error('Error toggling media:', error);\n          return throwError(\n            () => new Error('Erreur lors du changement des médias')\n          );\n        })\n      );\n  }\n\n  /**\n   * Démarre les médias pour un appel accepté\n   */\n  private async startMediaForCall(\n    incomingCall: IncomingCall,\n    call: Call\n  ): Promise<Call> {\n    console.log('🎥 [CallService] Call connected - playing connection sound');\n\n    // Jouer le son de connexion\n    this.play('call-connected');\n\n    // Mettre à jour l'état local\n    this.activeCall.next(call);\n    this.incomingCall.next(null); // Supprimer l'appel entrant\n\n    return call;\n  }\n\n  /**\n   * Active/désactive la vidéo\n   */\n  toggleVideo(): boolean {\n    this.isVideoEnabled = !this.isVideoEnabled;\n    console.log('📹 [CallService] Video toggled:', this.isVideoEnabled);\n    return this.isVideoEnabled;\n  }\n\n  /**\n   * Active/désactive l'audio\n   */\n  toggleAudio(): boolean {\n    this.isAudioEnabled = !this.isAudioEnabled;\n    console.log('🎤 [CallService] Audio toggled:', this.isAudioEnabled);\n    return this.isAudioEnabled;\n  }\n\n  /**\n   * Obtient l'état de la vidéo\n   */\n  getVideoEnabled(): boolean {\n    return this.isVideoEnabled;\n  }\n\n  /**\n   * Obtient l'état de l'audio\n   */\n  getAudioEnabled(): boolean {\n    return this.isAudioEnabled;\n  }\n\n  /**\n   * Arrête tous les sons\n   */\n  private stopAllSounds(): void {\n    console.log('🔇 [CallService] Stopping all sounds');\n    Object.keys(this.sounds).forEach((name) => {\n      this.stop(name);\n    });\n  }\n\n  /**\n   * Active les sons après interaction utilisateur (pour contourner les restrictions du navigateur)\n   */\n  public enableSounds(): void {\n    console.log('🔊 [CallService] Enabling sounds after user interaction');\n    Object.values(this.sounds).forEach((sound) => {\n      if (sound) {\n        sound.muted = false;\n        sound.volume = 0.7;\n        // Jouer et arrêter immédiatement pour \"débloquer\" l'audio\n        sound\n          .play()\n          .then(() => {\n            sound.pause();\n            sound.currentTime = 0;\n          })\n          .catch(() => {\n            // Ignorer les erreurs ici\n          });\n      }\n    });\n  }\n\n  /**\n   * Nettoie les ressources WebRTC\n   */\n  private cleanupWebRTC(): void {\n    console.log('🧹 [CallService] Cleaning up WebRTC resources');\n\n    // Arrêter les tracks du stream local\n    if (this.localStream) {\n      this.localStream.getTracks().forEach((track) => {\n        track.stop();\n      });\n      this.localStream = null;\n    }\n\n    // Nettoyer les éléments vidéo\n    if (this.localVideoElement) {\n      this.localVideoElement.srcObject = null;\n    }\n    if (this.remoteVideoElement) {\n      this.remoteVideoElement.srcObject = null;\n    }\n\n    // Fermer la PeerConnection\n    if (this.peerConnection) {\n      this.peerConnection.close();\n      this.peerConnection = null;\n    }\n\n    this.remoteStream = null;\n  }\n\n  ngOnDestroy(): void {\n    this.stopAllSounds();\n    this.cleanupWebRTC();\n  }\n}\n"], "mappings": ";AAEA,SAASA,eAAe,EAAcC,UAAU,EAAMC,IAAI,QAAQ,MAAM;AACxE,SAASC,GAAG,EAAEC,UAAU,EAAEC,SAAS,QAAQ,gBAAgB;AAC3D,SAEEC,QAAQ,EACRC,UAAU,QAGL,yBAAyB;AAChC,SACEC,sBAAsB,EACtBC,oBAAoB,EACpBC,oBAAoB,EACpBC,iBAAiB,EACjBC,0BAA0B,EAC1BC,0BAA0B,EAC1BC,gCAAgC,QAC3B,4BAA4B;;;;AAMnC,OAAM,MAAOC,WAAW;EAyBtBC,YAAoBC,MAAc,EAAUC,MAAqB;IAA7C,KAAAD,MAAM,GAANA,MAAM;IAAkB,KAAAC,MAAM,GAANA,MAAM;IAxBlD;IACQ,KAAAC,UAAU,GAAG,IAAInB,eAAe,CAAc,IAAI,CAAC;IACnD,KAAAoB,YAAY,GAAG,IAAIpB,eAAe,CAAsB,IAAI,CAAC;IAErE;IACO,KAAAqB,WAAW,GAAG,IAAI,CAACF,UAAU,CAACG,YAAY,EAAE;IAC5C,KAAAC,aAAa,GAAG,IAAI,CAACH,YAAY,CAACE,YAAY,EAAE;IAEvD;IACQ,KAAAE,MAAM,GAAwC,EAAE;IAChD,KAAAC,SAAS,GAA+B,EAAE;IAC1C,KAAAC,KAAK,GAAG,KAAK;IAErB;IACQ,KAAAC,cAAc,GAAG,IAAI;IACrB,KAAAC,cAAc,GAAG,IAAI;IAE7B;IACQ,KAAAC,cAAc,GAA6B,IAAI;IAC/C,KAAAC,WAAW,GAAuB,IAAI;IACtC,KAAAC,YAAY,GAAuB,IAAI;IACvC,KAAAC,iBAAiB,GAA4B,IAAI;IACjD,KAAAC,kBAAkB,GAA4B,IAAI;IAGxD,IAAI,CAACC,aAAa,EAAE;IACpB,IAAI,CAACC,uBAAuB,EAAE;IAC9B,IAAI,CAACC,gBAAgB,EAAE;EACzB;EAEA;;;EAGQD,uBAAuBA,CAAA;IAC7B;IACAE,UAAU,CAAC,MAAK;MACd,IAAI,CAACC,wBAAwB,EAAE;MAC/B,IAAI,CAACC,4BAA4B,EAAE;IACrC,CAAC,EAAE,IAAI,CAAC;IAER;IACAF,UAAU,CAAC,MAAK;MACd,IAAI,CAAC,IAAI,CAACjB,YAAY,CAACoB,KAAK,EAAE;QAC5B;QACA,IAAI,CAACF,wBAAwB,EAAE;QAC/B,IAAI,CAACC,4BAA4B,EAAE;;IAEvC,CAAC,EAAE,KAAK,CAAC;EACX;EAEA;;;EAGQH,gBAAgBA,CAAA;IACtBK,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;IAEtD;IACA,MAAMC,aAAa,GAAqB;MACtCC,UAAU,EAAE,CACV;QAAEC,IAAI,EAAE;MAA8B,CAAE,EACxC;QAAEA,IAAI,EAAE;MAA+B,CAAE;KAE5C;IAED,IAAI;MACF,IAAI,CAAChB,cAAc,GAAG,IAAIiB,iBAAiB,CAACH,aAAa,CAAC;MAC1D,IAAI,CAACI,yBAAyB,EAAE;MAChCN,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;KAC/D,CAAC,OAAOM,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CAAC,8CAA8C,EAAEA,KAAK,CAAC;;EAExE;EAEA;;;EAGQD,yBAAyBA,CAAA;IAC/B,IAAI,CAAC,IAAI,CAAClB,cAAc,EAAE;IAE1B;IACA,IAAI,CAACA,cAAc,CAACoB,OAAO,GAAIC,KAAK,IAAI;MACtCT,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;MACtD,IAAI,CAACX,YAAY,GAAGmB,KAAK,CAACC,OAAO,CAAC,CAAC,CAAC;MACpC,IAAI,IAAI,CAAClB,kBAAkB,EAAE;QAC3B,IAAI,CAACA,kBAAkB,CAACmB,SAAS,GAAG,IAAI,CAACrB,YAAY;QACrD;QACA,IAAI,CAACsB,mBAAmB,CAAC,IAAI,CAACpB,kBAAkB,CAAC;;IAErD,CAAC;IAED;IACA,IAAI,CAACJ,cAAc,CAACyB,cAAc,GAAIJ,KAAK,IAAI;MAC7C,IAAIA,KAAK,CAACK,SAAS,EAAE;QACnBd,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;QACvD;;IAEJ,CAAC;IAED;IACA,IAAI,CAACb,cAAc,CAAC2B,uBAAuB,GAAG,MAAK;MACjDf,OAAO,CAACC,GAAG,CACT,oCAAoC,EACpC,IAAI,CAACb,cAAc,EAAE4B,eAAe,CACrC;IACH,CAAC;EACH;EAEA;;;EAGQvB,aAAaA,CAAA;IACnB;IACA,IAAI,CAACwB,qBAAqB,EAAE;IAE5B;IACA,IAAI,CAACC,SAAS,CAAC,cAAc,EAAE,gCAAgC,CAAC;IAEhElB,OAAO,CAACC,GAAG,CACT,iEAAiE,CAClE;EACH;EAEA;;;EAGQgB,qBAAqBA,CAAA;IAC3B,IAAI;MACF;MACA,MAAME,YAAY,GAAG,KAAKC,MAAM,CAACC,YAAY,IAC1CD,MAAc,CAACE,kBAAkB,EAAC,CAAE;MAEvC;MACA,IAAI,CAACvC,MAAM,CAAC,oBAAoB,CAAC,GAC/B,IAAI,CAACwC,mBAAmB,CAACJ,YAAY,CAAC;MAExC;MACA,IAAI,CAACpC,MAAM,CAAC,0BAA0B,CAAC,GACrC,IAAI,CAACyC,oBAAoB,CAACL,YAAY,CAAC;MAEzC;MACA,IAAI,CAACpC,MAAM,CAAC,oBAAoB,CAAC,GAAG,IAAI,CAAC0C,cAAc,CAACN,YAAY,CAAC;MAErE;MACA,IAAI,CAACpC,MAAM,CAAC,wBAAwB,CAAC,GACnC,IAAI,CAAC2C,uBAAuB,CAACP,YAAY,CAAC;MAE5CnB,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;KACrE,CAAC,OAAOM,KAAK,EAAE;MACdP,OAAO,CAAC2B,IAAI,CACV,qDAAqD,EACrDpB,KAAK,CACN;;EAEL;EAEA;;;EAGQgB,mBAAmBA,CAACJ,YAA0B;IACpD,MAAMS,KAAK,GAAG,IAAIC,KAAK,EAAE;IACzBD,KAAK,CAACE,MAAM,GAAG,GAAG;IAElB,IAAI9C,SAAS,GAAG,KAAK;IACrB,IAAI+C,UAAU,GAAU,EAAE;IAEzBH,KAAa,CAACI,aAAa,GAAG,MAAK;MAClC,IAAIhD,SAAS,EAAE,OAAOiD,OAAO,CAACC,OAAO,EAAE;MAEvClD,SAAS,GAAG,IAAI;MAEhB,MAAMmD,UAAU,GAAGA,CAAA,KAAK;QACtB,IAAI,CAACnD,SAAS,EAAE;QAEhB;QACA,MAAMoD,MAAM,GAAG,CACb;UAAEC,IAAI,EAAE,MAAM;UAAEC,QAAQ,EAAE;QAAK,CAAE,EACjC;UAAED,IAAI,EAAE,MAAM;UAAEC,QAAQ,EAAE;QAAK,CAAE,EACjC;UAAED,IAAI,EAAE,MAAM;UAAEC,QAAQ,EAAE;QAAI,CAAE,EAChC;UAAED,IAAI,EAAE,MAAM;UAAEC,QAAQ,EAAE;QAAI,CAAE,EAChC;UAAED,IAAI,EAAE,MAAM;UAAEC,QAAQ,EAAE;QAAK,CAAE,EACjC;UAAED,IAAI,EAAE,MAAM;UAAEC,QAAQ,EAAE;QAAK,CAAE,EACjC;UAAED,IAAI,EAAE,MAAM;UAAEC,QAAQ,EAAE;QAAI,CAAE,EAChC;UAAED,IAAI,EAAE,MAAM;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAE;QAAA,CACnC;;QAED,IAAIC,WAAW,GAAGpB,YAAY,CAACoB,WAAW;QAE1CH,MAAM,CAACI,OAAO,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAI;UAC7B,IAAI,CAAC1D,SAAS,EAAE;UAEhB,MAAM2D,UAAU,GAAGxB,YAAY,CAACyB,gBAAgB,EAAE;UAClD,MAAMC,QAAQ,GAAG1B,YAAY,CAAC2B,UAAU,EAAE;UAE1CH,UAAU,CAACI,OAAO,CAACF,QAAQ,CAAC;UAC5BA,QAAQ,CAACE,OAAO,CAAC5B,YAAY,CAAC6B,WAAW,CAAC;UAE1CL,UAAU,CAACM,SAAS,CAAClD,KAAK,GAAG0C,IAAI,CAACJ,IAAI;UACtCM,UAAU,CAACO,IAAI,GAAG,QAAQ,CAAC,CAAC;UAE5B;UACAL,QAAQ,CAACM,IAAI,CAACC,cAAc,CAAC,CAAC,EAAEb,WAAW,CAAC;UAC5CM,QAAQ,CAACM,IAAI,CAACE,uBAAuB,CAAC,GAAG,EAAEd,WAAW,GAAG,IAAI,CAAC;UAC9DM,QAAQ,CAACM,IAAI,CAACE,uBAAuB,CACnC,GAAG,EACHd,WAAW,GAAGE,IAAI,CAACH,QAAQ,GAAG,GAAG,CAClC;UACDO,QAAQ,CAACM,IAAI,CAACG,4BAA4B,CACxC,IAAI,EACJf,WAAW,GAAGE,IAAI,CAACH,QAAQ,CAC5B;UAEDK,UAAU,CAACY,KAAK,CAAChB,WAAW,CAAC;UAC7BI,UAAU,CAACa,IAAI,CAACjB,WAAW,GAAGE,IAAI,CAACH,QAAQ,CAAC;UAE5CC,WAAW,IAAIE,IAAI,CAACH,QAAQ,GAAG,IAAI,CAAC,CAAC;QACvC,CAAC,CAAC;QAEF;QACA,MAAMmB,SAAS,GAAG7D,UAAU,CAAC,MAAK;UAChC,IAAIZ,SAAS,EAAE;YACbmD,UAAU,EAAE;;QAEhB,CAAC,EAAE,CAACI,WAAW,GAAGpB,YAAY,CAACoB,WAAW,GAAG,GAAG,IAAI,IAAI,CAAC;QAEzDR,UAAU,CAAC2B,IAAI,CAACD,SAAS,CAAC;MAC5B,CAAC;MAEDtB,UAAU,EAAE;MACZ,OAAOF,OAAO,CAACC,OAAO,EAAE;IAC1B,CAAC;IAEAN,KAAa,CAAC+B,aAAa,GAAG,MAAK;MAClC3E,SAAS,GAAG,KAAK;MACjB+C,UAAU,CAACS,OAAO,CAAEoB,EAAE,IAAKC,YAAY,CAACD,EAAE,CAAC,CAAC;MAC5C7B,UAAU,GAAG,EAAE;IACjB,CAAC;IAED,OAAOH,KAAK;EACd;EAEA;;;EAGQJ,oBAAoBA,CAACL,YAA0B;IACrD,MAAMS,KAAK,GAAG,IAAIC,KAAK,EAAE;IACzBD,KAAK,CAACE,MAAM,GAAG,GAAG;IAEjBF,KAAa,CAACI,aAAa,GAAG,MAAK;MAClC;MACA,MAAMI,MAAM,GAAG,CACb;QAAEC,IAAI,EAAE,MAAM;QAAEC,QAAQ,EAAE;MAAI,CAAE,EAChC;QAAED,IAAI,EAAE,MAAM;QAAEC,QAAQ,EAAE;MAAI,CAAE,EAChC;QAAED,IAAI,EAAE,MAAM;QAAEC,QAAQ,EAAE;MAAI,CAAE,EAChC;QAAED,IAAI,EAAE,MAAM;QAAEC,QAAQ,EAAE;MAAG,CAAE,CAAE;MAAA,CAClC;;MAED,IAAIC,WAAW,GAAGpB,YAAY,CAACoB,WAAW;MAE1CH,MAAM,CAACI,OAAO,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAI;QAC7B,MAAMC,UAAU,GAAGxB,YAAY,CAACyB,gBAAgB,EAAE;QAClD,MAAMC,QAAQ,GAAG1B,YAAY,CAAC2B,UAAU,EAAE;QAE1CH,UAAU,CAACI,OAAO,CAACF,QAAQ,CAAC;QAC5BA,QAAQ,CAACE,OAAO,CAAC5B,YAAY,CAAC6B,WAAW,CAAC;QAE1CL,UAAU,CAACM,SAAS,CAAClD,KAAK,GAAG0C,IAAI,CAACJ,IAAI;QACtCM,UAAU,CAACO,IAAI,GAAG,UAAU,CAAC,CAAC;QAE9B;QACAL,QAAQ,CAACM,IAAI,CAACC,cAAc,CAAC,CAAC,EAAEb,WAAW,CAAC;QAC5CM,QAAQ,CAACM,IAAI,CAACE,uBAAuB,CAAC,IAAI,EAAEd,WAAW,GAAG,IAAI,CAAC;QAC/DM,QAAQ,CAACM,IAAI,CAACE,uBAAuB,CACnC,IAAI,EACJd,WAAW,GAAGE,IAAI,CAACH,QAAQ,GAAG,GAAG,CAClC;QACDO,QAAQ,CAACM,IAAI,CAACG,4BAA4B,CACxC,IAAI,EACJf,WAAW,GAAGE,IAAI,CAACH,QAAQ,CAC5B;QAEDK,UAAU,CAACY,KAAK,CAAChB,WAAW,CAAC;QAC7BI,UAAU,CAACa,IAAI,CAACjB,WAAW,GAAGE,IAAI,CAACH,QAAQ,CAAC;QAE5CC,WAAW,IAAIE,IAAI,CAACH,QAAQ,GAAG,GAAG,CAAC,CAAC;MACtC,CAAC,CAAC;;MAEF,OAAOL,OAAO,CAACC,OAAO,EAAE;IAC1B,CAAC;IAED,OAAON,KAAK;EACd;EAEA;;;EAGQH,cAAcA,CAACN,YAA0B;IAC/C,MAAMS,KAAK,GAAG,IAAIC,KAAK,EAAE;IACzBD,KAAK,CAACE,MAAM,GAAG,GAAG;IAEjBF,KAAa,CAACI,aAAa,GAAG,MAAK;MAClC;MACA,MAAMI,MAAM,GAAG,CACb;QAAEC,IAAI,EAAE,MAAM;QAAEC,QAAQ,EAAE;MAAG,CAAE,EAC/B;QAAED,IAAI,EAAE,MAAM;QAAEC,QAAQ,EAAE;MAAG,CAAE,EAC/B;QAAED,IAAI,EAAE,MAAM;QAAEC,QAAQ,EAAE;MAAG,CAAE,EAC/B;QAAED,IAAI,EAAE,KAAK;QAAEC,QAAQ,EAAE;MAAG,CAAE,CAAE;MAAA,CACjC;;MAED,IAAIC,WAAW,GAAGpB,YAAY,CAACoB,WAAW;MAE1CH,MAAM,CAACI,OAAO,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAI;QAC7B,MAAMC,UAAU,GAAGxB,YAAY,CAACyB,gBAAgB,EAAE;QAClD,MAAMC,QAAQ,GAAG1B,YAAY,CAAC2B,UAAU,EAAE;QAE1CH,UAAU,CAACI,OAAO,CAACF,QAAQ,CAAC;QAC5BA,QAAQ,CAACE,OAAO,CAAC5B,YAAY,CAAC6B,WAAW,CAAC;QAE1CL,UAAU,CAACM,SAAS,CAAClD,KAAK,GAAG0C,IAAI,CAACJ,IAAI;QACtCM,UAAU,CAACO,IAAI,GAAG,MAAM,CAAC,CAAC;QAE1B;QACAL,QAAQ,CAACM,IAAI,CAACC,cAAc,CAAC,CAAC,EAAEb,WAAW,CAAC;QAC5CM,QAAQ,CAACM,IAAI,CAACE,uBAAuB,CAAC,GAAG,EAAEd,WAAW,GAAG,IAAI,CAAC;QAC9DM,QAAQ,CAACM,IAAI,CAACE,uBAAuB,CACnC,GAAG,EACHd,WAAW,GAAGE,IAAI,CAACH,QAAQ,GAAG,GAAG,CAClC;QACDO,QAAQ,CAACM,IAAI,CAACG,4BAA4B,CACxC,IAAI,EACJf,WAAW,GAAGE,IAAI,CAACH,QAAQ,CAC5B;QAEDK,UAAU,CAACY,KAAK,CAAChB,WAAW,CAAC;QAC7BI,UAAU,CAACa,IAAI,CAACjB,WAAW,GAAGE,IAAI,CAACH,QAAQ,CAAC;QAE5CC,WAAW,IAAIE,IAAI,CAACH,QAAQ,GAAG,GAAG,CAAC,CAAC;MACtC,CAAC,CAAC;;MAEF,OAAOL,OAAO,CAACC,OAAO,EAAE;IAC1B,CAAC;IAED,OAAON,KAAK;EACd;EAEA;;;EAGQF,uBAAuBA,CAC7BP,YAA0B;IAE1B,MAAMS,KAAK,GAAG,IAAIC,KAAK,EAAE;IACzBD,KAAK,CAACE,MAAM,GAAG,GAAG;IAEjBF,KAAa,CAACI,aAAa,GAAG,MAAK;MAClC;MACA,MAAM8B,KAAK,GAAG,CACZ;QAAEzB,IAAI,EAAE,MAAM;QAAEC,QAAQ,EAAE,IAAI;QAAEyB,KAAK,EAAE;MAAC,CAAE,EAC1C;QAAE1B,IAAI,EAAE,MAAM;QAAEC,QAAQ,EAAE,IAAI;QAAEyB,KAAK,EAAE;MAAG,CAAE,CAAE;MAAA,CAC/C;;MAEDD,KAAK,CAACtB,OAAO,CAAEC,IAAI,IAAI;QACrB7C,UAAU,CAAC,MAAK;UACd,MAAM+C,UAAU,GAAGxB,YAAY,CAACyB,gBAAgB,EAAE;UAClD,MAAMC,QAAQ,GAAG1B,YAAY,CAAC2B,UAAU,EAAE;UAE1CH,UAAU,CAACI,OAAO,CAACF,QAAQ,CAAC;UAC5BA,QAAQ,CAACE,OAAO,CAAC5B,YAAY,CAAC6B,WAAW,CAAC;UAE1CL,UAAU,CAACM,SAAS,CAAClD,KAAK,GAAG0C,IAAI,CAACJ,IAAI;UACtCM,UAAU,CAACO,IAAI,GAAG,UAAU,CAAC,CAAC;UAE9B,MAAMc,SAAS,GAAG7C,YAAY,CAACoB,WAAW;UAE1C;UACAM,QAAQ,CAACM,IAAI,CAACC,cAAc,CAAC,CAAC,EAAEY,SAAS,CAAC;UAC1CnB,QAAQ,CAACM,IAAI,CAACE,uBAAuB,CAAC,GAAG,EAAEW,SAAS,GAAG,IAAI,CAAC;UAC5DnB,QAAQ,CAACM,IAAI,CAACE,uBAAuB,CACnC,GAAG,EACHW,SAAS,GAAGvB,IAAI,CAACH,QAAQ,GAAG,GAAG,CAChC;UACDO,QAAQ,CAACM,IAAI,CAACG,4BAA4B,CACxC,IAAI,EACJU,SAAS,GAAGvB,IAAI,CAACH,QAAQ,CAC1B;UAEDK,UAAU,CAACY,KAAK,CAACS,SAAS,CAAC;UAC3BrB,UAAU,CAACa,IAAI,CAACQ,SAAS,GAAGvB,IAAI,CAACH,QAAQ,CAAC;QAC5C,CAAC,EAAEG,IAAI,CAACsB,KAAK,GAAG,IAAI,CAAC;MACvB,CAAC,CAAC;MAEF,OAAO9B,OAAO,CAACC,OAAO,EAAE;IAC1B,CAAC;IAED,OAAON,KAAK;EACd;EAEA;;;EAGQV,SAASA,CAAC+C,IAAY,EAAEC,IAAY;IAC1C,IAAI;MACF,MAAMtC,KAAK,GAAG,IAAIC,KAAK,EAAE;MAEzB;MACAD,KAAK,CAACuC,OAAO,GAAG,MAAM;MACtBvC,KAAK,CAACE,MAAM,GAAG,GAAG;MAElB;MACAF,KAAK,CAACwC,gBAAgB,CAAC,gBAAgB,EAAE,MAAK;QAC5CpE,OAAO,CAACC,GAAG,CACT,yBAAyBgE,IAAI,6BAA6BC,IAAI,EAAE,CACjE;MACH,CAAC,CAAC;MAEFtC,KAAK,CAACwC,gBAAgB,CAAC,OAAO,EAAGC,CAAC,IAAI;QACpCrE,OAAO,CAACO,KAAK,CACX,uCAAuC0D,IAAI,SAASC,IAAI,GAAG,EAC3DG,CAAC,CACF;QACDrE,OAAO,CAACC,GAAG,CACT,mCAAmCgE,IAAI,6BAA6B,CACrE;QAED;QACA,MAAMK,OAAO,GAAGJ,IAAI,CAACK,UAAU,CAAC,GAAG,CAAC,GAAGL,IAAI,CAACM,SAAS,CAAC,CAAC,CAAC,GAAGN,IAAI;QAC/D,IAAII,OAAO,KAAKJ,IAAI,EAAE;UACpBtE,UAAU,CAAC,MAAK;YACdgC,KAAK,CAAC6C,GAAG,GAAGH,OAAO;YACnB1C,KAAK,CAAC8C,IAAI,EAAE;UACd,CAAC,EAAE,GAAG,CAAC;;MAEX,CAAC,CAAC;MAEF9C,KAAK,CAACwC,gBAAgB,CAAC,OAAO,EAAE,MAAK;QACnC,IAAI,CAACpF,SAAS,CAACiF,IAAI,CAAC,GAAG,KAAK;QAC5BjE,OAAO,CAACC,GAAG,CAAC,0BAA0BgE,IAAI,QAAQ,CAAC;MACrD,CAAC,CAAC;MAEF;MACArC,KAAK,CAAC6C,GAAG,GAAGP,IAAI;MAChBtC,KAAK,CAAC8C,IAAI,EAAE;MAEZ,IAAI,CAAC3F,MAAM,CAACkF,IAAI,CAAC,GAAGrC,KAAK;MACzB,IAAI,CAAC5C,SAAS,CAACiF,IAAI,CAAC,GAAG,KAAK;MAE5BjE,OAAO,CAACC,GAAG,CAAC,kCAAkCgE,IAAI,SAASC,IAAI,EAAE,CAAC;KACnE,CAAC,OAAO3D,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CACX,oDAAoD0D,IAAI,GAAG,EAC3D1D,KAAK,CACN;;EAEL;EAEA;;;EAGQoE,IAAIA,CAACV,IAAY,EAAEW,IAAA,GAAgB,KAAK;IAC9C,IAAI,IAAI,CAAC3F,KAAK,EAAE;MACde,OAAO,CAACC,GAAG,CAAC,0BAA0BgE,IAAI,QAAQ,CAAC;MACnD;;IAGF,IAAI;MACF;MACA,IAAIY,KAAK;MACT,IACEZ,IAAI,KAAK,UAAU,IACnBA,IAAI,KAAK,gBAAgB,IACzBA,IAAI,KAAK,UAAU,EACnB;QACA,MAAMa,aAAa,GAAG,GAAGb,IAAI,YAAY;QACzCY,KAAK,GAAG,IAAI,CAAC9F,MAAM,CAAC+F,aAAa,CAAC;QAClC,IAAID,KAAK,EAAE;UACT7E,OAAO,CAACC,GAAG,CACT,yDAAyDgE,IAAI,EAAE,CAChE;;OAEJ,MAAM;QACL;QACAY,KAAK,GAAG,IAAI,CAAC9F,MAAM,CAACkF,IAAI,CAAC;QACzB,IAAI,CAACY,KAAK,IAAIA,KAAK,CAACtE,KAAK,EAAE;UACzB,MAAMuE,aAAa,GAAG,GAAGb,IAAI,YAAY;UACzCY,KAAK,GAAG,IAAI,CAAC9F,MAAM,CAAC+F,aAAa,CAAC;UAClC,IAAID,KAAK,EAAE;YACT7E,OAAO,CAACC,GAAG,CAAC,8CAA8CgE,IAAI,EAAE,CAAC;;;;MAKvE,IAAI,CAACY,KAAK,EAAE;QACV7E,OAAO,CAAC2B,IAAI,CACV,0BAA0BsC,IAAI,6CAA6C,CAC5E;QACD;QACA,IAAI,CAACc,cAAc,CACjBd,IAAI,KAAK,UAAU,GAAG,GAAG,GAAGA,IAAI,KAAK,gBAAgB,GAAG,IAAI,GAAG,GAAG,CACnE;QACD;;MAGFY,KAAK,CAACD,IAAI,GAAGA,IAAI;MACjBC,KAAK,CAAC/C,MAAM,GAAG,GAAG;MAElB,IAAI,CAAC,IAAI,CAAC9C,SAAS,CAACiF,IAAI,CAAC,EAAE;QACzBjE,OAAO,CAACC,GAAG,CAAC,mCAAmCgE,IAAI,WAAWW,IAAI,GAAG,CAAC;QAEtE;QACA,IAAKC,KAAa,CAAC7C,aAAa,EAAE;UAC/B6C,KAAa,CACX7C,aAAa,EAAE,CACfgD,IAAI,CAAC,MAAK;YACThF,OAAO,CAACC,GAAG,CACT,mCAAmCgE,IAAI,uBAAuB,CAC/D;YACD,IAAI,CAACjF,SAAS,CAACiF,IAAI,CAAC,GAAG,IAAI;YAE3B;YACA,IAAIA,IAAI,KAAK,UAAU,IAAI,CAACW,IAAI,EAAE;cAChC;cACAhF,UAAU,CAAC,MAAK;gBACd,IAAI,CAACZ,SAAS,CAACiF,IAAI,CAAC,GAAG,KAAK;cAC9B,CAAC,EAAE,IAAI,CAAC;aACT,MAAM,IAAIA,IAAI,KAAK,UAAU,EAAE;cAC9B;cACArE,UAAU,CACR,MAAK;gBACH,IAAI,CAACZ,SAAS,CAACiF,IAAI,CAAC,GAAG,KAAK;cAC9B,CAAC,EACDA,IAAI,KAAK,gBAAgB,GAAG,IAAI,GAAG,IAAI,CACxC;;UAEL,CAAC,CAAC,CACDgB,KAAK,CAAE1E,KAAU,IAAI;YACpBP,OAAO,CAACO,KAAK,CACX,iDAAiD0D,IAAI,GAAG,EACxD1D,KAAK,CACN;UACH,CAAC,CAAC;SACL,MAAM;UACL;UACAsE,KAAK,CAACtC,WAAW,GAAG,CAAC;UACrBsC,KAAK,CACFF,IAAI,EAAE,CACNK,IAAI,CAAC,MAAK;YACThF,OAAO,CAACC,GAAG,CACT,yBAAyBgE,IAAI,uBAAuB,CACrD;YACD,IAAI,CAACjF,SAAS,CAACiF,IAAI,CAAC,GAAG,IAAI;UAC7B,CAAC,CAAC,CACDgB,KAAK,CAAE1E,KAAK,IAAI;YACfP,OAAO,CAACO,KAAK,CACX,uCAAuC0D,IAAI,GAAG,EAC9C1D,KAAK,CACN;YAED;YACA,MAAMuE,aAAa,GAAG,GAAGb,IAAI,YAAY;YACzC,MAAMiB,cAAc,GAAG,IAAI,CAACnG,MAAM,CAAC+F,aAAa,CAAC;YACjD,IAAII,cAAc,IAAKA,cAAsB,CAAClD,aAAa,EAAE;cAC3DhC,OAAO,CAACC,GAAG,CACT,wDAAwDgE,IAAI,EAAE,CAC/D;cACD,IAAI,CAACU,IAAI,CAACV,IAAI,EAAEW,IAAI,CAAC;aACtB,MAAM;cACL;cACA,IAAI,CAACG,cAAc,CACjBd,IAAI,KAAK,UAAU,GACf,GAAG,GACHA,IAAI,KAAK,gBAAgB,GACzB,IAAI,GACJ,GAAG,CACR;;UAEL,CAAC,CAAC;;OAEP,MAAM;QACLjE,OAAO,CAACC,GAAG,CAAC,0BAA0BgE,IAAI,kBAAkB,CAAC;;KAEhE,CAAC,OAAO1D,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CACX,4CAA4C0D,IAAI,GAAG,EACnD1D,KAAK,CACN;MACD;MACA,IAAI,CAACwE,cAAc,CACjBd,IAAI,KAAK,UAAU,GAAG,GAAG,GAAGA,IAAI,KAAK,gBAAgB,GAAG,IAAI,GAAG,GAAG,CACnE;;EAEL;EAEA;;;EAGQc,cAAcA,CAAC9B,SAAiB;IACtC,IAAI;MACF,MAAM9B,YAAY,GAAG,KAAKC,MAAM,CAACC,YAAY,IAC1CD,MAAc,CAACE,kBAAkB,EAAC,CAAE;MACvC,MAAMqB,UAAU,GAAGxB,YAAY,CAACyB,gBAAgB,EAAE;MAClD,MAAMC,QAAQ,GAAG1B,YAAY,CAAC2B,UAAU,EAAE;MAE1CH,UAAU,CAACI,OAAO,CAACF,QAAQ,CAAC;MAC5BA,QAAQ,CAACE,OAAO,CAAC5B,YAAY,CAAC6B,WAAW,CAAC;MAE1CL,UAAU,CAACM,SAAS,CAAClD,KAAK,GAAGkD,SAAS;MACtCN,UAAU,CAACO,IAAI,GAAG,MAAM;MAExBL,QAAQ,CAACM,IAAI,CAACC,cAAc,CAAC,GAAG,EAAEjC,YAAY,CAACoB,WAAW,CAAC;MAC3DM,QAAQ,CAACM,IAAI,CAACG,4BAA4B,CACxC,IAAI,EACJnC,YAAY,CAACoB,WAAW,GAAG,GAAG,CAC/B;MAEDI,UAAU,CAACY,KAAK,CAACpC,YAAY,CAACoB,WAAW,CAAC;MAC1CI,UAAU,CAACa,IAAI,CAACrC,YAAY,CAACoB,WAAW,GAAG,GAAG,CAAC;MAE/CvC,OAAO,CAACC,GAAG,CAAC,2CAA2CgD,SAAS,IAAI,CAAC;KACtE,CAAC,OAAO1C,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CAAC,6CAA6C,EAAEA,KAAK,CAAC;;EAEvE;EAEA;;;EAGQiD,IAAIA,CAACS,IAAY;IACvB,IAAI;MACF,IAAIY,KAAK,GAAG,IAAI,CAAC9F,MAAM,CAACkF,IAAI,CAAC;MAE7B;MACA,IAAI,CAACY,KAAK,EAAE;QACVA,KAAK,GAAG,IAAI,CAAC9F,MAAM,CAAC,GAAGkF,IAAI,YAAY,CAAC;;MAG1C,IAAI,CAACY,KAAK,EAAE;QACV7E,OAAO,CAAC2B,IAAI,CAAC,0BAA0BsC,IAAI,yBAAyB,CAAC;QACrE;;MAGF,IAAI,IAAI,CAACjF,SAAS,CAACiF,IAAI,CAAC,EAAE;QACxBjE,OAAO,CAACC,GAAG,CAAC,oCAAoCgE,IAAI,EAAE,CAAC;QAEvD;QACA,IAAKY,KAAa,CAAClB,aAAa,EAAE;UAC/BkB,KAAa,CAAClB,aAAa,EAAE;SAC/B,MAAM;UACLkB,KAAK,CAACM,KAAK,EAAE;UACbN,KAAK,CAACtC,WAAW,GAAG,CAAC;;QAGvB,IAAI,CAACvD,SAAS,CAACiF,IAAI,CAAC,GAAG,KAAK;OAC7B,MAAM;QACLjE,OAAO,CAACC,GAAG,CAAC,0BAA0BgE,IAAI,kBAAkB,CAAC;;KAEhE,CAAC,OAAO1D,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CAAC,wCAAwC0D,IAAI,GAAG,EAAE1D,KAAK,CAAC;;EAEzE;EAEA;;;EAGQV,wBAAwBA,CAAA;IAC9B;IAEA,IAAI;MACF,IAAI,CAACrB,MAAM,CACR4G,SAAS,CAAiC;QACzCC,KAAK,EAAEjH,0BAA0B;QACjCkH,WAAW,EAAE,KAAK,CAAE;OACrB,CAAC,CACDF,SAAS,CAAC;QACTG,IAAI,EAAEA,CAAC;UAAEC,IAAI;UAAEC;QAAM,CAAE,KAAI;UACzB,IAAIA,MAAM,EAAE;YACVzF,OAAO,CAAC2B,IAAI,CACV,kDAAkD,EAClD8D,MAAM,CACP;;UAGH,IAAID,IAAI,EAAE7G,YAAY,EAAE;YACtB;YACA,IAAI,CAAC+G,kBAAkB,CAACF,IAAI,CAAC7G,YAAY,CAAC;;QAE9C,CAAC;QACD4B,KAAK,EAAGA,KAAK,IAAI;UACfP,OAAO,CAACO,KAAK,CACX,sDAAsD,EACtDA,KAAK,CACN;UAED;UACAX,UAAU,CAAC,MAAK;YACd,IAAI,CAACC,wBAAwB,EAAE;UACjC,CAAC,EAAE,KAAK,CAAC;QACX,CAAC;QACD8F,QAAQ,EAAEA,CAAA,KAAK;UACb;UACA;UACA/F,UAAU,CAAC,MAAK;YACd,IAAI,CAACC,wBAAwB,EAAE;UACjC,CAAC,EAAE,IAAI,CAAC;QACV;OACD,CAAC;KACL,CAAC,OAAOU,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CAAC,gDAAgD,EAAEA,KAAK,CAAC;MACtE;MACAX,UAAU,CAAC,MAAK;QACd,IAAI,CAACC,wBAAwB,EAAE;MACjC,CAAC,EAAE,KAAK,CAAC;;EAEb;EAEA;;;EAGO+F,wBAAwBA,CAAA;IAC7B;IACA,IAAI,CAAC/F,wBAAwB,EAAE;IAC/B,IAAI,CAACC,4BAA4B,EAAE;EACrC;EAEA;;;EAGO+F,UAAUA,CAAA;IACf7F,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;IAEjD;IACAD,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;IACrC,IAAI,CAAC0E,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC;IAE3B/E,UAAU,CAAC,MAAK;MACd,IAAI,CAAC4D,IAAI,CAAC,UAAU,CAAC;MACrBxD,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;MAC3C,IAAI,CAAC0E,IAAI,CAAC,gBAAgB,CAAC;IAC7B,CAAC,EAAE,IAAI,CAAC;IAER/E,UAAU,CAAC,MAAK;MACdI,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;MACrC,IAAI,CAAC0E,IAAI,CAAC,UAAU,CAAC;IACvB,CAAC,EAAE,IAAI,CAAC;EACV;EAEA;;;EAGOmB,YAAYA,CAAA;IACjB9F,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC;IAChED,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAE8F,MAAM,CAACC,IAAI,CAAC,IAAI,CAACjH,MAAM,CAAC,CAAC;IAE3E;IACA,MAAMkH,QAAQ,GAAG,IAAI,CAAClH,MAAM,CAAC,UAAU,CAAC;IACxC,MAAMmH,iBAAiB,GAAG,IAAI,CAACnH,MAAM,CAAC,oBAAoB,CAAC;IAE3D,IAAIkH,QAAQ,EAAE;MACZjG,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAE;QACjDwE,GAAG,EAAEwB,QAAQ,CAACxB,GAAG;QACjB0B,UAAU,EAAEF,QAAQ,CAACE,UAAU;QAC/B5F,KAAK,EAAE0F,QAAQ,CAAC1F,KAAK;QACrB+B,QAAQ,EAAE2D,QAAQ,CAAC3D;OACpB,CAAC;KACH,MAAM;MACLtC,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;;IAGvD,IAAIiG,iBAAiB,EAAE;MACrBlG,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;;IAGzD;IACAD,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;IACpE,IAAI,CAAC0E,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC;IAE3B;IACA/E,UAAU,CAAC,MAAK;MACd,IAAI,CAAC4D,IAAI,CAAC,UAAU,CAAC;MACrBxD,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;IACvD,CAAC,EAAE,IAAI,CAAC;EACV;EAEA;;;EAGOmG,mBAAmBA,CAAA;IACxBpG,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;IAE/D;IACA;IACA,IAAI,CAAC0E,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC;IAE3B/E,UAAU,CAAC,MAAK;MACd,IAAI,CAAC4D,IAAI,CAAC,UAAU,CAAC;MACrB;MACA,IAAI,CAACmB,IAAI,CAAC,gBAAgB,CAAC;IAC7B,CAAC,EAAE,IAAI,CAAC;IAER/E,UAAU,CAAC,MAAK;MACd;MACA,IAAI,CAAC+E,IAAI,CAAC,UAAU,CAAC;IACvB,CAAC,EAAE,IAAI,CAAC;IAER/E,UAAU,CAAC,MAAK;MACd;MACA,IAAI,CAAC+E,IAAI,CAAC,cAAc,CAAC;IAC3B,CAAC,EAAE,IAAI,CAAC;IAER/E,UAAU,CAAC,MAAK;MACdI,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;IAC9C,CAAC,EAAE,KAAK,CAAC;EACX;EAEA;;;EAGOoG,gBAAgBA,CAAA;IACrBrG,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;IAC7D,IAAI,CAAC0E,IAAI,CAAC,cAAc,CAAC;EAC3B;EAEA;;;EAGQ7E,4BAA4BA,CAAA;IAClCE,OAAO,CAACC,GAAG,CACT,gEAAgE,CACjE;IAED,IAAI;MACF,IAAI,CAACzB,MAAM,CACR4G,SAAS,CAA8B;QACtCC,KAAK,EAAEhH,gCAAgC;QACvCiH,WAAW,EAAE;OACd,CAAC,CACDF,SAAS,CAAC;QACTG,IAAI,EAAEA,CAAC;UAAEC,IAAI;UAAEC;QAAM,CAAE,KAAI;UACzB,IAAIA,MAAM,EAAE;YACVzF,OAAO,CAAC2B,IAAI,CACV,8DAA8D,EAC9D8D,MAAM,CACP;;UAGH,IAAID,IAAI,EAAEc,iBAAiB,EAAE;YAC3BtG,OAAO,CAACC,GAAG,CACT,uCAAuC,EACvCuF,IAAI,CAACc,iBAAiB,CACvB;YACD,IAAI,CAACC,sBAAsB,CAACf,IAAI,CAACc,iBAAiB,CAAC;;QAEvD,CAAC;QACD/F,KAAK,EAAGA,KAAK,IAAI;UACfP,OAAO,CAACO,KAAK,CACX,oDAAoD,EACpDA,KAAK,CACN;UACD;UACAX,UAAU,CAAC,MAAK;YACd,IAAI,CAACE,4BAA4B,EAAE;UACrC,CAAC,EAAE,IAAI,CAAC;QACV,CAAC;QACD6F,QAAQ,EAAEA,CAAA,KAAK;UACb3F,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;UAClE;UACAL,UAAU,CAAC,MAAK;YACd,IAAI,CAACE,4BAA4B,EAAE;UACrC,CAAC,EAAE,IAAI,CAAC;QACV;OACD,CAAC;KACL,CAAC,OAAOS,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CACX,4DAA4D,EAC5DA,KAAK,CACN;MACDX,UAAU,CAAC,MAAK;QACd,IAAI,CAACE,4BAA4B,EAAE;MACrC,CAAC,EAAE,IAAI,CAAC;;EAEZ;EAEA;;;EAGQ4F,kBAAkBA,CAACc,IAAkB;IAC3CxG,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAE;MACtDwG,MAAM,EAAED,IAAI,CAAC5C,EAAE;MACf8C,QAAQ,EAAEF,IAAI,CAACtD,IAAI;MACnByD,MAAM,EAAEH,IAAI,CAACG,MAAM,EAAEC,QAAQ;MAC7BC,cAAc,EAAEL,IAAI,CAACK;KACtB,CAAC;IAEF,IAAI,CAAClI,YAAY,CAAC4G,IAAI,CAACiB,IAAI,CAAC;IAC5B,IAAI,CAAC7B,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC;IAE3B3E,OAAO,CAACC,GAAG,CACT,iEAAiE,CAClE;EACH;EAEA;;;EAGQsG,sBAAsBA,CAACC,IAAU;IACvCxG,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEuG,IAAI,CAACM,MAAM,CAAC;IAEjE,QAAQN,IAAI,CAACM,MAAM;MACjB,KAAKhJ,UAAU,CAACiJ,QAAQ;QACtB/G,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;QAChD,IAAI,CAACuD,IAAI,CAAC,UAAU,CAAC;QACrB,IAAI,CAACmB,IAAI,CAAC,UAAU,CAAC;QACrB,IAAI,CAACjG,UAAU,CAAC6G,IAAI,CAAC,IAAI,CAAC;QAC1B,IAAI,CAAC5G,YAAY,CAAC4G,IAAI,CAAC,IAAI,CAAC;QAC5B;MAEF,KAAKzH,UAAU,CAACkJ,KAAK;QACnBhH,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;QAC1C,IAAI,CAACgH,aAAa,EAAE;QACpB,IAAI,CAACtC,IAAI,CAAC,UAAU,CAAC;QACrB,IAAI,CAACjG,UAAU,CAAC6G,IAAI,CAAC,IAAI,CAAC;QAC1B,IAAI,CAAC5G,YAAY,CAAC4G,IAAI,CAAC,IAAI,CAAC;QAC5B;MAEF,KAAKzH,UAAU,CAACoJ,SAAS;QACvBlH,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;QAC7C,IAAI,CAACuD,IAAI,CAAC,UAAU,CAAC;QACrB,IAAI,CAACmB,IAAI,CAAC,gBAAgB,CAAC;QAC3B,IAAI,CAACjG,UAAU,CAAC6G,IAAI,CAACiB,IAAI,CAAC;QAC1B,IAAI,CAAC7H,YAAY,CAAC4G,IAAI,CAAC,IAAI,CAAC;QAC5B;MAEF,KAAKzH,UAAU,CAACqJ,OAAO;QACrBnH,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;QAC/C,IAAI,CAAC0E,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC;QAC3B;MAEF;QACE3E,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEuG,IAAI,CAACM,MAAM,CAAC;QACjE;;EAEN;EAEA;;;EAGcM,YAAYA,CAACV,QAAkB;IAAA,IAAAW,KAAA;IAAA,OAAAC,iBAAA;MAC3CtH,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAEyG,QAAQ,CAAC;MAEjE,MAAMa,WAAW,GAA2B;QAC1C3F,KAAK,EAAE,IAAI;QACX4F,KAAK,EAAEd,QAAQ,KAAK7I,QAAQ,CAAC4J;OAC9B;MAED,IAAI;QACF,MAAMC,MAAM,SAASC,SAAS,CAACC,YAAY,CAACR,YAAY,CAACG,WAAW,CAAC;QACrEvH,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;QAClDoH,KAAI,CAAChI,WAAW,GAAGqI,MAAM;QAEzB;QACA,IAAIL,KAAI,CAAC9H,iBAAiB,IAAImH,QAAQ,KAAK7I,QAAQ,CAAC4J,KAAK,EAAE;UACzDJ,KAAI,CAAC9H,iBAAiB,CAACoB,SAAS,GAAG+G,MAAM;;QAG3C,OAAOA,MAAM;OACd,CAAC,OAAOnH,KAAK,EAAE;QACdP,OAAO,CAACO,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;QACjE,MAAM,IAAIsH,KAAK,CAAC,6CAA6C,CAAC;;IAC/D;EACH;EAEA;;;EAGQC,8BAA8BA,CAACJ,MAAmB;IACxD,IAAI,CAAC,IAAI,CAACtI,cAAc,EAAE;MACxBY,OAAO,CAACO,KAAK,CAAC,8CAA8C,CAAC;MAC7D;;IAGFP,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC;IACtEyH,MAAM,CAACK,SAAS,EAAE,CAACvF,OAAO,CAAEwF,KAAK,IAAI;MACnC,IAAI,CAAC5I,cAAe,CAAC6I,QAAQ,CAACD,KAAK,EAAEN,MAAM,CAAC;IAC9C,CAAC,CAAC;EACJ;EAEA;;;EAGcQ,WAAWA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAb,iBAAA;MACvB,IAAI,CAACa,MAAI,CAAC/I,cAAc,EAAE;QACxB,MAAM,IAAIyI,KAAK,CAAC,8BAA8B,CAAC;;MAGjD7H,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;MACrD,MAAMmI,KAAK,SAASD,MAAI,CAAC/I,cAAc,CAAC8I,WAAW,EAAE;MACrD,MAAMC,MAAI,CAAC/I,cAAc,CAACiJ,mBAAmB,CAACD,KAAK,CAAC;MACpD,OAAOA,KAAK;IAAC;EACf;EAEA;;;EAGcE,YAAYA,CACxBF,KAAgC;IAAA,IAAAG,MAAA;IAAA,OAAAjB,iBAAA;MAEhC,IAAI,CAACiB,MAAI,CAACnJ,cAAc,EAAE;QACxB,MAAM,IAAIyI,KAAK,CAAC,8BAA8B,CAAC;;MAGjD7H,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;MACtD,MAAMsI,MAAI,CAACnJ,cAAc,CAACoJ,oBAAoB,CAACJ,KAAK,CAAC;MACrD,MAAMK,MAAM,SAASF,MAAI,CAACnJ,cAAc,CAACkJ,YAAY,EAAE;MACvD,MAAMC,MAAI,CAACnJ,cAAc,CAACiJ,mBAAmB,CAACI,MAAM,CAAC;MACrD,OAAOA,MAAM;IAAC;EAChB;EAEA;;;EAGOC,gBAAgBA,CACrBC,UAA4B,EAC5BC,WAA6B;IAE7B5I,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;IACtD,IAAI,CAACV,iBAAiB,GAAGoJ,UAAU;IACnC,IAAI,CAACnJ,kBAAkB,GAAGoJ,WAAW;IAErC;IACA,IAAI;MACF,IAAI,CAACC,kBAAkB,CAACF,UAAU,EAAEC,WAAW,CAAC;KACjD,CAAC,OAAOrI,KAAK,EAAE;MACdP,OAAO,CAAC2B,IAAI,CAAC,4CAA4C,EAAEpB,KAAK,CAAC;;IAGnE;IACA,IAAI,IAAI,CAAClB,WAAW,IAAIsJ,UAAU,EAAE;MAClCA,UAAU,CAAChI,SAAS,GAAG,IAAI,CAACtB,WAAW;MACvC,IAAI;QACF,IAAI,CAACuB,mBAAmB,CAAC+H,UAAU,CAAC;OACrC,CAAC,OAAOpI,KAAK,EAAE;QACdP,OAAO,CAAC2B,IAAI,CACV,uDAAuD,EACvDpB,KAAK,CACN;;;IAGL,IAAI,IAAI,CAACjB,YAAY,IAAIsJ,WAAW,EAAE;MACpCA,WAAW,CAACjI,SAAS,GAAG,IAAI,CAACrB,YAAY;MACzC,IAAI;QACF,IAAI,CAACsB,mBAAmB,CAACgI,WAAW,CAAC;OACtC,CAAC,OAAOrI,KAAK,EAAE;QACdP,OAAO,CAAC2B,IAAI,CACV,wDAAwD,EACxDpB,KAAK,CACN;;;EAGP;EAEA;;;EAGQsI,kBAAkBA,CACxBF,UAA4B,EAC5BC,WAA6B;IAE7B5I,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;IAEzD;IACA0I,UAAU,CAAC7G,MAAM,GAAG,CAAC,CAAC,CAAC;IACvB8G,WAAW,CAAC9G,MAAM,GAAG,CAAC,CAAC,CAAC;IAExB;IACA6G,UAAU,CAACG,QAAQ,GAAG,IAAI;IAC1BF,WAAW,CAACE,QAAQ,GAAG,IAAI;IAE3B;IACAF,WAAW,CAACxE,gBAAgB,CAAC,gBAAgB,EAAE,MAAK;MAClDpE,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;IAC9D,CAAC,CAAC;IAEF2I,WAAW,CAACxE,gBAAgB,CAAC,SAAS,EAAE,MAAK;MAC3CpE,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;MACrD,IAAI,CAACW,mBAAmB,CAACgI,WAAW,CAAC;IACvC,CAAC,CAAC;IAEFA,WAAW,CAACxE,gBAAgB,CAAC,MAAM,EAAE,MAAK;MACxCpE,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;IAC9D,CAAC,CAAC;IAEF2I,WAAW,CAACxE,gBAAgB,CAAC,OAAO,EAAE,MAAK;MACzCpE,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;IACrD,CAAC,CAAC;EACJ;EAEA;;;EAGQW,mBAAmBA,CAACmI,YAA8B;IACxD/I,OAAO,CAACC,GAAG,CACT,uDAAuD,EACvD8I,YAAY,KAAK,IAAI,CAACxJ,iBAAiB,GAAG,OAAO,GAAG,QAAQ,CAC7D;IAED;IACAwJ,YAAY,CACTpE,IAAI,EAAE,CACNK,IAAI,CAAC,MAAK;MACThF,OAAO,CAACC,GAAG,CACT,2DAA2D,CAC5D;IACH,CAAC,CAAC,CACDgF,KAAK,CAAE1E,KAAK,IAAI;MACfP,OAAO,CAAC2B,IAAI,CACV,iEAAiE,EACjEpB,KAAK,CACN;MAED;MACA,MAAMyI,iBAAiB,GAAGA,CAAA,KAAK;QAC7BD,YAAY,CACTpE,IAAI,EAAE,CACNK,IAAI,CAAC,MAAK;UACThF,OAAO,CAACC,GAAG,CACT,qEAAqE,CACtE;UACDgJ,QAAQ,CAACC,mBAAmB,CAAC,OAAO,EAAEF,iBAAiB,CAAC;UACxDC,QAAQ,CAACC,mBAAmB,CAAC,SAAS,EAAEF,iBAAiB,CAAC;QAC5D,CAAC,CAAC,CACD/D,KAAK,CAAEkE,GAAG,IAAI;UACbnJ,OAAO,CAACO,KAAK,CACX,mDAAmD,EACnD4I,GAAG,CACJ;QACH,CAAC,CAAC;MACN,CAAC;MAEDF,QAAQ,CAAC7E,gBAAgB,CAAC,OAAO,EAAE4E,iBAAiB,CAAC;MACrDC,QAAQ,CAAC7E,gBAAgB,CAAC,SAAS,EAAE4E,iBAAiB,CAAC;IACzD,CAAC,CAAC;EACN;EAEA;;;EAGAI,YAAYA,CACVC,WAAmB,EACnB3C,QAAkB,EAClBG,cAAuB;IAEvB7G,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAE;MAC/CoJ,WAAW;MACX3C,QAAQ;MACRG;KACD,CAAC;IAEF,IAAI,CAACwC,WAAW,EAAE;MAChB,MAAM9I,KAAK,GAAG,IAAIsH,KAAK,CAAC,0BAA0B,CAAC;MACnD7H,OAAO,CAACO,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3D,OAAO/C,UAAU,CAAC,MAAM+C,KAAK,CAAC;;IAGhC;IACA,MAAMkG,MAAM,GAAG,QAAQ6C,IAAI,CAACC,GAAG,EAAE,IAAIC,IAAI,CAACC,MAAM,EAAE,CAC/CC,QAAQ,CAAC,EAAE,CAAC,CACZC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;IAEjB;IACA,OAAOlM,IAAI,CAAC,IAAI,CAACmM,iBAAiB,CAAClD,QAAQ,CAAC,CAAC,CAACmD,IAAI,CAChDjM,SAAS,CAAEwK,KAAK,IAAI;MAClB,MAAM0B,SAAS,GAAG;QAChBT,WAAW;QACX3C,QAAQ,EAAEA,QAAQ;QAClBD,MAAM;QACN2B,KAAK,EAAE2B,IAAI,CAACC,SAAS,CAAC5B,KAAK,CAAC;QAC5BvB;OACD;MAED7G,OAAO,CAACC,GAAG,CACT,kDAAkD,EAClD6J,SAAS,CACV;MAED,OAAO,IAAI,CAACtL,MAAM,CACfyL,MAAM,CAAyB;QAC9BC,QAAQ,EAAEnM,sBAAsB;QAChC+L;OACD,CAAC,CACDD,IAAI,CACHnM,GAAG,CAAEyM,MAAM,IAAI;QACbnK,OAAO,CAACC,GAAG,CACT,8CAA8C,EAC9CkK,MAAM,CACP;QAED,IAAI,CAACA,MAAM,CAAC3E,IAAI,EAAE4D,YAAY,EAAE;UAC9B,MAAM,IAAIvB,KAAK,CAAC,mCAAmC,CAAC;;QAGtD,MAAMrB,IAAI,GAAG2D,MAAM,CAAC3E,IAAI,CAAC4D,YAAY;QACrCpJ,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE;UAC5C2D,EAAE,EAAE4C,IAAI,CAAC5C,EAAE;UACXV,IAAI,EAAEsD,IAAI,CAACtD,IAAI;UACf4D,MAAM,EAAEN,IAAI,CAACM,MAAM;UACnBH,MAAM,EAAEH,IAAI,CAACG,MAAM,EAAEC,QAAQ;UAC7BwD,SAAS,EAAE5D,IAAI,CAAC4D,SAAS,EAAExD;SAC5B,CAAC;QAEF;QACA,IAAI,CAAClI,UAAU,CAAC6G,IAAI,CAACiB,IAAI,CAAC;QAE1B,OAAOA,IAAI;MACb,CAAC,CAAC,EACF7I,UAAU,CAAE4C,KAAK,IAAI;QACnBP,OAAO,CAACO,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;QAC3D,IAAI,CAAC9B,MAAM,CAAC8B,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAElD,IAAI8J,YAAY,GAAG,wCAAwC;QAC3D,IAAI9J,KAAK,CAAC+J,YAAY,EAAE;UACtBD,YAAY,GAAG,4BAA4B;SAC5C,MAAM,IAAI9J,KAAK,CAACgK,aAAa,EAAEC,MAAM,GAAG,CAAC,EAAE;UAC1CH,YAAY,GAAG9J,KAAK,CAACgK,aAAa,CAAC,CAAC,CAAC,CAACE,OAAO,IAAIJ,YAAY;;QAG/D,OAAO7M,UAAU,CAAC,MAAM,IAAIqK,KAAK,CAACwC,YAAY,CAAC,CAAC;MAClD,CAAC,CAAC,CACH;IACL,CAAC,CAAC,EACF1M,UAAU,CAAE4C,KAAK,IAAI;MACnBP,OAAO,CAACO,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD,OAAO/C,UAAU,CAAC,MAAM,IAAIqK,KAAK,CAAC,iBAAiB,GAAGtH,KAAK,CAACkK,OAAO,CAAC,CAAC;IACvE,CAAC,CAAC,CACH;EACH;EAEA;;;EAGcb,iBAAiBA,CAC7BlD,QAAkB;IAAA,IAAAgE,MAAA;IAAA,OAAApD,iBAAA;MAElB,IAAI;QACF;QACA,IAAI,CAACoD,MAAI,CAACtL,cAAc,EAAE;UACxBsL,MAAI,CAAC/K,gBAAgB,EAAE;;QAGzB;QACA,MAAM+H,MAAM,SAASgD,MAAI,CAACtD,YAAY,CAACV,QAAQ,CAAC;QAEhD;QACAgE,MAAI,CAAC5C,8BAA8B,CAACJ,MAAM,CAAC;QAE3C;QACA,MAAMU,KAAK,SAASsC,MAAI,CAACxC,WAAW,EAAE;QAEtClI,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC;QAChE,OAAOmI,KAAK;OACb,CAAC,OAAO7H,KAAK,EAAE;QACdP,OAAO,CAACO,KAAK,CAAC,gDAAgD,EAAEA,KAAK,CAAC;QACtE,MAAMA,KAAK;;IACZ;EACH;EAEA;;;EAGAoK,UAAUA,CAAChM,YAA0B;IACnCqB,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEtB,YAAY,CAACiF,EAAE,CAAC;IAEhE;IACA,OAAOnG,IAAI,CAAC,IAAI,CAACmN,kBAAkB,CAACjM,YAAY,CAAC,CAAC,CAACkL,IAAI,CACrDjM,SAAS,CAAE6K,MAAM,IAAI;MACnB,OAAO,IAAI,CAACjK,MAAM,CACfyL,MAAM,CAAuB;QAC5BC,QAAQ,EAAElM,oBAAoB;QAC9B8L,SAAS,EAAE;UACTrD,MAAM,EAAE9H,YAAY,CAACiF,EAAE;UACvB6E,MAAM,EAAEsB,IAAI,CAACC,SAAS,CAACvB,MAAM;;OAEhC,CAAC,CACDoB,IAAI,CACHjM,SAAS,CAAEuM,MAAM,IAAI;QACnBnK,OAAO,CAACC,GAAG,CACT,6CAA6C,EAC7CkK,MAAM,CACP;QAED,IAAI,CAACA,MAAM,CAAC3E,IAAI,EAAEmF,UAAU,EAAE;UAC5B,MAAM,IAAI9C,KAAK,CAAC,mCAAmC,CAAC;;QAGtD,MAAMrB,IAAI,GAAG2D,MAAM,CAAC3E,IAAI,CAACmF,UAAU;QAEnC;QACA,IAAI,CAACnH,IAAI,CAAC,UAAU,CAAC;QACrB,IAAI,CAACmB,IAAI,CAAC,gBAAgB,CAAC;QAE3B;QACA,OAAOlH,IAAI,CAAC,IAAI,CAACoN,iBAAiB,CAAClM,YAAY,EAAE6H,IAAI,CAAC,CAAC;MACzD,CAAC,CAAC,EACF7I,UAAU,CAAE4C,KAAK,IAAI;QACnBP,OAAO,CAACO,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;QACzD,IAAI,CAAC9B,MAAM,CAAC8B,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;QACjD,OAAO/C,UAAU,CACf,MAAM,IAAIqK,KAAK,CAAC,yCAAyC,CAAC,CAC3D;MACH,CAAC,CAAC,CACH;IACL,CAAC,CAAC,EACFlK,UAAU,CAAE4C,KAAK,IAAI;MACnBP,OAAO,CAACO,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5D,OAAO/C,UAAU,CAAC,MAAM,IAAIqK,KAAK,CAAC,iBAAiB,GAAGtH,KAAK,CAACkK,OAAO,CAAC,CAAC;IACvE,CAAC,CAAC,CACH;EACH;EAEA;;;EAGcG,kBAAkBA,CAC9BjM,YAA0B;IAAA,IAAAmM,MAAA;IAAA,OAAAxD,iBAAA;MAE1B,IAAI;QACFtH,OAAO,CAACC,GAAG,CACT,4DAA4D,EAC5D;UACEwG,MAAM,EAAE9H,YAAY,CAACiF,EAAE;UACvB8C,QAAQ,EAAE/H,YAAY,CAACuE,IAAI;UAC3B6H,QAAQ,EAAE,CAAC,CAACpM,YAAY,CAACyJ,KAAK;UAC9B4C,WAAW,EAAErM,YAAY,CAACyJ,KAAK,EAAEoC,MAAM,IAAI;SAC5C,CACF;QAED;QACA,IAAI,CAACM,MAAI,CAAC1L,cAAc,EAAE;UACxBY,OAAO,CAACC,GAAG,CACT,6DAA6D,CAC9D;UACD6K,MAAI,CAACnL,gBAAgB,EAAE;;QAGzB;QACAK,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC;QAChE,MAAMyH,MAAM,SAASoD,MAAI,CAAC1D,YAAY,CAACzI,YAAY,CAACuE,IAAI,CAAC;QAEzD;QACAlD,OAAO,CAACC,GAAG,CACT,mEAAmE,CACpE;QACD6K,MAAI,CAAChD,8BAA8B,CAACJ,MAAM,CAAC;QAE3C;QACA,IAAI,CAAC/I,YAAY,CAACyJ,KAAK,EAAE;UACvB,MAAM,IAAIP,KAAK,CAAC,oCAAoC,CAAC;;QAGvD,MAAMO,KAAK,GAAG2B,IAAI,CAACkB,KAAK,CAACtM,YAAY,CAACyJ,KAAK,CAAC;QAE5C,IAAI,CAACA,KAAK,IAAI,CAACA,KAAK,CAAClF,IAAI,IAAI,CAACkF,KAAK,CAAC8C,GAAG,EAAE;UACvC,MAAM,IAAIrD,KAAK,CAAC,+BAA+B,CAAC;;QAGlD;QACA,MAAMY,MAAM,SAASqC,MAAI,CAACxC,YAAY,CAACF,KAAK,CAAC;QAE7CpI,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;QACjE,OAAOwI,MAAM;OACd,CAAC,OAAOlI,KAAK,EAAE;QACdP,OAAO,CAACO,KAAK,CAAC,iDAAiD,EAAEA,KAAK,CAAC;QACvE,MAAMA,KAAK;;IACZ;EACH;EAEA;;;EAGA4K,UAAUA,CAAC1E,MAAc,EAAE2E,MAAe;IACxCpL,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEwG,MAAM,EAAE2E,MAAM,CAAC;IAE/D,OAAO,IAAI,CAAC5M,MAAM,CACfyL,MAAM,CAA8B;MACnCC,QAAQ,EAAEjM,oBAAoB;MAC9B6L,SAAS,EAAE;QACTrD,MAAM;QACN2E,MAAM,EAAEA,MAAM,IAAI;;KAErB,CAAC,CACDvB,IAAI,CACHnM,GAAG,CAAEyM,MAAM,IAAI;MACbnK,OAAO,CAACC,GAAG,CAAC,6CAA6C,EAAEkK,MAAM,CAAC;MAElE,IAAI,CAACA,MAAM,CAAC3E,IAAI,EAAE2F,UAAU,EAAE;QAC5B,MAAM,IAAItD,KAAK,CAAC,kCAAkC,CAAC;;MAGrD;MACA,IAAI,CAAClJ,YAAY,CAAC4G,IAAI,CAAC,IAAI,CAAC;MAC5B,IAAI,CAAC7G,UAAU,CAAC6G,IAAI,CAAC,IAAI,CAAC;MAE1B;MACA,IAAI,CAAC/B,IAAI,CAAC,UAAU,CAAC;MAErB,OAAO2G,MAAM,CAAC3E,IAAI,CAAC2F,UAAU;IAC/B,CAAC,CAAC,EACFxN,UAAU,CAAE4C,KAAK,IAAI;MACnBP,OAAO,CAACO,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzD,IAAI,CAAC9B,MAAM,CAAC8B,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MACjD,OAAO/C,UAAU,CAAC,MAAM,IAAIqK,KAAK,CAAC,iCAAiC,CAAC,CAAC;IACvE,CAAC,CAAC,CACH;EACL;EAEA;;;EAGAwD,OAAOA,CAAC5E,MAAc;IACpBzG,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEwG,MAAM,CAAC;IAEpD,OAAO,IAAI,CAACjI,MAAM,CACfyL,MAAM,CAA2B;MAChCC,QAAQ,EAAEhM,iBAAiB;MAC3B4L,SAAS,EAAE;QACTrD,MAAM;QACN6E,QAAQ,EAAE,IAAI,CAAE;;KAEnB,CAAC,CACDzB,IAAI,CACHnM,GAAG,CAAEyM,MAAM,IAAI;MACbnK,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAEkK,MAAM,CAAC;MAE/D,IAAI,CAACA,MAAM,CAAC3E,IAAI,EAAE6F,OAAO,EAAE;QACzB,MAAM,IAAIxD,KAAK,CAAC,kCAAkC,CAAC;;MAGrD;MACA,IAAI,CAACnJ,UAAU,CAAC6G,IAAI,CAAC,IAAI,CAAC;MAC1B,IAAI,CAAC5G,YAAY,CAAC4G,IAAI,CAAC,IAAI,CAAC;MAE5B;MACA,IAAI,CAAC0B,aAAa,EAAE;MACpB,IAAI,CAACtC,IAAI,CAAC,UAAU,CAAC;MAErB;MACA,IAAI,CAAC4G,aAAa,EAAE;MAEpB,OAAOpB,MAAM,CAAC3E,IAAI,CAAC6F,OAAO;IAC5B,CAAC,CAAC,EACF1N,UAAU,CAAE4C,KAAK,IAAI;MACnBP,OAAO,CAACO,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD,IAAI,CAAC9B,MAAM,CAAC8B,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC9C,OAAO/C,UAAU,CACf,MAAM,IAAIqK,KAAK,CAAC,kCAAkC,CAAC,CACpD;IACH,CAAC,CAAC,CACH;EACL;EAEA;;;EAGA2D,WAAWA,CACT/E,MAAc,EACdgF,WAAqB,EACrBC,WAAqB;IAErB1L,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE;MAC9CwG,MAAM;MACNgF,WAAW;MACXC;KACD,CAAC;IAEF,OAAO,IAAI,CAAClN,MAAM,CACfyL,MAAM,CAAmC;MACxCC,QAAQ,EAAE/L,0BAA0B;MACpC2L,SAAS,EAAE;QACTrD,MAAM;QACNe,KAAK,EAAEiE,WAAW;QAClB7J,KAAK,EAAE8J;;KAEV,CAAC,CACD7B,IAAI,CACHnM,GAAG,CAAEyM,MAAM,IAAI;MACbnK,OAAO,CAACC,GAAG,CAAC,6CAA6C,EAAEkK,MAAM,CAAC;MAElE,IAAI,CAACA,MAAM,CAAC3E,IAAI,EAAEmG,eAAe,EAAE;QACjC,MAAM,IAAI9D,KAAK,CAAC,kCAAkC,CAAC;;MAGrD,OAAOsC,MAAM,CAAC3E,IAAI,CAACmG,eAAe;IACpC,CAAC,CAAC,EACFhO,UAAU,CAAE4C,KAAK,IAAI;MACnBP,OAAO,CAACO,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;MAC1D,IAAI,CAAC9B,MAAM,CAAC8B,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MACjD,OAAO/C,UAAU,CACf,MAAM,IAAIqK,KAAK,CAAC,sCAAsC,CAAC,CACxD;IACH,CAAC,CAAC,CACH;EACL;EAEA;;;EAGcgD,iBAAiBA,CAC7BlM,YAA0B,EAC1B6H,IAAU;IAAA,IAAAoF,MAAA;IAAA,OAAAtE,iBAAA;MAEVtH,OAAO,CAACC,GAAG,CAAC,4DAA4D,CAAC;MAEzE;MACA2L,MAAI,CAACjH,IAAI,CAAC,gBAAgB,CAAC;MAE3B;MACAiH,MAAI,CAAClN,UAAU,CAAC6G,IAAI,CAACiB,IAAI,CAAC;MAC1BoF,MAAI,CAACjN,YAAY,CAAC4G,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;MAE9B,OAAOiB,IAAI;IAAC;EACd;EAEA;;;EAGAqF,WAAWA,CAAA;IACT,IAAI,CAAC3M,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;IAC1Cc,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE,IAAI,CAACf,cAAc,CAAC;IACnE,OAAO,IAAI,CAACA,cAAc;EAC5B;EAEA;;;EAGA4M,WAAWA,CAAA;IACT,IAAI,CAAC3M,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;IAC1Ca,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE,IAAI,CAACd,cAAc,CAAC;IACnE,OAAO,IAAI,CAACA,cAAc;EAC5B;EAEA;;;EAGA4M,eAAeA,CAAA;IACb,OAAO,IAAI,CAAC7M,cAAc;EAC5B;EAEA;;;EAGA8M,eAAeA,CAAA;IACb,OAAO,IAAI,CAAC7M,cAAc;EAC5B;EAEA;;;EAGQ8H,aAAaA,CAAA;IACnBjH,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;IACnD8F,MAAM,CAACC,IAAI,CAAC,IAAI,CAACjH,MAAM,CAAC,CAACyD,OAAO,CAAEyB,IAAI,IAAI;MACxC,IAAI,CAACT,IAAI,CAACS,IAAI,CAAC;IACjB,CAAC,CAAC;EACJ;EAEA;;;EAGOgI,YAAYA,CAAA;IACjBjM,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC;IACtE8F,MAAM,CAACmG,MAAM,CAAC,IAAI,CAACnN,MAAM,CAAC,CAACyD,OAAO,CAAEqC,KAAK,IAAI;MAC3C,IAAIA,KAAK,EAAE;QACTA,KAAK,CAAC5F,KAAK,GAAG,KAAK;QACnB4F,KAAK,CAAC/C,MAAM,GAAG,GAAG;QAClB;QACA+C,KAAK,CACFF,IAAI,EAAE,CACNK,IAAI,CAAC,MAAK;UACTH,KAAK,CAACM,KAAK,EAAE;UACbN,KAAK,CAACtC,WAAW,GAAG,CAAC;QACvB,CAAC,CAAC,CACD0C,KAAK,CAAC,MAAK;UACV;QAAA,CACD,CAAC;;IAER,CAAC,CAAC;EACJ;EAEA;;;EAGQsG,aAAaA,CAAA;IACnBvL,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;IAE5D;IACA,IAAI,IAAI,CAACZ,WAAW,EAAE;MACpB,IAAI,CAACA,WAAW,CAAC0I,SAAS,EAAE,CAACvF,OAAO,CAAEwF,KAAK,IAAI;QAC7CA,KAAK,CAACxE,IAAI,EAAE;MACd,CAAC,CAAC;MACF,IAAI,CAACnE,WAAW,GAAG,IAAI;;IAGzB;IACA,IAAI,IAAI,CAACE,iBAAiB,EAAE;MAC1B,IAAI,CAACA,iBAAiB,CAACoB,SAAS,GAAG,IAAI;;IAEzC,IAAI,IAAI,CAACnB,kBAAkB,EAAE;MAC3B,IAAI,CAACA,kBAAkB,CAACmB,SAAS,GAAG,IAAI;;IAG1C;IACA,IAAI,IAAI,CAACvB,cAAc,EAAE;MACvB,IAAI,CAACA,cAAc,CAAC+M,KAAK,EAAE;MAC3B,IAAI,CAAC/M,cAAc,GAAG,IAAI;;IAG5B,IAAI,CAACE,YAAY,GAAG,IAAI;EAC1B;EAEA8M,WAAWA,CAAA;IACT,IAAI,CAACnF,aAAa,EAAE;IACpB,IAAI,CAACsE,aAAa,EAAE;EACtB;;;uBAnlDWjN,WAAW,EAAA+N,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,aAAA;IAAA;EAAA;;;aAAXpO,WAAW;MAAAqO,OAAA,EAAXrO,WAAW,CAAAsO,IAAA;MAAAC,UAAA,EAFV;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}