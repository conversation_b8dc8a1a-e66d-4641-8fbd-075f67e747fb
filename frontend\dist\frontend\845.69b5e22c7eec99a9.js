"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[845],{8448:(m,h,s)=>{s.d(h,{I:()=>l});var c=s(7705),o=s(345);let l=(()=>{class a{constructor(n){this.sanitizer=n}transform(n){if(!n)return this.sanitizer.bypassSecurityTrustHtml("");const e=n.replace(/\(presence obligatoire\)/gi,'<span class="text-red-600 font-semibold">(presence obligatoire)</span>');return this.sanitizer.bypassSecurityTrustHtml(e)}static{this.\u0275fac=function(e){return new(e||a)(c.rXU(o.up,16))}}static{this.\u0275pipe=c.EJ8({name:"highlightPresence",type:a,pure:!0})}}return a})()},1683:(m,h,s)=>{s.d(h,{Y:()=>l});var c=s(177),o=s(7705);let l=(()=>{class a{static{this.\u0275fac=function(e){return new(e||a)}}static{this.\u0275mod=o.$C({type:a})}static{this.\u0275inj=o.G2t({imports:[c.MD]})}}return a})()},6543:(m,h,s)=>{s.d(h,{z:()=>t});var c=s(1626),o=s(8810),l=s(8141),a=s(9437),u=s(5312),n=s(7705),e=s(7552);let t=(()=>{class i{constructor(r,d){this.http=r,this.jwtHelper=d}getUserHeaders(){const r=localStorage.getItem("token");if(!r||this.jwtHelper.isTokenExpired(r))throw new Error("Token invalide ou expir\xe9");return new c.Lr({Authorization:`Bearer ${r||""}`,"Content-Type":"application/json"})}getAdminHeaders(){const r=localStorage.getItem("token");if(!r||this.jwtHelper.isTokenExpired(r))throw new Error("Token invalide ou expir\xe9");return new c.Lr({Authorization:`Bearer ${r}`,role:"admin","Content-Type":"application/json"})}getAllPlannings(){return console.log("Service - R\xe9cup\xe9ration de tous les plannings"),this.http.get(`${u.c.urlBackend}plannings/getall`,{headers:this.getUserHeaders()}).pipe((0,l.M)(r=>{console.log("Service - Plannings r\xe9cup\xe9r\xe9s:",r)}),(0,a.W)(r=>(console.error("Service - Erreur lors de la r\xe9cup\xe9ration des plannings:",r),(0,o.$)(()=>r))))}getPlanningById(r){return this.http.get(`${u.c.urlBackend}plannings/getone/${r}`)}createPlanning(r){return this.http.post(`${u.c.urlBackend}plannings/add`,r,{headers:this.getUserHeaders()})}updatePlanning(r,d){console.log("Service - Mise \xe0 jour du planning:",r),console.log("Service - Donn\xe9es envoy\xe9es:",d),console.log("Service - URL:",`${u.c.urlBackend}plannings/update/${r}`);try{const p=this.getUserHeaders();return console.log("Service - Headers:",p),this.http.put(`${u.c.urlBackend}plannings/update/${r}`,d,{headers:p}).pipe((0,l.M)(g=>{console.log("Service - R\xe9ponse du serveur:",g)}),(0,a.W)(g=>(console.error("Service - Erreur lors de la mise \xe0 jour:",g),(0,o.$)(()=>g))))}catch(p){return console.error("Service - Erreur lors de la pr\xe9paration de la requ\xeate:",p),(0,o.$)(()=>new Error("Erreur d'authentification: "+(p instanceof Error?p.message:String(p))))}}deletePlanning(r){return this.http.delete(`${u.c.urlBackend}plannings/delete/${r}`,{headers:this.getUserHeaders()})}getPlanningsByUser(r){return this.http.get(`${u.c.urlBackend}plannings/user/${r}`,{headers:this.getUserHeaders()}).pipe((0,l.M)(d=>{console.log("Service - Plannings par utilisateur r\xe9cup\xe9r\xe9s:",d)}),(0,a.W)(d=>(console.error("Service - Erreur lors de la r\xe9cup\xe9ration des plannings par utilisateur:",d),(0,o.$)(()=>d))))}getPlanningWithReunions(r){return this.http.get(`${u.c.urlBackend}plannings/with-reunions/${r}`,{headers:this.getUserHeaders()})}getAllPlanningsAdmin(){return this.http.get(`${u.c.urlBackend}plannings/admin/all`,{headers:this.getAdminHeaders()}).pipe((0,l.M)(r=>{console.log("Service - Tous les plannings (admin) r\xe9cup\xe9r\xe9s:",r)}),(0,a.W)(r=>(console.error("Service - Erreur lors de la r\xe9cup\xe9ration des plannings admin:",r),(0,o.$)(()=>r))))}static{this.\u0275fac=function(d){return new(d||i)(n.KVO(c.Qq),n.KVO(e.X7))}}static{this.\u0275prov=n.jDH({token:i,factory:i.\u0275fac,providedIn:"root"})}}return i})()},78:(m,h,s)=>{s.d(h,{C:()=>u});var c=s(1626),o=s(5312),l=s(7705),a=s(7552);let u=(()=>{class n{constructor(t,i){this.http=t,this.jwtHelper=i}getUserHeaders(){const t=localStorage.getItem("token");if(!t||this.jwtHelper.isTokenExpired(t))throw new Error("Token invalide ou expir\xe9");return new c.Lr({Authorization:`Bearer ${t||""}`,"Content-Type":"application/json"})}getAllReunions(){return this.http.get(`${o.c.urlBackend}reunions/getall`)}getReunionById(t){return this.http.get(`${o.c.urlBackend}reunions/getone/${t}`)}createReunion(t){return this.http.post(`${o.c.urlBackend}reunions/add`,t,{headers:this.getUserHeaders()})}updateReunion(t,i){return this.http.put(`${o.c.urlBackend}reunions/update/${t}`,i,{headers:this.getUserHeaders()})}deleteReunion(t){return this.http.delete(`${o.c.urlBackend}reunions/delete/${t}`,{headers:this.getUserHeaders()})}checkLienVisioUniqueness(t,i){return this.http.post(`${o.c.urlBackend}reunions/check-lien-visio`,{lienVisio:t,excludeReunionId:i},{headers:this.getUserHeaders()})}getReunionsByPlanning(t){return this.http.get(`${o.c.urlBackend}reunions/planning/${t}`)}getProchainesReunions(t){return this.http.get(`${o.c.urlBackend}reunions/user/${t}`)}getAllReunionsAdmin(){return this.http.get(`${o.c.urlBackend}reunions/admin/all`,{headers:this.getUserHeaders()})}forceDeleteReunion(t){return this.http.delete(`${o.c.urlBackend}reunions/admin/force-delete/${t}`,{headers:this.getUserHeaders()})}static{this.\u0275fac=function(i){return new(i||n)(l.KVO(c.Qq),l.KVO(a.X7))}}static{this.\u0275prov=l.jDH({token:n,factory:n.\u0275fac,providedIn:"root"})}}return n})()},4866:(m,h,s)=>{s.d(h,{W:()=>a});var c=s(4412),o=s(7705),l=s(7552);let a=(()=>{class u{constructor(e){this.jwtHelper=e,this.currentUserSubject=new c.t(null),this.currentUser$=this.currentUserSubject.asObservable(),this.permissionsSubject=new c.t(null),this.permissions$=this.permissionsSubject.asObservable(),this.loadCurrentUser()}loadCurrentUser(){const e=localStorage.getItem("token");if(e&&!this.jwtHelper.isTokenExpired(e)){const t=this.jwtHelper.decodeToken(e),i={_id:t.id,id:t.id,username:t.username,email:t.email,role:t.role,image:t.image,isActive:!0};this.currentUserSubject.next(i),this.updatePermissions(i.role)}}updatePermissions(e){const t={canCreatePlanning:this.canCreatePlanning(e),canEditPlanning:this.canEditPlanning(e),canDeletePlanning:this.canDeletePlanning(e),canCreateReunion:this.canCreateReunion(e),canEditReunion:this.canEditReunion(e),canDeleteReunion:this.canDeleteReunion(e),canViewAllUsers:this.canViewAllUsers(e),canManageUsers:this.canManageUsers(e),canAccessAdminPanel:this.canAccessAdminPanel(e),canForceDelete:this.canForceDelete(e),canViewDetailedReports:this.canViewDetailedReports(e)};this.permissionsSubject.next(t)}getCurrentUser(){return this.currentUserSubject.value}getCurrentUserRole(){const e=this.getCurrentUser();return e?e.role:null}isAdmin(){return"admin"===this.getCurrentUserRole()}isTutor(){return"tutor"===this.getCurrentUserRole()}isStudent(){return"student"===this.getCurrentUserRole()}isAlumni(){return"alumni"===this.getCurrentUserRole()}canCreatePlanning(e){const t=e||this.getCurrentUserRole();return["admin","tutor","alumni"].includes(t||"")}canEditPlanning(e){const t=e||this.getCurrentUserRole();return["admin","tutor","alumni"].includes(t||"")}canDeletePlanning(e){const t=e||this.getCurrentUserRole();return["admin","tutor"].includes(t||"")}canCreateReunion(e){const t=e||this.getCurrentUserRole();return["admin","tutor","alumni","student"].includes(t||"")}canEditReunion(e){const t=e||this.getCurrentUserRole();return["admin","tutor","alumni","student"].includes(t||"")}canDeleteReunion(e){const t=e||this.getCurrentUserRole();return["admin","tutor","alumni"].includes(t||"")}canViewAllUsers(e){const t=e||this.getCurrentUserRole();return["admin","tutor"].includes(t||"")}canManageUsers(e){return"admin"===(e||this.getCurrentUserRole())}canAccessAdminPanel(e){return"admin"===(e||this.getCurrentUserRole())}canForceDelete(e){return"admin"===(e||this.getCurrentUserRole())}canViewDetailedReports(e){const t=e||this.getCurrentUserRole();return["admin","tutor"].includes(t||"")}isOwner(e){const t=this.getCurrentUser();return!!t&&t._id===e}canModifyResource(e){return this.isAdmin()||this.isOwner(e)}canDeleteResource(e){return this.isAdmin()||this.isOwner(e)}updateCurrentUser(e){this.currentUserSubject.next(e),this.updatePermissions(e.role)}clearUserData(){this.currentUserSubject.next(null),this.permissionsSubject.next(null)}getAccessDeniedMessage(e){const t=this.getCurrentUserRole();return`Acc\xe8s refus\xe9. Votre r\xf4le (${{admin:"Administrateur",tutor:"Tuteur",alumni:"Alumni",student:"\xc9tudiant"}[t]||t}) ne vous permet pas de ${e}.`}static{this.\u0275fac=function(t){return new(t||u)(o.KVO(l.X7))}}static{this.\u0275prov=o.jDH({token:u,factory:u.\u0275fac,providedIn:"root"})}}return u})()},8397:(m,h,s)=>{s.d(h,{f:()=>l});var c=s(4412),o=s(7705);let l=(()=>{class a{constructor(){this.toastsSubject=new c.t([]),this.toasts$=this.toastsSubject.asObservable(),this.currentId=0}generateId(){return Math.random().toString(36).substr(2,9)}addToast(n){const e={...n,id:this.generateId(),duration:n.duration||5e3};this.toastsSubject.next([...this.toastsSubject.value,e]),e.duration&&e.duration>0&&setTimeout(()=>{this.removeToast(e.id)},e.duration)}show(n,e="info",t=5e3){const i=this.generateId();this.toastsSubject.next([...this.toastsSubject.value,{id:i,type:e,title:"",message:n,duration:t}]),t>0&&setTimeout(()=>this.dismiss(i),t)}showSuccess(n,e=3e3){this.show(n,"success",e)}showError(n,e=5e3){this.show(n,"error",e)}showWarning(n,e=4e3){this.show(n,"warning",e)}showInfo(n,e=3e3){this.show(n,"info",e)}dismiss(n){const e=this.toastsSubject.value.filter(t=>t.id!==n);this.toastsSubject.next(e)}success(n,e,t){this.addToast({type:"success",title:n,message:e,duration:t,icon:"check-circle"})}error(n,e,t,i){this.addToast({type:"error",title:n,message:e,duration:t||8e3,icon:"x-circle",action:i})}warning(n,e,t){this.addToast({type:"warning",title:n,message:e,duration:t,icon:"exclamation-triangle"})}accessDenied(n="effectuer cette action",e){this.error("Acc\xe8s refus\xe9",`Vous n'avez pas les permissions n\xe9cessaires pour ${n}${e?` (Code: ${e})`:""}`,8e3,{label:"Comprendre les r\xf4les",handler:()=>{console.log("Redirection vers l'aide sur les r\xf4les")}})}ownershipRequired(n="cette ressource"){this.error("Propri\xe9taire requis",`Seul le propri\xe9taire ou un administrateur peut modifier ${n}`,8e3)}removeToast(n){this.toastsSubject.next(this.toastsSubject.value.filter(t=>t.id!==n))}clear(){this.toastsSubject.next([])}static{this.\u0275fac=function(e){return new(e||a)}}static{this.\u0275prov=o.jDH({token:a,factory:a.\u0275fac,providedIn:"root"})}}return a})()},152:(m,h,s)=>{s.d(h,{B:()=>a});var c=s(3236),o=s(9974),l=s(4360);function a(u,n=c.E){return(0,o.N)((e,t)=>{let i=null,_=null,r=null;const d=()=>{if(i){i.unsubscribe(),i=null;const g=_;_=null,t.next(g)}};function p(){const g=r+u,E=n.now();if(E<g)return i=this.schedule(void 0,g-E),void t.add(i);d()}e.subscribe((0,l._)(t,g=>{_=g,r=n.now(),i||(i=n.schedule(p,u),t.add(i))},()=>{d(),t.complete()},void 0,()=>{_=i=null}))})}}}]);