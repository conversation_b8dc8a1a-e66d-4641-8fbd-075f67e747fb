{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/OneDrive/Bureau/Project PI/devBridge/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Validators } from '@angular/forms';\nimport { Subscription } from 'rxjs';\nimport { CallType } from '../../../../models/message.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"../../../../services/message.service\";\nimport * as i4 from \"../../../../services/call.service\";\nimport * as i5 from \"../../../../services/toast.service\";\nimport * as i6 from \"@angular/common\";\nconst _c0 = [\"messagesContainer\"];\nconst _c1 = [\"fileInput\"];\nconst _c2 = [\"localVideo\"];\nconst _c3 = [\"remoteVideo\"];\nconst _c4 = [\"localVideoHidden\"];\nconst _c5 = [\"remoteVideoHidden\"];\nfunction MessageChatComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 55);\n  }\n}\nfunction MessageChatComponent_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 56)(1, \"span\");\n    i0.ɵɵtext(2, \"En train d'\\u00E9crire\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 57);\n    i0.ɵɵelement(4, \"div\", 58)(5, \"div\", 59)(6, \"div\", 60);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction MessageChatComponent_span_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r4.otherParticipant == null ? null : ctx_r4.otherParticipant.isOnline) ? \"En ligne\" : ctx_r4.formatLastActive(ctx_r4.otherParticipant == null ? null : ctx_r4.otherParticipant.lastActive), \" \");\n  }\n}\nfunction MessageChatComponent_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 61)(1, \"div\", 62)(2, \"button\", 63);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_29_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r19 = i0.ɵɵnextContext();\n      ctx_r19.toggleSearch();\n      return i0.ɵɵresetView(ctx_r19.showMainMenu = false);\n    });\n    i0.ɵɵelement(3, \"i\", 64);\n    i0.ɵɵelementStart(4, \"span\", 65);\n    i0.ɵɵtext(5, \"Rechercher\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"button\", 66);\n    i0.ɵɵelement(7, \"i\", 67);\n    i0.ɵɵelementStart(8, \"span\", 65);\n    i0.ɵɵtext(9, \"Voir le profil\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(10, \"hr\", 68);\n    i0.ɵɵelementStart(11, \"button\", 66);\n    i0.ɵɵelement(12, \"i\", 69);\n    i0.ɵɵelementStart(13, \"span\", 65);\n    i0.ɵɵtext(14, \"Param\\u00E8tres\");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nfunction MessageChatComponent_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 70)(1, \"div\", 71);\n    i0.ɵɵelement(2, \"i\", 72);\n    i0.ɵɵelementStart(3, \"p\", 73);\n    i0.ɵɵtext(4, \" D\\u00E9posez vos fichiers ici \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 74);\n    i0.ɵɵtext(6, \" Images, vid\\u00E9os, documents... \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction MessageChatComponent_div_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 75);\n    i0.ɵɵelement(1, \"div\", 76);\n    i0.ɵɵelementStart(2, \"span\", 77);\n    i0.ɵɵtext(3, \"Chargement des messages...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction MessageChatComponent_div_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 78)(1, \"div\", 79);\n    i0.ɵɵelement(2, \"i\", 80);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\", 81);\n    i0.ɵɵtext(4, \" Aucun message \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 82);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" Commencez votre conversation avec \", ctx_r9.otherParticipant == null ? null : ctx_r9.otherParticipant.username, \" \");\n  }\n}\nfunction MessageChatComponent_div_35_ng_container_1_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 96)(1, \"div\", 97)(2, \"span\", 98);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const message_r23 = i0.ɵɵnextContext().$implicit;\n    const ctx_r25 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r25.formatDateSeparator(message_r23.timestamp), \" \");\n  }\n}\nfunction MessageChatComponent_div_35_ng_container_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r35 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 99)(1, \"img\", 100);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_35_ng_container_1_div_3_Template_img_click_1_listener() {\n      i0.ɵɵrestoreView(_r35);\n      const message_r23 = i0.ɵɵnextContext().$implicit;\n      const ctx_r33 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r33.openUserProfile(message_r23.sender == null ? null : message_r23.sender.id));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const message_r23 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", (message_r23.sender == null ? null : message_r23.sender.image) || \"assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", message_r23.sender == null ? null : message_r23.sender.username);\n  }\n}\nfunction MessageChatComponent_div_35_ng_container_1_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 101);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r23 = i0.ɵɵnextContext().$implicit;\n    const ctx_r27 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleProp(\"color\", ctx_r27.getUserColor(message_r23.sender == null ? null : message_r23.sender.id));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", message_r23.sender == null ? null : message_r23.sender.username, \" \");\n  }\n}\nfunction MessageChatComponent_div_35_ng_container_1_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 102);\n    i0.ɵɵelement(1, \"div\", 103);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r23 = i0.ɵɵnextContext().$implicit;\n    const ctx_r28 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"innerHTML\", ctx_r28.formatMessageContent(message_r23.content), i0.ɵɵsanitizeHtml);\n  }\n}\nfunction MessageChatComponent_div_35_ng_container_1_div_7_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 107);\n  }\n  if (rf & 2) {\n    const message_r23 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r39 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleProp(\"color\", (message_r23.sender == null ? null : message_r23.sender.id) === ctx_r39.currentUserId ? \"#ffffff\" : \"#111827\");\n    i0.ɵɵproperty(\"innerHTML\", ctx_r39.formatMessageContent(message_r23.content), i0.ɵɵsanitizeHtml);\n  }\n}\nfunction MessageChatComponent_div_35_ng_container_1_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r43 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 104)(1, \"img\", 105);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_35_ng_container_1_div_7_Template_img_click_1_listener() {\n      i0.ɵɵrestoreView(_r43);\n      const message_r23 = i0.ɵɵnextContext().$implicit;\n      const ctx_r41 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r41.openImageViewer(message_r23));\n    })(\"load\", function MessageChatComponent_div_35_ng_container_1_div_7_Template_img_load_1_listener($event) {\n      i0.ɵɵrestoreView(_r43);\n      const message_r23 = i0.ɵɵnextContext().$implicit;\n      const ctx_r44 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r44.onImageLoad($event, message_r23));\n    })(\"error\", function MessageChatComponent_div_35_ng_container_1_div_7_Template_img_error_1_listener($event) {\n      i0.ɵɵrestoreView(_r43);\n      const message_r23 = i0.ɵɵnextContext().$implicit;\n      const ctx_r46 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r46.onImageError($event, message_r23));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(2, MessageChatComponent_div_35_ng_container_1_div_7_div_2_Template, 1, 3, \"div\", 106);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r23 = i0.ɵɵnextContext().$implicit;\n    const ctx_r29 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", ctx_r29.getImageUrl(message_r23), i0.ɵɵsanitizeUrl)(\"alt\", message_r23.content || \"Image\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r23.content);\n  }\n}\nfunction MessageChatComponent_div_35_ng_container_1_div_8_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 116);\n  }\n  if (rf & 2) {\n    const wave_r51 = ctx.$implicit;\n    const i_r52 = ctx.index;\n    const message_r23 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r49 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleProp(\"height\", ctx_r49.isVoicePlaying(message_r23.id) ? wave_r51 : 8, \"px\")(\"animation\", ctx_r49.isVoicePlaying(message_r23.id) ? \"pulse 1s infinite\" : \"none\")(\"animation-delay\", i_r52 * 0.1 + \"s\");\n  }\n}\nfunction MessageChatComponent_div_35_ng_container_1_div_8_button_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r56 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 117);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_35_ng_container_1_div_8_button_8_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r56);\n      const message_r23 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r54 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r54.changeVoiceSpeed(message_r23));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r23 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r50 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r50.getVoiceSpeed(message_r23), \"x \");\n  }\n}\nfunction MessageChatComponent_div_35_ng_container_1_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r60 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 108)(1, \"button\", 109);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_35_ng_container_1_div_8_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r60);\n      const message_r23 = i0.ɵɵnextContext().$implicit;\n      const ctx_r58 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r58.toggleVoicePlayback(message_r23));\n    });\n    i0.ɵɵelement(2, \"i\", 110);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 111);\n    i0.ɵɵtemplate(4, MessageChatComponent_div_35_ng_container_1_div_8_div_4_Template, 1, 6, \"div\", 112);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 113)(6, \"div\", 114);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, MessageChatComponent_div_35_ng_container_1_div_8_button_8_Template, 2, 1, \"button\", 115);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const message_r23 = i0.ɵɵnextContext().$implicit;\n    const ctx_r30 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(ctx_r30.isVoicePlaying(message_r23.id) ? \"fas fa-pause\" : \"fas fa-play\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r30.voiceWaves);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r30.getVoiceDuration(message_r23), \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r30.isVoicePlaying(message_r23.id));\n  }\n}\nfunction MessageChatComponent_div_35_ng_container_1_div_12_i_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 123);\n  }\n}\nfunction MessageChatComponent_div_35_ng_container_1_div_12_i_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 124);\n  }\n}\nfunction MessageChatComponent_div_35_ng_container_1_div_12_i_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 125);\n  }\n}\nfunction MessageChatComponent_div_35_ng_container_1_div_12_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 126);\n  }\n}\nfunction MessageChatComponent_div_35_ng_container_1_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 118);\n    i0.ɵɵtemplate(1, MessageChatComponent_div_35_ng_container_1_div_12_i_1_Template, 1, 0, \"i\", 119);\n    i0.ɵɵtemplate(2, MessageChatComponent_div_35_ng_container_1_div_12_i_2_Template, 1, 0, \"i\", 120);\n    i0.ɵɵtemplate(3, MessageChatComponent_div_35_ng_container_1_div_12_i_3_Template, 1, 0, \"i\", 121);\n    i0.ɵɵtemplate(4, MessageChatComponent_div_35_ng_container_1_div_12_i_4_Template, 1, 0, \"i\", 122);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r23 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r23.status === \"SENDING\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r23.status === \"SENT\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r23.status === \"DELIVERED\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r23.status === \"READ\");\n  }\n}\nfunction MessageChatComponent_div_35_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r68 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, MessageChatComponent_div_35_ng_container_1_div_1_Template, 4, 1, \"div\", 86);\n    i0.ɵɵelementStart(2, \"div\", 87);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_35_ng_container_1_Template_div_click_2_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r68);\n      const message_r23 = restoredCtx.$implicit;\n      const ctx_r67 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r67.onMessageClick(message_r23, $event));\n    })(\"contextmenu\", function MessageChatComponent_div_35_ng_container_1_Template_div_contextmenu_2_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r68);\n      const message_r23 = restoredCtx.$implicit;\n      const ctx_r69 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r69.onMessageContextMenu(message_r23, $event));\n    });\n    i0.ɵɵtemplate(3, MessageChatComponent_div_35_ng_container_1_div_3_Template, 2, 2, \"div\", 88);\n    i0.ɵɵelementStart(4, \"div\", 89);\n    i0.ɵɵtemplate(5, MessageChatComponent_div_35_ng_container_1_div_5_Template, 2, 3, \"div\", 90);\n    i0.ɵɵtemplate(6, MessageChatComponent_div_35_ng_container_1_div_6_Template, 2, 1, \"div\", 91);\n    i0.ɵɵtemplate(7, MessageChatComponent_div_35_ng_container_1_div_7_Template, 3, 3, \"div\", 92);\n    i0.ɵɵtemplate(8, MessageChatComponent_div_35_ng_container_1_div_8_Template, 9, 5, \"div\", 93);\n    i0.ɵɵelementStart(9, \"div\", 94)(10, \"span\");\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(12, MessageChatComponent_div_35_ng_container_1_div_12_Template, 5, 4, \"div\", 95);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const message_r23 = ctx.$implicit;\n    const i_r24 = ctx.index;\n    const ctx_r21 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r21.shouldShowDateSeparator(i_r24));\n    i0.ɵɵadvance(1);\n    i0.ɵɵstyleProp(\"justify-content\", (message_r23.sender == null ? null : message_r23.sender.id) === ctx_r21.currentUserId ? \"flex-end\" : \"flex-start\");\n    i0.ɵɵproperty(\"id\", \"message-\" + message_r23.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (message_r23.sender == null ? null : message_r23.sender.id) !== ctx_r21.currentUserId && ctx_r21.shouldShowAvatar(i_r24));\n    i0.ɵɵadvance(1);\n    i0.ɵɵstyleProp(\"background-color\", (message_r23.sender == null ? null : message_r23.sender.id) === ctx_r21.currentUserId ? \"#3b82f6\" : \"#ffffff\")(\"color\", (message_r23.sender == null ? null : message_r23.sender.id) === ctx_r21.currentUserId ? \"#ffffff\" : \"#111827\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r21.isGroupConversation() && (message_r23.sender == null ? null : message_r23.sender.id) !== ctx_r21.currentUserId && ctx_r21.shouldShowSenderName(i_r24));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r21.getMessageType(message_r23) === \"text\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r21.hasImage(message_r23));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r21.getMessageType(message_r23) === \"audio\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r21.formatMessageTime(message_r23.timestamp));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (message_r23.sender == null ? null : message_r23.sender.id) === ctx_r21.currentUserId);\n  }\n}\nfunction MessageChatComponent_div_35_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 127);\n    i0.ɵɵelement(1, \"img\", 128);\n    i0.ɵɵelementStart(2, \"div\", 129)(3, \"div\", 130);\n    i0.ɵɵelement(4, \"div\", 131)(5, \"div\", 132)(6, \"div\", 133);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r22 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", (ctx_r22.otherParticipant == null ? null : ctx_r22.otherParticipant.image) || \"assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", ctx_r22.otherParticipant == null ? null : ctx_r22.otherParticipant.username);\n  }\n}\nfunction MessageChatComponent_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 83);\n    i0.ɵɵtemplate(1, MessageChatComponent_div_35_ng_container_1_Template, 13, 15, \"ng-container\", 84);\n    i0.ɵɵtemplate(2, MessageChatComponent_div_35_div_2_Template, 7, 2, \"div\", 85);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r10.messages)(\"ngForTrackBy\", ctx_r10.trackByMessageId);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.otherUserIsTyping);\n  }\n}\nfunction MessageChatComponent_div_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 134);\n  }\n}\nfunction MessageChatComponent_i_49_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 135);\n  }\n}\nfunction MessageChatComponent_div_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 136);\n  }\n}\nfunction MessageChatComponent_div_51_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r73 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 142);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_51_button_5_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r73);\n      const emoji_r71 = restoredCtx.$implicit;\n      const ctx_r72 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r72.insertEmoji(emoji_r71));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const emoji_r71 = ctx.$implicit;\n    i0.ɵɵproperty(\"title\", emoji_r71.name);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", emoji_r71.emoji, \" \");\n  }\n}\nfunction MessageChatComponent_div_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 137)(1, \"div\", 138)(2, \"h4\", 139);\n    i0.ɵɵtext(3, \" \\u00C9mojis \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 140);\n    i0.ɵɵtemplate(5, MessageChatComponent_div_51_button_5_Template, 2, 2, \"button\", 141);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r14.getEmojisForCategory(ctx_r14.selectedEmojiCategory));\n  }\n}\nfunction MessageChatComponent_div_52_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r75 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 143)(1, \"div\", 138)(2, \"h4\", 139);\n    i0.ɵɵtext(3, \" Pi\\u00E8ces jointes \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 144)(5, \"button\", 145);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_52_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r75);\n      const ctx_r74 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r74.triggerFileInput(\"image\"));\n    });\n    i0.ɵɵelementStart(6, \"div\", 146);\n    i0.ɵɵelement(7, \"i\", 147);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"span\", 148);\n    i0.ɵɵtext(9, \"Images\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"button\", 145);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_52_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r75);\n      const ctx_r76 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r76.triggerFileInput(\"document\"));\n    });\n    i0.ɵɵelementStart(11, \"div\", 149);\n    i0.ɵɵelement(12, \"i\", 150);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"span\", 148);\n    i0.ɵɵtext(14, \"Documents\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"button\", 145);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_52_Template_button_click_15_listener() {\n      i0.ɵɵrestoreView(_r75);\n      const ctx_r77 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r77.openCamera());\n    });\n    i0.ɵɵelementStart(16, \"div\", 151);\n    i0.ɵɵelement(17, \"i\", 152);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"span\", 148);\n    i0.ɵɵtext(19, \"Cam\\u00E9ra\");\n    i0.ɵɵelementEnd()()()()();\n  }\n}\nfunction MessageChatComponent_div_55_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r79 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 153);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_55_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r79);\n      const ctx_r78 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r78.closeAllMenus());\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MessageChatComponent_div_56_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 166);\n  }\n  if (rf & 2) {\n    const wave_r81 = ctx.$implicit;\n    const i_r82 = ctx.index;\n    i0.ɵɵstyleProp(\"height\", wave_r81, \"px\")(\"animation\", \"bounce 1s infinite\")(\"animation-delay\", i_r82 * 0.1 + \"s\");\n  }\n}\nfunction MessageChatComponent_div_56_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r84 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 154)(1, \"div\", 155);\n    i0.ɵɵelement(2, \"i\", 156);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 157)(4, \"div\", 158);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 159);\n    i0.ɵɵtemplate(7, MessageChatComponent_div_56_div_7_Template, 1, 6, \"div\", 160);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 161);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 37)(11, \"button\", 162);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_56_Template_button_click_11_listener($event) {\n      i0.ɵɵrestoreView(_r84);\n      const ctx_r83 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r83.onRecordCancel($event));\n    });\n    i0.ɵɵelement(12, \"i\", 163);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"button\", 164);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_56_Template_button_click_13_listener($event) {\n      i0.ɵɵrestoreView(_r84);\n      const ctx_r85 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r85.onRecordEnd($event));\n    });\n    i0.ɵɵelement(14, \"i\", 165);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r18 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r18.formatRecordingDuration(ctx_r18.voiceRecordingDuration), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r18.voiceWaves);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" Format: \", ctx_r18.getRecordingFormat(), \" \");\n  }\n}\nexport class MessageChatComponent {\n  constructor(fb, route, router, MessageService, callService, toastService, cdr) {\n    this.fb = fb;\n    this.route = route;\n    this.router = router;\n    this.MessageService = MessageService;\n    this.callService = callService;\n    this.toastService = toastService;\n    this.cdr = cdr;\n    // === DONNÉES PRINCIPALES ===\n    this.conversation = null;\n    this.messages = [];\n    this.currentUserId = null;\n    this.currentUsername = 'You';\n    this.otherParticipant = null;\n    // === ÉTATS DE L'INTERFACE ===\n    this.isLoading = false;\n    this.isLoadingMore = false;\n    this.hasMoreMessages = true;\n    this.showEmojiPicker = false;\n    this.showAttachmentMenu = false;\n    this.showSearch = false;\n    this.searchQuery = '';\n    this.searchResults = [];\n    this.searchMode = false;\n    this.isSendingMessage = false;\n    this.otherUserIsTyping = false;\n    this.showMainMenu = false;\n    this.showMessageContextMenu = false;\n    this.selectedMessage = null;\n    this.contextMenuPosition = {\n      x: 0,\n      y: 0\n    };\n    this.showReactionPicker = false;\n    this.reactionPickerMessage = null;\n    this.showImageViewer = false;\n    this.selectedImage = null;\n    this.uploadProgress = 0;\n    this.isUploading = false;\n    this.isDragOver = false;\n    // === GESTION VOCALE OPTIMISÉE ===\n    this.isRecordingVoice = false;\n    this.voiceRecordingDuration = 0;\n    this.voiceRecordingState = 'idle';\n    this.mediaRecorder = null;\n    this.audioChunks = [];\n    this.recordingTimer = null;\n    this.voiceWaves = [4, 8, 12, 16, 20, 16, 12, 8, 4, 8, 12, 16, 20, 16, 12, 8];\n    // Lecture des messages vocaux\n    this.currentAudio = null;\n    this.playingMessageId = null;\n    this.voicePlayback = {};\n    // === APPELS WEBRTC ===\n    this.isInCall = false;\n    this.callType = null;\n    this.callDuration = 0;\n    this.callTimer = null;\n    // État de l'appel WebRTC - Géré globalement par les composants d'appel\n    // activeCall, isMuted, isVideoEnabled sont maintenant dans ActiveCallComponent\n    // === ÉMOJIS ===\n    this.emojiCategories = [{\n      id: 'smileys',\n      name: 'Smileys',\n      icon: '😀',\n      emojis: [{\n        emoji: '😀',\n        name: 'grinning face'\n      }, {\n        emoji: '😃',\n        name: 'grinning face with big eyes'\n      }, {\n        emoji: '😄',\n        name: 'grinning face with smiling eyes'\n      }, {\n        emoji: '😁',\n        name: 'beaming face with smiling eyes'\n      }, {\n        emoji: '😆',\n        name: 'grinning squinting face'\n      }, {\n        emoji: '😅',\n        name: 'grinning face with sweat'\n      }, {\n        emoji: '😂',\n        name: 'face with tears of joy'\n      }, {\n        emoji: '🤣',\n        name: 'rolling on the floor laughing'\n      }, {\n        emoji: '😊',\n        name: 'smiling face with smiling eyes'\n      }, {\n        emoji: '😇',\n        name: 'smiling face with halo'\n      }]\n    }, {\n      id: 'people',\n      name: 'People',\n      icon: '👤',\n      emojis: [{\n        emoji: '👶',\n        name: 'baby'\n      }, {\n        emoji: '🧒',\n        name: 'child'\n      }, {\n        emoji: '👦',\n        name: 'boy'\n      }, {\n        emoji: '👧',\n        name: 'girl'\n      }, {\n        emoji: '🧑',\n        name: 'person'\n      }, {\n        emoji: '👨',\n        name: 'man'\n      }, {\n        emoji: '👩',\n        name: 'woman'\n      }, {\n        emoji: '👴',\n        name: 'old man'\n      }, {\n        emoji: '👵',\n        name: 'old woman'\n      }]\n    }, {\n      id: 'nature',\n      name: 'Nature',\n      icon: '🌿',\n      emojis: [{\n        emoji: '🐶',\n        name: 'dog face'\n      }, {\n        emoji: '🐱',\n        name: 'cat face'\n      }, {\n        emoji: '🐭',\n        name: 'mouse face'\n      }, {\n        emoji: '🐹',\n        name: 'hamster'\n      }, {\n        emoji: '🐰',\n        name: 'rabbit face'\n      }, {\n        emoji: '🦊',\n        name: 'fox'\n      }, {\n        emoji: '🐻',\n        name: 'bear'\n      }, {\n        emoji: '🐼',\n        name: 'panda'\n      }]\n    }];\n    this.selectedEmojiCategory = this.emojiCategories[0];\n    // === PAGINATION ===\n    this.MAX_MESSAGES_TO_LOAD = 10;\n    this.currentPage = 1;\n    // === AUTRES ÉTATS ===\n    this.isTyping = false;\n    this.isUserTyping = false;\n    this.typingTimeout = null;\n    this.subscriptions = new Subscription();\n    this.messageForm = this.fb.group({\n      content: ['', [Validators.required, Validators.minLength(1)]]\n    });\n  }\n  // Méthode pour vérifier si le champ de saisie doit être désactivé\n  isInputDisabled() {\n    return !this.otherParticipant || this.isRecordingVoice || this.isSendingMessage;\n  }\n  // Méthode pour gérer l'état du contrôle de saisie\n  updateInputState() {\n    const contentControl = this.messageForm.get('content');\n    if (this.isInputDisabled()) {\n      contentControl?.disable();\n    } else {\n      contentControl?.enable();\n    }\n  }\n  ngOnInit() {\n    this.initializeComponent();\n    // Activer les sons après interaction utilisateur\n    this.enableSoundsOnFirstInteraction();\n  }\n  ngAfterViewInit() {\n    // Configurer les éléments vidéo pour WebRTC après que la vue soit initialisée\n    setTimeout(() => {\n      this.setupVideoElements();\n    }, 100);\n    // Réessayer plusieurs fois pour s'assurer que les éléments sont configurés\n    setTimeout(() => {\n      this.setupVideoElements();\n    }, 500);\n    setTimeout(() => {\n      this.setupVideoElements();\n    }, 1000);\n    setTimeout(() => {\n      this.setupVideoElements();\n    }, 2000);\n  }\n  /**\n   * Configure les éléments vidéo pour WebRTC\n   */\n  setupVideoElements() {\n    // Essayer d'abord les éléments visibles (pour appels vidéo)\n    if (this.localVideo && this.remoteVideo) {\n      this.callService.setVideoElements(this.localVideo.nativeElement, this.remoteVideo.nativeElement);\n    }\n    // Sinon utiliser les éléments cachés (pour appels audio)\n    else if (this.localVideoHidden && this.remoteVideoHidden) {\n      this.callService.setVideoElements(this.localVideoHidden.nativeElement, this.remoteVideoHidden.nativeElement);\n    } else {\n      this.createVideoElementsManually();\n      // Réessayer après un délai\n      setTimeout(() => {\n        this.setupVideoElements();\n      }, 500);\n      // Réessayer encore plus tard\n      setTimeout(() => {\n        this.setupVideoElements();\n      }, 1500);\n    }\n  }\n  /**\n   * Crée les éléments vidéo manuellement si les ViewChild ne fonctionnent pas\n   */\n  createVideoElementsManually() {\n    // Chercher les éléments dans le DOM\n    const localVideoEl = document.getElementById('localVideo');\n    const remoteVideoEl = document.getElementById('remoteVideo');\n    if (localVideoEl && remoteVideoEl) {\n      console.log('✅ [MessageChat] Found video elements in DOM, configuring...');\n      this.callService.setVideoElements(localVideoEl, remoteVideoEl);\n    } else {\n      console.warn('⚠️ [MessageChat] Video elements not found in DOM either');\n      // Créer les éléments dynamiquement\n      const localVideo = document.createElement('video');\n      localVideo.id = 'localVideo';\n      localVideo.autoplay = true;\n      localVideo.muted = true;\n      localVideo.playsInline = true;\n      localVideo.style.cssText = 'position: absolute; top: -9999px; left: -9999px; width: 1px; height: 1px;';\n      const remoteVideo = document.createElement('video');\n      remoteVideo.id = 'remoteVideo';\n      remoteVideo.autoplay = true;\n      remoteVideo.playsInline = true;\n      remoteVideo.style.cssText = 'position: absolute; top: -9999px; left: -9999px; width: 1px; height: 1px;';\n      document.body.appendChild(localVideo);\n      document.body.appendChild(remoteVideo);\n      this.callService.setVideoElements(localVideo, remoteVideo);\n    }\n  }\n  enableSoundsOnFirstInteraction() {\n    const enableSounds = () => {\n      this.callService.enableSounds();\n      document.removeEventListener('click', enableSounds);\n      document.removeEventListener('keydown', enableSounds);\n      document.removeEventListener('touchstart', enableSounds);\n    };\n    document.addEventListener('click', enableSounds, {\n      once: true\n    });\n    document.addEventListener('keydown', enableSounds, {\n      once: true\n    });\n    document.addEventListener('touchstart', enableSounds, {\n      once: true\n    });\n  }\n  initializeComponent() {\n    this.loadCurrentUser();\n    this.loadConversation();\n    this.setupCallSubscriptions();\n  }\n  setupCallSubscriptions() {\n    // Les appels sont maintenant gérés globalement par app-incoming-call et app-active-call\n    // Plus besoin de subscriptions locales ici\n    console.log('📞 [MessageChat] Call subscriptions handled globally');\n  }\n  handleIncomingCall(incomingCall) {\n    // Afficher une notification ou modal d'appel entrant\n    // Pour l'instant, on log juste\n    console.log('🔔 Handling incoming call from:', incomingCall.caller.username);\n    // Jouer la sonnerie\n    this.MessageService.play('ringtone');\n    // Ici on pourrait afficher une modal ou notification\n    // Pour l'instant, on accepte automatiquement pour tester\n    // this.acceptCall(incomingCall);\n  }\n\n  loadCurrentUser() {\n    try {\n      const userString = localStorage.getItem('user');\n      if (!userString || userString === 'null' || userString === 'undefined') {\n        console.error('❌ No user data in localStorage');\n        this.currentUserId = null;\n        this.currentUsername = 'You';\n        return;\n      }\n      const user = JSON.parse(userString);\n      // Essayer différentes propriétés pour l'ID utilisateur\n      const userId = user._id || user.id || user.userId;\n      if (userId) {\n        this.currentUserId = userId;\n        this.currentUsername = user.username || user.name || 'You';\n      } else {\n        console.error('❌ No valid user ID found in user object:', user);\n        this.currentUserId = null;\n        this.currentUsername = 'You';\n      }\n    } catch (error) {\n      console.error('❌ Error parsing user from localStorage:', error);\n      this.currentUserId = null;\n      this.currentUsername = 'You';\n    }\n  }\n  loadConversation() {\n    const conversationId = this.route.snapshot.paramMap.get('id');\n    if (!conversationId) {\n      this.toastService.showError('ID de conversation manquant');\n      return;\n    }\n    this.isLoading = true;\n    // Nettoyer les subscriptions existantes avant de recharger\n    this.cleanupSubscriptions();\n    this.MessageService.getConversation(conversationId).subscribe({\n      next: conversation => {\n        this.conversation = conversation;\n        this.setOtherParticipant();\n        this.loadMessages();\n        // Configurer les subscriptions temps réel après le chargement de la conversation\n        this.setupSubscriptions();\n        this.isLoading = false;\n      },\n      error: error => {\n        console.error('Erreur lors du chargement de la conversation:', error);\n        this.toastService.showError('Erreur lors du chargement de la conversation');\n        this.isLoading = false;\n        // Réessayer après 2 secondes en cas d'erreur\n        setTimeout(() => {\n          this.loadConversation();\n        }, 2000);\n      }\n    });\n  }\n  setOtherParticipant() {\n    if (!this.conversation?.participants || this.conversation.participants.length === 0) {\n      console.warn('No participants found in conversation');\n      this.otherParticipant = null;\n      return;\n    }\n    // Dans une conversation 1-à-1, on veut afficher l'autre personne (pas l'utilisateur actuel)\n    // Dans une conversation de groupe, on peut afficher le nom du groupe ou le premier autre participant\n    if (this.conversation.isGroup) {\n      // Pour les groupes, on pourrait afficher le nom du groupe\n      // Mais pour l'instant, on prend le premier participant qui n'est pas l'utilisateur actuel\n      this.otherParticipant = this.conversation.participants.find(p => {\n        const participantId = p.id || p._id;\n        return String(participantId) !== String(this.currentUserId);\n      });\n    } else {\n      // Pour les conversations 1-à-1, on prend l'autre participant\n      this.otherParticipant = this.conversation.participants.find(p => {\n        const participantId = p.id || p._id;\n        console.log('Comparing participant ID:', participantId, 'with current user ID:', this.currentUserId);\n        return String(participantId) !== String(this.currentUserId);\n      });\n    }\n    // Fallback si aucun autre participant n'est trouvé\n    if (!this.otherParticipant && this.conversation.participants.length > 0) {\n      this.otherParticipant = this.conversation.participants[0];\n      // Si le premier participant est l'utilisateur actuel et qu'il y en a d'autres\n      if (this.conversation.participants.length > 1) {\n        const firstParticipantId = this.otherParticipant.id || this.otherParticipant._id;\n        if (String(firstParticipantId) === String(this.currentUserId)) {\n          console.log('First participant is current user, using second participant');\n          this.otherParticipant = this.conversation.participants[1];\n        }\n      }\n    }\n    // Vérification finale et logs\n    if (this.otherParticipant) {\n      // Log très visible pour debug\n      console.log('🎯 FINAL RESULT: otherParticipant =', this.otherParticipant.username);\n      console.log('🎯 Should display in sidebar:', this.otherParticipant.username);\n    } else {\n      console.error('❌ No other participant found! This should not happen.');\n      // Log très visible pour debug\n    }\n    // Mettre à jour l'état du champ de saisie\n    this.updateInputState();\n  }\n  loadMessages() {\n    if (!this.conversation?.id) return;\n    // Les messages sont déjà chargés avec la conversation\n    let messages = this.conversation.messages || [];\n    // Trier les messages par timestamp (plus anciens en premier)\n    this.messages = messages.sort((a, b) => {\n      const dateA = new Date(a.timestamp || a.createdAt).getTime();\n      const dateB = new Date(b.timestamp || b.createdAt).getTime();\n      return dateA - dateB; // Ordre croissant (plus anciens en premier)\n    });\n\n    console.log('📋 Messages loaded and sorted:', {\n      total: this.messages.length,\n      first: this.messages[0]?.content,\n      last: this.messages[this.messages.length - 1]?.content\n    });\n    this.hasMoreMessages = this.messages.length === this.MAX_MESSAGES_TO_LOAD;\n    this.isLoading = false;\n    this.scrollToBottom();\n  }\n  loadMoreMessages() {\n    if (this.isLoadingMore || !this.hasMoreMessages || !this.conversation?.id) return;\n    this.isLoadingMore = true;\n    this.currentPage++;\n    // Calculer l'offset basé sur les messages déjà chargés\n    const offset = this.messages.length;\n    this.MessageService.getMessages(this.currentUserId,\n    // senderId\n    this.otherParticipant?.id || this.otherParticipant?._id,\n    // receiverId\n    this.conversation.id, this.currentPage, this.MAX_MESSAGES_TO_LOAD).subscribe({\n      next: newMessages => {\n        if (newMessages && newMessages.length > 0) {\n          // Ajouter les nouveaux messages au début de la liste\n          this.messages = [...newMessages.reverse(), ...this.messages];\n          this.hasMoreMessages = newMessages.length === this.MAX_MESSAGES_TO_LOAD;\n        } else {\n          this.hasMoreMessages = false;\n        }\n        this.isLoadingMore = false;\n      },\n      error: error => {\n        console.error('Erreur lors du chargement des messages:', error);\n        this.toastService.showError('Erreur lors du chargement des messages');\n        this.isLoadingMore = false;\n        this.currentPage--; // Revenir à la page précédente en cas d'erreur\n      }\n    });\n  }\n  /**\n   * Nettoie les subscriptions existantes\n   */\n  cleanupSubscriptions() {\n    this.subscriptions.unsubscribe();\n    this.subscriptions = new Subscription();\n  }\n  /**\n   * Recharge la conversation actuelle\n   */\n  reloadConversation() {\n    if (this.conversation?.id) {\n      // Réinitialiser l'état\n      this.messages = [];\n      this.currentPage = 1;\n      this.hasMoreMessages = true;\n      // Recharger\n      this.loadConversation();\n    }\n  }\n  setupSubscriptions() {\n    if (!this.conversation?.id) {\n      console.warn('❌ Cannot setup subscriptions: no conversation ID');\n      return;\n    }\n    console.log('🔄 Setting up real-time subscriptions for conversation:', this.conversation.id);\n    // Subscription pour les nouveaux messages\n    this.subscriptions.add(this.MessageService.subscribeToNewMessages(this.conversation.id).subscribe({\n      next: newMessage => {\n        console.log('📨 Message structure:', {\n          id: newMessage.id,\n          type: newMessage.type,\n          content: newMessage.content,\n          sender: newMessage.sender,\n          senderId: newMessage.senderId,\n          receiverId: newMessage.receiverId,\n          attachments: newMessage.attachments\n        });\n        // Debug des attachments\n        console.log('📨 [Debug] Message type detected:', this.getMessageType(newMessage));\n        if (newMessage.attachments) {\n          newMessage.attachments.forEach((att, index) => {\n            console.log(`📨 [Debug] Attachment ${index}:`, {\n              type: att.type,\n              url: att.url,\n              path: att.path,\n              name: att.name,\n              size: att.size\n            });\n          });\n        }\n        // Ajouter le message à la liste s'il n'existe pas déjà\n        const messageExists = this.messages.some(msg => msg.id === newMessage.id);\n        if (!messageExists) {\n          // Ajouter le nouveau message à la fin (en bas)\n          this.messages.push(newMessage);\n          console.log('✅ Message added to list, total messages:', this.messages.length);\n          // Forcer la détection de changements\n          this.cdr.detectChanges();\n          // Scroll vers le bas après un court délai\n          setTimeout(() => {\n            this.scrollToBottom();\n          }, 50);\n          // Marquer comme lu si ce n'est pas notre message\n          const senderId = newMessage.sender?.id || newMessage.senderId;\n          console.log('📨 Checking if message should be marked as read:', {\n            senderId,\n            currentUserId: this.currentUserId,\n            shouldMarkAsRead: senderId !== this.currentUserId\n          });\n          if (senderId && senderId !== this.currentUserId) {\n            this.markMessageAsRead(newMessage.id);\n          }\n        }\n      },\n      error: error => {\n        console.error('❌ Error in message subscription:', error);\n        this.toastService.showError('Connexion temps réel interrompue');\n        // Réessayer la connexion après 5 secondes\n        setTimeout(() => {\n          this.setupSubscriptions();\n        }, 5000);\n      }\n    }));\n    // Subscription pour les indicateurs de frappe\n    this.subscriptions.add(this.MessageService.subscribeToTypingIndicator(this.conversation.id).subscribe({\n      next: typingData => {\n        // Afficher l'indicateur seulement si c'est l'autre utilisateur qui tape\n        if (typingData.userId !== this.currentUserId) {\n          this.otherUserIsTyping = typingData.isTyping;\n          this.isUserTyping = typingData.isTyping; // Pour compatibilité avec le template\n          console.log('📝 Other user typing status updated:', this.otherUserIsTyping);\n          this.cdr.detectChanges();\n        }\n      },\n      error: error => {\n        console.error('❌ Error in typing subscription:', error);\n      }\n    }));\n    // Subscription pour les mises à jour de conversation\n    this.subscriptions.add(this.MessageService.subscribeToConversationUpdates(this.conversation.id).subscribe({\n      next: conversationUpdate => {\n        // Mettre à jour la conversation si nécessaire\n        if (conversationUpdate.id === this.conversation.id) {\n          this.conversation = {\n            ...this.conversation,\n            ...conversationUpdate\n          };\n          this.cdr.detectChanges();\n        }\n      },\n      error: error => {\n        console.error('❌ Error in conversation subscription:', error);\n      }\n    }));\n  }\n  markMessageAsRead(messageId) {\n    this.MessageService.markMessageAsRead(messageId).subscribe({\n      next: () => {},\n      error: error => {\n        console.error('❌ Error marking message as read:', error);\n      }\n    });\n  }\n  // === ENVOI DE MESSAGES ===\n  sendMessage() {\n    if (!this.messageForm.valid || !this.conversation?.id) return;\n    const content = this.messageForm.get('content')?.value?.trim();\n    if (!content) return;\n    const receiverId = this.otherParticipant?.id || this.otherParticipant?._id;\n    if (!receiverId) {\n      this.toastService.showError('Destinataire introuvable');\n      return;\n    }\n    // Désactiver le bouton d'envoi\n    this.isSendingMessage = true;\n    this.updateInputState();\n    console.log('📤 Sending message:', {\n      content,\n      receiverId,\n      conversationId: this.conversation.id\n    });\n    this.MessageService.sendMessage(receiverId, content, undefined, 'TEXT', this.conversation.id).subscribe({\n      next: message => {\n        // Ajouter le message à la liste s'il n'y est pas déjà\n        const messageExists = this.messages.some(msg => msg.id === message.id);\n        if (!messageExists) {\n          this.messages.push(message);\n          console.log('📋 Message added to local list, total:', this.messages.length);\n        }\n        // Réinitialiser le formulaire\n        this.messageForm.reset();\n        this.isSendingMessage = false;\n        this.updateInputState();\n        // Forcer la détection de changements et scroll\n        this.cdr.detectChanges();\n        setTimeout(() => {\n          this.scrollToBottom();\n        }, 50);\n      },\n      error: error => {\n        console.error(\"❌ Erreur lors de l'envoi du message:\", error);\n        this.toastService.showError(\"Erreur lors de l'envoi du message\");\n        this.isSendingMessage = false;\n        this.updateInputState();\n      }\n    });\n  }\n  scrollToBottom() {\n    setTimeout(() => {\n      if (this.messagesContainer) {\n        const element = this.messagesContainer.nativeElement;\n        element.scrollTop = element.scrollHeight;\n      }\n    }, 100);\n  }\n  // === MÉTHODES UTILITAIRES OPTIMISÉES ===\n  formatLastActive(lastActive) {\n    if (!lastActive) return 'Hors ligne';\n    const diffMins = Math.floor((Date.now() - new Date(lastActive).getTime()) / 60000);\n    if (diffMins < 1) return \"À l'instant\";\n    if (diffMins < 60) return `Il y a ${diffMins} min`;\n    if (diffMins < 1440) return `Il y a ${Math.floor(diffMins / 60)}h`;\n    return `Il y a ${Math.floor(diffMins / 1440)}j`;\n  }\n  // Méthodes utilitaires pour les messages vocaux\n  getVoicePlaybackData(messageId) {\n    return this.voicePlayback[messageId] || {\n      progress: 0,\n      duration: 0,\n      currentTime: 0,\n      speed: 1\n    };\n  }\n  setVoicePlaybackData(messageId, data) {\n    this.voicePlayback[messageId] = {\n      ...this.getVoicePlaybackData(messageId),\n      ...data\n    };\n  }\n  // === MÉTHODES POUR LES MESSAGES VOCAUX (TEMPLATE) ===\n  // === MÉTHODES POUR LES APPELS (TEMPLATE) ===\n  startVideoCall() {\n    if (!this.otherParticipant?.id) {\n      this.toastService.showError(\"Impossible de démarrer l'appel\");\n      return;\n    }\n    this.initiateCall(CallType.VIDEO);\n  }\n  startVoiceCall() {\n    if (!this.otherParticipant?.id) {\n      this.toastService.showError(\"Impossible de démarrer l'appel\");\n      return;\n    }\n    // Forcer la configuration des éléments vidéo avant l'appel\n    this.setupVideoElements();\n    this.initiateCall(CallType.AUDIO);\n  }\n  /**\n   * Bascule l'état du microphone\n   */\n  toggleMicrophone() {\n    this.isMuted = !this.isMuted;\n    if (this.callService.toggleAudio) {\n      this.callService.toggleAudio();\n    }\n    this.toastService.showSuccess(this.isMuted ? 'Microphone coupé' : 'Microphone activé');\n  }\n  /**\n   * Bascule l'état de la caméra\n   */\n  toggleCamera() {\n    this.isVideoEnabled = !this.isVideoEnabled;\n    console.log('📹 Camera toggled:', this.isVideoEnabled ? 'ENABLED' : 'DISABLED');\n    if (this.callService.toggleVideo) {\n      this.callService.toggleVideo();\n    }\n    this.toastService.showSuccess(this.isVideoEnabled ? 'Caméra activée' : 'Caméra désactivée');\n  }\n  /**\n   * Termine l'appel en cours\n   */\n  endCall() {\n    if (this.activeCall) {\n      if (this.callService.endCall) {\n        this.callService.endCall(this.activeCall.id).subscribe({\n          next: () => {\n            this.activeCall = null;\n            this.isCallConnected = false;\n          },\n          error: error => {\n            console.error('❌ Error ending call:', error);\n            this.toastService.showError(\"Erreur lors de la fin de l'appel\");\n          }\n        });\n      } else {\n        // Fallback si la méthode n'existe pas\n        this.activeCall = null;\n        this.isCallConnected = false;\n        this.toastService.showSuccess('Appel terminé');\n      }\n    }\n  }\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // onCallAccepted, onCallRejected - définies plus loin\n  // === MÉTHODES POUR LES FICHIERS (TEMPLATE) ===\n  formatFileSize(bytes) {\n    if (bytes < 1024) return bytes + ' B';\n    if (bytes < 1048576) return Math.round(bytes / 1024) + ' KB';\n    return Math.round(bytes / 1048576) + ' MB';\n  }\n  downloadFile(message) {\n    const fileAttachment = message.attachments?.find(att => !att.type?.startsWith('image/'));\n    if (fileAttachment?.url) {\n      const link = document.createElement('a');\n      link.href = fileAttachment.url;\n      link.download = fileAttachment.name || 'file';\n      link.target = '_blank';\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      this.toastService.showSuccess('Téléchargement démarré');\n    }\n  }\n  // === MÉTHODES POUR L'INTERFACE UTILISATEUR (TEMPLATE) ===\n  toggleSearch() {\n    this.searchMode = !this.searchMode;\n    this.showSearch = this.searchMode;\n  }\n  toggleMainMenu() {\n    this.showMainMenu = !this.showMainMenu;\n  }\n  goBackToConversations() {\n    // Naviguer vers la liste des conversations\n    this.router.navigate(['/front/messages/conversations']).then(() => {}).catch(error => {\n      console.error('❌ Navigation error:', error);\n      // Fallback: essayer la route parent\n      this.router.navigate(['/front/messages']).catch(() => {\n        // Dernier recours: recharger la page\n        window.location.href = '/front/messages/conversations';\n      });\n    });\n  }\n  // === MÉTHODES POUR LES MENUS ET INTERACTIONS ===\n  closeAllMenus() {\n    this.showEmojiPicker = false;\n    this.showAttachmentMenu = false;\n    this.showMainMenu = false;\n    this.showMessageContextMenu = false;\n    this.showReactionPicker = false;\n  }\n  onMessageContextMenu(message, event) {\n    event.preventDefault();\n    this.selectedMessage = message;\n    this.contextMenuPosition = {\n      x: event.clientX,\n      y: event.clientY\n    };\n    this.showMessageContextMenu = true;\n  }\n  showQuickReactions(message, event) {\n    event.stopPropagation();\n    this.reactionPickerMessage = message;\n    this.contextMenuPosition = {\n      x: event.clientX,\n      y: event.clientY\n    };\n    this.showReactionPicker = true;\n  }\n  quickReact(emoji) {\n    if (this.reactionPickerMessage) {\n      this.toggleReaction(this.reactionPickerMessage.id, emoji);\n    }\n    this.showReactionPicker = false;\n  }\n  toggleReaction(messageId, emoji) {\n    if (!messageId || !emoji) {\n      console.error('❌ Missing messageId or emoji for reaction');\n      return;\n    }\n    // Appeler le service pour ajouter/supprimer la réaction\n    this.MessageService.reactToMessage(messageId, emoji).subscribe({\n      next: result => {\n        // Mettre à jour le message local avec les nouvelles réactions\n        const messageIndex = this.messages.findIndex(msg => msg.id === messageId);\n        if (messageIndex !== -1) {\n          this.messages[messageIndex] = result;\n          this.cdr.detectChanges();\n        }\n        this.toastService.showSuccess(`Réaction ${emoji} ajoutée`);\n      },\n      error: error => {\n        console.error('❌ Error toggling reaction:', error);\n        this.toastService.showError(\"Erreur lors de l'ajout de la réaction\");\n      }\n    });\n  }\n  hasUserReacted(reaction, userId) {\n    return reaction.userId === userId;\n  }\n  replyToMessage(message) {\n    this.closeAllMenus();\n  }\n  forwardMessage(message) {\n    this.closeAllMenus();\n  }\n  deleteMessage(message) {\n    if (!message.id) {\n      console.error('❌ No message ID provided for deletion');\n      this.toastService.showError('Erreur: ID du message manquant');\n      return;\n    }\n    // Vérifier si l'utilisateur peut supprimer ce message\n    const canDelete = message.sender?.id === this.currentUserId || message.senderId === this.currentUserId;\n    if (!canDelete) {\n      this.toastService.showError('Vous ne pouvez supprimer que vos propres messages');\n      this.closeAllMenus();\n      return;\n    }\n    // Demander confirmation\n    if (!confirm('Êtes-vous sûr de vouloir supprimer ce message ?')) {\n      this.closeAllMenus();\n      return;\n    }\n    // Appeler le service pour supprimer le message\n    this.MessageService.deleteMessage(message.id).subscribe({\n      next: result => {\n        // Supprimer le message de la liste locale\n        this.messages = this.messages.filter(msg => msg.id !== message.id);\n        this.toastService.showSuccess('Message supprimé');\n        this.cdr.detectChanges();\n      },\n      error: error => {\n        console.error('❌ Error deleting message:', error);\n        this.toastService.showError('Erreur lors de la suppression du message');\n      }\n    });\n    this.closeAllMenus();\n  }\n  // === MÉTHODES POUR LES ÉMOJIS ET PIÈCES JOINTES ===\n  toggleEmojiPicker() {\n    this.showEmojiPicker = !this.showEmojiPicker;\n  }\n  selectEmojiCategory(category) {\n    this.selectedEmojiCategory = category;\n  }\n  getEmojisForCategory(category) {\n    return category?.emojis || [];\n  }\n  insertEmoji(emoji) {\n    const currentContent = this.messageForm.get('content')?.value || '';\n    const newContent = currentContent + emoji.emoji;\n    this.messageForm.patchValue({\n      content: newContent\n    });\n    this.showEmojiPicker = false;\n  }\n  toggleAttachmentMenu() {\n    this.showAttachmentMenu = !this.showAttachmentMenu;\n  }\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // triggerFileInput, getFileAcceptTypes, onFileSelected - définies plus loin\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // Ces méthodes sont déjà définies plus loin dans le fichier\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // handleTypingIndicator - définie plus loin\n  // === MÉTHODES UTILITAIRES POUR LE TEMPLATE ===\n  trackByMessageId(index, message) {\n    return message.id || message._id || index.toString();\n  }\n  // === MÉTHODES MANQUANTES POUR LE TEMPLATE ===\n  testAddMessage() {\n    const testMessage = {\n      id: `test-${Date.now()}`,\n      content: `Message de test ${new Date().toLocaleTimeString()}`,\n      timestamp: new Date().toISOString(),\n      sender: {\n        id: this.otherParticipant?.id || 'test-user',\n        username: this.otherParticipant?.username || 'Test User',\n        image: this.otherParticipant?.image || 'assets/images/default-avatar.png'\n      },\n      type: 'TEXT',\n      isRead: false\n    };\n    this.messages.push(testMessage);\n    this.cdr.detectChanges();\n    setTimeout(() => this.scrollToBottom(), 50);\n  }\n  isGroupConversation() {\n    return this.conversation?.isGroup || this.conversation?.participants?.length > 2 || false;\n  }\n  openCamera() {\n    this.showAttachmentMenu = false;\n    // TODO: Implémenter l'ouverture de la caméra\n  }\n\n  zoomImage(factor) {\n    const imageElement = document.querySelector('.image-viewer-zoom');\n    if (imageElement) {\n      const currentTransform = imageElement.style.transform || 'scale(1)';\n      const currentScale = parseFloat(currentTransform.match(/scale\\(([^)]+)\\)/)?.[1] || '1');\n      const newScale = Math.max(0.5, Math.min(3, currentScale * factor));\n      imageElement.style.transform = `scale(${newScale})`;\n      if (newScale > 1) {\n        imageElement.classList.add('zoomed');\n      } else {\n        imageElement.classList.remove('zoomed');\n      }\n    }\n  }\n  resetZoom() {\n    const imageElement = document.querySelector('.image-viewer-zoom');\n    if (imageElement) {\n      imageElement.style.transform = 'scale(1)';\n      imageElement.classList.remove('zoomed');\n    }\n  }\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // formatFileSize, downloadFile, toggleReaction, hasUserReacted, showQuickReactions\n  // toggleEmojiPicker, toggleAttachmentMenu, selectEmojiCategory, getEmojisForCategory, insertEmoji\n  // Ces méthodes sont déjà définies plus loin dans le fichier\n  triggerFileInput(type) {\n    const input = this.fileInput?.nativeElement;\n    if (!input) {\n      console.error('File input element not found');\n      return;\n    }\n    // Configurer le type de fichier accepté\n    if (type === 'image') {\n      input.accept = 'image/*';\n    } else if (type === 'video') {\n      input.accept = 'video/*';\n    } else if (type === 'document') {\n      input.accept = '.pdf,.doc,.docx,.xls,.xlsx,.txt';\n    } else {\n      input.accept = '*/*';\n    }\n    // Réinitialiser la valeur pour permettre la sélection du même fichier\n    input.value = '';\n    // Déclencher la sélection de fichier\n    input.click();\n    this.showAttachmentMenu = false;\n  }\n  formatMessageTime(timestamp) {\n    if (!timestamp) return '';\n    const date = new Date(timestamp);\n    return date.toLocaleTimeString('fr-FR', {\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  }\n  formatDateSeparator(timestamp) {\n    if (!timestamp) return '';\n    const date = new Date(timestamp);\n    const today = new Date();\n    const yesterday = new Date(today);\n    yesterday.setDate(yesterday.getDate() - 1);\n    if (date.toDateString() === today.toDateString()) {\n      return \"Aujourd'hui\";\n    } else if (date.toDateString() === yesterday.toDateString()) {\n      return 'Hier';\n    } else {\n      return date.toLocaleDateString('fr-FR');\n    }\n  }\n  formatMessageContent(content) {\n    if (!content) return '';\n    // Remplacer les URLs par des liens\n    const urlRegex = /(https?:\\/\\/[^\\s]+)/g;\n    return content.replace(urlRegex, '<a href=\"$1\" target=\"_blank\" class=\"text-blue-500 underline\">$1</a>');\n  }\n  shouldShowDateSeparator(index) {\n    if (index === 0) return true;\n    const currentMessage = this.messages[index];\n    const previousMessage = this.messages[index - 1];\n    if (!currentMessage?.timestamp || !previousMessage?.timestamp) return false;\n    const currentDate = new Date(currentMessage.timestamp).toDateString();\n    const previousDate = new Date(previousMessage.timestamp).toDateString();\n    return currentDate !== previousDate;\n  }\n  shouldShowAvatar(index) {\n    const currentMessage = this.messages[index];\n    const nextMessage = this.messages[index + 1];\n    if (!nextMessage) return true;\n    return currentMessage.sender?.id !== nextMessage.sender?.id;\n  }\n  shouldShowSenderName(index) {\n    const currentMessage = this.messages[index];\n    const previousMessage = this.messages[index - 1];\n    if (!previousMessage) return true;\n    return currentMessage.sender?.id !== previousMessage.sender?.id;\n  }\n  getMessageType(message) {\n    // Vérifier d'abord le type de message explicite\n    if (message.type) {\n      if (message.type === 'IMAGE' || message.type === 'image') return 'image';\n      if (message.type === 'VIDEO' || message.type === 'video') return 'video';\n      if (message.type === 'AUDIO' || message.type === 'audio') return 'audio';\n      if (message.type === 'VOICE_MESSAGE') return 'audio';\n      if (message.type === 'FILE' || message.type === 'file') return 'file';\n    }\n    // Ensuite vérifier les attachments\n    if (message.attachments && message.attachments.length > 0) {\n      const attachment = message.attachments[0];\n      if (attachment.type?.startsWith('image/')) return 'image';\n      if (attachment.type?.startsWith('video/')) return 'video';\n      if (attachment.type?.startsWith('audio/')) return 'audio';\n      return 'file';\n    }\n    // Vérifier si c'est un message vocal basé sur les propriétés\n    if (message.voiceUrl || message.audioUrl || message.voice) return 'audio';\n    return 'text';\n  }\n  hasImage(message) {\n    // Vérifier le type de message\n    if (message.type === 'IMAGE' || message.type === 'image') {\n      return true;\n    }\n    // Vérifier les attachments\n    const hasImageAttachment = message.attachments?.some(att => {\n      return att.type?.startsWith('image/') || att.type === 'IMAGE';\n    }) || false;\n    // Vérifier les propriétés directes d'image\n    const hasImageUrl = !!(message.imageUrl || message.image);\n    return hasImageAttachment || hasImageUrl;\n  }\n  hasFile(message) {\n    // Vérifier le type de message\n    if (message.type === 'FILE' || message.type === 'file') {\n      return true;\n    }\n    // Vérifier les attachments non-image\n    const hasFileAttachment = message.attachments?.some(att => {\n      return !att.type?.startsWith('image/') && att.type !== 'IMAGE';\n    }) || false;\n    return hasFileAttachment;\n  }\n  getImageUrl(message) {\n    // Vérifier les propriétés directes d'image\n    if (message.imageUrl) {\n      return message.imageUrl;\n    }\n    if (message.image) {\n      return message.image;\n    }\n    // Vérifier les attachments\n    const imageAttachment = message.attachments?.find(att => att.type?.startsWith('image/') || att.type === 'IMAGE');\n    if (imageAttachment) {\n      return imageAttachment.url || imageAttachment.path || '';\n    }\n    return '';\n  }\n  getFileName(message) {\n    const fileAttachment = message.attachments?.find(att => !att.type?.startsWith('image/'));\n    return fileAttachment?.name || 'Fichier';\n  }\n  getFileSize(message) {\n    const fileAttachment = message.attachments?.find(att => !att.type?.startsWith('image/'));\n    if (!fileAttachment?.size) return '';\n    const bytes = fileAttachment.size;\n    if (bytes < 1024) return bytes + ' B';\n    if (bytes < 1048576) return Math.round(bytes / 1024) + ' KB';\n    return Math.round(bytes / 1048576) + ' MB';\n  }\n  getFileIcon(message) {\n    const fileAttachment = message.attachments?.find(att => !att.type?.startsWith('image/'));\n    if (!fileAttachment?.type) return 'fas fa-file';\n    if (fileAttachment.type.startsWith('audio/')) return 'fas fa-file-audio';\n    if (fileAttachment.type.startsWith('video/')) return 'fas fa-file-video';\n    if (fileAttachment.type.includes('pdf')) return 'fas fa-file-pdf';\n    if (fileAttachment.type.includes('word')) return 'fas fa-file-word';\n    if (fileAttachment.type.includes('excel')) return 'fas fa-file-excel';\n    return 'fas fa-file';\n  }\n  getUserColor(userId) {\n    // Générer une couleur basée sur l'ID utilisateur\n    const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8'];\n    const index = userId.charCodeAt(0) % colors.length;\n    return colors[index];\n  }\n  // === MÉTHODES D'INTERACTION ===\n  onMessageClick(message, event) {}\n  onInputChange(event) {\n    // Gérer les changements dans le champ de saisie\n    this.handleTypingIndicator();\n  }\n  onInputKeyDown(event) {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n  onInputFocus() {\n    // Gérer le focus sur le champ de saisie\n  }\n  onInputBlur() {\n    // Gérer la perte de focus sur le champ de saisie\n  }\n  onScroll(event) {\n    // Gérer le scroll pour charger plus de messages\n    const element = event.target;\n    if (element.scrollTop === 0 && this.hasMoreMessages && !this.isLoadingMore) {\n      this.loadMoreMessages();\n    }\n  }\n  openUserProfile(userId) {}\n  onImageLoad(event, message) {\n    console.log('🖼️ [Debug] Image loaded successfully for message:', message.id, event.target.src);\n  }\n  onImageError(event, message) {\n    console.error('🖼️ [Debug] Image failed to load for message:', message.id, {\n      src: event.target.src,\n      error: event\n    });\n    // Optionnel : afficher une image de remplacement\n    event.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjNmNGY2Ii8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzZiNzI4MCIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkltYWdlIG5vbiBkaXNwb25pYmxlPC90ZXh0Pjwvc3ZnPg==';\n  }\n  openImageViewer(message) {\n    const imageAttachment = message.attachments?.find(att => att.type?.startsWith('image/'));\n    if (imageAttachment?.url) {\n      this.selectedImage = {\n        url: imageAttachment.url,\n        name: imageAttachment.name || 'Image',\n        size: this.formatFileSize(imageAttachment.size || 0),\n        message: message\n      };\n      this.showImageViewer = true;\n    }\n  }\n  closeImageViewer() {\n    this.showImageViewer = false;\n    this.selectedImage = null;\n  }\n  downloadImage() {\n    if (this.selectedImage?.url) {\n      const link = document.createElement('a');\n      link.href = this.selectedImage.url;\n      link.download = this.selectedImage.name || 'image';\n      link.target = '_blank';\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      this.toastService.showSuccess('Téléchargement démarré');\n      console.log('🖼️ [ImageViewer] Download started:', this.selectedImage.name);\n    }\n  }\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // zoomImage, resetZoom, formatFileSize, downloadFile, toggleReaction, hasUserReacted, toggleSearch\n  // Ces méthodes sont déjà définies plus loin dans le fichier\n  searchMessages() {\n    if (!this.searchQuery.trim()) {\n      this.searchResults = [];\n      return;\n    }\n    this.searchResults = this.messages.filter(message => message.content?.toLowerCase().includes(this.searchQuery.toLowerCase()) || message.sender?.username?.toLowerCase().includes(this.searchQuery.toLowerCase()));\n  }\n  onSearchQueryChange() {\n    this.searchMessages();\n  }\n  clearSearch() {\n    this.searchQuery = '';\n    this.searchResults = [];\n  }\n  jumpToMessage(messageId) {\n    const messageElement = document.getElementById(`message-${messageId}`);\n    if (messageElement) {\n      messageElement.scrollIntoView({\n        behavior: 'smooth',\n        block: 'center'\n      });\n      // Highlight temporairement le message\n      messageElement.classList.add('highlight');\n      setTimeout(() => {\n        messageElement.classList.remove('highlight');\n      }, 2000);\n    }\n  }\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // toggleMainMenu, toggleTheme, onMessageContextMenu\n  // Ces méthodes sont déjà définies plus loin dans le fichier\n  closeContextMenu() {\n    this.showMessageContextMenu = false;\n    this.selectedMessage = null;\n  }\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // replyToMessage, forwardMessage, deleteMessage, showQuickReactions, closeReactionPicker\n  // quickReact, closeAllMenus, testAddMessage, toggleAttachmentMenu, toggleEmojiPicker\n  // Ces méthodes sont déjà définies plus loin dans le fichier\n  // === MÉTHODE SUPPRIMÉE (DUPLICATION) ===\n  // triggerFileInput - définie plus loin\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // openCamera, getEmojisForCategory, selectEmojiCategory, insertEmoji\n  // goBackToConversations, startVideoCall, startVoiceCall\n  // Ces méthodes sont déjà définies plus loin dans le fichier\n  initiateCall(callType) {\n    console.log('📋 [MessageChat] Call details:', {\n      callType,\n      otherParticipant: this.otherParticipant,\n      conversation: this.conversation?.id,\n      currentUserId: this.currentUserId\n    });\n    if (!this.otherParticipant) {\n      console.error('❌ [MessageChat] No recipient selected');\n      this.toastService.showError('Aucun destinataire sélectionné');\n      return;\n    }\n    const recipientId = this.otherParticipant.id || this.otherParticipant._id;\n    if (!recipientId) {\n      console.error('❌ [MessageChat] Recipient ID not found');\n      this.toastService.showError('ID du destinataire introuvable');\n      return;\n    }\n    console.log(`📞 [MessageChat] Initiating ${callType} call to user:`, {\n      recipientId,\n      recipientName: this.otherParticipant.username || this.otherParticipant.name,\n      conversationId: this.conversation?.id\n    });\n    this.isInCall = true;\n    this.callType = callType === CallType.VIDEO ? 'VIDEO' : 'AUDIO';\n    this.callDuration = 0;\n    // Démarrer le timer d'appel\n    this.startCallTimer();\n    // Utiliser le CallService\n    this.callService.initiateCall(recipientId, callType, this.conversation?.id).subscribe({\n      next: call => {\n        this.activeCall = call;\n        this.isCallConnected = false;\n        this.toastService.showSuccess(`Appel ${callType === CallType.VIDEO ? 'vidéo' : 'audio'} initié`);\n        console.log('📡 [MessageChat] Call should now be sent to recipient via WebSocket');\n      },\n      error: error => {\n        console.error('❌ [MessageChat] Error initiating call:', {\n          error: error.message || error,\n          recipientId,\n          callType,\n          conversationId: this.conversation?.id\n        });\n        this.endCall();\n        this.toastService.showError(\"Erreur lors de l'initiation de l'appel\");\n      }\n    });\n  }\n  acceptCall(incomingCall) {\n    this.callService.acceptCall(incomingCall).subscribe({\n      next: call => {\n        this.activeCall = call;\n        this.isInCall = true;\n        this.isCallConnected = true;\n        this.callType = call.type === CallType.VIDEO ? 'VIDEO' : 'AUDIO';\n        this.startCallTimer();\n        this.toastService.showSuccess('Appel accepté');\n      },\n      error: error => {\n        console.error('❌ Error accepting call:', error);\n        this.toastService.showError(\"Erreur lors de l'acceptation de l'appel\");\n      }\n    });\n  }\n  rejectCall(incomingCall) {\n    this.callService.rejectCall(incomingCall.id, 'User rejected').subscribe({\n      next: () => {\n        this.toastService.showSuccess('Appel rejeté');\n      },\n      error: error => {\n        console.error('❌ Error rejecting call:', error);\n        this.toastService.showError(\"Erreur lors du rejet de l'appel\");\n      }\n    });\n  }\n  startCallTimer() {\n    this.callDuration = 0;\n    this.callTimer = setInterval(() => {\n      this.callDuration++;\n      this.cdr.detectChanges();\n    }, 1000);\n  }\n  resetCallState() {\n    if (this.callTimer) {\n      clearInterval(this.callTimer);\n      this.callTimer = null;\n    }\n    this.isInCall = false;\n    this.callType = null;\n    this.callDuration = 0;\n    this.activeCall = null;\n    this.isCallConnected = false;\n    this.isMuted = false;\n    this.isVideoEnabled = true;\n  }\n  // === CONTRÔLES D'APPEL ===\n  toggleMute() {\n    if (!this.activeCall) return;\n    this.isMuted = !this.isMuted;\n    // Utiliser la méthode toggleMedia du CallService\n    this.callService.toggleMedia(this.activeCall.id, undefined,\n    // video unchanged\n    !this.isMuted // audio state\n    ).subscribe({\n      next: () => {\n        this.toastService.showSuccess(this.isMuted ? 'Micro coupé' : 'Micro activé');\n      },\n      error: error => {\n        console.error('❌ Error toggling mute:', error);\n        // Revert state on error\n        this.isMuted = !this.isMuted;\n        this.toastService.showError('Erreur lors du changement du micro');\n      }\n    });\n  }\n  toggleVideo() {\n    if (!this.activeCall) return;\n    this.isVideoEnabled = !this.isVideoEnabled;\n    // Utiliser la méthode toggleMedia du CallService\n    this.callService.toggleMedia(this.activeCall.id, this.isVideoEnabled,\n    // video state\n    undefined // audio unchanged\n    ).subscribe({\n      next: () => {\n        this.toastService.showSuccess(this.isVideoEnabled ? 'Caméra activée' : 'Caméra désactivée');\n      },\n      error: error => {\n        console.error('❌ Error toggling video:', error);\n        // Revert state on error\n        this.isVideoEnabled = !this.isVideoEnabled;\n        this.toastService.showError('Erreur lors du changement de la caméra');\n      }\n    });\n  }\n  formatCallDuration(duration) {\n    const hours = Math.floor(duration / 3600);\n    const minutes = Math.floor(duration % 3600 / 60);\n    const seconds = duration % 60;\n    if (hours > 0) {\n      return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;\n    }\n    return `${minutes}:${seconds.toString().padStart(2, '0')}`;\n  }\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // trackByMessageId, isGroupConversation - Ces méthodes sont déjà définies plus loin dans le fichier\n  startVoiceRecording() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        // Vérifier le support du navigateur\n        if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {\n          throw new Error(\"Votre navigateur ne supporte pas l'enregistrement audio\");\n        }\n        // Vérifier si MediaRecorder est supporté\n        if (!window.MediaRecorder) {\n          throw new Error(\"MediaRecorder n'est pas supporté par votre navigateur\");\n        }\n        // Demander l'accès au microphone avec des contraintes optimisées\n        const stream = yield navigator.mediaDevices.getUserMedia({\n          audio: {\n            echoCancellation: true,\n            noiseSuppression: true,\n            autoGainControl: true,\n            sampleRate: 44100,\n            channelCount: 1\n          }\n        });\n        // Vérifier les types MIME supportés\n        let mimeType = 'audio/webm;codecs=opus';\n        if (!MediaRecorder.isTypeSupported(mimeType)) {\n          mimeType = 'audio/webm';\n          if (!MediaRecorder.isTypeSupported(mimeType)) {\n            mimeType = 'audio/mp4';\n            if (!MediaRecorder.isTypeSupported(mimeType)) {\n              mimeType = ''; // Laisser le navigateur choisir\n            }\n          }\n        }\n        // Créer le MediaRecorder\n        _this.mediaRecorder = new MediaRecorder(stream, {\n          mimeType: mimeType || undefined\n        });\n        // Initialiser les variables\n        _this.audioChunks = [];\n        _this.isRecordingVoice = true;\n        _this.voiceRecordingDuration = 0;\n        _this.voiceRecordingState = 'recording';\n        // Démarrer le timer\n        _this.recordingTimer = setInterval(() => {\n          _this.voiceRecordingDuration++;\n          // Animer les waves\n          _this.animateVoiceWaves();\n          _this.cdr.detectChanges();\n        }, 1000);\n        // Gérer les événements du MediaRecorder\n        _this.mediaRecorder.ondataavailable = event => {\n          if (event.data.size > 0) {\n            _this.audioChunks.push(event.data);\n          }\n        };\n        _this.mediaRecorder.onstop = () => {\n          _this.processRecordedAudio();\n        };\n        _this.mediaRecorder.onerror = event => {\n          console.error('🎤 [Voice] MediaRecorder error:', event.error);\n          _this.toastService.showError(\"Erreur lors de l'enregistrement\");\n          _this.cancelVoiceRecording();\n        };\n        // Démarrer l'enregistrement\n        _this.mediaRecorder.start(100); // Collecter les données toutes les 100ms\n        _this.toastService.showSuccess('🎤 Enregistrement vocal démarré');\n      } catch (error) {\n        console.error('🎤 [Voice] Error starting recording:', error);\n        let errorMessage = \"Impossible de démarrer l'enregistrement vocal\";\n        if (error.name === 'NotAllowedError') {\n          errorMessage = \"Accès au microphone refusé. Veuillez autoriser l'accès dans les paramètres de votre navigateur.\";\n        } else if (error.name === 'NotFoundError') {\n          errorMessage = 'Aucun microphone détecté. Veuillez connecter un microphone.';\n        } else if (error.name === 'NotSupportedError') {\n          errorMessage = \"Votre navigateur ne supporte pas l'enregistrement audio.\";\n        } else if (error.message) {\n          errorMessage = error.message;\n        }\n        _this.toastService.showError(errorMessage);\n        _this.cancelVoiceRecording();\n      }\n    })();\n  }\n  stopVoiceRecording() {\n    if (this.mediaRecorder && this.mediaRecorder.state === 'recording') {\n      this.mediaRecorder.stop();\n      this.mediaRecorder.stream.getTracks().forEach(track => track.stop());\n    }\n    if (this.recordingTimer) {\n      clearInterval(this.recordingTimer);\n      this.recordingTimer = null;\n    }\n    this.isRecordingVoice = false;\n    this.voiceRecordingState = 'processing';\n  }\n  cancelVoiceRecording() {\n    if (this.mediaRecorder) {\n      if (this.mediaRecorder.state === 'recording') {\n        this.mediaRecorder.stop();\n      }\n      this.mediaRecorder.stream.getTracks().forEach(track => track.stop());\n      this.mediaRecorder = null;\n    }\n    if (this.recordingTimer) {\n      clearInterval(this.recordingTimer);\n      this.recordingTimer = null;\n    }\n    this.isRecordingVoice = false;\n    this.voiceRecordingDuration = 0;\n    this.voiceRecordingState = 'idle';\n    this.audioChunks = [];\n  }\n  processRecordedAudio() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        // Vérifier qu'on a des données audio\n        if (_this2.audioChunks.length === 0) {\n          console.error('🎤 [Voice] No audio chunks available');\n          _this2.toastService.showError('Aucun audio enregistré');\n          _this2.cancelVoiceRecording();\n          return;\n        }\n        console.log('🎤 [Voice] Audio chunks:', _this2.audioChunks.length, 'Duration:', _this2.voiceRecordingDuration);\n        // Vérifier la durée minimale\n        if (_this2.voiceRecordingDuration < 1) {\n          console.error('🎤 [Voice] Recording too short:', _this2.voiceRecordingDuration);\n          _this2.toastService.showError('Enregistrement trop court (minimum 1 seconde)');\n          _this2.cancelVoiceRecording();\n          return;\n        }\n        // Déterminer le type MIME du blob\n        let mimeType = 'audio/webm;codecs=opus';\n        if (_this2.mediaRecorder?.mimeType) {\n          mimeType = _this2.mediaRecorder.mimeType;\n        }\n        // Créer le blob audio\n        const audioBlob = new Blob(_this2.audioChunks, {\n          type: mimeType\n        });\n        console.log('🎤 [Voice] Audio blob created:', {\n          size: audioBlob.size,\n          type: audioBlob.type\n        });\n        // Déterminer l'extension du fichier\n        let extension = '.webm';\n        if (mimeType.includes('mp4')) {\n          extension = '.mp4';\n        } else if (mimeType.includes('wav')) {\n          extension = '.wav';\n        } else if (mimeType.includes('ogg')) {\n          extension = '.ogg';\n        }\n        // Créer le fichier\n        const audioFile = new File([audioBlob], `voice_${Date.now()}${extension}`, {\n          type: mimeType\n        });\n        console.log('🎤 [Voice] Audio file created:', {\n          name: audioFile.name,\n          size: audioFile.size,\n          type: audioFile.type\n        });\n        // Envoyer le message vocal\n        _this2.voiceRecordingState = 'processing';\n        yield _this2.sendVoiceMessage(audioFile);\n        _this2.toastService.showSuccess('🎤 Message vocal envoyé');\n      } catch (error) {\n        console.error('🎤 [Voice] Error processing audio:', error);\n        _this2.toastService.showError(\"Erreur lors de l'envoi du message vocal: \" + (error.message || 'Erreur inconnue'));\n      } finally {\n        // Nettoyer l'état\n        _this2.voiceRecordingState = 'idle';\n        _this2.voiceRecordingDuration = 0;\n        _this2.audioChunks = [];\n        _this2.isRecordingVoice = false;\n      }\n    })();\n  }\n  sendVoiceMessage(audioFile) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      const receiverId = _this3.otherParticipant?.id || _this3.otherParticipant?._id;\n      if (!receiverId) {\n        throw new Error('Destinataire introuvable');\n      }\n      return new Promise((resolve, reject) => {\n        _this3.MessageService.sendMessage(receiverId, '', audioFile, 'AUDIO', _this3.conversation.id).subscribe({\n          next: message => {\n            _this3.messages.push(message);\n            _this3.scrollToBottom();\n            resolve();\n          },\n          error: error => {\n            console.error(\"Erreur lors de l'envoi du message vocal:\", error);\n            reject(error);\n          }\n        });\n      });\n    })();\n  }\n  formatRecordingDuration(duration) {\n    const minutes = Math.floor(duration / 60);\n    const seconds = duration % 60;\n    return `${minutes}:${seconds.toString().padStart(2, '0')}`;\n  }\n  // === MÉTHODES D'ENREGISTREMENT VOCAL AMÉLIORÉES ===\n  onRecordStart(event) {\n    event.preventDefault();\n    console.log('🎤 [Voice] Current state:', {\n      isRecordingVoice: this.isRecordingVoice,\n      voiceRecordingState: this.voiceRecordingState,\n      voiceRecordingDuration: this.voiceRecordingDuration,\n      mediaRecorder: !!this.mediaRecorder\n    });\n    // Vérifier si on peut enregistrer\n    if (this.voiceRecordingState === 'processing') {\n      this.toastService.showWarning('Traitement en cours...');\n      return;\n    }\n    if (this.isRecordingVoice) {\n      this.toastService.showWarning('Enregistrement déjà en cours...');\n      return;\n    }\n    // Afficher un message de début\n    this.toastService.showInfo(\"🎤 Démarrage de l'enregistrement vocal...\");\n    // Démarrer l'enregistrement\n    this.startVoiceRecording().catch(error => {\n      console.error('🎤 [Voice] Failed to start recording:', error);\n      this.toastService.showError(\"Impossible de démarrer l'enregistrement vocal: \" + (error.message || 'Erreur inconnue'));\n    });\n  }\n  onRecordEnd(event) {\n    event.preventDefault();\n    if (!this.isRecordingVoice) {\n      return;\n    }\n    // Arrêter l'enregistrement et envoyer\n    this.stopVoiceRecording();\n  }\n  onRecordCancel(event) {\n    event.preventDefault();\n    if (!this.isRecordingVoice) {\n      return;\n    }\n    // Annuler l'enregistrement\n    this.cancelVoiceRecording();\n  }\n  getRecordingFormat() {\n    if (this.mediaRecorder?.mimeType) {\n      if (this.mediaRecorder.mimeType.includes('webm')) return 'WebM';\n      if (this.mediaRecorder.mimeType.includes('mp4')) return 'MP4';\n      if (this.mediaRecorder.mimeType.includes('wav')) return 'WAV';\n      if (this.mediaRecorder.mimeType.includes('ogg')) return 'OGG';\n    }\n    return 'Auto';\n  }\n  // === ANIMATION DES WAVES VOCALES ===\n  animateVoiceWaves() {\n    // Animer les waves pendant l'enregistrement\n    this.voiceWaves = this.voiceWaves.map(() => {\n      return Math.floor(Math.random() * 20) + 4; // Hauteur entre 4 et 24px\n    });\n  }\n\n  onFileSelected(event) {\n    const files = event.target.files;\n    if (!files || files.length === 0) {\n      return;\n    }\n    for (let file of files) {\n      console.log(`📁 [Upload] Processing file: ${file.name}, size: ${file.size}, type: ${file.type}`);\n      this.uploadFile(file);\n    }\n  }\n  uploadFile(file) {\n    const receiverId = this.otherParticipant?.id || this.otherParticipant?._id;\n    if (!receiverId) {\n      console.error('📁 [Upload] No receiver ID found');\n      this.toastService.showError('Destinataire introuvable');\n      return;\n    }\n    // Vérifier la taille du fichier (max 50MB)\n    const maxSize = 50 * 1024 * 1024; // 50MB\n    if (file.size > maxSize) {\n      console.error(`📁 [Upload] File too large: ${file.size} bytes`);\n      this.toastService.showError('Fichier trop volumineux (max 50MB)');\n      return;\n    }\n    // 🖼️ Compression d'image si nécessaire\n    if (file.type.startsWith('image/') && file.size > 1024 * 1024) {\n      // > 1MB\n      console.log('🖼️ [Compression] Compressing image:', file.name, 'Original size:', file.size);\n      this.compressImage(file).then(compressedFile => {\n        console.log('🖼️ [Compression] ✅ Image compressed successfully. New size:', compressedFile.size);\n        this.sendFileToServer(compressedFile, receiverId);\n      }).catch(error => {\n        console.error('🖼️ [Compression] ❌ Error compressing image:', error);\n        // Envoyer le fichier original en cas d'erreur\n        this.sendFileToServer(file, receiverId);\n      });\n      return;\n    }\n    // Envoyer le fichier sans compression\n    this.sendFileToServer(file, receiverId);\n  }\n  sendFileToServer(file, receiverId) {\n    const messageType = this.getFileMessageType(file);\n    this.isSendingMessage = true;\n    this.isUploading = true;\n    this.uploadProgress = 0;\n    // Simuler la progression d'upload\n    const progressInterval = setInterval(() => {\n      this.uploadProgress += Math.random() * 15;\n      if (this.uploadProgress >= 90) {\n        clearInterval(progressInterval);\n      }\n      this.cdr.detectChanges();\n    }, 300);\n    this.MessageService.sendMessage(receiverId, '', file, messageType, this.conversation.id).subscribe({\n      next: message => {\n        console.log('📁 [Debug] Sent message structure:', {\n          id: message.id,\n          type: message.type,\n          attachments: message.attachments,\n          hasImage: this.hasImage(message),\n          hasFile: this.hasFile(message),\n          imageUrl: this.getImageUrl(message)\n        });\n        clearInterval(progressInterval);\n        this.uploadProgress = 100;\n        setTimeout(() => {\n          this.messages.push(message);\n          this.scrollToBottom();\n          this.toastService.showSuccess('Fichier envoyé avec succès');\n          this.resetUploadState();\n        }, 500);\n      },\n      error: error => {\n        console.error('📁 [Upload] ❌ Error sending file:', error);\n        clearInterval(progressInterval);\n        this.toastService.showError(\"Erreur lors de l'envoi du fichier\");\n        this.resetUploadState();\n      }\n    });\n  }\n  getFileMessageType(file) {\n    if (file.type.startsWith('image/')) return 'IMAGE';\n    if (file.type.startsWith('video/')) return 'VIDEO';\n    if (file.type.startsWith('audio/')) return 'AUDIO';\n    return 'FILE';\n  }\n  getFileAcceptTypes() {\n    return '*/*';\n  }\n  resetUploadState() {\n    this.isSendingMessage = false;\n    this.isUploading = false;\n    this.uploadProgress = 0;\n  }\n  // === DRAG & DROP ===\n  onDragOver(event) {\n    event.preventDefault();\n    event.stopPropagation();\n    this.isDragOver = true;\n  }\n  onDragLeave(event) {\n    event.preventDefault();\n    event.stopPropagation();\n    // Vérifier si on quitte vraiment la zone (pas un enfant)\n    const rect = event.currentTarget.getBoundingClientRect();\n    const x = event.clientX;\n    const y = event.clientY;\n    if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {\n      this.isDragOver = false;\n    }\n  }\n  onDrop(event) {\n    event.preventDefault();\n    event.stopPropagation();\n    this.isDragOver = false;\n    const files = event.dataTransfer?.files;\n    if (files && files.length > 0) {\n      // Traiter chaque fichier\n      Array.from(files).forEach(file => {\n        console.log('📁 [Drag&Drop] Processing file:', file.name, file.type, file.size);\n        this.uploadFile(file);\n      });\n      this.toastService.showSuccess(`${files.length} fichier(s) en cours d'envoi`);\n    }\n  }\n  // === COMPRESSION D'IMAGES ===\n  compressImage(file, quality = 0.8) {\n    return new Promise((resolve, reject) => {\n      const canvas = document.createElement('canvas');\n      const ctx = canvas.getContext('2d');\n      const img = new Image();\n      img.onload = () => {\n        // Calculer les nouvelles dimensions (max 1920x1080)\n        const maxWidth = 1920;\n        const maxHeight = 1080;\n        let {\n          width,\n          height\n        } = img;\n        if (width > maxWidth || height > maxHeight) {\n          const ratio = Math.min(maxWidth / width, maxHeight / height);\n          width *= ratio;\n          height *= ratio;\n        }\n        canvas.width = width;\n        canvas.height = height;\n        // Dessiner l'image redimensionnée\n        ctx?.drawImage(img, 0, 0, width, height);\n        // Convertir en blob avec compression\n        canvas.toBlob(blob => {\n          if (blob) {\n            const compressedFile = new File([blob], file.name, {\n              type: file.type,\n              lastModified: Date.now()\n            });\n            resolve(compressedFile);\n          } else {\n            reject(new Error('Failed to compress image'));\n          }\n        }, file.type, quality);\n      };\n      img.onerror = () => reject(new Error('Failed to load image'));\n      img.src = URL.createObjectURL(file);\n    });\n  }\n  // === MÉTHODES DE FORMATAGE SUPPLÉMENTAIRES ===\n  handleTypingIndicator() {\n    if (!this.isTyping) {\n      this.isTyping = true;\n      // Envoyer l'indicateur de frappe à l'autre utilisateur\n      this.sendTypingIndicator(true);\n    }\n    // Reset le timer\n    if (this.typingTimeout) {\n      clearTimeout(this.typingTimeout);\n    }\n    this.typingTimeout = setTimeout(() => {\n      this.isTyping = false;\n      // Arrêter l'indicateur de frappe\n      this.sendTypingIndicator(false);\n    }, 2000);\n  }\n  sendTypingIndicator(isTyping) {\n    // Envoyer l'indicateur de frappe via WebSocket/GraphQL\n    const receiverId = this.otherParticipant?.id || this.otherParticipant?._id;\n    if (receiverId && this.conversation?.id) {\n      console.log(`📝 Sending typing indicator: ${isTyping} to user ${receiverId}`);\n      // TODO: Implémenter l'envoi via WebSocket/GraphQL subscription\n      // this.MessageService.sendTypingIndicator(this.conversation.id, receiverId, isTyping);\n    }\n  }\n  // === MÉTHODES POUR L'INTERFACE D'APPEL ===\n  onCallAccepted(call) {\n    this.activeCall = call;\n    this.isInCall = true;\n    this.isCallConnected = true;\n    this.startCallTimer();\n    this.toastService.showSuccess('Appel accepté');\n  }\n  onCallRejected() {\n    this.endCall();\n    this.toastService.showInfo('Appel rejeté');\n  }\n  // === MÉTHODES POUR LA LECTURE DES MESSAGES VOCAUX ===\n  playVoiceMessage(message) {\n    this.toggleVoicePlayback(message);\n  }\n  isVoicePlaying(messageId) {\n    return this.playingMessageId === messageId;\n  }\n  toggleVoicePlayback(message) {\n    const messageId = message.id;\n    const audioUrl = this.getVoiceUrl(message);\n    if (!audioUrl) {\n      console.error('🎵 [Voice] No audio URL found for message:', messageId);\n      this.toastService.showError('Fichier audio introuvable');\n      return;\n    }\n    // Si c'est déjà en cours de lecture, arrêter\n    if (this.isVoicePlaying(messageId)) {\n      this.stopVoicePlayback();\n      return;\n    }\n    // Arrêter toute autre lecture en cours\n    this.stopVoicePlayback();\n    // Démarrer la nouvelle lecture\n    this.startVoicePlayback(message, audioUrl);\n  }\n  startVoicePlayback(message, audioUrl) {\n    const messageId = message.id;\n    try {\n      console.log('🎵 [Voice] Starting playback for:', messageId, 'URL:', audioUrl);\n      this.currentAudio = new Audio(audioUrl);\n      this.playingMessageId = messageId;\n      // Initialiser les valeurs par défaut avec la nouvelle structure\n      const currentData = this.getVoicePlaybackData(messageId);\n      this.setVoicePlaybackData(messageId, {\n        progress: 0,\n        currentTime: 0,\n        speed: currentData.speed || 1,\n        duration: currentData.duration || 0\n      });\n      // Configurer la vitesse de lecture\n      this.currentAudio.playbackRate = currentData.speed || 1;\n      // Événements audio\n      this.currentAudio.addEventListener('loadedmetadata', () => {\n        if (this.currentAudio) {\n          this.setVoicePlaybackData(messageId, {\n            duration: this.currentAudio.duration\n          });\n          console.log('🎵 [Voice] Audio loaded, duration:', this.currentAudio.duration);\n        }\n      });\n      this.currentAudio.addEventListener('timeupdate', () => {\n        if (this.currentAudio && this.playingMessageId === messageId) {\n          const currentTime = this.currentAudio.currentTime;\n          const progress = currentTime / this.currentAudio.duration * 100;\n          this.setVoicePlaybackData(messageId, {\n            currentTime,\n            progress\n          });\n          this.cdr.detectChanges();\n        }\n      });\n      this.currentAudio.addEventListener('ended', () => {\n        this.stopVoicePlayback();\n      });\n      this.currentAudio.addEventListener('error', error => {\n        console.error('🎵 [Voice] Audio error:', error);\n        this.toastService.showError('Erreur lors de la lecture audio');\n        this.stopVoicePlayback();\n      });\n      // Démarrer la lecture\n      this.currentAudio.play().then(() => {\n        this.toastService.showSuccess('🎵 Lecture du message vocal');\n      }).catch(error => {\n        console.error('🎵 [Voice] Error starting playback:', error);\n        this.toastService.showError('Impossible de lire le message vocal');\n        this.stopVoicePlayback();\n      });\n    } catch (error) {\n      console.error('🎵 [Voice] Error creating audio:', error);\n      this.toastService.showError('Erreur lors de la lecture audio');\n      this.stopVoicePlayback();\n    }\n  }\n  stopVoicePlayback() {\n    if (this.currentAudio) {\n      this.currentAudio.pause();\n      this.currentAudio.currentTime = 0;\n      this.currentAudio = null;\n    }\n    this.playingMessageId = null;\n    this.cdr.detectChanges();\n  }\n  getVoiceUrl(message) {\n    // Vérifier les propriétés directes d'audio\n    if (message.voiceUrl) return message.voiceUrl;\n    if (message.audioUrl) return message.audioUrl;\n    if (message.voice) return message.voice;\n    // Vérifier les attachments audio\n    const audioAttachment = message.attachments?.find(att => att.type?.startsWith('audio/') || att.type === 'AUDIO');\n    if (audioAttachment) {\n      return audioAttachment.url || audioAttachment.path || '';\n    }\n    return '';\n  }\n  getVoiceWaves(message) {\n    // Générer des waves basées sur l'ID du message pour la cohérence\n    const messageId = message.id || '';\n    const seed = messageId.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);\n    const waves = [];\n    for (let i = 0; i < 16; i++) {\n      const height = 4 + (seed + i * 7) % 20;\n      waves.push(height);\n    }\n    return waves;\n  }\n  getVoiceProgress(message) {\n    const data = this.getVoicePlaybackData(message.id);\n    const totalWaves = 16;\n    return Math.floor(data.progress / 100 * totalWaves);\n  }\n  getVoiceCurrentTime(message) {\n    const data = this.getVoicePlaybackData(message.id);\n    return this.formatAudioTime(data.currentTime);\n  }\n  getVoiceDuration(message) {\n    const data = this.getVoicePlaybackData(message.id);\n    const duration = data.duration || message.metadata?.duration || 0;\n    if (typeof duration === 'string') {\n      return duration; // Déjà formaté\n    }\n\n    return this.formatAudioTime(duration);\n  }\n  formatAudioTime(seconds) {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = Math.floor(seconds % 60);\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  }\n  seekVoiceMessage(message, waveIndex) {\n    const messageId = message.id;\n    if (!this.currentAudio || this.playingMessageId !== messageId) {\n      return;\n    }\n    const totalWaves = 16;\n    const seekPercentage = waveIndex / totalWaves * 100;\n    const seekTime = seekPercentage / 100 * this.currentAudio.duration;\n    this.currentAudio.currentTime = seekTime;\n  }\n  toggleVoiceSpeed(message) {\n    const messageId = message.id;\n    const data = this.getVoicePlaybackData(messageId);\n    // Cycle entre 1x, 1.5x, 2x\n    const newSpeed = data.speed === 1 ? 1.5 : data.speed === 1.5 ? 2 : 1;\n    this.setVoicePlaybackData(messageId, {\n      speed: newSpeed\n    });\n    if (this.currentAudio && this.playingMessageId === messageId) {\n      this.currentAudio.playbackRate = newSpeed;\n    }\n    this.toastService.showSuccess(`Vitesse: ${newSpeed}x`);\n  }\n  // === MÉTHODES MANQUANTES POUR LE TEMPLATE ===\n  changeVoiceSpeed(message) {\n    this.toggleVoiceSpeed(message);\n  }\n  getVoiceSpeed(message) {\n    const data = this.getVoicePlaybackData(message.id);\n    return data.speed || 1;\n  }\n  ngOnDestroy() {\n    this.subscriptions.unsubscribe();\n    // Nettoyer les timers\n    if (this.callTimer) {\n      clearInterval(this.callTimer);\n    }\n    if (this.recordingTimer) {\n      clearInterval(this.recordingTimer);\n    }\n    if (this.typingTimeout) {\n      clearTimeout(this.typingTimeout);\n    }\n    // Nettoyer les ressources audio\n    if (this.mediaRecorder) {\n      if (this.mediaRecorder.state === 'recording') {\n        this.mediaRecorder.stop();\n      }\n      this.mediaRecorder.stream?.getTracks().forEach(track => track.stop());\n    }\n    // Nettoyer la lecture audio\n    this.stopVoicePlayback();\n  }\n  static {\n    this.ɵfac = function MessageChatComponent_Factory(t) {\n      return new (t || MessageChatComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i4.CallService), i0.ɵɵdirectiveInject(i5.ToastService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: MessageChatComponent,\n      selectors: [[\"app-message-chat\"]],\n      viewQuery: function MessageChatComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n          i0.ɵɵviewQuery(_c2, 5);\n          i0.ɵɵviewQuery(_c3, 5);\n          i0.ɵɵviewQuery(_c4, 5);\n          i0.ɵɵviewQuery(_c5, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.messagesContainer = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.fileInput = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.localVideo = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.remoteVideo = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.localVideoHidden = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.remoteVideoHidden = _t.first);\n        }\n      },\n      decls: 57,\n      vars: 59,\n      consts: [[\"autoplay\", \"\", \"muted\", \"\", \"playsinline\", \"\", 2, \"position\", \"absolute\", \"top\", \"-9999px\", \"left\", \"-9999px\", \"width\", \"1px\", \"height\", \"1px\"], [\"localVideo\", \"\"], [\"autoplay\", \"\", \"playsinline\", \"\", 2, \"position\", \"absolute\", \"top\", \"-9999px\", \"left\", \"-9999px\", \"width\", \"1px\", \"height\", \"1px\"], [\"remoteVideo\", \"\"], [2, \"display\", \"flex\", \"flex-direction\", \"column\", \"height\", \"100vh\", \"background\", \"linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)\", \"color\", \"#1f2937\", \"font-family\", \"'Inter', -apple-system, BlinkMacSystemFont, sans-serif\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"padding\", \"12px 16px\", \"background\", \"#ffffff\", \"border-bottom\", \"1px solid #e5e7eb\", \"box-shadow\", \"0 1px 3px rgba(0, 0, 0, 0.1)\", \"position\", \"relative\", \"z-index\", \"10\"], [\"onmouseover\", \"this.style.background='#f3f4f6'; this.style.transform='scale(1.05)'\", \"onmouseout\", \"this.style.background='transparent'; this.style.transform='scale(1)'\", \"title\", \"Retour aux conversations\", 2, \"padding\", \"10px\", \"margin-right\", \"12px\", \"border-radius\", \"50%\", \"border\", \"none\", \"background\", \"transparent\", \"cursor\", \"pointer\", \"transition\", \"all 0.2s ease\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"center\", \"min-width\", \"40px\", \"min-height\", \"40px\", 3, \"click\"], [1, \"fas\", \"fa-arrow-left\", 2, \"color\", \"#374151\", \"font-size\", \"18px\", \"font-weight\", \"bold\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"flex\", \"1\", \"min-width\", \"0\"], [2, \"position\", \"relative\", \"margin-right\", \"12px\"], [\"onmouseover\", \"this.style.transform='scale(1.05)'\", \"onmouseout\", \"this.style.transform='scale(1)'\", \"title\", \"Voir le profil\", 2, \"width\", \"40px\", \"height\", \"40px\", \"border-radius\", \"50%\", \"object-fit\", \"cover\", \"border\", \"2px solid transparent\", \"cursor\", \"pointer\", \"transition\", \"transform 0.2s ease\", 3, \"src\", \"alt\", \"click\"], [\"style\", \"\\n            position: absolute;\\n            bottom: 0;\\n            right: 0;\\n            width: 12px;\\n            height: 12px;\\n            background: #10b981;\\n            border: 2px solid transparent;\\n            border-radius: 50%;\\n            animation: pulse 2s infinite;\\n          \", 4, \"ngIf\"], [2, \"flex\", \"1\", \"min-width\", \"0\"], [2, \"font-weight\", \"600\", \"color\", \"#111827\", \"margin\", \"0\", \"font-size\", \"16px\", \"white-space\", \"nowrap\", \"overflow\", \"hidden\", \"text-overflow\", \"ellipsis\"], [2, \"font-size\", \"14px\", \"color\", \"#6b7280\", \"margin-top\", \"2px\"], [\"style\", \"display: flex; align-items: center; gap: 4px; color: #10b981\", 4, \"ngIf\"], [4, \"ngIf\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"8px\"], [\"title\", \"Appel vid\\u00E9o\", \"onmouseover\", \"this.style.transform='scale(1.1)'; this.style.boxShadow='0 6px 20px rgba(59, 130, 246, 0.4)'\", \"onmouseout\", \"this.style.transform='scale(1)'; this.style.boxShadow='0 4px 12px rgba(59, 130, 246, 0.3)'\", 2, \"padding\", \"10px\", \"border-radius\", \"50%\", \"border\", \"none\", \"background\", \"linear-gradient(135deg, #3b82f6, #1d4ed8)\", \"color\", \"white\", \"cursor\", \"pointer\", \"transition\", \"all 0.3s\", \"box-shadow\", \"0 4px 12px rgba(59, 130, 246, 0.3)\", \"width\", \"40px\", \"height\", \"40px\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"center\", 3, \"click\"], [1, \"fas\", \"fa-video\", 2, \"font-size\", \"14px\"], [\"title\", \"Appel vocal\", \"onmouseover\", \"this.style.transform='scale(1.1)'; this.style.boxShadow='0 6px 20px rgba(16, 185, 129, 0.4)'\", \"onmouseout\", \"this.style.transform='scale(1)'; this.style.boxShadow='0 4px 12px rgba(16, 185, 129, 0.3)'\", 2, \"padding\", \"10px\", \"border-radius\", \"50%\", \"border\", \"none\", \"background\", \"linear-gradient(135deg, #10b981, #047857)\", \"color\", \"white\", \"cursor\", \"pointer\", \"transition\", \"all 0.3s\", \"box-shadow\", \"0 4px 12px rgba(16, 185, 129, 0.3)\", \"width\", \"40px\", \"height\", \"40px\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"center\", 3, \"click\"], [1, \"fas\", \"fa-phone\", 2, \"font-size\", \"14px\"], [\"title\", \"Rechercher\", 2, \"padding\", \"8px\", \"border-radius\", \"50%\", \"border\", \"none\", \"background\", \"transparent\", \"color\", \"#6b7280\", \"cursor\", \"pointer\", \"transition\", \"all 0.2s\", 3, \"click\"], [1, \"fas\", \"fa-search\"], [\"title\", \"Recharger la conversation\", \"onmouseover\", \"this.style.background='#f3f4f6'; this.style.color='#374151'\", \"onmouseout\", \"this.style.background='transparent'; this.style.color='#6b7280'\", 2, \"padding\", \"8px\", \"border-radius\", \"50%\", \"border\", \"none\", \"background\", \"transparent\", \"color\", \"#6b7280\", \"cursor\", \"pointer\", \"transition\", \"all 0.2s\", 3, \"disabled\", \"click\"], [1, \"fas\", \"fa-sync-alt\"], [\"title\", \"Menu\", 2, \"padding\", \"8px\", \"border-radius\", \"50%\", \"border\", \"none\", \"background\", \"transparent\", \"color\", \"#6b7280\", \"cursor\", \"pointer\", \"transition\", \"all 0.2s\", \"position\", \"relative\", 3, \"click\"], [1, \"fas\", \"fa-ellipsis-v\"], [\"style\", \"\\n        position: absolute;\\n        top: 64px;\\n        right: 16px;\\n        background: #ffffff;\\n        border-radius: 16px;\\n        box-shadow: 0 20px 25px rgba(0, 0, 0, 0.1);\\n        border: 1px solid #e5e7eb;\\n        z-index: 50;\\n        min-width: 192px;\\n      \", 4, \"ngIf\"], [2, \"flex\", \"1\", \"overflow-y\", \"auto\", \"padding\", \"16px\", \"position\", \"relative\", 3, \"scroll\", \"dragover\", \"dragleave\", \"drop\"], [\"messagesContainer\", \"\"], [\"style\", \"\\n        position: absolute;\\n        top: 0;\\n        left: 0;\\n        right: 0;\\n        bottom: 0;\\n        background: rgba(34, 197, 94, 0.2);\\n        border: 2px dashed transparent;\\n        border-radius: 8px;\\n        display: flex;\\n        align-items: center;\\n        justify-content: center;\\n        z-index: 50;\\n        backdrop-filter: blur(2px);\\n        animation: pulse 2s infinite;\\n      \", 4, \"ngIf\"], [\"style\", \"\\n        display: flex;\\n        flex-direction: column;\\n        align-items: center;\\n        justify-content: center;\\n        padding: 32px 0;\\n      \", 4, \"ngIf\"], [\"style\", \"\\n        display: flex;\\n        flex-direction: column;\\n        align-items: center;\\n        justify-content: center;\\n        padding: 64px 0;\\n      \", 4, \"ngIf\"], [\"style\", \"display: flex; flex-direction: column; gap: 8px\", 4, \"ngIf\"], [2, \"background\", \"#ffffff\", \"border-top\", \"1px solid #e5e7eb\", \"padding\", \"16px\"], [2, \"display\", \"flex\", \"align-items\", \"end\", \"gap\", \"12px\", 3, \"formGroup\", \"ngSubmit\"], [2, \"display\", \"flex\", \"gap\", \"8px\"], [\"type\", \"button\", \"title\", \"\\u00C9mojis\", \"onmouseover\", \"this.style.background=this.style.background === 'transparent' ? '#f3f4f6' : this.style.background\", \"onmouseout\", \"this.style.background=this.style.background === '#f3f4f6' ? 'transparent' : this.style.background\", 2, \"padding\", \"8px\", \"border-radius\", \"50%\", \"border\", \"none\", \"background\", \"transparent\", \"color\", \"#6b7280\", \"cursor\", \"pointer\", \"transition\", \"all 0.2s\", 3, \"click\"], [1, \"fas\", \"fa-smile\"], [\"type\", \"button\", \"title\", \"Pi\\u00E8ces jointes\", \"onmouseover\", \"this.style.background=this.style.background === 'transparent' ? '#f3f4f6' : this.style.background\", \"onmouseout\", \"this.style.background=this.style.background === '#f3f4f6' ? 'transparent' : this.style.background\", 2, \"padding\", \"8px\", \"border-radius\", \"50%\", \"border\", \"none\", \"background\", \"transparent\", \"color\", \"#6b7280\", \"cursor\", \"pointer\", \"transition\", \"all 0.2s\", 3, \"click\"], [1, \"fas\", \"fa-paperclip\"], [\"type\", \"button\", \"title\", \"Maintenir pour enregistrer un message vocal\", \"onmouseover\", \"if(!this.style.background || this.style.background === 'transparent') this.style.background='#f3f4f6'\", \"onmouseout\", \"if(this.style.background === '#f3f4f6') this.style.background='transparent'\", 2, \"padding\", \"8px\", \"border-radius\", \"50%\", \"border\", \"none\", \"background\", \"transparent\", \"color\", \"#6b7280\", \"cursor\", \"pointer\", \"transition\", \"all 0.2s\", \"position\", \"relative\", 3, \"mousedown\", \"mouseup\", \"mouseleave\", \"touchstart\", \"touchend\", \"touchcancel\"], [\"style\", \"\\n              position: absolute;\\n              top: -2px;\\n              right: -2px;\\n              width: 8px;\\n              height: 8px;\\n              background: #ef4444;\\n              border-radius: 50%;\\n              animation: ping 1s infinite;\\n            \", 4, \"ngIf\"], [2, \"flex\", \"1\", \"position\", \"relative\"], [\"formControlName\", \"content\", \"placeholder\", \"Tapez votre message...\", 2, \"width\", \"100%\", \"min-height\", \"44px\", \"max-height\", \"120px\", \"padding\", \"12px 16px\", \"border\", \"1px solid #e5e7eb\", \"border-radius\", \"22px\", \"resize\", \"none\", \"outline\", \"none\", \"font-family\", \"inherit\", \"font-size\", \"14px\", \"line-height\", \"1.4\", \"background\", \"#ffffff\", \"color\", \"#111827\", \"transition\", \"all 0.2s\", 3, \"disabled\", \"keydown\", \"input\", \"focus\"], [\"type\", \"submit\", \"title\", \"Envoyer\", \"onmouseover\", \"if(!this.disabled) this.style.background='#2563eb'\", \"onmouseout\", \"if(!this.disabled) this.style.background='#3b82f6'\", 2, \"padding\", \"12px\", \"border-radius\", \"50%\", \"border\", \"none\", \"background\", \"#3b82f6\", \"color\", \"#ffffff\", \"cursor\", \"pointer\", \"transition\", \"all 0.2s\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"center\", \"min-width\", \"44px\", \"min-height\", \"44px\", 3, \"disabled\"], [\"class\", \"fas fa-paper-plane\", 4, \"ngIf\"], [\"style\", \"\\n            width: 16px;\\n            height: 16px;\\n            border: 2px solid #ffffff;\\n            border-top-color: transparent;\\n            border-radius: 50%;\\n            animation: spin 1s linear infinite;\\n          \", 4, \"ngIf\"], [\"style\", \"\\n        position: absolute;\\n        bottom: 80px;\\n        left: 16px;\\n        background: #ffffff;\\n        border-radius: 16px;\\n        box-shadow: 0 20px 25px rgba(0, 0, 0, 0.1);\\n        border: 1px solid #e5e7eb;\\n        z-index: 50;\\n        width: 320px;\\n        max-height: 300px;\\n        overflow-y: auto;\\n      \", 4, \"ngIf\"], [\"style\", \"\\n        position: absolute;\\n        bottom: 80px;\\n        left: 60px;\\n        background: #ffffff;\\n        border-radius: 16px;\\n        box-shadow: 0 20px 25px rgba(0, 0, 0, 0.1);\\n        border: 1px solid #e5e7eb;\\n        z-index: 50;\\n        min-width: 200px;\\n      \", 4, \"ngIf\"], [\"type\", \"file\", \"multiple\", \"\", 2, \"display\", \"none\", 3, \"accept\", \"change\"], [\"fileInput\", \"\"], [\"style\", \"\\n      position: fixed;\\n      top: 0;\\n      left: 0;\\n      right: 0;\\n      bottom: 0;\\n      background: rgba(0, 0, 0, 0.25);\\n      z-index: 40;\\n    \", 3, \"click\", 4, \"ngIf\"], [\"style\", \"\\n      position: fixed;\\n      bottom: 100px;\\n      left: 50%;\\n      transform: translateX(-50%);\\n      background: linear-gradient(135deg, #f59e0b, #d97706);\\n      color: white;\\n      padding: 20px 24px;\\n      border-radius: 20px;\\n      box-shadow: 0 20px 25px rgba(0, 0, 0, 0.2);\\n      z-index: 60;\\n      display: flex;\\n      align-items: center;\\n      gap: 16px;\\n      min-width: 280px;\\n      animation: slideInUp 0.3s ease-out;\\n    \", 4, \"ngIf\"], [2, \"position\", \"absolute\", \"bottom\", \"0\", \"right\", \"0\", \"width\", \"12px\", \"height\", \"12px\", \"background\", \"#10b981\", \"border\", \"2px solid transparent\", \"border-radius\", \"50%\", \"animation\", \"pulse 2s infinite\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"4px\", \"color\", \"#10b981\"], [2, \"display\", \"flex\", \"gap\", \"2px\"], [2, \"width\", \"4px\", \"height\", \"4px\", \"background\", \"#10b981\", \"border-radius\", \"50%\", \"animation\", \"bounce 1s infinite\"], [2, \"width\", \"4px\", \"height\", \"4px\", \"background\", \"#10b981\", \"border-radius\", \"50%\", \"animation\", \"bounce 1s infinite 0.1s\"], [2, \"width\", \"4px\", \"height\", \"4px\", \"background\", \"#10b981\", \"border-radius\", \"50%\", \"animation\", \"bounce 1s infinite 0.2s\"], [2, \"position\", \"absolute\", \"top\", \"64px\", \"right\", \"16px\", \"background\", \"#ffffff\", \"border-radius\", \"16px\", \"box-shadow\", \"0 20px 25px rgba(0, 0, 0, 0.1)\", \"border\", \"1px solid #e5e7eb\", \"z-index\", \"50\", \"min-width\", \"192px\"], [2, \"padding\", \"8px\"], [\"onmouseover\", \"this.style.background='#f3f4f6'\", \"onmouseout\", \"this.style.background='transparent'\", 2, \"width\", \"100%\", \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"12px\", \"padding\", \"8px 12px\", \"border-radius\", \"8px\", \"border\", \"none\", \"background\", \"transparent\", \"cursor\", \"pointer\", \"transition\", \"all 0.2s\", \"text-align\", \"left\", 3, \"click\"], [1, \"fas\", \"fa-search\", 2, \"color\", \"#3b82f6\"], [2, \"color\", \"#374151\"], [\"onmouseover\", \"this.style.background='#f3f4f6'\", \"onmouseout\", \"this.style.background='transparent'\", 2, \"width\", \"100%\", \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"12px\", \"padding\", \"8px 12px\", \"border-radius\", \"8px\", \"border\", \"none\", \"background\", \"transparent\", \"cursor\", \"pointer\", \"transition\", \"all 0.2s\", \"text-align\", \"left\"], [1, \"fas\", \"fa-user\", 2, \"color\", \"#10b981\"], [2, \"margin\", \"8px 0\", \"border-color\", \"#e5e7eb\"], [1, \"fas\", \"fa-cog\", 2, \"color\", \"#6b7280\"], [2, \"position\", \"absolute\", \"top\", \"0\", \"left\", \"0\", \"right\", \"0\", \"bottom\", \"0\", \"background\", \"rgba(34, 197, 94, 0.2)\", \"border\", \"2px dashed transparent\", \"border-radius\", \"8px\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"center\", \"z-index\", \"50\", \"backdrop-filter\", \"blur(2px)\", \"animation\", \"pulse 2s infinite\"], [2, \"text-align\", \"center\", \"background\", \"#ffffff\", \"padding\", \"24px\", \"border-radius\", \"12px\", \"box-shadow\", \"0 10px 15px rgba(0, 0, 0, 0.1)\", \"border\", \"1px solid transparent\"], [1, \"fas\", \"fa-cloud-upload-alt\", 2, \"font-size\", \"48px\", \"color\", \"#10b981\", \"margin-bottom\", \"12px\", \"animation\", \"bounce 1s infinite\"], [2, \"font-size\", \"20px\", \"font-weight\", \"bold\", \"color\", \"#047857\", \"margin-bottom\", \"8px\"], [2, \"font-size\", \"14px\", \"color\", \"#10b981\"], [2, \"display\", \"flex\", \"flex-direction\", \"column\", \"align-items\", \"center\", \"justify-content\", \"center\", \"padding\", \"32px 0\"], [2, \"width\", \"32px\", \"height\", \"32px\", \"border\", \"2px solid #e5e7eb\", \"border-bottom-color\", \"#10b981\", \"border-radius\", \"50%\", \"animation\", \"spin 1s linear infinite\", \"margin-bottom\", \"16px\"], [2, \"color\", \"#6b7280\"], [2, \"display\", \"flex\", \"flex-direction\", \"column\", \"align-items\", \"center\", \"justify-content\", \"center\", \"padding\", \"64px 0\"], [2, \"font-size\", \"64px\", \"color\", \"#d1d5db\", \"margin-bottom\", \"16px\"], [1, \"fas\", \"fa-comments\"], [2, \"font-size\", \"20px\", \"font-weight\", \"600\", \"color\", \"#374151\", \"margin-bottom\", \"8px\"], [2, \"color\", \"#6b7280\", \"text-align\", \"center\"], [2, \"display\", \"flex\", \"flex-direction\", \"column\", \"gap\", \"8px\"], [4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"style\", \"display: flex; align-items: start; gap: 8px\", 4, \"ngIf\"], [\"style\", \"display: flex; justify-content: center; margin: 16px 0\", 4, \"ngIf\"], [2, \"display\", \"flex\", 3, \"id\", \"click\", \"contextmenu\"], [\"style\", \"margin-right: 8px; flex-shrink: 0\", 4, \"ngIf\"], [2, \"max-width\", \"320px\", \"padding\", \"12px 16px\", \"border-radius\", \"18px\", \"box-shadow\", \"0 1px 3px rgba(0, 0, 0, 0.1)\", \"position\", \"relative\", \"word-wrap\", \"break-word\", \"overflow-wrap\", \"break-word\", \"border\", \"none\"], [\"style\", \"\\n                font-size: 12px;\\n                font-weight: 600;\\n                margin-bottom: 4px;\\n                opacity: 0.75;\\n              \", 3, \"color\", 4, \"ngIf\"], [\"style\", \"word-wrap: break-word; overflow-wrap: break-word\", 4, \"ngIf\"], [\"style\", \"margin: 8px 0\", 4, \"ngIf\"], [\"style\", \"\\n                display: flex;\\n                align-items: center;\\n                gap: 12px;\\n                padding: 12px;\\n                background: rgba(255, 255, 255, 0.1);\\n                border-radius: 12px;\\n                margin: 8px 0;\\n                min-width: 200px;\\n                max-width: 280px;\\n              \", 4, \"ngIf\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"flex-end\", \"gap\", \"4px\", \"margin-top\", \"4px\", \"font-size\", \"12px\", \"opacity\", \"0.75\"], [\"style\", \"display: flex; align-items: center\", 4, \"ngIf\"], [2, \"display\", \"flex\", \"justify-content\", \"center\", \"margin\", \"16px 0\"], [2, \"background\", \"#ffffff\", \"padding\", \"4px 12px\", \"border-radius\", \"20px\", \"box-shadow\", \"0 1px 3px rgba(0, 0, 0, 0.1)\"], [2, \"font-size\", \"12px\", \"color\", \"#6b7280\"], [2, \"margin-right\", \"8px\", \"flex-shrink\", \"0\"], [\"onmouseover\", \"this.style.transform='scale(1.05)'\", \"onmouseout\", \"this.style.transform='scale(1)'\", 2, \"width\", \"32px\", \"height\", \"32px\", \"border-radius\", \"50%\", \"object-fit\", \"cover\", \"cursor\", \"pointer\", \"transition\", \"transform 0.2s\", 3, \"src\", \"alt\", \"click\"], [2, \"font-size\", \"12px\", \"font-weight\", \"600\", \"margin-bottom\", \"4px\", \"opacity\", \"0.75\"], [2, \"word-wrap\", \"break-word\", \"overflow-wrap\", \"break-word\"], [3, \"innerHTML\"], [2, \"margin\", \"8px 0\"], [\"onmouseover\", \"this.style.transform='scale(1.02)'\", \"onmouseout\", \"this.style.transform='scale(1)'\", 2, \"max-width\", \"280px\", \"height\", \"auto\", \"border-radius\", \"12px\", \"cursor\", \"pointer\", \"transition\", \"transform 0.2s\", 3, \"src\", \"alt\", \"click\", \"load\", \"error\"], [\"style\", \"font-size: 14px; margin-top: 8px; line-height: 1.4\", 3, \"color\", \"innerHTML\", 4, \"ngIf\"], [2, \"font-size\", \"14px\", \"margin-top\", \"8px\", \"line-height\", \"1.4\", 3, \"innerHTML\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"12px\", \"padding\", \"12px\", \"background\", \"rgba(255, 255, 255, 0.1)\", \"border-radius\", \"12px\", \"margin\", \"8px 0\", \"min-width\", \"200px\", \"max-width\", \"280px\"], [\"onmouseover\", \"this.style.background='rgba(255, 255, 255, 0.3)'\", \"onmouseout\", \"this.style.background='rgba(255, 255, 255, 0.2)'\", \"title\", \"Lire/Pause\", 2, \"width\", \"40px\", \"height\", \"40px\", \"border-radius\", \"50%\", \"border\", \"none\", \"background\", \"rgba(255, 255, 255, 0.2)\", \"color\", \"inherit\", \"cursor\", \"pointer\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"center\", \"transition\", \"all 0.2s\", \"flex-shrink\", \"0\", 3, \"click\"], [2, \"font-size\", \"14px\"], [2, \"flex\", \"1\", \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"2px\", \"height\", \"24px\", \"overflow\", \"hidden\"], [\"style\", \"\\n                    width: 3px;\\n                    background: currentColor;\\n                    border-radius: 2px;\\n                    opacity: 0.7;\\n                    transition: height 0.3s ease;\\n                  \", 3, \"height\", \"animation\", \"animation-delay\", 4, \"ngFor\", \"ngForOf\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"8px\", \"flex-shrink\", \"0\"], [2, \"font-size\", \"12px\", \"opacity\", \"0.8\", \"min-width\", \"40px\", \"text-align\", \"right\"], [\"style\", \"\\n                    padding: 4px 8px;\\n                    border-radius: 12px;\\n                    border: none;\\n                    background: rgba(255, 255, 255, 0.2);\\n                    color: inherit;\\n                    cursor: pointer;\\n                    font-size: 11px;\\n                    transition: all 0.2s;\\n                  \", \"onmouseover\", \"this.style.background='rgba(255, 255, 255, 0.3)'\", \"onmouseout\", \"this.style.background='rgba(255, 255, 255, 0.2)'\", \"title\", \"Changer la vitesse\", 3, \"click\", 4, \"ngIf\"], [2, \"width\", \"3px\", \"background\", \"currentColor\", \"border-radius\", \"2px\", \"opacity\", \"0.7\", \"transition\", \"height 0.3s ease\"], [\"onmouseover\", \"this.style.background='rgba(255, 255, 255, 0.3)'\", \"onmouseout\", \"this.style.background='rgba(255, 255, 255, 0.2)'\", \"title\", \"Changer la vitesse\", 2, \"padding\", \"4px 8px\", \"border-radius\", \"12px\", \"border\", \"none\", \"background\", \"rgba(255, 255, 255, 0.2)\", \"color\", \"inherit\", \"cursor\", \"pointer\", \"font-size\", \"11px\", \"transition\", \"all 0.2s\", 3, \"click\"], [2, \"display\", \"flex\", \"align-items\", \"center\"], [\"class\", \"fas fa-clock\", \"title\", \"Envoi en cours\", 4, \"ngIf\"], [\"class\", \"fas fa-check\", \"title\", \"Envoy\\u00E9\", 4, \"ngIf\"], [\"class\", \"fas fa-check-double\", \"title\", \"Livr\\u00E9\", 4, \"ngIf\"], [\"class\", \"fas fa-check-double\", \"style\", \"color: #3b82f6\", \"title\", \"Lu\", 4, \"ngIf\"], [\"title\", \"Envoi en cours\", 1, \"fas\", \"fa-clock\"], [\"title\", \"Envoy\\u00E9\", 1, \"fas\", \"fa-check\"], [\"title\", \"Livr\\u00E9\", 1, \"fas\", \"fa-check-double\"], [\"title\", \"Lu\", 1, \"fas\", \"fa-check-double\", 2, \"color\", \"#3b82f6\"], [2, \"display\", \"flex\", \"align-items\", \"start\", \"gap\", \"8px\"], [2, \"width\", \"32px\", \"height\", \"32px\", \"border-radius\", \"50%\", \"object-fit\", \"cover\", 3, \"src\", \"alt\"], [2, \"background\", \"#ffffff\", \"padding\", \"12px 16px\", \"border-radius\", \"18px\", \"box-shadow\", \"0 1px 3px rgba(0, 0, 0, 0.1)\"], [2, \"display\", \"flex\", \"gap\", \"4px\"], [2, \"width\", \"8px\", \"height\", \"8px\", \"background\", \"#6b7280\", \"border-radius\", \"50%\", \"animation\", \"bounce 1s infinite\"], [2, \"width\", \"8px\", \"height\", \"8px\", \"background\", \"#6b7280\", \"border-radius\", \"50%\", \"animation\", \"bounce 1s infinite 0.1s\"], [2, \"width\", \"8px\", \"height\", \"8px\", \"background\", \"#6b7280\", \"border-radius\", \"50%\", \"animation\", \"bounce 1s infinite 0.2s\"], [2, \"position\", \"absolute\", \"top\", \"-2px\", \"right\", \"-2px\", \"width\", \"8px\", \"height\", \"8px\", \"background\", \"#ef4444\", \"border-radius\", \"50%\", \"animation\", \"ping 1s infinite\"], [1, \"fas\", \"fa-paper-plane\"], [2, \"width\", \"16px\", \"height\", \"16px\", \"border\", \"2px solid #ffffff\", \"border-top-color\", \"transparent\", \"border-radius\", \"50%\", \"animation\", \"spin 1s linear infinite\"], [2, \"position\", \"absolute\", \"bottom\", \"80px\", \"left\", \"16px\", \"background\", \"#ffffff\", \"border-radius\", \"16px\", \"box-shadow\", \"0 20px 25px rgba(0, 0, 0, 0.1)\", \"border\", \"1px solid #e5e7eb\", \"z-index\", \"50\", \"width\", \"320px\", \"max-height\", \"300px\", \"overflow-y\", \"auto\"], [2, \"padding\", \"16px\"], [2, \"margin\", \"0 0 12px 0\", \"font-size\", \"14px\", \"font-weight\", \"600\", \"color\", \"#374151\"], [2, \"display\", \"grid\", \"grid-template-columns\", \"repeat(8, 1fr)\", \"gap\", \"8px\"], [\"style\", \"\\n              padding: 8px;\\n              border: none;\\n              background: transparent;\\n              border-radius: 8px;\\n              cursor: pointer;\\n              font-size: 20px;\\n              transition: all 0.2s;\\n            \", \"onmouseover\", \"this.style.background='#f3f4f6'\", \"onmouseout\", \"this.style.background='transparent'\", 3, \"title\", \"click\", 4, \"ngFor\", \"ngForOf\"], [\"onmouseover\", \"this.style.background='#f3f4f6'\", \"onmouseout\", \"this.style.background='transparent'\", 2, \"padding\", \"8px\", \"border\", \"none\", \"background\", \"transparent\", \"border-radius\", \"8px\", \"cursor\", \"pointer\", \"font-size\", \"20px\", \"transition\", \"all 0.2s\", 3, \"title\", \"click\"], [2, \"position\", \"absolute\", \"bottom\", \"80px\", \"left\", \"60px\", \"background\", \"#ffffff\", \"border-radius\", \"16px\", \"box-shadow\", \"0 20px 25px rgba(0, 0, 0, 0.1)\", \"border\", \"1px solid #e5e7eb\", \"z-index\", \"50\", \"min-width\", \"200px\"], [2, \"display\", \"grid\", \"grid-template-columns\", \"repeat(2, 1fr)\", \"gap\", \"12px\"], [\"onmouseover\", \"this.style.background='#f3f4f6'\", \"onmouseout\", \"this.style.background='transparent'\", 2, \"display\", \"flex\", \"flex-direction\", \"column\", \"align-items\", \"center\", \"gap\", \"8px\", \"padding\", \"16px\", \"border\", \"none\", \"background\", \"transparent\", \"border-radius\", \"12px\", \"cursor\", \"pointer\", \"transition\", \"all 0.2s\", 3, \"click\"], [2, \"width\", \"48px\", \"height\", \"48px\", \"background\", \"#dbeafe\", \"border-radius\", \"50%\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"center\"], [1, \"fas\", \"fa-image\", 2, \"color\", \"#3b82f6\", \"font-size\", \"20px\"], [2, \"font-size\", \"14px\", \"font-weight\", \"500\", \"color\", \"#374151\"], [2, \"width\", \"48px\", \"height\", \"48px\", \"background\", \"#fef3c7\", \"border-radius\", \"50%\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"center\"], [1, \"fas\", \"fa-file-alt\", 2, \"color\", \"#f59e0b\", \"font-size\", \"20px\"], [2, \"width\", \"48px\", \"height\", \"48px\", \"background\", \"#dcfce7\", \"border-radius\", \"50%\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"center\"], [1, \"fas\", \"fa-camera\", 2, \"color\", \"#10b981\", \"font-size\", \"20px\"], [2, \"position\", \"fixed\", \"top\", \"0\", \"left\", \"0\", \"right\", \"0\", \"bottom\", \"0\", \"background\", \"rgba(0, 0, 0, 0.25)\", \"z-index\", \"40\", 3, \"click\"], [2, \"position\", \"fixed\", \"bottom\", \"100px\", \"left\", \"50%\", \"transform\", \"translateX(-50%)\", \"background\", \"linear-gradient(135deg, #f59e0b, #d97706)\", \"color\", \"white\", \"padding\", \"20px 24px\", \"border-radius\", \"20px\", \"box-shadow\", \"0 20px 25px rgba(0, 0, 0, 0.2)\", \"z-index\", \"60\", \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"16px\", \"min-width\", \"280px\", \"animation\", \"slideInUp 0.3s ease-out\"], [2, \"width\", \"48px\", \"height\", \"48px\", \"background\", \"rgba(255, 255, 255, 0.2)\", \"border-radius\", \"50%\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"center\", \"animation\", \"pulse 1s infinite\"], [1, \"fas\", \"fa-microphone\", 2, \"font-size\", \"20px\"], [2, \"flex\", \"1\"], [2, \"font-size\", \"18px\", \"font-weight\", \"bold\", \"margin-bottom\", \"4px\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"2px\", \"height\", \"20px\"], [\"style\", \"\\n            width: 3px;\\n            background: rgba(255, 255, 255, 0.8);\\n            border-radius: 2px;\\n            transition: height 0.3s ease;\\n          \", 3, \"height\", \"animation\", \"animation-delay\", 4, \"ngFor\", \"ngForOf\"], [2, \"font-size\", \"12px\", \"opacity\", \"0.8\", \"margin-top\", \"4px\"], [\"onmouseover\", \"this.style.background='rgba(239, 68, 68, 1)'\", \"onmouseout\", \"this.style.background='rgba(239, 68, 68, 0.8)'\", \"title\", \"Annuler l'enregistrement\", 2, \"width\", \"40px\", \"height\", \"40px\", \"border-radius\", \"50%\", \"border\", \"none\", \"background\", \"rgba(239, 68, 68, 0.8)\", \"color\", \"white\", \"cursor\", \"pointer\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"center\", \"transition\", \"all 0.2s\", 3, \"click\"], [1, \"fas\", \"fa-times\", 2, \"font-size\", \"16px\"], [\"onmouseover\", \"this.style.background='rgba(34, 197, 94, 1)'\", \"onmouseout\", \"this.style.background='rgba(34, 197, 94, 0.8)'\", \"title\", \"Envoyer le message vocal\", 2, \"width\", \"40px\", \"height\", \"40px\", \"border-radius\", \"50%\", \"border\", \"none\", \"background\", \"rgba(34, 197, 94, 0.8)\", \"color\", \"white\", \"cursor\", \"pointer\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"center\", \"transition\", \"all 0.2s\", 3, \"click\"], [1, \"fas\", \"fa-paper-plane\", 2, \"font-size\", \"16px\"], [2, \"width\", \"3px\", \"background\", \"rgba(255, 255, 255, 0.8)\", \"border-radius\", \"2px\", \"transition\", \"height 0.3s ease\"]],\n      template: function MessageChatComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"video\", 0, 1)(2, \"video\", 2, 3);\n          i0.ɵɵelementStart(4, \"div\", 4)(5, \"header\", 5)(6, \"button\", 6);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_6_listener() {\n            return ctx.goBackToConversations();\n          });\n          i0.ɵɵelement(7, \"i\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"div\", 8)(9, \"div\", 9)(10, \"img\", 10);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_img_click_10_listener() {\n            return ctx.openUserProfile(ctx.otherParticipant == null ? null : ctx.otherParticipant.id);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(11, MessageChatComponent_div_11_Template, 1, 0, \"div\", 11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"div\", 12)(13, \"h3\", 13);\n          i0.ɵɵtext(14);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"div\", 14);\n          i0.ɵɵtemplate(16, MessageChatComponent_div_16_Template, 7, 0, \"div\", 15);\n          i0.ɵɵtemplate(17, MessageChatComponent_span_17_Template, 2, 1, \"span\", 16);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(18, \"div\", 17)(19, \"button\", 18);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_19_listener() {\n            return ctx.startVideoCall();\n          });\n          i0.ɵɵelement(20, \"i\", 19);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"button\", 20);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_21_listener() {\n            return ctx.startVoiceCall();\n          });\n          i0.ɵɵelement(22, \"i\", 21);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"button\", 22);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_23_listener() {\n            return ctx.toggleSearch();\n          });\n          i0.ɵɵelement(24, \"i\", 23);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"button\", 24);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_25_listener() {\n            return ctx.reloadConversation();\n          });\n          i0.ɵɵelement(26, \"i\", 25);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"button\", 26);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_27_listener() {\n            return ctx.toggleMainMenu();\n          });\n          i0.ɵɵelement(28, \"i\", 27);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(29, MessageChatComponent_div_29_Template, 15, 0, \"div\", 28);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(30, \"main\", 29, 30);\n          i0.ɵɵlistener(\"scroll\", function MessageChatComponent_Template_main_scroll_30_listener($event) {\n            return ctx.onScroll($event);\n          })(\"dragover\", function MessageChatComponent_Template_main_dragover_30_listener($event) {\n            return ctx.onDragOver($event);\n          })(\"dragleave\", function MessageChatComponent_Template_main_dragleave_30_listener($event) {\n            return ctx.onDragLeave($event);\n          })(\"drop\", function MessageChatComponent_Template_main_drop_30_listener($event) {\n            return ctx.onDrop($event);\n          });\n          i0.ɵɵtemplate(32, MessageChatComponent_div_32_Template, 7, 0, \"div\", 31);\n          i0.ɵɵtemplate(33, MessageChatComponent_div_33_Template, 4, 0, \"div\", 32);\n          i0.ɵɵtemplate(34, MessageChatComponent_div_34_Template, 7, 1, \"div\", 33);\n          i0.ɵɵtemplate(35, MessageChatComponent_div_35_Template, 3, 3, \"div\", 34);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(36, \"footer\", 35)(37, \"form\", 36);\n          i0.ɵɵlistener(\"ngSubmit\", function MessageChatComponent_Template_form_ngSubmit_37_listener() {\n            return ctx.sendMessage();\n          });\n          i0.ɵɵelementStart(38, \"div\", 37)(39, \"button\", 38);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_39_listener() {\n            return ctx.toggleEmojiPicker();\n          });\n          i0.ɵɵelement(40, \"i\", 39);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(41, \"button\", 40);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_41_listener() {\n            return ctx.toggleAttachmentMenu();\n          });\n          i0.ɵɵelement(42, \"i\", 41);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"button\", 42);\n          i0.ɵɵlistener(\"mousedown\", function MessageChatComponent_Template_button_mousedown_43_listener($event) {\n            return ctx.onRecordStart($event);\n          })(\"mouseup\", function MessageChatComponent_Template_button_mouseup_43_listener($event) {\n            return ctx.onRecordEnd($event);\n          })(\"mouseleave\", function MessageChatComponent_Template_button_mouseleave_43_listener($event) {\n            return ctx.onRecordCancel($event);\n          })(\"touchstart\", function MessageChatComponent_Template_button_touchstart_43_listener($event) {\n            return ctx.onRecordStart($event);\n          })(\"touchend\", function MessageChatComponent_Template_button_touchend_43_listener($event) {\n            return ctx.onRecordEnd($event);\n          })(\"touchcancel\", function MessageChatComponent_Template_button_touchcancel_43_listener($event) {\n            return ctx.onRecordCancel($event);\n          });\n          i0.ɵɵelement(44, \"i\");\n          i0.ɵɵtemplate(45, MessageChatComponent_div_45_Template, 1, 0, \"div\", 43);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(46, \"div\", 44)(47, \"textarea\", 45);\n          i0.ɵɵlistener(\"keydown\", function MessageChatComponent_Template_textarea_keydown_47_listener($event) {\n            return ctx.onInputKeyDown($event);\n          })(\"input\", function MessageChatComponent_Template_textarea_input_47_listener($event) {\n            return ctx.onInputChange($event);\n          })(\"focus\", function MessageChatComponent_Template_textarea_focus_47_listener() {\n            return ctx.onInputFocus();\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(48, \"button\", 46);\n          i0.ɵɵtemplate(49, MessageChatComponent_i_49_Template, 1, 0, \"i\", 47);\n          i0.ɵɵtemplate(50, MessageChatComponent_div_50_Template, 1, 0, \"div\", 48);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(51, MessageChatComponent_div_51_Template, 6, 1, \"div\", 49);\n          i0.ɵɵtemplate(52, MessageChatComponent_div_52_Template, 20, 0, \"div\", 50);\n          i0.ɵɵelementStart(53, \"input\", 51, 52);\n          i0.ɵɵlistener(\"change\", function MessageChatComponent_Template_input_change_53_listener($event) {\n            return ctx.onFileSelected($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(55, MessageChatComponent_div_55_Template, 1, 0, \"div\", 53);\n          i0.ɵɵtemplate(56, MessageChatComponent_div_56_Template, 15, 3, \"div\", 54);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"src\", (ctx.otherParticipant == null ? null : ctx.otherParticipant.image) || \"assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", ctx.otherParticipant == null ? null : ctx.otherParticipant.username);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.otherParticipant == null ? null : ctx.otherParticipant.isOnline);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", (ctx.otherParticipant == null ? null : ctx.otherParticipant.username) || \"Utilisateur\", \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.isUserTyping);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isUserTyping);\n          i0.ɵɵadvance(6);\n          i0.ɵɵstyleProp(\"background\", ctx.searchMode ? \"#dcfce7\" : \"transparent\")(\"color\", ctx.searchMode ? \"#16a34a\" : \"#6b7280\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵstyleProp(\"opacity\", ctx.isLoading ? \"0.5\" : \"1\");\n          i0.ɵɵproperty(\"disabled\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵstyleProp(\"animation\", ctx.isLoading ? \"spin 1s linear infinite\" : \"none\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵstyleProp(\"background\", ctx.showMainMenu ? \"#dcfce7\" : \"transparent\")(\"color\", ctx.showMainMenu ? \"#16a34a\" : \"#6b7280\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.showMainMenu);\n          i0.ɵɵadvance(1);\n          i0.ɵɵstyleProp(\"background\", ctx.isDragOver ? \"rgba(34, 197, 94, 0.1)\" : \"transparent\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.isDragOver);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.messages.length === 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.messages.length > 0);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"formGroup\", ctx.messageForm);\n          i0.ɵɵadvance(2);\n          i0.ɵɵstyleProp(\"background\", ctx.showEmojiPicker ? \"#dcfce7\" : \"transparent\")(\"color\", ctx.showEmojiPicker ? \"#16a34a\" : \"#6b7280\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵstyleProp(\"background\", ctx.showAttachmentMenu ? \"#dcfce7\" : \"transparent\")(\"color\", ctx.showAttachmentMenu ? \"#16a34a\" : \"#6b7280\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵstyleProp(\"background\", ctx.isRecordingVoice ? \"#fef3c7\" : \"transparent\")(\"color\", ctx.isRecordingVoice ? \"#f59e0b\" : \"#6b7280\")(\"transform\", ctx.isRecordingVoice ? \"scale(1.1)\" : \"scale(1)\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵclassMap(ctx.isRecordingVoice ? \"fas fa-stop\" : \"fas fa-microphone\");\n          i0.ɵɵstyleProp(\"animation\", ctx.isRecordingVoice ? \"pulse 1s infinite\" : \"none\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isRecordingVoice);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", ctx.isInputDisabled());\n          i0.ɵɵadvance(1);\n          i0.ɵɵstyleProp(\"background\", !ctx.messageForm.valid || ctx.isSendingMessage ? \"#9ca3af\" : \"#3b82f6\")(\"cursor\", !ctx.messageForm.valid || ctx.isSendingMessage ? \"not-allowed\" : \"pointer\");\n          i0.ɵɵproperty(\"disabled\", !ctx.messageForm.valid || ctx.isSendingMessage);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isSendingMessage);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isSendingMessage);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.showEmojiPicker);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.showAttachmentMenu);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"accept\", ctx.getFileAcceptTypes());\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.showEmojiPicker || ctx.showAttachmentMenu || ctx.showMainMenu);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isRecordingVoice);\n        }\n      },\n      dependencies: [i6.NgForOf, i6.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName],\n      styles: [\"@keyframes _ngcontent-%COMP%_pulse {\\n      0%,\\n      100% {\\n        opacity: 1;\\n      }\\n      50% {\\n        opacity: 0.5;\\n      }\\n    }\\n    @keyframes _ngcontent-%COMP%_bounce {\\n      0%,\\n      20%,\\n      53%,\\n      80%,\\n      100% {\\n        transform: translateY(0);\\n      }\\n      40%,\\n      43% {\\n        transform: translateY(-8px);\\n      }\\n      70% {\\n        transform: translateY(-4px);\\n      }\\n    }\\n    @keyframes _ngcontent-%COMP%_spin {\\n      from {\\n        transform: rotate(0deg);\\n      }\\n      to {\\n        transform: rotate(360deg);\\n      }\\n    }\\n    @keyframes _ngcontent-%COMP%_ping {\\n      75%,\\n      100% {\\n        transform: scale(2);\\n        opacity: 0;\\n      }\\n    }\\n    @keyframes _ngcontent-%COMP%_slideInUp {\\n      from {\\n        transform: translateX(-50%) translateY(20px);\\n        opacity: 0;\\n      }\\n      to {\\n        transform: translateX(-50%) translateY(0);\\n        opacity: 1;\\n      }\\n    }\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "Subscription", "CallType", "i0", "ɵɵelement", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r4", "otherParticipant", "isOnline", "formatLastActive", "lastActive", "ɵɵlistener", "MessageChatComponent_div_29_Template_button_click_2_listener", "ɵɵrestoreView", "_r20", "ctx_r19", "ɵɵnextContext", "toggleSearch", "ɵɵresetView", "showMainMenu", "ctx_r9", "username", "ctx_r25", "formatDateSeparator", "message_r23", "timestamp", "MessageChatComponent_div_35_ng_container_1_div_3_Template_img_click_1_listener", "_r35", "$implicit", "ctx_r33", "openUserProfile", "sender", "id", "ɵɵproperty", "image", "ɵɵsanitizeUrl", "ɵɵstyleProp", "ctx_r27", "getUserColor", "ctx_r28", "formatMessageContent", "content", "ɵɵsanitizeHtml", "ctx_r39", "currentUserId", "MessageChatComponent_div_35_ng_container_1_div_7_Template_img_click_1_listener", "_r43", "ctx_r41", "openImageViewer", "MessageChatComponent_div_35_ng_container_1_div_7_Template_img_load_1_listener", "$event", "ctx_r44", "onImageLoad", "MessageChatComponent_div_35_ng_container_1_div_7_Template_img_error_1_listener", "ctx_r46", "onImageError", "ɵɵtemplate", "MessageChatComponent_div_35_ng_container_1_div_7_div_2_Template", "ctx_r29", "getImageUrl", "ctx_r49", "isVoicePlaying", "wave_r51", "i_r52", "MessageChatComponent_div_35_ng_container_1_div_8_button_8_Template_button_click_0_listener", "_r56", "ctx_r54", "changeVoiceSpeed", "ctx_r50", "getVoiceSpeed", "MessageChatComponent_div_35_ng_container_1_div_8_Template_button_click_1_listener", "_r60", "ctx_r58", "toggleVoicePlayback", "MessageChatComponent_div_35_ng_container_1_div_8_div_4_Template", "MessageChatComponent_div_35_ng_container_1_div_8_button_8_Template", "ɵɵclassMap", "ctx_r30", "voiceWaves", "getVoiceDuration", "MessageChatComponent_div_35_ng_container_1_div_12_i_1_Template", "MessageChatComponent_div_35_ng_container_1_div_12_i_2_Template", "MessageChatComponent_div_35_ng_container_1_div_12_i_3_Template", "MessageChatComponent_div_35_ng_container_1_div_12_i_4_Template", "status", "ɵɵelementContainerStart", "MessageChatComponent_div_35_ng_container_1_div_1_Template", "MessageChatComponent_div_35_ng_container_1_Template_div_click_2_listener", "restoredCtx", "_r68", "ctx_r67", "onMessageClick", "MessageChatComponent_div_35_ng_container_1_Template_div_contextmenu_2_listener", "ctx_r69", "onMessageContextMenu", "MessageChatComponent_div_35_ng_container_1_div_3_Template", "MessageChatComponent_div_35_ng_container_1_div_5_Template", "MessageChatComponent_div_35_ng_container_1_div_6_Template", "MessageChatComponent_div_35_ng_container_1_div_7_Template", "MessageChatComponent_div_35_ng_container_1_div_8_Template", "MessageChatComponent_div_35_ng_container_1_div_12_Template", "ɵɵelementContainerEnd", "ctx_r21", "shouldShowDateSeparator", "i_r24", "shouldShowAvatar", "isGroupConversation", "shouldShowSenderName", "getMessageType", "hasImage", "ɵɵtextInterpolate", "formatMessageTime", "ctx_r22", "MessageChatComponent_div_35_ng_container_1_Template", "MessageChatComponent_div_35_div_2_Template", "ctx_r10", "messages", "trackByMessageId", "otherUserIsTyping", "MessageChatComponent_div_51_button_5_Template_button_click_0_listener", "_r73", "emoji_r71", "ctx_r72", "insert<PERSON><PERSON><PERSON>", "name", "emoji", "MessageChatComponent_div_51_button_5_Template", "ctx_r14", "getEmojisForCategory", "selectedEmojiCategory", "MessageChatComponent_div_52_Template_button_click_5_listener", "_r75", "ctx_r74", "triggerFileInput", "MessageChatComponent_div_52_Template_button_click_10_listener", "ctx_r76", "MessageChatComponent_div_52_Template_button_click_15_listener", "ctx_r77", "openCamera", "MessageChatComponent_div_55_Template_div_click_0_listener", "_r79", "ctx_r78", "closeAllMenus", "wave_r81", "i_r82", "MessageChatComponent_div_56_div_7_Template", "MessageChatComponent_div_56_Template_button_click_11_listener", "_r84", "ctx_r83", "onRecordCancel", "MessageChatComponent_div_56_Template_button_click_13_listener", "ctx_r85", "onRecordEnd", "ctx_r18", "formatRecordingDuration", "voiceRecordingDuration", "getRecordingFormat", "MessageChatComponent", "constructor", "fb", "route", "router", "MessageService", "callService", "toastService", "cdr", "conversation", "currentUsername", "isLoading", "isLoadingMore", "hasMoreMessages", "showEmojiPicker", "showAttachmentMenu", "showSearch", "searchQuery", "searchResults", "searchMode", "isSendingMessage", "showMessageContextMenu", "selectedMessage", "contextMenuPosition", "x", "y", "showReactionPicker", "reactionPickerMessage", "showImageViewer", "selectedImage", "uploadProgress", "isUploading", "isDragOver", "isRecordingVoice", "voiceRecordingState", "mediaRecorder", "audioChunks", "recordingTimer", "currentAudio", "playingMessageId", "voicePlayback", "isInCall", "callType", "callDuration", "callTimer", "emojiCategories", "icon", "emojis", "MAX_MESSAGES_TO_LOAD", "currentPage", "isTyping", "isUserTyping", "typingTimeout", "subscriptions", "messageForm", "group", "required", "<PERSON><PERSON><PERSON><PERSON>", "isInputDisabled", "updateInputState", "contentControl", "get", "disable", "enable", "ngOnInit", "initializeComponent", "enableSoundsOnFirstInteraction", "ngAfterViewInit", "setTimeout", "setupVideoElements", "localVideo", "remoteVideo", "setVideoElements", "nativeElement", "localVideoHidden", "remoteVideoHidden", "createVideoElementsManually", "localVideoEl", "document", "getElementById", "remoteVideoEl", "console", "log", "warn", "createElement", "autoplay", "muted", "playsInline", "style", "cssText", "body", "append<PERSON><PERSON><PERSON>", "enableSounds", "removeEventListener", "addEventListener", "once", "loadCurrentUser", "loadConversation", "setupCallSubscriptions", "handleIncomingCall", "incomingCall", "caller", "play", "userString", "localStorage", "getItem", "error", "user", "JSON", "parse", "userId", "_id", "conversationId", "snapshot", "paramMap", "showError", "cleanupSubscriptions", "getConversation", "subscribe", "next", "setOtherParticipant", "loadMessages", "setupSubscriptions", "participants", "length", "isGroup", "find", "p", "participantId", "String", "firstParticipantId", "sort", "a", "b", "dateA", "Date", "createdAt", "getTime", "dateB", "total", "first", "last", "scrollToBottom", "loadMoreMessages", "offset", "getMessages", "newMessages", "reverse", "unsubscribe", "reloadConversation", "add", "subscribeToNewMessages", "newMessage", "type", "senderId", "receiverId", "attachments", "for<PERSON>ach", "att", "index", "url", "path", "size", "messageExists", "some", "msg", "push", "detectChanges", "shouldMarkAsRead", "markMessageAsRead", "subscribeToTypingIndicator", "typingData", "subscribeToConversationUpdates", "conversationUpdate", "messageId", "sendMessage", "valid", "value", "trim", "undefined", "message", "reset", "messagesContainer", "element", "scrollTop", "scrollHeight", "diffMins", "Math", "floor", "now", "getVoicePlaybackData", "progress", "duration", "currentTime", "speed", "setVoicePlaybackData", "data", "startVideoCall", "initiateCall", "VIDEO", "startVoiceCall", "AUDIO", "toggleMicrophone", "isMuted", "toggleAudio", "showSuccess", "toggleCamera", "isVideoEnabled", "toggleVideo", "endCall", "activeCall", "isCallConnected", "formatFileSize", "bytes", "round", "downloadFile", "fileAttachment", "startsWith", "link", "href", "download", "target", "click", "<PERSON><PERSON><PERSON><PERSON>", "toggleMainMenu", "goBackToConversations", "navigate", "then", "catch", "window", "location", "event", "preventDefault", "clientX", "clientY", "showQuickReactions", "stopPropagation", "quickReact", "toggleReaction", "reactToMessage", "result", "messageIndex", "findIndex", "hasUserReacted", "reaction", "replyToMessage", "forwardMessage", "deleteMessage", "canDelete", "confirm", "filter", "toggleEmojiPicker", "selectEmojiCategory", "category", "currentC<PERSON>nt", "newContent", "patchValue", "toggleAttachmentMenu", "toString", "testAddMessage", "testMessage", "toLocaleTimeString", "toISOString", "isRead", "zoomImage", "factor", "imageElement", "querySelector", "currentTransform", "transform", "currentScale", "parseFloat", "match", "newScale", "max", "min", "classList", "remove", "resetZoom", "input", "fileInput", "accept", "date", "hour", "minute", "today", "yesterday", "setDate", "getDate", "toDateString", "toLocaleDateString", "urlRegex", "replace", "currentMessage", "previousMessage", "currentDate", "previousDate", "nextMessage", "attachment", "voiceUrl", "audioUrl", "voice", "hasImageAttachment", "hasImageUrl", "imageUrl", "hasFile", "hasFileAttachment", "imageAttachment", "getFileName", "getFileSize", "getFileIcon", "includes", "colors", "charCodeAt", "onInputChange", "handleTypingIndicator", "onInputKeyDown", "key", "shift<PERSON>ey", "onInputFocus", "onInputBlur", "onScroll", "src", "closeImageViewer", "downloadImage", "searchMessages", "toLowerCase", "onSearchQueryChange", "clearSearch", "jumpToMessage", "messageElement", "scrollIntoView", "behavior", "block", "closeContextMenu", "recipientId", "<PERSON><PERSON><PERSON>", "startCallTimer", "call", "acceptCall", "rejectCall", "setInterval", "resetCallState", "clearInterval", "toggleMute", "toggleMedia", "formatCallDuration", "hours", "minutes", "seconds", "padStart", "startVoiceRecording", "_this", "_asyncToGenerator", "navigator", "mediaDevices", "getUserMedia", "Error", "MediaRecorder", "stream", "audio", "echoCancellation", "noiseSuppression", "autoGainControl", "sampleRate", "channelCount", "mimeType", "isTypeSupported", "animateVoice<PERSON>aves", "ondataavailable", "onstop", "processRecordedAudio", "onerror", "cancelVoiceRecording", "start", "errorMessage", "stopVoiceRecording", "state", "stop", "getTracks", "track", "_this2", "audioBlob", "Blob", "extension", "audioFile", "File", "sendVoiceMessage", "_this3", "Promise", "resolve", "reject", "onRecordStart", "showWarning", "showInfo", "map", "random", "onFileSelected", "files", "file", "uploadFile", "maxSize", "compressImage", "compressedFile", "sendFileToServer", "messageType", "getFileMessageType", "progressInterval", "resetUploadState", "getFileAcceptTypes", "onDragOver", "onDragLeave", "rect", "currentTarget", "getBoundingClientRect", "left", "right", "top", "bottom", "onDrop", "dataTransfer", "Array", "from", "quality", "canvas", "ctx", "getContext", "img", "Image", "onload", "max<PERSON><PERSON><PERSON>", "maxHeight", "width", "height", "ratio", "drawImage", "toBlob", "blob", "lastModified", "URL", "createObjectURL", "sendTypingIndicator", "clearTimeout", "onCallAccepted", "onCallRejected", "playVoiceMessage", "getVoiceUrl", "stopVoicePlayback", "startVoicePlayback", "Audio", "currentData", "playbackRate", "pause", "audioAttachment", "getVoiceWaves", "seed", "split", "reduce", "acc", "char", "waves", "i", "getVoiceProgress", "totalWaves", "getVoiceCurrentTime", "formatAudioTime", "metadata", "remainingSeconds", "seekVoiceMessage", "waveIndex", "seekPercentage", "seekTime", "toggleVoiceSpeed", "newSpeed", "ngOnDestroy", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "ActivatedRoute", "Router", "i3", "i4", "CallService", "i5", "ToastService", "ChangeDetectorRef", "selectors", "viewQuery", "MessageChatComponent_Query", "rf", "MessageChatComponent_Template_button_click_6_listener", "MessageChatComponent_Template_img_click_10_listener", "MessageChatComponent_div_11_Template", "MessageChatComponent_div_16_Template", "MessageChatComponent_span_17_Template", "MessageChatComponent_Template_button_click_19_listener", "MessageChatComponent_Template_button_click_21_listener", "MessageChatComponent_Template_button_click_23_listener", "MessageChatComponent_Template_button_click_25_listener", "MessageChatComponent_Template_button_click_27_listener", "MessageChatComponent_div_29_Template", "MessageChatComponent_Template_main_scroll_30_listener", "MessageChatComponent_Template_main_dragover_30_listener", "MessageChatComponent_Template_main_dragleave_30_listener", "MessageChatComponent_Template_main_drop_30_listener", "MessageChatComponent_div_32_Template", "MessageChatComponent_div_33_Template", "MessageChatComponent_div_34_Template", "MessageChatComponent_div_35_Template", "MessageChatComponent_Template_form_ngSubmit_37_listener", "MessageChatComponent_Template_button_click_39_listener", "MessageChatComponent_Template_button_click_41_listener", "MessageChatComponent_Template_button_mousedown_43_listener", "MessageChatComponent_Template_button_mouseup_43_listener", "MessageChatComponent_Template_button_mouseleave_43_listener", "MessageChatComponent_Template_button_touchstart_43_listener", "MessageChatComponent_Template_button_touchend_43_listener", "MessageChatComponent_Template_button_touchcancel_43_listener", "MessageChatComponent_div_45_Template", "MessageChatComponent_Template_textarea_keydown_47_listener", "MessageChatComponent_Template_textarea_input_47_listener", "MessageChatComponent_Template_textarea_focus_47_listener", "MessageChatComponent_i_49_Template", "MessageChatComponent_div_50_Template", "MessageChatComponent_div_51_Template", "MessageChatComponent_div_52_Template", "MessageChatComponent_Template_input_change_53_listener", "MessageChatComponent_div_55_Template", "MessageChatComponent_div_56_Template"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\messages\\message-chat\\message-chat.component.ts", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\messages\\message-chat\\message-chat.component.html"], "sourcesContent": ["import {\r\n  Compo<PERSON>,\r\n  On<PERSON><PERSON><PERSON>,\r\n  <PERSON><PERSON><PERSON><PERSON>,\r\n  AfterViewInit,\r\n  ViewChild,\r\n  ElementRef,\r\n  ChangeDetectorRef,\r\n} from '@angular/core';\r\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { Subscription } from 'rxjs';\r\nimport { MessageService } from '../../../../services/message.service';\r\nimport { CallService } from '../../../../services/call.service';\r\nimport { ToastService } from '../../../../services/toast.service';\r\nimport { CallType, Call, IncomingCall } from '../../../../models/message.model';\r\n\r\n@Component({\r\n  selector: 'app-message-chat',\r\n  templateUrl: './message-chat.component.html',\r\n})\r\nexport class MessageChatComponent implements OnInit, AfterViewInit, OnDestroy {\r\n  // === RÉFÉRENCES DOM ===\r\n  @ViewChild('messagesContainer') private messagesContainer!: ElementRef;\r\n  @ViewChild('fileInput', { static: false })\r\n  fileInput!: ElementRef<HTMLInputElement>;\r\n  @ViewChild('localVideo', { static: false })\r\n  localVideo!: ElementRef<HTMLVideoElement>;\r\n  @ViewChild('remoteVideo', { static: false })\r\n  remoteVideo!: ElementRef<HTMLVideoElement>;\r\n  @ViewChild('localVideoHidden', { static: false })\r\n  localVideoHidden!: ElementRef<HTMLVideoElement>;\r\n  @ViewChild('remoteVideoHidden', { static: false })\r\n  remoteVideoHidden!: ElementRef<HTMLVideoElement>;\r\n\r\n  // === DONNÉES PRINCIPALES ===\r\n  conversation: any = null;\r\n  messages: any[] = [];\r\n  currentUserId: string | null = null;\r\n  currentUsername = 'You';\r\n  messageForm: FormGroup;\r\n  otherParticipant: any = null;\r\n\r\n  // === ÉTATS DE L'INTERFACE ===\r\n  isLoading = false;\r\n  isLoadingMore = false;\r\n  hasMoreMessages = true;\r\n  showEmojiPicker = false;\r\n  showAttachmentMenu = false;\r\n  showSearch = false;\r\n  searchQuery = '';\r\n  searchResults: any[] = [];\r\n  searchMode = false;\r\n  isSendingMessage = false;\r\n  otherUserIsTyping = false;\r\n  showMainMenu = false;\r\n  showMessageContextMenu = false;\r\n  selectedMessage: any = null;\r\n  contextMenuPosition = { x: 0, y: 0 };\r\n  showReactionPicker = false;\r\n  reactionPickerMessage: any = null;\r\n\r\n  showImageViewer = false;\r\n  selectedImage: any = null;\r\n  uploadProgress = 0;\r\n  isUploading = false;\r\n  isDragOver = false;\r\n\r\n  // === GESTION VOCALE OPTIMISÉE ===\r\n  isRecordingVoice = false;\r\n  voiceRecordingDuration = 0;\r\n  voiceRecordingState: 'idle' | 'recording' | 'processing' = 'idle';\r\n  private mediaRecorder: MediaRecorder | null = null;\r\n  private audioChunks: Blob[] = [];\r\n  private recordingTimer: any = null;\r\n  voiceWaves: number[] = [\r\n    4, 8, 12, 16, 20, 16, 12, 8, 4, 8, 12, 16, 20, 16, 12, 8,\r\n  ];\r\n\r\n  // Lecture des messages vocaux\r\n  private currentAudio: HTMLAudioElement | null = null;\r\n  private playingMessageId: string | null = null;\r\n  private voicePlayback: {\r\n    [messageId: string]: {\r\n      progress: number;\r\n      duration: number;\r\n      currentTime: number;\r\n      speed: number;\r\n    };\r\n  } = {};\r\n\r\n  // === APPELS WEBRTC ===\r\n  isInCall = false;\r\n  callType: 'VIDEO' | 'AUDIO' | null = null;\r\n  callDuration = 0;\r\n  private callTimer: any = null;\r\n\r\n  // État de l'appel WebRTC - Géré globalement par les composants d'appel\r\n  // activeCall, isMuted, isVideoEnabled sont maintenant dans ActiveCallComponent\r\n\r\n  // === ÉMOJIS ===\r\n  emojiCategories: any[] = [\r\n    {\r\n      id: 'smileys',\r\n      name: 'Smileys',\r\n      icon: '😀',\r\n      emojis: [\r\n        { emoji: '😀', name: 'grinning face' },\r\n        { emoji: '😃', name: 'grinning face with big eyes' },\r\n        { emoji: '😄', name: 'grinning face with smiling eyes' },\r\n        { emoji: '😁', name: 'beaming face with smiling eyes' },\r\n        { emoji: '😆', name: 'grinning squinting face' },\r\n        { emoji: '😅', name: 'grinning face with sweat' },\r\n        { emoji: '😂', name: 'face with tears of joy' },\r\n        { emoji: '🤣', name: 'rolling on the floor laughing' },\r\n        { emoji: '😊', name: 'smiling face with smiling eyes' },\r\n        { emoji: '😇', name: 'smiling face with halo' },\r\n      ],\r\n    },\r\n    {\r\n      id: 'people',\r\n      name: 'People',\r\n      icon: '👤',\r\n      emojis: [\r\n        { emoji: '👶', name: 'baby' },\r\n        { emoji: '🧒', name: 'child' },\r\n        { emoji: '👦', name: 'boy' },\r\n        { emoji: '👧', name: 'girl' },\r\n        { emoji: '🧑', name: 'person' },\r\n        { emoji: '👨', name: 'man' },\r\n        { emoji: '👩', name: 'woman' },\r\n        { emoji: '👴', name: 'old man' },\r\n        { emoji: '👵', name: 'old woman' },\r\n      ],\r\n    },\r\n    {\r\n      id: 'nature',\r\n      name: 'Nature',\r\n      icon: '🌿',\r\n      emojis: [\r\n        { emoji: '🐶', name: 'dog face' },\r\n        { emoji: '🐱', name: 'cat face' },\r\n        { emoji: '🐭', name: 'mouse face' },\r\n        { emoji: '🐹', name: 'hamster' },\r\n        { emoji: '🐰', name: 'rabbit face' },\r\n        { emoji: '🦊', name: 'fox' },\r\n        { emoji: '🐻', name: 'bear' },\r\n        { emoji: '🐼', name: 'panda' },\r\n      ],\r\n    },\r\n  ];\r\n  selectedEmojiCategory = this.emojiCategories[0];\r\n\r\n  // === PAGINATION ===\r\n  private readonly MAX_MESSAGES_TO_LOAD = 10;\r\n  private currentPage = 1;\r\n\r\n  // === AUTRES ÉTATS ===\r\n  isTyping = false;\r\n  isUserTyping = false;\r\n  private typingTimeout: any = null;\r\n  private subscriptions = new Subscription();\r\n\r\n  constructor(\r\n    private fb: FormBuilder,\r\n    private route: ActivatedRoute,\r\n    private router: Router,\r\n    private MessageService: MessageService,\r\n    private callService: CallService,\r\n    private toastService: ToastService,\r\n    private cdr: ChangeDetectorRef\r\n  ) {\r\n    this.messageForm = this.fb.group({\r\n      content: ['', [Validators.required, Validators.minLength(1)]],\r\n    });\r\n  }\r\n\r\n  // Méthode pour vérifier si le champ de saisie doit être désactivé\r\n  isInputDisabled(): boolean {\r\n    return (\r\n      !this.otherParticipant || this.isRecordingVoice || this.isSendingMessage\r\n    );\r\n  }\r\n\r\n  // Méthode pour gérer l'état du contrôle de saisie\r\n  private updateInputState(): void {\r\n    const contentControl = this.messageForm.get('content');\r\n    if (this.isInputDisabled()) {\r\n      contentControl?.disable();\r\n    } else {\r\n      contentControl?.enable();\r\n    }\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.initializeComponent();\r\n\r\n    // Activer les sons après interaction utilisateur\r\n    this.enableSoundsOnFirstInteraction();\r\n  }\r\n\r\n  ngAfterViewInit(): void {\r\n    // Configurer les éléments vidéo pour WebRTC après que la vue soit initialisée\r\n    setTimeout(() => {\r\n      this.setupVideoElements();\r\n    }, 100);\r\n\r\n    // Réessayer plusieurs fois pour s'assurer que les éléments sont configurés\r\n    setTimeout(() => {\r\n      this.setupVideoElements();\r\n    }, 500);\r\n\r\n    setTimeout(() => {\r\n      this.setupVideoElements();\r\n    }, 1000);\r\n\r\n    setTimeout(() => {\r\n      this.setupVideoElements();\r\n    }, 2000);\r\n  }\r\n\r\n  /**\r\n   * Configure les éléments vidéo pour WebRTC\r\n   */\r\n  private setupVideoElements(): void {\r\n    // Essayer d'abord les éléments visibles (pour appels vidéo)\r\n    if (this.localVideo && this.remoteVideo) {\r\n      this.callService.setVideoElements(\r\n        this.localVideo.nativeElement,\r\n        this.remoteVideo.nativeElement\r\n      );\r\n    }\r\n    // Sinon utiliser les éléments cachés (pour appels audio)\r\n    else if (this.localVideoHidden && this.remoteVideoHidden) {\r\n      this.callService.setVideoElements(\r\n        this.localVideoHidden.nativeElement,\r\n        this.remoteVideoHidden.nativeElement\r\n      );\r\n    } else {\r\n      this.createVideoElementsManually();\r\n\r\n      // Réessayer après un délai\r\n      setTimeout(() => {\r\n        this.setupVideoElements();\r\n      }, 500);\r\n\r\n      // Réessayer encore plus tard\r\n      setTimeout(() => {\r\n        this.setupVideoElements();\r\n      }, 1500);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Crée les éléments vidéo manuellement si les ViewChild ne fonctionnent pas\r\n   */\r\n  private createVideoElementsManually(): void {\r\n    // Chercher les éléments dans le DOM\r\n    const localVideoEl = document.getElementById(\r\n      'localVideo'\r\n    ) as HTMLVideoElement;\r\n    const remoteVideoEl = document.getElementById(\r\n      'remoteVideo'\r\n    ) as HTMLVideoElement;\r\n\r\n    if (localVideoEl && remoteVideoEl) {\r\n      console.log(\r\n        '✅ [MessageChat] Found video elements in DOM, configuring...'\r\n      );\r\n      this.callService.setVideoElements(localVideoEl, remoteVideoEl);\r\n    } else {\r\n      console.warn('⚠️ [MessageChat] Video elements not found in DOM either');\r\n\r\n      // Créer les éléments dynamiquement\r\n      const localVideo = document.createElement('video');\r\n      localVideo.id = 'localVideo';\r\n      localVideo.autoplay = true;\r\n      localVideo.muted = true;\r\n      localVideo.playsInline = true;\r\n      localVideo.style.cssText =\r\n        'position: absolute; top: -9999px; left: -9999px; width: 1px; height: 1px;';\r\n\r\n      const remoteVideo = document.createElement('video');\r\n      remoteVideo.id = 'remoteVideo';\r\n      remoteVideo.autoplay = true;\r\n      remoteVideo.playsInline = true;\r\n      remoteVideo.style.cssText =\r\n        'position: absolute; top: -9999px; left: -9999px; width: 1px; height: 1px;';\r\n\r\n      document.body.appendChild(localVideo);\r\n      document.body.appendChild(remoteVideo);\r\n\r\n      this.callService.setVideoElements(localVideo, remoteVideo);\r\n    }\r\n  }\r\n\r\n  private enableSoundsOnFirstInteraction(): void {\r\n    const enableSounds = () => {\r\n      this.callService.enableSounds();\r\n      document.removeEventListener('click', enableSounds);\r\n      document.removeEventListener('keydown', enableSounds);\r\n      document.removeEventListener('touchstart', enableSounds);\r\n    };\r\n\r\n    document.addEventListener('click', enableSounds, { once: true });\r\n    document.addEventListener('keydown', enableSounds, { once: true });\r\n    document.addEventListener('touchstart', enableSounds, { once: true });\r\n  }\r\n\r\n  private initializeComponent(): void {\r\n    this.loadCurrentUser();\r\n    this.loadConversation();\r\n    this.setupCallSubscriptions();\r\n  }\r\n\r\n  private setupCallSubscriptions(): void {\r\n    // Les appels sont maintenant gérés globalement par app-incoming-call et app-active-call\r\n    // Plus besoin de subscriptions locales ici\r\n    console.log('📞 [MessageChat] Call subscriptions handled globally');\r\n  }\r\n\r\n  private handleIncomingCall(incomingCall: IncomingCall): void {\r\n    // Afficher une notification ou modal d'appel entrant\r\n    // Pour l'instant, on log juste\r\n    console.log(\r\n      '🔔 Handling incoming call from:',\r\n      incomingCall.caller.username\r\n    );\r\n\r\n    // Jouer la sonnerie\r\n    this.MessageService.play('ringtone');\r\n\r\n    // Ici on pourrait afficher une modal ou notification\r\n    // Pour l'instant, on accepte automatiquement pour tester\r\n    // this.acceptCall(incomingCall);\r\n  }\r\n\r\n  private loadCurrentUser(): void {\r\n    try {\r\n      const userString = localStorage.getItem('user');\r\n\r\n      if (!userString || userString === 'null' || userString === 'undefined') {\r\n        console.error('❌ No user data in localStorage');\r\n        this.currentUserId = null;\r\n        this.currentUsername = 'You';\r\n        return;\r\n      }\r\n\r\n      const user = JSON.parse(userString);\r\n\r\n      // Essayer différentes propriétés pour l'ID utilisateur\r\n      const userId = user._id || user.id || user.userId;\r\n\r\n      if (userId) {\r\n        this.currentUserId = userId;\r\n        this.currentUsername = user.username || user.name || 'You';\r\n      } else {\r\n        console.error('❌ No valid user ID found in user object:', user);\r\n        this.currentUserId = null;\r\n        this.currentUsername = 'You';\r\n      }\r\n    } catch (error) {\r\n      console.error('❌ Error parsing user from localStorage:', error);\r\n      this.currentUserId = null;\r\n      this.currentUsername = 'You';\r\n    }\r\n  }\r\n\r\n  private loadConversation(): void {\r\n    const conversationId = this.route.snapshot.paramMap.get('id');\r\n\r\n    if (!conversationId) {\r\n      this.toastService.showError('ID de conversation manquant');\r\n      return;\r\n    }\r\n\r\n    this.isLoading = true;\r\n\r\n    // Nettoyer les subscriptions existantes avant de recharger\r\n    this.cleanupSubscriptions();\r\n\r\n    this.MessageService.getConversation(conversationId).subscribe({\r\n      next: (conversation) => {\r\n        this.conversation = conversation;\r\n        this.setOtherParticipant();\r\n        this.loadMessages();\r\n\r\n        // Configurer les subscriptions temps réel après le chargement de la conversation\r\n        this.setupSubscriptions();\r\n        this.isLoading = false;\r\n      },\r\n      error: (error) => {\r\n        console.error('Erreur lors du chargement de la conversation:', error);\r\n        this.toastService.showError(\r\n          'Erreur lors du chargement de la conversation'\r\n        );\r\n        this.isLoading = false;\r\n\r\n        // Réessayer après 2 secondes en cas d'erreur\r\n        setTimeout(() => {\r\n          this.loadConversation();\r\n        }, 2000);\r\n      },\r\n    });\r\n  }\r\n\r\n  private setOtherParticipant(): void {\r\n    if (\r\n      !this.conversation?.participants ||\r\n      this.conversation.participants.length === 0\r\n    ) {\r\n      console.warn('No participants found in conversation');\r\n      this.otherParticipant = null;\r\n      return;\r\n    }\r\n\r\n    // Dans une conversation 1-à-1, on veut afficher l'autre personne (pas l'utilisateur actuel)\r\n    // Dans une conversation de groupe, on peut afficher le nom du groupe ou le premier autre participant\r\n\r\n    if (this.conversation.isGroup) {\r\n      // Pour les groupes, on pourrait afficher le nom du groupe\r\n      // Mais pour l'instant, on prend le premier participant qui n'est pas l'utilisateur actuel\r\n      this.otherParticipant = this.conversation.participants.find((p: any) => {\r\n        const participantId = p.id || p._id;\r\n        return String(participantId) !== String(this.currentUserId);\r\n      });\r\n    } else {\r\n      // Pour les conversations 1-à-1, on prend l'autre participant\r\n      this.otherParticipant = this.conversation.participants.find((p: any) => {\r\n        const participantId = p.id || p._id;\r\n        console.log(\r\n          'Comparing participant ID:',\r\n          participantId,\r\n          'with current user ID:',\r\n          this.currentUserId\r\n        );\r\n        return String(participantId) !== String(this.currentUserId);\r\n      });\r\n    }\r\n\r\n    // Fallback si aucun autre participant n'est trouvé\r\n    if (!this.otherParticipant && this.conversation.participants.length > 0) {\r\n      this.otherParticipant = this.conversation.participants[0];\r\n\r\n      // Si le premier participant est l'utilisateur actuel et qu'il y en a d'autres\r\n      if (this.conversation.participants.length > 1) {\r\n        const firstParticipantId =\r\n          this.otherParticipant.id || this.otherParticipant._id;\r\n        if (String(firstParticipantId) === String(this.currentUserId)) {\r\n          console.log(\r\n            'First participant is current user, using second participant'\r\n          );\r\n          this.otherParticipant = this.conversation.participants[1];\r\n        }\r\n      }\r\n    }\r\n\r\n    // Vérification finale et logs\r\n    if (this.otherParticipant) {\r\n      // Log très visible pour debug\r\n      console.log(\r\n        '🎯 FINAL RESULT: otherParticipant =',\r\n        this.otherParticipant.username\r\n      );\r\n      console.log(\r\n        '🎯 Should display in sidebar:',\r\n        this.otherParticipant.username\r\n      );\r\n    } else {\r\n      console.error('❌ No other participant found! This should not happen.');\r\n\r\n      // Log très visible pour debug\r\n    }\r\n\r\n    // Mettre à jour l'état du champ de saisie\r\n    this.updateInputState();\r\n  }\r\n\r\n  private loadMessages(): void {\r\n    if (!this.conversation?.id) return;\r\n\r\n    // Les messages sont déjà chargés avec la conversation\r\n    let messages = this.conversation.messages || [];\r\n\r\n    // Trier les messages par timestamp (plus anciens en premier)\r\n    this.messages = messages.sort((a: any, b: any) => {\r\n      const dateA = new Date(a.timestamp || a.createdAt).getTime();\r\n      const dateB = new Date(b.timestamp || b.createdAt).getTime();\r\n      return dateA - dateB; // Ordre croissant (plus anciens en premier)\r\n    });\r\n\r\n    console.log('📋 Messages loaded and sorted:', {\r\n      total: this.messages.length,\r\n      first: this.messages[0]?.content,\r\n      last: this.messages[this.messages.length - 1]?.content,\r\n    });\r\n\r\n    this.hasMoreMessages = this.messages.length === this.MAX_MESSAGES_TO_LOAD;\r\n    this.isLoading = false;\r\n    this.scrollToBottom();\r\n  }\r\n\r\n  loadMoreMessages(): void {\r\n    if (this.isLoadingMore || !this.hasMoreMessages || !this.conversation?.id)\r\n      return;\r\n\r\n    this.isLoadingMore = true;\r\n    this.currentPage++;\r\n\r\n    // Calculer l'offset basé sur les messages déjà chargés\r\n    const offset = this.messages.length;\r\n\r\n    this.MessageService.getMessages(\r\n      this.currentUserId!, // senderId\r\n      this.otherParticipant?.id || this.otherParticipant?._id!, // receiverId\r\n      this.conversation.id,\r\n      this.currentPage,\r\n      this.MAX_MESSAGES_TO_LOAD\r\n    ).subscribe({\r\n      next: (newMessages: any[]) => {\r\n        if (newMessages && newMessages.length > 0) {\r\n          // Ajouter les nouveaux messages au début de la liste\r\n          this.messages = [...newMessages.reverse(), ...this.messages];\r\n          this.hasMoreMessages =\r\n            newMessages.length === this.MAX_MESSAGES_TO_LOAD;\r\n        } else {\r\n          this.hasMoreMessages = false;\r\n        }\r\n        this.isLoadingMore = false;\r\n      },\r\n      error: (error) => {\r\n        console.error('Erreur lors du chargement des messages:', error);\r\n        this.toastService.showError('Erreur lors du chargement des messages');\r\n        this.isLoadingMore = false;\r\n        this.currentPage--; // Revenir à la page précédente en cas d'erreur\r\n      },\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Nettoie les subscriptions existantes\r\n   */\r\n  private cleanupSubscriptions(): void {\r\n    this.subscriptions.unsubscribe();\r\n    this.subscriptions = new Subscription();\r\n  }\r\n\r\n  /**\r\n   * Recharge la conversation actuelle\r\n   */\r\n  public reloadConversation(): void {\r\n    if (this.conversation?.id) {\r\n      // Réinitialiser l'état\r\n      this.messages = [];\r\n      this.currentPage = 1;\r\n      this.hasMoreMessages = true;\r\n\r\n      // Recharger\r\n      this.loadConversation();\r\n    }\r\n  }\r\n\r\n  private setupSubscriptions(): void {\r\n    if (!this.conversation?.id) {\r\n      console.warn('❌ Cannot setup subscriptions: no conversation ID');\r\n      return;\r\n    }\r\n\r\n    console.log(\r\n      '🔄 Setting up real-time subscriptions for conversation:',\r\n      this.conversation.id\r\n    );\r\n\r\n    // Subscription pour les nouveaux messages\r\n\r\n    this.subscriptions.add(\r\n      this.MessageService.subscribeToNewMessages(\r\n        this.conversation.id\r\n      ).subscribe({\r\n        next: (newMessage: any) => {\r\n          console.log('📨 Message structure:', {\r\n            id: newMessage.id,\r\n            type: newMessage.type,\r\n            content: newMessage.content,\r\n            sender: newMessage.sender,\r\n            senderId: newMessage.senderId,\r\n            receiverId: newMessage.receiverId,\r\n            attachments: newMessage.attachments,\r\n          });\r\n\r\n          // Debug des attachments\r\n          console.log(\r\n            '📨 [Debug] Message type detected:',\r\n            this.getMessageType(newMessage)\r\n          );\r\n\r\n          if (newMessage.attachments) {\r\n            newMessage.attachments.forEach((att: any, index: number) => {\r\n              console.log(`📨 [Debug] Attachment ${index}:`, {\r\n                type: att.type,\r\n                url: att.url,\r\n                path: att.path,\r\n                name: att.name,\r\n                size: att.size,\r\n              });\r\n            });\r\n          }\r\n\r\n          // Ajouter le message à la liste s'il n'existe pas déjà\r\n          const messageExists = this.messages.some(\r\n            (msg) => msg.id === newMessage.id\r\n          );\r\n          if (!messageExists) {\r\n            // Ajouter le nouveau message à la fin (en bas)\r\n            this.messages.push(newMessage);\r\n            console.log(\r\n              '✅ Message added to list, total messages:',\r\n              this.messages.length\r\n            );\r\n\r\n            // Forcer la détection de changements\r\n            this.cdr.detectChanges();\r\n\r\n            // Scroll vers le bas après un court délai\r\n            setTimeout(() => {\r\n              this.scrollToBottom();\r\n            }, 50);\r\n\r\n            // Marquer comme lu si ce n'est pas notre message\r\n            const senderId = newMessage.sender?.id || newMessage.senderId;\r\n            console.log('📨 Checking if message should be marked as read:', {\r\n              senderId,\r\n              currentUserId: this.currentUserId,\r\n              shouldMarkAsRead: senderId !== this.currentUserId,\r\n            });\r\n\r\n            if (senderId && senderId !== this.currentUserId) {\r\n              this.markMessageAsRead(newMessage.id);\r\n            }\r\n          }\r\n        },\r\n        error: (error) => {\r\n          console.error('❌ Error in message subscription:', error);\r\n          this.toastService.showError('Connexion temps réel interrompue');\r\n\r\n          // Réessayer la connexion après 5 secondes\r\n          setTimeout(() => {\r\n            this.setupSubscriptions();\r\n          }, 5000);\r\n        },\r\n      })\r\n    );\r\n\r\n    // Subscription pour les indicateurs de frappe\r\n\r\n    this.subscriptions.add(\r\n      this.MessageService.subscribeToTypingIndicator(\r\n        this.conversation.id\r\n      ).subscribe({\r\n        next: (typingData: any) => {\r\n          // Afficher l'indicateur seulement si c'est l'autre utilisateur qui tape\r\n          if (typingData.userId !== this.currentUserId) {\r\n            this.otherUserIsTyping = typingData.isTyping;\r\n            this.isUserTyping = typingData.isTyping; // Pour compatibilité avec le template\r\n            console.log(\r\n              '📝 Other user typing status updated:',\r\n              this.otherUserIsTyping\r\n            );\r\n            this.cdr.detectChanges();\r\n          }\r\n        },\r\n        error: (error: any) => {\r\n          console.error('❌ Error in typing subscription:', error);\r\n        },\r\n      })\r\n    );\r\n\r\n    // Subscription pour les mises à jour de conversation\r\n    this.subscriptions.add(\r\n      this.MessageService.subscribeToConversationUpdates(\r\n        this.conversation.id\r\n      ).subscribe({\r\n        next: (conversationUpdate: any) => {\r\n          // Mettre à jour la conversation si nécessaire\r\n          if (conversationUpdate.id === this.conversation.id) {\r\n            this.conversation = { ...this.conversation, ...conversationUpdate };\r\n            this.cdr.detectChanges();\r\n          }\r\n        },\r\n        error: (error: any) => {\r\n          console.error('❌ Error in conversation subscription:', error);\r\n        },\r\n      })\r\n    );\r\n  }\r\n\r\n  private markMessageAsRead(messageId: string): void {\r\n    this.MessageService.markMessageAsRead(messageId).subscribe({\r\n      next: () => {},\r\n      error: (error) => {\r\n        console.error('❌ Error marking message as read:', error);\r\n      },\r\n    });\r\n  }\r\n\r\n  // === ENVOI DE MESSAGES ===\r\n  sendMessage(): void {\r\n    if (!this.messageForm.valid || !this.conversation?.id) return;\r\n\r\n    const content = this.messageForm.get('content')?.value?.trim();\r\n    if (!content) return;\r\n\r\n    const receiverId = this.otherParticipant?.id || this.otherParticipant?._id;\r\n\r\n    if (!receiverId) {\r\n      this.toastService.showError('Destinataire introuvable');\r\n      return;\r\n    }\r\n\r\n    // Désactiver le bouton d'envoi\r\n    this.isSendingMessage = true;\r\n    this.updateInputState();\r\n\r\n    console.log('📤 Sending message:', {\r\n      content,\r\n      receiverId,\r\n      conversationId: this.conversation.id,\r\n    });\r\n\r\n    this.MessageService.sendMessage(\r\n      receiverId,\r\n      content,\r\n      undefined,\r\n      'TEXT' as any,\r\n      this.conversation.id\r\n    ).subscribe({\r\n      next: (message: any) => {\r\n        // Ajouter le message à la liste s'il n'y est pas déjà\r\n        const messageExists = this.messages.some(\r\n          (msg) => msg.id === message.id\r\n        );\r\n        if (!messageExists) {\r\n          this.messages.push(message);\r\n          console.log(\r\n            '📋 Message added to local list, total:',\r\n            this.messages.length\r\n          );\r\n        }\r\n\r\n        // Réinitialiser le formulaire\r\n        this.messageForm.reset();\r\n        this.isSendingMessage = false;\r\n        this.updateInputState();\r\n\r\n        // Forcer la détection de changements et scroll\r\n        this.cdr.detectChanges();\r\n        setTimeout(() => {\r\n          this.scrollToBottom();\r\n        }, 50);\r\n      },\r\n      error: (error: any) => {\r\n        console.error(\"❌ Erreur lors de l'envoi du message:\", error);\r\n        this.toastService.showError(\"Erreur lors de l'envoi du message\");\r\n        this.isSendingMessage = false;\r\n        this.updateInputState();\r\n      },\r\n    });\r\n  }\r\n\r\n  scrollToBottom(): void {\r\n    setTimeout(() => {\r\n      if (this.messagesContainer) {\r\n        const element = this.messagesContainer.nativeElement;\r\n        element.scrollTop = element.scrollHeight;\r\n      }\r\n    }, 100);\r\n  }\r\n\r\n  // === MÉTHODES UTILITAIRES OPTIMISÉES ===\r\n  formatLastActive(lastActive: string | Date | null): string {\r\n    if (!lastActive) return 'Hors ligne';\r\n\r\n    const diffMins = Math.floor(\r\n      (Date.now() - new Date(lastActive).getTime()) / 60000\r\n    );\r\n\r\n    if (diffMins < 1) return \"À l'instant\";\r\n    if (diffMins < 60) return `Il y a ${diffMins} min`;\r\n    if (diffMins < 1440) return `Il y a ${Math.floor(diffMins / 60)}h`;\r\n    return `Il y a ${Math.floor(diffMins / 1440)}j`;\r\n  }\r\n\r\n  // Méthodes utilitaires pour les messages vocaux\r\n  getVoicePlaybackData(messageId: string) {\r\n    return (\r\n      this.voicePlayback[messageId] || {\r\n        progress: 0,\r\n        duration: 0,\r\n        currentTime: 0,\r\n        speed: 1,\r\n      }\r\n    );\r\n  }\r\n\r\n  private setVoicePlaybackData(\r\n    messageId: string,\r\n    data: Partial<(typeof this.voicePlayback)[string]>\r\n  ) {\r\n    this.voicePlayback[messageId] = {\r\n      ...this.getVoicePlaybackData(messageId),\r\n      ...data,\r\n    };\r\n  }\r\n\r\n  // === MÉTHODES POUR LES MESSAGES VOCAUX (TEMPLATE) ===\r\n\r\n  // === MÉTHODES POUR LES APPELS (TEMPLATE) ===\r\n\r\n  startVideoCall(): void {\r\n    if (!this.otherParticipant?.id) {\r\n      this.toastService.showError(\"Impossible de démarrer l'appel\");\r\n      return;\r\n    }\r\n\r\n    this.initiateCall(CallType.VIDEO);\r\n  }\r\n\r\n  startVoiceCall(): void {\r\n    if (!this.otherParticipant?.id) {\r\n      this.toastService.showError(\"Impossible de démarrer l'appel\");\r\n      return;\r\n    }\r\n\r\n    // Forcer la configuration des éléments vidéo avant l'appel\r\n    this.setupVideoElements();\r\n\r\n    this.initiateCall(CallType.AUDIO);\r\n  }\r\n\r\n  /**\r\n   * Bascule l'état du microphone\r\n   */\r\n  toggleMicrophone(): void {\r\n    this.isMuted = !this.isMuted;\r\n\r\n    if (this.callService.toggleAudio) {\r\n      this.callService.toggleAudio();\r\n    }\r\n\r\n    this.toastService.showSuccess(\r\n      this.isMuted ? 'Microphone coupé' : 'Microphone activé'\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Bascule l'état de la caméra\r\n   */\r\n  toggleCamera(): void {\r\n    this.isVideoEnabled = !this.isVideoEnabled;\r\n    console.log(\r\n      '📹 Camera toggled:',\r\n      this.isVideoEnabled ? 'ENABLED' : 'DISABLED'\r\n    );\r\n\r\n    if (this.callService.toggleVideo) {\r\n      this.callService.toggleVideo();\r\n    }\r\n\r\n    this.toastService.showSuccess(\r\n      this.isVideoEnabled ? 'Caméra activée' : 'Caméra désactivée'\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Termine l'appel en cours\r\n   */\r\n  endCall(): void {\r\n    if (this.activeCall) {\r\n      if (this.callService.endCall) {\r\n        this.callService.endCall(this.activeCall.id).subscribe({\r\n          next: () => {\r\n            this.activeCall = null;\r\n            this.isCallConnected = false;\r\n          },\r\n          error: (error) => {\r\n            console.error('❌ Error ending call:', error);\r\n            this.toastService.showError(\"Erreur lors de la fin de l'appel\");\r\n          },\r\n        });\r\n      } else {\r\n        // Fallback si la méthode n'existe pas\r\n        this.activeCall = null;\r\n        this.isCallConnected = false;\r\n        this.toastService.showSuccess('Appel terminé');\r\n      }\r\n    }\r\n  }\r\n\r\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\r\n  // onCallAccepted, onCallRejected - définies plus loin\r\n\r\n  // === MÉTHODES POUR LES FICHIERS (TEMPLATE) ===\r\n\r\n  formatFileSize(bytes: number): string {\r\n    if (bytes < 1024) return bytes + ' B';\r\n    if (bytes < 1048576) return Math.round(bytes / 1024) + ' KB';\r\n    return Math.round(bytes / 1048576) + ' MB';\r\n  }\r\n\r\n  downloadFile(message: any): void {\r\n    const fileAttachment = message.attachments?.find(\r\n      (att: any) => !att.type?.startsWith('image/')\r\n    );\r\n    if (fileAttachment?.url) {\r\n      const link = document.createElement('a');\r\n      link.href = fileAttachment.url;\r\n      link.download = fileAttachment.name || 'file';\r\n      link.target = '_blank';\r\n      document.body.appendChild(link);\r\n      link.click();\r\n      document.body.removeChild(link);\r\n      this.toastService.showSuccess('Téléchargement démarré');\r\n    }\r\n  }\r\n\r\n  // === MÉTHODES POUR L'INTERFACE UTILISATEUR (TEMPLATE) ===\r\n\r\n  toggleSearch(): void {\r\n    this.searchMode = !this.searchMode;\r\n    this.showSearch = this.searchMode;\r\n  }\r\n\r\n  toggleMainMenu(): void {\r\n    this.showMainMenu = !this.showMainMenu;\r\n  }\r\n\r\n  goBackToConversations(): void {\r\n    // Naviguer vers la liste des conversations\r\n    this.router\r\n      .navigate(['/front/messages/conversations'])\r\n      .then(() => {})\r\n      .catch((error) => {\r\n        console.error('❌ Navigation error:', error);\r\n        // Fallback: essayer la route parent\r\n        this.router.navigate(['/front/messages']).catch(() => {\r\n          // Dernier recours: recharger la page\r\n          window.location.href = '/front/messages/conversations';\r\n        });\r\n      });\r\n  }\r\n\r\n  // === MÉTHODES POUR LES MENUS ET INTERACTIONS ===\r\n\r\n  closeAllMenus(): void {\r\n    this.showEmojiPicker = false;\r\n    this.showAttachmentMenu = false;\r\n    this.showMainMenu = false;\r\n    this.showMessageContextMenu = false;\r\n    this.showReactionPicker = false;\r\n  }\r\n\r\n  onMessageContextMenu(message: any, event: MouseEvent): void {\r\n    event.preventDefault();\r\n    this.selectedMessage = message;\r\n    this.contextMenuPosition = { x: event.clientX, y: event.clientY };\r\n    this.showMessageContextMenu = true;\r\n  }\r\n\r\n  showQuickReactions(message: any, event: MouseEvent): void {\r\n    event.stopPropagation();\r\n    this.reactionPickerMessage = message;\r\n    this.contextMenuPosition = { x: event.clientX, y: event.clientY };\r\n    this.showReactionPicker = true;\r\n  }\r\n\r\n  quickReact(emoji: string): void {\r\n    if (this.reactionPickerMessage) {\r\n      this.toggleReaction(this.reactionPickerMessage.id, emoji);\r\n    }\r\n    this.showReactionPicker = false;\r\n  }\r\n\r\n  toggleReaction(messageId: string, emoji: string): void {\r\n    if (!messageId || !emoji) {\r\n      console.error('❌ Missing messageId or emoji for reaction');\r\n      return;\r\n    }\r\n\r\n    // Appeler le service pour ajouter/supprimer la réaction\r\n    this.MessageService.reactToMessage(messageId, emoji).subscribe({\r\n      next: (result) => {\r\n        // Mettre à jour le message local avec les nouvelles réactions\r\n        const messageIndex = this.messages.findIndex(\r\n          (msg) => msg.id === messageId\r\n        );\r\n        if (messageIndex !== -1) {\r\n          this.messages[messageIndex] = result;\r\n          this.cdr.detectChanges();\r\n        }\r\n\r\n        this.toastService.showSuccess(`Réaction ${emoji} ajoutée`);\r\n      },\r\n      error: (error) => {\r\n        console.error('❌ Error toggling reaction:', error);\r\n        this.toastService.showError(\"Erreur lors de l'ajout de la réaction\");\r\n      },\r\n    });\r\n  }\r\n\r\n  hasUserReacted(reaction: any, userId: string): boolean {\r\n    return reaction.userId === userId;\r\n  }\r\n\r\n  replyToMessage(message: any): void {\r\n    this.closeAllMenus();\r\n  }\r\n\r\n  forwardMessage(message: any): void {\r\n    this.closeAllMenus();\r\n  }\r\n\r\n  deleteMessage(message: any): void {\r\n    if (!message.id) {\r\n      console.error('❌ No message ID provided for deletion');\r\n      this.toastService.showError('Erreur: ID du message manquant');\r\n      return;\r\n    }\r\n\r\n    // Vérifier si l'utilisateur peut supprimer ce message\r\n    const canDelete =\r\n      message.sender?.id === this.currentUserId ||\r\n      message.senderId === this.currentUserId;\r\n\r\n    if (!canDelete) {\r\n      this.toastService.showError(\r\n        'Vous ne pouvez supprimer que vos propres messages'\r\n      );\r\n      this.closeAllMenus();\r\n      return;\r\n    }\r\n\r\n    // Demander confirmation\r\n    if (!confirm('Êtes-vous sûr de vouloir supprimer ce message ?')) {\r\n      this.closeAllMenus();\r\n      return;\r\n    }\r\n\r\n    // Appeler le service pour supprimer le message\r\n    this.MessageService.deleteMessage(message.id).subscribe({\r\n      next: (result) => {\r\n        // Supprimer le message de la liste locale\r\n        this.messages = this.messages.filter((msg) => msg.id !== message.id);\r\n\r\n        this.toastService.showSuccess('Message supprimé');\r\n        this.cdr.detectChanges();\r\n      },\r\n      error: (error) => {\r\n        console.error('❌ Error deleting message:', error);\r\n        this.toastService.showError('Erreur lors de la suppression du message');\r\n      },\r\n    });\r\n\r\n    this.closeAllMenus();\r\n  }\r\n\r\n  // === MÉTHODES POUR LES ÉMOJIS ET PIÈCES JOINTES ===\r\n\r\n  toggleEmojiPicker(): void {\r\n    this.showEmojiPicker = !this.showEmojiPicker;\r\n  }\r\n\r\n  selectEmojiCategory(category: any): void {\r\n    this.selectedEmojiCategory = category;\r\n  }\r\n\r\n  getEmojisForCategory(category: any): any[] {\r\n    return category?.emojis || [];\r\n  }\r\n\r\n  insertEmoji(emoji: any): void {\r\n    const currentContent = this.messageForm.get('content')?.value || '';\r\n    const newContent = currentContent + emoji.emoji;\r\n    this.messageForm.patchValue({ content: newContent });\r\n    this.showEmojiPicker = false;\r\n  }\r\n\r\n  toggleAttachmentMenu(): void {\r\n    this.showAttachmentMenu = !this.showAttachmentMenu;\r\n  }\r\n\r\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\r\n  // triggerFileInput, getFileAcceptTypes, onFileSelected - définies plus loin\r\n\r\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\r\n  // Ces méthodes sont déjà définies plus loin dans le fichier\r\n\r\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\r\n  // handleTypingIndicator - définie plus loin\r\n\r\n  // === MÉTHODES UTILITAIRES POUR LE TEMPLATE ===\r\n\r\n  trackByMessageId(index: number, message: any): string {\r\n    return message.id || message._id || index.toString();\r\n  }\r\n\r\n  // === MÉTHODES MANQUANTES POUR LE TEMPLATE ===\r\n\r\n  testAddMessage(): void {\r\n    const testMessage = {\r\n      id: `test-${Date.now()}`,\r\n      content: `Message de test ${new Date().toLocaleTimeString()}`,\r\n      timestamp: new Date().toISOString(),\r\n      sender: {\r\n        id: this.otherParticipant?.id || 'test-user',\r\n        username: this.otherParticipant?.username || 'Test User',\r\n        image:\r\n          this.otherParticipant?.image || 'assets/images/default-avatar.png',\r\n      },\r\n      type: 'TEXT',\r\n      isRead: false,\r\n    };\r\n    this.messages.push(testMessage);\r\n    this.cdr.detectChanges();\r\n    setTimeout(() => this.scrollToBottom(), 50);\r\n  }\r\n\r\n  isGroupConversation(): boolean {\r\n    return (\r\n      this.conversation?.isGroup ||\r\n      this.conversation?.participants?.length > 2 ||\r\n      false\r\n    );\r\n  }\r\n\r\n  openCamera(): void {\r\n    this.showAttachmentMenu = false;\r\n    // TODO: Implémenter l'ouverture de la caméra\r\n  }\r\n\r\n  zoomImage(factor: number): void {\r\n    const imageElement = document.querySelector(\r\n      '.image-viewer-zoom'\r\n    ) as HTMLElement;\r\n    if (imageElement) {\r\n      const currentTransform = imageElement.style.transform || 'scale(1)';\r\n      const currentScale = parseFloat(\r\n        currentTransform.match(/scale\\(([^)]+)\\)/)?.[1] || '1'\r\n      );\r\n      const newScale = Math.max(0.5, Math.min(3, currentScale * factor));\r\n      imageElement.style.transform = `scale(${newScale})`;\r\n      if (newScale > 1) {\r\n        imageElement.classList.add('zoomed');\r\n      } else {\r\n        imageElement.classList.remove('zoomed');\r\n      }\r\n    }\r\n  }\r\n\r\n  resetZoom(): void {\r\n    const imageElement = document.querySelector(\r\n      '.image-viewer-zoom'\r\n    ) as HTMLElement;\r\n    if (imageElement) {\r\n      imageElement.style.transform = 'scale(1)';\r\n      imageElement.classList.remove('zoomed');\r\n    }\r\n  }\r\n\r\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\r\n  // formatFileSize, downloadFile, toggleReaction, hasUserReacted, showQuickReactions\r\n  // toggleEmojiPicker, toggleAttachmentMenu, selectEmojiCategory, getEmojisForCategory, insertEmoji\r\n  // Ces méthodes sont déjà définies plus loin dans le fichier\r\n\r\n  triggerFileInput(type?: string): void {\r\n    const input = this.fileInput?.nativeElement;\r\n    if (!input) {\r\n      console.error('File input element not found');\r\n      return;\r\n    }\r\n\r\n    // Configurer le type de fichier accepté\r\n    if (type === 'image') {\r\n      input.accept = 'image/*';\r\n    } else if (type === 'video') {\r\n      input.accept = 'video/*';\r\n    } else if (type === 'document') {\r\n      input.accept = '.pdf,.doc,.docx,.xls,.xlsx,.txt';\r\n    } else {\r\n      input.accept = '*/*';\r\n    }\r\n\r\n    // Réinitialiser la valeur pour permettre la sélection du même fichier\r\n    input.value = '';\r\n\r\n    // Déclencher la sélection de fichier\r\n    input.click();\r\n    this.showAttachmentMenu = false;\r\n  }\r\n\r\n  formatMessageTime(timestamp: string | Date): string {\r\n    if (!timestamp) return '';\r\n\r\n    const date = new Date(timestamp);\r\n    return date.toLocaleTimeString('fr-FR', {\r\n      hour: '2-digit',\r\n      minute: '2-digit',\r\n    });\r\n  }\r\n\r\n  formatDateSeparator(timestamp: string | Date): string {\r\n    if (!timestamp) return '';\r\n\r\n    const date = new Date(timestamp);\r\n    const today = new Date();\r\n    const yesterday = new Date(today);\r\n    yesterday.setDate(yesterday.getDate() - 1);\r\n\r\n    if (date.toDateString() === today.toDateString()) {\r\n      return \"Aujourd'hui\";\r\n    } else if (date.toDateString() === yesterday.toDateString()) {\r\n      return 'Hier';\r\n    } else {\r\n      return date.toLocaleDateString('fr-FR');\r\n    }\r\n  }\r\n\r\n  formatMessageContent(content: string): string {\r\n    if (!content) return '';\r\n\r\n    // Remplacer les URLs par des liens\r\n    const urlRegex = /(https?:\\/\\/[^\\s]+)/g;\r\n    return content.replace(\r\n      urlRegex,\r\n      '<a href=\"$1\" target=\"_blank\" class=\"text-blue-500 underline\">$1</a>'\r\n    );\r\n  }\r\n\r\n  shouldShowDateSeparator(index: number): boolean {\r\n    if (index === 0) return true;\r\n\r\n    const currentMessage = this.messages[index];\r\n    const previousMessage = this.messages[index - 1];\r\n\r\n    if (!currentMessage?.timestamp || !previousMessage?.timestamp) return false;\r\n\r\n    const currentDate = new Date(currentMessage.timestamp).toDateString();\r\n    const previousDate = new Date(previousMessage.timestamp).toDateString();\r\n\r\n    return currentDate !== previousDate;\r\n  }\r\n\r\n  shouldShowAvatar(index: number): boolean {\r\n    const currentMessage = this.messages[index];\r\n    const nextMessage = this.messages[index + 1];\r\n\r\n    if (!nextMessage) return true;\r\n\r\n    return currentMessage.sender?.id !== nextMessage.sender?.id;\r\n  }\r\n\r\n  shouldShowSenderName(index: number): boolean {\r\n    const currentMessage = this.messages[index];\r\n    const previousMessage = this.messages[index - 1];\r\n\r\n    if (!previousMessage) return true;\r\n\r\n    return currentMessage.sender?.id !== previousMessage.sender?.id;\r\n  }\r\n\r\n  getMessageType(message: any): string {\r\n    // Vérifier d'abord le type de message explicite\r\n    if (message.type) {\r\n      if (message.type === 'IMAGE' || message.type === 'image') return 'image';\r\n      if (message.type === 'VIDEO' || message.type === 'video') return 'video';\r\n      if (message.type === 'AUDIO' || message.type === 'audio') return 'audio';\r\n      if (message.type === 'VOICE_MESSAGE') return 'audio';\r\n      if (message.type === 'FILE' || message.type === 'file') return 'file';\r\n    }\r\n\r\n    // Ensuite vérifier les attachments\r\n    if (message.attachments && message.attachments.length > 0) {\r\n      const attachment = message.attachments[0];\r\n      if (attachment.type?.startsWith('image/')) return 'image';\r\n      if (attachment.type?.startsWith('video/')) return 'video';\r\n      if (attachment.type?.startsWith('audio/')) return 'audio';\r\n      return 'file';\r\n    }\r\n\r\n    // Vérifier si c'est un message vocal basé sur les propriétés\r\n    if (message.voiceUrl || message.audioUrl || message.voice) return 'audio';\r\n\r\n    return 'text';\r\n  }\r\n\r\n  hasImage(message: any): boolean {\r\n    // Vérifier le type de message\r\n    if (message.type === 'IMAGE' || message.type === 'image') {\r\n      return true;\r\n    }\r\n\r\n    // Vérifier les attachments\r\n    const hasImageAttachment =\r\n      message.attachments?.some((att: any) => {\r\n        return att.type?.startsWith('image/') || att.type === 'IMAGE';\r\n      }) || false;\r\n\r\n    // Vérifier les propriétés directes d'image\r\n    const hasImageUrl = !!(message.imageUrl || message.image);\r\n\r\n    return hasImageAttachment || hasImageUrl;\r\n  }\r\n\r\n  hasFile(message: any): boolean {\r\n    // Vérifier le type de message\r\n    if (message.type === 'FILE' || message.type === 'file') {\r\n      return true;\r\n    }\r\n\r\n    // Vérifier les attachments non-image\r\n    const hasFileAttachment =\r\n      message.attachments?.some((att: any) => {\r\n        return !att.type?.startsWith('image/') && att.type !== 'IMAGE';\r\n      }) || false;\r\n\r\n    return hasFileAttachment;\r\n  }\r\n\r\n  getImageUrl(message: any): string {\r\n    // Vérifier les propriétés directes d'image\r\n    if (message.imageUrl) {\r\n      return message.imageUrl;\r\n    }\r\n    if (message.image) {\r\n      return message.image;\r\n    }\r\n\r\n    // Vérifier les attachments\r\n    const imageAttachment = message.attachments?.find(\r\n      (att: any) => att.type?.startsWith('image/') || att.type === 'IMAGE'\r\n    );\r\n\r\n    if (imageAttachment) {\r\n      return imageAttachment.url || imageAttachment.path || '';\r\n    }\r\n\r\n    return '';\r\n  }\r\n\r\n  getFileName(message: any): string {\r\n    const fileAttachment = message.attachments?.find(\r\n      (att: any) => !att.type?.startsWith('image/')\r\n    );\r\n    return fileAttachment?.name || 'Fichier';\r\n  }\r\n\r\n  getFileSize(message: any): string {\r\n    const fileAttachment = message.attachments?.find(\r\n      (att: any) => !att.type?.startsWith('image/')\r\n    );\r\n    if (!fileAttachment?.size) return '';\r\n\r\n    const bytes = fileAttachment.size;\r\n    if (bytes < 1024) return bytes + ' B';\r\n    if (bytes < 1048576) return Math.round(bytes / 1024) + ' KB';\r\n    return Math.round(bytes / 1048576) + ' MB';\r\n  }\r\n\r\n  getFileIcon(message: any): string {\r\n    const fileAttachment = message.attachments?.find(\r\n      (att: any) => !att.type?.startsWith('image/')\r\n    );\r\n    if (!fileAttachment?.type) return 'fas fa-file';\r\n\r\n    if (fileAttachment.type.startsWith('audio/')) return 'fas fa-file-audio';\r\n    if (fileAttachment.type.startsWith('video/')) return 'fas fa-file-video';\r\n    if (fileAttachment.type.includes('pdf')) return 'fas fa-file-pdf';\r\n    if (fileAttachment.type.includes('word')) return 'fas fa-file-word';\r\n    if (fileAttachment.type.includes('excel')) return 'fas fa-file-excel';\r\n    return 'fas fa-file';\r\n  }\r\n\r\n  getUserColor(userId: string): string {\r\n    // Générer une couleur basée sur l'ID utilisateur\r\n    const colors = [\r\n      '#FF6B6B',\r\n      '#4ECDC4',\r\n      '#45B7D1',\r\n      '#96CEB4',\r\n      '#FFEAA7',\r\n      '#DDA0DD',\r\n      '#98D8C8',\r\n    ];\r\n    const index = userId.charCodeAt(0) % colors.length;\r\n    return colors[index];\r\n  }\r\n\r\n  // === MÉTHODES D'INTERACTION ===\r\n  onMessageClick(message: any, event: any): void {}\r\n\r\n  onInputChange(event: any): void {\r\n    // Gérer les changements dans le champ de saisie\r\n    this.handleTypingIndicator();\r\n  }\r\n\r\n  onInputKeyDown(event: KeyboardEvent): void {\r\n    if (event.key === 'Enter' && !event.shiftKey) {\r\n      event.preventDefault();\r\n      this.sendMessage();\r\n    }\r\n  }\r\n\r\n  onInputFocus(): void {\r\n    // Gérer le focus sur le champ de saisie\r\n  }\r\n\r\n  onInputBlur(): void {\r\n    // Gérer la perte de focus sur le champ de saisie\r\n  }\r\n\r\n  onScroll(event: any): void {\r\n    // Gérer le scroll pour charger plus de messages\r\n    const element = event.target;\r\n    if (\r\n      element.scrollTop === 0 &&\r\n      this.hasMoreMessages &&\r\n      !this.isLoadingMore\r\n    ) {\r\n      this.loadMoreMessages();\r\n    }\r\n  }\r\n\r\n  openUserProfile(userId: string): void {}\r\n\r\n  onImageLoad(event: any, message: any): void {\r\n    console.log(\r\n      '🖼️ [Debug] Image loaded successfully for message:',\r\n      message.id,\r\n      event.target.src\r\n    );\r\n  }\r\n\r\n  onImageError(event: any, message: any): void {\r\n    console.error('🖼️ [Debug] Image failed to load for message:', message.id, {\r\n      src: event.target.src,\r\n      error: event,\r\n    });\r\n    // Optionnel : afficher une image de remplacement\r\n    event.target.src =\r\n      'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjNmNGY2Ii8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzZiNzI4MCIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkltYWdlIG5vbiBkaXNwb25pYmxlPC90ZXh0Pjwvc3ZnPg==';\r\n  }\r\n\r\n  openImageViewer(message: any): void {\r\n    const imageAttachment = message.attachments?.find((att: any) =>\r\n      att.type?.startsWith('image/')\r\n    );\r\n    if (imageAttachment?.url) {\r\n      this.selectedImage = {\r\n        url: imageAttachment.url,\r\n        name: imageAttachment.name || 'Image',\r\n        size: this.formatFileSize(imageAttachment.size || 0),\r\n        message: message,\r\n      };\r\n      this.showImageViewer = true;\r\n    }\r\n  }\r\n\r\n  closeImageViewer(): void {\r\n    this.showImageViewer = false;\r\n    this.selectedImage = null;\r\n  }\r\n\r\n  downloadImage(): void {\r\n    if (this.selectedImage?.url) {\r\n      const link = document.createElement('a');\r\n      link.href = this.selectedImage.url;\r\n      link.download = this.selectedImage.name || 'image';\r\n      link.target = '_blank';\r\n      document.body.appendChild(link);\r\n      link.click();\r\n      document.body.removeChild(link);\r\n      this.toastService.showSuccess('Téléchargement démarré');\r\n      console.log(\r\n        '🖼️ [ImageViewer] Download started:',\r\n        this.selectedImage.name\r\n      );\r\n    }\r\n  }\r\n\r\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\r\n  // zoomImage, resetZoom, formatFileSize, downloadFile, toggleReaction, hasUserReacted, toggleSearch\r\n  // Ces méthodes sont déjà définies plus loin dans le fichier\r\n\r\n  searchMessages(): void {\r\n    if (!this.searchQuery.trim()) {\r\n      this.searchResults = [];\r\n      return;\r\n    }\r\n\r\n    this.searchResults = this.messages.filter(\r\n      (message) =>\r\n        message.content\r\n          ?.toLowerCase()\r\n          .includes(this.searchQuery.toLowerCase()) ||\r\n        message.sender?.username\r\n          ?.toLowerCase()\r\n          .includes(this.searchQuery.toLowerCase())\r\n    );\r\n  }\r\n\r\n  onSearchQueryChange(): void {\r\n    this.searchMessages();\r\n  }\r\n\r\n  clearSearch(): void {\r\n    this.searchQuery = '';\r\n    this.searchResults = [];\r\n  }\r\n\r\n  jumpToMessage(messageId: string): void {\r\n    const messageElement = document.getElementById(`message-${messageId}`);\r\n    if (messageElement) {\r\n      messageElement.scrollIntoView({ behavior: 'smooth', block: 'center' });\r\n      // Highlight temporairement le message\r\n      messageElement.classList.add('highlight');\r\n      setTimeout(() => {\r\n        messageElement.classList.remove('highlight');\r\n      }, 2000);\r\n    }\r\n  }\r\n\r\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\r\n  // toggleMainMenu, toggleTheme, onMessageContextMenu\r\n  // Ces méthodes sont déjà définies plus loin dans le fichier\r\n\r\n  closeContextMenu(): void {\r\n    this.showMessageContextMenu = false;\r\n    this.selectedMessage = null;\r\n  }\r\n\r\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\r\n  // replyToMessage, forwardMessage, deleteMessage, showQuickReactions, closeReactionPicker\r\n  // quickReact, closeAllMenus, testAddMessage, toggleAttachmentMenu, toggleEmojiPicker\r\n  // Ces méthodes sont déjà définies plus loin dans le fichier\r\n\r\n  // === MÉTHODE SUPPRIMÉE (DUPLICATION) ===\r\n  // triggerFileInput - définie plus loin\r\n\r\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\r\n  // openCamera, getEmojisForCategory, selectEmojiCategory, insertEmoji\r\n  // goBackToConversations, startVideoCall, startVoiceCall\r\n  // Ces méthodes sont déjà définies plus loin dans le fichier\r\n\r\n  private initiateCall(callType: CallType): void {\r\n    console.log('📋 [MessageChat] Call details:', {\r\n      callType,\r\n      otherParticipant: this.otherParticipant,\r\n      conversation: this.conversation?.id,\r\n      currentUserId: this.currentUserId,\r\n    });\r\n\r\n    if (!this.otherParticipant) {\r\n      console.error('❌ [MessageChat] No recipient selected');\r\n      this.toastService.showError('Aucun destinataire sélectionné');\r\n      return;\r\n    }\r\n\r\n    const recipientId = this.otherParticipant.id || this.otherParticipant._id;\r\n    if (!recipientId) {\r\n      console.error('❌ [MessageChat] Recipient ID not found');\r\n      this.toastService.showError('ID du destinataire introuvable');\r\n      return;\r\n    }\r\n\r\n    console.log(`📞 [MessageChat] Initiating ${callType} call to user:`, {\r\n      recipientId,\r\n      recipientName:\r\n        this.otherParticipant.username || this.otherParticipant.name,\r\n      conversationId: this.conversation?.id,\r\n    });\r\n\r\n    this.isInCall = true;\r\n    this.callType = callType === CallType.VIDEO ? 'VIDEO' : 'AUDIO';\r\n    this.callDuration = 0;\r\n\r\n    // Démarrer le timer d'appel\r\n    this.startCallTimer();\r\n\r\n    // Utiliser le CallService\r\n    this.callService\r\n      .initiateCall(recipientId, callType, this.conversation?.id)\r\n      .subscribe({\r\n        next: (call: Call) => {\r\n          this.activeCall = call;\r\n          this.isCallConnected = false;\r\n          this.toastService.showSuccess(\r\n            `Appel ${callType === CallType.VIDEO ? 'vidéo' : 'audio'} initié`\r\n          );\r\n\r\n          console.log(\r\n            '📡 [MessageChat] Call should now be sent to recipient via WebSocket'\r\n          );\r\n        },\r\n        error: (error: any) => {\r\n          console.error('❌ [MessageChat] Error initiating call:', {\r\n            error: error.message || error,\r\n            recipientId,\r\n            callType,\r\n            conversationId: this.conversation?.id,\r\n          });\r\n\r\n          this.endCall();\r\n          this.toastService.showError(\"Erreur lors de l'initiation de l'appel\");\r\n        },\r\n      });\r\n  }\r\n\r\n  acceptCall(incomingCall: IncomingCall): void {\r\n    this.callService.acceptCall(incomingCall).subscribe({\r\n      next: (call: Call) => {\r\n        this.activeCall = call;\r\n        this.isInCall = true;\r\n        this.isCallConnected = true;\r\n        this.callType = call.type === CallType.VIDEO ? 'VIDEO' : 'AUDIO';\r\n        this.startCallTimer();\r\n        this.toastService.showSuccess('Appel accepté');\r\n      },\r\n      error: (error: any) => {\r\n        console.error('❌ Error accepting call:', error);\r\n        this.toastService.showError(\"Erreur lors de l'acceptation de l'appel\");\r\n      },\r\n    });\r\n  }\r\n\r\n  rejectCall(incomingCall: IncomingCall): void {\r\n    this.callService.rejectCall(incomingCall.id, 'User rejected').subscribe({\r\n      next: () => {\r\n        this.toastService.showSuccess('Appel rejeté');\r\n      },\r\n      error: (error) => {\r\n        console.error('❌ Error rejecting call:', error);\r\n        this.toastService.showError(\"Erreur lors du rejet de l'appel\");\r\n      },\r\n    });\r\n  }\r\n\r\n  private startCallTimer(): void {\r\n    this.callDuration = 0;\r\n    this.callTimer = setInterval(() => {\r\n      this.callDuration++;\r\n      this.cdr.detectChanges();\r\n    }, 1000);\r\n  }\r\n\r\n  private resetCallState(): void {\r\n    if (this.callTimer) {\r\n      clearInterval(this.callTimer);\r\n      this.callTimer = null;\r\n    }\r\n\r\n    this.isInCall = false;\r\n    this.callType = null;\r\n    this.callDuration = 0;\r\n    this.activeCall = null;\r\n    this.isCallConnected = false;\r\n    this.isMuted = false;\r\n    this.isVideoEnabled = true;\r\n  }\r\n\r\n  // === CONTRÔLES D'APPEL ===\r\n  toggleMute(): void {\r\n    if (!this.activeCall) return;\r\n\r\n    this.isMuted = !this.isMuted;\r\n\r\n    // Utiliser la méthode toggleMedia du CallService\r\n    this.callService\r\n      .toggleMedia(\r\n        this.activeCall.id,\r\n        undefined, // video unchanged\r\n        !this.isMuted // audio state\r\n      )\r\n      .subscribe({\r\n        next: () => {\r\n          this.toastService.showSuccess(\r\n            this.isMuted ? 'Micro coupé' : 'Micro activé'\r\n          );\r\n        },\r\n        error: (error) => {\r\n          console.error('❌ Error toggling mute:', error);\r\n          // Revert state on error\r\n          this.isMuted = !this.isMuted;\r\n          this.toastService.showError('Erreur lors du changement du micro');\r\n        },\r\n      });\r\n  }\r\n\r\n  toggleVideo(): void {\r\n    if (!this.activeCall) return;\r\n\r\n    this.isVideoEnabled = !this.isVideoEnabled;\r\n\r\n    // Utiliser la méthode toggleMedia du CallService\r\n    this.callService\r\n      .toggleMedia(\r\n        this.activeCall.id,\r\n        this.isVideoEnabled, // video state\r\n        undefined // audio unchanged\r\n      )\r\n      .subscribe({\r\n        next: () => {\r\n          this.toastService.showSuccess(\r\n            this.isVideoEnabled ? 'Caméra activée' : 'Caméra désactivée'\r\n          );\r\n        },\r\n        error: (error) => {\r\n          console.error('❌ Error toggling video:', error);\r\n          // Revert state on error\r\n          this.isVideoEnabled = !this.isVideoEnabled;\r\n          this.toastService.showError('Erreur lors du changement de la caméra');\r\n        },\r\n      });\r\n  }\r\n\r\n  formatCallDuration(duration: number): string {\r\n    const hours = Math.floor(duration / 3600);\r\n    const minutes = Math.floor((duration % 3600) / 60);\r\n    const seconds = duration % 60;\r\n\r\n    if (hours > 0) {\r\n      return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds\r\n        .toString()\r\n        .padStart(2, '0')}`;\r\n    }\r\n    return `${minutes}:${seconds.toString().padStart(2, '0')}`;\r\n  }\r\n\r\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\r\n  // trackByMessageId, isGroupConversation - Ces méthodes sont déjà définies plus loin dans le fichier\r\n\r\n  async startVoiceRecording(): Promise<void> {\r\n    try {\r\n      // Vérifier le support du navigateur\r\n      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {\r\n        throw new Error(\r\n          \"Votre navigateur ne supporte pas l'enregistrement audio\"\r\n        );\r\n      }\r\n\r\n      // Vérifier si MediaRecorder est supporté\r\n      if (!window.MediaRecorder) {\r\n        throw new Error(\r\n          \"MediaRecorder n'est pas supporté par votre navigateur\"\r\n        );\r\n      }\r\n\r\n      // Demander l'accès au microphone avec des contraintes optimisées\r\n      const stream = await navigator.mediaDevices.getUserMedia({\r\n        audio: {\r\n          echoCancellation: true,\r\n          noiseSuppression: true,\r\n          autoGainControl: true,\r\n          sampleRate: 44100,\r\n          channelCount: 1,\r\n        },\r\n      });\r\n\r\n      // Vérifier les types MIME supportés\r\n      let mimeType = 'audio/webm;codecs=opus';\r\n      if (!MediaRecorder.isTypeSupported(mimeType)) {\r\n        mimeType = 'audio/webm';\r\n        if (!MediaRecorder.isTypeSupported(mimeType)) {\r\n          mimeType = 'audio/mp4';\r\n          if (!MediaRecorder.isTypeSupported(mimeType)) {\r\n            mimeType = ''; // Laisser le navigateur choisir\r\n          }\r\n        }\r\n      }\r\n\r\n      // Créer le MediaRecorder\r\n      this.mediaRecorder = new MediaRecorder(stream, {\r\n        mimeType: mimeType || undefined,\r\n      });\r\n\r\n      // Initialiser les variables\r\n      this.audioChunks = [];\r\n      this.isRecordingVoice = true;\r\n      this.voiceRecordingDuration = 0;\r\n      this.voiceRecordingState = 'recording';\r\n\r\n      // Démarrer le timer\r\n      this.recordingTimer = setInterval(() => {\r\n        this.voiceRecordingDuration++;\r\n        // Animer les waves\r\n        this.animateVoiceWaves();\r\n        this.cdr.detectChanges();\r\n      }, 1000);\r\n\r\n      // Gérer les événements du MediaRecorder\r\n      this.mediaRecorder.ondataavailable = (event) => {\r\n        if (event.data.size > 0) {\r\n          this.audioChunks.push(event.data);\r\n        }\r\n      };\r\n\r\n      this.mediaRecorder.onstop = () => {\r\n        this.processRecordedAudio();\r\n      };\r\n\r\n      this.mediaRecorder.onerror = (event: any) => {\r\n        console.error('🎤 [Voice] MediaRecorder error:', event.error);\r\n        this.toastService.showError(\"Erreur lors de l'enregistrement\");\r\n        this.cancelVoiceRecording();\r\n      };\r\n\r\n      // Démarrer l'enregistrement\r\n      this.mediaRecorder.start(100); // Collecter les données toutes les 100ms\r\n\r\n      this.toastService.showSuccess('🎤 Enregistrement vocal démarré');\r\n    } catch (error: any) {\r\n      console.error('🎤 [Voice] Error starting recording:', error);\r\n\r\n      let errorMessage = \"Impossible de démarrer l'enregistrement vocal\";\r\n\r\n      if (error.name === 'NotAllowedError') {\r\n        errorMessage =\r\n          \"Accès au microphone refusé. Veuillez autoriser l'accès dans les paramètres de votre navigateur.\";\r\n      } else if (error.name === 'NotFoundError') {\r\n        errorMessage =\r\n          'Aucun microphone détecté. Veuillez connecter un microphone.';\r\n      } else if (error.name === 'NotSupportedError') {\r\n        errorMessage =\r\n          \"Votre navigateur ne supporte pas l'enregistrement audio.\";\r\n      } else if (error.message) {\r\n        errorMessage = error.message;\r\n      }\r\n\r\n      this.toastService.showError(errorMessage);\r\n      this.cancelVoiceRecording();\r\n    }\r\n  }\r\n\r\n  stopVoiceRecording(): void {\r\n    if (this.mediaRecorder && this.mediaRecorder.state === 'recording') {\r\n      this.mediaRecorder.stop();\r\n      this.mediaRecorder.stream.getTracks().forEach((track) => track.stop());\r\n    }\r\n\r\n    if (this.recordingTimer) {\r\n      clearInterval(this.recordingTimer);\r\n      this.recordingTimer = null;\r\n    }\r\n\r\n    this.isRecordingVoice = false;\r\n    this.voiceRecordingState = 'processing';\r\n  }\r\n\r\n  cancelVoiceRecording(): void {\r\n    if (this.mediaRecorder) {\r\n      if (this.mediaRecorder.state === 'recording') {\r\n        this.mediaRecorder.stop();\r\n      }\r\n      this.mediaRecorder.stream.getTracks().forEach((track) => track.stop());\r\n      this.mediaRecorder = null;\r\n    }\r\n\r\n    if (this.recordingTimer) {\r\n      clearInterval(this.recordingTimer);\r\n      this.recordingTimer = null;\r\n    }\r\n\r\n    this.isRecordingVoice = false;\r\n    this.voiceRecordingDuration = 0;\r\n    this.voiceRecordingState = 'idle';\r\n    this.audioChunks = [];\r\n  }\r\n\r\n  private async processRecordedAudio(): Promise<void> {\r\n    try {\r\n      // Vérifier qu'on a des données audio\r\n      if (this.audioChunks.length === 0) {\r\n        console.error('🎤 [Voice] No audio chunks available');\r\n        this.toastService.showError('Aucun audio enregistré');\r\n        this.cancelVoiceRecording();\r\n        return;\r\n      }\r\n\r\n      console.log(\r\n        '🎤 [Voice] Audio chunks:',\r\n        this.audioChunks.length,\r\n        'Duration:',\r\n        this.voiceRecordingDuration\r\n      );\r\n\r\n      // Vérifier la durée minimale\r\n      if (this.voiceRecordingDuration < 1) {\r\n        console.error(\r\n          '🎤 [Voice] Recording too short:',\r\n          this.voiceRecordingDuration\r\n        );\r\n        this.toastService.showError(\r\n          'Enregistrement trop court (minimum 1 seconde)'\r\n        );\r\n        this.cancelVoiceRecording();\r\n        return;\r\n      }\r\n\r\n      // Déterminer le type MIME du blob\r\n      let mimeType = 'audio/webm;codecs=opus';\r\n      if (this.mediaRecorder?.mimeType) {\r\n        mimeType = this.mediaRecorder.mimeType;\r\n      }\r\n\r\n      // Créer le blob audio\r\n      const audioBlob = new Blob(this.audioChunks, {\r\n        type: mimeType,\r\n      });\r\n\r\n      console.log('🎤 [Voice] Audio blob created:', {\r\n        size: audioBlob.size,\r\n        type: audioBlob.type,\r\n      });\r\n\r\n      // Déterminer l'extension du fichier\r\n      let extension = '.webm';\r\n      if (mimeType.includes('mp4')) {\r\n        extension = '.mp4';\r\n      } else if (mimeType.includes('wav')) {\r\n        extension = '.wav';\r\n      } else if (mimeType.includes('ogg')) {\r\n        extension = '.ogg';\r\n      }\r\n\r\n      // Créer le fichier\r\n      const audioFile = new File(\r\n        [audioBlob],\r\n        `voice_${Date.now()}${extension}`,\r\n        {\r\n          type: mimeType,\r\n        }\r\n      );\r\n\r\n      console.log('🎤 [Voice] Audio file created:', {\r\n        name: audioFile.name,\r\n        size: audioFile.size,\r\n        type: audioFile.type,\r\n      });\r\n\r\n      // Envoyer le message vocal\r\n      this.voiceRecordingState = 'processing';\r\n      await this.sendVoiceMessage(audioFile);\r\n\r\n      this.toastService.showSuccess('🎤 Message vocal envoyé');\r\n    } catch (error: any) {\r\n      console.error('🎤 [Voice] Error processing audio:', error);\r\n      this.toastService.showError(\r\n        \"Erreur lors de l'envoi du message vocal: \" +\r\n          (error.message || 'Erreur inconnue')\r\n      );\r\n    } finally {\r\n      // Nettoyer l'état\r\n      this.voiceRecordingState = 'idle';\r\n      this.voiceRecordingDuration = 0;\r\n      this.audioChunks = [];\r\n      this.isRecordingVoice = false;\r\n    }\r\n  }\r\n\r\n  private async sendVoiceMessage(audioFile: File): Promise<void> {\r\n    const receiverId = this.otherParticipant?.id || this.otherParticipant?._id;\r\n\r\n    if (!receiverId) {\r\n      throw new Error('Destinataire introuvable');\r\n    }\r\n\r\n    return new Promise((resolve, reject) => {\r\n      this.MessageService.sendMessage(\r\n        receiverId,\r\n        '',\r\n        audioFile,\r\n        'AUDIO' as any,\r\n        this.conversation.id\r\n      ).subscribe({\r\n        next: (message: any) => {\r\n          this.messages.push(message);\r\n          this.scrollToBottom();\r\n          resolve();\r\n        },\r\n        error: (error: any) => {\r\n          console.error(\"Erreur lors de l'envoi du message vocal:\", error);\r\n          reject(error);\r\n        },\r\n      });\r\n    });\r\n  }\r\n\r\n  formatRecordingDuration(duration: number): string {\r\n    const minutes = Math.floor(duration / 60);\r\n    const seconds = duration % 60;\r\n    return `${minutes}:${seconds.toString().padStart(2, '0')}`;\r\n  }\r\n\r\n  // === MÉTHODES D'ENREGISTREMENT VOCAL AMÉLIORÉES ===\r\n\r\n  onRecordStart(event: Event): void {\r\n    event.preventDefault();\r\n\r\n    console.log('🎤 [Voice] Current state:', {\r\n      isRecordingVoice: this.isRecordingVoice,\r\n      voiceRecordingState: this.voiceRecordingState,\r\n      voiceRecordingDuration: this.voiceRecordingDuration,\r\n      mediaRecorder: !!this.mediaRecorder,\r\n    });\r\n\r\n    // Vérifier si on peut enregistrer\r\n    if (this.voiceRecordingState === 'processing') {\r\n      this.toastService.showWarning('Traitement en cours...');\r\n      return;\r\n    }\r\n\r\n    if (this.isRecordingVoice) {\r\n      this.toastService.showWarning('Enregistrement déjà en cours...');\r\n      return;\r\n    }\r\n\r\n    // Afficher un message de début\r\n    this.toastService.showInfo(\"🎤 Démarrage de l'enregistrement vocal...\");\r\n\r\n    // Démarrer l'enregistrement\r\n    this.startVoiceRecording().catch((error) => {\r\n      console.error('🎤 [Voice] Failed to start recording:', error);\r\n      this.toastService.showError(\r\n        \"Impossible de démarrer l'enregistrement vocal: \" +\r\n          (error.message || 'Erreur inconnue')\r\n      );\r\n    });\r\n  }\r\n\r\n  onRecordEnd(event: Event): void {\r\n    event.preventDefault();\r\n\r\n    if (!this.isRecordingVoice) {\r\n      return;\r\n    }\r\n\r\n    // Arrêter l'enregistrement et envoyer\r\n    this.stopVoiceRecording();\r\n  }\r\n\r\n  onRecordCancel(event: Event): void {\r\n    event.preventDefault();\r\n\r\n    if (!this.isRecordingVoice) {\r\n      return;\r\n    }\r\n\r\n    // Annuler l'enregistrement\r\n    this.cancelVoiceRecording();\r\n  }\r\n\r\n  getRecordingFormat(): string {\r\n    if (this.mediaRecorder?.mimeType) {\r\n      if (this.mediaRecorder.mimeType.includes('webm')) return 'WebM';\r\n      if (this.mediaRecorder.mimeType.includes('mp4')) return 'MP4';\r\n      if (this.mediaRecorder.mimeType.includes('wav')) return 'WAV';\r\n      if (this.mediaRecorder.mimeType.includes('ogg')) return 'OGG';\r\n    }\r\n    return 'Auto';\r\n  }\r\n\r\n  // === ANIMATION DES WAVES VOCALES ===\r\n\r\n  private animateVoiceWaves(): void {\r\n    // Animer les waves pendant l'enregistrement\r\n    this.voiceWaves = this.voiceWaves.map(() => {\r\n      return Math.floor(Math.random() * 20) + 4; // Hauteur entre 4 et 24px\r\n    });\r\n  }\r\n\r\n  onFileSelected(event: any): void {\r\n    const files = event.target.files;\r\n\r\n    if (!files || files.length === 0) {\r\n      return;\r\n    }\r\n\r\n    for (let file of files) {\r\n      console.log(\r\n        `📁 [Upload] Processing file: ${file.name}, size: ${file.size}, type: ${file.type}`\r\n      );\r\n      this.uploadFile(file);\r\n    }\r\n  }\r\n\r\n  private uploadFile(file: File): void {\r\n    const receiverId = this.otherParticipant?.id || this.otherParticipant?._id;\r\n\r\n    if (!receiverId) {\r\n      console.error('📁 [Upload] No receiver ID found');\r\n      this.toastService.showError('Destinataire introuvable');\r\n      return;\r\n    }\r\n\r\n    // Vérifier la taille du fichier (max 50MB)\r\n    const maxSize = 50 * 1024 * 1024; // 50MB\r\n    if (file.size > maxSize) {\r\n      console.error(`📁 [Upload] File too large: ${file.size} bytes`);\r\n      this.toastService.showError('Fichier trop volumineux (max 50MB)');\r\n      return;\r\n    }\r\n\r\n    // 🖼️ Compression d'image si nécessaire\r\n    if (file.type.startsWith('image/') && file.size > 1024 * 1024) {\r\n      // > 1MB\r\n      console.log(\r\n        '🖼️ [Compression] Compressing image:',\r\n        file.name,\r\n        'Original size:',\r\n        file.size\r\n      );\r\n      this.compressImage(file)\r\n        .then((compressedFile) => {\r\n          console.log(\r\n            '🖼️ [Compression] ✅ Image compressed successfully. New size:',\r\n            compressedFile.size\r\n          );\r\n          this.sendFileToServer(compressedFile, receiverId);\r\n        })\r\n        .catch((error) => {\r\n          console.error('🖼️ [Compression] ❌ Error compressing image:', error);\r\n          // Envoyer le fichier original en cas d'erreur\r\n          this.sendFileToServer(file, receiverId);\r\n        });\r\n      return;\r\n    }\r\n\r\n    // Envoyer le fichier sans compression\r\n    this.sendFileToServer(file, receiverId);\r\n  }\r\n\r\n  private sendFileToServer(file: File, receiverId: string): void {\r\n    const messageType = this.getFileMessageType(file);\r\n\r\n    this.isSendingMessage = true;\r\n    this.isUploading = true;\r\n    this.uploadProgress = 0;\r\n\r\n    // Simuler la progression d'upload\r\n    const progressInterval = setInterval(() => {\r\n      this.uploadProgress += Math.random() * 15;\r\n      if (this.uploadProgress >= 90) {\r\n        clearInterval(progressInterval);\r\n      }\r\n      this.cdr.detectChanges();\r\n    }, 300);\r\n\r\n    this.MessageService.sendMessage(\r\n      receiverId,\r\n      '',\r\n      file,\r\n      messageType,\r\n      this.conversation.id\r\n    ).subscribe({\r\n      next: (message: any) => {\r\n        console.log('📁 [Debug] Sent message structure:', {\r\n          id: message.id,\r\n          type: message.type,\r\n          attachments: message.attachments,\r\n          hasImage: this.hasImage(message),\r\n          hasFile: this.hasFile(message),\r\n          imageUrl: this.getImageUrl(message),\r\n        });\r\n\r\n        clearInterval(progressInterval);\r\n        this.uploadProgress = 100;\r\n\r\n        setTimeout(() => {\r\n          this.messages.push(message);\r\n          this.scrollToBottom();\r\n          this.toastService.showSuccess('Fichier envoyé avec succès');\r\n          this.resetUploadState();\r\n        }, 500);\r\n      },\r\n      error: (error: any) => {\r\n        console.error('📁 [Upload] ❌ Error sending file:', error);\r\n        clearInterval(progressInterval);\r\n        this.toastService.showError(\"Erreur lors de l'envoi du fichier\");\r\n        this.resetUploadState();\r\n      },\r\n    });\r\n  }\r\n\r\n  private getFileMessageType(file: File): any {\r\n    if (file.type.startsWith('image/')) return 'IMAGE' as any;\r\n    if (file.type.startsWith('video/')) return 'VIDEO' as any;\r\n    if (file.type.startsWith('audio/')) return 'AUDIO' as any;\r\n    return 'FILE' as any;\r\n  }\r\n\r\n  getFileAcceptTypes(): string {\r\n    return '*/*';\r\n  }\r\n\r\n  resetUploadState(): void {\r\n    this.isSendingMessage = false;\r\n    this.isUploading = false;\r\n    this.uploadProgress = 0;\r\n  }\r\n\r\n  // === DRAG & DROP ===\r\n\r\n  onDragOver(event: DragEvent): void {\r\n    event.preventDefault();\r\n    event.stopPropagation();\r\n    this.isDragOver = true;\r\n  }\r\n\r\n  onDragLeave(event: DragEvent): void {\r\n    event.preventDefault();\r\n    event.stopPropagation();\r\n    // Vérifier si on quitte vraiment la zone (pas un enfant)\r\n    const rect = (event.currentTarget as HTMLElement).getBoundingClientRect();\r\n    const x = event.clientX;\r\n    const y = event.clientY;\r\n\r\n    if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {\r\n      this.isDragOver = false;\r\n    }\r\n  }\r\n\r\n  onDrop(event: DragEvent): void {\r\n    event.preventDefault();\r\n    event.stopPropagation();\r\n    this.isDragOver = false;\r\n\r\n    const files = event.dataTransfer?.files;\r\n    if (files && files.length > 0) {\r\n      // Traiter chaque fichier\r\n      Array.from(files).forEach((file) => {\r\n        console.log(\r\n          '📁 [Drag&Drop] Processing file:',\r\n          file.name,\r\n          file.type,\r\n          file.size\r\n        );\r\n        this.uploadFile(file);\r\n      });\r\n\r\n      this.toastService.showSuccess(\r\n        `${files.length} fichier(s) en cours d'envoi`\r\n      );\r\n    }\r\n  }\r\n\r\n  // === COMPRESSION D'IMAGES ===\r\n\r\n  private compressImage(file: File, quality: number = 0.8): Promise<File> {\r\n    return new Promise((resolve, reject) => {\r\n      const canvas = document.createElement('canvas');\r\n      const ctx = canvas.getContext('2d');\r\n      const img = new Image();\r\n\r\n      img.onload = () => {\r\n        // Calculer les nouvelles dimensions (max 1920x1080)\r\n        const maxWidth = 1920;\r\n        const maxHeight = 1080;\r\n        let { width, height } = img;\r\n\r\n        if (width > maxWidth || height > maxHeight) {\r\n          const ratio = Math.min(maxWidth / width, maxHeight / height);\r\n          width *= ratio;\r\n          height *= ratio;\r\n        }\r\n\r\n        canvas.width = width;\r\n        canvas.height = height;\r\n\r\n        // Dessiner l'image redimensionnée\r\n        ctx?.drawImage(img, 0, 0, width, height);\r\n\r\n        // Convertir en blob avec compression\r\n        canvas.toBlob(\r\n          (blob) => {\r\n            if (blob) {\r\n              const compressedFile = new File([blob], file.name, {\r\n                type: file.type,\r\n                lastModified: Date.now(),\r\n              });\r\n              resolve(compressedFile);\r\n            } else {\r\n              reject(new Error('Failed to compress image'));\r\n            }\r\n          },\r\n          file.type,\r\n          quality\r\n        );\r\n      };\r\n\r\n      img.onerror = () => reject(new Error('Failed to load image'));\r\n      img.src = URL.createObjectURL(file);\r\n    });\r\n  }\r\n\r\n  // === MÉTHODES DE FORMATAGE SUPPLÉMENTAIRES ===\r\n\r\n  private handleTypingIndicator(): void {\r\n    if (!this.isTyping) {\r\n      this.isTyping = true;\r\n      // Envoyer l'indicateur de frappe à l'autre utilisateur\r\n      this.sendTypingIndicator(true);\r\n    }\r\n\r\n    // Reset le timer\r\n    if (this.typingTimeout) {\r\n      clearTimeout(this.typingTimeout);\r\n    }\r\n\r\n    this.typingTimeout = setTimeout(() => {\r\n      this.isTyping = false;\r\n      // Arrêter l'indicateur de frappe\r\n      this.sendTypingIndicator(false);\r\n    }, 2000);\r\n  }\r\n\r\n  private sendTypingIndicator(isTyping: boolean): void {\r\n    // Envoyer l'indicateur de frappe via WebSocket/GraphQL\r\n    const receiverId = this.otherParticipant?.id || this.otherParticipant?._id;\r\n    if (receiverId && this.conversation?.id) {\r\n      console.log(\r\n        `📝 Sending typing indicator: ${isTyping} to user ${receiverId}`\r\n      );\r\n      // TODO: Implémenter l'envoi via WebSocket/GraphQL subscription\r\n      // this.MessageService.sendTypingIndicator(this.conversation.id, receiverId, isTyping);\r\n    }\r\n  }\r\n\r\n  // === MÉTHODES POUR L'INTERFACE D'APPEL ===\r\n\r\n  onCallAccepted(call: Call): void {\r\n    this.activeCall = call;\r\n    this.isInCall = true;\r\n    this.isCallConnected = true;\r\n    this.startCallTimer();\r\n    this.toastService.showSuccess('Appel accepté');\r\n  }\r\n\r\n  onCallRejected(): void {\r\n    this.endCall();\r\n    this.toastService.showInfo('Appel rejeté');\r\n  }\r\n\r\n  // === MÉTHODES POUR LA LECTURE DES MESSAGES VOCAUX ===\r\n\r\n  playVoiceMessage(message: any): void {\r\n    this.toggleVoicePlayback(message);\r\n  }\r\n\r\n  isVoicePlaying(messageId: string): boolean {\r\n    return this.playingMessageId === messageId;\r\n  }\r\n\r\n  toggleVoicePlayback(message: any): void {\r\n    const messageId = message.id;\r\n    const audioUrl = this.getVoiceUrl(message);\r\n\r\n    if (!audioUrl) {\r\n      console.error('🎵 [Voice] No audio URL found for message:', messageId);\r\n      this.toastService.showError('Fichier audio introuvable');\r\n      return;\r\n    }\r\n\r\n    // Si c'est déjà en cours de lecture, arrêter\r\n    if (this.isVoicePlaying(messageId)) {\r\n      this.stopVoicePlayback();\r\n      return;\r\n    }\r\n\r\n    // Arrêter toute autre lecture en cours\r\n    this.stopVoicePlayback();\r\n\r\n    // Démarrer la nouvelle lecture\r\n    this.startVoicePlayback(message, audioUrl);\r\n  }\r\n\r\n  private startVoicePlayback(message: any, audioUrl: string): void {\r\n    const messageId = message.id;\r\n\r\n    try {\r\n      console.log(\r\n        '🎵 [Voice] Starting playback for:',\r\n        messageId,\r\n        'URL:',\r\n        audioUrl\r\n      );\r\n\r\n      this.currentAudio = new Audio(audioUrl);\r\n      this.playingMessageId = messageId;\r\n\r\n      // Initialiser les valeurs par défaut avec la nouvelle structure\r\n      const currentData = this.getVoicePlaybackData(messageId);\r\n      this.setVoicePlaybackData(messageId, {\r\n        progress: 0,\r\n        currentTime: 0,\r\n        speed: currentData.speed || 1,\r\n        duration: currentData.duration || 0,\r\n      });\r\n\r\n      // Configurer la vitesse de lecture\r\n      this.currentAudio.playbackRate = currentData.speed || 1;\r\n\r\n      // Événements audio\r\n      this.currentAudio.addEventListener('loadedmetadata', () => {\r\n        if (this.currentAudio) {\r\n          this.setVoicePlaybackData(messageId, {\r\n            duration: this.currentAudio.duration,\r\n          });\r\n          console.log(\r\n            '🎵 [Voice] Audio loaded, duration:',\r\n            this.currentAudio.duration\r\n          );\r\n        }\r\n      });\r\n\r\n      this.currentAudio.addEventListener('timeupdate', () => {\r\n        if (this.currentAudio && this.playingMessageId === messageId) {\r\n          const currentTime = this.currentAudio.currentTime;\r\n          const progress = (currentTime / this.currentAudio.duration) * 100;\r\n          this.setVoicePlaybackData(messageId, { currentTime, progress });\r\n          this.cdr.detectChanges();\r\n        }\r\n      });\r\n\r\n      this.currentAudio.addEventListener('ended', () => {\r\n        this.stopVoicePlayback();\r\n      });\r\n\r\n      this.currentAudio.addEventListener('error', (error) => {\r\n        console.error('🎵 [Voice] Audio error:', error);\r\n        this.toastService.showError('Erreur lors de la lecture audio');\r\n        this.stopVoicePlayback();\r\n      });\r\n\r\n      // Démarrer la lecture\r\n      this.currentAudio\r\n        .play()\r\n        .then(() => {\r\n          this.toastService.showSuccess('🎵 Lecture du message vocal');\r\n        })\r\n        .catch((error) => {\r\n          console.error('🎵 [Voice] Error starting playback:', error);\r\n          this.toastService.showError('Impossible de lire le message vocal');\r\n          this.stopVoicePlayback();\r\n        });\r\n    } catch (error) {\r\n      console.error('🎵 [Voice] Error creating audio:', error);\r\n      this.toastService.showError('Erreur lors de la lecture audio');\r\n      this.stopVoicePlayback();\r\n    }\r\n  }\r\n\r\n  private stopVoicePlayback(): void {\r\n    if (this.currentAudio) {\r\n      this.currentAudio.pause();\r\n      this.currentAudio.currentTime = 0;\r\n      this.currentAudio = null;\r\n    }\r\n    this.playingMessageId = null;\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  getVoiceUrl(message: any): string {\r\n    // Vérifier les propriétés directes d'audio\r\n    if (message.voiceUrl) return message.voiceUrl;\r\n    if (message.audioUrl) return message.audioUrl;\r\n    if (message.voice) return message.voice;\r\n\r\n    // Vérifier les attachments audio\r\n    const audioAttachment = message.attachments?.find(\r\n      (att: any) => att.type?.startsWith('audio/') || att.type === 'AUDIO'\r\n    );\r\n\r\n    if (audioAttachment) {\r\n      return audioAttachment.url || audioAttachment.path || '';\r\n    }\r\n\r\n    return '';\r\n  }\r\n\r\n  getVoiceWaves(message: any): number[] {\r\n    // Générer des waves basées sur l'ID du message pour la cohérence\r\n    const messageId = message.id || '';\r\n    const seed = messageId\r\n      .split('')\r\n      .reduce((acc: number, char: string) => acc + char.charCodeAt(0), 0);\r\n    const waves: number[] = [];\r\n\r\n    for (let i = 0; i < 16; i++) {\r\n      const height = 4 + ((seed + i * 7) % 20);\r\n      waves.push(height);\r\n    }\r\n\r\n    return waves;\r\n  }\r\n\r\n  getVoiceProgress(message: any): number {\r\n    const data = this.getVoicePlaybackData(message.id);\r\n    const totalWaves = 16;\r\n    return Math.floor((data.progress / 100) * totalWaves);\r\n  }\r\n\r\n  getVoiceCurrentTime(message: any): string {\r\n    const data = this.getVoicePlaybackData(message.id);\r\n    return this.formatAudioTime(data.currentTime);\r\n  }\r\n\r\n  getVoiceDuration(message: any): string {\r\n    const data = this.getVoicePlaybackData(message.id);\r\n    const duration = data.duration || message.metadata?.duration || 0;\r\n\r\n    if (typeof duration === 'string') {\r\n      return duration; // Déjà formaté\r\n    }\r\n\r\n    return this.formatAudioTime(duration);\r\n  }\r\n\r\n  private formatAudioTime(seconds: number): string {\r\n    const minutes = Math.floor(seconds / 60);\r\n    const remainingSeconds = Math.floor(seconds % 60);\r\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\r\n  }\r\n\r\n  seekVoiceMessage(message: any, waveIndex: number): void {\r\n    const messageId = message.id;\r\n\r\n    if (!this.currentAudio || this.playingMessageId !== messageId) {\r\n      return;\r\n    }\r\n\r\n    const totalWaves = 16;\r\n    const seekPercentage = (waveIndex / totalWaves) * 100;\r\n    const seekTime = (seekPercentage / 100) * this.currentAudio.duration;\r\n\r\n    this.currentAudio.currentTime = seekTime;\r\n  }\r\n\r\n  toggleVoiceSpeed(message: any): void {\r\n    const messageId = message.id;\r\n    const data = this.getVoicePlaybackData(messageId);\r\n\r\n    // Cycle entre 1x, 1.5x, 2x\r\n    const newSpeed = data.speed === 1 ? 1.5 : data.speed === 1.5 ? 2 : 1;\r\n\r\n    this.setVoicePlaybackData(messageId, { speed: newSpeed });\r\n\r\n    if (this.currentAudio && this.playingMessageId === messageId) {\r\n      this.currentAudio.playbackRate = newSpeed;\r\n    }\r\n\r\n    this.toastService.showSuccess(`Vitesse: ${newSpeed}x`);\r\n  }\r\n\r\n  // === MÉTHODES MANQUANTES POUR LE TEMPLATE ===\r\n\r\n  changeVoiceSpeed(message: any): void {\r\n    this.toggleVoiceSpeed(message);\r\n  }\r\n\r\n  getVoiceSpeed(message: any): number {\r\n    const data = this.getVoicePlaybackData(message.id);\r\n    return data.speed || 1;\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.subscriptions.unsubscribe();\r\n\r\n    // Nettoyer les timers\r\n    if (this.callTimer) {\r\n      clearInterval(this.callTimer);\r\n    }\r\n    if (this.recordingTimer) {\r\n      clearInterval(this.recordingTimer);\r\n    }\r\n    if (this.typingTimeout) {\r\n      clearTimeout(this.typingTimeout);\r\n    }\r\n\r\n    // Nettoyer les ressources audio\r\n    if (this.mediaRecorder) {\r\n      if (this.mediaRecorder.state === 'recording') {\r\n        this.mediaRecorder.stop();\r\n      }\r\n      this.mediaRecorder.stream?.getTracks().forEach((track) => track.stop());\r\n    }\r\n\r\n    // Nettoyer la lecture audio\r\n    this.stopVoicePlayback();\r\n  }\r\n}\r\n", "<!-- ===== MESSAGE CHAT COMPONENT - REORGANIZED & OPTIMIZED ===== -->\n\n<!-- Éléments vidéo pour WebRTC - cachés visuellement mais audio actif -->\n<video\n  #localVideo\n  autoplay\n  muted\n  playsinline\n  style=\"\n    position: absolute;\n    top: -9999px;\n    left: -9999px;\n    width: 1px;\n    height: 1px;\n  \"\n></video>\n<video\n  #remoteVideo\n  autoplay\n  playsinline\n  style=\"\n    position: absolute;\n    top: -9999px;\n    left: -9999px;\n    width: 1px;\n    height: 1px;\n  \"\n></video>\n\n<div\n  style=\"\n    display: flex;\n    flex-direction: column;\n    height: 100vh;\n    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);\n    color: #1f2937;\n    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;\n  \"\n>\n  <!-- ===== ANIMATIONS CSS ===== -->\n  <style>\n    @keyframes pulse {\n      0%,\n      100% {\n        opacity: 1;\n      }\n      50% {\n        opacity: 0.5;\n      }\n    }\n    @keyframes bounce {\n      0%,\n      20%,\n      53%,\n      80%,\n      100% {\n        transform: translateY(0);\n      }\n      40%,\n      43% {\n        transform: translateY(-8px);\n      }\n      70% {\n        transform: translateY(-4px);\n      }\n    }\n    @keyframes spin {\n      from {\n        transform: rotate(0deg);\n      }\n      to {\n        transform: rotate(360deg);\n      }\n    }\n    @keyframes ping {\n      75%,\n      100% {\n        transform: scale(2);\n        opacity: 0;\n      }\n    }\n    @keyframes slideInUp {\n      from {\n        transform: translateX(-50%) translateY(20px);\n        opacity: 0;\n      }\n      to {\n        transform: translateX(-50%) translateY(0);\n        opacity: 1;\n      }\n    }\n  </style>\n\n  <!-- ===== HEADER SECTION ===== -->\n  <header\n    style=\"\n      display: flex;\n      align-items: center;\n      padding: 12px 16px;\n      background: #ffffff;\n      border-bottom: 1px solid #e5e7eb;\n      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n      position: relative;\n      z-index: 10;\n    \"\n  >\n    <!-- Bouton retour -->\n    <button\n      (click)=\"goBackToConversations()\"\n      style=\"\n        padding: 10px;\n        margin-right: 12px;\n        border-radius: 50%;\n        border: none;\n        background: transparent;\n        cursor: pointer;\n        transition: all 0.2s ease;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        min-width: 40px;\n        min-height: 40px;\n      \"\n      onmouseover=\"this.style.background='#f3f4f6'; this.style.transform='scale(1.05)'\"\n      onmouseout=\"this.style.background='transparent'; this.style.transform='scale(1)'\"\n      title=\"Retour aux conversations\"\n    >\n      <i\n        class=\"fas fa-arrow-left\"\n        style=\"color: #374151; font-size: 18px; font-weight: bold\"\n      ></i>\n    </button>\n\n    <!-- Info utilisateur -->\n    <div style=\"display: flex; align-items: center; flex: 1; min-width: 0\">\n      <!-- Avatar avec statut -->\n      <div style=\"position: relative; margin-right: 12px\">\n        <img\n          [src]=\"otherParticipant?.image || 'assets/images/default-avatar.png'\"\n          [alt]=\"otherParticipant?.username\"\n          style=\"\n            width: 40px;\n            height: 40px;\n            border-radius: 50%;\n            object-fit: cover;\n            border: 2px solid transparent;\n            cursor: pointer;\n            transition: transform 0.2s ease;\n          \"\n          (click)=\"openUserProfile(otherParticipant?.id!)\"\n          onmouseover=\"this.style.transform='scale(1.05)'\"\n          onmouseout=\"this.style.transform='scale(1)'\"\n          title=\"Voir le profil\"\n        />\n        <!-- Indicateur en ligne -->\n        <div\n          *ngIf=\"otherParticipant?.isOnline\"\n          style=\"\n            position: absolute;\n            bottom: 0;\n            right: 0;\n            width: 12px;\n            height: 12px;\n            background: #10b981;\n            border: 2px solid transparent;\n            border-radius: 50%;\n            animation: pulse 2s infinite;\n          \"\n        ></div>\n      </div>\n\n      <!-- Nom et statut -->\n      <div style=\"flex: 1; min-width: 0\">\n        <h3\n          style=\"\n            font-weight: 600;\n            color: #111827;\n            margin: 0;\n            font-size: 16px;\n            white-space: nowrap;\n            overflow: hidden;\n            text-overflow: ellipsis;\n          \"\n        >\n          {{ otherParticipant?.username || \"Utilisateur\" }}\n        </h3>\n        <div style=\"font-size: 14px; color: #6b7280; margin-top: 2px\">\n          <!-- Indicateur de frappe -->\n          <div\n            *ngIf=\"isUserTyping\"\n            style=\"display: flex; align-items: center; gap: 4px; color: #10b981\"\n          >\n            <span>En train d'écrire</span>\n            <div style=\"display: flex; gap: 2px\">\n              <div\n                style=\"\n                  width: 4px;\n                  height: 4px;\n                  background: #10b981;\n                  border-radius: 50%;\n                  animation: bounce 1s infinite;\n                \"\n              ></div>\n              <div\n                style=\"\n                  width: 4px;\n                  height: 4px;\n                  background: #10b981;\n                  border-radius: 50%;\n                  animation: bounce 1s infinite 0.1s;\n                \"\n              ></div>\n              <div\n                style=\"\n                  width: 4px;\n                  height: 4px;\n                  background: #10b981;\n                  border-radius: 50%;\n                  animation: bounce 1s infinite 0.2s;\n                \"\n              ></div>\n            </div>\n          </div>\n          <!-- Statut en ligne -->\n          <span *ngIf=\"!isUserTyping\">\n            {{\n              otherParticipant?.isOnline\n                ? \"En ligne\"\n                : formatLastActive(otherParticipant?.lastActive)\n            }}\n          </span>\n        </div>\n      </div>\n    </div>\n\n    <!-- Actions -->\n    <div style=\"display: flex; align-items: center; gap: 8px\">\n      <!-- Appel vidéo -->\n      <button\n        (click)=\"startVideoCall()\"\n        style=\"\n          padding: 10px;\n          border-radius: 50%;\n          border: none;\n          background: linear-gradient(135deg, #3b82f6, #1d4ed8);\n          color: white;\n          cursor: pointer;\n          transition: all 0.3s;\n          box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);\n          width: 40px;\n          height: 40px;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n        \"\n        title=\"Appel vidéo\"\n        onmouseover=\"this.style.transform='scale(1.1)'; this.style.boxShadow='0 6px 20px rgba(59, 130, 246, 0.4)'\"\n        onmouseout=\"this.style.transform='scale(1)'; this.style.boxShadow='0 4px 12px rgba(59, 130, 246, 0.3)'\"\n      >\n        <i class=\"fas fa-video\" style=\"font-size: 14px\"></i>\n      </button>\n\n      <!-- Appel vocal -->\n      <button\n        (click)=\"startVoiceCall()\"\n        style=\"\n          padding: 10px;\n          border-radius: 50%;\n          border: none;\n          background: linear-gradient(135deg, #10b981, #047857);\n          color: white;\n          cursor: pointer;\n          transition: all 0.3s;\n          box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);\n          width: 40px;\n          height: 40px;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n        \"\n        title=\"Appel vocal\"\n        onmouseover=\"this.style.transform='scale(1.1)'; this.style.boxShadow='0 6px 20px rgba(16, 185, 129, 0.4)'\"\n        onmouseout=\"this.style.transform='scale(1)'; this.style.boxShadow='0 4px 12px rgba(16, 185, 129, 0.3)'\"\n      >\n        <i class=\"fas fa-phone\" style=\"font-size: 14px\"></i>\n      </button>\n\n      <!-- Recherche -->\n      <button\n        (click)=\"toggleSearch()\"\n        style=\"\n          padding: 8px;\n          border-radius: 50%;\n          border: none;\n          background: transparent;\n          color: #6b7280;\n          cursor: pointer;\n          transition: all 0.2s;\n        \"\n        [style.background]=\"searchMode ? '#dcfce7' : 'transparent'\"\n        [style.color]=\"searchMode ? '#16a34a' : '#6b7280'\"\n        title=\"Rechercher\"\n      >\n        <i class=\"fas fa-search\"></i>\n      </button>\n\n      <!-- Rechargement -->\n      <button\n        (click)=\"reloadConversation()\"\n        style=\"\n          padding: 8px;\n          border-radius: 50%;\n          border: none;\n          background: transparent;\n          color: #6b7280;\n          cursor: pointer;\n          transition: all 0.2s;\n        \"\n        [style.opacity]=\"isLoading ? '0.5' : '1'\"\n        [disabled]=\"isLoading\"\n        title=\"Recharger la conversation\"\n        onmouseover=\"this.style.background='#f3f4f6'; this.style.color='#374151'\"\n        onmouseout=\"this.style.background='transparent'; this.style.color='#6b7280'\"\n      >\n        <i\n          class=\"fas fa-sync-alt\"\n          [style.animation]=\"isLoading ? 'spin 1s linear infinite' : 'none'\"\n        ></i>\n      </button>\n\n      <!-- Menu principal -->\n      <button\n        (click)=\"toggleMainMenu()\"\n        style=\"\n          padding: 8px;\n          border-radius: 50%;\n          border: none;\n          background: transparent;\n          color: #6b7280;\n          cursor: pointer;\n          transition: all 0.2s;\n          position: relative;\n        \"\n        [style.background]=\"showMainMenu ? '#dcfce7' : 'transparent'\"\n        [style.color]=\"showMainMenu ? '#16a34a' : '#6b7280'\"\n        title=\"Menu\"\n      >\n        <i class=\"fas fa-ellipsis-v\"></i>\n      </button>\n    </div>\n\n    <!-- Menu dropdown -->\n    <div\n      *ngIf=\"showMainMenu\"\n      style=\"\n        position: absolute;\n        top: 64px;\n        right: 16px;\n        background: #ffffff;\n        border-radius: 16px;\n        box-shadow: 0 20px 25px rgba(0, 0, 0, 0.1);\n        border: 1px solid #e5e7eb;\n        z-index: 50;\n        min-width: 192px;\n      \"\n    >\n      <div style=\"padding: 8px\">\n        <button\n          (click)=\"toggleSearch(); showMainMenu = false\"\n          style=\"\n            width: 100%;\n            display: flex;\n            align-items: center;\n            gap: 12px;\n            padding: 8px 12px;\n            border-radius: 8px;\n            border: none;\n            background: transparent;\n            cursor: pointer;\n            transition: all 0.2s;\n            text-align: left;\n          \"\n          onmouseover=\"this.style.background='#f3f4f6'\"\n          onmouseout=\"this.style.background='transparent'\"\n        >\n          <i class=\"fas fa-search\" style=\"color: #3b82f6\"></i>\n          <span style=\"color: #374151\">Rechercher</span>\n        </button>\n        <button\n          style=\"\n            width: 100%;\n            display: flex;\n            align-items: center;\n            gap: 12px;\n            padding: 8px 12px;\n            border-radius: 8px;\n            border: none;\n            background: transparent;\n            cursor: pointer;\n            transition: all 0.2s;\n            text-align: left;\n          \"\n          onmouseover=\"this.style.background='#f3f4f6'\"\n          onmouseout=\"this.style.background='transparent'\"\n        >\n          <i class=\"fas fa-user\" style=\"color: #10b981\"></i>\n          <span style=\"color: #374151\">Voir le profil</span>\n        </button>\n        <hr style=\"margin: 8px 0; border-color: #e5e7eb\" />\n        <button\n          style=\"\n            width: 100%;\n            display: flex;\n            align-items: center;\n            gap: 12px;\n            padding: 8px 12px;\n            border-radius: 8px;\n            border: none;\n            background: transparent;\n            cursor: pointer;\n            transition: all 0.2s;\n            text-align: left;\n          \"\n          onmouseover=\"this.style.background='#f3f4f6'\"\n          onmouseout=\"this.style.background='transparent'\"\n        >\n          <i class=\"fas fa-cog\" style=\"color: #6b7280\"></i>\n          <span style=\"color: #374151\">Paramètres</span>\n        </button>\n      </div>\n    </div>\n  </header>\n\n  <!-- ===== MAIN MESSAGES SECTION ===== -->\n  <main\n    style=\"flex: 1; overflow-y: auto; padding: 16px; position: relative\"\n    #messagesContainer\n    (scroll)=\"onScroll($event)\"\n    (dragover)=\"onDragOver($event)\"\n    (dragleave)=\"onDragLeave($event)\"\n    (drop)=\"onDrop($event)\"\n    [style.background]=\"isDragOver ? 'rgba(34, 197, 94, 0.1)' : 'transparent'\"\n  >\n    <!-- Drag & Drop Overlay -->\n    <div\n      *ngIf=\"isDragOver\"\n      style=\"\n        position: absolute;\n        top: 0;\n        left: 0;\n        right: 0;\n        bottom: 0;\n        background: rgba(34, 197, 94, 0.2);\n        border: 2px dashed transparent;\n        border-radius: 8px;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        z-index: 50;\n        backdrop-filter: blur(2px);\n        animation: pulse 2s infinite;\n      \"\n    >\n      <div\n        style=\"\n          text-align: center;\n          background: #ffffff;\n          padding: 24px;\n          border-radius: 12px;\n          box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);\n          border: 1px solid transparent;\n        \"\n      >\n        <i\n          class=\"fas fa-cloud-upload-alt\"\n          style=\"\n            font-size: 48px;\n            color: #10b981;\n            margin-bottom: 12px;\n            animation: bounce 1s infinite;\n          \"\n        ></i>\n        <p\n          style=\"\n            font-size: 20px;\n            font-weight: bold;\n            color: #047857;\n            margin-bottom: 8px;\n          \"\n        >\n          Déposez vos fichiers ici\n        </p>\n        <p style=\"font-size: 14px; color: #10b981\">\n          Images, vidéos, documents...\n        </p>\n      </div>\n    </div>\n\n    <!-- Loading State -->\n    <div\n      *ngIf=\"isLoading\"\n      style=\"\n        display: flex;\n        flex-direction: column;\n        align-items: center;\n        justify-content: center;\n        padding: 32px 0;\n      \"\n    >\n      <div\n        style=\"\n          width: 32px;\n          height: 32px;\n          border: 2px solid #e5e7eb;\n          border-bottom-color: #10b981;\n          border-radius: 50%;\n          animation: spin 1s linear infinite;\n          margin-bottom: 16px;\n        \"\n      ></div>\n      <span style=\"color: #6b7280\">Chargement des messages...</span>\n    </div>\n\n    <!-- Empty State -->\n    <div\n      *ngIf=\"!isLoading && messages.length === 0\"\n      style=\"\n        display: flex;\n        flex-direction: column;\n        align-items: center;\n        justify-content: center;\n        padding: 64px 0;\n      \"\n    >\n      <div style=\"font-size: 64px; color: #d1d5db; margin-bottom: 16px\">\n        <i class=\"fas fa-comments\"></i>\n      </div>\n      <h3\n        style=\"\n          font-size: 20px;\n          font-weight: 600;\n          color: #374151;\n          margin-bottom: 8px;\n        \"\n      >\n        Aucun message\n      </h3>\n      <p style=\"color: #6b7280; text-align: center\">\n        Commencez votre conversation avec {{ otherParticipant?.username }}\n      </p>\n    </div>\n\n    <!-- Messages List -->\n    <div\n      *ngIf=\"!isLoading && messages.length > 0\"\n      style=\"display: flex; flex-direction: column; gap: 8px\"\n    >\n      <ng-container\n        *ngFor=\"\n          let message of messages;\n          let i = index;\n          trackBy: trackByMessageId\n        \"\n      >\n        <!-- Date Separator -->\n        <div\n          *ngIf=\"shouldShowDateSeparator(i)\"\n          style=\"display: flex; justify-content: center; margin: 16px 0\"\n        >\n          <div\n            style=\"\n              background: #ffffff;\n              padding: 4px 12px;\n              border-radius: 20px;\n              box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n            \"\n          >\n            <span style=\"font-size: 12px; color: #6b7280\">\n              {{ formatDateSeparator(message.timestamp) }}\n            </span>\n          </div>\n        </div>\n\n        <!-- Message Container -->\n        <div\n          style=\"display: flex\"\n          [style.justify-content]=\"\n            message.sender?.id === currentUserId ? 'flex-end' : 'flex-start'\n          \"\n          [id]=\"'message-' + message.id\"\n          (click)=\"onMessageClick(message, $event)\"\n          (contextmenu)=\"onMessageContextMenu(message, $event)\"\n        >\n          <!-- Avatar for others -->\n          <div\n            *ngIf=\"message.sender?.id !== currentUserId && shouldShowAvatar(i)\"\n            style=\"margin-right: 8px; flex-shrink: 0\"\n          >\n            <img\n              [src]=\"\n                message.sender?.image || 'assets/images/default-avatar.png'\n              \"\n              [alt]=\"message.sender?.username\"\n              style=\"\n                width: 32px;\n                height: 32px;\n                border-radius: 50%;\n                object-fit: cover;\n                cursor: pointer;\n                transition: transform 0.2s;\n              \"\n              (click)=\"openUserProfile(message.sender?.id!)\"\n              onmouseover=\"this.style.transform='scale(1.05)'\"\n              onmouseout=\"this.style.transform='scale(1)'\"\n            />\n          </div>\n\n          <!-- Message Bubble -->\n          <div\n            [style.background-color]=\"\n              message.sender?.id === currentUserId ? '#3b82f6' : '#ffffff'\n            \"\n            [style.color]=\"\n              message.sender?.id === currentUserId ? '#ffffff' : '#111827'\n            \"\n            style=\"\n              max-width: 320px;\n              padding: 12px 16px;\n              border-radius: 18px;\n              box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n              position: relative;\n              word-wrap: break-word;\n              overflow-wrap: break-word;\n              border: none;\n            \"\n          >\n            <!-- Sender Name (for groups) -->\n            <div\n              *ngIf=\"\n                isGroupConversation() &&\n                message.sender?.id !== currentUserId &&\n                shouldShowSenderName(i)\n              \"\n              style=\"\n                font-size: 12px;\n                font-weight: 600;\n                margin-bottom: 4px;\n                opacity: 0.75;\n              \"\n              [style.color]=\"getUserColor(message.sender?.id!)\"\n            >\n              {{ message.sender?.username }}\n            </div>\n\n            <!-- Text Content -->\n            <div\n              *ngIf=\"getMessageType(message) === 'text'\"\n              style=\"word-wrap: break-word; overflow-wrap: break-word\"\n            >\n              <div [innerHTML]=\"formatMessageContent(message.content)\"></div>\n            </div>\n\n            <!-- Image Content -->\n            <div *ngIf=\"hasImage(message)\" style=\"margin: 8px 0\">\n              <img\n                [src]=\"getImageUrl(message)\"\n                [alt]=\"message.content || 'Image'\"\n                (click)=\"openImageViewer(message)\"\n                (load)=\"onImageLoad($event, message)\"\n                (error)=\"onImageError($event, message)\"\n                style=\"\n                  max-width: 280px;\n                  height: auto;\n                  border-radius: 12px;\n                  cursor: pointer;\n                  transition: transform 0.2s;\n                \"\n                onmouseover=\"this.style.transform='scale(1.02)'\"\n                onmouseout=\"this.style.transform='scale(1)'\"\n              />\n              <!-- Image Caption -->\n              <div\n                *ngIf=\"message.content\"\n                [style.color]=\"\n                  message.sender?.id === currentUserId ? '#ffffff' : '#111827'\n                \"\n                style=\"font-size: 14px; margin-top: 8px; line-height: 1.4\"\n                [innerHTML]=\"formatMessageContent(message.content)\"\n              ></div>\n            </div>\n\n            <!-- Voice Message Content -->\n            <div\n              *ngIf=\"getMessageType(message) === 'audio'\"\n              style=\"\n                display: flex;\n                align-items: center;\n                gap: 12px;\n                padding: 12px;\n                background: rgba(255, 255, 255, 0.1);\n                border-radius: 12px;\n                margin: 8px 0;\n                min-width: 200px;\n                max-width: 280px;\n              \"\n            >\n              <!-- Bouton Play/Pause -->\n              <button\n                (click)=\"toggleVoicePlayback(message)\"\n                style=\"\n                  width: 40px;\n                  height: 40px;\n                  border-radius: 50%;\n                  border: none;\n                  background: rgba(255, 255, 255, 0.2);\n                  color: inherit;\n                  cursor: pointer;\n                  display: flex;\n                  align-items: center;\n                  justify-content: center;\n                  transition: all 0.2s;\n                  flex-shrink: 0;\n                \"\n                onmouseover=\"this.style.background='rgba(255, 255, 255, 0.3)'\"\n                onmouseout=\"this.style.background='rgba(255, 255, 255, 0.2)'\"\n                title=\"Lire/Pause\"\n              >\n                <i\n                  [class]=\"\n                    isVoicePlaying(message.id) ? 'fas fa-pause' : 'fas fa-play'\n                  \"\n                  style=\"font-size: 14px\"\n                ></i>\n              </button>\n\n              <!-- Visualisation des ondes sonores -->\n              <div\n                style=\"\n                  flex: 1;\n                  display: flex;\n                  align-items: center;\n                  gap: 2px;\n                  height: 24px;\n                  overflow: hidden;\n                \"\n              >\n                <div\n                  *ngFor=\"let wave of voiceWaves; let i = index\"\n                  style=\"\n                    width: 3px;\n                    background: currentColor;\n                    border-radius: 2px;\n                    opacity: 0.7;\n                    transition: height 0.3s ease;\n                  \"\n                  [style.height.px]=\"isVoicePlaying(message.id) ? wave : 8\"\n                  [style.animation]=\"\n                    isVoicePlaying(message.id) ? 'pulse 1s infinite' : 'none'\n                  \"\n                  [style.animation-delay]=\"i * 0.1 + 's'\"\n                ></div>\n              </div>\n\n              <!-- Durée et contrôles -->\n              <div\n                style=\"\n                  display: flex;\n                  align-items: center;\n                  gap: 8px;\n                  flex-shrink: 0;\n                \"\n              >\n                <!-- Durée -->\n                <div\n                  style=\"\n                    font-size: 12px;\n                    opacity: 0.8;\n                    min-width: 40px;\n                    text-align: right;\n                  \"\n                >\n                  {{ getVoiceDuration(message) }}\n                </div>\n\n                <!-- Vitesse de lecture (si en cours de lecture) -->\n                <button\n                  *ngIf=\"isVoicePlaying(message.id)\"\n                  (click)=\"changeVoiceSpeed(message)\"\n                  style=\"\n                    padding: 4px 8px;\n                    border-radius: 12px;\n                    border: none;\n                    background: rgba(255, 255, 255, 0.2);\n                    color: inherit;\n                    cursor: pointer;\n                    font-size: 11px;\n                    transition: all 0.2s;\n                  \"\n                  onmouseover=\"this.style.background='rgba(255, 255, 255, 0.3)'\"\n                  onmouseout=\"this.style.background='rgba(255, 255, 255, 0.2)'\"\n                  title=\"Changer la vitesse\"\n                >\n                  {{ getVoiceSpeed(message) }}x\n                </button>\n              </div>\n            </div>\n\n            <!-- Message Metadata -->\n            <div\n              style=\"\n                display: flex;\n                align-items: center;\n                justify-content: flex-end;\n                gap: 4px;\n                margin-top: 4px;\n                font-size: 12px;\n                opacity: 0.75;\n              \"\n            >\n              <span>{{ formatMessageTime(message.timestamp) }}</span>\n              <div\n                *ngIf=\"message.sender?.id === currentUserId\"\n                style=\"display: flex; align-items: center\"\n              >\n                <i\n                  class=\"fas fa-clock\"\n                  *ngIf=\"message.status === 'SENDING'\"\n                  title=\"Envoi en cours\"\n                ></i>\n                <i\n                  class=\"fas fa-check\"\n                  *ngIf=\"message.status === 'SENT'\"\n                  title=\"Envoyé\"\n                ></i>\n                <i\n                  class=\"fas fa-check-double\"\n                  *ngIf=\"message.status === 'DELIVERED'\"\n                  title=\"Livré\"\n                ></i>\n                <i\n                  class=\"fas fa-check-double\"\n                  style=\"color: #3b82f6\"\n                  *ngIf=\"message.status === 'READ'\"\n                  title=\"Lu\"\n                ></i>\n              </div>\n            </div>\n          </div>\n        </div>\n      </ng-container>\n\n      <!-- Typing Indicator -->\n      <div\n        *ngIf=\"otherUserIsTyping\"\n        style=\"display: flex; align-items: start; gap: 8px\"\n      >\n        <img\n          [src]=\"otherParticipant?.image || 'assets/images/default-avatar.png'\"\n          [alt]=\"otherParticipant?.username\"\n          style=\"\n            width: 32px;\n            height: 32px;\n            border-radius: 50%;\n            object-fit: cover;\n          \"\n        />\n        <div\n          style=\"\n            background: #ffffff;\n            padding: 12px 16px;\n            border-radius: 18px;\n            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n          \"\n        >\n          <div style=\"display: flex; gap: 4px\">\n            <div\n              style=\"\n                width: 8px;\n                height: 8px;\n                background: #6b7280;\n                border-radius: 50%;\n                animation: bounce 1s infinite;\n              \"\n            ></div>\n            <div\n              style=\"\n                width: 8px;\n                height: 8px;\n                background: #6b7280;\n                border-radius: 50%;\n                animation: bounce 1s infinite 0.1s;\n              \"\n            ></div>\n            <div\n              style=\"\n                width: 8px;\n                height: 8px;\n                background: #6b7280;\n                border-radius: 50%;\n                animation: bounce 1s infinite 0.2s;\n              \"\n            ></div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </main>\n\n  <!-- ===== FOOTER INPUT SECTION ===== -->\n  <footer\n    style=\"background: #ffffff; border-top: 1px solid #e5e7eb; padding: 16px\"\n  >\n    <form\n      [formGroup]=\"messageForm\"\n      (ngSubmit)=\"sendMessage()\"\n      style=\"display: flex; align-items: end; gap: 12px\"\n    >\n      <!-- Left Actions -->\n      <div style=\"display: flex; gap: 8px\">\n        <!-- Emoji Button -->\n        <button\n          type=\"button\"\n          (click)=\"toggleEmojiPicker()\"\n          style=\"\n            padding: 8px;\n            border-radius: 50%;\n            border: none;\n            background: transparent;\n            color: #6b7280;\n            cursor: pointer;\n            transition: all 0.2s;\n          \"\n          [style.background]=\"showEmojiPicker ? '#dcfce7' : 'transparent'\"\n          [style.color]=\"showEmojiPicker ? '#16a34a' : '#6b7280'\"\n          title=\"Émojis\"\n          onmouseover=\"this.style.background=this.style.background === 'transparent' ? '#f3f4f6' : this.style.background\"\n          onmouseout=\"this.style.background=this.style.background === '#f3f4f6' ? 'transparent' : this.style.background\"\n        >\n          <i class=\"fas fa-smile\"></i>\n        </button>\n\n        <!-- Attachment Button -->\n        <button\n          type=\"button\"\n          (click)=\"toggleAttachmentMenu()\"\n          style=\"\n            padding: 8px;\n            border-radius: 50%;\n            border: none;\n            background: transparent;\n            color: #6b7280;\n            cursor: pointer;\n            transition: all 0.2s;\n          \"\n          [style.background]=\"showAttachmentMenu ? '#dcfce7' : 'transparent'\"\n          [style.color]=\"showAttachmentMenu ? '#16a34a' : '#6b7280'\"\n          title=\"Pièces jointes\"\n          onmouseover=\"this.style.background=this.style.background === 'transparent' ? '#f3f4f6' : this.style.background\"\n          onmouseout=\"this.style.background=this.style.background === '#f3f4f6' ? 'transparent' : this.style.background\"\n        >\n          <i class=\"fas fa-paperclip\"></i>\n        </button>\n\n        <!-- Voice Recording Button -->\n        <button\n          type=\"button\"\n          (mousedown)=\"onRecordStart($event)\"\n          (mouseup)=\"onRecordEnd($event)\"\n          (mouseleave)=\"onRecordCancel($event)\"\n          (touchstart)=\"onRecordStart($event)\"\n          (touchend)=\"onRecordEnd($event)\"\n          (touchcancel)=\"onRecordCancel($event)\"\n          style=\"\n            padding: 8px;\n            border-radius: 50%;\n            border: none;\n            background: transparent;\n            color: #6b7280;\n            cursor: pointer;\n            transition: all 0.2s;\n            position: relative;\n          \"\n          [style.background]=\"isRecordingVoice ? '#fef3c7' : 'transparent'\"\n          [style.color]=\"isRecordingVoice ? '#f59e0b' : '#6b7280'\"\n          [style.transform]=\"isRecordingVoice ? 'scale(1.1)' : 'scale(1)'\"\n          title=\"Maintenir pour enregistrer un message vocal\"\n          onmouseover=\"if(!this.style.background || this.style.background === 'transparent') this.style.background='#f3f4f6'\"\n          onmouseout=\"if(this.style.background === '#f3f4f6') this.style.background='transparent'\"\n        >\n          <i\n            [class]=\"isRecordingVoice ? 'fas fa-stop' : 'fas fa-microphone'\"\n            [style.animation]=\"isRecordingVoice ? 'pulse 1s infinite' : 'none'\"\n          ></i>\n\n          <!-- Indicateur d'enregistrement -->\n          <div\n            *ngIf=\"isRecordingVoice\"\n            style=\"\n              position: absolute;\n              top: -2px;\n              right: -2px;\n              width: 8px;\n              height: 8px;\n              background: #ef4444;\n              border-radius: 50%;\n              animation: ping 1s infinite;\n            \"\n          ></div>\n        </button>\n      </div>\n\n      <!-- Message Input -->\n      <div style=\"flex: 1; position: relative\">\n        <textarea\n          formControlName=\"content\"\n          placeholder=\"Tapez votre message...\"\n          (keydown)=\"onInputKeyDown($event)\"\n          (input)=\"onInputChange($event)\"\n          (focus)=\"onInputFocus()\"\n          style=\"\n            width: 100%;\n            min-height: 44px;\n            max-height: 120px;\n            padding: 12px 16px;\n            border: 1px solid #e5e7eb;\n            border-radius: 22px;\n            resize: none;\n            outline: none;\n            font-family: inherit;\n            font-size: 14px;\n            line-height: 1.4;\n            background: #ffffff;\n            color: #111827;\n            transition: all 0.2s;\n          \"\n          [disabled]=\"isInputDisabled()\"\n        ></textarea>\n      </div>\n\n      <!-- Send Button -->\n      <button\n        type=\"submit\"\n        [disabled]=\"!messageForm.valid || isSendingMessage\"\n        style=\"\n          padding: 12px;\n          border-radius: 50%;\n          border: none;\n          background: #3b82f6;\n          color: #ffffff;\n          cursor: pointer;\n          transition: all 0.2s;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          min-width: 44px;\n          min-height: 44px;\n        \"\n        [style.background]=\"\n          !messageForm.valid || isSendingMessage ? '#9ca3af' : '#3b82f6'\n        \"\n        [style.cursor]=\"\n          !messageForm.valid || isSendingMessage ? 'not-allowed' : 'pointer'\n        \"\n        title=\"Envoyer\"\n        onmouseover=\"if(!this.disabled) this.style.background='#2563eb'\"\n        onmouseout=\"if(!this.disabled) this.style.background='#3b82f6'\"\n      >\n        <i class=\"fas fa-paper-plane\" *ngIf=\"!isSendingMessage\"></i>\n        <div\n          *ngIf=\"isSendingMessage\"\n          style=\"\n            width: 16px;\n            height: 16px;\n            border: 2px solid #ffffff;\n            border-top-color: transparent;\n            border-radius: 50%;\n            animation: spin 1s linear infinite;\n          \"\n        ></div>\n      </button>\n    </form>\n\n    <!-- Emoji Picker -->\n    <div\n      *ngIf=\"showEmojiPicker\"\n      style=\"\n        position: absolute;\n        bottom: 80px;\n        left: 16px;\n        background: #ffffff;\n        border-radius: 16px;\n        box-shadow: 0 20px 25px rgba(0, 0, 0, 0.1);\n        border: 1px solid #e5e7eb;\n        z-index: 50;\n        width: 320px;\n        max-height: 300px;\n        overflow-y: auto;\n      \"\n    >\n      <div style=\"padding: 16px\">\n        <h4\n          style=\"\n            margin: 0 0 12px 0;\n            font-size: 14px;\n            font-weight: 600;\n            color: #374151;\n          \"\n        >\n          Émojis\n        </h4>\n        <div\n          style=\"display: grid; grid-template-columns: repeat(8, 1fr); gap: 8px\"\n        >\n          <button\n            *ngFor=\"let emoji of getEmojisForCategory(selectedEmojiCategory)\"\n            (click)=\"insertEmoji(emoji)\"\n            style=\"\n              padding: 8px;\n              border: none;\n              background: transparent;\n              border-radius: 8px;\n              cursor: pointer;\n              font-size: 20px;\n              transition: all 0.2s;\n            \"\n            onmouseover=\"this.style.background='#f3f4f6'\"\n            onmouseout=\"this.style.background='transparent'\"\n            [title]=\"emoji.name\"\n          >\n            {{ emoji.emoji }}\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Attachment Menu -->\n    <div\n      *ngIf=\"showAttachmentMenu\"\n      style=\"\n        position: absolute;\n        bottom: 80px;\n        left: 60px;\n        background: #ffffff;\n        border-radius: 16px;\n        box-shadow: 0 20px 25px rgba(0, 0, 0, 0.1);\n        border: 1px solid #e5e7eb;\n        z-index: 50;\n        min-width: 200px;\n      \"\n    >\n      <div style=\"padding: 16px\">\n        <h4\n          style=\"\n            margin: 0 0 12px 0;\n            font-size: 14px;\n            font-weight: 600;\n            color: #374151;\n          \"\n        >\n          Pièces jointes\n        </h4>\n        <div\n          style=\"\n            display: grid;\n            grid-template-columns: repeat(2, 1fr);\n            gap: 12px;\n          \"\n        >\n          <!-- Images -->\n          <button\n            (click)=\"triggerFileInput('image')\"\n            style=\"\n              display: flex;\n              flex-direction: column;\n              align-items: center;\n              gap: 8px;\n              padding: 16px;\n              border: none;\n              background: transparent;\n              border-radius: 12px;\n              cursor: pointer;\n              transition: all 0.2s;\n            \"\n            onmouseover=\"this.style.background='#f3f4f6'\"\n            onmouseout=\"this.style.background='transparent'\"\n          >\n            <div\n              style=\"\n                width: 48px;\n                height: 48px;\n                background: #dbeafe;\n                border-radius: 50%;\n                display: flex;\n                align-items: center;\n                justify-content: center;\n              \"\n            >\n              <i\n                class=\"fas fa-image\"\n                style=\"color: #3b82f6; font-size: 20px\"\n              ></i>\n            </div>\n            <span style=\"font-size: 14px; font-weight: 500; color: #374151\"\n              >Images</span\n            >\n          </button>\n\n          <!-- Documents -->\n          <button\n            (click)=\"triggerFileInput('document')\"\n            style=\"\n              display: flex;\n              flex-direction: column;\n              align-items: center;\n              gap: 8px;\n              padding: 16px;\n              border: none;\n              background: transparent;\n              border-radius: 12px;\n              cursor: pointer;\n              transition: all 0.2s;\n            \"\n            onmouseover=\"this.style.background='#f3f4f6'\"\n            onmouseout=\"this.style.background='transparent'\"\n          >\n            <div\n              style=\"\n                width: 48px;\n                height: 48px;\n                background: #fef3c7;\n                border-radius: 50%;\n                display: flex;\n                align-items: center;\n                justify-content: center;\n              \"\n            >\n              <i\n                class=\"fas fa-file-alt\"\n                style=\"color: #f59e0b; font-size: 20px\"\n              ></i>\n            </div>\n            <span style=\"font-size: 14px; font-weight: 500; color: #374151\"\n              >Documents</span\n            >\n          </button>\n\n          <!-- Camera -->\n          <button\n            (click)=\"openCamera()\"\n            style=\"\n              display: flex;\n              flex-direction: column;\n              align-items: center;\n              gap: 8px;\n              padding: 16px;\n              border: none;\n              background: transparent;\n              border-radius: 12px;\n              cursor: pointer;\n              transition: all 0.2s;\n            \"\n            onmouseover=\"this.style.background='#f3f4f6'\"\n            onmouseout=\"this.style.background='transparent'\"\n          >\n            <div\n              style=\"\n                width: 48px;\n                height: 48px;\n                background: #dcfce7;\n                border-radius: 50%;\n                display: flex;\n                align-items: center;\n                justify-content: center;\n              \"\n            >\n              <i\n                class=\"fas fa-camera\"\n                style=\"color: #10b981; font-size: 20px\"\n              ></i>\n            </div>\n            <span style=\"font-size: 14px; font-weight: 500; color: #374151\"\n              >Caméra</span\n            >\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Hidden File Input -->\n    <input\n      #fileInput\n      type=\"file\"\n      style=\"display: none\"\n      (change)=\"onFileSelected($event)\"\n      [accept]=\"getFileAcceptTypes()\"\n      multiple\n    />\n  </footer>\n\n  <!-- Overlay to close menus -->\n  <div\n    *ngIf=\"showEmojiPicker || showAttachmentMenu || showMainMenu\"\n    style=\"\n      position: fixed;\n      top: 0;\n      left: 0;\n      right: 0;\n      bottom: 0;\n      background: rgba(0, 0, 0, 0.25);\n      z-index: 40;\n    \"\n    (click)=\"closeAllMenus()\"\n  ></div>\n\n  <!-- Interface d'enregistrement vocal -->\n  <div\n    *ngIf=\"isRecordingVoice\"\n    style=\"\n      position: fixed;\n      bottom: 100px;\n      left: 50%;\n      transform: translateX(-50%);\n      background: linear-gradient(135deg, #f59e0b, #d97706);\n      color: white;\n      padding: 20px 24px;\n      border-radius: 20px;\n      box-shadow: 0 20px 25px rgba(0, 0, 0, 0.2);\n      z-index: 60;\n      display: flex;\n      align-items: center;\n      gap: 16px;\n      min-width: 280px;\n      animation: slideInUp 0.3s ease-out;\n    \"\n  >\n    <!-- Icône microphone animée -->\n    <div\n      style=\"\n        width: 48px;\n        height: 48px;\n        background: rgba(255, 255, 255, 0.2);\n        border-radius: 50%;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        animation: pulse 1s infinite;\n      \"\n    >\n      <i class=\"fas fa-microphone\" style=\"font-size: 20px\"></i>\n    </div>\n\n    <!-- Contenu central -->\n    <div style=\"flex: 1\">\n      <!-- Durée d'enregistrement -->\n      <div style=\"font-size: 18px; font-weight: bold; margin-bottom: 4px\">\n        {{ formatRecordingDuration(voiceRecordingDuration) }}\n      </div>\n\n      <!-- Visualisation des ondes sonores -->\n      <div style=\"display: flex; align-items: center; gap: 2px; height: 20px\">\n        <div\n          *ngFor=\"let wave of voiceWaves; let i = index\"\n          style=\"\n            width: 3px;\n            background: rgba(255, 255, 255, 0.8);\n            border-radius: 2px;\n            transition: height 0.3s ease;\n          \"\n          [style.height.px]=\"wave\"\n          [style.animation]=\"'bounce 1s infinite'\"\n          [style.animation-delay]=\"i * 0.1 + 's'\"\n        ></div>\n      </div>\n\n      <!-- Format d'enregistrement -->\n      <div style=\"font-size: 12px; opacity: 0.8; margin-top: 4px\">\n        Format: {{ getRecordingFormat() }}\n      </div>\n    </div>\n\n    <!-- Boutons d'action -->\n    <div style=\"display: flex; gap: 8px\">\n      <!-- Bouton Annuler -->\n      <button\n        (click)=\"onRecordCancel($event)\"\n        style=\"\n          width: 40px;\n          height: 40px;\n          border-radius: 50%;\n          border: none;\n          background: rgba(239, 68, 68, 0.8);\n          color: white;\n          cursor: pointer;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          transition: all 0.2s;\n        \"\n        onmouseover=\"this.style.background='rgba(239, 68, 68, 1)'\"\n        onmouseout=\"this.style.background='rgba(239, 68, 68, 0.8)'\"\n        title=\"Annuler l'enregistrement\"\n      >\n        <i class=\"fas fa-times\" style=\"font-size: 16px\"></i>\n      </button>\n\n      <!-- Bouton Envoyer -->\n      <button\n        (click)=\"onRecordEnd($event)\"\n        style=\"\n          width: 40px;\n          height: 40px;\n          border-radius: 50%;\n          border: none;\n          background: rgba(34, 197, 94, 0.8);\n          color: white;\n          cursor: pointer;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          transition: all 0.2s;\n        \"\n        onmouseover=\"this.style.background='rgba(34, 197, 94, 1)'\"\n        onmouseout=\"this.style.background='rgba(34, 197, 94, 0.8)'\"\n        title=\"Envoyer le message vocal\"\n      >\n        <i class=\"fas fa-paper-plane\" style=\"font-size: 16px\"></i>\n      </button>\n    </div>\n  </div>\n</div>\n\n<!-- Interface d'appel gérée par les composants globaux app-incoming-call et app-active-call -->\n\n<!-- Éléments vidéo gérés par les composants globaux d'appel -->\n\n<!-- Interface d'appel gérée par les composants globaux app-incoming-call et app-active-call -->\n\n<!-- Les composants d'appel sont maintenant gérés globalement dans app.component.html -->\n"], "mappings": ";AASA,SAAiCA,UAAU,QAAQ,gBAAgB;AAEnE,SAASC,YAAY,QAAQ,MAAM;AAInC,SAASC,QAAQ,QAA4B,kCAAkC;;;;;;;;;;;;;;;;IC4IvEC,EAAA,CAAAC,SAAA,cAaO;;;;;IAoBLD,EAAA,CAAAE,cAAA,cAGC;IACOF,EAAA,CAAAG,MAAA,6BAAiB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAC9BJ,EAAA,CAAAE,cAAA,cAAqC;IACnCF,EAAA,CAAAC,SAAA,cAQO;IAmBTD,EAAA,CAAAI,YAAA,EAAM;;;;;IAGRJ,EAAA,CAAAE,cAAA,WAA4B;IAC1BF,EAAA,CAAAG,MAAA,GAKF;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;IALLJ,EAAA,CAAAK,SAAA,GAKF;IALEL,EAAA,CAAAM,kBAAA,OAAAC,MAAA,CAAAC,gBAAA,kBAAAD,MAAA,CAAAC,gBAAA,CAAAC,QAAA,iBAAAF,MAAA,CAAAG,gBAAA,CAAAH,MAAA,CAAAC,gBAAA,kBAAAD,MAAA,CAAAC,gBAAA,CAAAG,UAAA,OAKF;;;;;;IA0HNX,EAAA,CAAAE,cAAA,cAaC;IAGKF,EAAA,CAAAY,UAAA,mBAAAC,6DAAA;MAAAb,EAAA,CAAAc,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAASD,OAAA,CAAAE,YAAA,EAAc;MAAA,OAAAlB,EAAA,CAAAmB,WAAA,CAAAH,OAAA,CAAAI,YAAA,GAAiB,KAAK;IAAA,EAAC;IAiB9CpB,EAAA,CAAAC,SAAA,YAAoD;IACpDD,EAAA,CAAAE,cAAA,eAA6B;IAAAF,EAAA,CAAAG,MAAA,iBAAU;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAEhDJ,EAAA,CAAAE,cAAA,iBAgBC;IACCF,EAAA,CAAAC,SAAA,YAAkD;IAClDD,EAAA,CAAAE,cAAA,eAA6B;IAAAF,EAAA,CAAAG,MAAA,qBAAc;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAEpDJ,EAAA,CAAAC,SAAA,cAAmD;IACnDD,EAAA,CAAAE,cAAA,kBAgBC;IACCF,EAAA,CAAAC,SAAA,aAAiD;IACjDD,EAAA,CAAAE,cAAA,gBAA6B;IAAAF,EAAA,CAAAG,MAAA,uBAAU;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;IAiBpDJ,EAAA,CAAAE,cAAA,cAkBC;IAWGF,EAAA,CAAAC,SAAA,YAQK;IACLD,EAAA,CAAAE,cAAA,YAOC;IACCF,EAAA,CAAAG,MAAA,sCACF;IAAAH,EAAA,CAAAI,YAAA,EAAI;IACJJ,EAAA,CAAAE,cAAA,YAA2C;IACzCF,EAAA,CAAAG,MAAA,0CACF;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;IAKRJ,EAAA,CAAAE,cAAA,cASC;IACCF,EAAA,CAAAC,SAAA,cAUO;IACPD,EAAA,CAAAE,cAAA,eAA6B;IAAAF,EAAA,CAAAG,MAAA,iCAA0B;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;IAIhEJ,EAAA,CAAAE,cAAA,cASC;IAEGF,EAAA,CAAAC,SAAA,YAA+B;IACjCD,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,aAOC;IACCF,EAAA,CAAAG,MAAA,sBACF;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAE,cAAA,YAA8C;IAC5CF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;IADFJ,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,wCAAAe,MAAA,CAAAb,gBAAA,kBAAAa,MAAA,CAAAb,gBAAA,CAAAc,QAAA,MACF;;;;;IAgBEtB,EAAA,CAAAE,cAAA,cAGC;IAUKF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;IADLJ,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAiB,OAAA,CAAAC,mBAAA,CAAAC,WAAA,CAAAC,SAAA,OACF;;;;;;IAeF1B,EAAA,CAAAE,cAAA,cAGC;IAcGF,EAAA,CAAAY,UAAA,mBAAAe,+EAAA;MAAA3B,EAAA,CAAAc,aAAA,CAAAc,IAAA;MAAA,MAAAH,WAAA,GAAAzB,EAAA,CAAAiB,aAAA,GAAAY,SAAA;MAAA,MAAAC,OAAA,GAAA9B,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAmB,WAAA,CAAAW,OAAA,CAAAC,eAAA,CAAAN,WAAA,CAAAO,MAAA,kBAAAP,WAAA,CAAAO,MAAA,CAAAC,EAAA,CAAoC;IAAA,EAAC;IAbhDjC,EAAA,CAAAI,YAAA,EAgBE;;;;IAfAJ,EAAA,CAAAK,SAAA,GAEC;IAFDL,EAAA,CAAAkC,UAAA,SAAAT,WAAA,CAAAO,MAAA,kBAAAP,WAAA,CAAAO,MAAA,CAAAG,KAAA,yCAAAnC,EAAA,CAAAoC,aAAA,CAEC,QAAAX,WAAA,CAAAO,MAAA,kBAAAP,WAAA,CAAAO,MAAA,CAAAV,QAAA;;;;;IAoCHtB,EAAA,CAAAE,cAAA,eAaC;IACCF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;IAHJJ,EAAA,CAAAqC,WAAA,UAAAC,OAAA,CAAAC,YAAA,CAAAd,WAAA,CAAAO,MAAA,kBAAAP,WAAA,CAAAO,MAAA,CAAAC,EAAA,EAAiD;IAEjDjC,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAmB,WAAA,CAAAO,MAAA,kBAAAP,WAAA,CAAAO,MAAA,CAAAV,QAAA,MACF;;;;;IAGAtB,EAAA,CAAAE,cAAA,eAGC;IACCF,EAAA,CAAAC,SAAA,eAA+D;IACjED,EAAA,CAAAI,YAAA,EAAM;;;;;IADCJ,EAAA,CAAAK,SAAA,GAAmD;IAAnDL,EAAA,CAAAkC,UAAA,cAAAM,OAAA,CAAAC,oBAAA,CAAAhB,WAAA,CAAAiB,OAAA,GAAA1C,EAAA,CAAA2C,cAAA,CAAmD;;;;;IAsBxD3C,EAAA,CAAAC,SAAA,eAOO;;;;;IALLD,EAAA,CAAAqC,WAAA,WAAAZ,WAAA,CAAAO,MAAA,kBAAAP,WAAA,CAAAO,MAAA,CAAAC,EAAA,MAAAW,OAAA,CAAAC,aAAA,yBAEC;IAED7C,EAAA,CAAAkC,UAAA,cAAAU,OAAA,CAAAH,oBAAA,CAAAhB,WAAA,CAAAiB,OAAA,GAAA1C,EAAA,CAAA2C,cAAA,CAAmD;;;;;;IAxBvD3C,EAAA,CAAAE,cAAA,eAAqD;IAIjDF,EAAA,CAAAY,UAAA,mBAAAkC,+EAAA;MAAA9C,EAAA,CAAAc,aAAA,CAAAiC,IAAA;MAAA,MAAAtB,WAAA,GAAAzB,EAAA,CAAAiB,aAAA,GAAAY,SAAA;MAAA,MAAAmB,OAAA,GAAAhD,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAmB,WAAA,CAAA6B,OAAA,CAAAC,eAAA,CAAAxB,WAAA,CAAwB;IAAA,EAAC,kBAAAyB,8EAAAC,MAAA;MAAAnD,EAAA,CAAAc,aAAA,CAAAiC,IAAA;MAAA,MAAAtB,WAAA,GAAAzB,EAAA,CAAAiB,aAAA,GAAAY,SAAA;MAAA,MAAAuB,OAAA,GAAApD,EAAA,CAAAiB,aAAA;MAAA,OAC1BjB,EAAA,CAAAmB,WAAA,CAAAiC,OAAA,CAAAC,WAAA,CAAAF,MAAA,EAAA1B,WAAA,CAA4B;IAAA,EADF,mBAAA6B,+EAAAH,MAAA;MAAAnD,EAAA,CAAAc,aAAA,CAAAiC,IAAA;MAAA,MAAAtB,WAAA,GAAAzB,EAAA,CAAAiB,aAAA,GAAAY,SAAA;MAAA,MAAA0B,OAAA,GAAAvD,EAAA,CAAAiB,aAAA;MAAA,OAEzBjB,EAAA,CAAAmB,WAAA,CAAAoC,OAAA,CAAAC,YAAA,CAAAL,MAAA,EAAA1B,WAAA,CAA6B;IAAA,EAFJ;IAHpCzB,EAAA,CAAAI,YAAA,EAeE;IAEFJ,EAAA,CAAAyD,UAAA,IAAAC,+DAAA,mBAOO;IACT1D,EAAA,CAAAI,YAAA,EAAM;;;;;IAxBFJ,EAAA,CAAAK,SAAA,GAA4B;IAA5BL,EAAA,CAAAkC,UAAA,QAAAyB,OAAA,CAAAC,WAAA,CAAAnC,WAAA,GAAAzB,EAAA,CAAAoC,aAAA,CAA4B,QAAAX,WAAA,CAAAiB,OAAA;IAiB3B1C,EAAA,CAAAK,SAAA,GAAqB;IAArBL,EAAA,CAAAkC,UAAA,SAAAT,WAAA,CAAAiB,OAAA,CAAqB;;;;;IAgEtB1C,EAAA,CAAAC,SAAA,eAcO;;;;;;;IALLD,EAAA,CAAAqC,WAAA,WAAAwB,OAAA,CAAAC,cAAA,CAAArC,WAAA,CAAAQ,EAAA,IAAA8B,QAAA,WAAyD,cAAAF,OAAA,CAAAC,cAAA,CAAArC,WAAA,CAAAQ,EAAA,qDAAA+B,KAAA;;;;;;IA8B3DhE,EAAA,CAAAE,cAAA,kBAgBC;IAdCF,EAAA,CAAAY,UAAA,mBAAAqD,2FAAA;MAAAjE,EAAA,CAAAc,aAAA,CAAAoD,IAAA;MAAA,MAAAzC,WAAA,GAAAzB,EAAA,CAAAiB,aAAA,IAAAY,SAAA;MAAA,MAAAsC,OAAA,GAAAnE,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAmB,WAAA,CAAAgD,OAAA,CAAAC,gBAAA,CAAA3C,WAAA,CAAyB;IAAA,EAAC;IAenCzB,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;;IADPJ,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAA+D,OAAA,CAAAC,aAAA,CAAA7C,WAAA,QACF;;;;;;IA/GJzB,EAAA,CAAAE,cAAA,eAaC;IAGGF,EAAA,CAAAY,UAAA,mBAAA2D,kFAAA;MAAAvE,EAAA,CAAAc,aAAA,CAAA0D,IAAA;MAAA,MAAA/C,WAAA,GAAAzB,EAAA,CAAAiB,aAAA,GAAAY,SAAA;MAAA,MAAA4C,OAAA,GAAAzE,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAmB,WAAA,CAAAsD,OAAA,CAAAC,mBAAA,CAAAjD,WAAA,CAA4B;IAAA,EAAC;IAmBtCzB,EAAA,CAAAC,SAAA,aAKK;IACPD,EAAA,CAAAI,YAAA,EAAS;IAGTJ,EAAA,CAAAE,cAAA,eASC;IACCF,EAAA,CAAAyD,UAAA,IAAAkB,+DAAA,mBAcO;IACT3E,EAAA,CAAAI,YAAA,EAAM;IAGNJ,EAAA,CAAAE,cAAA,eAOC;IAUGF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAGNJ,EAAA,CAAAyD,UAAA,IAAAmB,kEAAA,sBAkBS;IACX5E,EAAA,CAAAI,YAAA,EAAM;;;;;IA5EFJ,EAAA,CAAAK,SAAA,GAEC;IAFDL,EAAA,CAAA6E,UAAA,CAAAC,OAAA,CAAAhB,cAAA,CAAArC,WAAA,CAAAQ,EAAA,mCAEC;IAiBgBjC,EAAA,CAAAK,SAAA,GAAe;IAAfL,EAAA,CAAAkC,UAAA,YAAA4C,OAAA,CAAAC,UAAA,CAAe;IAkChC/E,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAwE,OAAA,CAAAE,gBAAA,CAAAvD,WAAA,OACF;IAIGzB,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAkC,UAAA,SAAA4C,OAAA,CAAAhB,cAAA,CAAArC,WAAA,CAAAQ,EAAA,EAAgC;;;;;IAsCnCjC,EAAA,CAAAC,SAAA,aAIK;;;;;IACLD,EAAA,CAAAC,SAAA,aAIK;;;;;IACLD,EAAA,CAAAC,SAAA,aAIK;;;;;IACLD,EAAA,CAAAC,SAAA,aAKK;;;;;IAxBPD,EAAA,CAAAE,cAAA,eAGC;IACCF,EAAA,CAAAyD,UAAA,IAAAwB,8DAAA,iBAIK;IACLjF,EAAA,CAAAyD,UAAA,IAAAyB,8DAAA,iBAIK;IACLlF,EAAA,CAAAyD,UAAA,IAAA0B,8DAAA,iBAIK;IACLnF,EAAA,CAAAyD,UAAA,IAAA2B,8DAAA,iBAKK;IACPpF,EAAA,CAAAI,YAAA,EAAM;;;;IAnBDJ,EAAA,CAAAK,SAAA,GAAkC;IAAlCL,EAAA,CAAAkC,UAAA,SAAAT,WAAA,CAAA4D,MAAA,eAAkC;IAKlCrF,EAAA,CAAAK,SAAA,GAA+B;IAA/BL,EAAA,CAAAkC,UAAA,SAAAT,WAAA,CAAA4D,MAAA,YAA+B;IAK/BrF,EAAA,CAAAK,SAAA,GAAoC;IAApCL,EAAA,CAAAkC,UAAA,SAAAT,WAAA,CAAA4D,MAAA,iBAAoC;IAMpCrF,EAAA,CAAAK,SAAA,GAA+B;IAA/BL,EAAA,CAAAkC,UAAA,SAAAT,WAAA,CAAA4D,MAAA,YAA+B;;;;;;IA7R5CrF,EAAA,CAAAsF,uBAAA,GAMC;IAECtF,EAAA,CAAAyD,UAAA,IAAA8B,yDAAA,kBAgBM;IAGNvF,EAAA,CAAAE,cAAA,cAQC;IAFCF,EAAA,CAAAY,UAAA,mBAAA4E,yEAAArC,MAAA;MAAA,MAAAsC,WAAA,GAAAzF,EAAA,CAAAc,aAAA,CAAA4E,IAAA;MAAA,MAAAjE,WAAA,GAAAgE,WAAA,CAAA5D,SAAA;MAAA,MAAA8D,OAAA,GAAA3F,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAmB,WAAA,CAAAwE,OAAA,CAAAC,cAAA,CAAAnE,WAAA,EAAA0B,MAAA,CAA+B;IAAA,EAAC,yBAAA0C,+EAAA1C,MAAA;MAAA,MAAAsC,WAAA,GAAAzF,EAAA,CAAAc,aAAA,CAAA4E,IAAA;MAAA,MAAAjE,WAAA,GAAAgE,WAAA,CAAA5D,SAAA;MAAA,MAAAiE,OAAA,GAAA9F,EAAA,CAAAiB,aAAA;MAAA,OAC1BjB,EAAA,CAAAmB,WAAA,CAAA2E,OAAA,CAAAC,oBAAA,CAAAtE,WAAA,EAAA0B,MAAA,CAAqC;IAAA,EADX;IAIzCnD,EAAA,CAAAyD,UAAA,IAAAuC,yDAAA,kBAqBM;IAGNhG,EAAA,CAAAE,cAAA,cAiBC;IAECF,EAAA,CAAAyD,UAAA,IAAAwC,yDAAA,kBAeM;IAGNjG,EAAA,CAAAyD,UAAA,IAAAyC,yDAAA,kBAKM;IAGNlG,EAAA,CAAAyD,UAAA,IAAA0C,yDAAA,kBA0BM;IAGNnG,EAAA,CAAAyD,UAAA,IAAA2C,yDAAA,kBAiHM;IAGNpG,EAAA,CAAAE,cAAA,cAUC;IACOF,EAAA,CAAAG,MAAA,IAA0C;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACvDJ,EAAA,CAAAyD,UAAA,KAAA4C,0DAAA,kBAyBM;IACRrG,EAAA,CAAAI,YAAA,EAAM;IAGZJ,EAAA,CAAAsG,qBAAA,EAAe;;;;;;IA3RVtG,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAkC,UAAA,SAAAqE,OAAA,CAAAC,uBAAA,CAAAC,KAAA,EAAgC;IAoBjCzG,EAAA,CAAAK,SAAA,GAEC;IAFDL,EAAA,CAAAqC,WAAA,qBAAAZ,WAAA,CAAAO,MAAA,kBAAAP,WAAA,CAAAO,MAAA,CAAAC,EAAA,MAAAsE,OAAA,CAAA1D,aAAA,6BAEC;IACD7C,EAAA,CAAAkC,UAAA,oBAAAT,WAAA,CAAAQ,EAAA,CAA8B;IAM3BjC,EAAA,CAAAK,SAAA,GAAiE;IAAjEL,EAAA,CAAAkC,UAAA,UAAAT,WAAA,CAAAO,MAAA,kBAAAP,WAAA,CAAAO,MAAA,CAAAC,EAAA,MAAAsE,OAAA,CAAA1D,aAAA,IAAA0D,OAAA,CAAAG,gBAAA,CAAAD,KAAA,EAAiE;IAwBlEzG,EAAA,CAAAK,SAAA,GAEC;IAFDL,EAAA,CAAAqC,WAAA,sBAAAZ,WAAA,CAAAO,MAAA,kBAAAP,WAAA,CAAAO,MAAA,CAAAC,EAAA,MAAAsE,OAAA,CAAA1D,aAAA,yBAEC,WAAApB,WAAA,CAAAO,MAAA,kBAAAP,WAAA,CAAAO,MAAA,CAAAC,EAAA,MAAAsE,OAAA,CAAA1D,aAAA;IAiBE7C,EAAA,CAAAK,SAAA,GAKf;IALeL,EAAA,CAAAkC,UAAA,SAAAqE,OAAA,CAAAI,mBAAA,OAAAlF,WAAA,CAAAO,MAAA,kBAAAP,WAAA,CAAAO,MAAA,CAAAC,EAAA,MAAAsE,OAAA,CAAA1D,aAAA,IAAA0D,OAAA,CAAAK,oBAAA,CAAAH,KAAA,EAKf;IAaezG,EAAA,CAAAK,SAAA,GAAwC;IAAxCL,EAAA,CAAAkC,UAAA,SAAAqE,OAAA,CAAAM,cAAA,CAAApF,WAAA,aAAwC;IAOrCzB,EAAA,CAAAK,SAAA,GAAuB;IAAvBL,EAAA,CAAAkC,UAAA,SAAAqE,OAAA,CAAAO,QAAA,CAAArF,WAAA,EAAuB;IA8B1BzB,EAAA,CAAAK,SAAA,GAAyC;IAAzCL,EAAA,CAAAkC,UAAA,SAAAqE,OAAA,CAAAM,cAAA,CAAApF,WAAA,cAAyC;IA8HpCzB,EAAA,CAAAK,SAAA,GAA0C;IAA1CL,EAAA,CAAA+G,iBAAA,CAAAR,OAAA,CAAAS,iBAAA,CAAAvF,WAAA,CAAAC,SAAA,EAA0C;IAE7C1B,EAAA,CAAAK,SAAA,GAA0C;IAA1CL,EAAA,CAAAkC,UAAA,UAAAT,WAAA,CAAAO,MAAA,kBAAAP,WAAA,CAAAO,MAAA,CAAAC,EAAA,MAAAsE,OAAA,CAAA1D,aAAA,CAA0C;;;;;IA+BrD7C,EAAA,CAAAE,cAAA,eAGC;IACCF,EAAA,CAAAC,SAAA,eASE;IACFD,EAAA,CAAAE,cAAA,eAOC;IAEGF,EAAA,CAAAC,SAAA,eAQO;IAmBTD,EAAA,CAAAI,YAAA,EAAM;;;;IA7CNJ,EAAA,CAAAK,SAAA,GAAqE;IAArEL,EAAA,CAAAkC,UAAA,SAAA+E,OAAA,CAAAzG,gBAAA,kBAAAyG,OAAA,CAAAzG,gBAAA,CAAA2B,KAAA,yCAAAnC,EAAA,CAAAoC,aAAA,CAAqE,QAAA6E,OAAA,CAAAzG,gBAAA,kBAAAyG,OAAA,CAAAzG,gBAAA,CAAAc,QAAA;;;;;IAhT3EtB,EAAA,CAAAE,cAAA,cAGC;IACCF,EAAA,CAAAyD,UAAA,IAAAyD,mDAAA,6BAoSe;IAGflH,EAAA,CAAAyD,UAAA,IAAA0D,0CAAA,kBAoDM;IACRnH,EAAA,CAAAI,YAAA,EAAM;;;;IA1VuBJ,EAAA,CAAAK,SAAA,GACZ;IADYL,EAAA,CAAAkC,UAAA,YAAAkF,OAAA,CAAAC,QAAA,CACZ,iBAAAD,OAAA,CAAAE,gBAAA;IAqSZtH,EAAA,CAAAK,SAAA,GAAuB;IAAvBL,EAAA,CAAAkC,UAAA,SAAAkF,OAAA,CAAAG,iBAAA,CAAuB;;;;;IA8ItBvH,EAAA,CAAAC,SAAA,eAYO;;;;;IA4DTD,EAAA,CAAAC,SAAA,aAA4D;;;;;IAC5DD,EAAA,CAAAC,SAAA,eAUO;;;;;;IAmCLD,EAAA,CAAAE,cAAA,kBAeC;IAbCF,EAAA,CAAAY,UAAA,mBAAA4G,sEAAA;MAAA,MAAA/B,WAAA,GAAAzF,EAAA,CAAAc,aAAA,CAAA2G,IAAA;MAAA,MAAAC,SAAA,GAAAjC,WAAA,CAAA5D,SAAA;MAAA,MAAA8F,OAAA,GAAA3H,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAmB,WAAA,CAAAwG,OAAA,CAAAC,WAAA,CAAAF,SAAA,CAAkB;IAAA,EAAC;IAc5B1H,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;IAHPJ,EAAA,CAAAkC,UAAA,UAAAwF,SAAA,CAAAG,IAAA,CAAoB;IAEpB7H,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAoH,SAAA,CAAAI,KAAA,MACF;;;;;IA/CN9H,EAAA,CAAAE,cAAA,eAeC;IAUKF,EAAA,CAAAG,MAAA,oBACF;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAE,cAAA,eAEC;IACCF,EAAA,CAAAyD,UAAA,IAAAsE,6CAAA,sBAiBS;IACX/H,EAAA,CAAAI,YAAA,EAAM;;;;IAjBgBJ,EAAA,CAAAK,SAAA,GAA8C;IAA9CL,EAAA,CAAAkC,UAAA,YAAA8F,OAAA,CAAAC,oBAAA,CAAAD,OAAA,CAAAE,qBAAA,EAA8C;;;;;;IAsBxElI,EAAA,CAAAE,cAAA,eAaC;IAUKF,EAAA,CAAAG,MAAA,4BACF;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAE,cAAA,eAMC;IAGGF,EAAA,CAAAY,UAAA,mBAAAuH,6DAAA;MAAAnI,EAAA,CAAAc,aAAA,CAAAsH,IAAA;MAAA,MAAAC,OAAA,GAAArI,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAmB,WAAA,CAAAkH,OAAA,CAAAC,gBAAA,CAAiB,OAAO,CAAC;IAAA,EAAC;IAgBnCtI,EAAA,CAAAE,cAAA,eAUC;IACCF,EAAA,CAAAC,SAAA,aAGK;IACPD,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,gBACG;IAAAF,EAAA,CAAAG,MAAA,aAAM;IAAAH,EAAA,CAAAI,YAAA,EACR;IAIHJ,EAAA,CAAAE,cAAA,mBAgBC;IAfCF,EAAA,CAAAY,UAAA,mBAAA2H,8DAAA;MAAAvI,EAAA,CAAAc,aAAA,CAAAsH,IAAA;MAAA,MAAAI,OAAA,GAAAxI,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAmB,WAAA,CAAAqH,OAAA,CAAAF,gBAAA,CAAiB,UAAU,CAAC;IAAA,EAAC;IAgBtCtI,EAAA,CAAAE,cAAA,gBAUC;IACCF,EAAA,CAAAC,SAAA,cAGK;IACPD,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,iBACG;IAAAF,EAAA,CAAAG,MAAA,iBAAS;IAAAH,EAAA,CAAAI,YAAA,EACX;IAIHJ,EAAA,CAAAE,cAAA,mBAgBC;IAfCF,EAAA,CAAAY,UAAA,mBAAA6H,8DAAA;MAAAzI,EAAA,CAAAc,aAAA,CAAAsH,IAAA;MAAA,MAAAM,OAAA,GAAA1I,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAmB,WAAA,CAAAuH,OAAA,CAAAC,UAAA,EAAY;IAAA,EAAC;IAgBtB3I,EAAA,CAAAE,cAAA,gBAUC;IACCF,EAAA,CAAAC,SAAA,cAGK;IACPD,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,iBACG;IAAAF,EAAA,CAAAG,MAAA,mBAAM;IAAAH,EAAA,CAAAI,YAAA,EACR;;;;;;IAkBXJ,EAAA,CAAAE,cAAA,eAYC;IADCF,EAAA,CAAAY,UAAA,mBAAAgI,0DAAA;MAAA5I,EAAA,CAAAc,aAAA,CAAA+H,IAAA;MAAA,MAAAC,OAAA,GAAA9I,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAmB,WAAA,CAAA2H,OAAA,CAAAC,aAAA,EAAe;IAAA,EAAC;IAC1B/I,EAAA,CAAAI,YAAA,EAAM;;;;;IAgDDJ,EAAA,CAAAC,SAAA,eAWO;;;;;IAHLD,EAAA,CAAAqC,WAAA,WAAA2G,QAAA,OAAwB,uDAAAC,KAAA;;;;;;IArDhCjJ,EAAA,CAAAE,cAAA,eAmBC;IAcGF,EAAA,CAAAC,SAAA,aAAyD;IAC3DD,EAAA,CAAAI,YAAA,EAAM;IAGNJ,EAAA,CAAAE,cAAA,eAAqB;IAGjBF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAGNJ,EAAA,CAAAE,cAAA,eAAwE;IACtEF,EAAA,CAAAyD,UAAA,IAAAyF,0CAAA,mBAWO;IACTlJ,EAAA,CAAAI,YAAA,EAAM;IAGNJ,EAAA,CAAAE,cAAA,eAA4D;IAC1DF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAIRJ,EAAA,CAAAE,cAAA,eAAqC;IAGjCF,EAAA,CAAAY,UAAA,mBAAAuI,8DAAAhG,MAAA;MAAAnD,EAAA,CAAAc,aAAA,CAAAsI,IAAA;MAAA,MAAAC,OAAA,GAAArJ,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAmB,WAAA,CAAAkI,OAAA,CAAAC,cAAA,CAAAnG,MAAA,CAAsB;IAAA,EAAC;IAkBhCnD,EAAA,CAAAC,SAAA,cAAoD;IACtDD,EAAA,CAAAI,YAAA,EAAS;IAGTJ,EAAA,CAAAE,cAAA,mBAkBC;IAjBCF,EAAA,CAAAY,UAAA,mBAAA2I,8DAAApG,MAAA;MAAAnD,EAAA,CAAAc,aAAA,CAAAsI,IAAA;MAAA,MAAAI,OAAA,GAAAxJ,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAmB,WAAA,CAAAqI,OAAA,CAAAC,WAAA,CAAAtG,MAAA,CAAmB;IAAA,EAAC;IAkB7BnD,EAAA,CAAAC,SAAA,cAA0D;IAC5DD,EAAA,CAAAI,YAAA,EAAS;;;;IAvEPJ,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAoJ,OAAA,CAAAC,uBAAA,CAAAD,OAAA,CAAAE,sBAAA,OACF;IAKqB5J,EAAA,CAAAK,SAAA,GAAe;IAAfL,EAAA,CAAAkC,UAAA,YAAAwH,OAAA,CAAA3E,UAAA,CAAe;IAelC/E,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,cAAAoJ,OAAA,CAAAG,kBAAA,QACF;;;AD50CN,OAAM,MAAOC,oBAAoB;EA8I/BC,YACUC,EAAe,EACfC,KAAqB,EACrBC,MAAc,EACdC,cAA8B,EAC9BC,WAAwB,EACxBC,YAA0B,EAC1BC,GAAsB;IANtB,KAAAN,EAAE,GAAFA,EAAE;IACF,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,GAAG,GAAHA,GAAG;IAvIb;IACA,KAAAC,YAAY,GAAQ,IAAI;IACxB,KAAAlD,QAAQ,GAAU,EAAE;IACpB,KAAAxE,aAAa,GAAkB,IAAI;IACnC,KAAA2H,eAAe,GAAG,KAAK;IAEvB,KAAAhK,gBAAgB,GAAQ,IAAI;IAE5B;IACA,KAAAiK,SAAS,GAAG,KAAK;IACjB,KAAAC,aAAa,GAAG,KAAK;IACrB,KAAAC,eAAe,GAAG,IAAI;IACtB,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAC,kBAAkB,GAAG,KAAK;IAC1B,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAC,WAAW,GAAG,EAAE;IAChB,KAAAC,aAAa,GAAU,EAAE;IACzB,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAC,gBAAgB,GAAG,KAAK;IACxB,KAAA3D,iBAAiB,GAAG,KAAK;IACzB,KAAAnG,YAAY,GAAG,KAAK;IACpB,KAAA+J,sBAAsB,GAAG,KAAK;IAC9B,KAAAC,eAAe,GAAQ,IAAI;IAC3B,KAAAC,mBAAmB,GAAG;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAC,CAAE;IACpC,KAAAC,kBAAkB,GAAG,KAAK;IAC1B,KAAAC,qBAAqB,GAAQ,IAAI;IAEjC,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAC,aAAa,GAAQ,IAAI;IACzB,KAAAC,cAAc,GAAG,CAAC;IAClB,KAAAC,WAAW,GAAG,KAAK;IACnB,KAAAC,UAAU,GAAG,KAAK;IAElB;IACA,KAAAC,gBAAgB,GAAG,KAAK;IACxB,KAAAnC,sBAAsB,GAAG,CAAC;IAC1B,KAAAoC,mBAAmB,GAAwC,MAAM;IACzD,KAAAC,aAAa,GAAyB,IAAI;IAC1C,KAAAC,WAAW,GAAW,EAAE;IACxB,KAAAC,cAAc,GAAQ,IAAI;IAClC,KAAApH,UAAU,GAAa,CACrB,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CACzD;IAED;IACQ,KAAAqH,YAAY,GAA4B,IAAI;IAC5C,KAAAC,gBAAgB,GAAkB,IAAI;IACtC,KAAAC,aAAa,GAOjB,EAAE;IAEN;IACA,KAAAC,QAAQ,GAAG,KAAK;IAChB,KAAAC,QAAQ,GAA6B,IAAI;IACzC,KAAAC,YAAY,GAAG,CAAC;IACR,KAAAC,SAAS,GAAQ,IAAI;IAE7B;IACA;IAEA;IACA,KAAAC,eAAe,GAAU,CACvB;MACE1K,EAAE,EAAE,SAAS;MACb4F,IAAI,EAAE,SAAS;MACf+E,IAAI,EAAE,IAAI;MACVC,MAAM,EAAE,CACN;QAAE/E,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAe,CAAE,EACtC;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAA6B,CAAE,EACpD;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAiC,CAAE,EACxD;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAgC,CAAE,EACvD;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAyB,CAAE,EAChD;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAA0B,CAAE,EACjD;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAwB,CAAE,EAC/C;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAA+B,CAAE,EACtD;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAgC,CAAE,EACvD;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAwB,CAAE;KAElD,EACD;MACE5F,EAAE,EAAE,QAAQ;MACZ4F,IAAI,EAAE,QAAQ;MACd+E,IAAI,EAAE,IAAI;MACVC,MAAM,EAAE,CACN;QAAE/E,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAM,CAAE,EAC7B;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAO,CAAE,EAC9B;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAK,CAAE,EAC5B;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAM,CAAE,EAC7B;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAQ,CAAE,EAC/B;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAK,CAAE,EAC5B;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAO,CAAE,EAC9B;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAS,CAAE,EAChC;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAW,CAAE;KAErC,EACD;MACE5F,EAAE,EAAE,QAAQ;MACZ4F,IAAI,EAAE,QAAQ;MACd+E,IAAI,EAAE,IAAI;MACVC,MAAM,EAAE,CACN;QAAE/E,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAU,CAAE,EACjC;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAU,CAAE,EACjC;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAY,CAAE,EACnC;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAS,CAAE,EAChC;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAa,CAAE,EACpC;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAK,CAAE,EAC5B;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAM,CAAE,EAC7B;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAO,CAAE;KAEjC,CACF;IACD,KAAAK,qBAAqB,GAAG,IAAI,CAACyE,eAAe,CAAC,CAAC,CAAC;IAE/C;IACiB,KAAAG,oBAAoB,GAAG,EAAE;IAClC,KAAAC,WAAW,GAAG,CAAC;IAEvB;IACA,KAAAC,QAAQ,GAAG,KAAK;IAChB,KAAAC,YAAY,GAAG,KAAK;IACZ,KAAAC,aAAa,GAAQ,IAAI;IACzB,KAAAC,aAAa,GAAG,IAAIrN,YAAY,EAAE;IAWxC,IAAI,CAACsN,WAAW,GAAG,IAAI,CAACpD,EAAE,CAACqD,KAAK,CAAC;MAC/B3K,OAAO,EAAE,CAAC,EAAE,EAAE,CAAC7C,UAAU,CAACyN,QAAQ,EAAEzN,UAAU,CAAC0N,SAAS,CAAC,CAAC,CAAC,CAAC;KAC7D,CAAC;EACJ;EAEA;EACAC,eAAeA,CAAA;IACb,OACE,CAAC,IAAI,CAAChN,gBAAgB,IAAI,IAAI,CAACuL,gBAAgB,IAAI,IAAI,CAACb,gBAAgB;EAE5E;EAEA;EACQuC,gBAAgBA,CAAA;IACtB,MAAMC,cAAc,GAAG,IAAI,CAACN,WAAW,CAACO,GAAG,CAAC,SAAS,CAAC;IACtD,IAAI,IAAI,CAACH,eAAe,EAAE,EAAE;MAC1BE,cAAc,EAAEE,OAAO,EAAE;KAC1B,MAAM;MACLF,cAAc,EAAEG,MAAM,EAAE;;EAE5B;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACC,mBAAmB,EAAE;IAE1B;IACA,IAAI,CAACC,8BAA8B,EAAE;EACvC;EAEAC,eAAeA,CAAA;IACb;IACAC,UAAU,CAAC,MAAK;MACd,IAAI,CAACC,kBAAkB,EAAE;IAC3B,CAAC,EAAE,GAAG,CAAC;IAEP;IACAD,UAAU,CAAC,MAAK;MACd,IAAI,CAACC,kBAAkB,EAAE;IAC3B,CAAC,EAAE,GAAG,CAAC;IAEPD,UAAU,CAAC,MAAK;MACd,IAAI,CAACC,kBAAkB,EAAE;IAC3B,CAAC,EAAE,IAAI,CAAC;IAERD,UAAU,CAAC,MAAK;MACd,IAAI,CAACC,kBAAkB,EAAE;IAC3B,CAAC,EAAE,IAAI,CAAC;EACV;EAEA;;;EAGQA,kBAAkBA,CAAA;IACxB;IACA,IAAI,IAAI,CAACC,UAAU,IAAI,IAAI,CAACC,WAAW,EAAE;MACvC,IAAI,CAACjE,WAAW,CAACkE,gBAAgB,CAC/B,IAAI,CAACF,UAAU,CAACG,aAAa,EAC7B,IAAI,CAACF,WAAW,CAACE,aAAa,CAC/B;;IAEH;IAAA,KACK,IAAI,IAAI,CAACC,gBAAgB,IAAI,IAAI,CAACC,iBAAiB,EAAE;MACxD,IAAI,CAACrE,WAAW,CAACkE,gBAAgB,CAC/B,IAAI,CAACE,gBAAgB,CAACD,aAAa,EACnC,IAAI,CAACE,iBAAiB,CAACF,aAAa,CACrC;KACF,MAAM;MACL,IAAI,CAACG,2BAA2B,EAAE;MAElC;MACAR,UAAU,CAAC,MAAK;QACd,IAAI,CAACC,kBAAkB,EAAE;MAC3B,CAAC,EAAE,GAAG,CAAC;MAEP;MACAD,UAAU,CAAC,MAAK;QACd,IAAI,CAACC,kBAAkB,EAAE;MAC3B,CAAC,EAAE,IAAI,CAAC;;EAEZ;EAEA;;;EAGQO,2BAA2BA,CAAA;IACjC;IACA,MAAMC,YAAY,GAAGC,QAAQ,CAACC,cAAc,CAC1C,YAAY,CACO;IACrB,MAAMC,aAAa,GAAGF,QAAQ,CAACC,cAAc,CAC3C,aAAa,CACM;IAErB,IAAIF,YAAY,IAAIG,aAAa,EAAE;MACjCC,OAAO,CAACC,GAAG,CACT,6DAA6D,CAC9D;MACD,IAAI,CAAC5E,WAAW,CAACkE,gBAAgB,CAACK,YAAY,EAAEG,aAAa,CAAC;KAC/D,MAAM;MACLC,OAAO,CAACE,IAAI,CAAC,yDAAyD,CAAC;MAEvE;MACA,MAAMb,UAAU,GAAGQ,QAAQ,CAACM,aAAa,CAAC,OAAO,CAAC;MAClDd,UAAU,CAACnM,EAAE,GAAG,YAAY;MAC5BmM,UAAU,CAACe,QAAQ,GAAG,IAAI;MAC1Bf,UAAU,CAACgB,KAAK,GAAG,IAAI;MACvBhB,UAAU,CAACiB,WAAW,GAAG,IAAI;MAC7BjB,UAAU,CAACkB,KAAK,CAACC,OAAO,GACtB,2EAA2E;MAE7E,MAAMlB,WAAW,GAAGO,QAAQ,CAACM,aAAa,CAAC,OAAO,CAAC;MACnDb,WAAW,CAACpM,EAAE,GAAG,aAAa;MAC9BoM,WAAW,CAACc,QAAQ,GAAG,IAAI;MAC3Bd,WAAW,CAACgB,WAAW,GAAG,IAAI;MAC9BhB,WAAW,CAACiB,KAAK,CAACC,OAAO,GACvB,2EAA2E;MAE7EX,QAAQ,CAACY,IAAI,CAACC,WAAW,CAACrB,UAAU,CAAC;MACrCQ,QAAQ,CAACY,IAAI,CAACC,WAAW,CAACpB,WAAW,CAAC;MAEtC,IAAI,CAACjE,WAAW,CAACkE,gBAAgB,CAACF,UAAU,EAAEC,WAAW,CAAC;;EAE9D;EAEQL,8BAA8BA,CAAA;IACpC,MAAM0B,YAAY,GAAGA,CAAA,KAAK;MACxB,IAAI,CAACtF,WAAW,CAACsF,YAAY,EAAE;MAC/Bd,QAAQ,CAACe,mBAAmB,CAAC,OAAO,EAAED,YAAY,CAAC;MACnDd,QAAQ,CAACe,mBAAmB,CAAC,SAAS,EAAED,YAAY,CAAC;MACrDd,QAAQ,CAACe,mBAAmB,CAAC,YAAY,EAAED,YAAY,CAAC;IAC1D,CAAC;IAEDd,QAAQ,CAACgB,gBAAgB,CAAC,OAAO,EAAEF,YAAY,EAAE;MAAEG,IAAI,EAAE;IAAI,CAAE,CAAC;IAChEjB,QAAQ,CAACgB,gBAAgB,CAAC,SAAS,EAAEF,YAAY,EAAE;MAAEG,IAAI,EAAE;IAAI,CAAE,CAAC;IAClEjB,QAAQ,CAACgB,gBAAgB,CAAC,YAAY,EAAEF,YAAY,EAAE;MAAEG,IAAI,EAAE;IAAI,CAAE,CAAC;EACvE;EAEQ9B,mBAAmBA,CAAA;IACzB,IAAI,CAAC+B,eAAe,EAAE;IACtB,IAAI,CAACC,gBAAgB,EAAE;IACvB,IAAI,CAACC,sBAAsB,EAAE;EAC/B;EAEQA,sBAAsBA,CAAA;IAC5B;IACA;IACAjB,OAAO,CAACC,GAAG,CAAC,sDAAsD,CAAC;EACrE;EAEQiB,kBAAkBA,CAACC,YAA0B;IACnD;IACA;IACAnB,OAAO,CAACC,GAAG,CACT,iCAAiC,EACjCkB,YAAY,CAACC,MAAM,CAAC7O,QAAQ,CAC7B;IAED;IACA,IAAI,CAAC6I,cAAc,CAACiG,IAAI,CAAC,UAAU,CAAC;IAEpC;IACA;IACA;EACF;;EAEQN,eAAeA,CAAA;IACrB,IAAI;MACF,MAAMO,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;MAE/C,IAAI,CAACF,UAAU,IAAIA,UAAU,KAAK,MAAM,IAAIA,UAAU,KAAK,WAAW,EAAE;QACtEtB,OAAO,CAACyB,KAAK,CAAC,gCAAgC,CAAC;QAC/C,IAAI,CAAC3N,aAAa,GAAG,IAAI;QACzB,IAAI,CAAC2H,eAAe,GAAG,KAAK;QAC5B;;MAGF,MAAMiG,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACN,UAAU,CAAC;MAEnC;MACA,MAAMO,MAAM,GAAGH,IAAI,CAACI,GAAG,IAAIJ,IAAI,CAACxO,EAAE,IAAIwO,IAAI,CAACG,MAAM;MAEjD,IAAIA,MAAM,EAAE;QACV,IAAI,CAAC/N,aAAa,GAAG+N,MAAM;QAC3B,IAAI,CAACpG,eAAe,GAAGiG,IAAI,CAACnP,QAAQ,IAAImP,IAAI,CAAC5I,IAAI,IAAI,KAAK;OAC3D,MAAM;QACLkH,OAAO,CAACyB,KAAK,CAAC,0CAA0C,EAAEC,IAAI,CAAC;QAC/D,IAAI,CAAC5N,aAAa,GAAG,IAAI;QACzB,IAAI,CAAC2H,eAAe,GAAG,KAAK;;KAE/B,CAAC,OAAOgG,KAAK,EAAE;MACdzB,OAAO,CAACyB,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;MAC/D,IAAI,CAAC3N,aAAa,GAAG,IAAI;MACzB,IAAI,CAAC2H,eAAe,GAAG,KAAK;;EAEhC;EAEQuF,gBAAgBA,CAAA;IACtB,MAAMe,cAAc,GAAG,IAAI,CAAC7G,KAAK,CAAC8G,QAAQ,CAACC,QAAQ,CAACrD,GAAG,CAAC,IAAI,CAAC;IAE7D,IAAI,CAACmD,cAAc,EAAE;MACnB,IAAI,CAACzG,YAAY,CAAC4G,SAAS,CAAC,6BAA6B,CAAC;MAC1D;;IAGF,IAAI,CAACxG,SAAS,GAAG,IAAI;IAErB;IACA,IAAI,CAACyG,oBAAoB,EAAE;IAE3B,IAAI,CAAC/G,cAAc,CAACgH,eAAe,CAACL,cAAc,CAAC,CAACM,SAAS,CAAC;MAC5DC,IAAI,EAAG9G,YAAY,IAAI;QACrB,IAAI,CAACA,YAAY,GAAGA,YAAY;QAChC,IAAI,CAAC+G,mBAAmB,EAAE;QAC1B,IAAI,CAACC,YAAY,EAAE;QAEnB;QACA,IAAI,CAACC,kBAAkB,EAAE;QACzB,IAAI,CAAC/G,SAAS,GAAG,KAAK;MACxB,CAAC;MACD+F,KAAK,EAAGA,KAAK,IAAI;QACfzB,OAAO,CAACyB,KAAK,CAAC,+CAA+C,EAAEA,KAAK,CAAC;QACrE,IAAI,CAACnG,YAAY,CAAC4G,SAAS,CACzB,8CAA8C,CAC/C;QACD,IAAI,CAACxG,SAAS,GAAG,KAAK;QAEtB;QACAyD,UAAU,CAAC,MAAK;UACd,IAAI,CAAC6B,gBAAgB,EAAE;QACzB,CAAC,EAAE,IAAI,CAAC;MACV;KACD,CAAC;EACJ;EAEQuB,mBAAmBA,CAAA;IACzB,IACE,CAAC,IAAI,CAAC/G,YAAY,EAAEkH,YAAY,IAChC,IAAI,CAAClH,YAAY,CAACkH,YAAY,CAACC,MAAM,KAAK,CAAC,EAC3C;MACA3C,OAAO,CAACE,IAAI,CAAC,uCAAuC,CAAC;MACrD,IAAI,CAACzO,gBAAgB,GAAG,IAAI;MAC5B;;IAGF;IACA;IAEA,IAAI,IAAI,CAAC+J,YAAY,CAACoH,OAAO,EAAE;MAC7B;MACA;MACA,IAAI,CAACnR,gBAAgB,GAAG,IAAI,CAAC+J,YAAY,CAACkH,YAAY,CAACG,IAAI,CAAEC,CAAM,IAAI;QACrE,MAAMC,aAAa,GAAGD,CAAC,CAAC5P,EAAE,IAAI4P,CAAC,CAAChB,GAAG;QACnC,OAAOkB,MAAM,CAACD,aAAa,CAAC,KAAKC,MAAM,CAAC,IAAI,CAAClP,aAAa,CAAC;MAC7D,CAAC,CAAC;KACH,MAAM;MACL;MACA,IAAI,CAACrC,gBAAgB,GAAG,IAAI,CAAC+J,YAAY,CAACkH,YAAY,CAACG,IAAI,CAAEC,CAAM,IAAI;QACrE,MAAMC,aAAa,GAAGD,CAAC,CAAC5P,EAAE,IAAI4P,CAAC,CAAChB,GAAG;QACnC9B,OAAO,CAACC,GAAG,CACT,2BAA2B,EAC3B8C,aAAa,EACb,uBAAuB,EACvB,IAAI,CAACjP,aAAa,CACnB;QACD,OAAOkP,MAAM,CAACD,aAAa,CAAC,KAAKC,MAAM,CAAC,IAAI,CAAClP,aAAa,CAAC;MAC7D,CAAC,CAAC;;IAGJ;IACA,IAAI,CAAC,IAAI,CAACrC,gBAAgB,IAAI,IAAI,CAAC+J,YAAY,CAACkH,YAAY,CAACC,MAAM,GAAG,CAAC,EAAE;MACvE,IAAI,CAAClR,gBAAgB,GAAG,IAAI,CAAC+J,YAAY,CAACkH,YAAY,CAAC,CAAC,CAAC;MAEzD;MACA,IAAI,IAAI,CAAClH,YAAY,CAACkH,YAAY,CAACC,MAAM,GAAG,CAAC,EAAE;QAC7C,MAAMM,kBAAkB,GACtB,IAAI,CAACxR,gBAAgB,CAACyB,EAAE,IAAI,IAAI,CAACzB,gBAAgB,CAACqQ,GAAG;QACvD,IAAIkB,MAAM,CAACC,kBAAkB,CAAC,KAAKD,MAAM,CAAC,IAAI,CAAClP,aAAa,CAAC,EAAE;UAC7DkM,OAAO,CAACC,GAAG,CACT,6DAA6D,CAC9D;UACD,IAAI,CAACxO,gBAAgB,GAAG,IAAI,CAAC+J,YAAY,CAACkH,YAAY,CAAC,CAAC,CAAC;;;;IAK/D;IACA,IAAI,IAAI,CAACjR,gBAAgB,EAAE;MACzB;MACAuO,OAAO,CAACC,GAAG,CACT,qCAAqC,EACrC,IAAI,CAACxO,gBAAgB,CAACc,QAAQ,CAC/B;MACDyN,OAAO,CAACC,GAAG,CACT,+BAA+B,EAC/B,IAAI,CAACxO,gBAAgB,CAACc,QAAQ,CAC/B;KACF,MAAM;MACLyN,OAAO,CAACyB,KAAK,CAAC,uDAAuD,CAAC;MAEtE;;IAGF;IACA,IAAI,CAAC/C,gBAAgB,EAAE;EACzB;EAEQ8D,YAAYA,CAAA;IAClB,IAAI,CAAC,IAAI,CAAChH,YAAY,EAAEtI,EAAE,EAAE;IAE5B;IACA,IAAIoF,QAAQ,GAAG,IAAI,CAACkD,YAAY,CAAClD,QAAQ,IAAI,EAAE;IAE/C;IACA,IAAI,CAACA,QAAQ,GAAGA,QAAQ,CAAC4K,IAAI,CAAC,CAACC,CAAM,EAAEC,CAAM,KAAI;MAC/C,MAAMC,KAAK,GAAG,IAAIC,IAAI,CAACH,CAAC,CAACxQ,SAAS,IAAIwQ,CAAC,CAACI,SAAS,CAAC,CAACC,OAAO,EAAE;MAC5D,MAAMC,KAAK,GAAG,IAAIH,IAAI,CAACF,CAAC,CAACzQ,SAAS,IAAIyQ,CAAC,CAACG,SAAS,CAAC,CAACC,OAAO,EAAE;MAC5D,OAAOH,KAAK,GAAGI,KAAK,CAAC,CAAC;IACxB,CAAC,CAAC;;IAEFzD,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE;MAC5CyD,KAAK,EAAE,IAAI,CAACpL,QAAQ,CAACqK,MAAM;MAC3BgB,KAAK,EAAE,IAAI,CAACrL,QAAQ,CAAC,CAAC,CAAC,EAAE3E,OAAO;MAChCiQ,IAAI,EAAE,IAAI,CAACtL,QAAQ,CAAC,IAAI,CAACA,QAAQ,CAACqK,MAAM,GAAG,CAAC,CAAC,EAAEhP;KAChD,CAAC;IAEF,IAAI,CAACiI,eAAe,GAAG,IAAI,CAACtD,QAAQ,CAACqK,MAAM,KAAK,IAAI,CAAC5E,oBAAoB;IACzE,IAAI,CAACrC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACmI,cAAc,EAAE;EACvB;EAEAC,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAACnI,aAAa,IAAI,CAAC,IAAI,CAACC,eAAe,IAAI,CAAC,IAAI,CAACJ,YAAY,EAAEtI,EAAE,EACvE;IAEF,IAAI,CAACyI,aAAa,GAAG,IAAI;IACzB,IAAI,CAACqC,WAAW,EAAE;IAElB;IACA,MAAM+F,MAAM,GAAG,IAAI,CAACzL,QAAQ,CAACqK,MAAM;IAEnC,IAAI,CAACvH,cAAc,CAAC4I,WAAW,CAC7B,IAAI,CAAClQ,aAAc;IAAE;IACrB,IAAI,CAACrC,gBAAgB,EAAEyB,EAAE,IAAI,IAAI,CAACzB,gBAAgB,EAAEqQ,GAAI;IAAE;IAC1D,IAAI,CAACtG,YAAY,CAACtI,EAAE,EACpB,IAAI,CAAC8K,WAAW,EAChB,IAAI,CAACD,oBAAoB,CAC1B,CAACsE,SAAS,CAAC;MACVC,IAAI,EAAG2B,WAAkB,IAAI;QAC3B,IAAIA,WAAW,IAAIA,WAAW,CAACtB,MAAM,GAAG,CAAC,EAAE;UACzC;UACA,IAAI,CAACrK,QAAQ,GAAG,CAAC,GAAG2L,WAAW,CAACC,OAAO,EAAE,EAAE,GAAG,IAAI,CAAC5L,QAAQ,CAAC;UAC5D,IAAI,CAACsD,eAAe,GAClBqI,WAAW,CAACtB,MAAM,KAAK,IAAI,CAAC5E,oBAAoB;SACnD,MAAM;UACL,IAAI,CAACnC,eAAe,GAAG,KAAK;;QAE9B,IAAI,CAACD,aAAa,GAAG,KAAK;MAC5B,CAAC;MACD8F,KAAK,EAAGA,KAAK,IAAI;QACfzB,OAAO,CAACyB,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;QAC/D,IAAI,CAACnG,YAAY,CAAC4G,SAAS,CAAC,wCAAwC,CAAC;QACrE,IAAI,CAACvG,aAAa,GAAG,KAAK;QAC1B,IAAI,CAACqC,WAAW,EAAE,CAAC,CAAC;MACtB;KACD,CAAC;EACJ;EAEA;;;EAGQmE,oBAAoBA,CAAA;IAC1B,IAAI,CAAC/D,aAAa,CAAC+F,WAAW,EAAE;IAChC,IAAI,CAAC/F,aAAa,GAAG,IAAIrN,YAAY,EAAE;EACzC;EAEA;;;EAGOqT,kBAAkBA,CAAA;IACvB,IAAI,IAAI,CAAC5I,YAAY,EAAEtI,EAAE,EAAE;MACzB;MACA,IAAI,CAACoF,QAAQ,GAAG,EAAE;MAClB,IAAI,CAAC0F,WAAW,GAAG,CAAC;MACpB,IAAI,CAACpC,eAAe,GAAG,IAAI;MAE3B;MACA,IAAI,CAACoF,gBAAgB,EAAE;;EAE3B;EAEQyB,kBAAkBA,CAAA;IACxB,IAAI,CAAC,IAAI,CAACjH,YAAY,EAAEtI,EAAE,EAAE;MAC1B8M,OAAO,CAACE,IAAI,CAAC,kDAAkD,CAAC;MAChE;;IAGFF,OAAO,CAACC,GAAG,CACT,yDAAyD,EACzD,IAAI,CAACzE,YAAY,CAACtI,EAAE,CACrB;IAED;IAEA,IAAI,CAACkL,aAAa,CAACiG,GAAG,CACpB,IAAI,CAACjJ,cAAc,CAACkJ,sBAAsB,CACxC,IAAI,CAAC9I,YAAY,CAACtI,EAAE,CACrB,CAACmP,SAAS,CAAC;MACVC,IAAI,EAAGiC,UAAe,IAAI;QACxBvE,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE;UACnC/M,EAAE,EAAEqR,UAAU,CAACrR,EAAE;UACjBsR,IAAI,EAAED,UAAU,CAACC,IAAI;UACrB7Q,OAAO,EAAE4Q,UAAU,CAAC5Q,OAAO;UAC3BV,MAAM,EAAEsR,UAAU,CAACtR,MAAM;UACzBwR,QAAQ,EAAEF,UAAU,CAACE,QAAQ;UAC7BC,UAAU,EAAEH,UAAU,CAACG,UAAU;UACjCC,WAAW,EAAEJ,UAAU,CAACI;SACzB,CAAC;QAEF;QACA3E,OAAO,CAACC,GAAG,CACT,mCAAmC,EACnC,IAAI,CAACnI,cAAc,CAACyM,UAAU,CAAC,CAChC;QAED,IAAIA,UAAU,CAACI,WAAW,EAAE;UAC1BJ,UAAU,CAACI,WAAW,CAACC,OAAO,CAAC,CAACC,GAAQ,EAAEC,KAAa,KAAI;YACzD9E,OAAO,CAACC,GAAG,CAAC,yBAAyB6E,KAAK,GAAG,EAAE;cAC7CN,IAAI,EAAEK,GAAG,CAACL,IAAI;cACdO,GAAG,EAAEF,GAAG,CAACE,GAAG;cACZC,IAAI,EAAEH,GAAG,CAACG,IAAI;cACdlM,IAAI,EAAE+L,GAAG,CAAC/L,IAAI;cACdmM,IAAI,EAAEJ,GAAG,CAACI;aACX,CAAC;UACJ,CAAC,CAAC;;QAGJ;QACA,MAAMC,aAAa,GAAG,IAAI,CAAC5M,QAAQ,CAAC6M,IAAI,CACrCC,GAAG,IAAKA,GAAG,CAAClS,EAAE,KAAKqR,UAAU,CAACrR,EAAE,CAClC;QACD,IAAI,CAACgS,aAAa,EAAE;UAClB;UACA,IAAI,CAAC5M,QAAQ,CAAC+M,IAAI,CAACd,UAAU,CAAC;UAC9BvE,OAAO,CAACC,GAAG,CACT,0CAA0C,EAC1C,IAAI,CAAC3H,QAAQ,CAACqK,MAAM,CACrB;UAED;UACA,IAAI,CAACpH,GAAG,CAAC+J,aAAa,EAAE;UAExB;UACAnG,UAAU,CAAC,MAAK;YACd,IAAI,CAAC0E,cAAc,EAAE;UACvB,CAAC,EAAE,EAAE,CAAC;UAEN;UACA,MAAMY,QAAQ,GAAGF,UAAU,CAACtR,MAAM,EAAEC,EAAE,IAAIqR,UAAU,CAACE,QAAQ;UAC7DzE,OAAO,CAACC,GAAG,CAAC,kDAAkD,EAAE;YAC9DwE,QAAQ;YACR3Q,aAAa,EAAE,IAAI,CAACA,aAAa;YACjCyR,gBAAgB,EAAEd,QAAQ,KAAK,IAAI,CAAC3Q;WACrC,CAAC;UAEF,IAAI2Q,QAAQ,IAAIA,QAAQ,KAAK,IAAI,CAAC3Q,aAAa,EAAE;YAC/C,IAAI,CAAC0R,iBAAiB,CAACjB,UAAU,CAACrR,EAAE,CAAC;;;MAG3C,CAAC;MACDuO,KAAK,EAAGA,KAAK,IAAI;QACfzB,OAAO,CAACyB,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;QACxD,IAAI,CAACnG,YAAY,CAAC4G,SAAS,CAAC,kCAAkC,CAAC;QAE/D;QACA/C,UAAU,CAAC,MAAK;UACd,IAAI,CAACsD,kBAAkB,EAAE;QAC3B,CAAC,EAAE,IAAI,CAAC;MACV;KACD,CAAC,CACH;IAED;IAEA,IAAI,CAACrE,aAAa,CAACiG,GAAG,CACpB,IAAI,CAACjJ,cAAc,CAACqK,0BAA0B,CAC5C,IAAI,CAACjK,YAAY,CAACtI,EAAE,CACrB,CAACmP,SAAS,CAAC;MACVC,IAAI,EAAGoD,UAAe,IAAI;QACxB;QACA,IAAIA,UAAU,CAAC7D,MAAM,KAAK,IAAI,CAAC/N,aAAa,EAAE;UAC5C,IAAI,CAAC0E,iBAAiB,GAAGkN,UAAU,CAACzH,QAAQ;UAC5C,IAAI,CAACC,YAAY,GAAGwH,UAAU,CAACzH,QAAQ,CAAC,CAAC;UACzC+B,OAAO,CAACC,GAAG,CACT,sCAAsC,EACtC,IAAI,CAACzH,iBAAiB,CACvB;UACD,IAAI,CAAC+C,GAAG,CAAC+J,aAAa,EAAE;;MAE5B,CAAC;MACD7D,KAAK,EAAGA,KAAU,IAAI;QACpBzB,OAAO,CAACyB,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACzD;KACD,CAAC,CACH;IAED;IACA,IAAI,CAACrD,aAAa,CAACiG,GAAG,CACpB,IAAI,CAACjJ,cAAc,CAACuK,8BAA8B,CAChD,IAAI,CAACnK,YAAY,CAACtI,EAAE,CACrB,CAACmP,SAAS,CAAC;MACVC,IAAI,EAAGsD,kBAAuB,IAAI;QAChC;QACA,IAAIA,kBAAkB,CAAC1S,EAAE,KAAK,IAAI,CAACsI,YAAY,CAACtI,EAAE,EAAE;UAClD,IAAI,CAACsI,YAAY,GAAG;YAAE,GAAG,IAAI,CAACA,YAAY;YAAE,GAAGoK;UAAkB,CAAE;UACnE,IAAI,CAACrK,GAAG,CAAC+J,aAAa,EAAE;;MAE5B,CAAC;MACD7D,KAAK,EAAGA,KAAU,IAAI;QACpBzB,OAAO,CAACyB,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;MAC/D;KACD,CAAC,CACH;EACH;EAEQ+D,iBAAiBA,CAACK,SAAiB;IACzC,IAAI,CAACzK,cAAc,CAACoK,iBAAiB,CAACK,SAAS,CAAC,CAACxD,SAAS,CAAC;MACzDC,IAAI,EAAEA,CAAA,KAAK,CAAE,CAAC;MACdb,KAAK,EAAGA,KAAK,IAAI;QACfzB,OAAO,CAACyB,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MAC1D;KACD,CAAC;EACJ;EAEA;EACAqE,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAACzH,WAAW,CAAC0H,KAAK,IAAI,CAAC,IAAI,CAACvK,YAAY,EAAEtI,EAAE,EAAE;IAEvD,MAAMS,OAAO,GAAG,IAAI,CAAC0K,WAAW,CAACO,GAAG,CAAC,SAAS,CAAC,EAAEoH,KAAK,EAAEC,IAAI,EAAE;IAC9D,IAAI,CAACtS,OAAO,EAAE;IAEd,MAAM+Q,UAAU,GAAG,IAAI,CAACjT,gBAAgB,EAAEyB,EAAE,IAAI,IAAI,CAACzB,gBAAgB,EAAEqQ,GAAG;IAE1E,IAAI,CAAC4C,UAAU,EAAE;MACf,IAAI,CAACpJ,YAAY,CAAC4G,SAAS,CAAC,0BAA0B,CAAC;MACvD;;IAGF;IACA,IAAI,CAAC/F,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACuC,gBAAgB,EAAE;IAEvBsB,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE;MACjCtM,OAAO;MACP+Q,UAAU;MACV3C,cAAc,EAAE,IAAI,CAACvG,YAAY,CAACtI;KACnC,CAAC;IAEF,IAAI,CAACkI,cAAc,CAAC0K,WAAW,CAC7BpB,UAAU,EACV/Q,OAAO,EACPuS,SAAS,EACT,MAAa,EACb,IAAI,CAAC1K,YAAY,CAACtI,EAAE,CACrB,CAACmP,SAAS,CAAC;MACVC,IAAI,EAAG6D,OAAY,IAAI;QACrB;QACA,MAAMjB,aAAa,GAAG,IAAI,CAAC5M,QAAQ,CAAC6M,IAAI,CACrCC,GAAG,IAAKA,GAAG,CAAClS,EAAE,KAAKiT,OAAO,CAACjT,EAAE,CAC/B;QACD,IAAI,CAACgS,aAAa,EAAE;UAClB,IAAI,CAAC5M,QAAQ,CAAC+M,IAAI,CAACc,OAAO,CAAC;UAC3BnG,OAAO,CAACC,GAAG,CACT,wCAAwC,EACxC,IAAI,CAAC3H,QAAQ,CAACqK,MAAM,CACrB;;QAGH;QACA,IAAI,CAACtE,WAAW,CAAC+H,KAAK,EAAE;QACxB,IAAI,CAACjK,gBAAgB,GAAG,KAAK;QAC7B,IAAI,CAACuC,gBAAgB,EAAE;QAEvB;QACA,IAAI,CAACnD,GAAG,CAAC+J,aAAa,EAAE;QACxBnG,UAAU,CAAC,MAAK;UACd,IAAI,CAAC0E,cAAc,EAAE;QACvB,CAAC,EAAE,EAAE,CAAC;MACR,CAAC;MACDpC,KAAK,EAAGA,KAAU,IAAI;QACpBzB,OAAO,CAACyB,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;QAC5D,IAAI,CAACnG,YAAY,CAAC4G,SAAS,CAAC,mCAAmC,CAAC;QAChE,IAAI,CAAC/F,gBAAgB,GAAG,KAAK;QAC7B,IAAI,CAACuC,gBAAgB,EAAE;MACzB;KACD,CAAC;EACJ;EAEAmF,cAAcA,CAAA;IACZ1E,UAAU,CAAC,MAAK;MACd,IAAI,IAAI,CAACkH,iBAAiB,EAAE;QAC1B,MAAMC,OAAO,GAAG,IAAI,CAACD,iBAAiB,CAAC7G,aAAa;QACpD8G,OAAO,CAACC,SAAS,GAAGD,OAAO,CAACE,YAAY;;IAE5C,CAAC,EAAE,GAAG,CAAC;EACT;EAEA;EACA7U,gBAAgBA,CAACC,UAAgC;IAC/C,IAAI,CAACA,UAAU,EAAE,OAAO,YAAY;IAEpC,MAAM6U,QAAQ,GAAGC,IAAI,CAACC,KAAK,CACzB,CAACrD,IAAI,CAACsD,GAAG,EAAE,GAAG,IAAItD,IAAI,CAAC1R,UAAU,CAAC,CAAC4R,OAAO,EAAE,IAAI,KAAK,CACtD;IAED,IAAIiD,QAAQ,GAAG,CAAC,EAAE,OAAO,aAAa;IACtC,IAAIA,QAAQ,GAAG,EAAE,EAAE,OAAO,UAAUA,QAAQ,MAAM;IAClD,IAAIA,QAAQ,GAAG,IAAI,EAAE,OAAO,UAAUC,IAAI,CAACC,KAAK,CAACF,QAAQ,GAAG,EAAE,CAAC,GAAG;IAClE,OAAO,UAAUC,IAAI,CAACC,KAAK,CAACF,QAAQ,GAAG,IAAI,CAAC,GAAG;EACjD;EAEA;EACAI,oBAAoBA,CAAChB,SAAiB;IACpC,OACE,IAAI,CAACtI,aAAa,CAACsI,SAAS,CAAC,IAAI;MAC/BiB,QAAQ,EAAE,CAAC;MACXC,QAAQ,EAAE,CAAC;MACXC,WAAW,EAAE,CAAC;MACdC,KAAK,EAAE;KACR;EAEL;EAEQC,oBAAoBA,CAC1BrB,SAAiB,EACjBsB,IAAkD;IAElD,IAAI,CAAC5J,aAAa,CAACsI,SAAS,CAAC,GAAG;MAC9B,GAAG,IAAI,CAACgB,oBAAoB,CAAChB,SAAS,CAAC;MACvC,GAAGsB;KACJ;EACH;EAEA;EAEA;EAEAC,cAAcA,CAAA;IACZ,IAAI,CAAC,IAAI,CAAC3V,gBAAgB,EAAEyB,EAAE,EAAE;MAC9B,IAAI,CAACoI,YAAY,CAAC4G,SAAS,CAAC,gCAAgC,CAAC;MAC7D;;IAGF,IAAI,CAACmF,YAAY,CAACrW,QAAQ,CAACsW,KAAK,CAAC;EACnC;EAEAC,cAAcA,CAAA;IACZ,IAAI,CAAC,IAAI,CAAC9V,gBAAgB,EAAEyB,EAAE,EAAE;MAC9B,IAAI,CAACoI,YAAY,CAAC4G,SAAS,CAAC,gCAAgC,CAAC;MAC7D;;IAGF;IACA,IAAI,CAAC9C,kBAAkB,EAAE;IAEzB,IAAI,CAACiI,YAAY,CAACrW,QAAQ,CAACwW,KAAK,CAAC;EACnC;EAEA;;;EAGAC,gBAAgBA,CAAA;IACd,IAAI,CAACC,OAAO,GAAG,CAAC,IAAI,CAACA,OAAO;IAE5B,IAAI,IAAI,CAACrM,WAAW,CAACsM,WAAW,EAAE;MAChC,IAAI,CAACtM,WAAW,CAACsM,WAAW,EAAE;;IAGhC,IAAI,CAACrM,YAAY,CAACsM,WAAW,CAC3B,IAAI,CAACF,OAAO,GAAG,kBAAkB,GAAG,mBAAmB,CACxD;EACH;EAEA;;;EAGAG,YAAYA,CAAA;IACV,IAAI,CAACC,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;IAC1C9H,OAAO,CAACC,GAAG,CACT,oBAAoB,EACpB,IAAI,CAAC6H,cAAc,GAAG,SAAS,GAAG,UAAU,CAC7C;IAED,IAAI,IAAI,CAACzM,WAAW,CAAC0M,WAAW,EAAE;MAChC,IAAI,CAAC1M,WAAW,CAAC0M,WAAW,EAAE;;IAGhC,IAAI,CAACzM,YAAY,CAACsM,WAAW,CAC3B,IAAI,CAACE,cAAc,GAAG,gBAAgB,GAAG,mBAAmB,CAC7D;EACH;EAEA;;;EAGAE,OAAOA,CAAA;IACL,IAAI,IAAI,CAACC,UAAU,EAAE;MACnB,IAAI,IAAI,CAAC5M,WAAW,CAAC2M,OAAO,EAAE;QAC5B,IAAI,CAAC3M,WAAW,CAAC2M,OAAO,CAAC,IAAI,CAACC,UAAU,CAAC/U,EAAE,CAAC,CAACmP,SAAS,CAAC;UACrDC,IAAI,EAAEA,CAAA,KAAK;YACT,IAAI,CAAC2F,UAAU,GAAG,IAAI;YACtB,IAAI,CAACC,eAAe,GAAG,KAAK;UAC9B,CAAC;UACDzG,KAAK,EAAGA,KAAK,IAAI;YACfzB,OAAO,CAACyB,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;YAC5C,IAAI,CAACnG,YAAY,CAAC4G,SAAS,CAAC,kCAAkC,CAAC;UACjE;SACD,CAAC;OACH,MAAM;QACL;QACA,IAAI,CAAC+F,UAAU,GAAG,IAAI;QACtB,IAAI,CAACC,eAAe,GAAG,KAAK;QAC5B,IAAI,CAAC5M,YAAY,CAACsM,WAAW,CAAC,eAAe,CAAC;;;EAGpD;EAEA;EACA;EAEA;EAEAO,cAAcA,CAACC,KAAa;IAC1B,IAAIA,KAAK,GAAG,IAAI,EAAE,OAAOA,KAAK,GAAG,IAAI;IACrC,IAAIA,KAAK,GAAG,OAAO,EAAE,OAAO1B,IAAI,CAAC2B,KAAK,CAACD,KAAK,GAAG,IAAI,CAAC,GAAG,KAAK;IAC5D,OAAO1B,IAAI,CAAC2B,KAAK,CAACD,KAAK,GAAG,OAAO,CAAC,GAAG,KAAK;EAC5C;EAEAE,YAAYA,CAACnC,OAAY;IACvB,MAAMoC,cAAc,GAAGpC,OAAO,CAACxB,WAAW,EAAE9B,IAAI,CAC7CgC,GAAQ,IAAK,CAACA,GAAG,CAACL,IAAI,EAAEgE,UAAU,CAAC,QAAQ,CAAC,CAC9C;IACD,IAAID,cAAc,EAAExD,GAAG,EAAE;MACvB,MAAM0D,IAAI,GAAG5I,QAAQ,CAACM,aAAa,CAAC,GAAG,CAAC;MACxCsI,IAAI,CAACC,IAAI,GAAGH,cAAc,CAACxD,GAAG;MAC9B0D,IAAI,CAACE,QAAQ,GAAGJ,cAAc,CAACzP,IAAI,IAAI,MAAM;MAC7C2P,IAAI,CAACG,MAAM,GAAG,QAAQ;MACtB/I,QAAQ,CAACY,IAAI,CAACC,WAAW,CAAC+H,IAAI,CAAC;MAC/BA,IAAI,CAACI,KAAK,EAAE;MACZhJ,QAAQ,CAACY,IAAI,CAACqI,WAAW,CAACL,IAAI,CAAC;MAC/B,IAAI,CAACnN,YAAY,CAACsM,WAAW,CAAC,wBAAwB,CAAC;;EAE3D;EAEA;EAEAzV,YAAYA,CAAA;IACV,IAAI,CAAC+J,UAAU,GAAG,CAAC,IAAI,CAACA,UAAU;IAClC,IAAI,CAACH,UAAU,GAAG,IAAI,CAACG,UAAU;EACnC;EAEA6M,cAAcA,CAAA;IACZ,IAAI,CAAC1W,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;EACxC;EAEA2W,qBAAqBA,CAAA;IACnB;IACA,IAAI,CAAC7N,MAAM,CACR8N,QAAQ,CAAC,CAAC,+BAA+B,CAAC,CAAC,CAC3CC,IAAI,CAAC,MAAK,CAAE,CAAC,CAAC,CACdC,KAAK,CAAE1H,KAAK,IAAI;MACfzB,OAAO,CAACyB,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3C;MACA,IAAI,CAACtG,MAAM,CAAC8N,QAAQ,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAACE,KAAK,CAAC,MAAK;QACnD;QACAC,MAAM,CAACC,QAAQ,CAACX,IAAI,GAAG,+BAA+B;MACxD,CAAC,CAAC;IACJ,CAAC,CAAC;EACN;EAEA;EAEA1O,aAAaA,CAAA;IACX,IAAI,CAAC6B,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACC,kBAAkB,GAAG,KAAK;IAC/B,IAAI,CAACzJ,YAAY,GAAG,KAAK;IACzB,IAAI,CAAC+J,sBAAsB,GAAG,KAAK;IACnC,IAAI,CAACK,kBAAkB,GAAG,KAAK;EACjC;EAEAzF,oBAAoBA,CAACmP,OAAY,EAAEmD,KAAiB;IAClDA,KAAK,CAACC,cAAc,EAAE;IACtB,IAAI,CAAClN,eAAe,GAAG8J,OAAO;IAC9B,IAAI,CAAC7J,mBAAmB,GAAG;MAAEC,CAAC,EAAE+M,KAAK,CAACE,OAAO;MAAEhN,CAAC,EAAE8M,KAAK,CAACG;IAAO,CAAE;IACjE,IAAI,CAACrN,sBAAsB,GAAG,IAAI;EACpC;EAEAsN,kBAAkBA,CAACvD,OAAY,EAAEmD,KAAiB;IAChDA,KAAK,CAACK,eAAe,EAAE;IACvB,IAAI,CAACjN,qBAAqB,GAAGyJ,OAAO;IACpC,IAAI,CAAC7J,mBAAmB,GAAG;MAAEC,CAAC,EAAE+M,KAAK,CAACE,OAAO;MAAEhN,CAAC,EAAE8M,KAAK,CAACG;IAAO,CAAE;IACjE,IAAI,CAAChN,kBAAkB,GAAG,IAAI;EAChC;EAEAmN,UAAUA,CAAC7Q,KAAa;IACtB,IAAI,IAAI,CAAC2D,qBAAqB,EAAE;MAC9B,IAAI,CAACmN,cAAc,CAAC,IAAI,CAACnN,qBAAqB,CAACxJ,EAAE,EAAE6F,KAAK,CAAC;;IAE3D,IAAI,CAAC0D,kBAAkB,GAAG,KAAK;EACjC;EAEAoN,cAAcA,CAAChE,SAAiB,EAAE9M,KAAa;IAC7C,IAAI,CAAC8M,SAAS,IAAI,CAAC9M,KAAK,EAAE;MACxBiH,OAAO,CAACyB,KAAK,CAAC,2CAA2C,CAAC;MAC1D;;IAGF;IACA,IAAI,CAACrG,cAAc,CAAC0O,cAAc,CAACjE,SAAS,EAAE9M,KAAK,CAAC,CAACsJ,SAAS,CAAC;MAC7DC,IAAI,EAAGyH,MAAM,IAAI;QACf;QACA,MAAMC,YAAY,GAAG,IAAI,CAAC1R,QAAQ,CAAC2R,SAAS,CACzC7E,GAAG,IAAKA,GAAG,CAAClS,EAAE,KAAK2S,SAAS,CAC9B;QACD,IAAImE,YAAY,KAAK,CAAC,CAAC,EAAE;UACvB,IAAI,CAAC1R,QAAQ,CAAC0R,YAAY,CAAC,GAAGD,MAAM;UACpC,IAAI,CAACxO,GAAG,CAAC+J,aAAa,EAAE;;QAG1B,IAAI,CAAChK,YAAY,CAACsM,WAAW,CAAC,YAAY7O,KAAK,UAAU,CAAC;MAC5D,CAAC;MACD0I,KAAK,EAAGA,KAAK,IAAI;QACfzB,OAAO,CAACyB,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QAClD,IAAI,CAACnG,YAAY,CAAC4G,SAAS,CAAC,uCAAuC,CAAC;MACtE;KACD,CAAC;EACJ;EAEAgI,cAAcA,CAACC,QAAa,EAAEtI,MAAc;IAC1C,OAAOsI,QAAQ,CAACtI,MAAM,KAAKA,MAAM;EACnC;EAEAuI,cAAcA,CAACjE,OAAY;IACzB,IAAI,CAACnM,aAAa,EAAE;EACtB;EAEAqQ,cAAcA,CAAClE,OAAY;IACzB,IAAI,CAACnM,aAAa,EAAE;EACtB;EAEAsQ,aAAaA,CAACnE,OAAY;IACxB,IAAI,CAACA,OAAO,CAACjT,EAAE,EAAE;MACf8M,OAAO,CAACyB,KAAK,CAAC,uCAAuC,CAAC;MACtD,IAAI,CAACnG,YAAY,CAAC4G,SAAS,CAAC,gCAAgC,CAAC;MAC7D;;IAGF;IACA,MAAMqI,SAAS,GACbpE,OAAO,CAAClT,MAAM,EAAEC,EAAE,KAAK,IAAI,CAACY,aAAa,IACzCqS,OAAO,CAAC1B,QAAQ,KAAK,IAAI,CAAC3Q,aAAa;IAEzC,IAAI,CAACyW,SAAS,EAAE;MACd,IAAI,CAACjP,YAAY,CAAC4G,SAAS,CACzB,mDAAmD,CACpD;MACD,IAAI,CAAClI,aAAa,EAAE;MACpB;;IAGF;IACA,IAAI,CAACwQ,OAAO,CAAC,iDAAiD,CAAC,EAAE;MAC/D,IAAI,CAACxQ,aAAa,EAAE;MACpB;;IAGF;IACA,IAAI,CAACoB,cAAc,CAACkP,aAAa,CAACnE,OAAO,CAACjT,EAAE,CAAC,CAACmP,SAAS,CAAC;MACtDC,IAAI,EAAGyH,MAAM,IAAI;QACf;QACA,IAAI,CAACzR,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACmS,MAAM,CAAErF,GAAG,IAAKA,GAAG,CAAClS,EAAE,KAAKiT,OAAO,CAACjT,EAAE,CAAC;QAEpE,IAAI,CAACoI,YAAY,CAACsM,WAAW,CAAC,kBAAkB,CAAC;QACjD,IAAI,CAACrM,GAAG,CAAC+J,aAAa,EAAE;MAC1B,CAAC;MACD7D,KAAK,EAAGA,KAAK,IAAI;QACfzB,OAAO,CAACyB,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjD,IAAI,CAACnG,YAAY,CAAC4G,SAAS,CAAC,0CAA0C,CAAC;MACzE;KACD,CAAC;IAEF,IAAI,CAAClI,aAAa,EAAE;EACtB;EAEA;EAEA0Q,iBAAiBA,CAAA;IACf,IAAI,CAAC7O,eAAe,GAAG,CAAC,IAAI,CAACA,eAAe;EAC9C;EAEA8O,mBAAmBA,CAACC,QAAa;IAC/B,IAAI,CAACzR,qBAAqB,GAAGyR,QAAQ;EACvC;EAEA1R,oBAAoBA,CAAC0R,QAAa;IAChC,OAAOA,QAAQ,EAAE9M,MAAM,IAAI,EAAE;EAC/B;EAEAjF,WAAWA,CAACE,KAAU;IACpB,MAAM8R,cAAc,GAAG,IAAI,CAACxM,WAAW,CAACO,GAAG,CAAC,SAAS,CAAC,EAAEoH,KAAK,IAAI,EAAE;IACnE,MAAM8E,UAAU,GAAGD,cAAc,GAAG9R,KAAK,CAACA,KAAK;IAC/C,IAAI,CAACsF,WAAW,CAAC0M,UAAU,CAAC;MAAEpX,OAAO,EAAEmX;IAAU,CAAE,CAAC;IACpD,IAAI,CAACjP,eAAe,GAAG,KAAK;EAC9B;EAEAmP,oBAAoBA,CAAA;IAClB,IAAI,CAAClP,kBAAkB,GAAG,CAAC,IAAI,CAACA,kBAAkB;EACpD;EAEA;EACA;EAEA;EACA;EAEA;EACA;EAEA;EAEAvD,gBAAgBA,CAACuM,KAAa,EAAEqB,OAAY;IAC1C,OAAOA,OAAO,CAACjT,EAAE,IAAIiT,OAAO,CAACrE,GAAG,IAAIgD,KAAK,CAACmG,QAAQ,EAAE;EACtD;EAEA;EAEAC,cAAcA,CAAA;IACZ,MAAMC,WAAW,GAAG;MAClBjY,EAAE,EAAE,QAAQoQ,IAAI,CAACsD,GAAG,EAAE,EAAE;MACxBjT,OAAO,EAAE,mBAAmB,IAAI2P,IAAI,EAAE,CAAC8H,kBAAkB,EAAE,EAAE;MAC7DzY,SAAS,EAAE,IAAI2Q,IAAI,EAAE,CAAC+H,WAAW,EAAE;MACnCpY,MAAM,EAAE;QACNC,EAAE,EAAE,IAAI,CAACzB,gBAAgB,EAAEyB,EAAE,IAAI,WAAW;QAC5CX,QAAQ,EAAE,IAAI,CAACd,gBAAgB,EAAEc,QAAQ,IAAI,WAAW;QACxDa,KAAK,EACH,IAAI,CAAC3B,gBAAgB,EAAE2B,KAAK,IAAI;OACnC;MACDoR,IAAI,EAAE,MAAM;MACZ8G,MAAM,EAAE;KACT;IACD,IAAI,CAAChT,QAAQ,CAAC+M,IAAI,CAAC8F,WAAW,CAAC;IAC/B,IAAI,CAAC5P,GAAG,CAAC+J,aAAa,EAAE;IACxBnG,UAAU,CAAC,MAAM,IAAI,CAAC0E,cAAc,EAAE,EAAE,EAAE,CAAC;EAC7C;EAEAjM,mBAAmBA,CAAA;IACjB,OACE,IAAI,CAAC4D,YAAY,EAAEoH,OAAO,IAC1B,IAAI,CAACpH,YAAY,EAAEkH,YAAY,EAAEC,MAAM,GAAG,CAAC,IAC3C,KAAK;EAET;EAEA/I,UAAUA,CAAA;IACR,IAAI,CAACkC,kBAAkB,GAAG,KAAK;IAC/B;EACF;;EAEAyP,SAASA,CAACC,MAAc;IACtB,MAAMC,YAAY,GAAG5L,QAAQ,CAAC6L,aAAa,CACzC,oBAAoB,CACN;IAChB,IAAID,YAAY,EAAE;MAChB,MAAME,gBAAgB,GAAGF,YAAY,CAAClL,KAAK,CAACqL,SAAS,IAAI,UAAU;MACnE,MAAMC,YAAY,GAAGC,UAAU,CAC7BH,gBAAgB,CAACI,KAAK,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,IAAI,GAAG,CACvD;MACD,MAAMC,QAAQ,GAAGtF,IAAI,CAACuF,GAAG,CAAC,GAAG,EAAEvF,IAAI,CAACwF,GAAG,CAAC,CAAC,EAAEL,YAAY,GAAGL,MAAM,CAAC,CAAC;MAClEC,YAAY,CAAClL,KAAK,CAACqL,SAAS,GAAG,SAASI,QAAQ,GAAG;MACnD,IAAIA,QAAQ,GAAG,CAAC,EAAE;QAChBP,YAAY,CAACU,SAAS,CAAC9H,GAAG,CAAC,QAAQ,CAAC;OACrC,MAAM;QACLoH,YAAY,CAACU,SAAS,CAACC,MAAM,CAAC,QAAQ,CAAC;;;EAG7C;EAEAC,SAASA,CAAA;IACP,MAAMZ,YAAY,GAAG5L,QAAQ,CAAC6L,aAAa,CACzC,oBAAoB,CACN;IAChB,IAAID,YAAY,EAAE;MAChBA,YAAY,CAAClL,KAAK,CAACqL,SAAS,GAAG,UAAU;MACzCH,YAAY,CAACU,SAAS,CAACC,MAAM,CAAC,QAAQ,CAAC;;EAE3C;EAEA;EACA;EACA;EACA;EAEA7S,gBAAgBA,CAACiL,IAAa;IAC5B,MAAM8H,KAAK,GAAG,IAAI,CAACC,SAAS,EAAE/M,aAAa;IAC3C,IAAI,CAAC8M,KAAK,EAAE;MACVtM,OAAO,CAACyB,KAAK,CAAC,8BAA8B,CAAC;MAC7C;;IAGF;IACA,IAAI+C,IAAI,KAAK,OAAO,EAAE;MACpB8H,KAAK,CAACE,MAAM,GAAG,SAAS;KACzB,MAAM,IAAIhI,IAAI,KAAK,OAAO,EAAE;MAC3B8H,KAAK,CAACE,MAAM,GAAG,SAAS;KACzB,MAAM,IAAIhI,IAAI,KAAK,UAAU,EAAE;MAC9B8H,KAAK,CAACE,MAAM,GAAG,iCAAiC;KACjD,MAAM;MACLF,KAAK,CAACE,MAAM,GAAG,KAAK;;IAGtB;IACAF,KAAK,CAACtG,KAAK,GAAG,EAAE;IAEhB;IACAsG,KAAK,CAACzD,KAAK,EAAE;IACb,IAAI,CAAC/M,kBAAkB,GAAG,KAAK;EACjC;EAEA7D,iBAAiBA,CAACtF,SAAwB;IACxC,IAAI,CAACA,SAAS,EAAE,OAAO,EAAE;IAEzB,MAAM8Z,IAAI,GAAG,IAAInJ,IAAI,CAAC3Q,SAAS,CAAC;IAChC,OAAO8Z,IAAI,CAACrB,kBAAkB,CAAC,OAAO,EAAE;MACtCsB,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;KACT,CAAC;EACJ;EAEAla,mBAAmBA,CAACE,SAAwB;IAC1C,IAAI,CAACA,SAAS,EAAE,OAAO,EAAE;IAEzB,MAAM8Z,IAAI,GAAG,IAAInJ,IAAI,CAAC3Q,SAAS,CAAC;IAChC,MAAMia,KAAK,GAAG,IAAItJ,IAAI,EAAE;IACxB,MAAMuJ,SAAS,GAAG,IAAIvJ,IAAI,CAACsJ,KAAK,CAAC;IACjCC,SAAS,CAACC,OAAO,CAACD,SAAS,CAACE,OAAO,EAAE,GAAG,CAAC,CAAC;IAE1C,IAAIN,IAAI,CAACO,YAAY,EAAE,KAAKJ,KAAK,CAACI,YAAY,EAAE,EAAE;MAChD,OAAO,aAAa;KACrB,MAAM,IAAIP,IAAI,CAACO,YAAY,EAAE,KAAKH,SAAS,CAACG,YAAY,EAAE,EAAE;MAC3D,OAAO,MAAM;KACd,MAAM;MACL,OAAOP,IAAI,CAACQ,kBAAkB,CAAC,OAAO,CAAC;;EAE3C;EAEAvZ,oBAAoBA,CAACC,OAAe;IAClC,IAAI,CAACA,OAAO,EAAE,OAAO,EAAE;IAEvB;IACA,MAAMuZ,QAAQ,GAAG,sBAAsB;IACvC,OAAOvZ,OAAO,CAACwZ,OAAO,CACpBD,QAAQ,EACR,qEAAqE,CACtE;EACH;EAEAzV,uBAAuBA,CAACqN,KAAa;IACnC,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO,IAAI;IAE5B,MAAMsI,cAAc,GAAG,IAAI,CAAC9U,QAAQ,CAACwM,KAAK,CAAC;IAC3C,MAAMuI,eAAe,GAAG,IAAI,CAAC/U,QAAQ,CAACwM,KAAK,GAAG,CAAC,CAAC;IAEhD,IAAI,CAACsI,cAAc,EAAEza,SAAS,IAAI,CAAC0a,eAAe,EAAE1a,SAAS,EAAE,OAAO,KAAK;IAE3E,MAAM2a,WAAW,GAAG,IAAIhK,IAAI,CAAC8J,cAAc,CAACza,SAAS,CAAC,CAACqa,YAAY,EAAE;IACrE,MAAMO,YAAY,GAAG,IAAIjK,IAAI,CAAC+J,eAAe,CAAC1a,SAAS,CAAC,CAACqa,YAAY,EAAE;IAEvE,OAAOM,WAAW,KAAKC,YAAY;EACrC;EAEA5V,gBAAgBA,CAACmN,KAAa;IAC5B,MAAMsI,cAAc,GAAG,IAAI,CAAC9U,QAAQ,CAACwM,KAAK,CAAC;IAC3C,MAAM0I,WAAW,GAAG,IAAI,CAAClV,QAAQ,CAACwM,KAAK,GAAG,CAAC,CAAC;IAE5C,IAAI,CAAC0I,WAAW,EAAE,OAAO,IAAI;IAE7B,OAAOJ,cAAc,CAACna,MAAM,EAAEC,EAAE,KAAKsa,WAAW,CAACva,MAAM,EAAEC,EAAE;EAC7D;EAEA2E,oBAAoBA,CAACiN,KAAa;IAChC,MAAMsI,cAAc,GAAG,IAAI,CAAC9U,QAAQ,CAACwM,KAAK,CAAC;IAC3C,MAAMuI,eAAe,GAAG,IAAI,CAAC/U,QAAQ,CAACwM,KAAK,GAAG,CAAC,CAAC;IAEhD,IAAI,CAACuI,eAAe,EAAE,OAAO,IAAI;IAEjC,OAAOD,cAAc,CAACna,MAAM,EAAEC,EAAE,KAAKma,eAAe,CAACpa,MAAM,EAAEC,EAAE;EACjE;EAEA4E,cAAcA,CAACqO,OAAY;IACzB;IACA,IAAIA,OAAO,CAAC3B,IAAI,EAAE;MAChB,IAAI2B,OAAO,CAAC3B,IAAI,KAAK,OAAO,IAAI2B,OAAO,CAAC3B,IAAI,KAAK,OAAO,EAAE,OAAO,OAAO;MACxE,IAAI2B,OAAO,CAAC3B,IAAI,KAAK,OAAO,IAAI2B,OAAO,CAAC3B,IAAI,KAAK,OAAO,EAAE,OAAO,OAAO;MACxE,IAAI2B,OAAO,CAAC3B,IAAI,KAAK,OAAO,IAAI2B,OAAO,CAAC3B,IAAI,KAAK,OAAO,EAAE,OAAO,OAAO;MACxE,IAAI2B,OAAO,CAAC3B,IAAI,KAAK,eAAe,EAAE,OAAO,OAAO;MACpD,IAAI2B,OAAO,CAAC3B,IAAI,KAAK,MAAM,IAAI2B,OAAO,CAAC3B,IAAI,KAAK,MAAM,EAAE,OAAO,MAAM;;IAGvE;IACA,IAAI2B,OAAO,CAACxB,WAAW,IAAIwB,OAAO,CAACxB,WAAW,CAAChC,MAAM,GAAG,CAAC,EAAE;MACzD,MAAM8K,UAAU,GAAGtH,OAAO,CAACxB,WAAW,CAAC,CAAC,CAAC;MACzC,IAAI8I,UAAU,CAACjJ,IAAI,EAAEgE,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,OAAO;MACzD,IAAIiF,UAAU,CAACjJ,IAAI,EAAEgE,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,OAAO;MACzD,IAAIiF,UAAU,CAACjJ,IAAI,EAAEgE,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,OAAO;MACzD,OAAO,MAAM;;IAGf;IACA,IAAIrC,OAAO,CAACuH,QAAQ,IAAIvH,OAAO,CAACwH,QAAQ,IAAIxH,OAAO,CAACyH,KAAK,EAAE,OAAO,OAAO;IAEzE,OAAO,MAAM;EACf;EAEA7V,QAAQA,CAACoO,OAAY;IACnB;IACA,IAAIA,OAAO,CAAC3B,IAAI,KAAK,OAAO,IAAI2B,OAAO,CAAC3B,IAAI,KAAK,OAAO,EAAE;MACxD,OAAO,IAAI;;IAGb;IACA,MAAMqJ,kBAAkB,GACtB1H,OAAO,CAACxB,WAAW,EAAEQ,IAAI,CAAEN,GAAQ,IAAI;MACrC,OAAOA,GAAG,CAACL,IAAI,EAAEgE,UAAU,CAAC,QAAQ,CAAC,IAAI3D,GAAG,CAACL,IAAI,KAAK,OAAO;IAC/D,CAAC,CAAC,IAAI,KAAK;IAEb;IACA,MAAMsJ,WAAW,GAAG,CAAC,EAAE3H,OAAO,CAAC4H,QAAQ,IAAI5H,OAAO,CAAC/S,KAAK,CAAC;IAEzD,OAAOya,kBAAkB,IAAIC,WAAW;EAC1C;EAEAE,OAAOA,CAAC7H,OAAY;IAClB;IACA,IAAIA,OAAO,CAAC3B,IAAI,KAAK,MAAM,IAAI2B,OAAO,CAAC3B,IAAI,KAAK,MAAM,EAAE;MACtD,OAAO,IAAI;;IAGb;IACA,MAAMyJ,iBAAiB,GACrB9H,OAAO,CAACxB,WAAW,EAAEQ,IAAI,CAAEN,GAAQ,IAAI;MACrC,OAAO,CAACA,GAAG,CAACL,IAAI,EAAEgE,UAAU,CAAC,QAAQ,CAAC,IAAI3D,GAAG,CAACL,IAAI,KAAK,OAAO;IAChE,CAAC,CAAC,IAAI,KAAK;IAEb,OAAOyJ,iBAAiB;EAC1B;EAEApZ,WAAWA,CAACsR,OAAY;IACtB;IACA,IAAIA,OAAO,CAAC4H,QAAQ,EAAE;MACpB,OAAO5H,OAAO,CAAC4H,QAAQ;;IAEzB,IAAI5H,OAAO,CAAC/S,KAAK,EAAE;MACjB,OAAO+S,OAAO,CAAC/S,KAAK;;IAGtB;IACA,MAAM8a,eAAe,GAAG/H,OAAO,CAACxB,WAAW,EAAE9B,IAAI,CAC9CgC,GAAQ,IAAKA,GAAG,CAACL,IAAI,EAAEgE,UAAU,CAAC,QAAQ,CAAC,IAAI3D,GAAG,CAACL,IAAI,KAAK,OAAO,CACrE;IAED,IAAI0J,eAAe,EAAE;MACnB,OAAOA,eAAe,CAACnJ,GAAG,IAAImJ,eAAe,CAAClJ,IAAI,IAAI,EAAE;;IAG1D,OAAO,EAAE;EACX;EAEAmJ,WAAWA,CAAChI,OAAY;IACtB,MAAMoC,cAAc,GAAGpC,OAAO,CAACxB,WAAW,EAAE9B,IAAI,CAC7CgC,GAAQ,IAAK,CAACA,GAAG,CAACL,IAAI,EAAEgE,UAAU,CAAC,QAAQ,CAAC,CAC9C;IACD,OAAOD,cAAc,EAAEzP,IAAI,IAAI,SAAS;EAC1C;EAEAsV,WAAWA,CAACjI,OAAY;IACtB,MAAMoC,cAAc,GAAGpC,OAAO,CAACxB,WAAW,EAAE9B,IAAI,CAC7CgC,GAAQ,IAAK,CAACA,GAAG,CAACL,IAAI,EAAEgE,UAAU,CAAC,QAAQ,CAAC,CAC9C;IACD,IAAI,CAACD,cAAc,EAAEtD,IAAI,EAAE,OAAO,EAAE;IAEpC,MAAMmD,KAAK,GAAGG,cAAc,CAACtD,IAAI;IACjC,IAAImD,KAAK,GAAG,IAAI,EAAE,OAAOA,KAAK,GAAG,IAAI;IACrC,IAAIA,KAAK,GAAG,OAAO,EAAE,OAAO1B,IAAI,CAAC2B,KAAK,CAACD,KAAK,GAAG,IAAI,CAAC,GAAG,KAAK;IAC5D,OAAO1B,IAAI,CAAC2B,KAAK,CAACD,KAAK,GAAG,OAAO,CAAC,GAAG,KAAK;EAC5C;EAEAiG,WAAWA,CAAClI,OAAY;IACtB,MAAMoC,cAAc,GAAGpC,OAAO,CAACxB,WAAW,EAAE9B,IAAI,CAC7CgC,GAAQ,IAAK,CAACA,GAAG,CAACL,IAAI,EAAEgE,UAAU,CAAC,QAAQ,CAAC,CAC9C;IACD,IAAI,CAACD,cAAc,EAAE/D,IAAI,EAAE,OAAO,aAAa;IAE/C,IAAI+D,cAAc,CAAC/D,IAAI,CAACgE,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,mBAAmB;IACxE,IAAID,cAAc,CAAC/D,IAAI,CAACgE,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,mBAAmB;IACxE,IAAID,cAAc,CAAC/D,IAAI,CAAC8J,QAAQ,CAAC,KAAK,CAAC,EAAE,OAAO,iBAAiB;IACjE,IAAI/F,cAAc,CAAC/D,IAAI,CAAC8J,QAAQ,CAAC,MAAM,CAAC,EAAE,OAAO,kBAAkB;IACnE,IAAI/F,cAAc,CAAC/D,IAAI,CAAC8J,QAAQ,CAAC,OAAO,CAAC,EAAE,OAAO,mBAAmB;IACrE,OAAO,aAAa;EACtB;EAEA9a,YAAYA,CAACqO,MAAc;IACzB;IACA,MAAM0M,MAAM,GAAG,CACb,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,CACV;IACD,MAAMzJ,KAAK,GAAGjD,MAAM,CAAC2M,UAAU,CAAC,CAAC,CAAC,GAAGD,MAAM,CAAC5L,MAAM;IAClD,OAAO4L,MAAM,CAACzJ,KAAK,CAAC;EACtB;EAEA;EACAjO,cAAcA,CAACsP,OAAY,EAAEmD,KAAU,GAAS;EAEhDmF,aAAaA,CAACnF,KAAU;IACtB;IACA,IAAI,CAACoF,qBAAqB,EAAE;EAC9B;EAEAC,cAAcA,CAACrF,KAAoB;IACjC,IAAIA,KAAK,CAACsF,GAAG,KAAK,OAAO,IAAI,CAACtF,KAAK,CAACuF,QAAQ,EAAE;MAC5CvF,KAAK,CAACC,cAAc,EAAE;MACtB,IAAI,CAACzD,WAAW,EAAE;;EAEtB;EAEAgJ,YAAYA,CAAA;IACV;EAAA;EAGFC,WAAWA,CAAA;IACT;EAAA;EAGFC,QAAQA,CAAC1F,KAAU;IACjB;IACA,MAAMhD,OAAO,GAAGgD,KAAK,CAACV,MAAM;IAC5B,IACEtC,OAAO,CAACC,SAAS,KAAK,CAAC,IACvB,IAAI,CAAC3K,eAAe,IACpB,CAAC,IAAI,CAACD,aAAa,EACnB;MACA,IAAI,CAACmI,gBAAgB,EAAE;;EAE3B;EAEA9Q,eAAeA,CAAC6O,MAAc,GAAS;EAEvCvN,WAAWA,CAACgV,KAAU,EAAEnD,OAAY;IAClCnG,OAAO,CAACC,GAAG,CACT,oDAAoD,EACpDkG,OAAO,CAACjT,EAAE,EACVoW,KAAK,CAACV,MAAM,CAACqG,GAAG,CACjB;EACH;EAEAxa,YAAYA,CAAC6U,KAAU,EAAEnD,OAAY;IACnCnG,OAAO,CAACyB,KAAK,CAAC,+CAA+C,EAAE0E,OAAO,CAACjT,EAAE,EAAE;MACzE+b,GAAG,EAAE3F,KAAK,CAACV,MAAM,CAACqG,GAAG;MACrBxN,KAAK,EAAE6H;KACR,CAAC;IACF;IACAA,KAAK,CAACV,MAAM,CAACqG,GAAG,GACd,4WAA4W;EAChX;EAEA/a,eAAeA,CAACiS,OAAY;IAC1B,MAAM+H,eAAe,GAAG/H,OAAO,CAACxB,WAAW,EAAE9B,IAAI,CAAEgC,GAAQ,IACzDA,GAAG,CAACL,IAAI,EAAEgE,UAAU,CAAC,QAAQ,CAAC,CAC/B;IACD,IAAI0F,eAAe,EAAEnJ,GAAG,EAAE;MACxB,IAAI,CAACnI,aAAa,GAAG;QACnBmI,GAAG,EAAEmJ,eAAe,CAACnJ,GAAG;QACxBjM,IAAI,EAAEoV,eAAe,CAACpV,IAAI,IAAI,OAAO;QACrCmM,IAAI,EAAE,IAAI,CAACkD,cAAc,CAAC+F,eAAe,CAACjJ,IAAI,IAAI,CAAC,CAAC;QACpDkB,OAAO,EAAEA;OACV;MACD,IAAI,CAACxJ,eAAe,GAAG,IAAI;;EAE/B;EAEAuS,gBAAgBA,CAAA;IACd,IAAI,CAACvS,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACC,aAAa,GAAG,IAAI;EAC3B;EAEAuS,aAAaA,CAAA;IACX,IAAI,IAAI,CAACvS,aAAa,EAAEmI,GAAG,EAAE;MAC3B,MAAM0D,IAAI,GAAG5I,QAAQ,CAACM,aAAa,CAAC,GAAG,CAAC;MACxCsI,IAAI,CAACC,IAAI,GAAG,IAAI,CAAC9L,aAAa,CAACmI,GAAG;MAClC0D,IAAI,CAACE,QAAQ,GAAG,IAAI,CAAC/L,aAAa,CAAC9D,IAAI,IAAI,OAAO;MAClD2P,IAAI,CAACG,MAAM,GAAG,QAAQ;MACtB/I,QAAQ,CAACY,IAAI,CAACC,WAAW,CAAC+H,IAAI,CAAC;MAC/BA,IAAI,CAACI,KAAK,EAAE;MACZhJ,QAAQ,CAACY,IAAI,CAACqI,WAAW,CAACL,IAAI,CAAC;MAC/B,IAAI,CAACnN,YAAY,CAACsM,WAAW,CAAC,wBAAwB,CAAC;MACvD5H,OAAO,CAACC,GAAG,CACT,qCAAqC,EACrC,IAAI,CAACrD,aAAa,CAAC9D,IAAI,CACxB;;EAEL;EAEA;EACA;EACA;EAEAsW,cAAcA,CAAA;IACZ,IAAI,CAAC,IAAI,CAACpT,WAAW,CAACiK,IAAI,EAAE,EAAE;MAC5B,IAAI,CAAChK,aAAa,GAAG,EAAE;MACvB;;IAGF,IAAI,CAACA,aAAa,GAAG,IAAI,CAAC3D,QAAQ,CAACmS,MAAM,CACtCtE,OAAO,IACNA,OAAO,CAACxS,OAAO,EACX0b,WAAW,EAAE,CACdf,QAAQ,CAAC,IAAI,CAACtS,WAAW,CAACqT,WAAW,EAAE,CAAC,IAC3ClJ,OAAO,CAAClT,MAAM,EAAEV,QAAQ,EACpB8c,WAAW,EAAE,CACdf,QAAQ,CAAC,IAAI,CAACtS,WAAW,CAACqT,WAAW,EAAE,CAAC,CAC9C;EACH;EAEAC,mBAAmBA,CAAA;IACjB,IAAI,CAACF,cAAc,EAAE;EACvB;EAEAG,WAAWA,CAAA;IACT,IAAI,CAACvT,WAAW,GAAG,EAAE;IACrB,IAAI,CAACC,aAAa,GAAG,EAAE;EACzB;EAEAuT,aAAaA,CAAC3J,SAAiB;IAC7B,MAAM4J,cAAc,GAAG5P,QAAQ,CAACC,cAAc,CAAC,WAAW+F,SAAS,EAAE,CAAC;IACtE,IAAI4J,cAAc,EAAE;MAClBA,cAAc,CAACC,cAAc,CAAC;QAAEC,QAAQ,EAAE,QAAQ;QAAEC,KAAK,EAAE;MAAQ,CAAE,CAAC;MACtE;MACAH,cAAc,CAACtD,SAAS,CAAC9H,GAAG,CAAC,WAAW,CAAC;MACzClF,UAAU,CAAC,MAAK;QACdsQ,cAAc,CAACtD,SAAS,CAACC,MAAM,CAAC,WAAW,CAAC;MAC9C,CAAC,EAAE,IAAI,CAAC;;EAEZ;EAEA;EACA;EACA;EAEAyD,gBAAgBA,CAAA;IACd,IAAI,CAACzT,sBAAsB,GAAG,KAAK;IACnC,IAAI,CAACC,eAAe,GAAG,IAAI;EAC7B;EAEA;EACA;EACA;EACA;EAEA;EACA;EAEA;EACA;EACA;EACA;EAEQgL,YAAYA,CAAC5J,QAAkB;IACrCuC,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE;MAC5CxC,QAAQ;MACRhM,gBAAgB,EAAE,IAAI,CAACA,gBAAgB;MACvC+J,YAAY,EAAE,IAAI,CAACA,YAAY,EAAEtI,EAAE;MACnCY,aAAa,EAAE,IAAI,CAACA;KACrB,CAAC;IAEF,IAAI,CAAC,IAAI,CAACrC,gBAAgB,EAAE;MAC1BuO,OAAO,CAACyB,KAAK,CAAC,uCAAuC,CAAC;MACtD,IAAI,CAACnG,YAAY,CAAC4G,SAAS,CAAC,gCAAgC,CAAC;MAC7D;;IAGF,MAAM4N,WAAW,GAAG,IAAI,CAACre,gBAAgB,CAACyB,EAAE,IAAI,IAAI,CAACzB,gBAAgB,CAACqQ,GAAG;IACzE,IAAI,CAACgO,WAAW,EAAE;MAChB9P,OAAO,CAACyB,KAAK,CAAC,wCAAwC,CAAC;MACvD,IAAI,CAACnG,YAAY,CAAC4G,SAAS,CAAC,gCAAgC,CAAC;MAC7D;;IAGFlC,OAAO,CAACC,GAAG,CAAC,+BAA+BxC,QAAQ,gBAAgB,EAAE;MACnEqS,WAAW;MACXC,aAAa,EACX,IAAI,CAACte,gBAAgB,CAACc,QAAQ,IAAI,IAAI,CAACd,gBAAgB,CAACqH,IAAI;MAC9DiJ,cAAc,EAAE,IAAI,CAACvG,YAAY,EAAEtI;KACpC,CAAC;IAEF,IAAI,CAACsK,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,QAAQ,GAAGA,QAAQ,KAAKzM,QAAQ,CAACsW,KAAK,GAAG,OAAO,GAAG,OAAO;IAC/D,IAAI,CAAC5J,YAAY,GAAG,CAAC;IAErB;IACA,IAAI,CAACsS,cAAc,EAAE;IAErB;IACA,IAAI,CAAC3U,WAAW,CACbgM,YAAY,CAACyI,WAAW,EAAErS,QAAQ,EAAE,IAAI,CAACjC,YAAY,EAAEtI,EAAE,CAAC,CAC1DmP,SAAS,CAAC;MACTC,IAAI,EAAG2N,IAAU,IAAI;QACnB,IAAI,CAAChI,UAAU,GAAGgI,IAAI;QACtB,IAAI,CAAC/H,eAAe,GAAG,KAAK;QAC5B,IAAI,CAAC5M,YAAY,CAACsM,WAAW,CAC3B,SAASnK,QAAQ,KAAKzM,QAAQ,CAACsW,KAAK,GAAG,OAAO,GAAG,OAAO,SAAS,CAClE;QAEDtH,OAAO,CAACC,GAAG,CACT,qEAAqE,CACtE;MACH,CAAC;MACDwB,KAAK,EAAGA,KAAU,IAAI;QACpBzB,OAAO,CAACyB,KAAK,CAAC,wCAAwC,EAAE;UACtDA,KAAK,EAAEA,KAAK,CAAC0E,OAAO,IAAI1E,KAAK;UAC7BqO,WAAW;UACXrS,QAAQ;UACRsE,cAAc,EAAE,IAAI,CAACvG,YAAY,EAAEtI;SACpC,CAAC;QAEF,IAAI,CAAC8U,OAAO,EAAE;QACd,IAAI,CAAC1M,YAAY,CAAC4G,SAAS,CAAC,wCAAwC,CAAC;MACvE;KACD,CAAC;EACN;EAEAgO,UAAUA,CAAC/O,YAA0B;IACnC,IAAI,CAAC9F,WAAW,CAAC6U,UAAU,CAAC/O,YAAY,CAAC,CAACkB,SAAS,CAAC;MAClDC,IAAI,EAAG2N,IAAU,IAAI;QACnB,IAAI,CAAChI,UAAU,GAAGgI,IAAI;QACtB,IAAI,CAACzS,QAAQ,GAAG,IAAI;QACpB,IAAI,CAAC0K,eAAe,GAAG,IAAI;QAC3B,IAAI,CAACzK,QAAQ,GAAGwS,IAAI,CAACzL,IAAI,KAAKxT,QAAQ,CAACsW,KAAK,GAAG,OAAO,GAAG,OAAO;QAChE,IAAI,CAAC0I,cAAc,EAAE;QACrB,IAAI,CAAC1U,YAAY,CAACsM,WAAW,CAAC,eAAe,CAAC;MAChD,CAAC;MACDnG,KAAK,EAAGA,KAAU,IAAI;QACpBzB,OAAO,CAACyB,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,IAAI,CAACnG,YAAY,CAAC4G,SAAS,CAAC,yCAAyC,CAAC;MACxE;KACD,CAAC;EACJ;EAEAiO,UAAUA,CAAChP,YAA0B;IACnC,IAAI,CAAC9F,WAAW,CAAC8U,UAAU,CAAChP,YAAY,CAACjO,EAAE,EAAE,eAAe,CAAC,CAACmP,SAAS,CAAC;MACtEC,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAAChH,YAAY,CAACsM,WAAW,CAAC,cAAc,CAAC;MAC/C,CAAC;MACDnG,KAAK,EAAGA,KAAK,IAAI;QACfzB,OAAO,CAACyB,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,IAAI,CAACnG,YAAY,CAAC4G,SAAS,CAAC,iCAAiC,CAAC;MAChE;KACD,CAAC;EACJ;EAEQ8N,cAAcA,CAAA;IACpB,IAAI,CAACtS,YAAY,GAAG,CAAC;IACrB,IAAI,CAACC,SAAS,GAAGyS,WAAW,CAAC,MAAK;MAChC,IAAI,CAAC1S,YAAY,EAAE;MACnB,IAAI,CAACnC,GAAG,CAAC+J,aAAa,EAAE;IAC1B,CAAC,EAAE,IAAI,CAAC;EACV;EAEQ+K,cAAcA,CAAA;IACpB,IAAI,IAAI,CAAC1S,SAAS,EAAE;MAClB2S,aAAa,CAAC,IAAI,CAAC3S,SAAS,CAAC;MAC7B,IAAI,CAACA,SAAS,GAAG,IAAI;;IAGvB,IAAI,CAACH,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,YAAY,GAAG,CAAC;IACrB,IAAI,CAACuK,UAAU,GAAG,IAAI;IACtB,IAAI,CAACC,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACR,OAAO,GAAG,KAAK;IACpB,IAAI,CAACI,cAAc,GAAG,IAAI;EAC5B;EAEA;EACAyI,UAAUA,CAAA;IACR,IAAI,CAAC,IAAI,CAACtI,UAAU,EAAE;IAEtB,IAAI,CAACP,OAAO,GAAG,CAAC,IAAI,CAACA,OAAO;IAE5B;IACA,IAAI,CAACrM,WAAW,CACbmV,WAAW,CACV,IAAI,CAACvI,UAAU,CAAC/U,EAAE,EAClBgT,SAAS;IAAE;IACX,CAAC,IAAI,CAACwB,OAAO,CAAC;KACf,CACArF,SAAS,CAAC;MACTC,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAAChH,YAAY,CAACsM,WAAW,CAC3B,IAAI,CAACF,OAAO,GAAG,aAAa,GAAG,cAAc,CAC9C;MACH,CAAC;MACDjG,KAAK,EAAGA,KAAK,IAAI;QACfzB,OAAO,CAACyB,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C;QACA,IAAI,CAACiG,OAAO,GAAG,CAAC,IAAI,CAACA,OAAO;QAC5B,IAAI,CAACpM,YAAY,CAAC4G,SAAS,CAAC,oCAAoC,CAAC;MACnE;KACD,CAAC;EACN;EAEA6F,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAACE,UAAU,EAAE;IAEtB,IAAI,CAACH,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;IAE1C;IACA,IAAI,CAACzM,WAAW,CACbmV,WAAW,CACV,IAAI,CAACvI,UAAU,CAAC/U,EAAE,EAClB,IAAI,CAAC4U,cAAc;IAAE;IACrB5B,SAAS,CAAC;KACX,CACA7D,SAAS,CAAC;MACTC,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAAChH,YAAY,CAACsM,WAAW,CAC3B,IAAI,CAACE,cAAc,GAAG,gBAAgB,GAAG,mBAAmB,CAC7D;MACH,CAAC;MACDrG,KAAK,EAAGA,KAAK,IAAI;QACfzB,OAAO,CAACyB,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C;QACA,IAAI,CAACqG,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;QAC1C,IAAI,CAACxM,YAAY,CAAC4G,SAAS,CAAC,wCAAwC,CAAC;MACvE;KACD,CAAC;EACN;EAEAuO,kBAAkBA,CAAC1J,QAAgB;IACjC,MAAM2J,KAAK,GAAGhK,IAAI,CAACC,KAAK,CAACI,QAAQ,GAAG,IAAI,CAAC;IACzC,MAAM4J,OAAO,GAAGjK,IAAI,CAACC,KAAK,CAAEI,QAAQ,GAAG,IAAI,GAAI,EAAE,CAAC;IAClD,MAAM6J,OAAO,GAAG7J,QAAQ,GAAG,EAAE;IAE7B,IAAI2J,KAAK,GAAG,CAAC,EAAE;MACb,OAAO,GAAGA,KAAK,IAAIC,OAAO,CAAC1F,QAAQ,EAAE,CAAC4F,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAID,OAAO,CAC9D3F,QAAQ,EAAE,CACV4F,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;;IAEvB,OAAO,GAAGF,OAAO,IAAIC,OAAO,CAAC3F,QAAQ,EAAE,CAAC4F,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EAC5D;EAEA;EACA;EAEMC,mBAAmBA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACvB,IAAI;QACF;QACA,IAAI,CAACC,SAAS,CAACC,YAAY,IAAI,CAACD,SAAS,CAACC,YAAY,CAACC,YAAY,EAAE;UACnE,MAAM,IAAIC,KAAK,CACb,yDAAyD,CAC1D;;QAGH;QACA,IAAI,CAAChI,MAAM,CAACiI,aAAa,EAAE;UACzB,MAAM,IAAID,KAAK,CACb,uDAAuD,CACxD;;QAGH;QACA,MAAME,MAAM,SAASL,SAAS,CAACC,YAAY,CAACC,YAAY,CAAC;UACvDI,KAAK,EAAE;YACLC,gBAAgB,EAAE,IAAI;YACtBC,gBAAgB,EAAE,IAAI;YACtBC,eAAe,EAAE,IAAI;YACrBC,UAAU,EAAE,KAAK;YACjBC,YAAY,EAAE;;SAEjB,CAAC;QAEF;QACA,IAAIC,QAAQ,GAAG,wBAAwB;QACvC,IAAI,CAACR,aAAa,CAACS,eAAe,CAACD,QAAQ,CAAC,EAAE;UAC5CA,QAAQ,GAAG,YAAY;UACvB,IAAI,CAACR,aAAa,CAACS,eAAe,CAACD,QAAQ,CAAC,EAAE;YAC5CA,QAAQ,GAAG,WAAW;YACtB,IAAI,CAACR,aAAa,CAACS,eAAe,CAACD,QAAQ,CAAC,EAAE;cAC5CA,QAAQ,GAAG,EAAE,CAAC,CAAC;;;;QAKrB;QACAd,KAAI,CAAC7T,aAAa,GAAG,IAAImU,aAAa,CAACC,MAAM,EAAE;UAC7CO,QAAQ,EAAEA,QAAQ,IAAI3L;SACvB,CAAC;QAEF;QACA6K,KAAI,CAAC5T,WAAW,GAAG,EAAE;QACrB4T,KAAI,CAAC/T,gBAAgB,GAAG,IAAI;QAC5B+T,KAAI,CAAClW,sBAAsB,GAAG,CAAC;QAC/BkW,KAAI,CAAC9T,mBAAmB,GAAG,WAAW;QAEtC;QACA8T,KAAI,CAAC3T,cAAc,GAAGgT,WAAW,CAAC,MAAK;UACrCW,KAAI,CAAClW,sBAAsB,EAAE;UAC7B;UACAkW,KAAI,CAACgB,iBAAiB,EAAE;UACxBhB,KAAI,CAACxV,GAAG,CAAC+J,aAAa,EAAE;QAC1B,CAAC,EAAE,IAAI,CAAC;QAER;QACAyL,KAAI,CAAC7T,aAAa,CAAC8U,eAAe,GAAI1I,KAAK,IAAI;UAC7C,IAAIA,KAAK,CAACnC,IAAI,CAAClC,IAAI,GAAG,CAAC,EAAE;YACvB8L,KAAI,CAAC5T,WAAW,CAACkI,IAAI,CAACiE,KAAK,CAACnC,IAAI,CAAC;;QAErC,CAAC;QAED4J,KAAI,CAAC7T,aAAa,CAAC+U,MAAM,GAAG,MAAK;UAC/BlB,KAAI,CAACmB,oBAAoB,EAAE;QAC7B,CAAC;QAEDnB,KAAI,CAAC7T,aAAa,CAACiV,OAAO,GAAI7I,KAAU,IAAI;UAC1CtJ,OAAO,CAACyB,KAAK,CAAC,iCAAiC,EAAE6H,KAAK,CAAC7H,KAAK,CAAC;UAC7DsP,KAAI,CAACzV,YAAY,CAAC4G,SAAS,CAAC,iCAAiC,CAAC;UAC9D6O,KAAI,CAACqB,oBAAoB,EAAE;QAC7B,CAAC;QAED;QACArB,KAAI,CAAC7T,aAAa,CAACmV,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;QAE/BtB,KAAI,CAACzV,YAAY,CAACsM,WAAW,CAAC,iCAAiC,CAAC;OACjE,CAAC,OAAOnG,KAAU,EAAE;QACnBzB,OAAO,CAACyB,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;QAE5D,IAAI6Q,YAAY,GAAG,+CAA+C;QAElE,IAAI7Q,KAAK,CAAC3I,IAAI,KAAK,iBAAiB,EAAE;UACpCwZ,YAAY,GACV,iGAAiG;SACpG,MAAM,IAAI7Q,KAAK,CAAC3I,IAAI,KAAK,eAAe,EAAE;UACzCwZ,YAAY,GACV,6DAA6D;SAChE,MAAM,IAAI7Q,KAAK,CAAC3I,IAAI,KAAK,mBAAmB,EAAE;UAC7CwZ,YAAY,GACV,0DAA0D;SAC7D,MAAM,IAAI7Q,KAAK,CAAC0E,OAAO,EAAE;UACxBmM,YAAY,GAAG7Q,KAAK,CAAC0E,OAAO;;QAG9B4K,KAAI,CAACzV,YAAY,CAAC4G,SAAS,CAACoQ,YAAY,CAAC;QACzCvB,KAAI,CAACqB,oBAAoB,EAAE;;IAC5B;EACH;EAEAG,kBAAkBA,CAAA;IAChB,IAAI,IAAI,CAACrV,aAAa,IAAI,IAAI,CAACA,aAAa,CAACsV,KAAK,KAAK,WAAW,EAAE;MAClE,IAAI,CAACtV,aAAa,CAACuV,IAAI,EAAE;MACzB,IAAI,CAACvV,aAAa,CAACoU,MAAM,CAACoB,SAAS,EAAE,CAAC9N,OAAO,CAAE+N,KAAK,IAAKA,KAAK,CAACF,IAAI,EAAE,CAAC;;IAGxE,IAAI,IAAI,CAACrV,cAAc,EAAE;MACvBkT,aAAa,CAAC,IAAI,CAAClT,cAAc,CAAC;MAClC,IAAI,CAACA,cAAc,GAAG,IAAI;;IAG5B,IAAI,CAACJ,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACC,mBAAmB,GAAG,YAAY;EACzC;EAEAmV,oBAAoBA,CAAA;IAClB,IAAI,IAAI,CAAClV,aAAa,EAAE;MACtB,IAAI,IAAI,CAACA,aAAa,CAACsV,KAAK,KAAK,WAAW,EAAE;QAC5C,IAAI,CAACtV,aAAa,CAACuV,IAAI,EAAE;;MAE3B,IAAI,CAACvV,aAAa,CAACoU,MAAM,CAACoB,SAAS,EAAE,CAAC9N,OAAO,CAAE+N,KAAK,IAAKA,KAAK,CAACF,IAAI,EAAE,CAAC;MACtE,IAAI,CAACvV,aAAa,GAAG,IAAI;;IAG3B,IAAI,IAAI,CAACE,cAAc,EAAE;MACvBkT,aAAa,CAAC,IAAI,CAAClT,cAAc,CAAC;MAClC,IAAI,CAACA,cAAc,GAAG,IAAI;;IAG5B,IAAI,CAACJ,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACnC,sBAAsB,GAAG,CAAC;IAC/B,IAAI,CAACoC,mBAAmB,GAAG,MAAM;IACjC,IAAI,CAACE,WAAW,GAAG,EAAE;EACvB;EAEc+U,oBAAoBA,CAAA;IAAA,IAAAU,MAAA;IAAA,OAAA5B,iBAAA;MAChC,IAAI;QACF;QACA,IAAI4B,MAAI,CAACzV,WAAW,CAACwF,MAAM,KAAK,CAAC,EAAE;UACjC3C,OAAO,CAACyB,KAAK,CAAC,sCAAsC,CAAC;UACrDmR,MAAI,CAACtX,YAAY,CAAC4G,SAAS,CAAC,wBAAwB,CAAC;UACrD0Q,MAAI,CAACR,oBAAoB,EAAE;UAC3B;;QAGFpS,OAAO,CAACC,GAAG,CACT,0BAA0B,EAC1B2S,MAAI,CAACzV,WAAW,CAACwF,MAAM,EACvB,WAAW,EACXiQ,MAAI,CAAC/X,sBAAsB,CAC5B;QAED;QACA,IAAI+X,MAAI,CAAC/X,sBAAsB,GAAG,CAAC,EAAE;UACnCmF,OAAO,CAACyB,KAAK,CACX,iCAAiC,EACjCmR,MAAI,CAAC/X,sBAAsB,CAC5B;UACD+X,MAAI,CAACtX,YAAY,CAAC4G,SAAS,CACzB,+CAA+C,CAChD;UACD0Q,MAAI,CAACR,oBAAoB,EAAE;UAC3B;;QAGF;QACA,IAAIP,QAAQ,GAAG,wBAAwB;QACvC,IAAIe,MAAI,CAAC1V,aAAa,EAAE2U,QAAQ,EAAE;UAChCA,QAAQ,GAAGe,MAAI,CAAC1V,aAAa,CAAC2U,QAAQ;;QAGxC;QACA,MAAMgB,SAAS,GAAG,IAAIC,IAAI,CAACF,MAAI,CAACzV,WAAW,EAAE;UAC3CqH,IAAI,EAAEqN;SACP,CAAC;QAEF7R,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE;UAC5CgF,IAAI,EAAE4N,SAAS,CAAC5N,IAAI;UACpBT,IAAI,EAAEqO,SAAS,CAACrO;SACjB,CAAC;QAEF;QACA,IAAIuO,SAAS,GAAG,OAAO;QACvB,IAAIlB,QAAQ,CAACvD,QAAQ,CAAC,KAAK,CAAC,EAAE;UAC5ByE,SAAS,GAAG,MAAM;SACnB,MAAM,IAAIlB,QAAQ,CAACvD,QAAQ,CAAC,KAAK,CAAC,EAAE;UACnCyE,SAAS,GAAG,MAAM;SACnB,MAAM,IAAIlB,QAAQ,CAACvD,QAAQ,CAAC,KAAK,CAAC,EAAE;UACnCyE,SAAS,GAAG,MAAM;;QAGpB;QACA,MAAMC,SAAS,GAAG,IAAIC,IAAI,CACxB,CAACJ,SAAS,CAAC,EACX,SAASvP,IAAI,CAACsD,GAAG,EAAE,GAAGmM,SAAS,EAAE,EACjC;UACEvO,IAAI,EAAEqN;SACP,CACF;QAED7R,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE;UAC5CnH,IAAI,EAAEka,SAAS,CAACla,IAAI;UACpBmM,IAAI,EAAE+N,SAAS,CAAC/N,IAAI;UACpBT,IAAI,EAAEwO,SAAS,CAACxO;SACjB,CAAC;QAEF;QACAoO,MAAI,CAAC3V,mBAAmB,GAAG,YAAY;QACvC,MAAM2V,MAAI,CAACM,gBAAgB,CAACF,SAAS,CAAC;QAEtCJ,MAAI,CAACtX,YAAY,CAACsM,WAAW,CAAC,yBAAyB,CAAC;OACzD,CAAC,OAAOnG,KAAU,EAAE;QACnBzB,OAAO,CAACyB,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;QAC1DmR,MAAI,CAACtX,YAAY,CAAC4G,SAAS,CACzB,2CAA2C,IACxCT,KAAK,CAAC0E,OAAO,IAAI,iBAAiB,CAAC,CACvC;OACF,SAAS;QACR;QACAyM,MAAI,CAAC3V,mBAAmB,GAAG,MAAM;QACjC2V,MAAI,CAAC/X,sBAAsB,GAAG,CAAC;QAC/B+X,MAAI,CAACzV,WAAW,GAAG,EAAE;QACrByV,MAAI,CAAC5V,gBAAgB,GAAG,KAAK;;IAC9B;EACH;EAEckW,gBAAgBA,CAACF,SAAe;IAAA,IAAAG,MAAA;IAAA,OAAAnC,iBAAA;MAC5C,MAAMtM,UAAU,GAAGyO,MAAI,CAAC1hB,gBAAgB,EAAEyB,EAAE,IAAIigB,MAAI,CAAC1hB,gBAAgB,EAAEqQ,GAAG;MAE1E,IAAI,CAAC4C,UAAU,EAAE;QACf,MAAM,IAAI0M,KAAK,CAAC,0BAA0B,CAAC;;MAG7C,OAAO,IAAIgC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;QACrCH,MAAI,CAAC/X,cAAc,CAAC0K,WAAW,CAC7BpB,UAAU,EACV,EAAE,EACFsO,SAAS,EACT,OAAc,EACdG,MAAI,CAAC3X,YAAY,CAACtI,EAAE,CACrB,CAACmP,SAAS,CAAC;UACVC,IAAI,EAAG6D,OAAY,IAAI;YACrBgN,MAAI,CAAC7a,QAAQ,CAAC+M,IAAI,CAACc,OAAO,CAAC;YAC3BgN,MAAI,CAACtP,cAAc,EAAE;YACrBwP,OAAO,EAAE;UACX,CAAC;UACD5R,KAAK,EAAGA,KAAU,IAAI;YACpBzB,OAAO,CAACyB,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;YAChE6R,MAAM,CAAC7R,KAAK,CAAC;UACf;SACD,CAAC;MACJ,CAAC,CAAC;IAAC;EACL;EAEA7G,uBAAuBA,CAACmM,QAAgB;IACtC,MAAM4J,OAAO,GAAGjK,IAAI,CAACC,KAAK,CAACI,QAAQ,GAAG,EAAE,CAAC;IACzC,MAAM6J,OAAO,GAAG7J,QAAQ,GAAG,EAAE;IAC7B,OAAO,GAAG4J,OAAO,IAAIC,OAAO,CAAC3F,QAAQ,EAAE,CAAC4F,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EAC5D;EAEA;EAEA0C,aAAaA,CAACjK,KAAY;IACxBA,KAAK,CAACC,cAAc,EAAE;IAEtBvJ,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE;MACvCjD,gBAAgB,EAAE,IAAI,CAACA,gBAAgB;MACvCC,mBAAmB,EAAE,IAAI,CAACA,mBAAmB;MAC7CpC,sBAAsB,EAAE,IAAI,CAACA,sBAAsB;MACnDqC,aAAa,EAAE,CAAC,CAAC,IAAI,CAACA;KACvB,CAAC;IAEF;IACA,IAAI,IAAI,CAACD,mBAAmB,KAAK,YAAY,EAAE;MAC7C,IAAI,CAAC3B,YAAY,CAACkY,WAAW,CAAC,wBAAwB,CAAC;MACvD;;IAGF,IAAI,IAAI,CAACxW,gBAAgB,EAAE;MACzB,IAAI,CAAC1B,YAAY,CAACkY,WAAW,CAAC,iCAAiC,CAAC;MAChE;;IAGF;IACA,IAAI,CAAClY,YAAY,CAACmY,QAAQ,CAAC,2CAA2C,CAAC;IAEvE;IACA,IAAI,CAAC3C,mBAAmB,EAAE,CAAC3H,KAAK,CAAE1H,KAAK,IAAI;MACzCzB,OAAO,CAACyB,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;MAC7D,IAAI,CAACnG,YAAY,CAAC4G,SAAS,CACzB,iDAAiD,IAC9CT,KAAK,CAAC0E,OAAO,IAAI,iBAAiB,CAAC,CACvC;IACH,CAAC,CAAC;EACJ;EAEAzL,WAAWA,CAAC4O,KAAY;IACtBA,KAAK,CAACC,cAAc,EAAE;IAEtB,IAAI,CAAC,IAAI,CAACvM,gBAAgB,EAAE;MAC1B;;IAGF;IACA,IAAI,CAACuV,kBAAkB,EAAE;EAC3B;EAEAhY,cAAcA,CAAC+O,KAAY;IACzBA,KAAK,CAACC,cAAc,EAAE;IAEtB,IAAI,CAAC,IAAI,CAACvM,gBAAgB,EAAE;MAC1B;;IAGF;IACA,IAAI,CAACoV,oBAAoB,EAAE;EAC7B;EAEAtX,kBAAkBA,CAAA;IAChB,IAAI,IAAI,CAACoC,aAAa,EAAE2U,QAAQ,EAAE;MAChC,IAAI,IAAI,CAAC3U,aAAa,CAAC2U,QAAQ,CAACvD,QAAQ,CAAC,MAAM,CAAC,EAAE,OAAO,MAAM;MAC/D,IAAI,IAAI,CAACpR,aAAa,CAAC2U,QAAQ,CAACvD,QAAQ,CAAC,KAAK,CAAC,EAAE,OAAO,KAAK;MAC7D,IAAI,IAAI,CAACpR,aAAa,CAAC2U,QAAQ,CAACvD,QAAQ,CAAC,KAAK,CAAC,EAAE,OAAO,KAAK;MAC7D,IAAI,IAAI,CAACpR,aAAa,CAAC2U,QAAQ,CAACvD,QAAQ,CAAC,KAAK,CAAC,EAAE,OAAO,KAAK;;IAE/D,OAAO,MAAM;EACf;EAEA;EAEQyD,iBAAiBA,CAAA;IACvB;IACA,IAAI,CAAC/b,UAAU,GAAG,IAAI,CAACA,UAAU,CAAC0d,GAAG,CAAC,MAAK;MACzC,OAAOhN,IAAI,CAACC,KAAK,CAACD,IAAI,CAACiN,MAAM,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;IAC7C,CAAC,CAAC;EACJ;;EAEAC,cAAcA,CAACtK,KAAU;IACvB,MAAMuK,KAAK,GAAGvK,KAAK,CAACV,MAAM,CAACiL,KAAK;IAEhC,IAAI,CAACA,KAAK,IAAIA,KAAK,CAAClR,MAAM,KAAK,CAAC,EAAE;MAChC;;IAGF,KAAK,IAAImR,IAAI,IAAID,KAAK,EAAE;MACtB7T,OAAO,CAACC,GAAG,CACT,gCAAgC6T,IAAI,CAAChb,IAAI,WAAWgb,IAAI,CAAC7O,IAAI,WAAW6O,IAAI,CAACtP,IAAI,EAAE,CACpF;MACD,IAAI,CAACuP,UAAU,CAACD,IAAI,CAAC;;EAEzB;EAEQC,UAAUA,CAACD,IAAU;IAC3B,MAAMpP,UAAU,GAAG,IAAI,CAACjT,gBAAgB,EAAEyB,EAAE,IAAI,IAAI,CAACzB,gBAAgB,EAAEqQ,GAAG;IAE1E,IAAI,CAAC4C,UAAU,EAAE;MACf1E,OAAO,CAACyB,KAAK,CAAC,kCAAkC,CAAC;MACjD,IAAI,CAACnG,YAAY,CAAC4G,SAAS,CAAC,0BAA0B,CAAC;MACvD;;IAGF;IACA,MAAM8R,OAAO,GAAG,EAAE,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC;IAClC,IAAIF,IAAI,CAAC7O,IAAI,GAAG+O,OAAO,EAAE;MACvBhU,OAAO,CAACyB,KAAK,CAAC,+BAA+BqS,IAAI,CAAC7O,IAAI,QAAQ,CAAC;MAC/D,IAAI,CAAC3J,YAAY,CAAC4G,SAAS,CAAC,oCAAoC,CAAC;MACjE;;IAGF;IACA,IAAI4R,IAAI,CAACtP,IAAI,CAACgE,UAAU,CAAC,QAAQ,CAAC,IAAIsL,IAAI,CAAC7O,IAAI,GAAG,IAAI,GAAG,IAAI,EAAE;MAC7D;MACAjF,OAAO,CAACC,GAAG,CACT,sCAAsC,EACtC6T,IAAI,CAAChb,IAAI,EACT,gBAAgB,EAChBgb,IAAI,CAAC7O,IAAI,CACV;MACD,IAAI,CAACgP,aAAa,CAACH,IAAI,CAAC,CACrB5K,IAAI,CAAEgL,cAAc,IAAI;QACvBlU,OAAO,CAACC,GAAG,CACT,8DAA8D,EAC9DiU,cAAc,CAACjP,IAAI,CACpB;QACD,IAAI,CAACkP,gBAAgB,CAACD,cAAc,EAAExP,UAAU,CAAC;MACnD,CAAC,CAAC,CACDyE,KAAK,CAAE1H,KAAK,IAAI;QACfzB,OAAO,CAACyB,KAAK,CAAC,8CAA8C,EAAEA,KAAK,CAAC;QACpE;QACA,IAAI,CAAC0S,gBAAgB,CAACL,IAAI,EAAEpP,UAAU,CAAC;MACzC,CAAC,CAAC;MACJ;;IAGF;IACA,IAAI,CAACyP,gBAAgB,CAACL,IAAI,EAAEpP,UAAU,CAAC;EACzC;EAEQyP,gBAAgBA,CAACL,IAAU,EAAEpP,UAAkB;IACrD,MAAM0P,WAAW,GAAG,IAAI,CAACC,kBAAkB,CAACP,IAAI,CAAC;IAEjD,IAAI,CAAC3X,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACW,WAAW,GAAG,IAAI;IACvB,IAAI,CAACD,cAAc,GAAG,CAAC;IAEvB;IACA,MAAMyX,gBAAgB,GAAGlE,WAAW,CAAC,MAAK;MACxC,IAAI,CAACvT,cAAc,IAAI6J,IAAI,CAACiN,MAAM,EAAE,GAAG,EAAE;MACzC,IAAI,IAAI,CAAC9W,cAAc,IAAI,EAAE,EAAE;QAC7ByT,aAAa,CAACgE,gBAAgB,CAAC;;MAEjC,IAAI,CAAC/Y,GAAG,CAAC+J,aAAa,EAAE;IAC1B,CAAC,EAAE,GAAG,CAAC;IAEP,IAAI,CAAClK,cAAc,CAAC0K,WAAW,CAC7BpB,UAAU,EACV,EAAE,EACFoP,IAAI,EACJM,WAAW,EACX,IAAI,CAAC5Y,YAAY,CAACtI,EAAE,CACrB,CAACmP,SAAS,CAAC;MACVC,IAAI,EAAG6D,OAAY,IAAI;QACrBnG,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAE;UAChD/M,EAAE,EAAEiT,OAAO,CAACjT,EAAE;UACdsR,IAAI,EAAE2B,OAAO,CAAC3B,IAAI;UAClBG,WAAW,EAAEwB,OAAO,CAACxB,WAAW;UAChC5M,QAAQ,EAAE,IAAI,CAACA,QAAQ,CAACoO,OAAO,CAAC;UAChC6H,OAAO,EAAE,IAAI,CAACA,OAAO,CAAC7H,OAAO,CAAC;UAC9B4H,QAAQ,EAAE,IAAI,CAAClZ,WAAW,CAACsR,OAAO;SACnC,CAAC;QAEFmK,aAAa,CAACgE,gBAAgB,CAAC;QAC/B,IAAI,CAACzX,cAAc,GAAG,GAAG;QAEzBsC,UAAU,CAAC,MAAK;UACd,IAAI,CAAC7G,QAAQ,CAAC+M,IAAI,CAACc,OAAO,CAAC;UAC3B,IAAI,CAACtC,cAAc,EAAE;UACrB,IAAI,CAACvI,YAAY,CAACsM,WAAW,CAAC,4BAA4B,CAAC;UAC3D,IAAI,CAAC2M,gBAAgB,EAAE;QACzB,CAAC,EAAE,GAAG,CAAC;MACT,CAAC;MACD9S,KAAK,EAAGA,KAAU,IAAI;QACpBzB,OAAO,CAACyB,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;QACzD6O,aAAa,CAACgE,gBAAgB,CAAC;QAC/B,IAAI,CAAChZ,YAAY,CAAC4G,SAAS,CAAC,mCAAmC,CAAC;QAChE,IAAI,CAACqS,gBAAgB,EAAE;MACzB;KACD,CAAC;EACJ;EAEQF,kBAAkBA,CAACP,IAAU;IACnC,IAAIA,IAAI,CAACtP,IAAI,CAACgE,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,OAAc;IACzD,IAAIsL,IAAI,CAACtP,IAAI,CAACgE,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,OAAc;IACzD,IAAIsL,IAAI,CAACtP,IAAI,CAACgE,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,OAAc;IACzD,OAAO,MAAa;EACtB;EAEAgM,kBAAkBA,CAAA;IAChB,OAAO,KAAK;EACd;EAEAD,gBAAgBA,CAAA;IACd,IAAI,CAACpY,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACW,WAAW,GAAG,KAAK;IACxB,IAAI,CAACD,cAAc,GAAG,CAAC;EACzB;EAEA;EAEA4X,UAAUA,CAACnL,KAAgB;IACzBA,KAAK,CAACC,cAAc,EAAE;IACtBD,KAAK,CAACK,eAAe,EAAE;IACvB,IAAI,CAAC5M,UAAU,GAAG,IAAI;EACxB;EAEA2X,WAAWA,CAACpL,KAAgB;IAC1BA,KAAK,CAACC,cAAc,EAAE;IACtBD,KAAK,CAACK,eAAe,EAAE;IACvB;IACA,MAAMgL,IAAI,GAAIrL,KAAK,CAACsL,aAA6B,CAACC,qBAAqB,EAAE;IACzE,MAAMtY,CAAC,GAAG+M,KAAK,CAACE,OAAO;IACvB,MAAMhN,CAAC,GAAG8M,KAAK,CAACG,OAAO;IAEvB,IAAIlN,CAAC,GAAGoY,IAAI,CAACG,IAAI,IAAIvY,CAAC,GAAGoY,IAAI,CAACI,KAAK,IAAIvY,CAAC,GAAGmY,IAAI,CAACK,GAAG,IAAIxY,CAAC,GAAGmY,IAAI,CAACM,MAAM,EAAE;MACtE,IAAI,CAAClY,UAAU,GAAG,KAAK;;EAE3B;EAEAmY,MAAMA,CAAC5L,KAAgB;IACrBA,KAAK,CAACC,cAAc,EAAE;IACtBD,KAAK,CAACK,eAAe,EAAE;IACvB,IAAI,CAAC5M,UAAU,GAAG,KAAK;IAEvB,MAAM8W,KAAK,GAAGvK,KAAK,CAAC6L,YAAY,EAAEtB,KAAK;IACvC,IAAIA,KAAK,IAAIA,KAAK,CAAClR,MAAM,GAAG,CAAC,EAAE;MAC7B;MACAyS,KAAK,CAACC,IAAI,CAACxB,KAAK,CAAC,CAACjP,OAAO,CAAEkP,IAAI,IAAI;QACjC9T,OAAO,CAACC,GAAG,CACT,iCAAiC,EACjC6T,IAAI,CAAChb,IAAI,EACTgb,IAAI,CAACtP,IAAI,EACTsP,IAAI,CAAC7O,IAAI,CACV;QACD,IAAI,CAAC8O,UAAU,CAACD,IAAI,CAAC;MACvB,CAAC,CAAC;MAEF,IAAI,CAACxY,YAAY,CAACsM,WAAW,CAC3B,GAAGiM,KAAK,CAAClR,MAAM,8BAA8B,CAC9C;;EAEL;EAEA;EAEQsR,aAAaA,CAACH,IAAU,EAAEwB,OAAA,GAAkB,GAAG;IACrD,OAAO,IAAIlC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;MACrC,MAAMiC,MAAM,GAAG1V,QAAQ,CAACM,aAAa,CAAC,QAAQ,CAAC;MAC/C,MAAMqV,GAAG,GAAGD,MAAM,CAACE,UAAU,CAAC,IAAI,CAAC;MACnC,MAAMC,GAAG,GAAG,IAAIC,KAAK,EAAE;MAEvBD,GAAG,CAACE,MAAM,GAAG,MAAK;QAChB;QACA,MAAMC,QAAQ,GAAG,IAAI;QACrB,MAAMC,SAAS,GAAG,IAAI;QACtB,IAAI;UAAEC,KAAK;UAAEC;QAAM,CAAE,GAAGN,GAAG;QAE3B,IAAIK,KAAK,GAAGF,QAAQ,IAAIG,MAAM,GAAGF,SAAS,EAAE;UAC1C,MAAMG,KAAK,GAAGvP,IAAI,CAACwF,GAAG,CAAC2J,QAAQ,GAAGE,KAAK,EAAED,SAAS,GAAGE,MAAM,CAAC;UAC5DD,KAAK,IAAIE,KAAK;UACdD,MAAM,IAAIC,KAAK;;QAGjBV,MAAM,CAACQ,KAAK,GAAGA,KAAK;QACpBR,MAAM,CAACS,MAAM,GAAGA,MAAM;QAEtB;QACAR,GAAG,EAAEU,SAAS,CAACR,GAAG,EAAE,CAAC,EAAE,CAAC,EAAEK,KAAK,EAAEC,MAAM,CAAC;QAExC;QACAT,MAAM,CAACY,MAAM,CACVC,IAAI,IAAI;UACP,IAAIA,IAAI,EAAE;YACR,MAAMlC,cAAc,GAAG,IAAIjB,IAAI,CAAC,CAACmD,IAAI,CAAC,EAAEtC,IAAI,CAAChb,IAAI,EAAE;cACjD0L,IAAI,EAAEsP,IAAI,CAACtP,IAAI;cACf6R,YAAY,EAAE/S,IAAI,CAACsD,GAAG;aACvB,CAAC;YACFyM,OAAO,CAACa,cAAc,CAAC;WACxB,MAAM;YACLZ,MAAM,CAAC,IAAIlC,KAAK,CAAC,0BAA0B,CAAC,CAAC;;QAEjD,CAAC,EACD0C,IAAI,CAACtP,IAAI,EACT8Q,OAAO,CACR;MACH,CAAC;MAEDI,GAAG,CAACvD,OAAO,GAAG,MAAMmB,MAAM,CAAC,IAAIlC,KAAK,CAAC,sBAAsB,CAAC,CAAC;MAC7DsE,GAAG,CAACzG,GAAG,GAAGqH,GAAG,CAACC,eAAe,CAACzC,IAAI,CAAC;IACrC,CAAC,CAAC;EACJ;EAEA;EAEQpF,qBAAqBA,CAAA;IAC3B,IAAI,CAAC,IAAI,CAACzQ,QAAQ,EAAE;MAClB,IAAI,CAACA,QAAQ,GAAG,IAAI;MACpB;MACA,IAAI,CAACuY,mBAAmB,CAAC,IAAI,CAAC;;IAGhC;IACA,IAAI,IAAI,CAACrY,aAAa,EAAE;MACtBsY,YAAY,CAAC,IAAI,CAACtY,aAAa,CAAC;;IAGlC,IAAI,CAACA,aAAa,GAAGgB,UAAU,CAAC,MAAK;MACnC,IAAI,CAAClB,QAAQ,GAAG,KAAK;MACrB;MACA,IAAI,CAACuY,mBAAmB,CAAC,KAAK,CAAC;IACjC,CAAC,EAAE,IAAI,CAAC;EACV;EAEQA,mBAAmBA,CAACvY,QAAiB;IAC3C;IACA,MAAMyG,UAAU,GAAG,IAAI,CAACjT,gBAAgB,EAAEyB,EAAE,IAAI,IAAI,CAACzB,gBAAgB,EAAEqQ,GAAG;IAC1E,IAAI4C,UAAU,IAAI,IAAI,CAAClJ,YAAY,EAAEtI,EAAE,EAAE;MACvC8M,OAAO,CAACC,GAAG,CACT,gCAAgChC,QAAQ,YAAYyG,UAAU,EAAE,CACjE;MACD;MACA;;EAEJ;EAEA;EAEAgS,cAAcA,CAACzG,IAAU;IACvB,IAAI,CAAChI,UAAU,GAAGgI,IAAI;IACtB,IAAI,CAACzS,QAAQ,GAAG,IAAI;IACpB,IAAI,CAAC0K,eAAe,GAAG,IAAI;IAC3B,IAAI,CAAC8H,cAAc,EAAE;IACrB,IAAI,CAAC1U,YAAY,CAACsM,WAAW,CAAC,eAAe,CAAC;EAChD;EAEA+O,cAAcA,CAAA;IACZ,IAAI,CAAC3O,OAAO,EAAE;IACd,IAAI,CAAC1M,YAAY,CAACmY,QAAQ,CAAC,cAAc,CAAC;EAC5C;EAEA;EAEAmD,gBAAgBA,CAACzQ,OAAY;IAC3B,IAAI,CAACxQ,mBAAmB,CAACwQ,OAAO,CAAC;EACnC;EAEApR,cAAcA,CAAC8Q,SAAiB;IAC9B,OAAO,IAAI,CAACvI,gBAAgB,KAAKuI,SAAS;EAC5C;EAEAlQ,mBAAmBA,CAACwQ,OAAY;IAC9B,MAAMN,SAAS,GAAGM,OAAO,CAACjT,EAAE;IAC5B,MAAMya,QAAQ,GAAG,IAAI,CAACkJ,WAAW,CAAC1Q,OAAO,CAAC;IAE1C,IAAI,CAACwH,QAAQ,EAAE;MACb3N,OAAO,CAACyB,KAAK,CAAC,4CAA4C,EAAEoE,SAAS,CAAC;MACtE,IAAI,CAACvK,YAAY,CAAC4G,SAAS,CAAC,2BAA2B,CAAC;MACxD;;IAGF;IACA,IAAI,IAAI,CAACnN,cAAc,CAAC8Q,SAAS,CAAC,EAAE;MAClC,IAAI,CAACiR,iBAAiB,EAAE;MACxB;;IAGF;IACA,IAAI,CAACA,iBAAiB,EAAE;IAExB;IACA,IAAI,CAACC,kBAAkB,CAAC5Q,OAAO,EAAEwH,QAAQ,CAAC;EAC5C;EAEQoJ,kBAAkBA,CAAC5Q,OAAY,EAAEwH,QAAgB;IACvD,MAAM9H,SAAS,GAAGM,OAAO,CAACjT,EAAE;IAE5B,IAAI;MACF8M,OAAO,CAACC,GAAG,CACT,mCAAmC,EACnC4F,SAAS,EACT,MAAM,EACN8H,QAAQ,CACT;MAED,IAAI,CAACtQ,YAAY,GAAG,IAAI2Z,KAAK,CAACrJ,QAAQ,CAAC;MACvC,IAAI,CAACrQ,gBAAgB,GAAGuI,SAAS;MAEjC;MACA,MAAMoR,WAAW,GAAG,IAAI,CAACpQ,oBAAoB,CAAChB,SAAS,CAAC;MACxD,IAAI,CAACqB,oBAAoB,CAACrB,SAAS,EAAE;QACnCiB,QAAQ,EAAE,CAAC;QACXE,WAAW,EAAE,CAAC;QACdC,KAAK,EAAEgQ,WAAW,CAAChQ,KAAK,IAAI,CAAC;QAC7BF,QAAQ,EAAEkQ,WAAW,CAAClQ,QAAQ,IAAI;OACnC,CAAC;MAEF;MACA,IAAI,CAAC1J,YAAY,CAAC6Z,YAAY,GAAGD,WAAW,CAAChQ,KAAK,IAAI,CAAC;MAEvD;MACA,IAAI,CAAC5J,YAAY,CAACwD,gBAAgB,CAAC,gBAAgB,EAAE,MAAK;QACxD,IAAI,IAAI,CAACxD,YAAY,EAAE;UACrB,IAAI,CAAC6J,oBAAoB,CAACrB,SAAS,EAAE;YACnCkB,QAAQ,EAAE,IAAI,CAAC1J,YAAY,CAAC0J;WAC7B,CAAC;UACF/G,OAAO,CAACC,GAAG,CACT,oCAAoC,EACpC,IAAI,CAAC5C,YAAY,CAAC0J,QAAQ,CAC3B;;MAEL,CAAC,CAAC;MAEF,IAAI,CAAC1J,YAAY,CAACwD,gBAAgB,CAAC,YAAY,EAAE,MAAK;QACpD,IAAI,IAAI,CAACxD,YAAY,IAAI,IAAI,CAACC,gBAAgB,KAAKuI,SAAS,EAAE;UAC5D,MAAMmB,WAAW,GAAG,IAAI,CAAC3J,YAAY,CAAC2J,WAAW;UACjD,MAAMF,QAAQ,GAAIE,WAAW,GAAG,IAAI,CAAC3J,YAAY,CAAC0J,QAAQ,GAAI,GAAG;UACjE,IAAI,CAACG,oBAAoB,CAACrB,SAAS,EAAE;YAAEmB,WAAW;YAAEF;UAAQ,CAAE,CAAC;UAC/D,IAAI,CAACvL,GAAG,CAAC+J,aAAa,EAAE;;MAE5B,CAAC,CAAC;MAEF,IAAI,CAACjI,YAAY,CAACwD,gBAAgB,CAAC,OAAO,EAAE,MAAK;QAC/C,IAAI,CAACiW,iBAAiB,EAAE;MAC1B,CAAC,CAAC;MAEF,IAAI,CAACzZ,YAAY,CAACwD,gBAAgB,CAAC,OAAO,EAAGY,KAAK,IAAI;QACpDzB,OAAO,CAACyB,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,IAAI,CAACnG,YAAY,CAAC4G,SAAS,CAAC,iCAAiC,CAAC;QAC9D,IAAI,CAAC4U,iBAAiB,EAAE;MAC1B,CAAC,CAAC;MAEF;MACA,IAAI,CAACzZ,YAAY,CACdgE,IAAI,EAAE,CACN6H,IAAI,CAAC,MAAK;QACT,IAAI,CAAC5N,YAAY,CAACsM,WAAW,CAAC,6BAA6B,CAAC;MAC9D,CAAC,CAAC,CACDuB,KAAK,CAAE1H,KAAK,IAAI;QACfzB,OAAO,CAACyB,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;QAC3D,IAAI,CAACnG,YAAY,CAAC4G,SAAS,CAAC,qCAAqC,CAAC;QAClE,IAAI,CAAC4U,iBAAiB,EAAE;MAC1B,CAAC,CAAC;KACL,CAAC,OAAOrV,KAAK,EAAE;MACdzB,OAAO,CAACyB,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxD,IAAI,CAACnG,YAAY,CAAC4G,SAAS,CAAC,iCAAiC,CAAC;MAC9D,IAAI,CAAC4U,iBAAiB,EAAE;;EAE5B;EAEQA,iBAAiBA,CAAA;IACvB,IAAI,IAAI,CAACzZ,YAAY,EAAE;MACrB,IAAI,CAACA,YAAY,CAAC8Z,KAAK,EAAE;MACzB,IAAI,CAAC9Z,YAAY,CAAC2J,WAAW,GAAG,CAAC;MACjC,IAAI,CAAC3J,YAAY,GAAG,IAAI;;IAE1B,IAAI,CAACC,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAAC/B,GAAG,CAAC+J,aAAa,EAAE;EAC1B;EAEAuR,WAAWA,CAAC1Q,OAAY;IACtB;IACA,IAAIA,OAAO,CAACuH,QAAQ,EAAE,OAAOvH,OAAO,CAACuH,QAAQ;IAC7C,IAAIvH,OAAO,CAACwH,QAAQ,EAAE,OAAOxH,OAAO,CAACwH,QAAQ;IAC7C,IAAIxH,OAAO,CAACyH,KAAK,EAAE,OAAOzH,OAAO,CAACyH,KAAK;IAEvC;IACA,MAAMwJ,eAAe,GAAGjR,OAAO,CAACxB,WAAW,EAAE9B,IAAI,CAC9CgC,GAAQ,IAAKA,GAAG,CAACL,IAAI,EAAEgE,UAAU,CAAC,QAAQ,CAAC,IAAI3D,GAAG,CAACL,IAAI,KAAK,OAAO,CACrE;IAED,IAAI4S,eAAe,EAAE;MACnB,OAAOA,eAAe,CAACrS,GAAG,IAAIqS,eAAe,CAACpS,IAAI,IAAI,EAAE;;IAG1D,OAAO,EAAE;EACX;EAEAqS,aAAaA,CAAClR,OAAY;IACxB;IACA,MAAMN,SAAS,GAAGM,OAAO,CAACjT,EAAE,IAAI,EAAE;IAClC,MAAMokB,IAAI,GAAGzR,SAAS,CACnB0R,KAAK,CAAC,EAAE,CAAC,CACTC,MAAM,CAAC,CAACC,GAAW,EAAEC,IAAY,KAAKD,GAAG,GAAGC,IAAI,CAAClJ,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACrE,MAAMmJ,KAAK,GAAa,EAAE;IAE1B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;MAC3B,MAAM5B,MAAM,GAAG,CAAC,GAAI,CAACsB,IAAI,GAAGM,CAAC,GAAG,CAAC,IAAI,EAAG;MACxCD,KAAK,CAACtS,IAAI,CAAC2Q,MAAM,CAAC;;IAGpB,OAAO2B,KAAK;EACd;EAEAE,gBAAgBA,CAAC1R,OAAY;IAC3B,MAAMgB,IAAI,GAAG,IAAI,CAACN,oBAAoB,CAACV,OAAO,CAACjT,EAAE,CAAC;IAClD,MAAM4kB,UAAU,GAAG,EAAE;IACrB,OAAOpR,IAAI,CAACC,KAAK,CAAEQ,IAAI,CAACL,QAAQ,GAAG,GAAG,GAAIgR,UAAU,CAAC;EACvD;EAEAC,mBAAmBA,CAAC5R,OAAY;IAC9B,MAAMgB,IAAI,GAAG,IAAI,CAACN,oBAAoB,CAACV,OAAO,CAACjT,EAAE,CAAC;IAClD,OAAO,IAAI,CAAC8kB,eAAe,CAAC7Q,IAAI,CAACH,WAAW,CAAC;EAC/C;EAEA/Q,gBAAgBA,CAACkQ,OAAY;IAC3B,MAAMgB,IAAI,GAAG,IAAI,CAACN,oBAAoB,CAACV,OAAO,CAACjT,EAAE,CAAC;IAClD,MAAM6T,QAAQ,GAAGI,IAAI,CAACJ,QAAQ,IAAIZ,OAAO,CAAC8R,QAAQ,EAAElR,QAAQ,IAAI,CAAC;IAEjE,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;MAChC,OAAOA,QAAQ,CAAC,CAAC;;;IAGnB,OAAO,IAAI,CAACiR,eAAe,CAACjR,QAAQ,CAAC;EACvC;EAEQiR,eAAeA,CAACpH,OAAe;IACrC,MAAMD,OAAO,GAAGjK,IAAI,CAACC,KAAK,CAACiK,OAAO,GAAG,EAAE,CAAC;IACxC,MAAMsH,gBAAgB,GAAGxR,IAAI,CAACC,KAAK,CAACiK,OAAO,GAAG,EAAE,CAAC;IACjD,OAAO,GAAGD,OAAO,IAAIuH,gBAAgB,CAACjN,QAAQ,EAAE,CAAC4F,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EACrE;EAEAsH,gBAAgBA,CAAChS,OAAY,EAAEiS,SAAiB;IAC9C,MAAMvS,SAAS,GAAGM,OAAO,CAACjT,EAAE;IAE5B,IAAI,CAAC,IAAI,CAACmK,YAAY,IAAI,IAAI,CAACC,gBAAgB,KAAKuI,SAAS,EAAE;MAC7D;;IAGF,MAAMiS,UAAU,GAAG,EAAE;IACrB,MAAMO,cAAc,GAAID,SAAS,GAAGN,UAAU,GAAI,GAAG;IACrD,MAAMQ,QAAQ,GAAID,cAAc,GAAG,GAAG,GAAI,IAAI,CAAChb,YAAY,CAAC0J,QAAQ;IAEpE,IAAI,CAAC1J,YAAY,CAAC2J,WAAW,GAAGsR,QAAQ;EAC1C;EAEAC,gBAAgBA,CAACpS,OAAY;IAC3B,MAAMN,SAAS,GAAGM,OAAO,CAACjT,EAAE;IAC5B,MAAMiU,IAAI,GAAG,IAAI,CAACN,oBAAoB,CAAChB,SAAS,CAAC;IAEjD;IACA,MAAM2S,QAAQ,GAAGrR,IAAI,CAACF,KAAK,KAAK,CAAC,GAAG,GAAG,GAAGE,IAAI,CAACF,KAAK,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC;IAEpE,IAAI,CAACC,oBAAoB,CAACrB,SAAS,EAAE;MAAEoB,KAAK,EAAEuR;IAAQ,CAAE,CAAC;IAEzD,IAAI,IAAI,CAACnb,YAAY,IAAI,IAAI,CAACC,gBAAgB,KAAKuI,SAAS,EAAE;MAC5D,IAAI,CAACxI,YAAY,CAAC6Z,YAAY,GAAGsB,QAAQ;;IAG3C,IAAI,CAACld,YAAY,CAACsM,WAAW,CAAC,YAAY4Q,QAAQ,GAAG,CAAC;EACxD;EAEA;EAEAnjB,gBAAgBA,CAAC8Q,OAAY;IAC3B,IAAI,CAACoS,gBAAgB,CAACpS,OAAO,CAAC;EAChC;EAEA5Q,aAAaA,CAAC4Q,OAAY;IACxB,MAAMgB,IAAI,GAAG,IAAI,CAACN,oBAAoB,CAACV,OAAO,CAACjT,EAAE,CAAC;IAClD,OAAOiU,IAAI,CAACF,KAAK,IAAI,CAAC;EACxB;EAEAwR,WAAWA,CAAA;IACT,IAAI,CAACra,aAAa,CAAC+F,WAAW,EAAE;IAEhC;IACA,IAAI,IAAI,CAACxG,SAAS,EAAE;MAClB2S,aAAa,CAAC,IAAI,CAAC3S,SAAS,CAAC;;IAE/B,IAAI,IAAI,CAACP,cAAc,EAAE;MACvBkT,aAAa,CAAC,IAAI,CAAClT,cAAc,CAAC;;IAEpC,IAAI,IAAI,CAACe,aAAa,EAAE;MACtBsY,YAAY,CAAC,IAAI,CAACtY,aAAa,CAAC;;IAGlC;IACA,IAAI,IAAI,CAACjB,aAAa,EAAE;MACtB,IAAI,IAAI,CAACA,aAAa,CAACsV,KAAK,KAAK,WAAW,EAAE;QAC5C,IAAI,CAACtV,aAAa,CAACuV,IAAI,EAAE;;MAE3B,IAAI,CAACvV,aAAa,CAACoU,MAAM,EAAEoB,SAAS,EAAE,CAAC9N,OAAO,CAAE+N,KAAK,IAAKA,KAAK,CAACF,IAAI,EAAE,CAAC;;IAGzE;IACA,IAAI,CAACqE,iBAAiB,EAAE;EAC1B;;;uBA/gFW/b,oBAAoB,EAAA9J,EAAA,CAAAynB,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA3nB,EAAA,CAAAynB,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAA7nB,EAAA,CAAAynB,iBAAA,CAAAG,EAAA,CAAAE,MAAA,GAAA9nB,EAAA,CAAAynB,iBAAA,CAAAM,EAAA,CAAA5d,cAAA,GAAAnK,EAAA,CAAAynB,iBAAA,CAAAO,EAAA,CAAAC,WAAA,GAAAjoB,EAAA,CAAAynB,iBAAA,CAAAS,EAAA,CAAAC,YAAA,GAAAnoB,EAAA,CAAAynB,iBAAA,CAAAznB,EAAA,CAAAooB,iBAAA;IAAA;EAAA;;;YAApBte,oBAAoB;MAAAue,SAAA;MAAAC,SAAA,WAAAC,2BAAAC,EAAA,EAAAjE,GAAA;QAAA,IAAAiE,EAAA;;;;;;;;;;;;;;;;;;;;;;;UClBjCxoB,EAAA,CAAAC,SAAA,kBAYS;UAcTD,EAAA,CAAAE,cAAA,aASC;UAsEKF,EAAA,CAAAY,UAAA,mBAAA6nB,sDAAA;YAAA,OAASlE,GAAA,CAAAxM,qBAAA,EAAuB;UAAA,EAAC;UAmBjC/X,EAAA,CAAAC,SAAA,WAGK;UACPD,EAAA,CAAAI,YAAA,EAAS;UAGTJ,EAAA,CAAAE,cAAA,aAAuE;UAejEF,EAAA,CAAAY,UAAA,mBAAA8nB,oDAAA;YAAA,OAASnE,GAAA,CAAAxiB,eAAA,CAAAwiB,GAAA,CAAA/jB,gBAAA,kBAAA+jB,GAAA,CAAA/jB,gBAAA,CAAAyB,EAAA,CAAsC;UAAA,EAAC;UAZlDjC,EAAA,CAAAI,YAAA,EAgBE;UAEFJ,EAAA,CAAAyD,UAAA,KAAAklB,oCAAA,kBAaO;UACT3oB,EAAA,CAAAI,YAAA,EAAM;UAGNJ,EAAA,CAAAE,cAAA,eAAmC;UAY/BF,EAAA,CAAAG,MAAA,IACF;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACLJ,EAAA,CAAAE,cAAA,eAA8D;UAE5DF,EAAA,CAAAyD,UAAA,KAAAmlB,oCAAA,kBAkCM;UAEN5oB,EAAA,CAAAyD,UAAA,KAAAolB,qCAAA,mBAMO;UACT7oB,EAAA,CAAAI,YAAA,EAAM;UAKVJ,EAAA,CAAAE,cAAA,eAA0D;UAGtDF,EAAA,CAAAY,UAAA,mBAAAkoB,uDAAA;YAAA,OAASvE,GAAA,CAAApO,cAAA,EAAgB;UAAA,EAAC;UAoB1BnW,EAAA,CAAAC,SAAA,aAAoD;UACtDD,EAAA,CAAAI,YAAA,EAAS;UAGTJ,EAAA,CAAAE,cAAA,kBAoBC;UAnBCF,EAAA,CAAAY,UAAA,mBAAAmoB,uDAAA;YAAA,OAASxE,GAAA,CAAAjO,cAAA,EAAgB;UAAA,EAAC;UAoB1BtW,EAAA,CAAAC,SAAA,aAAoD;UACtDD,EAAA,CAAAI,YAAA,EAAS;UAGTJ,EAAA,CAAAE,cAAA,kBAcC;UAbCF,EAAA,CAAAY,UAAA,mBAAAooB,uDAAA;YAAA,OAASzE,GAAA,CAAArjB,YAAA,EAAc;UAAA,EAAC;UAcxBlB,EAAA,CAAAC,SAAA,aAA6B;UAC/BD,EAAA,CAAAI,YAAA,EAAS;UAGTJ,EAAA,CAAAE,cAAA,kBAgBC;UAfCF,EAAA,CAAAY,UAAA,mBAAAqoB,uDAAA;YAAA,OAAS1E,GAAA,CAAApR,kBAAA,EAAoB;UAAA,EAAC;UAgB9BnT,EAAA,CAAAC,SAAA,aAGK;UACPD,EAAA,CAAAI,YAAA,EAAS;UAGTJ,EAAA,CAAAE,cAAA,kBAeC;UAdCF,EAAA,CAAAY,UAAA,mBAAAsoB,uDAAA;YAAA,OAAS3E,GAAA,CAAAzM,cAAA,EAAgB;UAAA,EAAC;UAe1B9X,EAAA,CAAAC,SAAA,aAAiC;UACnCD,EAAA,CAAAI,YAAA,EAAS;UAIXJ,EAAA,CAAAyD,UAAA,KAAA0lB,oCAAA,mBA8EM;UACRnpB,EAAA,CAAAI,YAAA,EAAS;UAGTJ,EAAA,CAAAE,cAAA,oBAQC;UALCF,EAAA,CAAAY,UAAA,oBAAAwoB,sDAAAjmB,MAAA;YAAA,OAAUohB,GAAA,CAAAxG,QAAA,CAAA5a,MAAA,CAAgB;UAAA,EAAC,sBAAAkmB,wDAAAlmB,MAAA;YAAA,OACfohB,GAAA,CAAAf,UAAA,CAAArgB,MAAA,CAAkB;UAAA,EADH,uBAAAmmB,yDAAAnmB,MAAA;YAAA,OAEdohB,GAAA,CAAAd,WAAA,CAAAtgB,MAAA,CAAmB;UAAA,EAFL,kBAAAomB,oDAAApmB,MAAA;YAAA,OAGnBohB,GAAA,CAAAN,MAAA,CAAA9gB,MAAA,CAAc;UAAA,EAHK;UAO3BnD,EAAA,CAAAyD,UAAA,KAAA+lB,oCAAA,kBAoDM;UAGNxpB,EAAA,CAAAyD,UAAA,KAAAgmB,oCAAA,kBAsBM;UAGNzpB,EAAA,CAAAyD,UAAA,KAAAimB,oCAAA,kBA0BM;UAGN1pB,EAAA,CAAAyD,UAAA,KAAAkmB,oCAAA,kBAgWM;UACR3pB,EAAA,CAAAI,YAAA,EAAO;UAGPJ,EAAA,CAAAE,cAAA,kBAEC;UAGGF,EAAA,CAAAY,UAAA,sBAAAgpB,wDAAA;YAAA,OAAYrF,GAAA,CAAA1P,WAAA,EAAa;UAAA,EAAC;UAI1B7U,EAAA,CAAAE,cAAA,eAAqC;UAIjCF,EAAA,CAAAY,UAAA,mBAAAipB,uDAAA;YAAA,OAAStF,GAAA,CAAA9K,iBAAA,EAAmB;UAAA,EAAC;UAgB7BzZ,EAAA,CAAAC,SAAA,aAA4B;UAC9BD,EAAA,CAAAI,YAAA,EAAS;UAGTJ,EAAA,CAAAE,cAAA,kBAiBC;UAfCF,EAAA,CAAAY,UAAA,mBAAAkpB,uDAAA;YAAA,OAASvF,GAAA,CAAAxK,oBAAA,EAAsB;UAAA,EAAC;UAgBhC/Z,EAAA,CAAAC,SAAA,aAAgC;UAClCD,EAAA,CAAAI,YAAA,EAAS;UAGTJ,EAAA,CAAAE,cAAA,kBAwBC;UAtBCF,EAAA,CAAAY,UAAA,uBAAAmpB,2DAAA5mB,MAAA;YAAA,OAAaohB,GAAA,CAAAjC,aAAA,CAAAnf,MAAA,CAAqB;UAAA,EAAC,qBAAA6mB,yDAAA7mB,MAAA;YAAA,OACxBohB,GAAA,CAAA9a,WAAA,CAAAtG,MAAA,CAAmB;UAAA,EADK,wBAAA8mB,4DAAA9mB,MAAA;YAAA,OAErBohB,GAAA,CAAAjb,cAAA,CAAAnG,MAAA,CAAsB;UAAA,EAFD,wBAAA+mB,4DAAA/mB,MAAA;YAAA,OAGrBohB,GAAA,CAAAjC,aAAA,CAAAnf,MAAA,CAAqB;UAAA,EAHA,sBAAAgnB,0DAAAhnB,MAAA;YAAA,OAIvBohB,GAAA,CAAA9a,WAAA,CAAAtG,MAAA,CAAmB;UAAA,EAJI,yBAAAinB,6DAAAjnB,MAAA;YAAA,OAKpBohB,GAAA,CAAAjb,cAAA,CAAAnG,MAAA,CAAsB;UAAA,EALF;UAuBnCnD,EAAA,CAAAC,SAAA,SAGK;UAGLD,EAAA,CAAAyD,UAAA,KAAA4mB,oCAAA,kBAYO;UACTrqB,EAAA,CAAAI,YAAA,EAAS;UAIXJ,EAAA,CAAAE,cAAA,eAAyC;UAIrCF,EAAA,CAAAY,UAAA,qBAAA0pB,2DAAAnnB,MAAA;YAAA,OAAWohB,GAAA,CAAA7G,cAAA,CAAAva,MAAA,CAAsB;UAAA,EAAC,mBAAAonB,yDAAApnB,MAAA;YAAA,OACzBohB,GAAA,CAAA/G,aAAA,CAAAra,MAAA,CAAqB;UAAA,EADI,mBAAAqnB,yDAAA;YAAA,OAEzBjG,GAAA,CAAA1G,YAAA,EAAc;UAAA,EAFW;UAoBnC7d,EAAA,CAAAI,YAAA,EAAW;UAIdJ,EAAA,CAAAE,cAAA,kBA0BC;UACCF,EAAA,CAAAyD,UAAA,KAAAgnB,kCAAA,gBAA4D;UAC5DzqB,EAAA,CAAAyD,UAAA,KAAAinB,oCAAA,kBAUO;UACT1qB,EAAA,CAAAI,YAAA,EAAS;UAIXJ,EAAA,CAAAyD,UAAA,KAAAknB,oCAAA,kBAkDM;UAGN3qB,EAAA,CAAAyD,UAAA,KAAAmnB,oCAAA,mBAsJM;UAGN5qB,EAAA,CAAAE,cAAA,qBAOE;UAHAF,EAAA,CAAAY,UAAA,oBAAAiqB,uDAAA1nB,MAAA;YAAA,OAAUohB,GAAA,CAAA5B,cAAA,CAAAxf,MAAA,CAAsB;UAAA,EAAC;UAJnCnD,EAAA,CAAAI,YAAA,EAOE;UAIJJ,EAAA,CAAAyD,UAAA,KAAAqnB,oCAAA,kBAYO;UAGP9qB,EAAA,CAAAyD,UAAA,KAAAsnB,oCAAA,mBAiHM;UACR/qB,EAAA,CAAAI,YAAA,EAAM;;;UA3wCIJ,EAAA,CAAAK,SAAA,IAAqE;UAArEL,EAAA,CAAAkC,UAAA,SAAAqiB,GAAA,CAAA/jB,gBAAA,kBAAA+jB,GAAA,CAAA/jB,gBAAA,CAAA2B,KAAA,yCAAAnC,EAAA,CAAAoC,aAAA,CAAqE,QAAAmiB,GAAA,CAAA/jB,gBAAA,kBAAA+jB,GAAA,CAAA/jB,gBAAA,CAAAc,QAAA;UAkBpEtB,EAAA,CAAAK,SAAA,GAAgC;UAAhCL,EAAA,CAAAkC,UAAA,SAAAqiB,GAAA,CAAA/jB,gBAAA,kBAAA+jB,GAAA,CAAA/jB,gBAAA,CAAAC,QAAA,CAAgC;UA4BjCT,EAAA,CAAAK,SAAA,GACF;UADEL,EAAA,CAAAM,kBAAA,OAAAikB,GAAA,CAAA/jB,gBAAA,kBAAA+jB,GAAA,CAAA/jB,gBAAA,CAAAc,QAAA,wBACF;UAIKtB,EAAA,CAAAK,SAAA,GAAkB;UAAlBL,EAAA,CAAAkC,UAAA,SAAAqiB,GAAA,CAAAtX,YAAA,CAAkB;UAmCdjN,EAAA,CAAAK,SAAA,GAAmB;UAAnBL,EAAA,CAAAkC,UAAA,UAAAqiB,GAAA,CAAAtX,YAAA,CAAmB;UA2E5BjN,EAAA,CAAAK,SAAA,GAA2D;UAA3DL,EAAA,CAAAqC,WAAA,eAAAkiB,GAAA,CAAAtZ,UAAA,6BAA2D,UAAAsZ,GAAA,CAAAtZ,UAAA;UAmB3DjL,EAAA,CAAAK,SAAA,GAAyC;UAAzCL,EAAA,CAAAqC,WAAA,YAAAkiB,GAAA,CAAA9Z,SAAA,eAAyC;UACzCzK,EAAA,CAAAkC,UAAA,aAAAqiB,GAAA,CAAA9Z,SAAA,CAAsB;UAOpBzK,EAAA,CAAAK,SAAA,GAAkE;UAAlEL,EAAA,CAAAqC,WAAA,cAAAkiB,GAAA,CAAA9Z,SAAA,sCAAkE;UAiBpEzK,EAAA,CAAAK,SAAA,GAA6D;UAA7DL,EAAA,CAAAqC,WAAA,eAAAkiB,GAAA,CAAAnjB,YAAA,6BAA6D,UAAAmjB,GAAA,CAAAnjB,YAAA;UAU9DpB,EAAA,CAAAK,SAAA,GAAkB;UAAlBL,EAAA,CAAAkC,UAAA,SAAAqiB,GAAA,CAAAnjB,YAAA,CAAkB;UAwFrBpB,EAAA,CAAAK,SAAA,GAA0E;UAA1EL,EAAA,CAAAqC,WAAA,eAAAkiB,GAAA,CAAAzY,UAAA,4CAA0E;UAIvE9L,EAAA,CAAAK,SAAA,GAAgB;UAAhBL,EAAA,CAAAkC,UAAA,SAAAqiB,GAAA,CAAAzY,UAAA,CAAgB;UAuDhB9L,EAAA,CAAAK,SAAA,GAAe;UAAfL,EAAA,CAAAkC,UAAA,SAAAqiB,GAAA,CAAA9Z,SAAA,CAAe;UAyBfzK,EAAA,CAAAK,SAAA,GAAyC;UAAzCL,EAAA,CAAAkC,UAAA,UAAAqiB,GAAA,CAAA9Z,SAAA,IAAA8Z,GAAA,CAAAld,QAAA,CAAAqK,MAAA,OAAyC;UA6BzC1R,EAAA,CAAAK,SAAA,GAAuC;UAAvCL,EAAA,CAAAkC,UAAA,UAAAqiB,GAAA,CAAA9Z,SAAA,IAAA8Z,GAAA,CAAAld,QAAA,CAAAqK,MAAA,KAAuC;UAuWxC1R,EAAA,CAAAK,SAAA,GAAyB;UAAzBL,EAAA,CAAAkC,UAAA,cAAAqiB,GAAA,CAAAnX,WAAA,CAAyB;UAmBrBpN,EAAA,CAAAK,SAAA,GAAgE;UAAhEL,EAAA,CAAAqC,WAAA,eAAAkiB,GAAA,CAAA3Z,eAAA,6BAAgE,UAAA2Z,GAAA,CAAA3Z,eAAA;UAsBhE5K,EAAA,CAAAK,SAAA,GAAmE;UAAnEL,EAAA,CAAAqC,WAAA,eAAAkiB,GAAA,CAAA1Z,kBAAA,6BAAmE,UAAA0Z,GAAA,CAAA1Z,kBAAA;UA4BnE7K,EAAA,CAAAK,SAAA,GAAiE;UAAjEL,EAAA,CAAAqC,WAAA,eAAAkiB,GAAA,CAAAxY,gBAAA,6BAAiE,UAAAwY,GAAA,CAAAxY,gBAAA,uCAAAwY,GAAA,CAAAxY,gBAAA;UAQ/D/L,EAAA,CAAAK,SAAA,GAAgE;UAAhEL,EAAA,CAAA6E,UAAA,CAAA0f,GAAA,CAAAxY,gBAAA,uCAAgE;UAChE/L,EAAA,CAAAqC,WAAA,cAAAkiB,GAAA,CAAAxY,gBAAA,gCAAmE;UAKlE/L,EAAA,CAAAK,SAAA,GAAsB;UAAtBL,EAAA,CAAAkC,UAAA,SAAAqiB,GAAA,CAAAxY,gBAAA,CAAsB;UAuCzB/L,EAAA,CAAAK,SAAA,GAA8B;UAA9BL,EAAA,CAAAkC,UAAA,aAAAqiB,GAAA,CAAA/W,eAAA,GAA8B;UAsBhCxN,EAAA,CAAAK,SAAA,GAEC;UAFDL,EAAA,CAAAqC,WAAA,gBAAAkiB,GAAA,CAAAnX,WAAA,CAAA0H,KAAA,IAAAyP,GAAA,CAAArZ,gBAAA,yBAEC,YAAAqZ,GAAA,CAAAnX,WAAA,CAAA0H,KAAA,IAAAyP,GAAA,CAAArZ,gBAAA;UAjBDlL,EAAA,CAAAkC,UAAA,cAAAqiB,GAAA,CAAAnX,WAAA,CAAA0H,KAAA,IAAAyP,GAAA,CAAArZ,gBAAA,CAAmD;UAyBpBlL,EAAA,CAAAK,SAAA,GAAuB;UAAvBL,EAAA,CAAAkC,UAAA,UAAAqiB,GAAA,CAAArZ,gBAAA,CAAuB;UAEnDlL,EAAA,CAAAK,SAAA,GAAsB;UAAtBL,EAAA,CAAAkC,UAAA,SAAAqiB,GAAA,CAAArZ,gBAAA,CAAsB;UAe1BlL,EAAA,CAAAK,SAAA,GAAqB;UAArBL,EAAA,CAAAkC,UAAA,SAAAqiB,GAAA,CAAA3Z,eAAA,CAAqB;UAqDrB5K,EAAA,CAAAK,SAAA,GAAwB;UAAxBL,EAAA,CAAAkC,UAAA,SAAAqiB,GAAA,CAAA1Z,kBAAA,CAAwB;UA6JzB7K,EAAA,CAAAK,SAAA,GAA+B;UAA/BL,EAAA,CAAAkC,UAAA,WAAAqiB,GAAA,CAAAhB,kBAAA,GAA+B;UAOhCvjB,EAAA,CAAAK,SAAA,GAA2D;UAA3DL,EAAA,CAAAkC,UAAA,SAAAqiB,GAAA,CAAA3Z,eAAA,IAAA2Z,GAAA,CAAA1Z,kBAAA,IAAA0Z,GAAA,CAAAnjB,YAAA,CAA2D;UAe3DpB,EAAA,CAAAK,SAAA,GAAsB;UAAtBL,EAAA,CAAAkC,UAAA,SAAAqiB,GAAA,CAAAxY,gBAAA,CAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}