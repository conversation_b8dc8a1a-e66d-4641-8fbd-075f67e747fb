{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/OneDrive/Bureau/Project PI/devBridge/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { BehaviorSubject, throwError, from } from 'rxjs';\nimport { map, catchError, switchMap } from 'rxjs/operators';\nimport { INITIATE_CALL_MUTATION, ACCEPT_CALL_MUTATION, REJECT_CALL_MUTATION, END_CALL_MUTATION, TOGGLE_CALL_MEDIA_MUTATION, INCOMING_CALL_SUBSCRIPTION } from '../graphql/message.graphql';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"apollo-angular\";\nimport * as i2 from \"./logger.service\";\nexport class CallService {\n  constructor(apollo, logger) {\n    this.apollo = apollo;\n    this.logger = logger;\n    // État des appels\n    this.activeCall = new BehaviorSubject(null);\n    this.incomingCall = new BehaviorSubject(null);\n    // Observables publics\n    this.activeCall$ = this.activeCall.asObservable();\n    this.incomingCall$ = this.incomingCall.asObservable();\n    // Propriétés pour la gestion des sons\n    this.sounds = {};\n    this.isPlaying = {};\n    this.muted = false;\n    // États simples pour les médias\n    this.isVideoEnabled = true;\n    this.isAudioEnabled = true;\n    this.preloadSounds();\n    this.initializeSubscriptions();\n  }\n  /**\n   * Initialise les subscriptions avec un délai pour s'assurer que l'authentification est prête\n   */\n  initializeSubscriptions() {\n    // Attendre un peu pour s'assurer que l'authentification est prête\n    setTimeout(() => {\n      this.subscribeToIncomingCalls();\n    }, 1000);\n    // Réessayer après 5 secondes si la première tentative échoue\n    setTimeout(() => {\n      if (!this.incomingCall.value) {\n        console.log('🔄 [CallService] Retrying subscription initialization...');\n        this.subscribeToIncomingCalls();\n      }\n    }, 5000);\n  }\n  /**\n   * Précharge les sons utilisés dans l'application\n   */\n  preloadSounds() {\n    this.loadSound('ringtone', 'assets/sounds/ringtone.mp3');\n    this.loadSound('call-end', 'assets/sounds/call-end.mp3');\n    this.loadSound('call-connected', 'assets/sounds/call-connected.mp3');\n  }\n  /**\n   * Charge un fichier audio\n   */\n  loadSound(name, path) {\n    try {\n      const audio = new Audio(path);\n      audio.load();\n      this.sounds[name] = audio;\n      this.isPlaying[name] = false;\n      audio.addEventListener('ended', () => {\n        this.isPlaying[name] = false;\n      });\n    } catch (error) {\n      console.error(`Error loading sound ${name}:`, error);\n    }\n  }\n  /**\n   * Joue un son\n   */\n  play(name, loop = false) {\n    if (this.muted) return;\n    try {\n      const sound = this.sounds[name];\n      if (!sound) return;\n      sound.loop = loop;\n      if (!this.isPlaying[name]) {\n        sound.currentTime = 0;\n        sound.play().catch(console.error);\n        this.isPlaying[name] = true;\n      }\n    } catch (error) {\n      console.error(`Error playing sound ${name}:`, error);\n    }\n  }\n  /**\n   * Arrête un son\n   */\n  stop(name) {\n    try {\n      const sound = this.sounds[name];\n      if (!sound) return;\n      if (this.isPlaying[name]) {\n        sound.pause();\n        sound.currentTime = 0;\n        this.isPlaying[name] = false;\n      }\n    } catch (error) {\n      console.error(`Error stopping sound ${name}:`, error);\n    }\n  }\n  /**\n   * S'abonne aux appels entrants\n   */\n  subscribeToIncomingCalls() {\n    console.log('🔔 [CallService] Setting up incoming call subscription...');\n    try {\n      this.apollo.subscribe({\n        query: INCOMING_CALL_SUBSCRIPTION,\n        errorPolicy: 'all' // Continuer même en cas d'erreur\n      }).subscribe({\n        next: ({\n          data,\n          errors\n        }) => {\n          if (errors) {\n            console.warn('⚠️ [CallService] GraphQL errors in subscription:', errors);\n          }\n          if (data?.incomingCall) {\n            console.log('📞 [CallService] Incoming call received:', {\n              callId: data.incomingCall.id,\n              callType: data.incomingCall.type,\n              caller: data.incomingCall.caller?.username,\n              conversationId: data.incomingCall.conversationId\n            });\n            this.handleIncomingCall(data.incomingCall);\n          }\n        },\n        error: error => {\n          console.error('❌ [CallService] Error in incoming call subscription:', error);\n          // Réessayer après 5 secondes en cas d'erreur\n          setTimeout(() => {\n            console.log('🔄 [CallService] Retrying incoming call subscription...');\n            this.subscribeToIncomingCalls();\n          }, 5000);\n        },\n        complete: () => {\n          console.log('🔚 [CallService] Incoming call subscription completed');\n          // Réessayer si la subscription se ferme de manière inattendue\n          setTimeout(() => {\n            console.log('🔄 [CallService] Restarting subscription after completion...');\n            this.subscribeToIncomingCalls();\n          }, 2000);\n        }\n      });\n    } catch (error) {\n      console.error('❌ [CallService] Failed to create subscription:', error);\n      // Réessayer après 3 secondes\n      setTimeout(() => {\n        this.subscribeToIncomingCalls();\n      }, 3000);\n    }\n  }\n  /**\n   * Méthode publique pour forcer la réinitialisation de la subscription\n   */\n  reinitializeSubscription() {\n    console.log('🔄 [CallService] Manually reinitializing subscription...');\n    this.subscribeToIncomingCalls();\n  }\n  /**\n   * Gère un appel entrant\n   */\n  handleIncomingCall(call) {\n    console.log('🔔 [CallService] Handling incoming call:', {\n      callId: call.id,\n      callType: call.type,\n      caller: call.caller?.username,\n      conversationId: call.conversationId\n    });\n    this.incomingCall.next(call);\n    this.play('ringtone', true);\n    console.log('🔊 [CallService] Ringtone started, call notification sent to UI');\n  }\n  /**\n   * Initie un appel WebRTC\n   */\n  initiateCall(recipientId, callType, conversationId) {\n    console.log('🔄 [CallService] Initiating call:', {\n      recipientId,\n      callType,\n      conversationId\n    });\n    if (!recipientId) {\n      const error = new Error('Recipient ID is required');\n      console.error('❌ [CallService] initiateCall error:', error);\n      return throwError(() => error);\n    }\n    // Générer un ID unique pour l'appel\n    const callId = `call_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n    // Pour l'instant, utiliser une offre WebRTC factice\n    const offer = JSON.stringify({\n      type: 'offer',\n      sdp: 'v=0\\r\\no=- 0 0 IN IP4 127.0.0.1\\r\\ns=-\\r\\nt=0 0\\r\\n'\n    });\n    const variables = {\n      recipientId,\n      callType: callType,\n      callId,\n      offer,\n      conversationId\n    };\n    console.log('📤 [CallService] Sending initiate call mutation:', variables);\n    return this.apollo.mutate({\n      mutation: INITIATE_CALL_MUTATION,\n      variables\n    }).pipe(map(result => {\n      console.log('✅ [CallService] Call initiated successfully:', result);\n      if (!result.data?.initiateCall) {\n        throw new Error('No call data received from server');\n      }\n      const call = result.data.initiateCall;\n      console.log('📞 [CallService] Call details:', {\n        id: call.id,\n        type: call.type,\n        status: call.status,\n        caller: call.caller?.username,\n        recipient: call.recipient?.username\n      });\n      // Mettre à jour l'état local\n      this.activeCall.next(call);\n      return call;\n    }), catchError(error => {\n      console.error('❌ [CallService] initiateCall error:', error);\n      this.logger.error('Error initiating call:', error);\n      let errorMessage = \"Erreur lors de l'initiation de l'appel\";\n      if (error.networkError) {\n        errorMessage = 'Erreur de connexion réseau';\n      } else if (error.graphQLErrors?.length > 0) {\n        errorMessage = error.graphQLErrors[0].message || errorMessage;\n      }\n      return throwError(() => new Error(errorMessage));\n    }));\n  }\n  /**\n   * Accepte un appel entrant\n   */\n  acceptCall(incomingCall) {\n    console.log('🔄 [CallService] Accepting call:', incomingCall.id);\n    // Générer une réponse WebRTC factice\n    const answer = JSON.stringify({\n      type: 'answer',\n      sdp: 'v=0\\r\\no=- 0 0 IN IP4 127.0.0.1\\r\\ns=-\\r\\nt=0 0\\r\\n'\n    });\n    return this.apollo.mutate({\n      mutation: ACCEPT_CALL_MUTATION,\n      variables: {\n        callId: incomingCall.id,\n        answer\n      }\n    }).pipe(switchMap(result => {\n      console.log('✅ [CallService] Call accepted successfully:', result);\n      if (!result.data?.acceptCall) {\n        throw new Error('No call data received from server');\n      }\n      const call = result.data.acceptCall;\n      // Arrêter la sonnerie\n      this.stop('ringtone');\n      this.play('call-connected');\n      // Démarrer les médias pour l'appel de manière asynchrone\n      return from(this.startMediaForCall(incomingCall, call));\n    }), catchError(error => {\n      console.error('❌ [CallService] acceptCall error:', error);\n      this.logger.error('Error accepting call:', error);\n      return throwError(() => new Error(\"Erreur lors de l'acceptation de l'appel\"));\n    }));\n  }\n  /**\n   * Rejette un appel entrant\n   */\n  rejectCall(callId, reason) {\n    console.log('🔄 [CallService] Rejecting call:', callId, reason);\n    return this.apollo.mutate({\n      mutation: REJECT_CALL_MUTATION,\n      variables: {\n        callId,\n        reason: reason || 'User rejected'\n      }\n    }).pipe(map(result => {\n      console.log('✅ [CallService] Call rejected successfully:', result);\n      if (!result.data?.rejectCall) {\n        throw new Error('No response received from server');\n      }\n      // Mettre à jour l'état local\n      this.incomingCall.next(null);\n      this.activeCall.next(null);\n      // Arrêter la sonnerie\n      this.stop('ringtone');\n      return result.data.rejectCall;\n    }), catchError(error => {\n      console.error('❌ [CallService] rejectCall error:', error);\n      this.logger.error('Error rejecting call:', error);\n      return throwError(() => new Error(\"Erreur lors du rejet de l'appel\"));\n    }));\n  }\n  /**\n   * Termine un appel en cours\n   */\n  endCall(callId) {\n    console.log('🔄 [CallService] Ending call:', callId);\n    return this.apollo.mutate({\n      mutation: END_CALL_MUTATION,\n      variables: {\n        callId,\n        feedback: null // Pas de feedback pour l'instant\n      }\n    }).pipe(map(result => {\n      console.log('✅ [CallService] Call ended successfully:', result);\n      if (!result.data?.endCall) {\n        throw new Error('No response received from server');\n      }\n      // Mettre à jour l'état local\n      this.activeCall.next(null);\n      this.incomingCall.next(null);\n      // Arrêter tous les sons\n      this.stopAllSounds();\n      this.play('call-end');\n      return result.data.endCall;\n    }), catchError(error => {\n      console.error('❌ [CallService] endCall error:', error);\n      this.logger.error('Error ending call:', error);\n      return throwError(() => new Error(\"Erreur lors de la fin de l'appel\"));\n    }));\n  }\n  /**\n   * Bascule l'état des médias (audio/vidéo) pendant un appel\n   */\n  toggleMedia(callId, enableVideo, enableAudio) {\n    console.log('🔄 [CallService] Toggling media:', {\n      callId,\n      enableVideo,\n      enableAudio\n    });\n    return this.apollo.mutate({\n      mutation: TOGGLE_CALL_MEDIA_MUTATION,\n      variables: {\n        callId,\n        video: enableVideo,\n        audio: enableAudio\n      }\n    }).pipe(map(result => {\n      console.log('✅ [CallService] Media toggled successfully:', result);\n      if (!result.data?.toggleCallMedia) {\n        throw new Error('No response received from server');\n      }\n      return result.data.toggleCallMedia;\n    }), catchError(error => {\n      console.error('❌ [CallService] toggleMedia error:', error);\n      this.logger.error('Error toggling media:', error);\n      return throwError(() => new Error('Erreur lors du changement des médias'));\n    }));\n  }\n  /**\n   * Démarre les médias pour un appel accepté\n   */\n  startMediaForCall(incomingCall, call) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      console.log('🎥 [CallService] Call connected - playing connection sound');\n      // Jouer le son de connexion\n      _this.play('call-connected');\n      // Mettre à jour l'état local\n      _this.activeCall.next(call);\n      _this.incomingCall.next(null); // Supprimer l'appel entrant\n      return call;\n    })();\n  }\n  /**\n   * Active/désactive la vidéo\n   */\n  toggleVideo() {\n    this.isVideoEnabled = !this.isVideoEnabled;\n    console.log('📹 [CallService] Video toggled:', this.isVideoEnabled);\n    return this.isVideoEnabled;\n  }\n  /**\n   * Active/désactive l'audio\n   */\n  toggleAudio() {\n    this.isAudioEnabled = !this.isAudioEnabled;\n    console.log('🎤 [CallService] Audio toggled:', this.isAudioEnabled);\n    return this.isAudioEnabled;\n  }\n  /**\n   * Obtient l'état de la vidéo\n   */\n  getVideoEnabled() {\n    return this.isVideoEnabled;\n  }\n  /**\n   * Obtient l'état de l'audio\n   */\n  getAudioEnabled() {\n    return this.isAudioEnabled;\n  }\n  /**\n   * Arrête tous les sons\n   */\n  stopAllSounds() {\n    Object.keys(this.sounds).forEach(name => {\n      this.stop(name);\n    });\n  }\n  ngOnDestroy() {\n    this.stopAllSounds();\n  }\n  static {\n    this.ɵfac = function CallService_Factory(t) {\n      return new (t || CallService)(i0.ɵɵinject(i1.Apollo), i0.ɵɵinject(i2.LoggerService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: CallService,\n      factory: CallService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["BehaviorSubject", "throwError", "from", "map", "catchError", "switchMap", "INITIATE_CALL_MUTATION", "ACCEPT_CALL_MUTATION", "REJECT_CALL_MUTATION", "END_CALL_MUTATION", "TOGGLE_CALL_MEDIA_MUTATION", "INCOMING_CALL_SUBSCRIPTION", "CallService", "constructor", "apollo", "logger", "activeCall", "incomingCall", "activeCall$", "asObservable", "incomingCall$", "sounds", "isPlaying", "muted", "isVideoEnabled", "isAudioEnabled", "preloadSounds", "initializeSubscriptions", "setTimeout", "subscribeToIncomingCalls", "value", "console", "log", "loadSound", "name", "path", "audio", "Audio", "load", "addEventListener", "error", "play", "loop", "sound", "currentTime", "catch", "stop", "pause", "subscribe", "query", "errorPolicy", "next", "data", "errors", "warn", "callId", "id", "callType", "type", "caller", "username", "conversationId", "handleIncomingCall", "complete", "reinitializeSubscription", "call", "initiateCall", "recipientId", "Error", "Date", "now", "Math", "random", "toString", "substr", "offer", "JSON", "stringify", "sdp", "variables", "mutate", "mutation", "pipe", "result", "status", "recipient", "errorMessage", "networkError", "graphQLErrors", "length", "message", "acceptCall", "answer", "startMediaForCall", "rejectCall", "reason", "endCall", "feedback", "stopAllSounds", "toggleMedia", "enableVideo", "enableAudio", "video", "toggleCallMedia", "_this", "_asyncToGenerator", "toggleVideo", "toggleAudio", "getVideoEnabled", "getAudioEnabled", "Object", "keys", "for<PERSON>ach", "ngOnDestroy", "i0", "ɵɵinject", "i1", "Apollo", "i2", "LoggerService", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\services\\call.service.ts"], "sourcesContent": ["import { Injectable, <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';\nimport { Apollo } from 'apollo-angular';\nimport { BehaviorSubject, Observable, throwError, of, from } from 'rxjs';\nimport { map, catchError, switchMap } from 'rxjs/operators';\nimport {\n  Call,\n  CallType,\n  CallStatus,\n  IncomingCall,\n  CallSuccess,\n} from '../models/message.model';\nimport {\n  INITIATE_CALL_MUTATION,\n  ACCEPT_CALL_MUTATION,\n  REJECT_CALL_MUTATION,\n  END_CALL_MUTATION,\n  TOGGLE_CALL_MEDIA_MUTATION,\n  INCOMING_CALL_SUBSCRIPTION,\n  CALL_STATUS_CHANGED_SUBSCRIPTION,\n} from '../graphql/message.graphql';\nimport { LoggerService } from './logger.service';\n\n@Injectable({\n  providedIn: 'root',\n})\nexport class CallService implements OnDestroy {\n  // État des appels\n  private activeCall = new BehaviorSubject<Call | null>(null);\n  private incomingCall = new BehaviorSubject<IncomingCall | null>(null);\n\n  // Observables publics\n  public activeCall$ = this.activeCall.asObservable();\n  public incomingCall$ = this.incomingCall.asObservable();\n\n  // Propriétés pour la gestion des sons\n  private sounds: { [key: string]: HTMLAudioElement } = {};\n  private isPlaying: { [key: string]: boolean } = {};\n  private muted = false;\n\n  // États simples pour les médias\n  private isVideoEnabled = true;\n  private isAudioEnabled = true;\n\n  constructor(private apollo: Apollo, private logger: LoggerService) {\n    this.preloadSounds();\n    this.initializeSubscriptions();\n  }\n\n  /**\n   * Initialise les subscriptions avec un délai pour s'assurer que l'authentification est prête\n   */\n  private initializeSubscriptions(): void {\n    // Attendre un peu pour s'assurer que l'authentification est prête\n    setTimeout(() => {\n      this.subscribeToIncomingCalls();\n    }, 1000);\n\n    // Réessayer après 5 secondes si la première tentative échoue\n    setTimeout(() => {\n      if (!this.incomingCall.value) {\n        console.log('🔄 [CallService] Retrying subscription initialization...');\n        this.subscribeToIncomingCalls();\n      }\n    }, 5000);\n  }\n\n  /**\n   * Précharge les sons utilisés dans l'application\n   */\n  private preloadSounds(): void {\n    this.loadSound('ringtone', 'assets/sounds/ringtone.mp3');\n    this.loadSound('call-end', 'assets/sounds/call-end.mp3');\n    this.loadSound('call-connected', 'assets/sounds/call-connected.mp3');\n  }\n\n  /**\n   * Charge un fichier audio\n   */\n  private loadSound(name: string, path: string): void {\n    try {\n      const audio = new Audio(path);\n      audio.load();\n      this.sounds[name] = audio;\n      this.isPlaying[name] = false;\n\n      audio.addEventListener('ended', () => {\n        this.isPlaying[name] = false;\n      });\n    } catch (error) {\n      console.error(`Error loading sound ${name}:`, error);\n    }\n  }\n\n  /**\n   * Joue un son\n   */\n  private play(name: string, loop: boolean = false): void {\n    if (this.muted) return;\n\n    try {\n      const sound = this.sounds[name];\n      if (!sound) return;\n\n      sound.loop = loop;\n      if (!this.isPlaying[name]) {\n        sound.currentTime = 0;\n        sound.play().catch(console.error);\n        this.isPlaying[name] = true;\n      }\n    } catch (error) {\n      console.error(`Error playing sound ${name}:`, error);\n    }\n  }\n\n  /**\n   * Arrête un son\n   */\n  private stop(name: string): void {\n    try {\n      const sound = this.sounds[name];\n      if (!sound) return;\n\n      if (this.isPlaying[name]) {\n        sound.pause();\n        sound.currentTime = 0;\n        this.isPlaying[name] = false;\n      }\n    } catch (error) {\n      console.error(`Error stopping sound ${name}:`, error);\n    }\n  }\n\n  /**\n   * S'abonne aux appels entrants\n   */\n  private subscribeToIncomingCalls(): void {\n    console.log('🔔 [CallService] Setting up incoming call subscription...');\n\n    try {\n      this.apollo\n        .subscribe<{ incomingCall: IncomingCall }>({\n          query: INCOMING_CALL_SUBSCRIPTION,\n          errorPolicy: 'all', // Continuer même en cas d'erreur\n        })\n        .subscribe({\n          next: ({ data, errors }) => {\n            if (errors) {\n              console.warn(\n                '⚠️ [CallService] GraphQL errors in subscription:',\n                errors\n              );\n            }\n\n            if (data?.incomingCall) {\n              console.log('📞 [CallService] Incoming call received:', {\n                callId: data.incomingCall.id,\n                callType: data.incomingCall.type,\n                caller: data.incomingCall.caller?.username,\n                conversationId: data.incomingCall.conversationId,\n              });\n              this.handleIncomingCall(data.incomingCall);\n            }\n          },\n          error: (error) => {\n            console.error(\n              '❌ [CallService] Error in incoming call subscription:',\n              error\n            );\n\n            // Réessayer après 5 secondes en cas d'erreur\n            setTimeout(() => {\n              console.log(\n                '🔄 [CallService] Retrying incoming call subscription...'\n              );\n              this.subscribeToIncomingCalls();\n            }, 5000);\n          },\n          complete: () => {\n            console.log(\n              '🔚 [CallService] Incoming call subscription completed'\n            );\n            // Réessayer si la subscription se ferme de manière inattendue\n            setTimeout(() => {\n              console.log(\n                '🔄 [CallService] Restarting subscription after completion...'\n              );\n              this.subscribeToIncomingCalls();\n            }, 2000);\n          },\n        });\n    } catch (error) {\n      console.error('❌ [CallService] Failed to create subscription:', error);\n      // Réessayer après 3 secondes\n      setTimeout(() => {\n        this.subscribeToIncomingCalls();\n      }, 3000);\n    }\n  }\n\n  /**\n   * Méthode publique pour forcer la réinitialisation de la subscription\n   */\n  public reinitializeSubscription(): void {\n    console.log('🔄 [CallService] Manually reinitializing subscription...');\n    this.subscribeToIncomingCalls();\n  }\n\n  /**\n   * Gère un appel entrant\n   */\n  private handleIncomingCall(call: IncomingCall): void {\n    console.log('🔔 [CallService] Handling incoming call:', {\n      callId: call.id,\n      callType: call.type,\n      caller: call.caller?.username,\n      conversationId: call.conversationId,\n    });\n\n    this.incomingCall.next(call);\n    this.play('ringtone', true);\n\n    console.log(\n      '🔊 [CallService] Ringtone started, call notification sent to UI'\n    );\n  }\n\n  /**\n   * Initie un appel WebRTC\n   */\n  initiateCall(\n    recipientId: string,\n    callType: CallType,\n    conversationId?: string\n  ): Observable<Call> {\n    console.log('🔄 [CallService] Initiating call:', {\n      recipientId,\n      callType,\n      conversationId,\n    });\n\n    if (!recipientId) {\n      const error = new Error('Recipient ID is required');\n      console.error('❌ [CallService] initiateCall error:', error);\n      return throwError(() => error);\n    }\n\n    // Générer un ID unique pour l'appel\n    const callId = `call_${Date.now()}_${Math.random()\n      .toString(36)\n      .substr(2, 9)}`;\n\n    // Pour l'instant, utiliser une offre WebRTC factice\n    const offer = JSON.stringify({\n      type: 'offer',\n      sdp: 'v=0\\r\\no=- 0 0 IN IP4 127.0.0.1\\r\\ns=-\\r\\nt=0 0\\r\\n',\n    });\n\n    const variables = {\n      recipientId,\n      callType: callType,\n      callId,\n      offer,\n      conversationId,\n    };\n\n    console.log('📤 [CallService] Sending initiate call mutation:', variables);\n\n    return this.apollo\n      .mutate<{ initiateCall: Call }>({\n        mutation: INITIATE_CALL_MUTATION,\n        variables,\n      })\n      .pipe(\n        map((result) => {\n          console.log('✅ [CallService] Call initiated successfully:', result);\n\n          if (!result.data?.initiateCall) {\n            throw new Error('No call data received from server');\n          }\n\n          const call = result.data.initiateCall;\n          console.log('📞 [CallService] Call details:', {\n            id: call.id,\n            type: call.type,\n            status: call.status,\n            caller: call.caller?.username,\n            recipient: call.recipient?.username,\n          });\n\n          // Mettre à jour l'état local\n          this.activeCall.next(call);\n\n          return call;\n        }),\n        catchError((error) => {\n          console.error('❌ [CallService] initiateCall error:', error);\n          this.logger.error('Error initiating call:', error);\n\n          let errorMessage = \"Erreur lors de l'initiation de l'appel\";\n          if (error.networkError) {\n            errorMessage = 'Erreur de connexion réseau';\n          } else if (error.graphQLErrors?.length > 0) {\n            errorMessage = error.graphQLErrors[0].message || errorMessage;\n          }\n\n          return throwError(() => new Error(errorMessage));\n        })\n      );\n  }\n\n  /**\n   * Accepte un appel entrant\n   */\n  acceptCall(incomingCall: IncomingCall): Observable<Call> {\n    console.log('🔄 [CallService] Accepting call:', incomingCall.id);\n\n    // Générer une réponse WebRTC factice\n    const answer = JSON.stringify({\n      type: 'answer',\n      sdp: 'v=0\\r\\no=- 0 0 IN IP4 127.0.0.1\\r\\ns=-\\r\\nt=0 0\\r\\n',\n    });\n\n    return this.apollo\n      .mutate<{ acceptCall: Call }>({\n        mutation: ACCEPT_CALL_MUTATION,\n        variables: {\n          callId: incomingCall.id,\n          answer,\n        },\n      })\n      .pipe(\n        switchMap((result) => {\n          console.log('✅ [CallService] Call accepted successfully:', result);\n\n          if (!result.data?.acceptCall) {\n            throw new Error('No call data received from server');\n          }\n\n          const call = result.data.acceptCall;\n\n          // Arrêter la sonnerie\n          this.stop('ringtone');\n          this.play('call-connected');\n\n          // Démarrer les médias pour l'appel de manière asynchrone\n          return from(this.startMediaForCall(incomingCall, call));\n        }),\n        catchError((error) => {\n          console.error('❌ [CallService] acceptCall error:', error);\n          this.logger.error('Error accepting call:', error);\n          return throwError(\n            () => new Error(\"Erreur lors de l'acceptation de l'appel\")\n          );\n        })\n      );\n  }\n\n  /**\n   * Rejette un appel entrant\n   */\n  rejectCall(callId: string, reason?: string): Observable<CallSuccess> {\n    console.log('🔄 [CallService] Rejecting call:', callId, reason);\n\n    return this.apollo\n      .mutate<{ rejectCall: CallSuccess }>({\n        mutation: REJECT_CALL_MUTATION,\n        variables: {\n          callId,\n          reason: reason || 'User rejected',\n        },\n      })\n      .pipe(\n        map((result) => {\n          console.log('✅ [CallService] Call rejected successfully:', result);\n\n          if (!result.data?.rejectCall) {\n            throw new Error('No response received from server');\n          }\n\n          // Mettre à jour l'état local\n          this.incomingCall.next(null);\n          this.activeCall.next(null);\n\n          // Arrêter la sonnerie\n          this.stop('ringtone');\n\n          return result.data.rejectCall;\n        }),\n        catchError((error) => {\n          console.error('❌ [CallService] rejectCall error:', error);\n          this.logger.error('Error rejecting call:', error);\n          return throwError(() => new Error(\"Erreur lors du rejet de l'appel\"));\n        })\n      );\n  }\n\n  /**\n   * Termine un appel en cours\n   */\n  endCall(callId: string): Observable<CallSuccess> {\n    console.log('🔄 [CallService] Ending call:', callId);\n\n    return this.apollo\n      .mutate<{ endCall: CallSuccess }>({\n        mutation: END_CALL_MUTATION,\n        variables: {\n          callId,\n          feedback: null, // Pas de feedback pour l'instant\n        },\n      })\n      .pipe(\n        map((result) => {\n          console.log('✅ [CallService] Call ended successfully:', result);\n\n          if (!result.data?.endCall) {\n            throw new Error('No response received from server');\n          }\n\n          // Mettre à jour l'état local\n          this.activeCall.next(null);\n          this.incomingCall.next(null);\n\n          // Arrêter tous les sons\n          this.stopAllSounds();\n          this.play('call-end');\n\n          return result.data.endCall;\n        }),\n        catchError((error) => {\n          console.error('❌ [CallService] endCall error:', error);\n          this.logger.error('Error ending call:', error);\n          return throwError(\n            () => new Error(\"Erreur lors de la fin de l'appel\")\n          );\n        })\n      );\n  }\n\n  /**\n   * Bascule l'état des médias (audio/vidéo) pendant un appel\n   */\n  toggleMedia(\n    callId: string,\n    enableVideo?: boolean,\n    enableAudio?: boolean\n  ): Observable<CallSuccess> {\n    console.log('🔄 [CallService] Toggling media:', {\n      callId,\n      enableVideo,\n      enableAudio,\n    });\n\n    return this.apollo\n      .mutate<{ toggleCallMedia: CallSuccess }>({\n        mutation: TOGGLE_CALL_MEDIA_MUTATION,\n        variables: {\n          callId,\n          video: enableVideo,\n          audio: enableAudio,\n        },\n      })\n      .pipe(\n        map((result) => {\n          console.log('✅ [CallService] Media toggled successfully:', result);\n\n          if (!result.data?.toggleCallMedia) {\n            throw new Error('No response received from server');\n          }\n\n          return result.data.toggleCallMedia;\n        }),\n        catchError((error) => {\n          console.error('❌ [CallService] toggleMedia error:', error);\n          this.logger.error('Error toggling media:', error);\n          return throwError(\n            () => new Error('Erreur lors du changement des médias')\n          );\n        })\n      );\n  }\n\n  /**\n   * Démarre les médias pour un appel accepté\n   */\n  private async startMediaForCall(\n    incomingCall: IncomingCall,\n    call: Call\n  ): Promise<Call> {\n    console.log('🎥 [CallService] Call connected - playing connection sound');\n\n    // Jouer le son de connexion\n    this.play('call-connected');\n\n    // Mettre à jour l'état local\n    this.activeCall.next(call);\n    this.incomingCall.next(null); // Supprimer l'appel entrant\n\n    return call;\n  }\n\n  /**\n   * Active/désactive la vidéo\n   */\n  toggleVideo(): boolean {\n    this.isVideoEnabled = !this.isVideoEnabled;\n    console.log('📹 [CallService] Video toggled:', this.isVideoEnabled);\n    return this.isVideoEnabled;\n  }\n\n  /**\n   * Active/désactive l'audio\n   */\n  toggleAudio(): boolean {\n    this.isAudioEnabled = !this.isAudioEnabled;\n    console.log('🎤 [CallService] Audio toggled:', this.isAudioEnabled);\n    return this.isAudioEnabled;\n  }\n\n  /**\n   * Obtient l'état de la vidéo\n   */\n  getVideoEnabled(): boolean {\n    return this.isVideoEnabled;\n  }\n\n  /**\n   * Obtient l'état de l'audio\n   */\n  getAudioEnabled(): boolean {\n    return this.isAudioEnabled;\n  }\n\n  /**\n   * Arrête tous les sons\n   */\n  private stopAllSounds(): void {\n    Object.keys(this.sounds).forEach((name) => {\n      this.stop(name);\n    });\n  }\n\n  ngOnDestroy(): void {\n    this.stopAllSounds();\n  }\n}\n"], "mappings": ";AAEA,SAASA,eAAe,EAAcC,UAAU,EAAMC,IAAI,QAAQ,MAAM;AACxE,SAASC,GAAG,EAAEC,UAAU,EAAEC,SAAS,QAAQ,gBAAgB;AAQ3D,SACEC,sBAAsB,EACtBC,oBAAoB,EACpBC,oBAAoB,EACpBC,iBAAiB,EACjBC,0BAA0B,EAC1BC,0BAA0B,QAErB,4BAA4B;;;;AAMnC,OAAM,MAAOC,WAAW;EAkBtBC,YAAoBC,MAAc,EAAUC,MAAqB;IAA7C,KAAAD,MAAM,GAANA,MAAM;IAAkB,KAAAC,MAAM,GAANA,MAAM;IAjBlD;IACQ,KAAAC,UAAU,GAAG,IAAIhB,eAAe,CAAc,IAAI,CAAC;IACnD,KAAAiB,YAAY,GAAG,IAAIjB,eAAe,CAAsB,IAAI,CAAC;IAErE;IACO,KAAAkB,WAAW,GAAG,IAAI,CAACF,UAAU,CAACG,YAAY,EAAE;IAC5C,KAAAC,aAAa,GAAG,IAAI,CAACH,YAAY,CAACE,YAAY,EAAE;IAEvD;IACQ,KAAAE,MAAM,GAAwC,EAAE;IAChD,KAAAC,SAAS,GAA+B,EAAE;IAC1C,KAAAC,KAAK,GAAG,KAAK;IAErB;IACQ,KAAAC,cAAc,GAAG,IAAI;IACrB,KAAAC,cAAc,GAAG,IAAI;IAG3B,IAAI,CAACC,aAAa,EAAE;IACpB,IAAI,CAACC,uBAAuB,EAAE;EAChC;EAEA;;;EAGQA,uBAAuBA,CAAA;IAC7B;IACAC,UAAU,CAAC,MAAK;MACd,IAAI,CAACC,wBAAwB,EAAE;IACjC,CAAC,EAAE,IAAI,CAAC;IAER;IACAD,UAAU,CAAC,MAAK;MACd,IAAI,CAAC,IAAI,CAACX,YAAY,CAACa,KAAK,EAAE;QAC5BC,OAAO,CAACC,GAAG,CAAC,0DAA0D,CAAC;QACvE,IAAI,CAACH,wBAAwB,EAAE;;IAEnC,CAAC,EAAE,IAAI,CAAC;EACV;EAEA;;;EAGQH,aAAaA,CAAA;IACnB,IAAI,CAACO,SAAS,CAAC,UAAU,EAAE,4BAA4B,CAAC;IACxD,IAAI,CAACA,SAAS,CAAC,UAAU,EAAE,4BAA4B,CAAC;IACxD,IAAI,CAACA,SAAS,CAAC,gBAAgB,EAAE,kCAAkC,CAAC;EACtE;EAEA;;;EAGQA,SAASA,CAACC,IAAY,EAAEC,IAAY;IAC1C,IAAI;MACF,MAAMC,KAAK,GAAG,IAAIC,KAAK,CAACF,IAAI,CAAC;MAC7BC,KAAK,CAACE,IAAI,EAAE;MACZ,IAAI,CAACjB,MAAM,CAACa,IAAI,CAAC,GAAGE,KAAK;MACzB,IAAI,CAACd,SAAS,CAACY,IAAI,CAAC,GAAG,KAAK;MAE5BE,KAAK,CAACG,gBAAgB,CAAC,OAAO,EAAE,MAAK;QACnC,IAAI,CAACjB,SAAS,CAACY,IAAI,CAAC,GAAG,KAAK;MAC9B,CAAC,CAAC;KACH,CAAC,OAAOM,KAAK,EAAE;MACdT,OAAO,CAACS,KAAK,CAAC,uBAAuBN,IAAI,GAAG,EAAEM,KAAK,CAAC;;EAExD;EAEA;;;EAGQC,IAAIA,CAACP,IAAY,EAAEQ,IAAA,GAAgB,KAAK;IAC9C,IAAI,IAAI,CAACnB,KAAK,EAAE;IAEhB,IAAI;MACF,MAAMoB,KAAK,GAAG,IAAI,CAACtB,MAAM,CAACa,IAAI,CAAC;MAC/B,IAAI,CAACS,KAAK,EAAE;MAEZA,KAAK,CAACD,IAAI,GAAGA,IAAI;MACjB,IAAI,CAAC,IAAI,CAACpB,SAAS,CAACY,IAAI,CAAC,EAAE;QACzBS,KAAK,CAACC,WAAW,GAAG,CAAC;QACrBD,KAAK,CAACF,IAAI,EAAE,CAACI,KAAK,CAACd,OAAO,CAACS,KAAK,CAAC;QACjC,IAAI,CAAClB,SAAS,CAACY,IAAI,CAAC,GAAG,IAAI;;KAE9B,CAAC,OAAOM,KAAK,EAAE;MACdT,OAAO,CAACS,KAAK,CAAC,uBAAuBN,IAAI,GAAG,EAAEM,KAAK,CAAC;;EAExD;EAEA;;;EAGQM,IAAIA,CAACZ,IAAY;IACvB,IAAI;MACF,MAAMS,KAAK,GAAG,IAAI,CAACtB,MAAM,CAACa,IAAI,CAAC;MAC/B,IAAI,CAACS,KAAK,EAAE;MAEZ,IAAI,IAAI,CAACrB,SAAS,CAACY,IAAI,CAAC,EAAE;QACxBS,KAAK,CAACI,KAAK,EAAE;QACbJ,KAAK,CAACC,WAAW,GAAG,CAAC;QACrB,IAAI,CAACtB,SAAS,CAACY,IAAI,CAAC,GAAG,KAAK;;KAE/B,CAAC,OAAOM,KAAK,EAAE;MACdT,OAAO,CAACS,KAAK,CAAC,wBAAwBN,IAAI,GAAG,EAAEM,KAAK,CAAC;;EAEzD;EAEA;;;EAGQX,wBAAwBA,CAAA;IAC9BE,OAAO,CAACC,GAAG,CAAC,2DAA2D,CAAC;IAExE,IAAI;MACF,IAAI,CAAClB,MAAM,CACRkC,SAAS,CAAiC;QACzCC,KAAK,EAAEtC,0BAA0B;QACjCuC,WAAW,EAAE,KAAK,CAAE;OACrB,CAAC,CACDF,SAAS,CAAC;QACTG,IAAI,EAAEA,CAAC;UAAEC,IAAI;UAAEC;QAAM,CAAE,KAAI;UACzB,IAAIA,MAAM,EAAE;YACVtB,OAAO,CAACuB,IAAI,CACV,kDAAkD,EAClDD,MAAM,CACP;;UAGH,IAAID,IAAI,EAAEnC,YAAY,EAAE;YACtBc,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAE;cACtDuB,MAAM,EAAEH,IAAI,CAACnC,YAAY,CAACuC,EAAE;cAC5BC,QAAQ,EAAEL,IAAI,CAACnC,YAAY,CAACyC,IAAI;cAChCC,MAAM,EAAEP,IAAI,CAACnC,YAAY,CAAC0C,MAAM,EAAEC,QAAQ;cAC1CC,cAAc,EAAET,IAAI,CAACnC,YAAY,CAAC4C;aACnC,CAAC;YACF,IAAI,CAACC,kBAAkB,CAACV,IAAI,CAACnC,YAAY,CAAC;;QAE9C,CAAC;QACDuB,KAAK,EAAGA,KAAK,IAAI;UACfT,OAAO,CAACS,KAAK,CACX,sDAAsD,EACtDA,KAAK,CACN;UAED;UACAZ,UAAU,CAAC,MAAK;YACdG,OAAO,CAACC,GAAG,CACT,yDAAyD,CAC1D;YACD,IAAI,CAACH,wBAAwB,EAAE;UACjC,CAAC,EAAE,IAAI,CAAC;QACV,CAAC;QACDkC,QAAQ,EAAEA,CAAA,KAAK;UACbhC,OAAO,CAACC,GAAG,CACT,uDAAuD,CACxD;UACD;UACAJ,UAAU,CAAC,MAAK;YACdG,OAAO,CAACC,GAAG,CACT,8DAA8D,CAC/D;YACD,IAAI,CAACH,wBAAwB,EAAE;UACjC,CAAC,EAAE,IAAI,CAAC;QACV;OACD,CAAC;KACL,CAAC,OAAOW,KAAK,EAAE;MACdT,OAAO,CAACS,KAAK,CAAC,gDAAgD,EAAEA,KAAK,CAAC;MACtE;MACAZ,UAAU,CAAC,MAAK;QACd,IAAI,CAACC,wBAAwB,EAAE;MACjC,CAAC,EAAE,IAAI,CAAC;;EAEZ;EAEA;;;EAGOmC,wBAAwBA,CAAA;IAC7BjC,OAAO,CAACC,GAAG,CAAC,0DAA0D,CAAC;IACvE,IAAI,CAACH,wBAAwB,EAAE;EACjC;EAEA;;;EAGQiC,kBAAkBA,CAACG,IAAkB;IAC3ClC,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAE;MACtDuB,MAAM,EAAEU,IAAI,CAACT,EAAE;MACfC,QAAQ,EAAEQ,IAAI,CAACP,IAAI;MACnBC,MAAM,EAAEM,IAAI,CAACN,MAAM,EAAEC,QAAQ;MAC7BC,cAAc,EAAEI,IAAI,CAACJ;KACtB,CAAC;IAEF,IAAI,CAAC5C,YAAY,CAACkC,IAAI,CAACc,IAAI,CAAC;IAC5B,IAAI,CAACxB,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC;IAE3BV,OAAO,CAACC,GAAG,CACT,iEAAiE,CAClE;EACH;EAEA;;;EAGAkC,YAAYA,CACVC,WAAmB,EACnBV,QAAkB,EAClBI,cAAuB;IAEvB9B,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAE;MAC/CmC,WAAW;MACXV,QAAQ;MACRI;KACD,CAAC;IAEF,IAAI,CAACM,WAAW,EAAE;MAChB,MAAM3B,KAAK,GAAG,IAAI4B,KAAK,CAAC,0BAA0B,CAAC;MACnDrC,OAAO,CAACS,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3D,OAAOvC,UAAU,CAAC,MAAMuC,KAAK,CAAC;;IAGhC;IACA,MAAMe,MAAM,GAAG,QAAQc,IAAI,CAACC,GAAG,EAAE,IAAIC,IAAI,CAACC,MAAM,EAAE,CAC/CC,QAAQ,CAAC,EAAE,CAAC,CACZC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;IAEjB;IACA,MAAMC,KAAK,GAAGC,IAAI,CAACC,SAAS,CAAC;MAC3BnB,IAAI,EAAE,OAAO;MACboB,GAAG,EAAE;KACN,CAAC;IAEF,MAAMC,SAAS,GAAG;MAChBZ,WAAW;MACXV,QAAQ,EAAEA,QAAQ;MAClBF,MAAM;MACNoB,KAAK;MACLd;KACD;IAED9B,OAAO,CAACC,GAAG,CAAC,kDAAkD,EAAE+C,SAAS,CAAC;IAE1E,OAAO,IAAI,CAACjE,MAAM,CACfkE,MAAM,CAAyB;MAC9BC,QAAQ,EAAE3E,sBAAsB;MAChCyE;KACD,CAAC,CACDG,IAAI,CACH/E,GAAG,CAAEgF,MAAM,IAAI;MACbpD,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAEmD,MAAM,CAAC;MAEnE,IAAI,CAACA,MAAM,CAAC/B,IAAI,EAAEc,YAAY,EAAE;QAC9B,MAAM,IAAIE,KAAK,CAAC,mCAAmC,CAAC;;MAGtD,MAAMH,IAAI,GAAGkB,MAAM,CAAC/B,IAAI,CAACc,YAAY;MACrCnC,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE;QAC5CwB,EAAE,EAAES,IAAI,CAACT,EAAE;QACXE,IAAI,EAAEO,IAAI,CAACP,IAAI;QACf0B,MAAM,EAAEnB,IAAI,CAACmB,MAAM;QACnBzB,MAAM,EAAEM,IAAI,CAACN,MAAM,EAAEC,QAAQ;QAC7ByB,SAAS,EAAEpB,IAAI,CAACoB,SAAS,EAAEzB;OAC5B,CAAC;MAEF;MACA,IAAI,CAAC5C,UAAU,CAACmC,IAAI,CAACc,IAAI,CAAC;MAE1B,OAAOA,IAAI;IACb,CAAC,CAAC,EACF7D,UAAU,CAAEoC,KAAK,IAAI;MACnBT,OAAO,CAACS,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3D,IAAI,CAACzB,MAAM,CAACyB,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAElD,IAAI8C,YAAY,GAAG,wCAAwC;MAC3D,IAAI9C,KAAK,CAAC+C,YAAY,EAAE;QACtBD,YAAY,GAAG,4BAA4B;OAC5C,MAAM,IAAI9C,KAAK,CAACgD,aAAa,EAAEC,MAAM,GAAG,CAAC,EAAE;QAC1CH,YAAY,GAAG9C,KAAK,CAACgD,aAAa,CAAC,CAAC,CAAC,CAACE,OAAO,IAAIJ,YAAY;;MAG/D,OAAOrF,UAAU,CAAC,MAAM,IAAImE,KAAK,CAACkB,YAAY,CAAC,CAAC;IAClD,CAAC,CAAC,CACH;EACL;EAEA;;;EAGAK,UAAUA,CAAC1E,YAA0B;IACnCc,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEf,YAAY,CAACuC,EAAE,CAAC;IAEhE;IACA,MAAMoC,MAAM,GAAGhB,IAAI,CAACC,SAAS,CAAC;MAC5BnB,IAAI,EAAE,QAAQ;MACdoB,GAAG,EAAE;KACN,CAAC;IAEF,OAAO,IAAI,CAAChE,MAAM,CACfkE,MAAM,CAAuB;MAC5BC,QAAQ,EAAE1E,oBAAoB;MAC9BwE,SAAS,EAAE;QACTxB,MAAM,EAAEtC,YAAY,CAACuC,EAAE;QACvBoC;;KAEH,CAAC,CACDV,IAAI,CACH7E,SAAS,CAAE8E,MAAM,IAAI;MACnBpD,OAAO,CAACC,GAAG,CAAC,6CAA6C,EAAEmD,MAAM,CAAC;MAElE,IAAI,CAACA,MAAM,CAAC/B,IAAI,EAAEuC,UAAU,EAAE;QAC5B,MAAM,IAAIvB,KAAK,CAAC,mCAAmC,CAAC;;MAGtD,MAAMH,IAAI,GAAGkB,MAAM,CAAC/B,IAAI,CAACuC,UAAU;MAEnC;MACA,IAAI,CAAC7C,IAAI,CAAC,UAAU,CAAC;MACrB,IAAI,CAACL,IAAI,CAAC,gBAAgB,CAAC;MAE3B;MACA,OAAOvC,IAAI,CAAC,IAAI,CAAC2F,iBAAiB,CAAC5E,YAAY,EAAEgD,IAAI,CAAC,CAAC;IACzD,CAAC,CAAC,EACF7D,UAAU,CAAEoC,KAAK,IAAI;MACnBT,OAAO,CAACS,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzD,IAAI,CAACzB,MAAM,CAACyB,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MACjD,OAAOvC,UAAU,CACf,MAAM,IAAImE,KAAK,CAAC,yCAAyC,CAAC,CAC3D;IACH,CAAC,CAAC,CACH;EACL;EAEA;;;EAGA0B,UAAUA,CAACvC,MAAc,EAAEwC,MAAe;IACxChE,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEuB,MAAM,EAAEwC,MAAM,CAAC;IAE/D,OAAO,IAAI,CAACjF,MAAM,CACfkE,MAAM,CAA8B;MACnCC,QAAQ,EAAEzE,oBAAoB;MAC9BuE,SAAS,EAAE;QACTxB,MAAM;QACNwC,MAAM,EAAEA,MAAM,IAAI;;KAErB,CAAC,CACDb,IAAI,CACH/E,GAAG,CAAEgF,MAAM,IAAI;MACbpD,OAAO,CAACC,GAAG,CAAC,6CAA6C,EAAEmD,MAAM,CAAC;MAElE,IAAI,CAACA,MAAM,CAAC/B,IAAI,EAAE0C,UAAU,EAAE;QAC5B,MAAM,IAAI1B,KAAK,CAAC,kCAAkC,CAAC;;MAGrD;MACA,IAAI,CAACnD,YAAY,CAACkC,IAAI,CAAC,IAAI,CAAC;MAC5B,IAAI,CAACnC,UAAU,CAACmC,IAAI,CAAC,IAAI,CAAC;MAE1B;MACA,IAAI,CAACL,IAAI,CAAC,UAAU,CAAC;MAErB,OAAOqC,MAAM,CAAC/B,IAAI,CAAC0C,UAAU;IAC/B,CAAC,CAAC,EACF1F,UAAU,CAAEoC,KAAK,IAAI;MACnBT,OAAO,CAACS,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzD,IAAI,CAACzB,MAAM,CAACyB,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MACjD,OAAOvC,UAAU,CAAC,MAAM,IAAImE,KAAK,CAAC,iCAAiC,CAAC,CAAC;IACvE,CAAC,CAAC,CACH;EACL;EAEA;;;EAGA4B,OAAOA,CAACzC,MAAc;IACpBxB,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEuB,MAAM,CAAC;IAEpD,OAAO,IAAI,CAACzC,MAAM,CACfkE,MAAM,CAA2B;MAChCC,QAAQ,EAAExE,iBAAiB;MAC3BsE,SAAS,EAAE;QACTxB,MAAM;QACN0C,QAAQ,EAAE,IAAI,CAAE;;KAEnB,CAAC,CACDf,IAAI,CACH/E,GAAG,CAAEgF,MAAM,IAAI;MACbpD,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAEmD,MAAM,CAAC;MAE/D,IAAI,CAACA,MAAM,CAAC/B,IAAI,EAAE4C,OAAO,EAAE;QACzB,MAAM,IAAI5B,KAAK,CAAC,kCAAkC,CAAC;;MAGrD;MACA,IAAI,CAACpD,UAAU,CAACmC,IAAI,CAAC,IAAI,CAAC;MAC1B,IAAI,CAAClC,YAAY,CAACkC,IAAI,CAAC,IAAI,CAAC;MAE5B;MACA,IAAI,CAAC+C,aAAa,EAAE;MACpB,IAAI,CAACzD,IAAI,CAAC,UAAU,CAAC;MAErB,OAAO0C,MAAM,CAAC/B,IAAI,CAAC4C,OAAO;IAC5B,CAAC,CAAC,EACF5F,UAAU,CAAEoC,KAAK,IAAI;MACnBT,OAAO,CAACS,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD,IAAI,CAACzB,MAAM,CAACyB,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC9C,OAAOvC,UAAU,CACf,MAAM,IAAImE,KAAK,CAAC,kCAAkC,CAAC,CACpD;IACH,CAAC,CAAC,CACH;EACL;EAEA;;;EAGA+B,WAAWA,CACT5C,MAAc,EACd6C,WAAqB,EACrBC,WAAqB;IAErBtE,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE;MAC9CuB,MAAM;MACN6C,WAAW;MACXC;KACD,CAAC;IAEF,OAAO,IAAI,CAACvF,MAAM,CACfkE,MAAM,CAAmC;MACxCC,QAAQ,EAAEvE,0BAA0B;MACpCqE,SAAS,EAAE;QACTxB,MAAM;QACN+C,KAAK,EAAEF,WAAW;QAClBhE,KAAK,EAAEiE;;KAEV,CAAC,CACDnB,IAAI,CACH/E,GAAG,CAAEgF,MAAM,IAAI;MACbpD,OAAO,CAACC,GAAG,CAAC,6CAA6C,EAAEmD,MAAM,CAAC;MAElE,IAAI,CAACA,MAAM,CAAC/B,IAAI,EAAEmD,eAAe,EAAE;QACjC,MAAM,IAAInC,KAAK,CAAC,kCAAkC,CAAC;;MAGrD,OAAOe,MAAM,CAAC/B,IAAI,CAACmD,eAAe;IACpC,CAAC,CAAC,EACFnG,UAAU,CAAEoC,KAAK,IAAI;MACnBT,OAAO,CAACS,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;MAC1D,IAAI,CAACzB,MAAM,CAACyB,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MACjD,OAAOvC,UAAU,CACf,MAAM,IAAImE,KAAK,CAAC,sCAAsC,CAAC,CACxD;IACH,CAAC,CAAC,CACH;EACL;EAEA;;;EAGcyB,iBAAiBA,CAC7B5E,YAA0B,EAC1BgD,IAAU;IAAA,IAAAuC,KAAA;IAAA,OAAAC,iBAAA;MAEV1E,OAAO,CAACC,GAAG,CAAC,4DAA4D,CAAC;MAEzE;MACAwE,KAAI,CAAC/D,IAAI,CAAC,gBAAgB,CAAC;MAE3B;MACA+D,KAAI,CAACxF,UAAU,CAACmC,IAAI,CAACc,IAAI,CAAC;MAC1BuC,KAAI,CAACvF,YAAY,CAACkC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;MAE9B,OAAOc,IAAI;IAAC;EACd;EAEA;;;EAGAyC,WAAWA,CAAA;IACT,IAAI,CAAClF,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;IAC1CO,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE,IAAI,CAACR,cAAc,CAAC;IACnE,OAAO,IAAI,CAACA,cAAc;EAC5B;EAEA;;;EAGAmF,WAAWA,CAAA;IACT,IAAI,CAAClF,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;IAC1CM,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE,IAAI,CAACP,cAAc,CAAC;IACnE,OAAO,IAAI,CAACA,cAAc;EAC5B;EAEA;;;EAGAmF,eAAeA,CAAA;IACb,OAAO,IAAI,CAACpF,cAAc;EAC5B;EAEA;;;EAGAqF,eAAeA,CAAA;IACb,OAAO,IAAI,CAACpF,cAAc;EAC5B;EAEA;;;EAGQyE,aAAaA,CAAA;IACnBY,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC1F,MAAM,CAAC,CAAC2F,OAAO,CAAE9E,IAAI,IAAI;MACxC,IAAI,CAACY,IAAI,CAACZ,IAAI,CAAC;IACjB,CAAC,CAAC;EACJ;EAEA+E,WAAWA,CAAA;IACT,IAAI,CAACf,aAAa,EAAE;EACtB;;;uBAtgBWtF,WAAW,EAAAsG,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,aAAA;IAAA;EAAA;;;aAAX3G,WAAW;MAAA4G,OAAA,EAAX5G,WAAW,CAAA6G,IAAA;MAAAC,UAAA,EAFV;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}