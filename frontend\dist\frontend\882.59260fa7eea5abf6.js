"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[882],{5007:(Q,M,_)=>{_.d(M,{E:()=>r});var h=_(467);const D=["user","model","function","system"];var f=function(t){return t.FINISH_REASON_UNSPECIFIED="FINISH_REASON_UNSPECIFIED",t.STOP="STOP",t.MAX_TOKENS="MAX_TOKENS",t.SAFETY="SAFETY",t.RECITATION="RECITATION",t.LANGUAGE="LANGUAGE",t.BLOCKLIST="BLOCKLIST",t.PROHIBITED_CONTENT="PROHIBITED_CONTENT",t.SPII="SPII",t.MALFORMED_FUNCTION_CALL="MALFORMED_FUNCTION_CALL",t.OTHER="OTHER",t}(f||{});class T extends Error{constructor(o){super(`[GoogleGenerativeAI Error]: ${o}`)}}class H extends T{constructor(o,s){super(o),this.response=s}}class tt extends T{constructor(o,s,a,l){super(o),this.status=s,this.statusText=a,this.errorDetails=l}}class B extends T{}class Et extends T{}const V="0.24.1",yt="genai-js";var U=function(t){return t.GENERATE_CONTENT="generateContent",t.STREAM_GENERATE_CONTENT="streamGenerateContent",t.COUNT_TOKENS="countTokens",t.EMBED_CONTENT="embedContent",t.BATCH_EMBED_CONTENTS="batchEmbedContents",t}(U||{});class Ct{constructor(o,s,a,l,d){this.model=o,this.task=s,this.apiKey=a,this.stream=l,this.requestOptions=d}toString(){var o,s;const a=(null===(o=this.requestOptions)||void 0===o?void 0:o.apiVersion)||"v1beta";let d=`${(null===(s=this.requestOptions)||void 0===s?void 0:s.baseUrl)||"https://generativelanguage.googleapis.com"}/${a}/${this.model}:${this.task}`;return this.stream&&(d+="?alt=sse"),d}}function dt(t){return X.apply(this,arguments)}function X(){return(X=(0,h.A)(function*(t){var o;const s=new Headers;s.append("Content-Type","application/json"),s.append("x-goog-api-client",function $t(t){const o=[];return t?.apiClient&&o.push(t.apiClient),o.push(`${yt}/${V}`),o.join(" ")}(t.requestOptions)),s.append("x-goog-api-key",t.apiKey);let a=null===(o=t.requestOptions)||void 0===o?void 0:o.customHeaders;if(a){if(!(a instanceof Headers))try{a=new Headers(a)}catch(l){throw new B(`unable to convert customHeaders value ${JSON.stringify(a)} to Headers: ${l.message}`)}for(const[l,d]of a.entries()){if("x-goog-api-key"===l)throw new B(`Cannot set reserved header name ${l}`);if("x-goog-api-client"===l)throw new B(`Header name ${l} can only be set using the apiClient field`);s.append(l,d)}}return s})).apply(this,arguments)}function j(){return(j=(0,h.A)(function*(t,o,s,a,l,d){const u=new Ct(t,o,s,a,d);return{url:u.toString(),fetchOptions:Object.assign(Object.assign({},St(d)),{method:"POST",headers:yield dt(u),body:l})}})).apply(this,arguments)}function z(t,o,s,a,l){return et.apply(this,arguments)}function et(){return et=(0,h.A)(function*(t,o,s,a,l,d={},u=fetch){const{url:g,fetchOptions:v}=yield function ht(t,o,s,a,l,d){return j.apply(this,arguments)}(t,o,s,a,l,d);return function bt(t,o){return it.apply(this,arguments)}(g,v,u)}),et.apply(this,arguments)}function it(){return it=(0,h.A)(function*(t,o,s=fetch){let a;try{a=yield s(t,o)}catch(l){!function Bt(t,o){let s=t;throw"AbortError"===s.name?(s=new Et(`Request aborted when fetching ${o.toString()}: ${t.message}`),s.stack=t.stack):t instanceof tt||t instanceof B||(s=new T(`Error fetching from ${o.toString()}: ${t.message}`),s.stack=t.stack),s}(l,t)}return a.ok||(yield function Rt(t,o){return st.apply(this,arguments)}(a,t)),a}),it.apply(this,arguments)}function st(){return(st=(0,h.A)(function*(t,o){let a,s="";try{const l=yield t.json();s=l.error.message,l.error.details&&(s+=` ${JSON.stringify(l.error.details)}`,a=l.error.details)}catch{}throw new tt(`Error fetching from ${o.toString()}: [${t.status} ${t.statusText}] ${s}`,t.status,t.statusText,a)})).apply(this,arguments)}function St(t){const o={};if(void 0!==t?.signal||t?.timeout>=0){const s=new AbortController;t?.timeout>=0&&setTimeout(()=>s.abort(),t.timeout),t?.signal&&t.signal.addEventListener("abort",()=>{s.abort()}),o.signal=s.signal}return o}function Y(t){return t.text=()=>{if(t.candidates&&t.candidates.length>0){if(t.candidates.length>1&&console.warn(`This response had ${t.candidates.length} candidates. Returning text from the first candidate only. Access response.candidates directly to use the other candidates.`),W(t.candidates[0]))throw new H(`${k(t)}`,t);return function Ft(t){var o,s,a,l;const d=[];if(null!==(s=null===(o=t.candidates)||void 0===o?void 0:o[0].content)&&void 0!==s&&s.parts)for(const u of null===(l=null===(a=t.candidates)||void 0===a?void 0:a[0].content)||void 0===l?void 0:l.parts)u.text&&d.push(u.text),u.executableCode&&d.push("\n```"+u.executableCode.language+"\n"+u.executableCode.code+"\n```\n"),u.codeExecutionResult&&d.push("\n```\n"+u.codeExecutionResult.output+"\n```\n");return d.length>0?d.join(""):""}(t)}if(t.promptFeedback)throw new H(`Text not available. ${k(t)}`,t);return""},t.functionCall=()=>{if(t.candidates&&t.candidates.length>0){if(t.candidates.length>1&&console.warn(`This response had ${t.candidates.length} candidates. Returning function calls from the first candidate only. Access response.candidates directly to use the other candidates.`),W(t.candidates[0]))throw new H(`${k(t)}`,t);return console.warn("response.functionCall() is deprecated. Use response.functionCalls() instead."),nt(t)[0]}if(t.promptFeedback)throw new H(`Function call not available. ${k(t)}`,t)},t.functionCalls=()=>{if(t.candidates&&t.candidates.length>0){if(t.candidates.length>1&&console.warn(`This response had ${t.candidates.length} candidates. Returning function calls from the first candidate only. Access response.candidates directly to use the other candidates.`),W(t.candidates[0]))throw new H(`${k(t)}`,t);return nt(t)}if(t.promptFeedback)throw new H(`Function call not available. ${k(t)}`,t)},t}function nt(t){var o,s,a,l;const d=[];if(null!==(s=null===(o=t.candidates)||void 0===o?void 0:o[0].content)&&void 0!==s&&s.parts)for(const u of null===(l=null===(a=t.candidates)||void 0===a?void 0:a[0].content)||void 0===l?void 0:l.parts)u.functionCall&&d.push(u.functionCall);if(d.length>0)return d}const Kt=[f.RECITATION,f.SAFETY,f.LANGUAGE];function W(t){return!!t.finishReason&&Kt.includes(t.finishReason)}function k(t){var o,s,a;let l="";if(t.candidates&&0!==t.candidates.length||!t.promptFeedback){if(null!==(a=t.candidates)&&void 0!==a&&a[0]){const d=t.candidates[0];W(d)&&(l+=`Candidate was blocked due to ${d.finishReason}`,d.finishMessage&&(l+=`: ${d.finishMessage}`))}}else l+="Response was blocked",!(null===(o=t.promptFeedback)||void 0===o)&&o.blockReason&&(l+=` due to ${t.promptFeedback.blockReason}`),null!==(s=t.promptFeedback)&&void 0!==s&&s.blockReasonMessage&&(l+=`: ${t.promptFeedback.blockReasonMessage}`);return l}function $(t){return this instanceof $?(this.v=t,this):new $(t)}"function"==typeof SuppressedError&&SuppressedError;const ut=/^data\: (.*)(?:\n\n|\r\r|\r\n\r\n)/;function Gt(t){return wt.apply(this,arguments)}function wt(){return(wt=(0,h.A)(function*(t){const o=[],s=t.getReader();for(;;){const{done:a,value:l}=yield s.read();if(a)return Y(Tt(o));o.push(l)}})).apply(this,arguments)}function Z(t){return function Pt(t,o,s){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var l,a=s.apply(t,o||[]),d=[];return l={},u("next"),u("throw"),u("return"),l[Symbol.asyncIterator]=function(){return this},l;function u(w){a[w]&&(l[w]=function(L){return new Promise(function(N,A){d.push([w,L,N,A])>1||g(w,L)})})}function g(w,L){try{!function v(w){w.value instanceof $?Promise.resolve(w.value.v).then(y,O):S(d[0][2],w)}(a[w](L))}catch(N){S(d[0][3],N)}}function y(w){g("next",w)}function O(w){g("throw",w)}function S(w,L){w(L),d.shift(),d.length&&g(d[0][0],d[0][1])}}(this,arguments,function*(){const s=t.getReader();for(;;){const{value:a,done:l}=yield $(s.read());if(l)break;yield yield $(Y(a))}})}function Tt(t){const o=t[t.length-1],s={promptFeedback:o?.promptFeedback};for(const a of t){if(a.candidates){let l=0;for(const d of a.candidates)if(s.candidates||(s.candidates=[]),s.candidates[l]||(s.candidates[l]={index:l}),s.candidates[l].citationMetadata=d.citationMetadata,s.candidates[l].groundingMetadata=d.groundingMetadata,s.candidates[l].finishReason=d.finishReason,s.candidates[l].finishMessage=d.finishMessage,s.candidates[l].safetyRatings=d.safetyRatings,d.content&&d.content.parts){s.candidates[l].content||(s.candidates[l].content={role:d.content.role||"user",parts:[]});const u={};for(const g of d.content.parts)g.text&&(u.text=g.text),g.functionCall&&(u.functionCall=g.functionCall),g.executableCode&&(u.executableCode=g.executableCode),g.codeExecutionResult&&(u.codeExecutionResult=g.codeExecutionResult),0===Object.keys(u).length&&(u.text=""),s.candidates[l].content.parts.push(u)}l++}a.usageMetadata&&(s.usageMetadata=a.usageMetadata)}return s}function pt(t,o,s,a){return gt.apply(this,arguments)}function gt(){return(gt=(0,h.A)(function*(t,o,s,a){return function It(t){const s=function Ht(t){const o=t.getReader();return new ReadableStream({start(a){let l="";return function d(){return o.read().then(({value:u,done:g})=>{if(g)return l.trim()?void a.error(new T("Failed to parse stream")):void a.close();l+=u;let y,v=l.match(ut);for(;v;){try{y=JSON.parse(v[1])}catch{return void a.error(new T(`Error parsing JSON response: "${v[1]}"`))}a.enqueue(y),l=l.substring(v[0].length),v=l.match(ut)}return d()}).catch(u=>{let g=u;throw g.stack=u.stack,g="AbortError"===g.name?new Et("Request aborted when reading from the stream"):new T("Error reading from the stream"),g})}()}})}(t.body.pipeThrough(new TextDecoderStream("utf8",{fatal:!0}))),[a,l]=s.tee();return{stream:Z(a),response:Gt(l)}}(yield z(o,U.STREAM_GENERATE_CONTENT,t,!0,JSON.stringify(s),a))})).apply(this,arguments)}function _t(t,o,s,a){return ot.apply(this,arguments)}function ot(){return(ot=(0,h.A)(function*(t,o,s,a){return{response:Y(yield(yield z(o,U.GENERATE_CONTENT,t,!1,JSON.stringify(s),a)).json())}})).apply(this,arguments)}function Ot(t){if(null!=t){if("string"==typeof t)return{role:"system",parts:[{text:t}]};if(t.text)return{role:"system",parts:[t]};if(t.parts)return t.role?t:{role:"system",parts:t.parts}}}function F(t){let o=[];if("string"==typeof t)o=[{text:t}];else for(const s of t)o.push("string"==typeof s?{text:s}:s);return function jt(t){const o={role:"user",parts:[]},s={role:"function",parts:[]};let a=!1,l=!1;for(const d of t)"functionResponse"in d?(s.parts.push(d),l=!0):(o.parts.push(d),a=!0);if(a&&l)throw new T("Within a single message, FunctionResponse cannot be mixed with other type of part in the request for sending chat message.");if(!a&&!l)throw new T("No content is provided for sending chat message.");return a?o:s}(o)}function rt(t){let o;return o=t.contents?t:{contents:[F(t)]},t.systemInstruction&&(o.systemInstruction=Ot(t.systemInstruction)),o}const Vt=["text","inlineData","functionCall","functionResponse","executableCode","codeExecutionResult"],mt={user:["text","inlineData"],function:["functionResponse"],model:["text","functionCall","executableCode","codeExecutionResult"],system:["text"]};function vt(t){var o;if(void 0===t.candidates||0===t.candidates.length)return!1;const s=null===(o=t.candidates[0])||void 0===o?void 0:o.content;if(void 0===s||void 0===s.parts||0===s.parts.length)return!1;for(const a of s.parts)if(void 0===a||0===Object.keys(a).length||void 0!==a.text&&""===a.text)return!1;return!0}const At="SILENT_ERROR";class Mt{constructor(o,s,a,l={}){this.model=s,this.params=a,this._requestOptions=l,this._history=[],this._sendPromise=Promise.resolve(),this._apiKey=o,a?.history&&(function xt(t){let o=!1;for(const s of t){const{role:a,parts:l}=s;if(!o&&"user"!==a)throw new T(`First content should be with role 'user', got ${a}`);if(!D.includes(a))throw new T(`Each item should include role field. Got ${a} but valid roles are: ${JSON.stringify(D)}`);if(!Array.isArray(l))throw new T("Content should have 'parts' property with an array of Parts");if(0===l.length)throw new T("Each Content should have at least one part");const d={text:0,inlineData:0,functionCall:0,functionResponse:0,fileData:0,executableCode:0,codeExecutionResult:0};for(const g of l)for(const v of Vt)v in g&&(d[v]+=1);const u=mt[a];for(const g of Vt)if(!u.includes(g)&&d[g]>0)throw new T(`Content with role '${a}' can't contain '${g}' part`);o=!0}}(a.history),this._history=a.history)}getHistory(){var o=this;return(0,h.A)(function*(){return yield o._sendPromise,o._history})()}sendMessage(o){var s=this;return(0,h.A)(function*(a,l={}){var d,u,g,v,y,O;yield s._sendPromise;const S=F(a),w={safetySettings:null===(d=s.params)||void 0===d?void 0:d.safetySettings,generationConfig:null===(u=s.params)||void 0===u?void 0:u.generationConfig,tools:null===(g=s.params)||void 0===g?void 0:g.tools,toolConfig:null===(v=s.params)||void 0===v?void 0:v.toolConfig,systemInstruction:null===(y=s.params)||void 0===y?void 0:y.systemInstruction,cachedContent:null===(O=s.params)||void 0===O?void 0:O.cachedContent,contents:[...s._history,S]},L=Object.assign(Object.assign({},s._requestOptions),l);let N;return s._sendPromise=s._sendPromise.then(()=>_t(s._apiKey,s.model,w,L)).then(A=>{var K;if(vt(A.response)){s._history.push(S);const kt=Object.assign({parts:[],role:"model"},null===(K=A.response.candidates)||void 0===K?void 0:K[0].content);s._history.push(kt)}else{const kt=k(A.response);kt&&console.warn(`sendMessage() was unsuccessful. ${kt}. Inspect response object for details.`)}N=A}).catch(A=>{throw s._sendPromise=Promise.resolve(),A}),yield s._sendPromise,N}).apply(this,arguments)}sendMessageStream(o){var s=this;return(0,h.A)(function*(a,l={}){var d,u,g,v,y,O;yield s._sendPromise;const S=F(a),w={safetySettings:null===(d=s.params)||void 0===d?void 0:d.safetySettings,generationConfig:null===(u=s.params)||void 0===u?void 0:u.generationConfig,tools:null===(g=s.params)||void 0===g?void 0:g.tools,toolConfig:null===(v=s.params)||void 0===v?void 0:v.toolConfig,systemInstruction:null===(y=s.params)||void 0===y?void 0:y.systemInstruction,cachedContent:null===(O=s.params)||void 0===O?void 0:O.cachedContent,contents:[...s._history,S]},L=Object.assign(Object.assign({},s._requestOptions),l),N=pt(s._apiKey,s.model,w,L);return s._sendPromise=s._sendPromise.then(()=>N).catch(A=>{throw new Error(At)}).then(A=>A.response).then(A=>{if(vt(A)){s._history.push(S);const K=Object.assign({},A.candidates[0].content);K.role||(K.role="model"),s._history.push(K)}else{const K=k(A);K&&console.warn(`sendMessageStream() was unsuccessful. ${K}. Inspect response object for details.`)}}).catch(A=>{A.message!==At&&console.error(A)}),N}).apply(this,arguments)}}function at(){return(at=(0,h.A)(function*(t,o,s,a){return(yield z(o,U.COUNT_TOKENS,t,!1,JSON.stringify(s),a)).json()})).apply(this,arguments)}function lt(){return(lt=(0,h.A)(function*(t,o,s,a){return(yield z(o,U.EMBED_CONTENT,t,!1,JSON.stringify(s),a)).json()})).apply(this,arguments)}function q(){return(q=(0,h.A)(function*(t,o,s,a){const l=s.requests.map(u=>Object.assign(Object.assign({},u),{model:o}));return(yield z(o,U.BATCH_EMBED_CONTENTS,t,!1,JSON.stringify({requests:l}),a)).json()})).apply(this,arguments)}class Nt{constructor(o,s,a={}){this.apiKey=o,this._requestOptions=a,this.model=s.model.includes("/")?s.model:`models/${s.model}`,this.generationConfig=s.generationConfig||{},this.safetySettings=s.safetySettings||[],this.tools=s.tools,this.toolConfig=s.toolConfig,this.systemInstruction=Ot(s.systemInstruction),this.cachedContent=s.cachedContent}generateContent(o){var s=this;return(0,h.A)(function*(a,l={}){var d;const u=rt(a),g=Object.assign(Object.assign({},s._requestOptions),l);return _t(s.apiKey,s.model,Object.assign({generationConfig:s.generationConfig,safetySettings:s.safetySettings,tools:s.tools,toolConfig:s.toolConfig,systemInstruction:s.systemInstruction,cachedContent:null===(d=s.cachedContent)||void 0===d?void 0:d.name},u),g)}).apply(this,arguments)}generateContentStream(o){var s=this;return(0,h.A)(function*(a,l={}){var d;const u=rt(a),g=Object.assign(Object.assign({},s._requestOptions),l);return pt(s.apiKey,s.model,Object.assign({generationConfig:s.generationConfig,safetySettings:s.safetySettings,tools:s.tools,toolConfig:s.toolConfig,systemInstruction:s.systemInstruction,cachedContent:null===(d=s.cachedContent)||void 0===d?void 0:d.name},u),g)}).apply(this,arguments)}startChat(o){var s;return new Mt(this.apiKey,this.model,Object.assign({generationConfig:this.generationConfig,safetySettings:this.safetySettings,tools:this.tools,toolConfig:this.toolConfig,systemInstruction:this.systemInstruction,cachedContent:null===(s=this.cachedContent)||void 0===s?void 0:s.name},o),this._requestOptions)}countTokens(o){var s=this;return(0,h.A)(function*(a,l={}){const d=function Wt(t,o){var s;let a={model:o?.model,generationConfig:o?.generationConfig,safetySettings:o?.safetySettings,tools:o?.tools,toolConfig:o?.toolConfig,systemInstruction:o?.systemInstruction,cachedContent:null===(s=o?.cachedContent)||void 0===s?void 0:s.name,contents:[]};const l=null!=t.generateContentRequest;if(t.contents){if(l)throw new B("CountTokensRequest must have one of contents or generateContentRequest, not both.");a.contents=t.contents}else if(l)a=Object.assign(Object.assign({},a),t.generateContentRequest);else{const d=F(t);a.contents=[d]}return{generateContentRequest:a}}(a,{model:s.model,generationConfig:s.generationConfig,safetySettings:s.safetySettings,tools:s.tools,toolConfig:s.toolConfig,systemInstruction:s.systemInstruction,cachedContent:s.cachedContent}),u=Object.assign(Object.assign({},s._requestOptions),l);return function zt(t,o,s,a){return at.apply(this,arguments)}(s.apiKey,s.model,d,u)}).apply(this,arguments)}embedContent(o){var s=this;return(0,h.A)(function*(a,l={}){const d=function ft(t){return"string"==typeof t||Array.isArray(t)?{content:F(t)}:t}(a),u=Object.assign(Object.assign({},s._requestOptions),l);return function Yt(t,o,s,a){return lt.apply(this,arguments)}(s.apiKey,s.model,d,u)}).apply(this,arguments)}batchEmbedContents(o){var s=this;return(0,h.A)(function*(a,l={}){const d=Object.assign(Object.assign({},s._requestOptions),l);return function Lt(t,o,s,a){return q.apply(this,arguments)}(s.apiKey,s.model,a,d)}).apply(this,arguments)}}class Xt{constructor(o){this.apiKey=o}getGenerativeModel(o,s){if(!o.model)throw new T("Must provide a model name. Example: genai.getGenerativeModel({ model: 'my-model-name' })");return new Nt(this.apiKey,o,s)}getGenerativeModelFromCachedContent(o,s,a){if(!o.name)throw new B("Cached content must contain a `name` field.");if(!o.model)throw new B("Cached content must contain a `model` field.");const l=["model","systemInstruction"];for(const u of l)if(s?.[u]&&o[u]&&s?.[u]!==o[u]){if("model"===u&&(s.model.startsWith("models/")?s.model.replace("models/",""):s.model)===(o.model.startsWith("models/")?o.model.replace("models/",""):o.model))continue;throw new B(`Different value for "${u}" specified in modelParams (${s[u]}) and cachedContent (${o[u]})`)}const d=Object.assign(Object.assign({},s),{model:o.model,tools:o.tools,toolConfig:o.toolConfig,systemInstruction:o.systemInstruction,cachedContent:o});return new Nt(this.apiKey,d,a)}}var Ut=_(1985),Zt=_(2806),J=_(7673),c=_(6354),i=_(9437),e=_(5312),n=_(7705);let r=(()=>{class t{constructor(){this.apiAvailable=!0;const s=e.c.geminiApiKey||"AIzaSyDCXc16FzaVWSJkW4RGboTZ8AD9_PTDL88";try{this.genAI=new Xt(s),this.model=this.genAI.getGenerativeModel({model:"gemini-1.5-pro"}),console.log("Service AI initialis\xe9 avec succ\xe8s")}catch(a){console.error("Erreur lors de l'initialisation du service AI:",a),this.genAI={},this.model={generateContent:()=>Promise.resolve({response:{text:()=>"Service AI non disponible"}})},this.apiAvailable=!1}}generateProjectTasks(s,a,l){const d=Math.max(a,3),u=this.createFallbackTaskData(s,d,l);if(!this.isApiAvailable())return console.log("API Gemini non disponible, utilisation des donn\xe9es de d\xe9monstration"),new Ut.c(y=>{setTimeout(()=>{y.next(u),y.complete()},1e3)});let g="";l&&l.length>0&&(g=`\n      Membres de l'\xe9quipe:\n      ${l.map((y,O)=>`- ${y.name||y.firstName||y.lastName||`Membre ${O+1}`} (${y.role||"membre"})`).join("\n")}\n      `);const v=`\n      Agis comme un expert en gestion de projet. Je travaille sur un projet intitul\xe9 "${s}"\n      avec une \xe9quipe de ${d} membres.\n      ${g}\n\n      Divise ce projet en exactement ${d} entit\xe9s ou modules principaux qui peuvent \xeatre travaill\xe9s en parall\xe8le par chaque membre de l'\xe9quipe.\n\n      IMPORTANT: Chaque entit\xe9 doit \xeatre simple, claire et concise (maximum 3-4 mots).\n      Exemples d'entit\xe9s pour un site e-commerce avec 3 membres:\n      - CRUD des produits\n      - Interface utilisateur\n      - D\xe9ploiement\n\n      Pour chaque entit\xe9/module:\n      1. Donne un nom tr\xe8s court et concis (maximum 3-4 mots)\n      2. Fournis une br\xe8ve description (1 phrase maximum)\n      3. Liste 2-3 t\xe2ches sp\xe9cifiques avec leur priorit\xe9 (haute, moyenne, basse)\n\n      R\xe9ponds au format JSON suivant sans aucun texte suppl\xe9mentaire:\n      {\n        "projectTitle": "${s}",\n        "entities": [\n          {\n            "name": "Nom court de l'entit\xe9",\n            "description": "Description tr\xe8s br\xe8ve de l'entit\xe9",\n            "assignedTo": "Nom du membre (optionnel)",\n            "tasks": [\n              {\n                "title": "Titre court de la t\xe2che",\n                "description": "Description br\xe8ve de la t\xe2che",\n                "priority": "high|medium|low",\n                "status": "todo"\n              }\n            ]\n          }\n        ]\n      }\n    `;try{return(0,Zt.H)(this.model.generateContent(v)).pipe((0,c.T)(y=>{try{const S=y.response.text().match(/\{[\s\S]*\}/);return S?JSON.parse(S[0]):(console.warn("Format JSON non trouv\xe9 dans la r\xe9ponse, utilisation des donn\xe9es de d\xe9monstration"),u)}catch(O){return console.error("Erreur lors du parsing de la r\xe9ponse:",O),u}}),(0,i.W)(y=>(console.error("Erreur lors de la g\xe9n\xe9ration de contenu:",y),this.markApiAsUnavailable(),(0,J.of)(u))))}catch(y){return console.error("Erreur lors de l'appel \xe0 l'API:",y),this.markApiAsUnavailable(),(0,J.of)(u)}}createFallbackTaskData(s,a,l){const d=[];for(l&&l.length>0&&l.forEach((g,v)=>{d.push(g.name||g.firstName||(g.firstName&&g.lastName?`${g.firstName} ${g.lastName}`:null)||`Membre ${v+1}`)});d.length<a;)d.push(`Membre ${d.length+1}`);if(s.toLowerCase().includes("ecommerce")||s.toLowerCase().includes("e-commerce")||s.toLowerCase().includes("boutique"))return{projectTitle:s,entities:[{name:"CRUD des produits",description:"Gestion des produits dans la base de donn\xe9es",assignedTo:d[0]||"Non assign\xe9",tasks:[{title:"Cr\xe9er API produits",description:"D\xe9velopper les endpoints pour cr\xe9er, lire, modifier et supprimer des produits",priority:"high",status:"todo"},{title:"Mod\xe8le de donn\xe9es",description:"Concevoir le sch\xe9ma de la base de donn\xe9es pour les produits",priority:"medium",status:"todo"}]},{name:"Interface utilisateur",description:"D\xe9veloppement du frontend de l'application",assignedTo:d[1]||"Non assign\xe9",tasks:[{title:"Page d'accueil",description:"Cr\xe9er la page d'accueil avec la liste des produits",priority:"high",status:"todo"},{title:"Panier d'achat",description:"Impl\xe9menter la fonctionnalit\xe9 du panier d'achat",priority:"medium",status:"todo"}]},{name:"D\xe9ploiement",description:"Mise en production de l'application",assignedTo:d[2]||"Non assign\xe9",tasks:[{title:"Configuration serveur",description:"Configurer le serveur pour l'h\xe9bergement",priority:"medium",status:"todo"},{title:"Tests d'int\xe9gration",description:"Effectuer des tests d'int\xe9gration avant le d\xe9ploiement",priority:"high",status:"todo"}]},{name:"Gestion utilisateurs",description:"Syst\xe8me d'authentification et profils",assignedTo:d[3]||"Non assign\xe9",tasks:[{title:"Authentification",description:"Impl\xe9menter le syst\xe8me de connexion et d'inscription",priority:"high",status:"todo"},{title:"Profils utilisateurs",description:"Cr\xe9er les pages de profil et de gestion des informations personnelles",priority:"medium",status:"todo"}]},{name:"Paiement en ligne",description:"Int\xe9gration des syst\xe8mes de paiement",assignedTo:d[4]||"Non assign\xe9",tasks:[{title:"API de paiement",description:"Int\xe9grer une passerelle de paiement comme Stripe ou PayPal",priority:"high",status:"todo"},{title:"S\xe9curit\xe9 transactions",description:"Mettre en place les mesures de s\xe9curit\xe9 pour les transactions",priority:"high",status:"todo"}]},{name:"SEO & Analytics",description:"Optimisation pour les moteurs de recherche",assignedTo:d[5]||"Non assign\xe9",tasks:[{title:"Balises m\xe9ta",description:"Optimiser les balises m\xe9ta et la structure du site",priority:"medium",status:"todo"},{title:"Google Analytics",description:"Int\xe9grer des outils d'analyse du trafic",priority:"low",status:"todo"}]}].slice(0,a)};const u=[{name:"Backend",description:"D\xe9veloppement du backend de l'application"},{name:"Frontend",description:"D\xe9veloppement de l'interface utilisateur"},{name:"Base de donn\xe9es",description:"Conception et gestion de la base de donn\xe9es"},{name:"Tests",description:"Tests et assurance qualit\xe9"},{name:"D\xe9ploiement",description:"Configuration et d\xe9ploiement de l'application"},{name:"Documentation",description:"R\xe9daction de la documentation technique"}];return{projectTitle:s,entities:Array.from({length:a},(g,v)=>({name:u[v%u.length].name,description:u[v%u.length].description,assignedTo:d[v]||"Non assign\xe9",tasks:[{title:`Conception ${u[v%u.length].name}`,description:`Planifier l'architecture du ${u[v%u.length].name.toLowerCase()}`,priority:"high",status:"todo"},{title:`Impl\xe9mentation ${u[v%u.length].name}`,description:`D\xe9velopper les fonctionnalit\xe9s du ${u[v%u.length].name.toLowerCase()}`,priority:"medium",status:"todo"},{title:`Tests ${u[v%u.length].name}`,description:`Tester les fonctionnalit\xe9s du ${u[v%u.length].name.toLowerCase()}`,priority:"medium",status:"todo"}]}))}}isApiAvailable(){return this.apiAvailable}markApiAsUnavailable(){this.apiAvailable=!1,console.warn("API Gemini marqu\xe9e comme non disponible pour les prochains appels")}askProjectQuestion(s,a){const l=[`Pour votre projet "${a.title||"en cours"}", je recommande de commencer par d\xe9finir clairement les objectifs et les livrables attendus.`,`La gestion efficace d'un projet comme "${a.title||"celui-ci"}" n\xe9cessite une bonne planification et une communication claire entre les membres de l'\xe9quipe.`,`Pour r\xe9pondre \xe0 votre question sur "${s}", je vous sugg\xe8re de diviser le travail en t\xe2ches plus petites et de les assigner aux membres de l'\xe9quipe en fonction de leurs comp\xe9tences.`,"Dans le cadre de votre projet, il est important de d\xe9finir des jalons clairs et de suivre r\xe9guli\xe8rement l'avancement des travaux."],d=()=>{const g=Math.floor(Math.random()*l.length);return l[g]};if(!this.isApiAvailable())return console.log("API Gemini non disponible, utilisation d'une r\xe9ponse de secours"),(0,J.of)(d());const u=`\n      Contexte du projet:\n      Titre: ${a.title||"Non sp\xe9cifi\xe9"}\n      Description: ${a.description||"Non sp\xe9cifi\xe9e"}\n\n      Question: ${s}\n\n      R\xe9ponds de mani\xe8re concise et professionnelle en tant qu'assistant de gestion de projet.\n    `;try{return(0,Zt.H)(this.model.generateContent(u)).pipe((0,c.T)(g=>{try{return g.response.text()}catch(v){return console.error("Erreur lors de la r\xe9cup\xe9ration de la r\xe9ponse:",v),d()}}),(0,i.W)(g=>(console.error("Erreur lors de la g\xe9n\xe9ration de contenu:",g),this.markApiAsUnavailable(),(0,J.of)(d()))))}catch(g){return console.error("Erreur lors de la g\xe9n\xe9ration de contenu:",g),this.markApiAsUnavailable(),(0,J.of)(d())}}static{this.\u0275fac=function(a){return new(a||t)}}static{this.\u0275prov=n.jDH({token:t,factory:t.\u0275fac,providedIn:"root"})}}return t})()},8133:(Q,M,_)=>{_.d(M,{r:()=>x});var h=_(8810),C=_(8141),b=_(9437),R=_(6354),D=_(5312),P=_(7705),E=_(1626);let x=(()=>{class G{constructor(p){this.http=p,this.apiUrl=`${D.c.urlBackend}teams`,console.log("API URL:",this.apiUrl)}getEquipes(){return console.log("Fetching teams from:",this.apiUrl),this.http.get(this.apiUrl).pipe((0,C.M)(p=>console.log("Teams received:",p)),(0,b.W)(this.handleError))}getEquipe(p){return console.log(`Fetching team with id ${p} from: ${this.apiUrl}/${p}`),this.http.get(`${this.apiUrl}/${p}`).pipe((0,C.M)(m=>console.log("Team received:",m)),(0,b.W)(this.handleError))}addEquipe(p){return console.log("Adding team:",p),this.http.post(this.apiUrl,p).pipe((0,C.M)(m=>console.log("Team added, response:",m)),(0,b.W)(this.handleError))}updateEquipe(p,m){return console.log(`Updating team with id ${p}:`,m),this.http.put(`${this.apiUrl}/${p}`,m).pipe((0,C.M)(I=>console.log("Team updated, response:",I)),(0,b.W)(this.handleError))}deleteEquipe(p){return console.log(`Deleting team with id ${p}`),console.log(`API URL: ${this.apiUrl}/${p}`),this.http.delete(`${this.apiUrl}/${p}`).pipe((0,C.M)(m=>console.log("Team deleted, response:",m)),(0,b.W)(m=>(console.error("Error deleting team:",m),console.error("Request URL:",`${this.apiUrl}/${p}`),this.handleError(m))))}addMembreToEquipe(p,m){console.log(`Adding member to team ${p}:`,m);const I={userId:m.id,role:m.role||"membre"};return console.log("Sending to backend:",I),console.log("Team ID type:",typeof p,"value:",p),console.log("User ID type:",typeof m.id,"value:",m.id),this.http.post(`${this.apiUrl}/${p}/members`,I).pipe((0,C.M)(T=>console.log("Member added, response:",T)),(0,b.W)(this.handleError))}removeMembreFromEquipe(p,m){return console.log(`Removing member ${m} from team ${p}`),console.log(`API URL: ${this.apiUrl}/${p}/members/${m}`),this.http.delete(`${this.apiUrl}/${p}/members/${m}`).pipe((0,C.M)(I=>console.log("Member removed, response:",I)),(0,b.W)(I=>(console.error("Error removing member:",I),console.error("Request URL:",`${this.apiUrl}/${p}/members/${m}`),this.handleError(I))))}getTeamMembers(p){return console.log(`Fetching team members for team ${p}`),this.http.get(`${this.apiUrl}/${p}`).pipe((0,R.T)(m=>(console.log("Team data received:",m),m&&m.members?m.members.map(I=>({user:I,role:"membre",_id:I})):[])),(0,C.M)(m=>console.log("Team members processed:",m)),(0,b.W)(this.handleError))}handleError(p){let m="";if(p.error instanceof ErrorEvent)m=`Erreur client: ${p.error.message}`;else{const I=p.status;m=`Erreur serveur: Code ${I}, Message: ${p.error?.message||p.statusText}`,console.error("Error details:",{status:p.status,statusText:p.statusText,url:p.url,error:p.error}),0===I&&console.error("Le serveur est-il en cours d'ex\xe9cution? V\xe9rifiez la connexion r\xe9seau.")}return console.error("API Error:",m),(0,h.$)(()=>new Error(m))}static{this.\u0275fac=function(m){return new(m||G)(P.KVO(E.Qq))}}static{this.\u0275prov=P.jDH({token:G,factory:G.\u0275fac,providedIn:"root"})}}return G})()},1622:(Q,M,_)=>{_.d(M,{A:()=>E});var h=_(8810),C=_(8141),b=_(9437),R=_(5312),D=_(7705),P=_(1626);let E=(()=>{class x{constructor(f){this.http=f,this.apiUrl=`${R.c.urlBackend}teammembers`,console.log("Membre API URL:",this.apiUrl)}getMembres(){return console.log("Fetching members from:",this.apiUrl),this.http.get(this.apiUrl).pipe((0,C.M)(f=>console.log("Members received:",f)),(0,b.W)(this.handleError))}getMembre(f){return console.log(`Fetching member with id ${f} from: ${this.apiUrl}/${f}`),this.http.get(`${this.apiUrl}/${f}`).pipe((0,C.M)(p=>console.log("Member received:",p)),(0,b.W)(this.handleError))}addMembre(f){return console.log("Adding member:",f),this.http.post(this.apiUrl,f).pipe((0,C.M)(p=>console.log("Member added, response:",p)),(0,b.W)(this.handleError))}deleteMembre(f){return console.log(`Deleting member with id ${f}`),this.http.delete(`${this.apiUrl}/${f}`).pipe((0,C.M)(p=>console.log("Member deleted, response:",p)),(0,b.W)(this.handleError))}handleError(f){let p="";if(f.error instanceof ErrorEvent)p=`Erreur client: ${f.error.message}`;else{const m=f.status;p=`Erreur serveur: Code ${m}, Message: ${f.error?.message||f.statusText}`,console.error("Error details:",{status:f.status,statusText:f.statusText,url:f.url,error:f.error}),0===m&&console.error("Le serveur est-il en cours d'ex\xe9cution? V\xe9rifiez la connexion r\xe9seau.")}return console.error("API Error:",p),(0,h.$)(()=>new Error(p))}static{this.\u0275fac=function(p){return new(p||x)(D.KVO(P.Qq))}}static{this.\u0275prov=D.jDH({token:x,factory:x.\u0275fac,providedIn:"root"})}}return x})()},7473:(Q,M,_)=>{_.d(M,{J:()=>b});var h=_(4412),C=_(7705);let b=(()=>{class R{constructor(){this.notificationSubject=new h.t(null)}getNotifications(){return this.notificationSubject.asObservable()}showSuccess(P,E=5e3){this.show({message:P,type:"success",timeout:E})}showError(P,E=5e3){this.show({message:P,type:"error",timeout:E})}showInfo(P,E=5e3){this.show({message:P,type:"info",timeout:E})}showWarning(P,E=5e3){this.show({message:P,type:"warning",timeout:E})}show(P){this.notificationSubject.next(P),P.timeout&&setTimeout(()=>{this.notificationSubject.value===P&&this.notificationSubject.next(null)},P.timeout)}clear(){this.notificationSubject.next(null)}static{this.\u0275fac=function(E){return new(E||R)}}static{this.\u0275prov=C.jDH({token:R,factory:R.\u0275fac,providedIn:"root"})}}return R})()},2611:(Q,M,_)=>{_.d(M,{R:()=>E});var h=_(8810),C=_(8141),b=_(9437),R=_(5312),D=_(7705),P=_(1626);let E=(()=>{class x{constructor(f){this.http=f,this.apiUrl=`${R.c.urlBackend}tasks`,console.log("Task API URL:",this.apiUrl)}getTasks(){return this.http.get(this.apiUrl).pipe((0,C.M)(f=>console.log("Tasks received:",f)),(0,b.W)(this.handleError))}getTasksByTeam(f){return this.http.get(`${this.apiUrl}/team/${f}`).pipe((0,C.M)(p=>console.log(`Tasks for team ${f} received:`,p)),(0,b.W)(this.handleError))}getTask(f){return this.http.get(`${this.apiUrl}/${f}`).pipe((0,C.M)(p=>console.log("Task received:",p)),(0,b.W)(this.handleError))}createTask(f){return this.http.post(this.apiUrl,f).pipe((0,C.M)(p=>console.log("Task created:",p)),(0,b.W)(this.handleError))}updateTask(f,p){return this.http.put(`${this.apiUrl}/${f}`,p).pipe((0,C.M)(m=>console.log("Task updated:",m)),(0,b.W)(this.handleError))}deleteTask(f){return this.http.delete(`${this.apiUrl}/${f}`).pipe((0,C.M)(p=>console.log("Task deleted:",p)),(0,b.W)(this.handleError))}updateTaskStatus(f,p){return this.http.patch(`${this.apiUrl}/${f}/status`,{status:p}).pipe((0,C.M)(m=>console.log("Task status updated:",m)),(0,b.W)(this.handleError))}handleError(f){let p="";return p=f.error instanceof ErrorEvent?`Error: ${f.error.message}`:`Error Code: ${f.status}\nMessage: ${f.message}`,console.error(p),(0,h.$)(()=>new Error(p))}static{this.\u0275fac=function(p){return new(p||x)(D.KVO(P.Qq))}}static{this.\u0275prov=D.jDH({token:x,factory:x.\u0275fac,providedIn:"root"})}}return x})()},605:(Q,M,_)=>{_.d(M,{Y:()=>b});var h=_(3236),C=_(1584);function b(R=0,D=h.E){return R<0&&(R=0),(0,C.O)(R,R,D)}},1391:(Q,M,_)=>{_.d(M,{Fb:()=>xt,HD:()=>It,O7:()=>Ut,T1:()=>Lt,ad:()=>J,eg:()=>Gt});var h=_(7705),C=_(177),b=_(6535),R=_(6860),D=_(4085),P=_(9037),E=_(1413),x=_(8359),G=_(605),f=_(536),p=_(1985),m=_(7786),I=_(6977),T=_(6354),H=_(6697),tt=_(9172),B=_(8141),Et=_(5558),Dt=_(8203);function ct(c,i,e){for(let n in i)if(i.hasOwnProperty(n)){const r=i[n];r?c.setProperty(n,r,e?.has(n)?"important":""):c.removeProperty(n)}return c}function V(c,i){const e=i?"":"none";ct(c.style,{"touch-action":i?"":"none","-webkit-user-drag":i?"":"none","-webkit-tap-highlight-color":i?"":"transparent","user-select":e,"-ms-user-select":e,"-webkit-user-select":e,"-moz-user-select":e})}function yt(c,i,e){ct(c.style,{position:i?"":"fixed",top:i?"":"0",opacity:i?"":"0",left:i?"":"-999em"},e)}function U(c,i){return i&&"none"!=i?c+" "+i:c}function Ct(c){const i=c.toLowerCase().indexOf("ms")>-1?1:1e3;return parseFloat(c)*i}function dt(c,i){return c.getPropertyValue(i).split(",").map(n=>n.trim())}function X(c){const i=c.getBoundingClientRect();return{top:i.top,right:i.right,bottom:i.bottom,left:i.left,width:i.width,height:i.height,x:i.x,y:i.y}}function ht(c,i,e){const{top:n,bottom:r,left:t,right:o}=c;return e>=n&&e<=r&&i>=t&&i<=o}function j(c,i,e){c.top+=i,c.bottom=c.top+c.height,c.left+=e,c.right=c.left+c.width}function z(c,i,e,n){const{top:r,right:t,bottom:o,left:s,width:a,height:l}=c,d=a*i,u=l*i;return n>r-u&&n<o+u&&e>s-d&&e<t+d}class et{constructor(i){this._document=i,this.positions=new Map}clear(){this.positions.clear()}cache(i){this.clear(),this.positions.set(this._document,{scrollPosition:this.getViewportScrollPosition()}),i.forEach(e=>{this.positions.set(e,{scrollPosition:{top:e.scrollTop,left:e.scrollLeft},clientRect:X(e)})})}handleScroll(i){const e=(0,R.Fb)(i),n=this.positions.get(e);if(!n)return null;const r=n.scrollPosition;let t,o;if(e===this._document){const l=this.getViewportScrollPosition();t=l.top,o=l.left}else t=e.scrollTop,o=e.scrollLeft;const s=r.top-t,a=r.left-o;return this.positions.forEach((l,d)=>{l.clientRect&&e!==d&&e.contains(d)&&j(l.clientRect,s,a)}),r.top=t,r.left=o,{top:s,left:a}}getViewportScrollPosition(){return{top:window.scrollY,left:window.scrollX}}}function bt(c){const i=c.cloneNode(!0),e=i.querySelectorAll("[id]"),n=c.nodeName.toLowerCase();i.removeAttribute("id");for(let r=0;r<e.length;r++)e[r].removeAttribute("id");return"canvas"===n?st(c,i):("input"===n||"select"===n||"textarea"===n)&&Rt(c,i),it("canvas",c,i,st),it("input, textarea, select",c,i,Rt),i}function it(c,i,e,n){const r=i.querySelectorAll(c);if(r.length){const t=e.querySelectorAll(c);for(let o=0;o<r.length;o++)n(r[o],t[o])}}let Bt=0;function Rt(c,i){"file"!==i.type&&(i.value=c.value),"radio"===i.type&&i.name&&(i.name=`mat-clone-${i.name}-${Bt++}`)}function st(c,i){const e=i.getContext("2d");if(e)try{e.drawImage(c,0,0)}catch{}}const St=(0,R.BQ)({passive:!0}),Y=(0,R.BQ)({passive:!1}),nt=new Set(["position"]);class Kt{get disabled(){return this._disabled||!(!this._dropContainer||!this._dropContainer.disabled)}set disabled(i){const e=(0,D.he)(i);e!==this._disabled&&(this._disabled=e,this._toggleNativeDragInteractions(),this._handles.forEach(n=>V(n,e)))}constructor(i,e,n,r,t,o){this._config=e,this._document=n,this._ngZone=r,this._viewportRuler=t,this._dragDropRegistry=o,this._passiveTransform={x:0,y:0},this._activeTransform={x:0,y:0},this._hasStartedDragging=!1,this._moveEvents=new E.B,this._pointerMoveSubscription=x.yU.EMPTY,this._pointerUpSubscription=x.yU.EMPTY,this._scrollSubscription=x.yU.EMPTY,this._resizeSubscription=x.yU.EMPTY,this._boundaryElement=null,this._nativeInteractionsEnabled=!0,this._handles=[],this._disabledHandles=new Set,this._direction="ltr",this.dragStartDelay=0,this._disabled=!1,this.beforeStarted=new E.B,this.started=new E.B,this.released=new E.B,this.ended=new E.B,this.entered=new E.B,this.exited=new E.B,this.dropped=new E.B,this.moved=this._moveEvents,this._pointerDown=s=>{if(this.beforeStarted.next(),this._handles.length){const a=this._getTargetHandle(s);a&&!this._disabledHandles.has(a)&&!this.disabled&&this._initializeDragSequence(a,s)}else this.disabled||this._initializeDragSequence(this._rootElement,s)},this._pointerMove=s=>{const a=this._getPointerPositionOnPage(s);if(!this._hasStartedDragging){if(Math.abs(a.x-this._pickupPositionOnPage.x)+Math.abs(a.y-this._pickupPositionOnPage.y)>=this._config.dragStartThreshold){const v=Date.now()>=this._dragStartTime+this._getDragStartDelay(s),y=this._dropContainer;if(!v)return void this._endDragSequence(s);(!y||!y.isDragging()&&!y.isReceiving())&&(s.preventDefault(),this._hasStartedDragging=!0,this._ngZone.run(()=>this._startDragSequence(s)))}return}s.preventDefault();const l=this._getConstrainedPointerPosition(a);if(this._hasMoved=!0,this._lastKnownPointerPosition=a,this._updatePointerDirectionDelta(l),this._dropContainer)this._updateActiveDropContainer(l,a);else{const d=this.constrainPosition?this._initialClientRect:this._pickupPositionOnPage,u=this._activeTransform;u.x=l.x-d.x+this._passiveTransform.x,u.y=l.y-d.y+this._passiveTransform.y,this._applyRootElementTransform(u.x,u.y)}this._moveEvents.observers.length&&this._ngZone.run(()=>{this._moveEvents.next({source:this,pointerPosition:l,event:s,distance:this._getDragDistance(l),delta:this._pointerDirectionDelta})})},this._pointerUp=s=>{this._endDragSequence(s)},this._nativeDragStart=s=>{if(this._handles.length){const a=this._getTargetHandle(s);a&&!this._disabledHandles.has(a)&&!this.disabled&&s.preventDefault()}else this.disabled||s.preventDefault()},this.withRootElement(i).withParent(e.parentDragRef||null),this._parentPositions=new et(n),o.registerDragItem(this)}getPlaceholderElement(){return this._placeholder}getRootElement(){return this._rootElement}getVisibleElement(){return this.isDragging()?this.getPlaceholderElement():this.getRootElement()}withHandles(i){this._handles=i.map(n=>(0,D.i8)(n)),this._handles.forEach(n=>V(n,this.disabled)),this._toggleNativeDragInteractions();const e=new Set;return this._disabledHandles.forEach(n=>{this._handles.indexOf(n)>-1&&e.add(n)}),this._disabledHandles=e,this}withPreviewTemplate(i){return this._previewTemplate=i,this}withPlaceholderTemplate(i){return this._placeholderTemplate=i,this}withRootElement(i){const e=(0,D.i8)(i);return e!==this._rootElement&&(this._rootElement&&this._removeRootElementListeners(this._rootElement),this._ngZone.runOutsideAngular(()=>{e.addEventListener("mousedown",this._pointerDown,Y),e.addEventListener("touchstart",this._pointerDown,St),e.addEventListener("dragstart",this._nativeDragStart,Y)}),this._initialTransform=void 0,this._rootElement=e),typeof SVGElement<"u"&&this._rootElement instanceof SVGElement&&(this._ownerSVGElement=this._rootElement.ownerSVGElement),this}withBoundaryElement(i){return this._boundaryElement=i?(0,D.i8)(i):null,this._resizeSubscription.unsubscribe(),i&&(this._resizeSubscription=this._viewportRuler.change(10).subscribe(()=>this._containInsideBoundaryOnResize())),this}withParent(i){return this._parentDragRef=i,this}dispose(){this._removeRootElementListeners(this._rootElement),this.isDragging()&&this._rootElement?.remove(),this._anchor?.remove(),this._destroyPreview(),this._destroyPlaceholder(),this._dragDropRegistry.removeDragItem(this),this._removeSubscriptions(),this.beforeStarted.complete(),this.started.complete(),this.released.complete(),this.ended.complete(),this.entered.complete(),this.exited.complete(),this.dropped.complete(),this._moveEvents.complete(),this._handles=[],this._disabledHandles.clear(),this._dropContainer=void 0,this._resizeSubscription.unsubscribe(),this._parentPositions.clear(),this._boundaryElement=this._rootElement=this._ownerSVGElement=this._placeholderTemplate=this._previewTemplate=this._anchor=this._parentDragRef=null}isDragging(){return this._hasStartedDragging&&this._dragDropRegistry.isDragging(this)}reset(){this._rootElement.style.transform=this._initialTransform||"",this._activeTransform={x:0,y:0},this._passiveTransform={x:0,y:0}}disableHandle(i){!this._disabledHandles.has(i)&&this._handles.indexOf(i)>-1&&(this._disabledHandles.add(i),V(i,!0))}enableHandle(i){this._disabledHandles.has(i)&&(this._disabledHandles.delete(i),V(i,this.disabled))}withDirection(i){return this._direction=i,this}_withDropContainer(i){this._dropContainer=i}getFreeDragPosition(){const i=this.isDragging()?this._activeTransform:this._passiveTransform;return{x:i.x,y:i.y}}setFreeDragPosition(i){return this._activeTransform={x:0,y:0},this._passiveTransform.x=i.x,this._passiveTransform.y=i.y,this._dropContainer||this._applyRootElementTransform(i.x,i.y),this}withPreviewContainer(i){return this._previewContainer=i,this}_sortFromLastPointerPosition(){const i=this._lastKnownPointerPosition;i&&this._dropContainer&&this._updateActiveDropContainer(this._getConstrainedPointerPosition(i),i)}_removeSubscriptions(){this._pointerMoveSubscription.unsubscribe(),this._pointerUpSubscription.unsubscribe(),this._scrollSubscription.unsubscribe()}_destroyPreview(){this._preview?.remove(),this._previewRef?.destroy(),this._preview=this._previewRef=null}_destroyPlaceholder(){this._placeholder?.remove(),this._placeholderRef?.destroy(),this._placeholder=this._placeholderRef=null}_endDragSequence(i){if(this._dragDropRegistry.isDragging(this)&&(this._removeSubscriptions(),this._dragDropRegistry.stopDragging(this),this._toggleNativeDragInteractions(),this._handles&&(this._rootElement.style.webkitTapHighlightColor=this._rootElementTapHighlight),this._hasStartedDragging))if(this.released.next({source:this,event:i}),this._dropContainer)this._dropContainer._stopScrolling(),this._animatePreviewToPlaceholder().then(()=>{this._cleanupDragArtifacts(i),this._cleanupCachedDimensions(),this._dragDropRegistry.stopDragging(this)});else{this._passiveTransform.x=this._activeTransform.x;const e=this._getPointerPositionOnPage(i);this._passiveTransform.y=this._activeTransform.y,this._ngZone.run(()=>{this.ended.next({source:this,distance:this._getDragDistance(e),dropPoint:e,event:i})}),this._cleanupCachedDimensions(),this._dragDropRegistry.stopDragging(this)}}_startDragSequence(i){$(i)&&(this._lastTouchEventTime=Date.now()),this._toggleNativeDragInteractions();const e=this._dropContainer;if(e){const n=this._rootElement,r=n.parentNode,t=this._placeholder=this._createPlaceholderElement(),o=this._anchor=this._anchor||this._document.createComment(""),s=this._getShadowRoot();r.insertBefore(o,n),this._initialTransform=n.style.transform||"",this._preview=this._createPreviewElement(),yt(n,!1,nt),this._document.body.appendChild(r.replaceChild(t,n)),this._getPreviewInsertionPoint(r,s).appendChild(this._preview),this.started.next({source:this,event:i}),e.start(),this._initialContainer=e,this._initialIndex=e.getItemIndex(this)}else this.started.next({source:this,event:i}),this._initialContainer=this._initialIndex=void 0;this._parentPositions.cache(e?e.getScrollableParents():[])}_initializeDragSequence(i,e){this._parentDragRef&&e.stopPropagation();const n=this.isDragging(),r=$(e),t=!r&&0!==e.button,o=this._rootElement,s=(0,R.Fb)(e),a=!r&&this._lastTouchEventTime&&this._lastTouchEventTime+800>Date.now(),l=r?(0,P.w6)(e):(0,P._G)(e);if(s&&s.draggable&&"mousedown"===e.type&&e.preventDefault(),n||t||a||l)return;if(this._handles.length){const g=o.style;this._rootElementTapHighlight=g.webkitTapHighlightColor||"",g.webkitTapHighlightColor="transparent"}this._hasStartedDragging=this._hasMoved=!1,this._removeSubscriptions(),this._initialClientRect=this._rootElement.getBoundingClientRect(),this._pointerMoveSubscription=this._dragDropRegistry.pointerMove.subscribe(this._pointerMove),this._pointerUpSubscription=this._dragDropRegistry.pointerUp.subscribe(this._pointerUp),this._scrollSubscription=this._dragDropRegistry.scrolled(this._getShadowRoot()).subscribe(g=>this._updateOnScroll(g)),this._boundaryElement&&(this._boundaryRect=X(this._boundaryElement));const d=this._previewTemplate;this._pickupPositionInElement=d&&d.template&&!d.matchSize?{x:0,y:0}:this._getPointerPositionInElement(this._initialClientRect,i,e);const u=this._pickupPositionOnPage=this._lastKnownPointerPosition=this._getPointerPositionOnPage(e);this._pointerDirectionDelta={x:0,y:0},this._pointerPositionAtLastDirectionChange={x:u.x,y:u.y},this._dragStartTime=Date.now(),this._dragDropRegistry.startDragging(this,e)}_cleanupDragArtifacts(i){yt(this._rootElement,!0,nt),this._anchor.parentNode.replaceChild(this._rootElement,this._anchor),this._destroyPreview(),this._destroyPlaceholder(),this._initialClientRect=this._boundaryRect=this._previewRect=this._initialTransform=void 0,this._ngZone.run(()=>{const e=this._dropContainer,n=e.getItemIndex(this),r=this._getPointerPositionOnPage(i),t=this._getDragDistance(r),o=e._isOverContainer(r.x,r.y);this.ended.next({source:this,distance:t,dropPoint:r,event:i}),this.dropped.next({item:this,currentIndex:n,previousIndex:this._initialIndex,container:e,previousContainer:this._initialContainer,isPointerOverContainer:o,distance:t,dropPoint:r,event:i}),e.drop(this,n,this._initialIndex,this._initialContainer,o,t,r,i),this._dropContainer=this._initialContainer})}_updateActiveDropContainer({x:i,y:e},{x:n,y:r}){let t=this._initialContainer._getSiblingContainerFromPosition(this,i,e);!t&&this._dropContainer!==this._initialContainer&&this._initialContainer._isOverContainer(i,e)&&(t=this._initialContainer),t&&t!==this._dropContainer&&this._ngZone.run(()=>{this.exited.next({item:this,container:this._dropContainer}),this._dropContainer.exit(this),this._dropContainer=t,this._dropContainer.enter(this,i,e,t===this._initialContainer&&t.sortingDisabled?this._initialIndex:void 0),this.entered.next({item:this,container:t,currentIndex:t.getItemIndex(this)})}),this.isDragging()&&(this._dropContainer._startScrollingIfNecessary(n,r),this._dropContainer._sortItem(this,i,e,this._pointerDirectionDelta),this.constrainPosition?this._applyPreviewTransform(i,e):this._applyPreviewTransform(i-this._pickupPositionInElement.x,e-this._pickupPositionInElement.y))}_createPreviewElement(){const i=this._previewTemplate,e=this.previewClass,n=i?i.template:null;let r;if(n&&i){const t=i.matchSize?this._initialClientRect:null,o=i.viewContainer.createEmbeddedView(n,i.context);o.detectChanges(),r=Pt(o,this._document),this._previewRef=o,i.matchSize?ut(r,t):r.style.transform=W(this._pickupPositionOnPage.x,this._pickupPositionOnPage.y)}else r=bt(this._rootElement),ut(r,this._initialClientRect),this._initialTransform&&(r.style.transform=this._initialTransform);return ct(r.style,{"pointer-events":"none",margin:"0",position:"fixed",top:"0",left:"0","z-index":`${this._config.zIndex||1e3}`},nt),V(r,!1),r.classList.add("cdk-drag-preview"),r.setAttribute("dir",this._direction),e&&(Array.isArray(e)?e.forEach(t=>r.classList.add(t)):r.classList.add(e)),r}_animatePreviewToPlaceholder(){if(!this._hasMoved)return Promise.resolve();const i=this._placeholder.getBoundingClientRect();this._preview.classList.add("cdk-drag-animating"),this._applyPreviewTransform(i.left,i.top);const e=function $t(c){const i=getComputedStyle(c),e=dt(i,"transition-property"),n=e.find(s=>"transform"===s||"all"===s);if(!n)return 0;const r=e.indexOf(n),t=dt(i,"transition-duration"),o=dt(i,"transition-delay");return Ct(t[r])+Ct(o[r])}(this._preview);return 0===e?Promise.resolve():this._ngZone.runOutsideAngular(()=>new Promise(n=>{const r=o=>{(!o||(0,R.Fb)(o)===this._preview&&"transform"===o.propertyName)&&(this._preview?.removeEventListener("transitionend",r),n(),clearTimeout(t))},t=setTimeout(r,1.5*e);this._preview.addEventListener("transitionend",r)}))}_createPlaceholderElement(){const i=this._placeholderTemplate,e=i?i.template:null;let n;return e?(this._placeholderRef=i.viewContainer.createEmbeddedView(e,i.context),this._placeholderRef.detectChanges(),n=Pt(this._placeholderRef,this._document)):n=bt(this._rootElement),n.style.pointerEvents="none",n.classList.add("cdk-drag-placeholder"),n}_getPointerPositionInElement(i,e,n){const r=e===this._rootElement?null:e,t=r?r.getBoundingClientRect():i,o=$(n)?n.targetTouches[0]:n,s=this._getViewportScrollPosition();return{x:t.left-i.left+(o.pageX-t.left-s.left),y:t.top-i.top+(o.pageY-t.top-s.top)}}_getPointerPositionOnPage(i){const e=this._getViewportScrollPosition(),n=$(i)?i.touches[0]||i.changedTouches[0]||{pageX:0,pageY:0}:i,r=n.pageX-e.left,t=n.pageY-e.top;if(this._ownerSVGElement){const o=this._ownerSVGElement.getScreenCTM();if(o){const s=this._ownerSVGElement.createSVGPoint();return s.x=r,s.y=t,s.matrixTransform(o.inverse())}}return{x:r,y:t}}_getConstrainedPointerPosition(i){const e=this._dropContainer?this._dropContainer.lockAxis:null;let{x:n,y:r}=this.constrainPosition?this.constrainPosition(i,this,this._initialClientRect,this._pickupPositionInElement):i;if("x"===this.lockAxis||"x"===e?r=this._pickupPositionOnPage.y-(this.constrainPosition?this._pickupPositionInElement.y:0):("y"===this.lockAxis||"y"===e)&&(n=this._pickupPositionOnPage.x-(this.constrainPosition?this._pickupPositionInElement.x:0)),this._boundaryRect){const{x:t,y:o}=this.constrainPosition?{x:0,y:0}:this._pickupPositionInElement,s=this._boundaryRect,{width:a,height:l}=this._getPreviewRect(),d=s.top+o,u=s.bottom-(l-o);n=k(n,s.left+t,s.right-(a-t)),r=k(r,d,u)}return{x:n,y:r}}_updatePointerDirectionDelta(i){const{x:e,y:n}=i,r=this._pointerDirectionDelta,t=this._pointerPositionAtLastDirectionChange,o=Math.abs(e-t.x),s=Math.abs(n-t.y);return o>this._config.pointerDirectionChangeThreshold&&(r.x=e>t.x?1:-1,t.x=e),s>this._config.pointerDirectionChangeThreshold&&(r.y=n>t.y?1:-1,t.y=n),r}_toggleNativeDragInteractions(){if(!this._rootElement||!this._handles)return;const i=this._handles.length>0||!this.isDragging();i!==this._nativeInteractionsEnabled&&(this._nativeInteractionsEnabled=i,V(this._rootElement,i))}_removeRootElementListeners(i){i.removeEventListener("mousedown",this._pointerDown,Y),i.removeEventListener("touchstart",this._pointerDown,St),i.removeEventListener("dragstart",this._nativeDragStart,Y)}_applyRootElementTransform(i,e){const n=W(i,e),r=this._rootElement.style;null==this._initialTransform&&(this._initialTransform=r.transform&&"none"!=r.transform?r.transform:""),r.transform=U(n,this._initialTransform)}_applyPreviewTransform(i,e){const n=this._previewTemplate?.template?void 0:this._initialTransform,r=W(i,e);this._preview.style.transform=U(r,n)}_getDragDistance(i){const e=this._pickupPositionOnPage;return e?{x:i.x-e.x,y:i.y-e.y}:{x:0,y:0}}_cleanupCachedDimensions(){this._boundaryRect=this._previewRect=void 0,this._parentPositions.clear()}_containInsideBoundaryOnResize(){let{x:i,y:e}=this._passiveTransform;if(0===i&&0===e||this.isDragging()||!this._boundaryElement)return;const n=this._rootElement.getBoundingClientRect(),r=this._boundaryElement.getBoundingClientRect();if(0===r.width&&0===r.height||0===n.width&&0===n.height)return;const t=r.left-n.left,o=n.right-r.right,s=r.top-n.top,a=n.bottom-r.bottom;r.width>n.width?(t>0&&(i+=t),o>0&&(i-=o)):i=0,r.height>n.height?(s>0&&(e+=s),a>0&&(e-=a)):e=0,(i!==this._passiveTransform.x||e!==this._passiveTransform.y)&&this.setFreeDragPosition({y:e,x:i})}_getDragStartDelay(i){const e=this.dragStartDelay;return"number"==typeof e?e:$(i)?e.touch:e?e.mouse:0}_updateOnScroll(i){const e=this._parentPositions.handleScroll(i);if(e){const n=(0,R.Fb)(i);this._boundaryRect&&n!==this._boundaryElement&&n.contains(this._boundaryElement)&&j(this._boundaryRect,e.top,e.left),this._pickupPositionOnPage.x+=e.left,this._pickupPositionOnPage.y+=e.top,this._dropContainer||(this._activeTransform.x-=e.left,this._activeTransform.y-=e.top,this._applyRootElementTransform(this._activeTransform.x,this._activeTransform.y))}}_getViewportScrollPosition(){return this._parentPositions.positions.get(this._document)?.scrollPosition||this._parentPositions.getViewportScrollPosition()}_getShadowRoot(){return void 0===this._cachedShadowRoot&&(this._cachedShadowRoot=(0,R.KT)(this._rootElement)),this._cachedShadowRoot}_getPreviewInsertionPoint(i,e){const n=this._previewContainer||"global";if("parent"===n)return i;if("global"===n){const r=this._document;return e||r.fullscreenElement||r.webkitFullscreenElement||r.mozFullScreenElement||r.msFullscreenElement||r.body}return(0,D.i8)(n)}_getPreviewRect(){return(!this._previewRect||!this._previewRect.width&&!this._previewRect.height)&&(this._previewRect=this._preview?this._preview.getBoundingClientRect():this._initialClientRect),this._previewRect}_getTargetHandle(i){return this._handles.find(e=>i.target&&(i.target===e||e.contains(i.target)))}}function W(c,i){return`translate3d(${Math.round(c)}px, ${Math.round(i)}px, 0)`}function k(c,i,e){return Math.max(i,Math.min(e,c))}function $(c){return"t"===c.type[0]}function Pt(c,i){const e=c.rootNodes;if(1===e.length&&e[0].nodeType===i.ELEMENT_NODE)return e[0];const n=i.createElement("div");return e.forEach(r=>n.appendChild(r)),n}function ut(c,i){c.style.width=`${i.width}px`,c.style.height=`${i.height}px`,c.style.transform=W(i.left,i.top)}function It(c,i,e){const n=Z(i,c.length-1),r=Z(e,c.length-1);if(n===r)return;const t=c[n],o=r<n?-1:1;for(let s=n;s!==r;s+=o)c[s]=c[s+o];c[r]=t}function Gt(c,i,e,n){const r=Z(e,c.length-1),t=Z(n,i.length);c.length&&i.splice(t,0,c.splice(r,1)[0])}function Z(c,i){return Math.max(0,Math.min(i,c))}class Ht{constructor(i,e){this._element=i,this._dragDropRegistry=e,this._itemPositions=[],this.orientation="vertical",this._previousSwap={drag:null,delta:0,overlaps:!1}}start(i){this.withItems(i)}sort(i,e,n,r){const t=this._itemPositions,o=this._getItemIndexFromPointerPosition(i,e,n,r);if(-1===o&&t.length>0)return null;const s="horizontal"===this.orientation,a=t.findIndex(S=>S.drag===i),l=t[o],u=l.clientRect,g=a>o?1:-1,v=this._getItemOffsetPx(t[a].clientRect,u,g),y=this._getSiblingOffsetPx(a,t,g),O=t.slice();return It(t,a,o),t.forEach((S,w)=>{if(O[w]===S)return;const L=S.drag===i,N=L?v:y,A=L?i.getPlaceholderElement():S.drag.getRootElement();S.offset+=N,s?(A.style.transform=U(`translate3d(${Math.round(S.offset)}px, 0, 0)`,S.initialTransform),j(S.clientRect,0,N)):(A.style.transform=U(`translate3d(0, ${Math.round(S.offset)}px, 0)`,S.initialTransform),j(S.clientRect,N,0))}),this._previousSwap.overlaps=ht(u,e,n),this._previousSwap.drag=l.drag,this._previousSwap.delta=s?r.x:r.y,{previousIndex:a,currentIndex:o}}enter(i,e,n,r){const t=null==r||r<0?this._getItemIndexFromPointerPosition(i,e,n):r,o=this._activeDraggables,s=o.indexOf(i),a=i.getPlaceholderElement();let l=o[t];if(l===i&&(l=o[t+1]),!l&&(null==t||-1===t||t<o.length-1)&&this._shouldEnterAsFirstChild(e,n)&&(l=o[0]),s>-1&&o.splice(s,1),l&&!this._dragDropRegistry.isDragging(l)){const d=l.getRootElement();d.parentElement.insertBefore(a,d),o.splice(t,0,i)}else(0,D.i8)(this._element).appendChild(a),o.push(i);a.style.transform="",this._cacheItemPositions()}withItems(i){this._activeDraggables=i.slice(),this._cacheItemPositions()}withSortPredicate(i){this._sortPredicate=i}reset(){this._activeDraggables.forEach(i=>{const e=i.getRootElement();if(e){const n=this._itemPositions.find(r=>r.drag===i)?.initialTransform;e.style.transform=n||""}}),this._itemPositions=[],this._activeDraggables=[],this._previousSwap.drag=null,this._previousSwap.delta=0,this._previousSwap.overlaps=!1}getActiveItemsSnapshot(){return this._activeDraggables}getItemIndex(i){return("horizontal"===this.orientation&&"rtl"===this.direction?this._itemPositions.slice().reverse():this._itemPositions).findIndex(n=>n.drag===i)}updateOnScroll(i,e){this._itemPositions.forEach(({clientRect:n})=>{j(n,i,e)}),this._itemPositions.forEach(({drag:n})=>{this._dragDropRegistry.isDragging(n)&&n._sortFromLastPointerPosition()})}_cacheItemPositions(){const i="horizontal"===this.orientation;this._itemPositions=this._activeDraggables.map(e=>{const n=e.getVisibleElement();return{drag:e,offset:0,initialTransform:n.style.transform||"",clientRect:X(n)}}).sort((e,n)=>i?e.clientRect.left-n.clientRect.left:e.clientRect.top-n.clientRect.top)}_getItemOffsetPx(i,e,n){const r="horizontal"===this.orientation;let t=r?e.left-i.left:e.top-i.top;return-1===n&&(t+=r?e.width-i.width:e.height-i.height),t}_getSiblingOffsetPx(i,e,n){const r="horizontal"===this.orientation,t=e[i].clientRect,o=e[i+-1*n];let s=t[r?"width":"height"]*n;if(o){const a=r?"left":"top",l=r?"right":"bottom";-1===n?s-=o.clientRect[a]-t[l]:s+=t[a]-o.clientRect[l]}return s}_shouldEnterAsFirstChild(i,e){if(!this._activeDraggables.length)return!1;const n=this._itemPositions,r="horizontal"===this.orientation;if(n[0].drag!==this._activeDraggables[0]){const o=n[n.length-1].clientRect;return r?i>=o.right:e>=o.bottom}{const o=n[0].clientRect;return r?i<=o.left:e<=o.top}}_getItemIndexFromPointerPosition(i,e,n,r){const t="horizontal"===this.orientation,o=this._itemPositions.findIndex(({drag:s,clientRect:a})=>s!==i&&((!r||s!==this._previousSwap.drag||!this._previousSwap.overlaps||(t?r.x:r.y)!==this._previousSwap.delta)&&(t?e>=Math.floor(a.left)&&e<Math.floor(a.right):n>=Math.floor(a.top)&&n<Math.floor(a.bottom))));return-1!==o&&this._sortPredicate(o,i)?o:-1}}class gt{constructor(i,e,n,r,t){this._dragDropRegistry=e,this._ngZone=r,this._viewportRuler=t,this.disabled=!1,this.sortingDisabled=!1,this.autoScrollDisabled=!1,this.autoScrollStep=2,this.enterPredicate=()=>!0,this.sortPredicate=()=>!0,this.beforeStarted=new E.B,this.entered=new E.B,this.exited=new E.B,this.dropped=new E.B,this.sorted=new E.B,this.receivingStarted=new E.B,this.receivingStopped=new E.B,this._isDragging=!1,this._draggables=[],this._siblings=[],this._activeSiblings=new Set,this._viewportScrollSubscription=x.yU.EMPTY,this._verticalScrollDirection=0,this._horizontalScrollDirection=0,this._stopScrollTimers=new E.B,this._cachedShadowRoot=null,this._startScrollInterval=()=>{this._stopScrolling(),(0,G.Y)(0,f.X).pipe((0,I.Q)(this._stopScrollTimers)).subscribe(()=>{const o=this._scrollNode,s=this.autoScrollStep;1===this._verticalScrollDirection?o.scrollBy(0,-s):2===this._verticalScrollDirection&&o.scrollBy(0,s),1===this._horizontalScrollDirection?o.scrollBy(-s,0):2===this._horizontalScrollDirection&&o.scrollBy(s,0)})},this.element=(0,D.i8)(i),this._document=n,this.withScrollableParents([this.element]),e.registerDropContainer(this),this._parentPositions=new et(n),this._sortStrategy=new Ht(this.element,e),this._sortStrategy.withSortPredicate((o,s)=>this.sortPredicate(o,s,this))}dispose(){this._stopScrolling(),this._stopScrollTimers.complete(),this._viewportScrollSubscription.unsubscribe(),this.beforeStarted.complete(),this.entered.complete(),this.exited.complete(),this.dropped.complete(),this.sorted.complete(),this.receivingStarted.complete(),this.receivingStopped.complete(),this._activeSiblings.clear(),this._scrollNode=null,this._parentPositions.clear(),this._dragDropRegistry.removeDropContainer(this)}isDragging(){return this._isDragging}start(){this._draggingStarted(),this._notifyReceivingSiblings()}enter(i,e,n,r){this._draggingStarted(),null==r&&this.sortingDisabled&&(r=this._draggables.indexOf(i)),this._sortStrategy.enter(i,e,n,r),this._cacheParentPositions(),this._notifyReceivingSiblings(),this.entered.next({item:i,container:this,currentIndex:this.getItemIndex(i)})}exit(i){this._reset(),this.exited.next({item:i,container:this})}drop(i,e,n,r,t,o,s,a={}){this._reset(),this.dropped.next({item:i,currentIndex:e,previousIndex:n,container:this,previousContainer:r,isPointerOverContainer:t,distance:o,dropPoint:s,event:a})}withItems(i){const e=this._draggables;return this._draggables=i,i.forEach(n=>n._withDropContainer(this)),this.isDragging()&&(e.filter(r=>r.isDragging()).every(r=>-1===i.indexOf(r))?this._reset():this._sortStrategy.withItems(this._draggables)),this}withDirection(i){return this._sortStrategy.direction=i,this}connectedTo(i){return this._siblings=i.slice(),this}withOrientation(i){return this._sortStrategy.orientation=i,this}withScrollableParents(i){const e=(0,D.i8)(this.element);return this._scrollableElements=-1===i.indexOf(e)?[e,...i]:i.slice(),this}getScrollableParents(){return this._scrollableElements}getItemIndex(i){return this._isDragging?this._sortStrategy.getItemIndex(i):this._draggables.indexOf(i)}isReceiving(){return this._activeSiblings.size>0}_sortItem(i,e,n,r){if(this.sortingDisabled||!this._clientRect||!z(this._clientRect,.05,e,n))return;const t=this._sortStrategy.sort(i,e,n,r);t&&this.sorted.next({previousIndex:t.previousIndex,currentIndex:t.currentIndex,container:this,item:i})}_startScrollingIfNecessary(i,e){if(this.autoScrollDisabled)return;let n,r=0,t=0;if(this._parentPositions.positions.forEach((o,s)=>{s===this._document||!o.clientRect||n||z(o.clientRect,.05,i,e)&&([r,t]=function Ot(c,i,e,n){const r=_t(i,n),t=ot(i,e);let o=0,s=0;if(r){const a=c.scrollTop;1===r?a>0&&(o=1):c.scrollHeight-a>c.clientHeight&&(o=2)}if(t){const a=c.scrollLeft;1===t?a>0&&(s=1):c.scrollWidth-a>c.clientWidth&&(s=2)}return[o,s]}(s,o.clientRect,i,e),(r||t)&&(n=s))}),!r&&!t){const{width:o,height:s}=this._viewportRuler.getViewportSize(),a={width:o,height:s,top:0,right:o,bottom:s,left:0};r=_t(a,e),t=ot(a,i),n=window}n&&(r!==this._verticalScrollDirection||t!==this._horizontalScrollDirection||n!==this._scrollNode)&&(this._verticalScrollDirection=r,this._horizontalScrollDirection=t,this._scrollNode=n,(r||t)&&n?this._ngZone.runOutsideAngular(this._startScrollInterval):this._stopScrolling())}_stopScrolling(){this._stopScrollTimers.next()}_draggingStarted(){const i=(0,D.i8)(this.element).style;this.beforeStarted.next(),this._isDragging=!0,this._initialScrollSnap=i.msScrollSnapType||i.scrollSnapType||"",i.scrollSnapType=i.msScrollSnapType="none",this._sortStrategy.start(this._draggables),this._cacheParentPositions(),this._viewportScrollSubscription.unsubscribe(),this._listenToScrollEvents()}_cacheParentPositions(){const i=(0,D.i8)(this.element);this._parentPositions.cache(this._scrollableElements),this._clientRect=this._parentPositions.positions.get(i).clientRect}_reset(){this._isDragging=!1;const i=(0,D.i8)(this.element).style;i.scrollSnapType=i.msScrollSnapType=this._initialScrollSnap,this._siblings.forEach(e=>e._stopReceiving(this)),this._sortStrategy.reset(),this._stopScrolling(),this._viewportScrollSubscription.unsubscribe(),this._parentPositions.clear()}_isOverContainer(i,e){return null!=this._clientRect&&ht(this._clientRect,i,e)}_getSiblingContainerFromPosition(i,e,n){return this._siblings.find(r=>r._canReceive(i,e,n))}_canReceive(i,e,n){if(!this._clientRect||!ht(this._clientRect,e,n)||!this.enterPredicate(i,this))return!1;const r=this._getShadowRoot().elementFromPoint(e,n);if(!r)return!1;const t=(0,D.i8)(this.element);return r===t||t.contains(r)}_startReceiving(i,e){const n=this._activeSiblings;!n.has(i)&&e.every(r=>this.enterPredicate(r,this)||this._draggables.indexOf(r)>-1)&&(n.add(i),this._cacheParentPositions(),this._listenToScrollEvents(),this.receivingStarted.next({initiator:i,receiver:this,items:e}))}_stopReceiving(i){this._activeSiblings.delete(i),this._viewportScrollSubscription.unsubscribe(),this.receivingStopped.next({initiator:i,receiver:this})}_listenToScrollEvents(){this._viewportScrollSubscription=this._dragDropRegistry.scrolled(this._getShadowRoot()).subscribe(i=>{if(this.isDragging()){const e=this._parentPositions.handleScroll(i);e&&this._sortStrategy.updateOnScroll(e.top,e.left)}else this.isReceiving()&&this._cacheParentPositions()})}_getShadowRoot(){if(!this._cachedShadowRoot){const i=(0,R.KT)((0,D.i8)(this.element));this._cachedShadowRoot=i||this._document}return this._cachedShadowRoot}_notifyReceivingSiblings(){const i=this._sortStrategy.getActiveItemsSnapshot().filter(e=>e.isDragging());this._siblings.forEach(e=>e._startReceiving(this,i))}}function _t(c,i){const{top:e,bottom:n,height:r}=c,t=.05*r;return i>=e-t&&i<=e+t?1:i>=n-t&&i<=n+t?2:0}function ot(c,i){const{left:e,right:n,width:r}=c,t=.05*r;return i>=e-t&&i<=e+t?1:i>=n-t&&i<=n+t?2:0}const F=(0,R.BQ)({passive:!1,capture:!0});let jt=(()=>{class c{constructor(e,n){this._ngZone=e,this._dropInstances=new Set,this._dragInstances=new Set,this._activeDragInstances=[],this._globalListeners=new Map,this._draggingPredicate=r=>r.isDragging(),this.pointerMove=new E.B,this.pointerUp=new E.B,this.scroll=new E.B,this._preventDefaultWhileDragging=r=>{this._activeDragInstances.length>0&&r.preventDefault()},this._persistentTouchmoveListener=r=>{this._activeDragInstances.length>0&&(this._activeDragInstances.some(this._draggingPredicate)&&r.preventDefault(),this.pointerMove.next(r))},this._document=n}registerDropContainer(e){this._dropInstances.has(e)||this._dropInstances.add(e)}registerDragItem(e){this._dragInstances.add(e),1===this._dragInstances.size&&this._ngZone.runOutsideAngular(()=>{this._document.addEventListener("touchmove",this._persistentTouchmoveListener,F)})}removeDropContainer(e){this._dropInstances.delete(e)}removeDragItem(e){this._dragInstances.delete(e),this.stopDragging(e),0===this._dragInstances.size&&this._document.removeEventListener("touchmove",this._persistentTouchmoveListener,F)}startDragging(e,n){if(!(this._activeDragInstances.indexOf(e)>-1)&&(this._activeDragInstances.push(e),1===this._activeDragInstances.length)){const r=n.type.startsWith("touch");this._globalListeners.set(r?"touchend":"mouseup",{handler:t=>this.pointerUp.next(t),options:!0}).set("scroll",{handler:t=>this.scroll.next(t),options:!0}).set("selectstart",{handler:this._preventDefaultWhileDragging,options:F}),r||this._globalListeners.set("mousemove",{handler:t=>this.pointerMove.next(t),options:F}),this._ngZone.runOutsideAngular(()=>{this._globalListeners.forEach((t,o)=>{this._document.addEventListener(o,t.handler,t.options)})})}}stopDragging(e){const n=this._activeDragInstances.indexOf(e);n>-1&&(this._activeDragInstances.splice(n,1),0===this._activeDragInstances.length&&this._clearGlobalListeners())}isDragging(e){return this._activeDragInstances.indexOf(e)>-1}scrolled(e){const n=[this.scroll];return e&&e!==this._document&&n.push(new p.c(r=>this._ngZone.runOutsideAngular(()=>{const o=s=>{this._activeDragInstances.length&&r.next(s)};return e.addEventListener("scroll",o,!0),()=>{e.removeEventListener("scroll",o,!0)}}))),(0,m.h)(...n)}ngOnDestroy(){this._dragInstances.forEach(e=>this.removeDragItem(e)),this._dropInstances.forEach(e=>this.removeDropContainer(e)),this._clearGlobalListeners(),this.pointerMove.complete(),this.pointerUp.complete()}_clearGlobalListeners(){this._globalListeners.forEach((e,n)=>{this._document.removeEventListener(n,e.handler,e.options)}),this._globalListeners.clear()}static{this.\u0275fac=function(n){return new(n||c)(h.KVO(h.SKi),h.KVO(C.qQ))}}static{this.\u0275prov=h.jDH({token:c,factory:c.\u0275fac,providedIn:"root"})}}return c})();const Wt={dragStartThreshold:5,pointerDirectionChangeThreshold:5};let rt=(()=>{class c{constructor(e,n,r,t){this._document=e,this._ngZone=n,this._viewportRuler=r,this._dragDropRegistry=t}createDrag(e,n=Wt){return new Kt(e,n,this._document,this._ngZone,this._viewportRuler,this._dragDropRegistry)}createDropList(e){return new gt(e,this._dragDropRegistry,this._document,this._ngZone,this._viewportRuler)}static{this.\u0275fac=function(n){return new(n||c)(h.KVO(C.qQ),h.KVO(h.SKi),h.KVO(b.Xj),h.KVO(jt))}}static{this.\u0275prov=h.jDH({token:c,factory:c.\u0275fac,providedIn:"root"})}}return c})();const ft=new h.nKC("CDK_DRAG_PARENT"),mt=new h.nKC("CdkDragHandle");let xt=(()=>{class c{get disabled(){return this._disabled}set disabled(e){this._disabled=(0,D.he)(e),this._stateChanges.next(this)}constructor(e,n){this.element=e,this._stateChanges=new E.B,this._disabled=!1,this._parentDrag=n}ngOnDestroy(){this._stateChanges.complete()}static{this.\u0275fac=function(n){return new(n||c)(h.rXU(h.aKT),h.rXU(ft,12))}}static{this.\u0275dir=h.FsC({type:c,selectors:[["","cdkDragHandle",""]],hostAttrs:[1,"cdk-drag-handle"],inputs:{disabled:["cdkDragHandleDisabled","disabled"]},standalone:!0,features:[h.Jv_([{provide:mt,useExisting:c}])]})}}return c})();const vt=new h.nKC("CdkDragPlaceholder"),Mt=new h.nKC("CdkDragPreview"),at=new h.nKC("CDK_DRAG_CONFIG"),lt=new h.nKC("CdkDropList");let Lt=(()=>{class c{static{this._dragInstances=[]}get disabled(){return this._disabled||this.dropContainer&&this.dropContainer.disabled}set disabled(e){this._disabled=(0,D.he)(e),this._dragRef.disabled=this._disabled}constructor(e,n,r,t,o,s,a,l,d,u,g){this.element=e,this.dropContainer=n,this._ngZone=t,this._viewContainerRef=o,this._dir=a,this._changeDetectorRef=d,this._selfHandle=u,this._parentDrag=g,this._destroyed=new E.B,this.started=new h.bkB,this.released=new h.bkB,this.ended=new h.bkB,this.entered=new h.bkB,this.exited=new h.bkB,this.dropped=new h.bkB,this.moved=new p.c(v=>{const y=this._dragRef.moved.pipe((0,T.T)(O=>({source:this,pointerPosition:O.pointerPosition,event:O.event,delta:O.delta,distance:O.distance}))).subscribe(v);return()=>{y.unsubscribe()}}),this._dragRef=l.createDrag(e,{dragStartThreshold:s&&null!=s.dragStartThreshold?s.dragStartThreshold:5,pointerDirectionChangeThreshold:s&&null!=s.pointerDirectionChangeThreshold?s.pointerDirectionChangeThreshold:5,zIndex:s?.zIndex}),this._dragRef.data=this,c._dragInstances.push(this),s&&this._assignDefaults(s),n&&(this._dragRef._withDropContainer(n._dropListRef),n.addItem(this)),this._syncInputs(this._dragRef),this._handleEvents(this._dragRef)}getPlaceholderElement(){return this._dragRef.getPlaceholderElement()}getRootElement(){return this._dragRef.getRootElement()}reset(){this._dragRef.reset()}getFreeDragPosition(){return this._dragRef.getFreeDragPosition()}setFreeDragPosition(e){this._dragRef.setFreeDragPosition(e)}ngAfterViewInit(){this._ngZone.runOutsideAngular(()=>{this._ngZone.onStable.pipe((0,H.s)(1),(0,I.Q)(this._destroyed)).subscribe(()=>{this._updateRootElement(),this._setupHandlesListener(),this.freeDragPosition&&this._dragRef.setFreeDragPosition(this.freeDragPosition)})})}ngOnChanges(e){const n=e.rootElementSelector,r=e.freeDragPosition;n&&!n.firstChange&&this._updateRootElement(),r&&!r.firstChange&&this.freeDragPosition&&this._dragRef.setFreeDragPosition(this.freeDragPosition)}ngOnDestroy(){this.dropContainer&&this.dropContainer.removeItem(this);const e=c._dragInstances.indexOf(this);e>-1&&c._dragInstances.splice(e,1),this._ngZone.runOutsideAngular(()=>{this._destroyed.next(),this._destroyed.complete(),this._dragRef.dispose()})}_updateRootElement(){const e=this.element.nativeElement;let n=e;this.rootElementSelector&&(n=void 0!==e.closest?e.closest(this.rootElementSelector):e.parentElement?.closest(this.rootElementSelector)),this._dragRef.withRootElement(n||e)}_getBoundaryElement(){const e=this.boundaryElement;return e?"string"==typeof e?this.element.nativeElement.closest(e):(0,D.i8)(e):null}_syncInputs(e){e.beforeStarted.subscribe(()=>{if(!e.isDragging()){const n=this._dir,r=this.dragStartDelay,t=this._placeholderTemplate?{template:this._placeholderTemplate.templateRef,context:this._placeholderTemplate.data,viewContainer:this._viewContainerRef}:null,o=this._previewTemplate?{template:this._previewTemplate.templateRef,context:this._previewTemplate.data,matchSize:this._previewTemplate.matchSize,viewContainer:this._viewContainerRef}:null;e.disabled=this.disabled,e.lockAxis=this.lockAxis,e.dragStartDelay="object"==typeof r&&r?r:(0,D.OE)(r),e.constrainPosition=this.constrainPosition,e.previewClass=this.previewClass,e.withBoundaryElement(this._getBoundaryElement()).withPlaceholderTemplate(t).withPreviewTemplate(o).withPreviewContainer(this.previewContainer||"global"),n&&e.withDirection(n.value)}}),e.beforeStarted.pipe((0,H.s)(1)).subscribe(()=>{if(this._parentDrag)return void e.withParent(this._parentDrag._dragRef);let n=this.element.nativeElement.parentElement;for(;n;){if(n.classList.contains("cdk-drag")){e.withParent(c._dragInstances.find(r=>r.element.nativeElement===n)?._dragRef||null);break}n=n.parentElement}})}_handleEvents(e){e.started.subscribe(n=>{this.started.emit({source:this,event:n.event}),this._changeDetectorRef.markForCheck()}),e.released.subscribe(n=>{this.released.emit({source:this,event:n.event})}),e.ended.subscribe(n=>{this.ended.emit({source:this,distance:n.distance,dropPoint:n.dropPoint,event:n.event}),this._changeDetectorRef.markForCheck()}),e.entered.subscribe(n=>{this.entered.emit({container:n.container.data,item:this,currentIndex:n.currentIndex})}),e.exited.subscribe(n=>{this.exited.emit({container:n.container.data,item:this})}),e.dropped.subscribe(n=>{this.dropped.emit({previousIndex:n.previousIndex,currentIndex:n.currentIndex,previousContainer:n.previousContainer.data,container:n.container.data,isPointerOverContainer:n.isPointerOverContainer,item:this,distance:n.distance,dropPoint:n.dropPoint,event:n.event})})}_assignDefaults(e){const{lockAxis:n,dragStartDelay:r,constrainPosition:t,previewClass:o,boundaryElement:s,draggingDisabled:a,rootElementSelector:l,previewContainer:d}=e;this.disabled=a??!1,this.dragStartDelay=r||0,n&&(this.lockAxis=n),t&&(this.constrainPosition=t),o&&(this.previewClass=o),s&&(this.boundaryElement=s),l&&(this.rootElementSelector=l),d&&(this.previewContainer=d)}_setupHandlesListener(){this._handles.changes.pipe((0,tt.Z)(this._handles),(0,B.M)(e=>{const n=e.filter(r=>r._parentDrag===this).map(r=>r.element);this._selfHandle&&this.rootElementSelector&&n.push(this.element),this._dragRef.withHandles(n)}),(0,Et.n)(e=>(0,m.h)(...e.map(n=>n._stateChanges.pipe((0,tt.Z)(n))))),(0,I.Q)(this._destroyed)).subscribe(e=>{const n=this._dragRef,r=e.element.nativeElement;e.disabled?n.disableHandle(r):n.enableHandle(r)})}static{this.\u0275fac=function(n){return new(n||c)(h.rXU(h.aKT),h.rXU(lt,12),h.rXU(C.qQ),h.rXU(h.SKi),h.rXU(h.c1b),h.rXU(at,8),h.rXU(Dt.dS,8),h.rXU(rt),h.rXU(h.gRc),h.rXU(mt,10),h.rXU(ft,12))}}static{this.\u0275dir=h.FsC({type:c,selectors:[["","cdkDrag",""]],contentQueries:function(n,r,t){if(1&n&&(h.wni(t,Mt,5),h.wni(t,vt,5),h.wni(t,mt,5)),2&n){let o;h.mGM(o=h.lsd())&&(r._previewTemplate=o.first),h.mGM(o=h.lsd())&&(r._placeholderTemplate=o.first),h.mGM(o=h.lsd())&&(r._handles=o)}},hostAttrs:[1,"cdk-drag"],hostVars:4,hostBindings:function(n,r){2&n&&h.AVh("cdk-drag-disabled",r.disabled)("cdk-drag-dragging",r._dragRef.isDragging())},inputs:{data:["cdkDragData","data"],lockAxis:["cdkDragLockAxis","lockAxis"],rootElementSelector:["cdkDragRootElement","rootElementSelector"],boundaryElement:["cdkDragBoundary","boundaryElement"],dragStartDelay:["cdkDragStartDelay","dragStartDelay"],freeDragPosition:["cdkDragFreeDragPosition","freeDragPosition"],disabled:["cdkDragDisabled","disabled"],constrainPosition:["cdkDragConstrainPosition","constrainPosition"],previewClass:["cdkDragPreviewClass","previewClass"],previewContainer:["cdkDragPreviewContainer","previewContainer"]},outputs:{started:"cdkDragStarted",released:"cdkDragReleased",ended:"cdkDragEnded",entered:"cdkDragEntered",exited:"cdkDragExited",dropped:"cdkDragDropped",moved:"cdkDragMoved"},exportAs:["cdkDrag"],standalone:!0,features:[h.Jv_([{provide:ft,useExisting:c}]),h.OA$]})}}return c})();const q=new h.nKC("CdkDropListGroup");let Xt=0,Ut=(()=>{class c{static{this._dropLists=[]}get disabled(){return this._disabled||!!this._group&&this._group.disabled}set disabled(e){this._dropListRef.disabled=this._disabled=(0,D.he)(e)}constructor(e,n,r,t,o,s,a){this.element=e,this._changeDetectorRef=r,this._scrollDispatcher=t,this._dir=o,this._group=s,this._destroyed=new E.B,this.connectedTo=[],this.id="cdk-drop-list-"+Xt++,this.enterPredicate=()=>!0,this.sortPredicate=()=>!0,this.dropped=new h.bkB,this.entered=new h.bkB,this.exited=new h.bkB,this.sorted=new h.bkB,this._unsortedItems=new Set,this._dropListRef=n.createDropList(e),this._dropListRef.data=this,a&&this._assignDefaults(a),this._dropListRef.enterPredicate=(l,d)=>this.enterPredicate(l.data,d.data),this._dropListRef.sortPredicate=(l,d,u)=>this.sortPredicate(l,d.data,u.data),this._setupInputSyncSubscription(this._dropListRef),this._handleEvents(this._dropListRef),c._dropLists.push(this),s&&s._items.add(this)}addItem(e){this._unsortedItems.add(e),this._dropListRef.isDragging()&&this._syncItemsWithRef()}removeItem(e){this._unsortedItems.delete(e),this._dropListRef.isDragging()&&this._syncItemsWithRef()}getSortedItems(){return Array.from(this._unsortedItems).sort((e,n)=>e._dragRef.getVisibleElement().compareDocumentPosition(n._dragRef.getVisibleElement())&Node.DOCUMENT_POSITION_FOLLOWING?-1:1)}ngOnDestroy(){const e=c._dropLists.indexOf(this);e>-1&&c._dropLists.splice(e,1),this._group&&this._group._items.delete(this),this._unsortedItems.clear(),this._dropListRef.dispose(),this._destroyed.next(),this._destroyed.complete()}_setupInputSyncSubscription(e){this._dir&&this._dir.change.pipe((0,tt.Z)(this._dir.value),(0,I.Q)(this._destroyed)).subscribe(n=>e.withDirection(n)),e.beforeStarted.subscribe(()=>{const n=(0,D.FG)(this.connectedTo).map(r=>"string"==typeof r?c._dropLists.find(o=>o.id===r):r);if(this._group&&this._group._items.forEach(r=>{-1===n.indexOf(r)&&n.push(r)}),!this._scrollableParentsResolved){const r=this._scrollDispatcher.getAncestorScrollContainers(this.element).map(t=>t.getElementRef().nativeElement);this._dropListRef.withScrollableParents(r),this._scrollableParentsResolved=!0}e.disabled=this.disabled,e.lockAxis=this.lockAxis,e.sortingDisabled=(0,D.he)(this.sortingDisabled),e.autoScrollDisabled=(0,D.he)(this.autoScrollDisabled),e.autoScrollStep=(0,D.OE)(this.autoScrollStep,2),e.connectedTo(n.filter(r=>r&&r!==this).map(r=>r._dropListRef)).withOrientation(this.orientation)})}_handleEvents(e){e.beforeStarted.subscribe(()=>{this._syncItemsWithRef(),this._changeDetectorRef.markForCheck()}),e.entered.subscribe(n=>{this.entered.emit({container:this,item:n.item.data,currentIndex:n.currentIndex})}),e.exited.subscribe(n=>{this.exited.emit({container:this,item:n.item.data}),this._changeDetectorRef.markForCheck()}),e.sorted.subscribe(n=>{this.sorted.emit({previousIndex:n.previousIndex,currentIndex:n.currentIndex,container:this,item:n.item.data})}),e.dropped.subscribe(n=>{this.dropped.emit({previousIndex:n.previousIndex,currentIndex:n.currentIndex,previousContainer:n.previousContainer.data,container:n.container.data,item:n.item.data,isPointerOverContainer:n.isPointerOverContainer,distance:n.distance,dropPoint:n.dropPoint,event:n.event}),this._changeDetectorRef.markForCheck()}),(0,m.h)(e.receivingStarted,e.receivingStopped).subscribe(()=>this._changeDetectorRef.markForCheck())}_assignDefaults(e){const{lockAxis:n,draggingDisabled:r,sortingDisabled:t,listAutoScrollDisabled:o,listOrientation:s}=e;this.disabled=r??!1,this.sortingDisabled=t??!1,this.autoScrollDisabled=o??!1,this.orientation=s||"vertical",n&&(this.lockAxis=n)}_syncItemsWithRef(){this._dropListRef.withItems(this.getSortedItems().map(e=>e._dragRef))}static{this.\u0275fac=function(n){return new(n||c)(h.rXU(h.aKT),h.rXU(rt),h.rXU(h.gRc),h.rXU(b.R),h.rXU(Dt.dS,8),h.rXU(q,12),h.rXU(at,8))}}static{this.\u0275dir=h.FsC({type:c,selectors:[["","cdkDropList",""],["cdk-drop-list"]],hostAttrs:[1,"cdk-drop-list"],hostVars:7,hostBindings:function(n,r){2&n&&(h.BMQ("id",r.id),h.AVh("cdk-drop-list-disabled",r.disabled)("cdk-drop-list-dragging",r._dropListRef.isDragging())("cdk-drop-list-receiving",r._dropListRef.isReceiving()))},inputs:{connectedTo:["cdkDropListConnectedTo","connectedTo"],data:["cdkDropListData","data"],orientation:["cdkDropListOrientation","orientation"],id:"id",lockAxis:["cdkDropListLockAxis","lockAxis"],disabled:["cdkDropListDisabled","disabled"],sortingDisabled:["cdkDropListSortingDisabled","sortingDisabled"],enterPredicate:["cdkDropListEnterPredicate","enterPredicate"],sortPredicate:["cdkDropListSortPredicate","sortPredicate"],autoScrollDisabled:["cdkDropListAutoScrollDisabled","autoScrollDisabled"],autoScrollStep:["cdkDropListAutoScrollStep","autoScrollStep"]},outputs:{dropped:"cdkDropListDropped",entered:"cdkDropListEntered",exited:"cdkDropListExited",sorted:"cdkDropListSorted"},exportAs:["cdkDropList"],standalone:!0,features:[h.Jv_([{provide:q,useValue:void 0},{provide:lt,useExisting:c}])]})}}return c})(),J=(()=>{class c{static{this.\u0275fac=function(n){return new(n||c)}}static{this.\u0275mod=h.$C({type:c})}static{this.\u0275inj=h.G2t({providers:[rt],imports:[b.Gj]})}}return c})()}}]);