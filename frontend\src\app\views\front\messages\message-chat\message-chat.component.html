<!-- ===== MESSAGE CHAT COMPONENT - REORGANIZED & OPTIMIZED ===== -->

<!-- Éléments vidéo pour WebRTC - cachés visuellement mais audio actif -->
<video
  #localVideo
  autoplay
  muted
  playsinline
  style="
    position: absolute;
    top: -9999px;
    left: -9999px;
    width: 1px;
    height: 1px;
  "
></video>
<video
  #remoteVideo
  autoplay
  playsinline
  style="
    position: absolute;
    top: -9999px;
    left: -9999px;
    width: 1px;
    height: 1px;
  "
></video>

<div
  style="
    display: flex;
    flex-direction: column;
    height: 100vh;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    color: #1f2937;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  "
>
  <!-- ===== ANIMATIONS CSS ===== -->
  <style>
    @keyframes pulse {
      0%,
      100% {
        opacity: 1;
      }
      50% {
        opacity: 0.5;
      }
    }
    @keyframes bounce {
      0%,
      20%,
      53%,
      80%,
      100% {
        transform: translateY(0);
      }
      40%,
      43% {
        transform: translateY(-8px);
      }
      70% {
        transform: translateY(-4px);
      }
    }
    @keyframes spin {
      from {
        transform: rotate(0deg);
      }
      to {
        transform: rotate(360deg);
      }
    }
    @keyframes ping {
      75%,
      100% {
        transform: scale(2);
        opacity: 0;
      }
    }
    @keyframes slideInUp {
      from {
        transform: translateX(-50%) translateY(20px);
        opacity: 0;
      }
      to {
        transform: translateX(-50%) translateY(0);
        opacity: 1;
      }
    }
  </style>

  <!-- ===== HEADER SECTION ===== -->
  <header
    style="
      display: flex;
      align-items: center;
      padding: 12px 16px;
      background: #ffffff;
      border-bottom: 1px solid #e5e7eb;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      position: relative;
      z-index: 10;
    "
  >
    <!-- Bouton retour -->
    <button
      (click)="goBackToConversations()"
      style="
        padding: 10px;
        margin-right: 12px;
        border-radius: 50%;
        border: none;
        background: transparent;
        cursor: pointer;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        min-width: 40px;
        min-height: 40px;
      "
      onmouseover="this.style.background='#f3f4f6'; this.style.transform='scale(1.05)'"
      onmouseout="this.style.background='transparent'; this.style.transform='scale(1)'"
      title="Retour aux conversations"
    >
      <i
        class="fas fa-arrow-left"
        style="color: #374151; font-size: 18px; font-weight: bold"
      ></i>
    </button>

    <!-- Info utilisateur -->
    <div style="display: flex; align-items: center; flex: 1; min-width: 0">
      <!-- Avatar avec statut -->
      <div style="position: relative; margin-right: 12px">
        <img
          [src]="otherParticipant?.image || 'assets/images/default-avatar.png'"
          [alt]="otherParticipant?.username"
          style="
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;
            border: 2px solid transparent;
            cursor: pointer;
            transition: transform 0.2s ease;
          "
          (click)="openUserProfile(otherParticipant?.id!)"
          onmouseover="this.style.transform='scale(1.05)'"
          onmouseout="this.style.transform='scale(1)'"
          title="Voir le profil"
        />
        <!-- Indicateur en ligne -->
        <div
          *ngIf="otherParticipant?.isOnline"
          style="
            position: absolute;
            bottom: 0;
            right: 0;
            width: 12px;
            height: 12px;
            background: #10b981;
            border: 2px solid transparent;
            border-radius: 50%;
            animation: pulse 2s infinite;
          "
        ></div>
      </div>

      <!-- Nom et statut -->
      <div style="flex: 1; min-width: 0">
        <h3
          style="
            font-weight: 600;
            color: #111827;
            margin: 0;
            font-size: 16px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          "
        >
          {{ otherParticipant?.username || "Utilisateur" }}
        </h3>
        <div style="font-size: 14px; color: #6b7280; margin-top: 2px">
          <!-- Indicateur de frappe -->
          <div
            *ngIf="isUserTyping"
            style="display: flex; align-items: center; gap: 4px; color: #10b981"
          >
            <span>En train d'écrire</span>
            <div style="display: flex; gap: 2px">
              <div
                style="
                  width: 4px;
                  height: 4px;
                  background: #10b981;
                  border-radius: 50%;
                  animation: bounce 1s infinite;
                "
              ></div>
              <div
                style="
                  width: 4px;
                  height: 4px;
                  background: #10b981;
                  border-radius: 50%;
                  animation: bounce 1s infinite 0.1s;
                "
              ></div>
              <div
                style="
                  width: 4px;
                  height: 4px;
                  background: #10b981;
                  border-radius: 50%;
                  animation: bounce 1s infinite 0.2s;
                "
              ></div>
            </div>
          </div>
          <!-- Statut en ligne -->
          <span *ngIf="!isUserTyping">
            {{
              otherParticipant?.isOnline
                ? "En ligne"
                : formatLastActive(otherParticipant?.lastActive)
            }}
          </span>
        </div>
      </div>
    </div>

    <!-- Actions -->
    <div style="display: flex; align-items: center; gap: 8px">
      <!-- Appel vidéo -->
      <button
        (click)="startVideoCall()"
        style="
          padding: 10px;
          border-radius: 50%;
          border: none;
          background: linear-gradient(135deg, #3b82f6, #1d4ed8);
          color: white;
          cursor: pointer;
          transition: all 0.3s;
          box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
          width: 40px;
          height: 40px;
          display: flex;
          align-items: center;
          justify-content: center;
        "
        title="Appel vidéo"
        onmouseover="this.style.transform='scale(1.1)'; this.style.boxShadow='0 6px 20px rgba(59, 130, 246, 0.4)'"
        onmouseout="this.style.transform='scale(1)'; this.style.boxShadow='0 4px 12px rgba(59, 130, 246, 0.3)'"
      >
        <i class="fas fa-video" style="font-size: 14px"></i>
      </button>

      <!-- Appel vocal -->
      <button
        (click)="startVoiceCall()"
        style="
          padding: 10px;
          border-radius: 50%;
          border: none;
          background: linear-gradient(135deg, #10b981, #047857);
          color: white;
          cursor: pointer;
          transition: all 0.3s;
          box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
          width: 40px;
          height: 40px;
          display: flex;
          align-items: center;
          justify-content: center;
        "
        title="Appel vocal"
        onmouseover="this.style.transform='scale(1.1)'; this.style.boxShadow='0 6px 20px rgba(16, 185, 129, 0.4)'"
        onmouseout="this.style.transform='scale(1)'; this.style.boxShadow='0 4px 12px rgba(16, 185, 129, 0.3)'"
      >
        <i class="fas fa-phone" style="font-size: 14px"></i>
      </button>

      <!-- Recherche -->
      <button
        (click)="toggleSearch()"
        style="
          padding: 8px;
          border-radius: 50%;
          border: none;
          background: transparent;
          color: #6b7280;
          cursor: pointer;
          transition: all 0.2s;
        "
        [style.background]="searchMode ? '#dcfce7' : 'transparent'"
        [style.color]="searchMode ? '#16a34a' : '#6b7280'"
        title="Rechercher"
      >
        <i class="fas fa-search"></i>
      </button>

      <!-- Rechargement -->
      <button
        (click)="reloadConversation()"
        style="
          padding: 8px;
          border-radius: 50%;
          border: none;
          background: transparent;
          color: #6b7280;
          cursor: pointer;
          transition: all 0.2s;
        "
        [style.opacity]="isLoading ? '0.5' : '1'"
        [disabled]="isLoading"
        title="Recharger la conversation"
        onmouseover="this.style.background='#f3f4f6'; this.style.color='#374151'"
        onmouseout="this.style.background='transparent'; this.style.color='#6b7280'"
      >
        <i
          class="fas fa-sync-alt"
          [style.animation]="isLoading ? 'spin 1s linear infinite' : 'none'"
        ></i>
      </button>

      <!-- Menu principal -->
      <button
        (click)="toggleMainMenu()"
        style="
          padding: 8px;
          border-radius: 50%;
          border: none;
          background: transparent;
          color: #6b7280;
          cursor: pointer;
          transition: all 0.2s;
          position: relative;
        "
        [style.background]="showMainMenu ? '#dcfce7' : 'transparent'"
        [style.color]="showMainMenu ? '#16a34a' : '#6b7280'"
        title="Menu"
      >
        <i class="fas fa-ellipsis-v"></i>
      </button>
    </div>

    <!-- Menu dropdown -->
    <div
      *ngIf="showMainMenu"
      style="
        position: absolute;
        top: 64px;
        right: 16px;
        background: #ffffff;
        border-radius: 16px;
        box-shadow: 0 20px 25px rgba(0, 0, 0, 0.1);
        border: 1px solid #e5e7eb;
        z-index: 50;
        min-width: 192px;
      "
    >
      <div style="padding: 8px">
        <button
          (click)="toggleSearch(); showMainMenu = false"
          style="
            width: 100%;
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 8px 12px;
            border-radius: 8px;
            border: none;
            background: transparent;
            cursor: pointer;
            transition: all 0.2s;
            text-align: left;
          "
          onmouseover="this.style.background='#f3f4f6'"
          onmouseout="this.style.background='transparent'"
        >
          <i class="fas fa-search" style="color: #3b82f6"></i>
          <span style="color: #374151">Rechercher</span>
        </button>
        <button
          style="
            width: 100%;
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 8px 12px;
            border-radius: 8px;
            border: none;
            background: transparent;
            cursor: pointer;
            transition: all 0.2s;
            text-align: left;
          "
          onmouseover="this.style.background='#f3f4f6'"
          onmouseout="this.style.background='transparent'"
        >
          <i class="fas fa-user" style="color: #10b981"></i>
          <span style="color: #374151">Voir le profil</span>
        </button>
        <hr style="margin: 8px 0; border-color: #e5e7eb" />
        <button
          style="
            width: 100%;
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 8px 12px;
            border-radius: 8px;
            border: none;
            background: transparent;
            cursor: pointer;
            transition: all 0.2s;
            text-align: left;
          "
          onmouseover="this.style.background='#f3f4f6'"
          onmouseout="this.style.background='transparent'"
        >
          <i class="fas fa-cog" style="color: #6b7280"></i>
          <span style="color: #374151">Paramètres</span>
        </button>
      </div>
    </div>
  </header>

  <!-- ===== MAIN MESSAGES SECTION ===== -->
  <main
    style="flex: 1; overflow-y: auto; padding: 16px; position: relative"
    #messagesContainer
    (scroll)="onScroll($event)"
    (dragover)="onDragOver($event)"
    (dragleave)="onDragLeave($event)"
    (drop)="onDrop($event)"
    [style.background]="isDragOver ? 'rgba(34, 197, 94, 0.1)' : 'transparent'"
  >
    <!-- Drag & Drop Overlay -->
    <div
      *ngIf="isDragOver"
      style="
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(34, 197, 94, 0.2);
        border: 2px dashed transparent;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 50;
        backdrop-filter: blur(2px);
        animation: pulse 2s infinite;
      "
    >
      <div
        style="
          text-align: center;
          background: #ffffff;
          padding: 24px;
          border-radius: 12px;
          box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
          border: 1px solid transparent;
        "
      >
        <i
          class="fas fa-cloud-upload-alt"
          style="
            font-size: 48px;
            color: #10b981;
            margin-bottom: 12px;
            animation: bounce 1s infinite;
          "
        ></i>
        <p
          style="
            font-size: 20px;
            font-weight: bold;
            color: #047857;
            margin-bottom: 8px;
          "
        >
          Déposez vos fichiers ici
        </p>
        <p style="font-size: 14px; color: #10b981">
          Images, vidéos, documents...
        </p>
      </div>
    </div>

    <!-- Loading State -->
    <div
      *ngIf="isLoading"
      style="
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 32px 0;
      "
    >
      <div
        style="
          width: 32px;
          height: 32px;
          border: 2px solid #e5e7eb;
          border-bottom-color: #10b981;
          border-radius: 50%;
          animation: spin 1s linear infinite;
          margin-bottom: 16px;
        "
      ></div>
      <span style="color: #6b7280">Chargement des messages...</span>
    </div>

    <!-- Empty State -->
    <div
      *ngIf="!isLoading && messages.length === 0"
      style="
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 64px 0;
      "
    >
      <div style="font-size: 64px; color: #d1d5db; margin-bottom: 16px">
        <i class="fas fa-comments"></i>
      </div>
      <h3
        style="
          font-size: 20px;
          font-weight: 600;
          color: #374151;
          margin-bottom: 8px;
        "
      >
        Aucun message
      </h3>
      <p style="color: #6b7280; text-align: center">
        Commencez votre conversation avec {{ otherParticipant?.username }}
      </p>
    </div>

    <!-- Messages List -->
    <div
      *ngIf="!isLoading && messages.length > 0"
      style="display: flex; flex-direction: column; gap: 8px"
    >
      <ng-container
        *ngFor="
          let message of messages;
          let i = index;
          trackBy: trackByMessageId
        "
      >
        <!-- Date Separator -->
        <div
          *ngIf="shouldShowDateSeparator(i)"
          style="display: flex; justify-content: center; margin: 16px 0"
        >
          <div
            style="
              background: #ffffff;
              padding: 4px 12px;
              border-radius: 20px;
              box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            "
          >
            <span style="font-size: 12px; color: #6b7280">
              {{ formatDateSeparator(message.timestamp) }}
            </span>
          </div>
        </div>

        <!-- Message Container -->
        <div
          style="display: flex"
          [style.justify-content]="
            message.sender?.id === currentUserId ? 'flex-end' : 'flex-start'
          "
          [id]="'message-' + message.id"
          (click)="onMessageClick(message, $event)"
          (contextmenu)="onMessageContextMenu(message, $event)"
        >
          <!-- Avatar for others -->
          <div
            *ngIf="message.sender?.id !== currentUserId && shouldShowAvatar(i)"
            style="margin-right: 8px; flex-shrink: 0"
          >
            <img
              [src]="
                message.sender?.image || 'assets/images/default-avatar.png'
              "
              [alt]="message.sender?.username"
              style="
                width: 32px;
                height: 32px;
                border-radius: 50%;
                object-fit: cover;
                cursor: pointer;
                transition: transform 0.2s;
              "
              (click)="openUserProfile(message.sender?.id!)"
              onmouseover="this.style.transform='scale(1.05)'"
              onmouseout="this.style.transform='scale(1)'"
            />
          </div>

          <!-- Message Bubble -->
          <div
            [style.background-color]="
              message.sender?.id === currentUserId ? '#3b82f6' : '#ffffff'
            "
            [style.color]="
              message.sender?.id === currentUserId ? '#ffffff' : '#111827'
            "
            style="
              max-width: 320px;
              padding: 12px 16px;
              border-radius: 18px;
              box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
              position: relative;
              word-wrap: break-word;
              overflow-wrap: break-word;
              border: none;
            "
          >
            <!-- Sender Name (for groups) -->
            <div
              *ngIf="
                isGroupConversation() &&
                message.sender?.id !== currentUserId &&
                shouldShowSenderName(i)
              "
              style="
                font-size: 12px;
                font-weight: 600;
                margin-bottom: 4px;
                opacity: 0.75;
              "
              [style.color]="getUserColor(message.sender?.id!)"
            >
              {{ message.sender?.username }}
            </div>

            <!-- Text Content -->
            <div
              *ngIf="getMessageType(message) === 'text'"
              style="word-wrap: break-word; overflow-wrap: break-word"
            >
              <div [innerHTML]="formatMessageContent(message.content)"></div>
            </div>

            <!-- Image Content -->
            <div *ngIf="hasImage(message)" style="margin: 8px 0">
              <img
                [src]="getImageUrl(message)"
                [alt]="message.content || 'Image'"
                (click)="openImageViewer(message)"
                (load)="onImageLoad($event, message)"
                (error)="onImageError($event, message)"
                style="
                  max-width: 280px;
                  height: auto;
                  border-radius: 12px;
                  cursor: pointer;
                  transition: transform 0.2s;
                "
                onmouseover="this.style.transform='scale(1.02)'"
                onmouseout="this.style.transform='scale(1)'"
              />
              <!-- Image Caption -->
              <div
                *ngIf="message.content"
                [style.color]="
                  message.sender?.id === currentUserId ? '#ffffff' : '#111827'
                "
                style="font-size: 14px; margin-top: 8px; line-height: 1.4"
                [innerHTML]="formatMessageContent(message.content)"
              ></div>
            </div>

            <!-- Voice Message Content -->
            <div
              *ngIf="getMessageType(message) === 'audio'"
              style="
                display: flex;
                align-items: center;
                gap: 12px;
                padding: 12px;
                background: rgba(255, 255, 255, 0.1);
                border-radius: 12px;
                margin: 8px 0;
                min-width: 200px;
                max-width: 280px;
              "
            >
              <!-- Bouton Play/Pause -->
              <button
                (click)="toggleVoicePlayback(message)"
                style="
                  width: 40px;
                  height: 40px;
                  border-radius: 50%;
                  border: none;
                  background: rgba(255, 255, 255, 0.2);
                  color: inherit;
                  cursor: pointer;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  transition: all 0.2s;
                  flex-shrink: 0;
                "
                onmouseover="this.style.background='rgba(255, 255, 255, 0.3)'"
                onmouseout="this.style.background='rgba(255, 255, 255, 0.2)'"
                title="Lire/Pause"
              >
                <i
                  [class]="
                    isVoicePlaying(message.id) ? 'fas fa-pause' : 'fas fa-play'
                  "
                  style="font-size: 14px"
                ></i>
              </button>

              <!-- Visualisation des ondes sonores -->
              <div
                style="
                  flex: 1;
                  display: flex;
                  align-items: center;
                  gap: 2px;
                  height: 24px;
                  overflow: hidden;
                "
              >
                <div
                  *ngFor="let wave of voiceWaves; let i = index"
                  style="
                    width: 3px;
                    background: currentColor;
                    border-radius: 2px;
                    opacity: 0.7;
                    transition: height 0.3s ease;
                  "
                  [style.height.px]="isVoicePlaying(message.id) ? wave : 8"
                  [style.animation]="
                    isVoicePlaying(message.id) ? 'pulse 1s infinite' : 'none'
                  "
                  [style.animation-delay]="i * 0.1 + 's'"
                ></div>
              </div>

              <!-- Durée et contrôles -->
              <div
                style="
                  display: flex;
                  align-items: center;
                  gap: 8px;
                  flex-shrink: 0;
                "
              >
                <!-- Durée -->
                <div
                  style="
                    font-size: 12px;
                    opacity: 0.8;
                    min-width: 40px;
                    text-align: right;
                  "
                >
                  {{ getVoiceDuration(message) }}
                </div>

                <!-- Vitesse de lecture (si en cours de lecture) -->
                <button
                  *ngIf="isVoicePlaying(message.id)"
                  (click)="changeVoiceSpeed(message)"
                  style="
                    padding: 4px 8px;
                    border-radius: 12px;
                    border: none;
                    background: rgba(255, 255, 255, 0.2);
                    color: inherit;
                    cursor: pointer;
                    font-size: 11px;
                    transition: all 0.2s;
                  "
                  onmouseover="this.style.background='rgba(255, 255, 255, 0.3)'"
                  onmouseout="this.style.background='rgba(255, 255, 255, 0.2)'"
                  title="Changer la vitesse"
                >
                  {{ getVoiceSpeed(message) }}x
                </button>
              </div>
            </div>

            <!-- Message Metadata -->
            <div
              style="
                display: flex;
                align-items: center;
                justify-content: flex-end;
                gap: 4px;
                margin-top: 4px;
                font-size: 12px;
                opacity: 0.75;
              "
            >
              <span>{{ formatMessageTime(message.timestamp) }}</span>
              <div
                *ngIf="message.sender?.id === currentUserId"
                style="display: flex; align-items: center"
              >
                <i
                  class="fas fa-clock"
                  *ngIf="message.status === 'SENDING'"
                  title="Envoi en cours"
                ></i>
                <i
                  class="fas fa-check"
                  *ngIf="message.status === 'SENT'"
                  title="Envoyé"
                ></i>
                <i
                  class="fas fa-check-double"
                  *ngIf="message.status === 'DELIVERED'"
                  title="Livré"
                ></i>
                <i
                  class="fas fa-check-double"
                  style="color: #3b82f6"
                  *ngIf="message.status === 'READ'"
                  title="Lu"
                ></i>
              </div>
            </div>
          </div>
        </div>
      </ng-container>

      <!-- Typing Indicator -->
      <div
        *ngIf="otherUserIsTyping"
        style="display: flex; align-items: start; gap: 8px"
      >
        <img
          [src]="otherParticipant?.image || 'assets/images/default-avatar.png'"
          [alt]="otherParticipant?.username"
          style="
            width: 32px;
            height: 32px;
            border-radius: 50%;
            object-fit: cover;
          "
        />
        <div
          style="
            background: #ffffff;
            padding: 12px 16px;
            border-radius: 18px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          "
        >
          <div style="display: flex; gap: 4px">
            <div
              style="
                width: 8px;
                height: 8px;
                background: #6b7280;
                border-radius: 50%;
                animation: bounce 1s infinite;
              "
            ></div>
            <div
              style="
                width: 8px;
                height: 8px;
                background: #6b7280;
                border-radius: 50%;
                animation: bounce 1s infinite 0.1s;
              "
            ></div>
            <div
              style="
                width: 8px;
                height: 8px;
                background: #6b7280;
                border-radius: 50%;
                animation: bounce 1s infinite 0.2s;
              "
            ></div>
          </div>
        </div>
      </div>
    </div>
  </main>

  <!-- ===== FOOTER INPUT SECTION ===== -->
  <footer
    style="background: #ffffff; border-top: 1px solid #e5e7eb; padding: 16px"
  >
    <form
      [formGroup]="messageForm"
      (ngSubmit)="sendMessage()"
      style="display: flex; align-items: end; gap: 12px"
    >
      <!-- Left Actions -->
      <div style="display: flex; gap: 8px">
        <!-- Emoji Button -->
        <button
          type="button"
          (click)="toggleEmojiPicker()"
          style="
            padding: 8px;
            border-radius: 50%;
            border: none;
            background: transparent;
            color: #6b7280;
            cursor: pointer;
            transition: all 0.2s;
          "
          [style.background]="showEmojiPicker ? '#dcfce7' : 'transparent'"
          [style.color]="showEmojiPicker ? '#16a34a' : '#6b7280'"
          title="Émojis"
          onmouseover="this.style.background=this.style.background === 'transparent' ? '#f3f4f6' : this.style.background"
          onmouseout="this.style.background=this.style.background === '#f3f4f6' ? 'transparent' : this.style.background"
        >
          <i class="fas fa-smile"></i>
        </button>

        <!-- Attachment Button -->
        <button
          type="button"
          (click)="toggleAttachmentMenu()"
          style="
            padding: 8px;
            border-radius: 50%;
            border: none;
            background: transparent;
            color: #6b7280;
            cursor: pointer;
            transition: all 0.2s;
          "
          [style.background]="showAttachmentMenu ? '#dcfce7' : 'transparent'"
          [style.color]="showAttachmentMenu ? '#16a34a' : '#6b7280'"
          title="Pièces jointes"
          onmouseover="this.style.background=this.style.background === 'transparent' ? '#f3f4f6' : this.style.background"
          onmouseout="this.style.background=this.style.background === '#f3f4f6' ? 'transparent' : this.style.background"
        >
          <i class="fas fa-paperclip"></i>
        </button>

        <!-- Voice Recording Button -->
        <button
          type="button"
          (mousedown)="onRecordStart($event)"
          (mouseup)="onRecordEnd($event)"
          (mouseleave)="onRecordCancel($event)"
          (touchstart)="onRecordStart($event)"
          (touchend)="onRecordEnd($event)"
          (touchcancel)="onRecordCancel($event)"
          style="
            padding: 8px;
            border-radius: 50%;
            border: none;
            background: transparent;
            color: #6b7280;
            cursor: pointer;
            transition: all 0.2s;
            position: relative;
          "
          [style.background]="isRecordingVoice ? '#fef3c7' : 'transparent'"
          [style.color]="isRecordingVoice ? '#f59e0b' : '#6b7280'"
          [style.transform]="isRecordingVoice ? 'scale(1.1)' : 'scale(1)'"
          title="Maintenir pour enregistrer un message vocal"
          onmouseover="if(!this.style.background || this.style.background === 'transparent') this.style.background='#f3f4f6'"
          onmouseout="if(this.style.background === '#f3f4f6') this.style.background='transparent'"
        >
          <i
            [class]="isRecordingVoice ? 'fas fa-stop' : 'fas fa-microphone'"
            [style.animation]="isRecordingVoice ? 'pulse 1s infinite' : 'none'"
          ></i>

          <!-- Indicateur d'enregistrement -->
          <div
            *ngIf="isRecordingVoice"
            style="
              position: absolute;
              top: -2px;
              right: -2px;
              width: 8px;
              height: 8px;
              background: #ef4444;
              border-radius: 50%;
              animation: ping 1s infinite;
            "
          ></div>
        </button>
      </div>

      <!-- Message Input -->
      <div style="flex: 1; position: relative">
        <textarea
          formControlName="content"
          placeholder="Tapez votre message..."
          (keydown)="onInputKeyDown($event)"
          (input)="onInputChange($event)"
          (focus)="onInputFocus()"
          style="
            width: 100%;
            min-height: 44px;
            max-height: 120px;
            padding: 12px 16px;
            border: 1px solid #e5e7eb;
            border-radius: 22px;
            resize: none;
            outline: none;
            font-family: inherit;
            font-size: 14px;
            line-height: 1.4;
            background: #ffffff;
            color: #111827;
            transition: all 0.2s;
          "
          [disabled]="isInputDisabled()"
        ></textarea>
      </div>

      <!-- Send Button -->
      <button
        type="submit"
        [disabled]="!messageForm.valid || isSendingMessage"
        style="
          padding: 12px;
          border-radius: 50%;
          border: none;
          background: #3b82f6;
          color: #ffffff;
          cursor: pointer;
          transition: all 0.2s;
          display: flex;
          align-items: center;
          justify-content: center;
          min-width: 44px;
          min-height: 44px;
        "
        [style.background]="
          !messageForm.valid || isSendingMessage ? '#9ca3af' : '#3b82f6'
        "
        [style.cursor]="
          !messageForm.valid || isSendingMessage ? 'not-allowed' : 'pointer'
        "
        title="Envoyer"
        onmouseover="if(!this.disabled) this.style.background='#2563eb'"
        onmouseout="if(!this.disabled) this.style.background='#3b82f6'"
      >
        <i class="fas fa-paper-plane" *ngIf="!isSendingMessage"></i>
        <div
          *ngIf="isSendingMessage"
          style="
            width: 16px;
            height: 16px;
            border: 2px solid #ffffff;
            border-top-color: transparent;
            border-radius: 50%;
            animation: spin 1s linear infinite;
          "
        ></div>
      </button>
    </form>

    <!-- Emoji Picker -->
    <div
      *ngIf="showEmojiPicker"
      style="
        position: absolute;
        bottom: 80px;
        left: 16px;
        background: #ffffff;
        border-radius: 16px;
        box-shadow: 0 20px 25px rgba(0, 0, 0, 0.1);
        border: 1px solid #e5e7eb;
        z-index: 50;
        width: 320px;
        max-height: 300px;
        overflow-y: auto;
      "
    >
      <div style="padding: 16px">
        <h4
          style="
            margin: 0 0 12px 0;
            font-size: 14px;
            font-weight: 600;
            color: #374151;
          "
        >
          Émojis
        </h4>
        <div
          style="display: grid; grid-template-columns: repeat(8, 1fr); gap: 8px"
        >
          <button
            *ngFor="let emoji of getEmojisForCategory(selectedEmojiCategory)"
            (click)="insertEmoji(emoji)"
            style="
              padding: 8px;
              border: none;
              background: transparent;
              border-radius: 8px;
              cursor: pointer;
              font-size: 20px;
              transition: all 0.2s;
            "
            onmouseover="this.style.background='#f3f4f6'"
            onmouseout="this.style.background='transparent'"
            [title]="emoji.name"
          >
            {{ emoji.emoji }}
          </button>
        </div>
      </div>
    </div>

    <!-- Attachment Menu -->
    <div
      *ngIf="showAttachmentMenu"
      style="
        position: absolute;
        bottom: 80px;
        left: 60px;
        background: #ffffff;
        border-radius: 16px;
        box-shadow: 0 20px 25px rgba(0, 0, 0, 0.1);
        border: 1px solid #e5e7eb;
        z-index: 50;
        min-width: 200px;
      "
    >
      <div style="padding: 16px">
        <h4
          style="
            margin: 0 0 12px 0;
            font-size: 14px;
            font-weight: 600;
            color: #374151;
          "
        >
          Pièces jointes
        </h4>
        <div
          style="
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
          "
        >
          <!-- Images -->
          <button
            (click)="triggerFileInput('image')"
            style="
              display: flex;
              flex-direction: column;
              align-items: center;
              gap: 8px;
              padding: 16px;
              border: none;
              background: transparent;
              border-radius: 12px;
              cursor: pointer;
              transition: all 0.2s;
            "
            onmouseover="this.style.background='#f3f4f6'"
            onmouseout="this.style.background='transparent'"
          >
            <div
              style="
                width: 48px;
                height: 48px;
                background: #dbeafe;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
              "
            >
              <i
                class="fas fa-image"
                style="color: #3b82f6; font-size: 20px"
              ></i>
            </div>
            <span style="font-size: 14px; font-weight: 500; color: #374151"
              >Images</span
            >
          </button>

          <!-- Documents -->
          <button
            (click)="triggerFileInput('document')"
            style="
              display: flex;
              flex-direction: column;
              align-items: center;
              gap: 8px;
              padding: 16px;
              border: none;
              background: transparent;
              border-radius: 12px;
              cursor: pointer;
              transition: all 0.2s;
            "
            onmouseover="this.style.background='#f3f4f6'"
            onmouseout="this.style.background='transparent'"
          >
            <div
              style="
                width: 48px;
                height: 48px;
                background: #fef3c7;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
              "
            >
              <i
                class="fas fa-file-alt"
                style="color: #f59e0b; font-size: 20px"
              ></i>
            </div>
            <span style="font-size: 14px; font-weight: 500; color: #374151"
              >Documents</span
            >
          </button>

          <!-- Camera -->
          <button
            (click)="openCamera()"
            style="
              display: flex;
              flex-direction: column;
              align-items: center;
              gap: 8px;
              padding: 16px;
              border: none;
              background: transparent;
              border-radius: 12px;
              cursor: pointer;
              transition: all 0.2s;
            "
            onmouseover="this.style.background='#f3f4f6'"
            onmouseout="this.style.background='transparent'"
          >
            <div
              style="
                width: 48px;
                height: 48px;
                background: #dcfce7;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
              "
            >
              <i
                class="fas fa-camera"
                style="color: #10b981; font-size: 20px"
              ></i>
            </div>
            <span style="font-size: 14px; font-weight: 500; color: #374151"
              >Caméra</span
            >
          </button>
        </div>
      </div>
    </div>

    <!-- Hidden File Input -->
    <input
      #fileInput
      type="file"
      style="display: none"
      (change)="onFileSelected($event)"
      [accept]="getFileAcceptTypes()"
      multiple
    />
  </footer>

  <!-- Overlay to close menus -->
  <div
    *ngIf="showEmojiPicker || showAttachmentMenu || showMainMenu"
    style="
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.25);
      z-index: 40;
    "
    (click)="closeAllMenus()"
  ></div>

  <!-- Interface d'enregistrement vocal -->
  <div
    *ngIf="isRecordingVoice"
    style="
      position: fixed;
      bottom: 100px;
      left: 50%;
      transform: translateX(-50%);
      background: linear-gradient(135deg, #f59e0b, #d97706);
      color: white;
      padding: 20px 24px;
      border-radius: 20px;
      box-shadow: 0 20px 25px rgba(0, 0, 0, 0.2);
      z-index: 60;
      display: flex;
      align-items: center;
      gap: 16px;
      min-width: 280px;
      animation: slideInUp 0.3s ease-out;
    "
  >
    <!-- Icône microphone animée -->
    <div
      style="
        width: 48px;
        height: 48px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        animation: pulse 1s infinite;
      "
    >
      <i class="fas fa-microphone" style="font-size: 20px"></i>
    </div>

    <!-- Contenu central -->
    <div style="flex: 1">
      <!-- Durée d'enregistrement -->
      <div style="font-size: 18px; font-weight: bold; margin-bottom: 4px">
        {{ formatRecordingDuration(voiceRecordingDuration) }}
      </div>

      <!-- Visualisation des ondes sonores -->
      <div style="display: flex; align-items: center; gap: 2px; height: 20px">
        <div
          *ngFor="let wave of voiceWaves; let i = index"
          style="
            width: 3px;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 2px;
            transition: height 0.3s ease;
          "
          [style.height.px]="wave"
          [style.animation]="'bounce 1s infinite'"
          [style.animation-delay]="i * 0.1 + 's'"
        ></div>
      </div>

      <!-- Format d'enregistrement -->
      <div style="font-size: 12px; opacity: 0.8; margin-top: 4px">
        Format: {{ getRecordingFormat() }}
      </div>
    </div>

    <!-- Boutons d'action -->
    <div style="display: flex; gap: 8px">
      <!-- Bouton Annuler -->
      <button
        (click)="onRecordCancel($event)"
        style="
          width: 40px;
          height: 40px;
          border-radius: 50%;
          border: none;
          background: rgba(239, 68, 68, 0.8);
          color: white;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.2s;
        "
        onmouseover="this.style.background='rgba(239, 68, 68, 1)'"
        onmouseout="this.style.background='rgba(239, 68, 68, 0.8)'"
        title="Annuler l'enregistrement"
      >
        <i class="fas fa-times" style="font-size: 16px"></i>
      </button>

      <!-- Bouton Envoyer -->
      <button
        (click)="onRecordEnd($event)"
        style="
          width: 40px;
          height: 40px;
          border-radius: 50%;
          border: none;
          background: rgba(34, 197, 94, 0.8);
          color: white;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.2s;
        "
        onmouseover="this.style.background='rgba(34, 197, 94, 1)'"
        onmouseout="this.style.background='rgba(34, 197, 94, 0.8)'"
        title="Envoyer le message vocal"
      >
        <i class="fas fa-paper-plane" style="font-size: 16px"></i>
      </button>
    </div>
  </div>
</div>

<!-- Interface d'appel vidéo -->
<div
  *ngIf="activeCall && activeCall.type === 'VIDEO'"
  class="video-call-overlay"
  style="
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: linear-gradient(135deg, #1a1a2e, #16213e);
    z-index: 9999;
    display: flex;
    flex-direction: column;
  "
>
  <!-- En-tête de l'appel -->
  <div
    style="
      padding: 20px;
      text-align: center;
      color: white;
      background: rgba(0, 0, 0, 0.3);
    "
  >
    <h3 style="margin: 0; font-size: 18px">
      📹 Appel vidéo avec {{ otherParticipant?.username }}
    </h3>
    <p style="margin: 5px 0 0 0; opacity: 0.8">
      {{ activeCall.status === "connected" ? "Connecté" : "Connexion..." }}
    </p>
  </div>

  <!-- Zone vidéo principale -->
  <div
    style="
      flex: 1;
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
    "
  >
    <!-- Vidéo distante (principale) -->
    <video
      #remoteVideo
      id="remoteVideo"
      autoplay
      playsinline
      style="width: 100%; height: 100%; object-fit: cover; background: #000"
    ></video>

    <!-- Vidéo locale (petite fenêtre) -->
    <video
      #localVideo
      id="localVideo"
      autoplay
      muted
      playsinline
      style="
        position: absolute;
        top: 20px;
        right: 20px;
        width: 200px;
        height: 150px;
        border-radius: 10px;
        border: 2px solid #00ff88;
        object-fit: cover;
        background: #000;
        z-index: 10;
      "
    ></video>

    <!-- Message si pas de vidéo -->
    <div
      *ngIf="!activeCall || activeCall.status !== 'connected'"
      style="
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        text-align: center;
        color: white;
      "
    >
      <div
        style="
          width: 120px;
          height: 120px;
          border-radius: 50%;
          background: linear-gradient(135deg, #667eea, #764ba2);
          margin: 0 auto 20px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 48px;
        "
      >
        👤
      </div>
      <h3>{{ otherParticipant?.username }}</h3>
      <p style="opacity: 0.8">En attente de connexion...</p>
    </div>
  </div>

  <!-- Contrôles d'appel -->
  <div
    style="
      padding: 30px;
      display: flex;
      justify-content: center;
      gap: 20px;
      background: rgba(0, 0, 0, 0.3);
    "
  >
    <!-- Bouton micro -->
    <button
      (click)="toggleMicrophone()"
      [style.background]="isMuted ? '#ff4757' : '#2ed573'"
      style="
        width: 60px;
        height: 60px;
        border-radius: 50%;
        border: none;
        color: white;
        font-size: 20px;
        cursor: pointer;
        transition: all 0.3s;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
      "
      title="{{ isMuted ? 'Activer le micro' : 'Couper le micro' }}"
    >
      <i
        class="fas {{ isMuted ? 'fa-microphone-slash' : 'fa-microphone' }}"
      ></i>
    </button>

    <!-- Bouton caméra -->
    <button
      (click)="toggleCamera()"
      [style.background]="isVideoEnabled ? '#2ed573' : '#ff4757'"
      style="
        width: 60px;
        height: 60px;
        border-radius: 50%;
        border: none;
        color: white;
        font-size: 20px;
        cursor: pointer;
        transition: all 0.3s;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
      "
      title="{{
        isVideoEnabled ? 'Désactiver la caméra' : 'Activer la caméra'
      }}"
    >
      <i class="fas {{ isVideoEnabled ? 'fa-video' : 'fa-video-slash' }}"></i>
    </button>

    <!-- Bouton raccrocher -->
    <button
      (click)="endCall()"
      style="
        width: 60px;
        height: 60px;
        border-radius: 50%;
        border: none;
        background: #ff4757;
        color: white;
        font-size: 20px;
        cursor: pointer;
        transition: all 0.3s;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
      "
      title="Raccrocher"
    >
      <i class="fas fa-phone-slash"></i>
    </button>
  </div>
</div>

<!-- Éléments vidéo cachés pour appels audio uniquement -->
<video
  *ngIf="!activeCall || activeCall.type !== 'VIDEO'"
  #localVideoHidden
  id="localVideoHidden"
  autoplay
  muted
  playsinline
  style="
    position: absolute;
    top: -9999px;
    left: -9999px;
    width: 1px;
    height: 1px;
  "
></video>

<video
  *ngIf="!activeCall || activeCall.type !== 'VIDEO'"
  #remoteVideoHidden
  id="remoteVideoHidden"
  autoplay
  playsinline
  style="
    position: absolute;
    top: -9999px;
    left: -9999px;
    width: 1px;
    height: 1px;
  "
></video>

<!-- Interface d'appel gérée par les composants globaux app-incoming-call et app-active-call -->

<!-- Les composants d'appel sont maintenant gérés globalement dans app.component.html -->
