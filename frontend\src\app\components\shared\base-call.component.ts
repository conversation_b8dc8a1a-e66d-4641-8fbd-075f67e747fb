import { Component, On<PERSON><PERSON>roy } from '@angular/core';
import { Subscription } from 'rxjs';
import { CallService } from '../../services/call.service';
import { CallUtilsService } from './call-utils.service';
import { LoggerService } from '../../services/logger.service';

/**
 * Composant de base partagé pour les interfaces d'appel
 * Contient la logique commune entre ActiveCall et IncomingCall
 */
@Component({
  template: '' // Sera surchargé par les composants enfants
})
export abstract class BaseCallComponent implements OnDestroy {
  
  protected subscriptions: Subscription[] = [];
  isProcessing = false;

  constructor(
    protected callService: CallService,
    protected callUtils: CallUtilsService,
    protected logger: LoggerService
  ) {}

  ngOnDestroy(): void {
    // Nettoyer toutes les subscriptions
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  /**
   * Ajoute une subscription à la liste pour nettoyage automatique
   */
  protected addSubscription(subscription: Subscription): void {
    this.subscriptions.push(subscription);
  }

  /**
   * Gère les erreurs d'appel de manière standardisée
   */
  protected handleCallError(error: any, action: string): void {
    console.error(`❌ [${this.constructor.name}] Error ${action}:`, error);
    this.logger.error(`Error ${action}:`, error);
    this.isProcessing = false;
    
    // Message d'erreur générique
    const errorMessage = `Erreur lors de ${action}. Veuillez réessayer.`;
    alert(errorMessage); // TODO: Remplacer par un service de toast
  }

  /**
   * Log standardisé pour les actions d'appel
   */
  protected logCallAction(action: string, data?: any): void {
    console.log(`📞 [${this.constructor.name}] ${action}:`, data || '');
  }

  /**
   * Vérifie si une action peut être exécutée (pas de traitement en cours)
   */
  protected canExecuteAction(): boolean {
    return !this.isProcessing;
  }

  /**
   * Démarre le traitement d'une action
   */
  protected startProcessing(): void {
    this.isProcessing = true;
  }

  /**
   * Arrête le traitement d'une action
   */
  protected stopProcessing(): void {
    this.isProcessing = false;
  }

  // Méthodes abstraites à implémenter par les composants enfants
  abstract ngOnInit(): void;
}
