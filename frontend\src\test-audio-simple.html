<!DOCTYPE html>
<html>
<head>
    <title>Test Audio Simple</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #1a1a1a;
            color: white;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
        }
        button {
            background: #00ff88;
            color: #1a1a1a;
            border: none;
            padding: 15px 25px;
            margin: 10px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
            font-size: 16px;
        }
        button:hover {
            background: #00cc66;
        }
        button:disabled {
            background: #666;
            cursor: not-allowed;
        }
        .status {
            background: #333;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
            font-size: 14px;
        }
        .error {
            background: #cc0000;
            color: white;
        }
        .success {
            background: #00cc66;
            color: white;
        }
        video {
            width: 200px;
            height: 150px;
            background: #333;
            border: 2px solid #00ff88;
            border-radius: 8px;
            margin: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔊 Test Audio WebRTC Simple</h1>
        
        <div id="status" class="status">
            Prêt à tester l'audio
        </div>
        
        <div>
            <h3>Étape 1: Test Microphone</h3>
            <button onclick="testMicrophone()">🎤 Tester Microphone</button>
            <button onclick="stopMicrophone()">⏹️ Arrêter</button>
        </div>
        
        <div>
            <h3>Étape 2: Test Écho Audio</h3>
            <button onclick="testEcho()" id="echoBtn" disabled>🔄 Test Écho</button>
            <button onclick="stopEcho()">⏹️ Arrêter Écho</button>
        </div>
        
        <div>
            <h3>Étape 3: Test WebRTC Local</h3>
            <button onclick="testWebRTCLocal()" id="webrtcBtn" disabled>📞 Test WebRTC</button>
            <button onclick="stopWebRTC()">⏹️ Arrêter WebRTC</button>
        </div>
        
        <div>
            <video id="localVideo" autoplay muted playsinline></video>
            <video id="remoteVideo" autoplay playsinline></video>
        </div>
        
        <div class="status">
            <h3>📋 Instructions:</h3>
            <ol>
                <li><strong>Tester Microphone</strong> - Vérifier l'accès au micro</li>
                <li><strong>Test Écho</strong> - Vous devriez entendre votre voix</li>
                <li><strong>Test WebRTC</strong> - Simulation d'appel local</li>
            </ol>
        </div>
    </div>

    <script>
        let localStream = null;
        let peerConnection1 = null;
        let peerConnection2 = null;
        
        function updateStatus(message, type = 'normal') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = 'status';
            if (type === 'error') statusDiv.className += ' error';
            if (type === 'success') statusDiv.className += ' success';
            console.log('🔍 [Test]', message);
        }
        
        // Étape 1: Test du microphone
        async function testMicrophone() {
            try {
                updateStatus('🎤 Demande d\'accès au microphone...');
                
                localStream = await navigator.mediaDevices.getUserMedia({
                    audio: true,
                    video: false
                });
                
                updateStatus('✅ Microphone accessible!', 'success');
                document.getElementById('echoBtn').disabled = false;
                document.getElementById('webrtcBtn').disabled = false;
                
                // Afficher les informations sur les tracks
                const audioTracks = localStream.getAudioTracks();
                console.log('🎤 Audio tracks:', audioTracks.length);
                if (audioTracks.length > 0) {
                    console.log('🎤 Audio track settings:', audioTracks[0].getSettings());
                    console.log('🎤 Audio track constraints:', audioTracks[0].getConstraints());
                }
                
            } catch (error) {
                updateStatus('❌ Erreur microphone: ' + error.message, 'error');
                console.error('❌ [Test] Erreur getUserMedia:', error);
            }
        }
        
        function stopMicrophone() {
            if (localStream) {
                localStream.getTracks().forEach(track => track.stop());
                localStream = null;
                updateStatus('⏹️ Microphone arrêté');
                document.getElementById('echoBtn').disabled = true;
                document.getElementById('webrtcBtn').disabled = true;
            }
        }
        
        // Étape 2: Test écho audio
        function testEcho() {
            if (!localStream) {
                updateStatus('❌ Activez d\'abord le microphone', 'error');
                return;
            }
            
            try {
                updateStatus('🔄 Test écho audio...');
                
                const remoteVideo = document.getElementById('remoteVideo');
                remoteVideo.srcObject = localStream;
                remoteVideo.volume = 0.8; // Volume élevé pour bien entendre
                
                // Forcer la lecture
                remoteVideo.play().then(() => {
                    updateStatus('✅ Écho actif - Parlez, vous devriez vous entendre!', 'success');
                }).catch((error) => {
                    updateStatus('❌ Erreur lecture écho: ' + error.message, 'error');
                    console.error('❌ [Test] Erreur play écho:', error);
                });
                
            } catch (error) {
                updateStatus('❌ Erreur écho: ' + error.message, 'error');
                console.error('❌ [Test] Erreur écho:', error);
            }
        }
        
        function stopEcho() {
            const remoteVideo = document.getElementById('remoteVideo');
            remoteVideo.srcObject = null;
            updateStatus('⏹️ Écho arrêté');
        }
        
        // Étape 3: Test WebRTC local (simulation d'appel)
        async function testWebRTCLocal() {
            if (!localStream) {
                updateStatus('❌ Activez d\'abord le microphone', 'error');
                return;
            }
            
            try {
                updateStatus('📞 Test WebRTC local...');
                
                // Configuration WebRTC
                const configuration = {
                    iceServers: [{ urls: 'stun:stun.l.google.com:19302' }]
                };
                
                // Créer deux PeerConnections pour simuler un appel
                peerConnection1 = new RTCPeerConnection(configuration);
                peerConnection2 = new RTCPeerConnection(configuration);
                
                // Ajouter le stream local à PC1
                localStream.getTracks().forEach(track => {
                    peerConnection1.addTrack(track, localStream);
                });
                
                // Quand PC2 reçoit le stream, l'afficher
                peerConnection2.ontrack = (event) => {
                    console.log('📺 [Test] Stream reçu via WebRTC');
                    const remoteVideo = document.getElementById('remoteVideo');
                    remoteVideo.srcObject = event.streams[0];
                    remoteVideo.volume = 1.0;
                    
                    remoteVideo.play().then(() => {
                        updateStatus('✅ WebRTC fonctionne - Audio transmis!', 'success');
                    }).catch((error) => {
                        updateStatus('❌ Erreur lecture WebRTC: ' + error.message, 'error');
                    });
                };
                
                // Gestion des candidats ICE
                peerConnection1.onicecandidate = (event) => {
                    if (event.candidate) {
                        peerConnection2.addIceCandidate(event.candidate);
                    }
                };
                
                peerConnection2.onicecandidate = (event) => {
                    if (event.candidate) {
                        peerConnection1.addIceCandidate(event.candidate);
                    }
                };
                
                // Créer l'offre et la réponse
                const offer = await peerConnection1.createOffer();
                await peerConnection1.setLocalDescription(offer);
                await peerConnection2.setRemoteDescription(offer);
                
                const answer = await peerConnection2.createAnswer();
                await peerConnection2.setLocalDescription(answer);
                await peerConnection1.setRemoteDescription(answer);
                
                console.log('✅ [Test] WebRTC négociation terminée');
                
            } catch (error) {
                updateStatus('❌ Erreur WebRTC: ' + error.message, 'error');
                console.error('❌ [Test] Erreur WebRTC:', error);
            }
        }
        
        function stopWebRTC() {
            if (peerConnection1) {
                peerConnection1.close();
                peerConnection1 = null;
            }
            if (peerConnection2) {
                peerConnection2.close();
                peerConnection2 = null;
            }
            
            const remoteVideo = document.getElementById('remoteVideo');
            remoteVideo.srcObject = null;
            
            updateStatus('⏹️ WebRTC arrêté');
        }
        
        // Vérifications initiales
        window.onload = function() {
            if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
                updateStatus('❌ WebRTC non supporté par ce navigateur', 'error');
                return;
            }
            
            if (!window.RTCPeerConnection) {
                updateStatus('❌ RTCPeerConnection non supporté', 'error');
                return;
            }
            
            updateStatus('✅ Navigateur compatible WebRTC');
        };
    </script>
</body>
</html>
