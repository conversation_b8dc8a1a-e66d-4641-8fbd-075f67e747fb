"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[576],{8576:(v,n,a)=>{a.r(n),a.d(n,{ResetPasswordModule:()=>p});var f=a(177),t=a(4341),i=a(6647),e=a(7705),u=a(4796);function c(r,k){if(1&r&&(e.j41(0,"div",37)(1,"div",38)(2,"div",39),e.nrm(3,"i",40)(4,"div",41),e.k0s(),e.j41(5,"div",42)(6,"p",43),e.<PERSON><PERSON>(7),e.k0s()()()()),2&r){const d=e.XpG();e.R7$(7),e.SpI(" ",d.error," ")}}function m(r,k){if(1&r&&(e.j41(0,"div",44)(1,"div",38)(2,"div",45),e.nrm(3,"i",46)(4,"div",47),e.k0s(),e.j41(5,"div",42)(6,"p",48),e.EFF(7),e.k0s()()()()),2&r){const d=e.XpG();e.R7$(7),e.SpI(" ",d.message," ")}}const b=[{path:"",component:(()=>{class r{constructor(d,o,s,l){this.fb=d,this.authService=o,this.router=s,this.route=l,this.message="",this.error="",this.resetForm=this.fb.group({email:["",[t.k0.required,t.k0.email]],code:["",[t.k0.required,t.k0.minLength(6)]],newPassword:["",[t.k0.required,t.k0.minLength(6)]]})}ngOnInit(){this.route.queryParams.subscribe(d=>{d.email&&this.resetForm.patchValue({email:d.email})})}onSubmit(){this.resetForm.invalid||this.authService.resetPassword(this.resetForm.value).subscribe({next:d=>{this.message=d.message+" Redirecting to login...",this.error="",setTimeout(()=>this.router.navigate(["/login"]),1500)},error:d=>{this.error=d.error.message||"Reset failed",this.message=""}})}static{this.\u0275fac=function(o){return new(o||r)(e.rXU(t.ok),e.rXU(u.u),e.rXU(i.Ix),e.rXU(i.nX))}}static{this.\u0275cmp=e.VBU({type:r,selectors:[["app-reset-password"]],decls:65,vars:4,consts:[[1,"container-fluid","p-4","md:p-6","bg-[#edf1f4]","dark:bg-[#121212]","min-h-screen","flex","items-center","justify-center","relative"],[1,"absolute","inset-0","overflow-hidden","pointer-events-none"],[1,"absolute","top-[15%]","left-[10%]","w-64","h-64","rounded-full","bg-gradient-to-br","from-[#4f5fad]/5","to-transparent","dark:from-[#6d78c9]/3","dark:to-transparent","blur-3xl"],[1,"absolute","bottom-[20%]","right-[10%]","w-80","h-80","rounded-full","bg-gradient-to-tl","from-[#4f5fad]/5","to-transparent","dark:from-[#6d78c9]/3","dark:to-transparent","blur-3xl"],[1,"absolute","inset-0","opacity-5","dark:opacity-[0.03]"],[1,"h-full","grid","grid-cols-12"],[1,"border-r","border-[#4f5fad]","dark:border-[#6d78c9]"],[1,"w-full","max-w-md","relative","z-10"],[1,"bg-white","dark:bg-[#1e1e1e]","rounded-xl","shadow-lg","dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)]","overflow-hidden","backdrop-blur-sm","border","border-[#edf1f4]/50","dark:border-[#2a2a2a]","relative"],[1,"absolute","top-0","left-0","right-0","h-1","bg-gradient-to-r","from-[#3d4a85]","to-[#4f5fad]","dark:from-[#6d78c9]","dark:to-[#4f5fad]"],[1,"absolute","top-0","left-0","right-0","h-1","bg-gradient-to-r","from-[#3d4a85]","to-[#4f5fad]","dark:from-[#6d78c9]","dark:to-[#4f5fad]","blur-md"],[1,"p-6","text-center"],[1,"text-2xl","font-bold","bg-gradient-to-r","from-[#3d4a85]","to-[#4f5fad]","dark:from-[#6d78c9]","dark:to-[#4f5fad]","bg-clip-text","text-transparent"],[1,"text-sm","text-[#6d6870]","dark:text-[#a0a0a0]","mt-2"],[1,"p-6"],[1,"space-y-5",3,"formGroup","ngSubmit"],[1,"group"],[1,"flex","items-center","text-sm","font-medium","text-[#4f5fad]","dark:text-[#6d78c9]","mb-2"],[1,"fas","fa-envelope","mr-1.5","text-xs"],[1,"relative"],["type","email","formControlName","email","placeholder","<EMAIL>",1,"w-full","px-4","py-2.5","text-sm","rounded-lg","border","border-[#bdc6cc]","dark:border-[#2a2a2a]","bg-white","dark:bg-[#1e1e1e]","text-[#6d6870]","dark:text-[#e0e0e0]","focus:outline-none","focus:border-[#4f5fad]","dark:focus:border-[#6d78c9]","focus:ring-2","focus:ring-[#4f5fad]/20","dark:focus:ring-[#6d78c9]/20","transition-all"],[1,"absolute","inset-y-0","left-0","pl-3","flex","items-center","pointer-events-none","opacity-0","group-focus-within:opacity-100","transition-opacity"],[1,"w-0.5","h-4","bg-gradient-to-b","from-[#3d4a85]","to-[#4f5fad]","dark:from-[#6d78c9]","dark:to-[#4f5fad]","rounded-full"],[1,"fas","fa-key","mr-1.5","text-xs"],["type","text","formControlName","code","placeholder","123456","maxlength","6",1,"w-full","px-4","py-2.5","text-sm","rounded-lg","border","border-[#bdc6cc]","dark:border-[#2a2a2a]","bg-white","dark:bg-[#1e1e1e]","text-[#6d6870]","dark:text-[#e0e0e0]","focus:outline-none","focus:border-[#4f5fad]","dark:focus:border-[#6d78c9]","focus:ring-2","focus:ring-[#4f5fad]/20","dark:focus:ring-[#6d78c9]/20","transition-all"],[1,"fas","fa-lock","mr-1.5","text-xs"],["type","password","formControlName","newPassword","placeholder","\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022",1,"w-full","px-4","py-2.5","text-sm","rounded-lg","border","border-[#bdc6cc]","dark:border-[#2a2a2a]","bg-white","dark:bg-[#1e1e1e]","text-[#6d6870]","dark:text-[#e0e0e0]","focus:outline-none","focus:border-[#4f5fad]","dark:focus:border-[#6d78c9]","focus:ring-2","focus:ring-[#4f5fad]/20","dark:focus:ring-[#6d78c9]/20","transition-all"],["class","bg-[#ff6b69]/10 dark:bg-[#ff6b69]/5 border border-[#ff6b69] dark:border-[#ff6b69]/30 rounded-lg p-3 backdrop-blur-sm",4,"ngIf"],["class","bg-[#4f5fad]/10 dark:bg-[#6d78c9]/5 border border-[#4f5fad] dark:border-[#6d78c9]/30 rounded-lg p-3 backdrop-blur-sm",4,"ngIf"],["type","submit",1,"w-full","relative","overflow-hidden","group","mt-6",3,"disabled"],[1,"absolute","inset-0","bg-gradient-to-r","from-[#3d4a85]","to-[#4f5fad]","dark:from-[#3d4a85]","dark:to-[#6d78c9]","rounded-lg","transition-transform","duration-300","group-hover:scale-105","disabled:opacity-50"],[1,"absolute","inset-0","bg-gradient-to-r","from-[#3d4a85]","to-[#4f5fad]","dark:from-[#3d4a85]","dark:to-[#6d78c9]","rounded-lg","opacity-0","group-hover:opacity-100","blur-xl","transition-opacity","duration-300","disabled:opacity-0"],[1,"relative","flex","items-center","justify-center","text-white","font-medium","py-2.5","px-4","rounded-lg","transition-all","z-10"],[1,"fas","fa-key","mr-2"],[1,"text-center","text-sm","text-[#6d6870]","dark:text-[#a0a0a0]","space-y-2","pt-4"],["routerLink","/login",1,"text-[#4f5fad]","dark:text-[#6d78c9]","hover:text-[#3d4a85]","dark:hover:text-[#4f5fad]","transition-colors","font-medium","flex","items-center","justify-center"],[1,"fas","fa-arrow-left","mr-1.5","text-xs"],[1,"bg-[#ff6b69]/10","dark:bg-[#ff6b69]/5","border","border-[#ff6b69]","dark:border-[#ff6b69]/30","rounded-lg","p-3","backdrop-blur-sm"],[1,"flex","items-start"],[1,"text-[#ff6b69]","dark:text-[#ff8785]","mr-2","text-base","relative"],[1,"fas","fa-exclamation-triangle"],[1,"absolute","inset-0","bg-[#ff6b69]/20","dark:bg-[#ff8785]/20","blur-xl","rounded-full","transform","scale-150","-z-10"],[1,"flex-1"],[1,"text-xs","text-[#ff6b69]","dark:text-[#ff8785]"],[1,"bg-[#4f5fad]/10","dark:bg-[#6d78c9]/5","border","border-[#4f5fad]","dark:border-[#6d78c9]/30","rounded-lg","p-3","backdrop-blur-sm"],[1,"text-[#4f5fad]","dark:text-[#6d78c9]","mr-2","text-base","relative"],[1,"fas","fa-check-circle"],[1,"absolute","inset-0","bg-[#4f5fad]/20","dark:bg-[#6d78c9]/20","blur-xl","rounded-full","transform","scale-150","-z-10"],[1,"text-xs","text-[#4f5fad]","dark:text-[#6d78c9]"]],template:function(o,s){1&o&&(e.j41(0,"div",0)(1,"div",1),e.nrm(2,"div",2)(3,"div",3),e.j41(4,"div",4)(5,"div",5),e.nrm(6,"div",6)(7,"div",6)(8,"div",6)(9,"div",6)(10,"div",6)(11,"div",6)(12,"div",6)(13,"div",6)(14,"div",6)(15,"div",6)(16,"div",6),e.k0s()()(),e.j41(17,"div",7)(18,"div",8),e.nrm(19,"div",9)(20,"div",10),e.j41(21,"div",11)(22,"h1",12),e.EFF(23," Reset Password "),e.k0s(),e.j41(24,"p",13),e.EFF(25," Enter your new password "),e.k0s()(),e.j41(26,"div",14)(27,"form",15),e.bIt("ngSubmit",function(){return s.onSubmit()}),e.j41(28,"div",16)(29,"label",17),e.nrm(30,"i",18),e.EFF(31," Email "),e.k0s(),e.j41(32,"div",19),e.nrm(33,"input",20),e.j41(34,"div",21),e.nrm(35,"div",22),e.k0s()()(),e.j41(36,"div",16)(37,"label",17),e.nrm(38,"i",23),e.EFF(39," Reset Code "),e.k0s(),e.j41(40,"div",19),e.nrm(41,"input",24),e.j41(42,"div",21),e.nrm(43,"div",22),e.k0s()()(),e.j41(44,"div",16)(45,"label",17),e.nrm(46,"i",25),e.EFF(47," New Password "),e.k0s(),e.j41(48,"div",19),e.nrm(49,"input",26),e.j41(50,"div",21),e.nrm(51,"div",22),e.k0s()()(),e.DNE(52,c,8,1,"div",27),e.DNE(53,m,8,1,"div",28),e.j41(54,"button",29),e.nrm(55,"div",30)(56,"div",31),e.j41(57,"span",32),e.nrm(58,"i",33),e.EFF(59," Reset Password "),e.k0s()(),e.j41(60,"div",34)(61,"div")(62,"a",35),e.nrm(63,"i",36),e.EFF(64," Back to Login "),e.k0s()()()()()()()()),2&o&&(e.R7$(27),e.Y8G("formGroup",s.resetForm),e.R7$(25),e.Y8G("ngIf",s.error),e.R7$(1),e.Y8G("ngIf",s.message),e.R7$(1),e.Y8G("disabled",s.resetForm.invalid))},dependencies:[f.bT,t.qT,t.me,t.BC,t.cb,t.tU,t.j4,t.JD,i.Wk]})}}return r})()}];let g=(()=>{class r{static{this.\u0275fac=function(o){return new(o||r)}}static{this.\u0275mod=e.$C({type:r})}static{this.\u0275inj=e.G2t({imports:[i.iI.forChild(b),i.iI]})}}return r})(),p=(()=>{class r{static{this.\u0275fac=function(o){return new(o||r)}}static{this.\u0275mod=e.$C({type:r})}static{this.\u0275inj=e.G2t({imports:[f.MD,t.YN,t.X1,g]})}}return r})()}}]);