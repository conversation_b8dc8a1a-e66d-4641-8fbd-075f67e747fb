import { Injectable } from '@angular/core';
import { Call, IncomingCall, CallType, CallStatus } from '../../models/message.model';

/**
 * Service utilitaire partagé pour la logique commune des appels
 */
@Injectable({
  providedIn: 'root'
})
export class CallUtilsService {

  /**
   * Vérifie si c'est un appel vidéo
   */
  isVideoCall(call: Call | IncomingCall | null): boolean {
    return call?.type === CallType.VIDEO;
  }

  /**
   * Obtient l'avatar d'un participant d'appel
   */
  getParticipantAvatar(participant: any): string {
    return participant?.image || '/assets/images/default-avatar.png';
  }

  /**
   * Obtient le nom d'un participant d'appel
   */
  getParticipantName(participant: any): string {
    return participant?.username || 'Utilisateur inconnu';
  }

  /**
   * Obtient l'icône du type d'appel
   */
  getCallTypeIcon(callType: CallType): string {
    return callType === CallType.VIDEO ? 'fa-video' : 'fa-phone';
  }

  /**
   * Obtient le texte du type d'appel
   */
  getCallTypeText(callType: CallType): string {
    return callType === CallType.VIDEO ? 'Appel vidéo' : 'Appel audio';
  }

  /**
   * Obtient le texte du statut d'appel
   */
  getCallStatusText(status: CallStatus): string {
    switch (status) {
      case CallStatus.RINGING:
        return 'Sonnerie...';
      case CallStatus.CONNECTED:
        return 'Connecté';
      case CallStatus.ENDED:
        return 'Terminé';
      default:
        return 'En cours...';
    }
  }

  /**
   * Formate la durée d'appel en MM:SS
   */
  formatCallDuration(seconds: number): string {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  }

  /**
   * Obtient les informations de l'autre participant dans un appel actif
   */
  getOtherParticipant(call: Call): any {
    // Pour un appel actif, l'autre participant peut être soit le recipient soit le caller
    // selon qui a initié l'appel
    return call.recipient || call.caller;
  }

  /**
   * Obtient les informations de l'appelant dans un appel entrant
   */
  getCaller(incomingCall: IncomingCall): any {
    return incomingCall.caller;
  }

  /**
   * Vérifie si un appel est connecté
   */
  isCallConnected(call: Call | null): boolean {
    return call?.status === CallStatus.CONNECTED;
  }

  /**
   * Vérifie si un appel est en cours de sonnerie
   */
  isCallRinging(call: Call | null): boolean {
    return call?.status === CallStatus.RINGING;
  }

  /**
   * Génère les classes CSS pour les boutons d'appel
   */
  getCallButtonClasses(isActive: boolean, color: 'blue' | 'red' | 'green' | 'gray' = 'blue'): string {
    const baseClasses = 'w-16 h-16 rounded-full flex items-center justify-center transition-all duration-300 transform hover:scale-110 shadow-lg';
    
    const colorClasses = {
      blue: isActive ? 'bg-blue-500 hover:bg-blue-600 shadow-blue-500/50' : 'bg-gray-600 hover:bg-gray-700 shadow-gray-600/50',
      red: 'bg-red-500 hover:bg-red-600 shadow-red-500/50',
      green: 'bg-green-500 hover:bg-green-600 shadow-green-500/50',
      gray: 'bg-gray-600 hover:bg-gray-700 shadow-gray-600/50'
    };

    return `${baseClasses} ${colorClasses[color]}`;
  }

  /**
   * Génère les classes CSS pour les icônes d'appel
   */
  getCallIconClasses(iconType: string, isMuted: boolean = false): string {
    const baseClasses = 'fas text-white text-xl';
    
    const iconMap: { [key: string]: string } = {
      microphone: isMuted ? 'fa-microphone-slash' : 'fa-microphone',
      video: isMuted ? 'fa-video-slash' : 'fa-video',
      speaker: isMuted ? 'fa-volume-mute' : 'fa-volume-up',
      phone: 'fa-phone',
      phoneSlash: 'fa-phone-slash',
      callType: this.getCallTypeIcon(iconType as CallType)
    };

    return `${baseClasses} ${iconMap[iconType] || iconType}`;
  }
}
