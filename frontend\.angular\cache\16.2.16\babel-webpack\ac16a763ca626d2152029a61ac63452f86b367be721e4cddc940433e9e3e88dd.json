{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ActiveCallComponent } from '../active-call/active-call.component';\nimport { IncomingCallComponent } from '../incoming-call/incoming-call.component';\nexport let CallModule = class CallModule {};\nCallModule = __decorate([NgModule({\n  declarations: [ActiveCallComponent, IncomingCallComponent],\n  imports: [CommonModule],\n  exports: [ActiveCallComponent, IncomingCallComponent]\n})], CallModule);", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}