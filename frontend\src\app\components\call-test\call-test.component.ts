import { Component, OnIni<PERSON>, On<PERSON><PERSON>roy } from '@angular/core';
import { CallService } from '../../services/call.service';
import { MessageService } from '../../services/message.service';
import { ToastService } from '../../services/toast.service';
import { CallType, CallStatus } from '../../models/message.model';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-call-test',
  template: `
    <div class="call-test-panel" style="
      position: fixed;
      top: 20px;
      right: 20px;
      width: 300px;
      background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
      border: 2px solid #3b82f6;
      border-radius: 12px;
      padding: 16px;
      color: white;
      z-index: 10000;
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
    ">
      <h3 style="margin: 0 0 16px 0; color: #3b82f6; font-size: 16px;">
        🧪 Test d'Appels
      </h3>

      <!-- État des services -->
      <div style="margin-bottom: 16px; font-size: 12px;">
        <div style="color: #10b981;">
          ✅ CallService: {{ callServiceStatus }}
        </div>
        <div style="color: #10b981;">
          ✅ Subscription: {{ subscriptionStatus }}
        </div>
        <div style="color: #f59e0b;">
          📞 Appel actif: {{ activeCallStatus }}
        </div>
      </div>

      <!-- Simulation d'utilisateurs -->
      <div style="margin-bottom: 16px;">
        <label style="display: block; margin-bottom: 4px; font-size: 12px;">
          ID Destinataire:
        </label>
        <input 
          [(ngModel)]="testRecipientId"
          placeholder="ID utilisateur"
          style="
            width: 100%;
            padding: 6px;
            border: 1px solid #374151;
            border-radius: 6px;
            background: #374151;
            color: white;
            font-size: 12px;
          "
        />
      </div>

      <!-- Boutons de test -->
      <div style="display: flex; flex-direction: column; gap: 8px;">
        <button 
          (click)="testVideoCall()"
          [disabled]="isTestRunning"
          style="
            padding: 8px 12px;
            background: #3b82f6;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.2s;
          "
          onmouseover="this.style.background='#2563eb'"
          onmouseout="this.style.background='#3b82f6'"
        >
          📹 Test Appel Vidéo
        </button>

        <button 
          (click)="testAudioCall()"
          [disabled]="isTestRunning"
          style="
            padding: 8px 12px;
            background: #10b981;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.2s;
          "
          onmouseover="this.style.background='#059669'"
          onmouseout="this.style.background='#10b981'"
        >
          📞 Test Appel Audio
        </button>

        <button 
          (click)="simulateIncomingCall()"
          [disabled]="isTestRunning"
          style="
            padding: 8px 12px;
            background: #f59e0b;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.2s;
          "
          onmouseover="this.style.background='#d97706'"
          onmouseout="this.style.background='#f59e0b'"
        >
          📲 Simuler Appel Entrant
        </button>

        <button 
          (click)="reinitializeSubscription()"
          style="
            padding: 8px 12px;
            background: #6b7280;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.2s;
          "
          onmouseover="this.style.background='#4b5563'"
          onmouseout="this.style.background='#6b7280'"
        >
          🔄 Réinitialiser Subscription
        </button>

        <button 
          (click)="endCurrentCall()"
          [disabled]="!hasActiveCall"
          style="
            padding: 8px 12px;
            background: #ef4444;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.2s;
          "
          onmouseover="this.style.background='#dc2626'"
          onmouseout="this.style.background='#ef4444'"
        >
          ❌ Terminer Appel
        </button>
      </div>

      <!-- Logs de test -->
      <div style="margin-top: 16px;">
        <div style="font-size: 12px; color: #9ca3af; margin-bottom: 8px;">
          📋 Logs de test:
        </div>
        <div style="
          max-height: 120px;
          overflow-y: auto;
          background: #000;
          padding: 8px;
          border-radius: 4px;
          font-family: monospace;
          font-size: 10px;
        ">
          <div *ngFor="let log of testLogs" [style.color]="getLogColor(log)">
            {{ log }}
          </div>
        </div>
      </div>

      <!-- Bouton fermer -->
      <button 
        (click)="closePanel()"
        style="
          position: absolute;
          top: 8px;
          right: 8px;
          width: 20px;
          height: 20px;
          background: #ef4444;
          color: white;
          border: none;
          border-radius: 50%;
          cursor: pointer;
          font-size: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
        "
      >
        ×
      </button>
    </div>
  `,
  styles: []
})
export class CallTestComponent implements OnInit, OnDestroy {
  testRecipientId = '';
  isTestRunning = false;
  hasActiveCall = false;
  callServiceStatus = 'Initialisation...';
  subscriptionStatus = 'En attente...';
  activeCallStatus = 'Aucun';
  testLogs: string[] = [];

  private subscriptions: Subscription[] = [];

  constructor(
    private callService: CallService,
    private messageService: MessageService,
    private toastService: ToastService
  ) {}

  ngOnInit(): void {
    this.initializeTestPanel();
    this.setupSubscriptions();
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  private initializeTestPanel(): void {
    this.addLog('🧪 Panel de test initialisé');
    this.callServiceStatus = 'Actif';
    this.subscriptionStatus = 'Connecté';
  }

  private setupSubscriptions(): void {
    // Surveiller les appels actifs
    const activeCallSub = this.callService.activeCall$.subscribe(call => {
      this.hasActiveCall = !!call;
      this.activeCallStatus = call ? `${call.type} - ${call.status}` : 'Aucun';
      if (call) {
        this.addLog(`📞 Appel actif: ${call.type} (${call.status})`);
      }
    });

    // Surveiller les appels entrants
    const incomingCallSub = this.callService.incomingCall$.subscribe(call => {
      if (call) {
        this.addLog(`📲 Appel entrant: ${call.type} de ${call.caller?.username}`);
      }
    });

    this.subscriptions.push(activeCallSub, incomingCallSub);
  }

  testVideoCall(): void {
    if (!this.testRecipientId) {
      this.toastService.showError('Veuillez saisir un ID destinataire');
      return;
    }

    this.isTestRunning = true;
    this.addLog(`📹 Test appel vidéo vers ${this.testRecipientId}`);

    this.callService.initiateCall(this.testRecipientId, CallType.VIDEO).subscribe({
      next: (call) => {
        this.addLog(`✅ Appel vidéo initié: ${call.id}`);
        this.isTestRunning = false;
      },
      error: (error) => {
        this.addLog(`❌ Erreur appel vidéo: ${error.message}`);
        this.isTestRunning = false;
      }
    });
  }

  testAudioCall(): void {
    if (!this.testRecipientId) {
      this.toastService.showError('Veuillez saisir un ID destinataire');
      return;
    }

    this.isTestRunning = true;
    this.addLog(`📞 Test appel audio vers ${this.testRecipientId}`);

    this.callService.initiateCall(this.testRecipientId, CallType.AUDIO).subscribe({
      next: (call) => {
        this.addLog(`✅ Appel audio initié: ${call.id}`);
        this.isTestRunning = false;
      },
      error: (error) => {
        this.addLog(`❌ Erreur appel audio: ${error.message}`);
        this.isTestRunning = false;
      }
    });
  }

  simulateIncomingCall(): void {
    this.addLog('📲 Simulation d\'appel entrant...');
    // Cette méthode simule un appel entrant pour les tests
    const mockIncomingCall = {
      id: `test_call_${Date.now()}`,
      type: CallType.VIDEO,
      caller: {
        id: 'test_user',
        username: 'Utilisateur Test',
        image: '/assets/images/default-avatar.png'
      },
      conversationId: 'test_conversation'
    };

    // Simuler la réception d'un appel entrant
    this.callService['handleIncomingCall'](mockIncomingCall as any);
    this.addLog('✅ Appel entrant simulé');
  }

  reinitializeSubscription(): void {
    this.addLog('🔄 Réinitialisation des subscriptions...');
    this.callService.reinitializeSubscription();
    this.subscriptionStatus = 'Reconnexion...';
    
    setTimeout(() => {
      this.subscriptionStatus = 'Connecté';
      this.addLog('✅ Subscriptions réinitialisées');
    }, 2000);
  }

  endCurrentCall(): void {
    this.addLog('❌ Fin de l\'appel en cours...');
    // Logique pour terminer l'appel actuel
    this.hasActiveCall = false;
    this.activeCallStatus = 'Aucun';
  }

  closePanel(): void {
    // Masquer le panel de test
    const panel = document.querySelector('.call-test-panel') as HTMLElement;
    if (panel) {
      panel.style.display = 'none';
    }
  }

  private addLog(message: string): void {
    const timestamp = new Date().toLocaleTimeString();
    this.testLogs.unshift(`[${timestamp}] ${message}`);
    
    // Garder seulement les 20 derniers logs
    if (this.testLogs.length > 20) {
      this.testLogs = this.testLogs.slice(0, 20);
    }
  }

  getLogColor(log: string): string {
    if (log.includes('❌')) return '#ef4444';
    if (log.includes('✅')) return '#10b981';
    if (log.includes('📞') || log.includes('📹')) return '#3b82f6';
    if (log.includes('📲')) return '#f59e0b';
    if (log.includes('🔄')) return '#6b7280';
    return '#9ca3af';
  }
}
