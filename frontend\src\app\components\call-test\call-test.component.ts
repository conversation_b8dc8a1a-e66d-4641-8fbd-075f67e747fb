import { Component, OnIni<PERSON>, On<PERSON><PERSON>roy } from '@angular/core';
import { CallService } from '../../services/call.service';
import { MessageService } from '../../services/message.service';
import { ToastService } from '../../services/toast.service';
import { CallType, CallStatus } from '../../models/message.model';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-call-test',
  template: `
    <div
      class="call-test-panel"
      style="
      position: fixed;
      top: 20px;
      right: 20px;
      width: 300px;
      background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
      border: 2px solid #3b82f6;
      border-radius: 12px;
      padding: 16px;
      color: white;
      z-index: 10000;
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
    "
    >
      <h3 style="margin: 0 0 16px 0; color: #3b82f6; font-size: 16px;">
        🧪 Test d'Appels
      </h3>

      <!-- État des services -->
      <div style="margin-bottom: 16px; font-size: 12px;">
        <div style="color: #10b981;">
          ✅ CallService: {{ callServiceStatus }}
        </div>
        <div style="color: #10b981;">
          ✅ Subscription: {{ subscriptionStatus }}
        </div>
        <div style="color: #f59e0b;">
          📞 Appel actif: {{ activeCallStatus }}
        </div>
      </div>

      <!-- Simulation d'utilisateurs -->
      <div style="margin-bottom: 16px;">
        <label style="display: block; margin-bottom: 4px; font-size: 12px;">
          ID Destinataire:
        </label>
        <input
          [(ngModel)]="testRecipientId"
          placeholder="ID utilisateur"
          style="
            width: 100%;
            padding: 6px;
            border: 1px solid #374151;
            border-radius: 6px;
            background: #374151;
            color: white;
            font-size: 12px;
          "
        />
      </div>

      <!-- Boutons de test -->
      <div style="display: flex; flex-direction: column; gap: 8px;">
        <button
          (click)="testVideoCall()"
          [disabled]="isTestRunning"
          style="
            padding: 8px 12px;
            background: #3b82f6;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.2s;
          "
          onmouseover="this.style.background='#2563eb'"
          onmouseout="this.style.background='#3b82f6'"
        >
          📹 Test Appel Vidéo
        </button>

        <button
          (click)="testAudioCall()"
          [disabled]="isTestRunning"
          style="
            padding: 8px 12px;
            background: #10b981;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.2s;
          "
          onmouseover="this.style.background='#059669'"
          onmouseout="this.style.background='#10b981'"
        >
          📞 Test Appel Audio
        </button>

        <button
          (click)="simulateIncomingCall()"
          [disabled]="isTestRunning"
          style="
            padding: 8px 12px;
            background: #f59e0b;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.2s;
          "
          onmouseover="this.style.background='#d97706'"
          onmouseout="this.style.background='#f59e0b'"
        >
          📲 Simuler Appel Entrant
        </button>

        <button
          (click)="reinitializeSubscription()"
          style="
            padding: 8px 12px;
            background: #6b7280;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.2s;
          "
          onmouseover="this.style.background='#4b5563'"
          onmouseout="this.style.background='#6b7280'"
        >
          🔄 Réinitialiser Subscription
        </button>

        <button
          (click)="endCurrentCall()"
          [disabled]="!hasActiveCall"
          style="
            padding: 8px 12px;
            background: #ef4444;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.2s;
          "
          onmouseover="this.style.background='#dc2626'"
          onmouseout="this.style.background='#ef4444'"
        >
          ❌ Terminer Appel
        </button>

        <button
          (click)="runDiagnostic()"
          style="
            padding: 8px 12px;
            background: #8b5cf6;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.2s;
          "
          onmouseover="this.style.background='#7c3aed'"
          onmouseout="this.style.background='#8b5cf6'"
        >
          🔍 Diagnostic Complet
        </button>

        <button
          (click)="useCurrentUserId()"
          style="
            padding: 8px 12px;
            background: #059669;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.2s;
          "
          onmouseover="this.style.background='#047857'"
          onmouseout="this.style.background='#059669'"
        >
          👤 Utiliser mon ID
        </button>
      </div>

      <!-- Logs de test -->
      <div style="margin-top: 16px;">
        <div style="font-size: 12px; color: #9ca3af; margin-bottom: 8px;">
          📋 Logs de test:
        </div>
        <div
          style="
          max-height: 120px;
          overflow-y: auto;
          background: #000;
          padding: 8px;
          border-radius: 4px;
          font-family: monospace;
          font-size: 10px;
        "
        >
          <div *ngFor="let log of testLogs" [style.color]="getLogColor(log)">
            {{ log }}
          </div>
        </div>
      </div>

      <!-- Bouton fermer -->
      <button
        (click)="closePanel()"
        style="
          position: absolute;
          top: 8px;
          right: 8px;
          width: 20px;
          height: 20px;
          background: #ef4444;
          color: white;
          border: none;
          border-radius: 50%;
          cursor: pointer;
          font-size: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
        "
      >
        ×
      </button>
    </div>
  `,
  styles: [],
})
export class CallTestComponent implements OnInit, OnDestroy {
  testRecipientId = '';
  isTestRunning = false;
  hasActiveCall = false;
  callServiceStatus = 'Initialisation...';
  subscriptionStatus = 'En attente...';
  activeCallStatus = 'Aucun';
  testLogs: string[] = [];

  private subscriptions: Subscription[] = [];

  constructor(
    private callService: CallService,
    private messageService: MessageService,
    private toastService: ToastService
  ) {}

  ngOnInit(): void {
    this.initializeTestPanel();
    this.setupSubscriptions();
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((sub) => sub.unsubscribe());
  }

  private initializeTestPanel(): void {
    this.addLog('🧪 Panel de test initialisé');
    this.callServiceStatus = 'Actif';
    this.subscriptionStatus = 'Connecté';
  }

  private setupSubscriptions(): void {
    // Surveiller les appels actifs
    const activeCallSub = this.callService.activeCall$.subscribe((call) => {
      this.hasActiveCall = !!call;
      this.activeCallStatus = call ? `${call.type} - ${call.status}` : 'Aucun';
      if (call) {
        this.addLog(`📞 Appel actif: ${call.type} (${call.status})`);
      }
    });

    // Surveiller les appels entrants
    const incomingCallSub = this.callService.incomingCall$.subscribe((call) => {
      if (call) {
        this.addLog(
          `📲 Appel entrant: ${call.type} de ${call.caller?.username}`
        );
      }
    });

    this.subscriptions.push(activeCallSub, incomingCallSub);
  }

  testVideoCall(): void {
    if (!this.testRecipientId) {
      this.toastService.showError('Veuillez saisir un ID destinataire');
      return;
    }

    this.isTestRunning = true;
    this.addLog(`📹 Test appel vidéo vers ${this.testRecipientId}`);

    this.callService
      .initiateCall(this.testRecipientId, CallType.VIDEO)
      .subscribe({
        next: (call) => {
          this.addLog(`✅ Appel vidéo initié: ${call.id}`);
          this.isTestRunning = false;
        },
        error: (error) => {
          this.addLog(`❌ Erreur appel vidéo: ${error.message}`);
          this.isTestRunning = false;
        },
      });
  }

  testAudioCall(): void {
    if (!this.testRecipientId) {
      this.toastService.showError('Veuillez saisir un ID destinataire');
      return;
    }

    this.isTestRunning = true;
    this.addLog(`📞 Test appel audio vers ${this.testRecipientId}`);

    this.callService
      .initiateCall(this.testRecipientId, CallType.AUDIO)
      .subscribe({
        next: (call) => {
          this.addLog(`✅ Appel audio initié: ${call.id}`);
          this.isTestRunning = false;
        },
        error: (error) => {
          this.addLog(`❌ Erreur appel audio: ${error.message}`);
          this.isTestRunning = false;
        },
      });
  }

  simulateIncomingCall(): void {
    this.addLog("📲 Simulation d'appel entrant...");
    // Cette méthode simule un appel entrant pour les tests
    const mockIncomingCall = {
      id: `test_call_${Date.now()}`,
      type: CallType.VIDEO,
      caller: {
        id: 'test_user',
        username: 'Utilisateur Test',
        image: '/assets/images/default-avatar.png',
      },
      conversationId: 'test_conversation',
    };

    // Simuler la réception d'un appel entrant
    this.callService['handleIncomingCall'](mockIncomingCall as any);
    this.addLog('✅ Appel entrant simulé');
  }

  reinitializeSubscription(): void {
    this.addLog('🔄 Réinitialisation des subscriptions...');

    // Vérifier si la méthode existe
    if (typeof this.callService['reinitializeSubscription'] === 'function') {
      this.callService['reinitializeSubscription']();
    } else {
      this.addLog('⚠️ Méthode reinitializeSubscription non trouvée');
      // Réinitialiser manuellement
      this.callService['subscribeToIncomingCalls']();
    }

    this.subscriptionStatus = 'Reconnexion...';

    setTimeout(() => {
      this.subscriptionStatus = 'Connecté';
      this.addLog('✅ Subscriptions réinitialisées');
    }, 2000);
  }

  endCurrentCall(): void {
    this.addLog("❌ Fin de l'appel en cours...");
    // Logique pour terminer l'appel actuel
    this.hasActiveCall = false;
    this.activeCallStatus = 'Aucun';
  }

  runDiagnostic(): void {
    this.addLog('🔍 Démarrage du diagnostic complet...');

    // 1. Vérifier l'authentification
    const token = localStorage.getItem('token');
    const userStr = localStorage.getItem('user');
    let userId = null;

    if (!token) {
      this.addLog("❌ Token d'authentification manquant");
    } else {
      this.addLog("✅ Token d'authentification présent");
    }

    if (!userStr) {
      this.addLog('❌ Données utilisateur manquantes');
    } else {
      try {
        const user = JSON.parse(userStr);
        userId = user._id || user.id;
        if (userId) {
          this.addLog(`✅ ID utilisateur: ${userId}`);
          // Stocker l'ID pour les tests
          localStorage.setItem('userId', userId);
        } else {
          this.addLog('❌ ID utilisateur non trouvé dans les données');
        }
      } catch (error) {
        this.addLog('❌ Erreur parsing données utilisateur');
      }
    }

    // 2. Vérifier la connexion GraphQL
    this.addLog('🔄 Test de connexion GraphQL...');

    // 3. Vérifier les services
    this.addLog('🔄 Vérification des services...');

    if (this.callService) {
      this.addLog('✅ CallService disponible');
    } else {
      this.addLog('❌ CallService indisponible');
    }

    if (this.messageService) {
      this.addLog('✅ MessageService disponible');
    } else {
      this.addLog('❌ MessageService indisponible');
    }

    // 4. Test de mutation simple
    this.testSimpleGraphQLQuery();
  }

  useCurrentUserId(): void {
    const userStr = localStorage.getItem('user');
    if (userStr) {
      try {
        const user = JSON.parse(userStr);
        const userId = user._id || user.id;
        if (userId) {
          this.testRecipientId = userId;
          this.addLog(`👤 ID utilisateur défini: ${userId}`);
          this.addLog('⚠️ Attention: Vous allez vous appeler vous-même !');
        } else {
          this.addLog('❌ ID utilisateur non trouvé');
        }
      } catch (error) {
        this.addLog('❌ Erreur lecture données utilisateur');
      }
    } else {
      this.addLog('❌ Aucune donnée utilisateur trouvée');
    }
  }

  private testSimpleGraphQLQuery(): void {
    this.addLog('🔄 Test de requête GraphQL simple...');

    // Test avec une query simple pour vérifier la connexion
    this.messageService.getConversations().subscribe({
      next: (conversations) => {
        this.addLog(
          `✅ GraphQL fonctionne - ${conversations.length} conversations trouvées`
        );
        this.addLog("🎯 Diagnostic terminé - Prêt pour les tests d'appel");
      },
      error: (error) => {
        this.addLog(`❌ Erreur GraphQL: ${error.message}`);
        this.addLog("⚠️ Vérifiez la connexion backend et l'authentification");
      },
    });
  }

  closePanel(): void {
    // Masquer le panel de test
    const panel = document.querySelector('.call-test-panel') as HTMLElement;
    if (panel) {
      panel.style.display = 'none';
    }
  }

  private addLog(message: string): void {
    const timestamp = new Date().toLocaleTimeString();
    this.testLogs.unshift(`[${timestamp}] ${message}`);

    // Garder seulement les 20 derniers logs
    if (this.testLogs.length > 20) {
      this.testLogs = this.testLogs.slice(0, 20);
    }
  }

  getLogColor(log: string): string {
    if (log.includes('❌')) return '#ef4444';
    if (log.includes('✅')) return '#10b981';
    if (log.includes('📞') || log.includes('📹')) return '#3b82f6';
    if (log.includes('📲')) return '#f59e0b';
    if (log.includes('🔄')) return '#6b7280';
    return '#9ca3af';
  }
}
