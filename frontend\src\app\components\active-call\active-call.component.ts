import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { Subscription } from 'rxjs';
import { CallService } from '../../services/call.service';
import { LoggerService } from '../../services/logger.service';
import { Call, CallType, CallStatus } from '../../models/message.model';

@Component({
  selector: 'app-active-call',
  templateUrl: './active-call.component.html',
  styleUrls: ['./active-call.component.css'],
})
export class ActiveCallComponent implements OnInit, OnDestroy {
  activeCall: Call | null = null;
  callDuration: string = '00:00';
  isAudioMuted: boolean = false;
  isVideoMuted: boolean = false;
  isSpeakerOn: boolean = true;

  private durationInterval: any;
  private callStartTime: Date | null = null;
  private subscriptions: Subscription[] = [];

  // Exposer les énums au template
  CallType = CallType;
  CallStatus = CallStatus;

  constructor(
    private callService: CallService,
    private logger: LoggerService
  ) {}

  ngOnInit(): void {
    // S'abonner à l'appel actif
    const activeCallSub = this.callService.activeCall$.subscribe((call) => {
      const previousCall = this.activeCall;
      this.activeCall = call;

      console.log('📞 [ActiveCall] Active call updated:', call);

      if (call && call.status === CallStatus.CONNECTED) {
        if (!previousCall || previousCall.id !== call.id) {
          console.log('✅ [ActiveCall] Call connected, starting timer');
          this.startCallTimer();
        }
      } else if (!call || call.status !== CallStatus.CONNECTED) {
        this.stopCallTimer();
      }
    });

    this.subscriptions.push(activeCallSub);
  }

  ngOnDestroy(): void {
    this.stopCallTimer();
    this.subscriptions.forEach((sub) => sub.unsubscribe());
  }

  // Démarrer le minuteur d'appel
  private startCallTimer(): void {
    this.callStartTime = new Date();
    this.stopCallTimer(); // Arrêter tout minuteur existant

    this.durationInterval = setInterval(() => {
      if (this.callStartTime) {
        const now = new Date();
        const duration = Math.floor(
          (now.getTime() - this.callStartTime.getTime()) / 1000
        );
        this.callDuration = this.formatDuration(duration);
      }
    }, 1000);
  }

  // Arrêter le minuteur d'appel
  private stopCallTimer(): void {
    if (this.durationInterval) {
      clearInterval(this.durationInterval);
      this.durationInterval = null;
    }
    this.callStartTime = null;
    this.callDuration = '00:00';
  }

  // Formater la durée en MM:SS
  private formatDuration(seconds: number): string {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds
      .toString()
      .padStart(2, '0')}`;
  }

  // Basculer le micro
  toggleMicrophone(): void {
    if (!this.activeCall) return;

    this.isAudioMuted = !this.isAudioMuted;
    console.log('🎤 [ActiveCall] Audio muted:', this.isAudioMuted);
  }

  // Basculer la caméra
  toggleCamera(): void {
    if (!this.activeCall || this.activeCall.type === CallType.AUDIO) return;

    this.isVideoMuted = !this.isVideoMuted;
    console.log('📹 [ActiveCall] Video muted:', this.isVideoMuted);
  }

  // Basculer le haut-parleur
  toggleSpeaker(): void {
    this.isSpeakerOn = !this.isSpeakerOn;
    console.log('🔊 [ActiveCall] Speaker on:', this.isSpeakerOn);
  }

  // Activer l'audio manuellement (résout les problèmes d'autoplay)
  activateAudio(): void {
    console.log('🔊 [ActiveCall] Activating audio manually...');

    try {
      // 1. Activer AudioContext si suspendu
      const audioContext = new AudioContext();
      if (audioContext.state === 'suspended') {
        audioContext.resume().then(() => {
          console.log('✅ [ActiveCall] AudioContext resumed');
        });
      }

      // 2. Forcer la lecture de tous les éléments vidéo avec stream
      const videos = document.querySelectorAll('video');
      videos.forEach((video, index) => {
        if (video.srcObject && index > 0) {
          // Pas le local pour éviter l'écho
          console.log(`🔊 [ActiveCall] Activating audio for Video ${index}`);

          // Configuration audio
          video.volume = 1;
          video.muted = false;

          // Activer tous les tracks audio
          if (video.srcObject instanceof MediaStream) {
            const audioTracks = video.srcObject.getAudioTracks();
            audioTracks.forEach((track: MediaStreamTrack) => {
              track.enabled = true;
              console.log(
                `✅ [ActiveCall] Audio track enabled for Video ${index}`
              );
            });
          }

          // Forcer la lecture
          video
            .play()
            .then(() => {
              console.log(`✅ [ActiveCall] Video ${index} playing with audio`);
            })
            .catch((e) => {
              console.warn(`⚠️ [ActiveCall] Video ${index} play failed:`, e);
            });
        }
      });

      // 3. Appeler aussi la méthode du CallService
      this.callService.forceActivateAudio();

      console.log('✅ [ActiveCall] Audio activation completed');
    } catch (error) {
      console.error('❌ [ActiveCall] Error activating audio:', error);
    }
  }

  // Test audio WebRTC (temporaire pour debug)
  testAudio(): void {
    console.log('🧪 [ActiveCall] Testing WebRTC audio...');

    // Vérifier les éléments vidéo
    const videos = document.querySelectorAll('video');
    console.log('🔍 [ActiveCall] Found video elements:', videos.length);

    videos.forEach((video, index) => {
      console.log(`📺 [ActiveCall] Video ${index}:`, {
        srcObject: !!video.srcObject,
        muted: video.muted,
        volume: video.volume,
        paused: video.paused,
        tracks:
          video.srcObject && (video.srcObject as MediaStream).getTracks
            ? (video.srcObject as MediaStream)
                .getTracks()
                .map((t: MediaStreamTrack) => ({
                  kind: t.kind,
                  enabled: t.enabled,
                  readyState: t.readyState,
                }))
            : [],
      });

      // Forcer la lecture si pas en cours
      if (video.srcObject && video.paused) {
        console.log(`▶️ [ActiveCall] Forcing play for video ${index}`);
        video.play().catch((error) => {
          console.warn(`⚠️ [ActiveCall] Failed to play video ${index}:`, error);
        });
      }
    });

    // Test du CallService
    if (this.callService) {
      console.log('🔧 [ActiveCall] CallService state:', {
        hasLocalStream: !!(this.callService as any).localStream,
        hasRemoteStream: !!(this.callService as any).remoteStream,
        hasLocalVideoElement: !!(this.callService as any).localVideoElement,
        hasRemoteVideoElement: !!(this.callService as any).remoteVideoElement,
      });
    }
  }

  // Terminer l'appel
  endCall(): void {
    if (!this.activeCall) return;

    console.log('📞 [ActiveCall] Ending call:', this.activeCall.id);

    this.callService.endCall(this.activeCall.id).subscribe({
      next: () => {
        console.log('✅ [ActiveCall] Call ended successfully');
      },
      error: (error) => {
        console.error('❌ [ActiveCall] Error ending call:', error);
      },
    });
  }

  // Méthodes utilitaires pour le template
  isVideoCall(): boolean {
    return this.activeCall?.type === CallType.VIDEO;
  }

  getCallStatusText(): string {
    if (!this.activeCall) return '';

    switch (this.activeCall.status) {
      case CallStatus.RINGING:
        return 'Sonnerie...';
      case CallStatus.CONNECTED:
        return 'Connecté';
      case CallStatus.ENDED:
        return 'Terminé';
      default:
        return 'En cours...';
    }
  }

  getOtherParticipantName(): string {
    if (!this.activeCall) return '';

    // Logique pour obtenir le nom de l'autre participant
    // Si on est l'appelant, on affiche le destinataire, sinon l'appelant
    return (
      this.activeCall.recipient?.username ||
      this.activeCall.caller?.username ||
      'Utilisateur'
    );
  }

  getOtherParticipantAvatar(): string {
    if (!this.activeCall) return '/assets/images/default-avatar.png';

    // Logique pour obtenir l'avatar de l'autre participant
    return (
      this.activeCall.recipient?.image ||
      this.activeCall.caller?.image ||
      '/assets/images/default-avatar.png'
    );
  }
}
