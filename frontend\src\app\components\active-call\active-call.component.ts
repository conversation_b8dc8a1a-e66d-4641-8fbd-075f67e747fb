import {
  Component,
  OnInit,
  On<PERSON><PERSON>roy,
  ViewChild,
  ElementRef,
  AfterViewInit,
} from '@angular/core';
import { Subscription } from 'rxjs';
import { CallService } from '../../services/call.service';
import { LoggerService } from '../../services/logger.service';
import { Call, CallType, CallStatus } from '../../models/message.model';

@Component({
  selector: 'app-active-call',
  templateUrl: './active-call.component.html',
  styleUrls: ['./active-call.component.css'],
})
export class ActiveCallComponent implements OnInit, OnDestroy, AfterViewInit {
  @ViewChild('localVideo') localVideo!: ElementRef<HTMLVideoElement>;
  @ViewChild('remoteVideo') remoteVideo!: ElementRef<HTMLVideoElement>;

  activeCall: Call | null = null;
  callDuration = '00:00';

  // États des contrôles
  isAudioMuted = false;
  isVideoMuted = false;
  isSpeakerOn = true;

  private subscription: Subscription = new Subscription();
  private callStartTime: Date | null = null;
  private durationInterval: any;

  constructor(private callService: CallService, private logger: LoggerService) {
    this.logger.info('ActiveCall', '🚀 ActiveCall component initialized');
  }

  ngOnInit(): void {
    this.logger.info('ActiveCall', '📞 Setting up active call subscription');

    // S'abonner aux appels actifs
    this.subscription.add(
      this.callService.activeCall$.subscribe((call) => {
        this.logger.debug(
          'ActiveCall',
          '📞 Active call update received:',
          call
        );
        this.activeCall = call;

        if (call && call.status === CallStatus.CONNECTED) {
          this.logger.info('ActiveCall', '✅ Call connected, starting timer');
          this.startCallTimer();
        } else {
          this.logger.debug(
            'ActiveCall',
            '⏹️ Call not connected, stopping timer'
          );
          this.stopCallTimer();
        }
      })
    );
  }

  ngAfterViewInit(): void {
    // Configurer les éléments vidéo après l'initialisation de la vue
    if (this.localVideo && this.remoteVideo) {
      this.callService.setVideoElements(
        this.localVideo.nativeElement,
        this.remoteVideo.nativeElement
      );
    }
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
    this.stopCallTimer();
  }

  /**
   * Démarre le timer de durée d'appel
   */
  private startCallTimer(): void {
    this.callStartTime = new Date();
    this.durationInterval = setInterval(() => {
      if (this.callStartTime) {
        const now = new Date();
        const diff = now.getTime() - this.callStartTime.getTime();
        const minutes = Math.floor(diff / 60000);
        const seconds = Math.floor((diff % 60000) / 1000);
        this.callDuration = `${minutes.toString().padStart(2, '0')}:${seconds
          .toString()
          .padStart(2, '0')}`;
      }
    }, 1000);
  }

  /**
   * Arrête le timer de durée d'appel
   */
  private stopCallTimer(): void {
    if (this.durationInterval) {
      clearInterval(this.durationInterval);
      this.durationInterval = null;
    }
    this.callDuration = '00:00';
    this.callStartTime = null;
  }

  /**
   * Bascule l'état du microphone
   */
  toggleMicrophone(): void {
    this.isAudioMuted = !this.isAudioMuted;
    console.log('Microphone:', this.isAudioMuted ? 'Muted' : 'Unmuted');
  }

  /**
   * Bascule l'état de la caméra
   */
  toggleCamera(): void {
    this.isVideoMuted = !this.isVideoMuted;
    console.log('Camera:', this.isVideoMuted ? 'Muted' : 'Unmuted');
  }

  /**
   * Bascule l'état du haut-parleur
   */
  toggleSpeaker(): void {
    this.isSpeakerOn = !this.isSpeakerOn;
    console.log('Speaker:', this.isSpeakerOn ? 'On' : 'Off');
  }

  /**
   * Termine l'appel
   */
  endCall(): void {
    if (!this.activeCall) return;

    console.log('Ending call:', this.activeCall.id);

    this.callService.endCall(this.activeCall.id).subscribe({
      next: () => {
        console.log('Call ended successfully');
      },
      error: (error: any) => {
        console.error('Error ending call:', error);
      },
    });
  }

  // === MÉTHODES UTILITAIRES CONSOLIDÉES ===

  /**
   * Vérifie si c'est un appel vidéo
   */
  isVideoCall(): boolean {
    return this.activeCall?.type === CallType.VIDEO;
  }

  /**
   * Obtient le texte du statut de l'appel
   */
  getCallStatusText(): string {
    if (!this.activeCall) return '';
    return this.getStatusText(this.activeCall.status);
  }

  /**
   * Obtient le nom de l'autre participant
   */
  getOtherParticipantName(): string {
    if (!this.activeCall) return '';
    return this.getParticipantName(this.getOtherParticipant());
  }

  /**
   * Obtient l'avatar de l'autre participant
   */
  getOtherParticipantAvatar(): string {
    if (!this.activeCall) return '/assets/images/default-avatar.png';
    return this.getParticipantAvatar(this.getOtherParticipant());
  }

  // === MÉTHODES UTILITAIRES PRIVÉES (RÉUTILISABLES) ===

  /**
   * Obtient l'autre participant (caller ou recipient)
   */
  private getOtherParticipant(): any {
    return this.activeCall?.recipient || this.activeCall?.caller;
  }

  /**
   * Obtient le nom d'un participant avec fallback
   */
  private getParticipantName(participant: any): string {
    return participant?.username || 'Utilisateur';
  }

  /**
   * Obtient l'avatar d'un participant avec fallback
   */
  private getParticipantAvatar(participant: any): string {
    return participant?.image || '/assets/images/default-avatar.png';
  }

  /**
   * Obtient le texte du statut avec fallback
   */
  private getStatusText(status: CallStatus): string {
    const statusMap: Partial<Record<CallStatus, string>> = {
      [CallStatus.RINGING]: 'Sonnerie...',
      [CallStatus.CONNECTED]: 'Connecté',
      [CallStatus.ENDED]: 'Terminé',
      [CallStatus.MISSED]: 'Manqué',
      [CallStatus.REJECTED]: 'Rejeté',
      [CallStatus.FAILED]: 'Échec',
    };
    return statusMap[status] || 'En cours...';
  }
}
