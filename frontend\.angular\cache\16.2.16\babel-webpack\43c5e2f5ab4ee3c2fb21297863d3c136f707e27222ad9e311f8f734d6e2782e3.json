{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/OneDrive/Bureau/Project PI/devBridge/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { BehaviorSubject, throwError, from } from 'rxjs';\nimport { map, catchError, switchMap } from 'rxjs/operators';\nimport { CallType, CallStatus } from '../models/message.model';\nimport { INITIATE_CALL_MUTATION, ACCEPT_CALL_MUTATION, REJECT_CALL_MUTATION, END_CALL_MUTATION, TOGGLE_CALL_MEDIA_MUTATION, INCOMING_CALL_SUBSCRIPTION, CALL_STATUS_CHANGED_SUBSCRIPTION, CALL_SIGNAL_SUBSCRIPTION } from '../graphql/message.graphql';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"apollo-angular\";\nimport * as i2 from \"./logger.service\";\nexport class CallService {\n  constructor(apollo, logger) {\n    this.apollo = apollo;\n    this.logger = logger;\n    // État des appels\n    this.activeCall = new BehaviorSubject(null);\n    this.incomingCall = new BehaviorSubject(null);\n    // Observables publics\n    this.activeCall$ = this.activeCall.asObservable();\n    this.incomingCall$ = this.incomingCall.asObservable();\n    // Propriétés pour la gestion des sons\n    this.sounds = {};\n    this.isPlaying = {};\n    this.muted = false;\n    // États simples pour les médias\n    this.isVideoEnabled = true;\n    this.isAudioEnabled = true;\n    // WebRTC\n    this.peerConnection = null;\n    this.localStream = null;\n    this.remoteStream = null;\n    this.localVideoElement = null;\n    this.remoteVideoElement = null;\n    this.preloadSounds();\n    this.initializeSubscriptions();\n    this.initializeWebRTC();\n  }\n  /**\n   * Initialise les subscriptions avec un délai pour s'assurer que l'authentification est prête\n   */\n  initializeSubscriptions() {\n    // Attendre un peu pour s'assurer que l'authentification est prête\n    setTimeout(() => {\n      this.subscribeToIncomingCalls();\n      this.subscribeToCallStatusChanges();\n      this.subscribeToCallSignals();\n    }, 1000);\n    // Réessayer UNE SEULE FOIS après 10 secondes si nécessaire\n    setTimeout(() => {\n      if (!this.incomingCall.value) {\n        // console.log('🔄 [CallService] Retrying subscription initialization...');\n        this.subscribeToIncomingCalls();\n        this.subscribeToCallStatusChanges();\n        this.subscribeToCallSignals();\n      }\n    }, 10000);\n  }\n  /**\n   * Initialise WebRTC\n   */\n  initializeWebRTC() {\n    console.log('🔧 [CallService] Initializing WebRTC...');\n    // Configuration des serveurs STUN/TURN\n    const configuration = {\n      iceServers: [{\n        urls: 'stun:stun.l.google.com:19302'\n      }, {\n        urls: 'stun:stun1.l.google.com:19302'\n      }]\n    };\n    try {\n      this.peerConnection = new RTCPeerConnection(configuration);\n      this.setupPeerConnectionEvents();\n      console.log('✅ [CallService] WebRTC initialized successfully');\n    } catch (error) {\n      console.error('❌ [CallService] Failed to initialize WebRTC:', error);\n    }\n  }\n  /**\n   * Configure les événements de la PeerConnection\n   */\n  setupPeerConnectionEvents() {\n    if (!this.peerConnection) return;\n    // Quand on reçoit un stream distant\n    this.peerConnection.ontrack = event => {\n      console.log('📺 [CallService] Remote stream received');\n      console.log('🔍 [CallService] Stream details:', {\n        streamId: event.streams[0]?.id,\n        tracks: event.streams[0]?.getTracks().map(t => ({\n          kind: t.kind,\n          enabled: t.enabled,\n          readyState: t.readyState\n        }))\n      });\n      this.remoteStream = event.streams[0];\n      if (this.remoteVideoElement) {\n        console.log('🎥 [CallService] Setting remote stream to video element');\n        this.remoteVideoElement.srcObject = this.remoteStream;\n        // Configurer les événements de l'élément vidéo\n        this.remoteVideoElement.onloadedmetadata = () => {\n          console.log('🎵 [CallService] Remote video metadata loaded');\n        };\n        this.remoteVideoElement.oncanplay = () => {\n          console.log('🎵 [CallService] Remote video can play');\n        };\n        this.remoteVideoElement.onplay = () => {\n          console.log('🎵 [CallService] Remote video started playing');\n        };\n        // Forcer la lecture audio immédiatement\n        try {\n          this.ensureAudioPlayback(this.remoteVideoElement);\n        } catch (error) {\n          console.warn('⚠️ [CallService] ensureAudioPlayback error in ontrack:', error);\n          // Fallback simple : forcer la lecture directement\n          console.log('🔄 [CallService] Trying direct play as fallback');\n          this.remoteVideoElement.play().catch(e => {\n            console.warn('⚠️ [CallService] Direct play failed:', e);\n            // Dernier recours : attendre une interaction utilisateur\n            console.log('⏳ [CallService] Waiting for user interaction to enable audio');\n          });\n        }\n      } else {\n        console.warn('⚠️ [CallService] No remote video element available');\n      }\n    };\n    // Gestion des candidats ICE\n    this.peerConnection.onicecandidate = event => {\n      if (event.candidate) {\n        console.log('🧊 [CallService] ICE candidate generated');\n        // TODO: Envoyer le candidat via WebSocket\n      }\n    };\n    // État de la connexion\n    this.peerConnection.onconnectionstatechange = () => {\n      console.log('🔗 [CallService] Connection state:', this.peerConnection?.connectionState);\n    };\n  }\n  /**\n   * Précharge les sons utilisés dans l'application\n   */\n  preloadSounds() {\n    // Créer des sons synthétiques de haute qualité\n    this.createSyntheticSounds();\n    // Charger le son de notification qui existe encore\n    this.loadSound('notification', 'assets/sounds/notification.mp3');\n    console.log('🎵 [CallService] Beautiful synthetic melodies created for calls');\n  }\n  /**\n   * Crée des sons synthétiques comme fallback\n   */\n  createSyntheticSounds() {\n    try {\n      // Créer un contexte audio pour les sons synthétiques\n      const audioContext = new (window.AudioContext || window.webkitAudioContext)();\n      // Son de sonnerie (mélodie agréable)\n      this.sounds['ringtone-synthetic'] = this.createRingtoneSound(audioContext);\n      // Son de connexion (accord agréable)\n      this.sounds['call-connected-synthetic'] = this.createConnectedSound(audioContext);\n      // Son de fin d'appel (ton descendant)\n      this.sounds['call-end-synthetic'] = this.createEndSound(audioContext);\n      // Son de notification (bip agréable)\n      this.sounds['notification-synthetic'] = this.createNotificationSound(audioContext);\n      console.log('🔊 [CallService] Synthetic sounds created as fallback');\n    } catch (error) {\n      console.warn('⚠️ [CallService] Could not create synthetic sounds:', error);\n    }\n  }\n  /**\n   * Crée une sonnerie agréable (mélodie)\n   */\n  createRingtoneSound(audioContext) {\n    const audio = new Audio();\n    audio.volume = 0.5;\n    let isPlaying = false;\n    let timeoutIds = [];\n    audio.playSynthetic = () => {\n      if (isPlaying) return Promise.resolve();\n      isPlaying = true;\n      const playMelody = () => {\n        if (!isPlaying) return;\n        // Mélodie inspirée de Nokia mais plus moderne : Mi-Ré-Fa#-Sol-Do#-Si-Ré-Do\n        const melody = [{\n          freq: 659.25,\n          duration: 0.125\n        }, {\n          freq: 587.33,\n          duration: 0.125\n        }, {\n          freq: 739.99,\n          duration: 0.25\n        }, {\n          freq: 783.99,\n          duration: 0.25\n        }, {\n          freq: 554.37,\n          duration: 0.125\n        }, {\n          freq: 493.88,\n          duration: 0.125\n        }, {\n          freq: 587.33,\n          duration: 0.25\n        }, {\n          freq: 523.25,\n          duration: 0.25\n        } // Do\n        ];\n\n        let currentTime = audioContext.currentTime;\n        melody.forEach((note, index) => {\n          if (!isPlaying) return;\n          const oscillator = audioContext.createOscillator();\n          const gainNode = audioContext.createGain();\n          oscillator.connect(gainNode);\n          gainNode.connect(audioContext.destination);\n          oscillator.frequency.value = note.freq;\n          oscillator.type = 'square'; // Son plus moderne\n          // Enveloppe ADSR pour un son plus naturel\n          gainNode.gain.setValueAtTime(0, currentTime);\n          gainNode.gain.linearRampToValueAtTime(0.3, currentTime + 0.02);\n          gainNode.gain.linearRampToValueAtTime(0.2, currentTime + note.duration * 0.7);\n          gainNode.gain.exponentialRampToValueAtTime(0.01, currentTime + note.duration);\n          oscillator.start(currentTime);\n          oscillator.stop(currentTime + note.duration);\n          currentTime += note.duration + 0.05; // Petite pause entre les notes\n        });\n        // Répéter la mélodie après une pause\n        const timeoutId = setTimeout(() => {\n          if (isPlaying) {\n            playMelody();\n          }\n        }, (currentTime - audioContext.currentTime + 0.8) * 1000);\n        timeoutIds.push(timeoutId);\n      };\n      playMelody();\n      return Promise.resolve();\n    };\n    audio.stopSynthetic = () => {\n      isPlaying = false;\n      timeoutIds.forEach(id => clearTimeout(id));\n      timeoutIds = [];\n    };\n    return audio;\n  }\n  /**\n   * Crée un son de connexion agréable (mélodie ascendante)\n   */\n  createConnectedSound(audioContext) {\n    const audio = new Audio();\n    audio.volume = 0.5;\n    audio.playSynthetic = () => {\n      // Mélodie ascendante positive : Do-Mi-Sol-Do (octave supérieure)\n      const melody = [{\n        freq: 523.25,\n        duration: 0.15\n      }, {\n        freq: 659.25,\n        duration: 0.15\n      }, {\n        freq: 783.99,\n        duration: 0.15\n      }, {\n        freq: 1046.5,\n        duration: 0.4\n      } // Do (octave supérieure)\n      ];\n\n      let currentTime = audioContext.currentTime;\n      melody.forEach((note, index) => {\n        const oscillator = audioContext.createOscillator();\n        const gainNode = audioContext.createGain();\n        oscillator.connect(gainNode);\n        gainNode.connect(audioContext.destination);\n        oscillator.frequency.value = note.freq;\n        oscillator.type = 'triangle'; // Son plus doux\n        // Enveloppe pour un son naturel\n        gainNode.gain.setValueAtTime(0, currentTime);\n        gainNode.gain.linearRampToValueAtTime(0.25, currentTime + 0.02);\n        gainNode.gain.linearRampToValueAtTime(0.15, currentTime + note.duration * 0.8);\n        gainNode.gain.exponentialRampToValueAtTime(0.01, currentTime + note.duration);\n        oscillator.start(currentTime);\n        oscillator.stop(currentTime + note.duration);\n        currentTime += note.duration * 0.8; // Chevauchement léger des notes\n      });\n\n      return Promise.resolve();\n    };\n    return audio;\n  }\n  /**\n   * Crée un son de fin d'appel (mélodie descendante douce)\n   */\n  createEndSound(audioContext) {\n    const audio = new Audio();\n    audio.volume = 0.4;\n    audio.playSynthetic = () => {\n      // Mélodie descendante douce : Sol-Mi-Do-Sol(grave)\n      const melody = [{\n        freq: 783.99,\n        duration: 0.2\n      }, {\n        freq: 659.25,\n        duration: 0.2\n      }, {\n        freq: 523.25,\n        duration: 0.2\n      }, {\n        freq: 392.0,\n        duration: 0.4\n      } // Sol (octave inférieure)\n      ];\n\n      let currentTime = audioContext.currentTime;\n      melody.forEach((note, index) => {\n        const oscillator = audioContext.createOscillator();\n        const gainNode = audioContext.createGain();\n        oscillator.connect(gainNode);\n        gainNode.connect(audioContext.destination);\n        oscillator.frequency.value = note.freq;\n        oscillator.type = 'sine'; // Son doux pour la fin\n        // Enveloppe douce pour un son apaisant\n        gainNode.gain.setValueAtTime(0, currentTime);\n        gainNode.gain.linearRampToValueAtTime(0.2, currentTime + 0.05);\n        gainNode.gain.linearRampToValueAtTime(0.1, currentTime + note.duration * 0.7);\n        gainNode.gain.exponentialRampToValueAtTime(0.01, currentTime + note.duration);\n        oscillator.start(currentTime);\n        oscillator.stop(currentTime + note.duration);\n        currentTime += note.duration * 0.9; // Léger chevauchement\n      });\n\n      return Promise.resolve();\n    };\n    return audio;\n  }\n  /**\n   * Crée un son de notification agréable (double bip)\n   */\n  createNotificationSound(audioContext) {\n    const audio = new Audio();\n    audio.volume = 0.6;\n    audio.playSynthetic = () => {\n      // Double bip agréable : Do-Sol\n      const notes = [{\n        freq: 523.25,\n        duration: 0.15,\n        delay: 0\n      }, {\n        freq: 783.99,\n        duration: 0.25,\n        delay: 0.2\n      } // Sol\n      ];\n\n      notes.forEach(note => {\n        setTimeout(() => {\n          const oscillator = audioContext.createOscillator();\n          const gainNode = audioContext.createGain();\n          oscillator.connect(gainNode);\n          gainNode.connect(audioContext.destination);\n          oscillator.frequency.value = note.freq;\n          oscillator.type = 'triangle'; // Son doux et agréable\n          const startTime = audioContext.currentTime;\n          // Enveloppe pour un son naturel\n          gainNode.gain.setValueAtTime(0, startTime);\n          gainNode.gain.linearRampToValueAtTime(0.4, startTime + 0.02);\n          gainNode.gain.linearRampToValueAtTime(0.2, startTime + note.duration * 0.7);\n          gainNode.gain.exponentialRampToValueAtTime(0.01, startTime + note.duration);\n          oscillator.start(startTime);\n          oscillator.stop(startTime + note.duration);\n        }, note.delay * 1000);\n      });\n      return Promise.resolve();\n    };\n    return audio;\n  }\n  /**\n   * Charge un fichier audio\n   */\n  loadSound(name, path) {\n    try {\n      const audio = new Audio();\n      // Configuration de l'audio\n      audio.preload = 'auto';\n      audio.volume = 0.7;\n      // Gérer les événements de chargement\n      audio.addEventListener('canplaythrough', () => {\n        console.log(`✅ [CallService] Sound ${name} loaded successfully from ${path}`);\n      });\n      audio.addEventListener('error', e => {\n        console.error(`❌ [CallService] Error loading sound ${name} from ${path}:`, e);\n        console.log(`🔄 [CallService] Trying to load ${name} with different approach...`);\n        // Essayer de charger avec un chemin relatif\n        const altPath = path.startsWith('/') ? path.substring(1) : path;\n        if (altPath !== path) {\n          setTimeout(() => {\n            audio.src = altPath;\n            audio.load();\n          }, 100);\n        }\n      });\n      audio.addEventListener('ended', () => {\n        this.isPlaying[name] = false;\n        console.log(`🔇 [CallService] Sound ${name} ended`);\n      });\n      // Définir la source et charger\n      audio.src = path;\n      audio.load();\n      this.sounds[name] = audio;\n      this.isPlaying[name] = false;\n      console.log(`🔊 [CallService] Loading sound ${name} from ${path}`);\n    } catch (error) {\n      console.error(`❌ [CallService] Error creating audio element for ${name}:`, error);\n    }\n  }\n  /**\n   * Joue un son\n   */\n  play(name, loop = false) {\n    if (this.muted) {\n      console.log(`🔇 [CallService] Sound ${name} muted`);\n      return;\n    }\n    try {\n      // Pour les sons d'appel, utiliser directement les versions synthétiques\n      let sound;\n      if (name === 'ringtone' || name === 'call-connected' || name === 'call-end') {\n        const syntheticName = `${name}-synthetic`;\n        sound = this.sounds[syntheticName];\n        if (sound) {\n          console.log(`🎵 [CallService] Using beautiful synthetic melody for ${name}`);\n        }\n      } else {\n        // Pour les autres sons (comme notification), essayer d'abord le fichier\n        sound = this.sounds[name];\n        if (!sound || sound.error) {\n          const syntheticName = `${name}-synthetic`;\n          sound = this.sounds[syntheticName];\n          if (sound) {\n            console.log(`🔊 [CallService] Using synthetic sound for ${name}`);\n          }\n        }\n      }\n      if (!sound) {\n        console.warn(`🔊 [CallService] Sound ${name} not found (neither original nor synthetic)`);\n        // Créer un bip simple comme dernier recours\n        this.playSimpleBeep(name === 'ringtone' ? 800 : name === 'call-connected' ? 1000 : 400);\n        return;\n      }\n      sound.loop = loop;\n      sound.volume = 0.7;\n      if (!this.isPlaying[name]) {\n        console.log(`🔊 [CallService] Playing sound: ${name} (loop: ${loop})`);\n        // Vérifier si c'est un son synthétique\n        if (sound.playSynthetic) {\n          sound.playSynthetic().then(() => {\n            console.log(`✅ [CallService] Synthetic sound ${name} started successfully`);\n            this.isPlaying[name] = true;\n            // Pour la sonnerie synthétique, elle gère sa propre boucle\n            if (name === 'ringtone' && !loop) {\n              // Si ce n'est pas en boucle, arrêter après un certain temps\n              setTimeout(() => {\n                this.isPlaying[name] = false;\n              }, 3000);\n            } else if (name !== 'ringtone') {\n              // Pour les autres sons, arrêter après leur durée\n              setTimeout(() => {\n                this.isPlaying[name] = false;\n              }, name === 'call-connected' ? 1200 : 1000);\n            }\n          }).catch(error => {\n            console.error(`❌ [CallService] Error playing synthetic sound ${name}:`, error);\n          });\n        } else {\n          // Son normal\n          sound.currentTime = 0;\n          sound.play().then(() => {\n            console.log(`✅ [CallService] Sound ${name} started successfully`);\n            this.isPlaying[name] = true;\n          }).catch(error => {\n            console.error(`❌ [CallService] Error playing sound ${name}:`, error);\n            // Essayer le son synthétique en cas d'échec\n            const syntheticName = `${name}-synthetic`;\n            const syntheticSound = this.sounds[syntheticName];\n            if (syntheticSound && syntheticSound.playSynthetic) {\n              console.log(`🔄 [CallService] Falling back to synthetic sound for ${name}`);\n              this.play(name, loop);\n            } else {\n              // Dernier recours : bip simple\n              this.playSimpleBeep(name === 'ringtone' ? 800 : name === 'call-connected' ? 1000 : 400);\n            }\n          });\n        }\n      } else {\n        console.log(`🔊 [CallService] Sound ${name} already playing`);\n      }\n    } catch (error) {\n      console.error(`❌ [CallService] Error in play method for ${name}:`, error);\n      // Dernier recours\n      this.playSimpleBeep(name === 'ringtone' ? 800 : name === 'call-connected' ? 1000 : 400);\n    }\n  }\n  /**\n   * Joue un bip simple comme dernier recours\n   */\n  playSimpleBeep(frequency) {\n    try {\n      const audioContext = new (window.AudioContext || window.webkitAudioContext)();\n      const oscillator = audioContext.createOscillator();\n      const gainNode = audioContext.createGain();\n      oscillator.connect(gainNode);\n      gainNode.connect(audioContext.destination);\n      oscillator.frequency.value = frequency;\n      oscillator.type = 'sine';\n      gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);\n      gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);\n      oscillator.start(audioContext.currentTime);\n      oscillator.stop(audioContext.currentTime + 0.3);\n      console.log(`🔊 [CallService] Playing simple beep at ${frequency}Hz`);\n    } catch (error) {\n      console.error('❌ [CallService] Could not play simple beep:', error);\n    }\n  }\n  /**\n   * Arrête un son\n   */\n  stop(name) {\n    try {\n      let sound = this.sounds[name];\n      // Essayer aussi la version synthétique\n      if (!sound) {\n        sound = this.sounds[`${name}-synthetic`];\n      }\n      if (!sound) {\n        console.warn(`🔊 [CallService] Sound ${name} not found for stopping`);\n        return;\n      }\n      if (this.isPlaying[name]) {\n        console.log(`🔇 [CallService] Stopping sound: ${name}`);\n        // Arrêter le son synthétique si c'est le cas\n        if (sound.stopSynthetic) {\n          sound.stopSynthetic();\n        } else {\n          sound.pause();\n          sound.currentTime = 0;\n        }\n        this.isPlaying[name] = false;\n      } else {\n        console.log(`🔇 [CallService] Sound ${name} was not playing`);\n      }\n    } catch (error) {\n      console.error(`❌ [CallService] Error stopping sound ${name}:`, error);\n    }\n  }\n  /**\n   * S'abonne aux appels entrants\n   */\n  subscribeToIncomingCalls() {\n    // console.log('🔔 [CallService] Setting up incoming call subscription...');\n    try {\n      this.apollo.subscribe({\n        query: INCOMING_CALL_SUBSCRIPTION,\n        errorPolicy: 'all' // Continuer même en cas d'erreur\n      }).subscribe({\n        next: ({\n          data,\n          errors\n        }) => {\n          if (errors) {\n            console.warn('⚠️ [CallService] GraphQL errors in subscription:', errors);\n          }\n          if (data?.incomingCall) {\n            // console.log('📞 [CallService] Incoming call received:', data.incomingCall.id);\n            this.handleIncomingCall(data.incomingCall);\n          }\n        },\n        error: error => {\n          console.error('❌ [CallService] Error in incoming call subscription:', error);\n          // Réessayer UNE SEULE FOIS après 10 secondes\n          setTimeout(() => {\n            this.subscribeToIncomingCalls();\n          }, 10000);\n        },\n        complete: () => {\n          // console.log('🔚 [CallService] Incoming call subscription completed');\n          // Réessayer si la subscription se ferme de manière inattendue\n          setTimeout(() => {\n            this.subscribeToIncomingCalls();\n          }, 5000);\n        }\n      });\n    } catch (error) {\n      console.error('❌ [CallService] Failed to create subscription:', error);\n      // Réessayer après 10 secondes\n      setTimeout(() => {\n        this.subscribeToIncomingCalls();\n      }, 10000);\n    }\n  }\n  /**\n   * Méthode publique pour forcer la réinitialisation de la subscription\n   */\n  reinitializeSubscription() {\n    // console.log('🔄 [CallService] Manually reinitializing subscription...');\n    this.subscribeToIncomingCalls();\n    this.subscribeToCallStatusChanges();\n    this.subscribeToCallSignals();\n  }\n  /**\n   * S'abonne aux signaux WebRTC (offres, réponses, candidats ICE)\n   */\n  subscribeToCallSignals() {\n    console.log('📡 [CallService] Setting up call signal subscription...');\n    try {\n      this.apollo.subscribe({\n        query: CALL_SIGNAL_SUBSCRIPTION,\n        variables: {\n          callId: this.currentCallId || null\n        },\n        errorPolicy: 'all'\n      }).subscribe({\n        next: ({\n          data,\n          errors\n        }) => {\n          if (errors) {\n            console.warn('⚠️ [CallService] Call signal subscription errors:', errors);\n          }\n          if (data?.callSignal) {\n            console.log('📡 [CallService] Received call signal:', data.callSignal);\n            this.handleCallSignal(data.callSignal);\n          }\n        },\n        error: error => {\n          console.error('❌ [CallService] Error in call signal subscription:', error);\n          // Réessayer après 5 secondes\n          setTimeout(() => {\n            this.subscribeToCallSignals();\n          }, 5000);\n        },\n        complete: () => {\n          console.log('🔚 [CallService] Call signal subscription completed');\n          // Réessayer si la subscription se ferme\n          setTimeout(() => {\n            this.subscribeToCallSignals();\n          }, 2000);\n        }\n      });\n    } catch (error) {\n      console.error('❌ [CallService] Failed to create call signal subscription:', error);\n      setTimeout(() => {\n        this.subscribeToCallSignals();\n      }, 3000);\n    }\n  }\n  /**\n   * Gère les signaux WebRTC reçus\n   */\n  handleCallSignal(signal) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      console.log('🔄 [CallService] Handling call signal:', signal.type);\n      try {\n        switch (signal.type) {\n          case 'offer':\n            yield _this.handleRemoteOffer(signal);\n            break;\n          case 'answer':\n            yield _this.handleRemoteAnswer(signal);\n            break;\n          case 'ice-candidate':\n            yield _this.handleRemoteIceCandidate(signal);\n            break;\n          default:\n            console.warn('⚠️ [CallService] Unknown signal type:', signal.type);\n        }\n      } catch (error) {\n        console.error('❌ [CallService] Error handling call signal:', error);\n      }\n    })();\n  }\n  /**\n   * Gère une offre WebRTC distante\n   */\n  handleRemoteOffer(signal) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      console.log('📥 [CallService] Handling remote offer');\n      if (!_this2.peerConnection) {\n        console.log('🔧 [CallService] Initializing WebRTC for remote offer');\n        _this2.initializeWebRTC();\n      }\n      const offer = JSON.parse(signal.data);\n      yield _this2.peerConnection.setRemoteDescription(offer);\n      console.log('✅ [CallService] Remote offer set successfully');\n    })();\n  }\n  /**\n   * Gère une réponse WebRTC distante\n   */\n  handleRemoteAnswer(signal) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      console.log('📥 [CallService] Handling remote answer');\n      if (!_this3.peerConnection) {\n        console.error('❌ [CallService] No peer connection for remote answer');\n        return;\n      }\n      const answer = JSON.parse(signal.data);\n      yield _this3.peerConnection.setRemoteDescription(answer);\n      console.log('✅ [CallService] Remote answer set successfully');\n    })();\n  }\n  /**\n   * Gère un candidat ICE distant\n   */\n  handleRemoteIceCandidate(signal) {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      console.log('🧊 [CallService] Handling remote ICE candidate');\n      if (!_this4.peerConnection) {\n        console.error('❌ [CallService] No peer connection for ICE candidate');\n        return;\n      }\n      const candidate = JSON.parse(signal.data);\n      yield _this4.peerConnection.addIceCandidate(candidate);\n      console.log('✅ [CallService] Remote ICE candidate added successfully');\n    })();\n  }\n  /**\n   * Méthode de test pour vérifier les sons\n   */\n  testSounds() {\n    console.log('🧪 [CallService] Testing sounds...');\n    // Test de la sonnerie\n    console.log('🔊 Testing ringtone...');\n    this.play('ringtone', true);\n    setTimeout(() => {\n      this.stop('ringtone');\n      console.log('🔊 Testing call-connected...');\n      this.play('call-connected');\n    }, 3000);\n    setTimeout(() => {\n      console.log('🔊 Testing call-end...');\n      this.play('call-end');\n    }, 5000);\n  }\n  /**\n   * Test spécifique pour la sonnerie\n   */\n  testRingtone() {\n    console.log('🧪 [CallService] Testing ringtone specifically...');\n    console.log('🔊 [CallService] Available sounds:', Object.keys(this.sounds));\n    // Vérifier si le son est chargé\n    const ringtone = this.sounds['ringtone'];\n    const ringtoneSynthetic = this.sounds['ringtone-synthetic'];\n    if (ringtone) {\n      console.log('✅ [CallService] Ringtone MP3 found:', {\n        src: ringtone.src,\n        readyState: ringtone.readyState,\n        error: ringtone.error,\n        duration: ringtone.duration\n      });\n    } else {\n      console.log('❌ [CallService] Ringtone MP3 not found');\n    }\n    if (ringtoneSynthetic) {\n      console.log('✅ [CallService] Ringtone synthetic found');\n    }\n    // Jouer la sonnerie (elle va automatiquement utiliser le fallback si nécessaire)\n    console.log('🎵 [CallService] Playing beautiful ringtone melody...');\n    this.play('ringtone', true);\n    // Arrêter après 8 secondes pour entendre plusieurs cycles de la mélodie\n    setTimeout(() => {\n      this.stop('ringtone');\n      console.log('🔇 [CallService] Ringtone test stopped');\n    }, 8000);\n  }\n  /**\n   * Test tous les nouveaux sons améliorés\n   */\n  testBeautifulSounds() {\n    console.log('🧪 [CallService] Testing all beautiful sounds...');\n    // Test de la sonnerie (mélodie)\n    // console.log('🎵 Testing beautiful ringtone melody...');\n    this.play('ringtone', true);\n    setTimeout(() => {\n      this.stop('ringtone');\n      // console.log('🎵 Testing beautiful connection sound...');\n      this.play('call-connected');\n    }, 4000);\n    setTimeout(() => {\n      // console.log('🎵 Testing beautiful end sound...');\n      this.play('call-end');\n    }, 6000);\n    setTimeout(() => {\n      // console.log('🎵 Testing beautiful notification sound...');\n      this.play('notification');\n    }, 8000);\n    setTimeout(() => {\n      console.log('🎵 All sound tests completed!');\n    }, 10000);\n  }\n  /**\n   * Joue le son de notification (méthode publique)\n   */\n  playNotification() {\n    console.log('🔔 [CallService] Playing notification sound...');\n    this.play('notification');\n  }\n  /**\n   * S'abonne aux changements de statut d'appel\n   */\n  subscribeToCallStatusChanges() {\n    console.log('📞 [CallService] Setting up call status change subscription...');\n    try {\n      this.apollo.subscribe({\n        query: CALL_STATUS_CHANGED_SUBSCRIPTION,\n        errorPolicy: 'all'\n      }).subscribe({\n        next: ({\n          data,\n          errors\n        }) => {\n          if (errors) {\n            console.warn('⚠️ [CallService] GraphQL errors in call status subscription:', errors);\n          }\n          if (data?.callStatusChanged) {\n            console.log('📞 [CallService] Call status changed:', data.callStatusChanged);\n            this.handleCallStatusChange(data.callStatusChanged);\n          }\n        },\n        error: error => {\n          console.error('❌ [CallService] Error in call status subscription:', error);\n          // Réessayer après 5 secondes\n          setTimeout(() => {\n            this.subscribeToCallStatusChanges();\n          }, 5000);\n        },\n        complete: () => {\n          console.log('🔚 [CallService] Call status subscription completed');\n          // Réessayer si la subscription se ferme\n          setTimeout(() => {\n            this.subscribeToCallStatusChanges();\n          }, 2000);\n        }\n      });\n    } catch (error) {\n      console.error('❌ [CallService] Failed to create call status subscription:', error);\n      setTimeout(() => {\n        this.subscribeToCallStatusChanges();\n      }, 3000);\n    }\n  }\n  /**\n   * Gère un appel entrant\n   */\n  handleIncomingCall(call) {\n    console.log('🔔 [CallService] Handling incoming call:', {\n      callId: call.id,\n      callType: call.type,\n      caller: call.caller?.username,\n      conversationId: call.conversationId\n    });\n    this.incomingCall.next(call);\n    this.play('ringtone', true);\n    console.log('🔊 [CallService] Ringtone started, call notification sent to UI');\n  }\n  /**\n   * Gère les changements de statut d'appel\n   */\n  handleCallStatusChange(call) {\n    console.log('📞 [CallService] Call status changed:', call.status);\n    switch (call.status) {\n      case CallStatus.REJECTED:\n        console.log('❌ [CallService] Call was rejected');\n        this.stop('ringtone');\n        this.play('call-end');\n        this.activeCall.next(null);\n        this.incomingCall.next(null);\n        break;\n      case CallStatus.ENDED:\n        console.log('📞 [CallService] Call ended');\n        this.stopAllSounds();\n        this.play('call-end');\n        this.activeCall.next(null);\n        this.incomingCall.next(null);\n        break;\n      case CallStatus.CONNECTED:\n        console.log('✅ [CallService] Call connected');\n        this.stop('ringtone');\n        this.play('call-connected');\n        this.activeCall.next(call);\n        this.incomingCall.next(null);\n        break;\n      case CallStatus.RINGING:\n        console.log('📞 [CallService] Call is ringing');\n        this.play('ringtone', true);\n        break;\n      default:\n        console.log('📞 [CallService] Unknown call status:', call.status);\n        break;\n    }\n  }\n  /**\n   * Obtient les médias utilisateur (caméra/micro)\n   */\n  getUserMedia(callType) {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      console.log('🎥 [CallService] Getting user media for:', callType);\n      const constraints = {\n        audio: true,\n        video: callType === CallType.VIDEO\n      };\n      try {\n        const stream = yield navigator.mediaDevices.getUserMedia(constraints);\n        console.log('✅ [CallService] User media obtained');\n        console.log('🔍 [CallService] Stream tracks:', stream.getTracks().map(t => ({\n          kind: t.kind,\n          enabled: t.enabled,\n          readyState: t.readyState\n        })));\n        _this5.localStream = stream;\n        // Afficher le stream local si on a un élément vidéo\n        if (_this5.localVideoElement && callType === CallType.VIDEO) {\n          _this5.localVideoElement.srcObject = stream;\n        }\n        return stream;\n      } catch (error) {\n        console.error('❌ [CallService] Failed to get user media:', error);\n        throw new Error(\"Impossible d'accéder à la caméra/microphone\");\n      }\n    })();\n  }\n  /**\n   * Ajoute le stream local à la PeerConnection\n   */\n  addLocalStreamToPeerConnection(stream) {\n    if (!this.peerConnection) {\n      console.error('❌ [CallService] No peer connection available');\n      return;\n    }\n    console.log('📤 [CallService] Adding local stream to peer connection');\n    stream.getTracks().forEach(track => {\n      this.peerConnection.addTrack(track, stream);\n    });\n  }\n  /**\n   * Crée une offre WebRTC\n   */\n  createOffer() {\n    var _this6 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this6.peerConnection) {\n        throw new Error('No peer connection available');\n      }\n      console.log('📝 [CallService] Creating WebRTC offer');\n      const offer = yield _this6.peerConnection.createOffer();\n      yield _this6.peerConnection.setLocalDescription(offer);\n      return offer;\n    })();\n  }\n  /**\n   * Crée une réponse WebRTC\n   */\n  createAnswer(offer) {\n    var _this7 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this7.peerConnection) {\n        throw new Error('No peer connection available');\n      }\n      console.log('📝 [CallService] Creating WebRTC answer');\n      yield _this7.peerConnection.setRemoteDescription(offer);\n      const answer = yield _this7.peerConnection.createAnswer();\n      yield _this7.peerConnection.setLocalDescription(answer);\n      return answer;\n    })();\n  }\n  /**\n   * Configure les éléments vidéo\n   */\n  setVideoElements(localVideo, remoteVideo) {\n    console.log('📺 [CallService] Setting video elements');\n    this.localVideoElement = localVideo;\n    this.remoteVideoElement = remoteVideo;\n    // Configurer les éléments pour la lecture audio\n    try {\n      this.setupAudioPlayback(localVideo, remoteVideo);\n    } catch (error) {\n      console.warn('⚠️ [CallService] setupAudioPlayback error:', error);\n    }\n    // Si on a déjà des streams, les connecter IMMÉDIATEMENT\n    if (this.localStream && localVideo) {\n      console.log('🔗 [CallService] Attaching local stream to video element');\n      localVideo.srcObject = this.localStream;\n      localVideo.muted = true; // Local toujours muet pour éviter l'écho\n      localVideo.volume = 0;\n      localVideo.autoplay = true;\n      try {\n        localVideo.play();\n        console.log('✅ [CallService] Local video playing');\n      } catch (error) {\n        console.warn('⚠️ [CallService] Local video play error:', error);\n      }\n    } else {\n      console.warn('⚠️ [CallService] No local stream or video element available');\n    }\n    if (this.remoteStream && remoteVideo) {\n      console.log('🔗 [CallService] Attaching remote stream to video element');\n      remoteVideo.srcObject = this.remoteStream;\n      remoteVideo.muted = false; // Remote avec audio\n      remoteVideo.volume = 1;\n      remoteVideo.autoplay = true;\n      try {\n        remoteVideo.play();\n        console.log('✅ [CallService] Remote video playing');\n      } catch (error) {\n        console.warn('⚠️ [CallService] Remote video play error:', error);\n      }\n    } else {\n      console.warn('⚠️ [CallService] No remote stream or video element available');\n    }\n    // Forcer l'attachement après un délai pour s'assurer que les streams sont prêts\n    setTimeout(() => {\n      this.forceStreamAttachment();\n    }, 1000);\n  }\n  /**\n   * Force l'attachement des streams aux éléments vidéo\n   */\n  forceStreamAttachment() {\n    console.log('🔄 [CallService] Forcing stream attachment...');\n    // Forcer l'attachement du stream local\n    if (this.localStream && this.localVideoElement) {\n      if (!this.localVideoElement.srcObject) {\n        console.log('🔗 [CallService] Force attaching local stream');\n        this.localVideoElement.srcObject = this.localStream;\n        this.localVideoElement.muted = true;\n        this.localVideoElement.autoplay = true;\n        this.localVideoElement.play().catch(e => console.warn('⚠️ [CallService] Force local play failed:', e));\n      }\n    }\n    // Forcer l'attachement du stream distant\n    if (this.remoteStream && this.remoteVideoElement) {\n      if (!this.remoteVideoElement.srcObject) {\n        console.log('🔗 [CallService] Force attaching remote stream');\n        this.remoteVideoElement.srcObject = this.remoteStream;\n        this.remoteVideoElement.muted = false;\n        this.remoteVideoElement.volume = 1;\n        this.remoteVideoElement.autoplay = true;\n        this.remoteVideoElement.play().catch(e => console.warn('⚠️ [CallService] Force remote play failed:', e));\n      }\n    }\n    // Vérifier l'état après forçage\n    console.log('📊 [CallService] Stream attachment status:', {\n      localAttached: !!this.localVideoElement?.srcObject,\n      remoteAttached: !!this.remoteVideoElement?.srcObject,\n      localStreamExists: !!this.localStream,\n      remoteStreamExists: !!this.remoteStream\n    });\n  }\n  /**\n   * Configure la lecture audio pour les éléments vidéo\n   */\n  setupAudioPlayback(localVideo, remoteVideo) {\n    console.log('🔊 [CallService] Setting up audio playback');\n    // Configurer les propriétés audio\n    localVideo.volume = 0; // Local toujours muet pour éviter l'écho\n    remoteVideo.volume = 1; // Remote avec volume maximum\n    // Forcer l'autoplay\n    localVideo.autoplay = true;\n    remoteVideo.autoplay = true;\n    // Événements pour débugger\n    remoteVideo.addEventListener('loadedmetadata', () => {\n      console.log('🎵 [CallService] Remote video metadata loaded');\n    });\n    remoteVideo.addEventListener('canplay', () => {\n      console.log('🎵 [CallService] Remote video can play');\n      this.ensureAudioPlayback(remoteVideo);\n    });\n    remoteVideo.addEventListener('play', () => {\n      console.log('🎵 [CallService] Remote video started playing');\n    });\n    remoteVideo.addEventListener('pause', () => {\n      console.log('⏸️ [CallService] Remote video paused');\n    });\n  }\n  /**\n   * Force la lecture audio\n   */\n  ensureAudioPlayback(videoElement) {\n    console.log('🔊 [CallService] Ensuring audio playback for element:', videoElement === this.localVideoElement ? 'local' : 'remote');\n    // Forcer la lecture\n    videoElement.play().then(() => {\n      console.log('✅ [CallService] Video/audio playback started successfully');\n    }).catch(error => {\n      console.warn('⚠️ [CallService] Autoplay prevented, user interaction required:', error);\n      // Essayer de jouer après interaction utilisateur\n      const playOnInteraction = () => {\n        videoElement.play().then(() => {\n          console.log('✅ [CallService] Video/audio playback started after user interaction');\n          document.removeEventListener('click', playOnInteraction);\n          document.removeEventListener('keydown', playOnInteraction);\n        }).catch(err => {\n          console.error('❌ [CallService] Failed to play after interaction:', err);\n        });\n      };\n      document.addEventListener('click', playOnInteraction);\n      document.addEventListener('keydown', playOnInteraction);\n    });\n  }\n  /**\n   * Initie un appel WebRTC\n   */\n  initiateCall(recipientId, callType, conversationId) {\n    console.log('🔄 [CallService] Initiating call:', {\n      recipientId,\n      callType,\n      conversationId\n    });\n    if (!recipientId) {\n      const error = new Error('Recipient ID is required');\n      console.error('❌ [CallService] initiateCall error:', error);\n      return throwError(() => error);\n    }\n    // Générer un ID unique pour l'appel\n    const callId = `call_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n    // Créer une vraie offre WebRTC\n    return from(this.createWebRTCOffer(callType)).pipe(switchMap(offer => {\n      const variables = {\n        recipientId,\n        callType: callType,\n        callId,\n        offer: JSON.stringify(offer),\n        conversationId\n      };\n      console.log('📤 [CallService] Sending initiate call mutation:', variables);\n      return this.apollo.mutate({\n        mutation: INITIATE_CALL_MUTATION,\n        variables\n      }).pipe(map(result => {\n        console.log('✅ [CallService] Call initiated successfully:', result);\n        if (!result.data?.initiateCall) {\n          throw new Error('No call data received from server');\n        }\n        const call = result.data.initiateCall;\n        console.log('📞 [CallService] Call details:', {\n          id: call.id,\n          type: call.type,\n          status: call.status,\n          caller: call.caller?.username,\n          recipient: call.recipient?.username\n        });\n        // Mettre à jour l'état local\n        this.activeCall.next(call);\n        return call;\n      }), catchError(error => {\n        console.error('❌ [CallService] initiateCall error:', error);\n        this.logger.error('Error initiating call:', error);\n        let errorMessage = \"Erreur lors de l'initiation de l'appel\";\n        if (error.networkError) {\n          errorMessage = 'Erreur de connexion réseau';\n        } else if (error.graphQLErrors?.length > 0) {\n          errorMessage = error.graphQLErrors[0].message || errorMessage;\n        }\n        return throwError(() => new Error(errorMessage));\n      }));\n    }), catchError(error => {\n      console.error('❌ [CallService] WebRTC error:', error);\n      return throwError(() => new Error('Erreur WebRTC: ' + error.message));\n    }));\n  }\n  /**\n   * Crée une offre WebRTC complète avec médias\n   */\n  createWebRTCOffer(callType) {\n    var _this8 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        // Réinitialiser la PeerConnection si nécessaire\n        if (!_this8.peerConnection) {\n          _this8.initializeWebRTC();\n        }\n        // Obtenir les médias utilisateur\n        const stream = yield _this8.getUserMedia(callType);\n        // Ajouter le stream à la PeerConnection\n        _this8.addLocalStreamToPeerConnection(stream);\n        // Créer l'offre\n        const offer = yield _this8.createOffer();\n        console.log('✅ [CallService] WebRTC offer created successfully');\n        return offer;\n      } catch (error) {\n        console.error('❌ [CallService] Failed to create WebRTC offer:', error);\n        throw error;\n      }\n    })();\n  }\n  /**\n   * Accepte un appel entrant\n   */\n  acceptCall(incomingCall) {\n    console.log('🔄 [CallService] Accepting call:', incomingCall.id);\n    // Créer une vraie réponse WebRTC\n    return from(this.createWebRTCAnswer(incomingCall)).pipe(switchMap(answer => {\n      return this.apollo.mutate({\n        mutation: ACCEPT_CALL_MUTATION,\n        variables: {\n          callId: incomingCall.id,\n          answer: JSON.stringify(answer)\n        }\n      }).pipe(switchMap(result => {\n        console.log('✅ [CallService] Call accepted successfully:', result);\n        if (!result.data?.acceptCall) {\n          throw new Error('No call data received from server');\n        }\n        const call = result.data.acceptCall;\n        // Arrêter la sonnerie\n        this.stop('ringtone');\n        this.play('call-connected');\n        // Démarrer les médias pour l'appel de manière asynchrone\n        return from(this.startMediaForCall(incomingCall, call));\n      }), catchError(error => {\n        console.error('❌ [CallService] acceptCall error:', error);\n        this.logger.error('Error accepting call:', error);\n        return throwError(() => new Error(\"Erreur lors de l'acceptation de l'appel\"));\n      }));\n    }), catchError(error => {\n      console.error('❌ [CallService] WebRTC answer error:', error);\n      return throwError(() => new Error('Erreur WebRTC: ' + error.message));\n    }));\n  }\n  /**\n   * Crée une réponse WebRTC complète avec médias\n   */\n  createWebRTCAnswer(incomingCall) {\n    var _this9 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        console.log('🔄 [CallService] Creating WebRTC answer for incoming call:', {\n          callId: incomingCall.id,\n          callType: incomingCall.type,\n          hasOffer: !!incomingCall.offer,\n          offerLength: incomingCall.offer?.length || 0\n        });\n        // Réinitialiser la PeerConnection si nécessaire\n        if (!_this9.peerConnection) {\n          console.log('🔧 [CallService] Initializing new PeerConnection for answer');\n          _this9.initializeWebRTC();\n        }\n        // Obtenir les médias utilisateur\n        console.log('🎥 [CallService] Getting user media for answer...');\n        const stream = yield _this9.getUserMedia(incomingCall.type);\n        // Ajouter le stream à la PeerConnection\n        console.log('📤 [CallService] Adding local stream to PeerConnection for answer');\n        _this9.addLocalStreamToPeerConnection(stream);\n        // Récupérer l'offre depuis l'appel entrant\n        if (!incomingCall.offer) {\n          throw new Error('No offer received in incoming call');\n        }\n        const offer = JSON.parse(incomingCall.offer);\n        if (!offer || !offer.type || !offer.sdp) {\n          throw new Error('Invalid offer format received');\n        }\n        // Créer la réponse\n        const answer = yield _this9.createAnswer(offer);\n        console.log('✅ [CallService] WebRTC answer created successfully');\n        return answer;\n      } catch (error) {\n        console.error('❌ [CallService] Failed to create WebRTC answer:', error);\n        throw error;\n      }\n    })();\n  }\n  /**\n   * Rejette un appel entrant\n   */\n  rejectCall(callId, reason) {\n    console.log('🔄 [CallService] Rejecting call:', callId, reason);\n    return this.apollo.mutate({\n      mutation: REJECT_CALL_MUTATION,\n      variables: {\n        callId,\n        reason: reason || 'User rejected'\n      }\n    }).pipe(map(result => {\n      console.log('✅ [CallService] Call rejected successfully:', result);\n      if (!result.data?.rejectCall) {\n        throw new Error('No response received from server');\n      }\n      // Mettre à jour l'état local\n      this.incomingCall.next(null);\n      this.activeCall.next(null);\n      // Arrêter la sonnerie\n      this.stop('ringtone');\n      return result.data.rejectCall;\n    }), catchError(error => {\n      console.error('❌ [CallService] rejectCall error:', error);\n      this.logger.error('Error rejecting call:', error);\n      return throwError(() => new Error(\"Erreur lors du rejet de l'appel\"));\n    }));\n  }\n  /**\n   * Termine un appel en cours\n   */\n  endCall(callId) {\n    console.log('🔄 [CallService] Ending call:', callId);\n    return this.apollo.mutate({\n      mutation: END_CALL_MUTATION,\n      variables: {\n        callId,\n        feedback: null // Pas de feedback pour l'instant\n      }\n    }).pipe(map(result => {\n      console.log('✅ [CallService] Call ended successfully:', result);\n      if (!result.data?.endCall) {\n        throw new Error('No response received from server');\n      }\n      // Mettre à jour l'état local\n      this.activeCall.next(null);\n      this.incomingCall.next(null);\n      // Arrêter tous les sons\n      this.stopAllSounds();\n      this.play('call-end');\n      // Nettoyer les ressources WebRTC\n      this.cleanupWebRTC();\n      return result.data.endCall;\n    }), catchError(error => {\n      console.error('❌ [CallService] endCall error:', error);\n      this.logger.error('Error ending call:', error);\n      return throwError(() => new Error(\"Erreur lors de la fin de l'appel\"));\n    }));\n  }\n  /**\n   * Bascule l'état des médias (audio/vidéo) pendant un appel\n   */\n  toggleMedia(callId, enableVideo, enableAudio) {\n    console.log('🔄 [CallService] Toggling media:', {\n      callId,\n      enableVideo,\n      enableAudio\n    });\n    return this.apollo.mutate({\n      mutation: TOGGLE_CALL_MEDIA_MUTATION,\n      variables: {\n        callId,\n        video: enableVideo,\n        audio: enableAudio\n      }\n    }).pipe(map(result => {\n      console.log('✅ [CallService] Media toggled successfully:', result);\n      if (!result.data?.toggleCallMedia) {\n        throw new Error('No response received from server');\n      }\n      return result.data.toggleCallMedia;\n    }), catchError(error => {\n      console.error('❌ [CallService] toggleMedia error:', error);\n      this.logger.error('Error toggling media:', error);\n      return throwError(() => new Error('Erreur lors du changement des médias'));\n    }));\n  }\n  /**\n   * Démarre les médias pour un appel accepté\n   */\n  startMediaForCall(incomingCall, call) {\n    var _this0 = this;\n    return _asyncToGenerator(function* () {\n      console.log('🎥 [CallService] Call connected - playing connection sound');\n      // Jouer le son de connexion\n      _this0.play('call-connected');\n      // Mettre à jour l'état local\n      _this0.activeCall.next(call);\n      _this0.incomingCall.next(null); // Supprimer l'appel entrant\n      return call;\n    })();\n  }\n  /**\n   * Active/désactive la vidéo\n   */\n  toggleVideo() {\n    this.isVideoEnabled = !this.isVideoEnabled;\n    console.log('📹 [CallService] Video toggled:', this.isVideoEnabled);\n    return this.isVideoEnabled;\n  }\n  /**\n   * Active/désactive l'audio\n   */\n  toggleAudio() {\n    this.isAudioEnabled = !this.isAudioEnabled;\n    console.log('🎤 [CallService] Audio toggled:', this.isAudioEnabled);\n    return this.isAudioEnabled;\n  }\n  /**\n   * Obtient l'état de la vidéo\n   */\n  getVideoEnabled() {\n    return this.isVideoEnabled;\n  }\n  /**\n   * Obtient l'état de l'audio\n   */\n  getAudioEnabled() {\n    return this.isAudioEnabled;\n  }\n  /**\n   * Arrête tous les sons\n   */\n  stopAllSounds() {\n    console.log('🔇 [CallService] Stopping all sounds');\n    Object.keys(this.sounds).forEach(name => {\n      this.stop(name);\n    });\n  }\n  /**\n   * Active les sons après interaction utilisateur (pour contourner les restrictions du navigateur)\n   */\n  enableSounds() {\n    console.log('🔊 [CallService] Enabling sounds after user interaction');\n    Object.values(this.sounds).forEach(sound => {\n      if (sound) {\n        sound.muted = false;\n        sound.volume = 0.7;\n        // Jouer et arrêter immédiatement pour \"débloquer\" l'audio\n        sound.play().then(() => {\n          sound.pause();\n          sound.currentTime = 0;\n        }).catch(() => {\n          // Ignorer les erreurs ici\n        });\n      }\n    });\n  }\n  /**\n   * Nettoie les ressources WebRTC\n   */\n  cleanupWebRTC() {\n    console.log('🧹 [CallService] Cleaning up WebRTC resources');\n    // Arrêter les tracks du stream local\n    if (this.localStream) {\n      this.localStream.getTracks().forEach(track => {\n        track.stop();\n      });\n      this.localStream = null;\n    }\n    // Nettoyer les éléments vidéo\n    if (this.localVideoElement) {\n      this.localVideoElement.srcObject = null;\n    }\n    if (this.remoteVideoElement) {\n      this.remoteVideoElement.srcObject = null;\n    }\n    // Fermer la PeerConnection\n    if (this.peerConnection) {\n      this.peerConnection.close();\n      this.peerConnection = null;\n    }\n    this.remoteStream = null;\n  }\n  ngOnDestroy() {\n    this.stopAllSounds();\n    this.cleanupWebRTC();\n  }\n  static {\n    this.ɵfac = function CallService_Factory(t) {\n      return new (t || CallService)(i0.ɵɵinject(i1.Apollo), i0.ɵɵinject(i2.LoggerService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: CallService,\n      factory: CallService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["BehaviorSubject", "throwError", "from", "map", "catchError", "switchMap", "CallType", "CallStatus", "INITIATE_CALL_MUTATION", "ACCEPT_CALL_MUTATION", "REJECT_CALL_MUTATION", "END_CALL_MUTATION", "TOGGLE_CALL_MEDIA_MUTATION", "INCOMING_CALL_SUBSCRIPTION", "CALL_STATUS_CHANGED_SUBSCRIPTION", "CALL_SIGNAL_SUBSCRIPTION", "CallService", "constructor", "apollo", "logger", "activeCall", "incomingCall", "activeCall$", "asObservable", "incomingCall$", "sounds", "isPlaying", "muted", "isVideoEnabled", "isAudioEnabled", "peerConnection", "localStream", "remoteStream", "localVideoElement", "remoteVideoElement", "preloadSounds", "initializeSubscriptions", "initializeWebRTC", "setTimeout", "subscribeToIncomingCalls", "subscribeToCallStatusChanges", "subscribeToCallSignals", "value", "console", "log", "configuration", "iceServers", "urls", "RTCPeerConnection", "setupPeerConnectionEvents", "error", "ontrack", "event", "streamId", "streams", "id", "tracks", "getTracks", "t", "kind", "enabled", "readyState", "srcObject", "onloadedmetadata", "oncanplay", "onplay", "ensureAudioPlayback", "warn", "play", "catch", "e", "onicecandidate", "candidate", "onconnectionstatechange", "connectionState", "createSyntheticSounds", "loadSound", "audioContext", "window", "AudioContext", "webkitAudioContext", "createRingtoneSound", "createConnectedSound", "createEndSound", "createNotificationSound", "audio", "Audio", "volume", "timeoutIds", "playSynthetic", "Promise", "resolve", "playMelody", "melody", "freq", "duration", "currentTime", "for<PERSON>ach", "note", "index", "oscillator", "createOscillator", "gainNode", "createGain", "connect", "destination", "frequency", "type", "gain", "setValueAtTime", "linearRampToValueAtTime", "exponentialRampToValueAtTime", "start", "stop", "timeoutId", "push", "stopSynthetic", "clearTimeout", "notes", "delay", "startTime", "name", "path", "preload", "addEventListener", "altPath", "startsWith", "substring", "src", "load", "loop", "sound", "syntheticName", "playSimpleBeep", "then", "syntheticSound", "pause", "subscribe", "query", "errorPolicy", "next", "data", "errors", "handleIncomingCall", "complete", "reinitializeSubscription", "variables", "callId", "currentCallId", "callSignal", "handleCallSignal", "signal", "_this", "_asyncToGenerator", "handleRemoteOffer", "handleRemoteAnswer", "handleRemoteIceCandidate", "_this2", "offer", "JSON", "parse", "setRemoteDescription", "_this3", "answer", "_this4", "addIceCandidate", "testSounds", "testRingtone", "Object", "keys", "ringtone", "ringtoneSynthetic", "testBeautifulSounds", "playNotification", "callStatusChanged", "handleCallStatusChange", "call", "callType", "caller", "username", "conversationId", "status", "REJECTED", "ENDED", "stopAllSounds", "CONNECTED", "RINGING", "getUserMedia", "_this5", "constraints", "video", "VIDEO", "stream", "navigator", "mediaDevices", "Error", "addLocalStreamToPeerConnection", "track", "addTrack", "createOffer", "_this6", "setLocalDescription", "createAnswer", "_this7", "setVideoElements", "localVideo", "remoteVideo", "setupAudioPlayback", "autoplay", "forceStreamAttachment", "localAttached", "remoteAttached", "localStreamExists", "remoteStreamExists", "videoElement", "playOnInteraction", "document", "removeEventListener", "err", "initiateCall", "recipientId", "Date", "now", "Math", "random", "toString", "substr", "createWebRTCOffer", "pipe", "stringify", "mutate", "mutation", "result", "recipient", "errorMessage", "networkError", "graphQLErrors", "length", "message", "_this8", "acceptCall", "createWebRTCAnswer", "startMediaForCall", "_this9", "<PERSON><PERSON><PERSON>", "offerLength", "sdp", "rejectCall", "reason", "endCall", "feedback", "cleanupWebRTC", "toggleMedia", "enableVideo", "enableAudio", "toggleCallMedia", "_this0", "toggleVideo", "toggleAudio", "getVideoEnabled", "getAudioEnabled", "enableSounds", "values", "close", "ngOnDestroy", "i0", "ɵɵinject", "i1", "Apollo", "i2", "LoggerService", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\services\\call.service.ts"], "sourcesContent": ["import { Injectable, <PERSON><PERSON><PERSON>roy } from '@angular/core';\nimport { Apollo } from 'apollo-angular';\nimport { BehaviorSubject, Observable, throwError, of, from } from 'rxjs';\nimport { map, catchError, switchMap } from 'rxjs/operators';\nimport {\n  Call,\n  CallType,\n  CallStatus,\n  IncomingCall,\n  CallSuccess,\n} from '../models/message.model';\nimport {\n  INITIATE_CALL_MUTATION,\n  ACCEPT_CALL_MUTATION,\n  REJECT_CALL_MUTATION,\n  END_CALL_MUTATION,\n  TOGGLE_CALL_MEDIA_MUTATION,\n  INCOMING_CALL_SUBSCRIPTION,\n  CALL_STATUS_CHANGED_SUBSCRIPTION,\n  CALL_SIGNAL_SUBSCRIPTION,\n} from '../graphql/message.graphql';\nimport { LoggerService } from './logger.service';\n\n@Injectable({\n  providedIn: 'root',\n})\nexport class CallService implements OnDestroy {\n  // État des appels\n  private activeCall = new BehaviorSubject<Call | null>(null);\n  private incomingCall = new BehaviorSubject<IncomingCall | null>(null);\n\n  // Observables publics\n  public activeCall$ = this.activeCall.asObservable();\n  public incomingCall$ = this.incomingCall.asObservable();\n\n  // Propriétés pour la gestion des sons\n  private sounds: { [key: string]: HTMLAudioElement } = {};\n  private isPlaying: { [key: string]: boolean } = {};\n  private muted = false;\n\n  // États simples pour les médias\n  private isVideoEnabled = true;\n  private isAudioEnabled = true;\n\n  // WebRTC\n  private peerConnection: RTCPeerConnection | null = null;\n  private localStream: MediaStream | null = null;\n  private remoteStream: MediaStream | null = null;\n  private localVideoElement: HTMLVideoElement | null = null;\n  private remoteVideoElement: HTMLVideoElement | null = null;\n\n  constructor(private apollo: Apollo, private logger: LoggerService) {\n    this.preloadSounds();\n    this.initializeSubscriptions();\n    this.initializeWebRTC();\n  }\n\n  /**\n   * Initialise les subscriptions avec un délai pour s'assurer que l'authentification est prête\n   */\n  private initializeSubscriptions(): void {\n    // Attendre un peu pour s'assurer que l'authentification est prête\n    setTimeout(() => {\n      this.subscribeToIncomingCalls();\n      this.subscribeToCallStatusChanges();\n      this.subscribeToCallSignals();\n    }, 1000);\n\n    // Réessayer UNE SEULE FOIS après 10 secondes si nécessaire\n    setTimeout(() => {\n      if (!this.incomingCall.value) {\n        // console.log('🔄 [CallService] Retrying subscription initialization...');\n        this.subscribeToIncomingCalls();\n        this.subscribeToCallStatusChanges();\n        this.subscribeToCallSignals();\n      }\n    }, 10000);\n  }\n\n  /**\n   * Initialise WebRTC\n   */\n  private initializeWebRTC(): void {\n    console.log('🔧 [CallService] Initializing WebRTC...');\n\n    // Configuration des serveurs STUN/TURN\n    const configuration: RTCConfiguration = {\n      iceServers: [\n        { urls: 'stun:stun.l.google.com:19302' },\n        { urls: 'stun:stun1.l.google.com:19302' },\n      ],\n    };\n\n    try {\n      this.peerConnection = new RTCPeerConnection(configuration);\n      this.setupPeerConnectionEvents();\n      console.log('✅ [CallService] WebRTC initialized successfully');\n    } catch (error) {\n      console.error('❌ [CallService] Failed to initialize WebRTC:', error);\n    }\n  }\n\n  /**\n   * Configure les événements de la PeerConnection\n   */\n  private setupPeerConnectionEvents(): void {\n    if (!this.peerConnection) return;\n\n    // Quand on reçoit un stream distant\n    this.peerConnection.ontrack = (event) => {\n      console.log('📺 [CallService] Remote stream received');\n      console.log('🔍 [CallService] Stream details:', {\n        streamId: event.streams[0]?.id,\n        tracks: event.streams[0]?.getTracks().map((t) => ({\n          kind: t.kind,\n          enabled: t.enabled,\n          readyState: t.readyState,\n        })),\n      });\n\n      this.remoteStream = event.streams[0];\n\n      if (this.remoteVideoElement) {\n        console.log('🎥 [CallService] Setting remote stream to video element');\n        this.remoteVideoElement.srcObject = this.remoteStream;\n\n        // Configurer les événements de l'élément vidéo\n        this.remoteVideoElement.onloadedmetadata = () => {\n          console.log('🎵 [CallService] Remote video metadata loaded');\n        };\n\n        this.remoteVideoElement.oncanplay = () => {\n          console.log('🎵 [CallService] Remote video can play');\n        };\n\n        this.remoteVideoElement.onplay = () => {\n          console.log('🎵 [CallService] Remote video started playing');\n        };\n\n        // Forcer la lecture audio immédiatement\n        try {\n          this.ensureAudioPlayback(this.remoteVideoElement);\n        } catch (error) {\n          console.warn(\n            '⚠️ [CallService] ensureAudioPlayback error in ontrack:',\n            error\n          );\n          // Fallback simple : forcer la lecture directement\n          console.log('🔄 [CallService] Trying direct play as fallback');\n          this.remoteVideoElement.play().catch((e) => {\n            console.warn('⚠️ [CallService] Direct play failed:', e);\n            // Dernier recours : attendre une interaction utilisateur\n            console.log(\n              '⏳ [CallService] Waiting for user interaction to enable audio'\n            );\n          });\n        }\n      } else {\n        console.warn('⚠️ [CallService] No remote video element available');\n      }\n    };\n\n    // Gestion des candidats ICE\n    this.peerConnection.onicecandidate = (event) => {\n      if (event.candidate) {\n        console.log('🧊 [CallService] ICE candidate generated');\n        // TODO: Envoyer le candidat via WebSocket\n      }\n    };\n\n    // État de la connexion\n    this.peerConnection.onconnectionstatechange = () => {\n      console.log(\n        '🔗 [CallService] Connection state:',\n        this.peerConnection?.connectionState\n      );\n    };\n  }\n\n  /**\n   * Précharge les sons utilisés dans l'application\n   */\n  private preloadSounds(): void {\n    // Créer des sons synthétiques de haute qualité\n    this.createSyntheticSounds();\n\n    // Charger le son de notification qui existe encore\n    this.loadSound('notification', 'assets/sounds/notification.mp3');\n\n    console.log(\n      '🎵 [CallService] Beautiful synthetic melodies created for calls'\n    );\n  }\n\n  /**\n   * Crée des sons synthétiques comme fallback\n   */\n  private createSyntheticSounds(): void {\n    try {\n      // Créer un contexte audio pour les sons synthétiques\n      const audioContext = new (window.AudioContext ||\n        (window as any).webkitAudioContext)();\n\n      // Son de sonnerie (mélodie agréable)\n      this.sounds['ringtone-synthetic'] =\n        this.createRingtoneSound(audioContext);\n\n      // Son de connexion (accord agréable)\n      this.sounds['call-connected-synthetic'] =\n        this.createConnectedSound(audioContext);\n\n      // Son de fin d'appel (ton descendant)\n      this.sounds['call-end-synthetic'] = this.createEndSound(audioContext);\n\n      // Son de notification (bip agréable)\n      this.sounds['notification-synthetic'] =\n        this.createNotificationSound(audioContext);\n\n      console.log('🔊 [CallService] Synthetic sounds created as fallback');\n    } catch (error) {\n      console.warn(\n        '⚠️ [CallService] Could not create synthetic sounds:',\n        error\n      );\n    }\n  }\n\n  /**\n   * Crée une sonnerie agréable (mélodie)\n   */\n  private createRingtoneSound(audioContext: AudioContext): HTMLAudioElement {\n    const audio = new Audio();\n    audio.volume = 0.5;\n\n    let isPlaying = false;\n    let timeoutIds: any[] = [];\n\n    (audio as any).playSynthetic = () => {\n      if (isPlaying) return Promise.resolve();\n\n      isPlaying = true;\n\n      const playMelody = () => {\n        if (!isPlaying) return;\n\n        // Mélodie inspirée de Nokia mais plus moderne : Mi-Ré-Fa#-Sol-Do#-Si-Ré-Do\n        const melody = [\n          { freq: 659.25, duration: 0.125 }, // Mi\n          { freq: 587.33, duration: 0.125 }, // Ré\n          { freq: 739.99, duration: 0.25 }, // Fa#\n          { freq: 783.99, duration: 0.25 }, // Sol\n          { freq: 554.37, duration: 0.125 }, // Do#\n          { freq: 493.88, duration: 0.125 }, // Si\n          { freq: 587.33, duration: 0.25 }, // Ré\n          { freq: 523.25, duration: 0.25 }, // Do\n        ];\n\n        let currentTime = audioContext.currentTime;\n\n        melody.forEach((note, index) => {\n          if (!isPlaying) return;\n\n          const oscillator = audioContext.createOscillator();\n          const gainNode = audioContext.createGain();\n\n          oscillator.connect(gainNode);\n          gainNode.connect(audioContext.destination);\n\n          oscillator.frequency.value = note.freq;\n          oscillator.type = 'square'; // Son plus moderne\n\n          // Enveloppe ADSR pour un son plus naturel\n          gainNode.gain.setValueAtTime(0, currentTime);\n          gainNode.gain.linearRampToValueAtTime(0.3, currentTime + 0.02);\n          gainNode.gain.linearRampToValueAtTime(\n            0.2,\n            currentTime + note.duration * 0.7\n          );\n          gainNode.gain.exponentialRampToValueAtTime(\n            0.01,\n            currentTime + note.duration\n          );\n\n          oscillator.start(currentTime);\n          oscillator.stop(currentTime + note.duration);\n\n          currentTime += note.duration + 0.05; // Petite pause entre les notes\n        });\n\n        // Répéter la mélodie après une pause\n        const timeoutId = setTimeout(() => {\n          if (isPlaying) {\n            playMelody();\n          }\n        }, (currentTime - audioContext.currentTime + 0.8) * 1000);\n\n        timeoutIds.push(timeoutId);\n      };\n\n      playMelody();\n      return Promise.resolve();\n    };\n\n    (audio as any).stopSynthetic = () => {\n      isPlaying = false;\n      timeoutIds.forEach((id) => clearTimeout(id));\n      timeoutIds = [];\n    };\n\n    return audio;\n  }\n\n  /**\n   * Crée un son de connexion agréable (mélodie ascendante)\n   */\n  private createConnectedSound(audioContext: AudioContext): HTMLAudioElement {\n    const audio = new Audio();\n    audio.volume = 0.5;\n\n    (audio as any).playSynthetic = () => {\n      // Mélodie ascendante positive : Do-Mi-Sol-Do (octave supérieure)\n      const melody = [\n        { freq: 523.25, duration: 0.15 }, // Do\n        { freq: 659.25, duration: 0.15 }, // Mi\n        { freq: 783.99, duration: 0.15 }, // Sol\n        { freq: 1046.5, duration: 0.4 }, // Do (octave supérieure)\n      ];\n\n      let currentTime = audioContext.currentTime;\n\n      melody.forEach((note, index) => {\n        const oscillator = audioContext.createOscillator();\n        const gainNode = audioContext.createGain();\n\n        oscillator.connect(gainNode);\n        gainNode.connect(audioContext.destination);\n\n        oscillator.frequency.value = note.freq;\n        oscillator.type = 'triangle'; // Son plus doux\n\n        // Enveloppe pour un son naturel\n        gainNode.gain.setValueAtTime(0, currentTime);\n        gainNode.gain.linearRampToValueAtTime(0.25, currentTime + 0.02);\n        gainNode.gain.linearRampToValueAtTime(\n          0.15,\n          currentTime + note.duration * 0.8\n        );\n        gainNode.gain.exponentialRampToValueAtTime(\n          0.01,\n          currentTime + note.duration\n        );\n\n        oscillator.start(currentTime);\n        oscillator.stop(currentTime + note.duration);\n\n        currentTime += note.duration * 0.8; // Chevauchement léger des notes\n      });\n\n      return Promise.resolve();\n    };\n\n    return audio;\n  }\n\n  /**\n   * Crée un son de fin d'appel (mélodie descendante douce)\n   */\n  private createEndSound(audioContext: AudioContext): HTMLAudioElement {\n    const audio = new Audio();\n    audio.volume = 0.4;\n\n    (audio as any).playSynthetic = () => {\n      // Mélodie descendante douce : Sol-Mi-Do-Sol(grave)\n      const melody = [\n        { freq: 783.99, duration: 0.2 }, // Sol\n        { freq: 659.25, duration: 0.2 }, // Mi\n        { freq: 523.25, duration: 0.2 }, // Do\n        { freq: 392.0, duration: 0.4 }, // Sol (octave inférieure)\n      ];\n\n      let currentTime = audioContext.currentTime;\n\n      melody.forEach((note, index) => {\n        const oscillator = audioContext.createOscillator();\n        const gainNode = audioContext.createGain();\n\n        oscillator.connect(gainNode);\n        gainNode.connect(audioContext.destination);\n\n        oscillator.frequency.value = note.freq;\n        oscillator.type = 'sine'; // Son doux pour la fin\n\n        // Enveloppe douce pour un son apaisant\n        gainNode.gain.setValueAtTime(0, currentTime);\n        gainNode.gain.linearRampToValueAtTime(0.2, currentTime + 0.05);\n        gainNode.gain.linearRampToValueAtTime(\n          0.1,\n          currentTime + note.duration * 0.7\n        );\n        gainNode.gain.exponentialRampToValueAtTime(\n          0.01,\n          currentTime + note.duration\n        );\n\n        oscillator.start(currentTime);\n        oscillator.stop(currentTime + note.duration);\n\n        currentTime += note.duration * 0.9; // Léger chevauchement\n      });\n\n      return Promise.resolve();\n    };\n\n    return audio;\n  }\n\n  /**\n   * Crée un son de notification agréable (double bip)\n   */\n  private createNotificationSound(\n    audioContext: AudioContext\n  ): HTMLAudioElement {\n    const audio = new Audio();\n    audio.volume = 0.6;\n\n    (audio as any).playSynthetic = () => {\n      // Double bip agréable : Do-Sol\n      const notes = [\n        { freq: 523.25, duration: 0.15, delay: 0 }, // Do\n        { freq: 783.99, duration: 0.25, delay: 0.2 }, // Sol\n      ];\n\n      notes.forEach((note) => {\n        setTimeout(() => {\n          const oscillator = audioContext.createOscillator();\n          const gainNode = audioContext.createGain();\n\n          oscillator.connect(gainNode);\n          gainNode.connect(audioContext.destination);\n\n          oscillator.frequency.value = note.freq;\n          oscillator.type = 'triangle'; // Son doux et agréable\n\n          const startTime = audioContext.currentTime;\n\n          // Enveloppe pour un son naturel\n          gainNode.gain.setValueAtTime(0, startTime);\n          gainNode.gain.linearRampToValueAtTime(0.4, startTime + 0.02);\n          gainNode.gain.linearRampToValueAtTime(\n            0.2,\n            startTime + note.duration * 0.7\n          );\n          gainNode.gain.exponentialRampToValueAtTime(\n            0.01,\n            startTime + note.duration\n          );\n\n          oscillator.start(startTime);\n          oscillator.stop(startTime + note.duration);\n        }, note.delay * 1000);\n      });\n\n      return Promise.resolve();\n    };\n\n    return audio;\n  }\n\n  /**\n   * Charge un fichier audio\n   */\n  private loadSound(name: string, path: string): void {\n    try {\n      const audio = new Audio();\n\n      // Configuration de l'audio\n      audio.preload = 'auto';\n      audio.volume = 0.7;\n\n      // Gérer les événements de chargement\n      audio.addEventListener('canplaythrough', () => {\n        console.log(\n          `✅ [CallService] Sound ${name} loaded successfully from ${path}`\n        );\n      });\n\n      audio.addEventListener('error', (e) => {\n        console.error(\n          `❌ [CallService] Error loading sound ${name} from ${path}:`,\n          e\n        );\n        console.log(\n          `🔄 [CallService] Trying to load ${name} with different approach...`\n        );\n\n        // Essayer de charger avec un chemin relatif\n        const altPath = path.startsWith('/') ? path.substring(1) : path;\n        if (altPath !== path) {\n          setTimeout(() => {\n            audio.src = altPath;\n            audio.load();\n          }, 100);\n        }\n      });\n\n      audio.addEventListener('ended', () => {\n        this.isPlaying[name] = false;\n        console.log(`🔇 [CallService] Sound ${name} ended`);\n      });\n\n      // Définir la source et charger\n      audio.src = path;\n      audio.load();\n\n      this.sounds[name] = audio;\n      this.isPlaying[name] = false;\n\n      console.log(`🔊 [CallService] Loading sound ${name} from ${path}`);\n    } catch (error) {\n      console.error(\n        `❌ [CallService] Error creating audio element for ${name}:`,\n        error\n      );\n    }\n  }\n\n  /**\n   * Joue un son\n   */\n  private play(name: string, loop: boolean = false): void {\n    if (this.muted) {\n      console.log(`🔇 [CallService] Sound ${name} muted`);\n      return;\n    }\n\n    try {\n      // Pour les sons d'appel, utiliser directement les versions synthétiques\n      let sound;\n      if (\n        name === 'ringtone' ||\n        name === 'call-connected' ||\n        name === 'call-end'\n      ) {\n        const syntheticName = `${name}-synthetic`;\n        sound = this.sounds[syntheticName];\n        if (sound) {\n          console.log(\n            `🎵 [CallService] Using beautiful synthetic melody for ${name}`\n          );\n        }\n      } else {\n        // Pour les autres sons (comme notification), essayer d'abord le fichier\n        sound = this.sounds[name];\n        if (!sound || sound.error) {\n          const syntheticName = `${name}-synthetic`;\n          sound = this.sounds[syntheticName];\n          if (sound) {\n            console.log(`🔊 [CallService] Using synthetic sound for ${name}`);\n          }\n        }\n      }\n\n      if (!sound) {\n        console.warn(\n          `🔊 [CallService] Sound ${name} not found (neither original nor synthetic)`\n        );\n        // Créer un bip simple comme dernier recours\n        this.playSimpleBeep(\n          name === 'ringtone' ? 800 : name === 'call-connected' ? 1000 : 400\n        );\n        return;\n      }\n\n      sound.loop = loop;\n      sound.volume = 0.7;\n\n      if (!this.isPlaying[name]) {\n        console.log(`🔊 [CallService] Playing sound: ${name} (loop: ${loop})`);\n\n        // Vérifier si c'est un son synthétique\n        if ((sound as any).playSynthetic) {\n          (sound as any)\n            .playSynthetic()\n            .then(() => {\n              console.log(\n                `✅ [CallService] Synthetic sound ${name} started successfully`\n              );\n              this.isPlaying[name] = true;\n\n              // Pour la sonnerie synthétique, elle gère sa propre boucle\n              if (name === 'ringtone' && !loop) {\n                // Si ce n'est pas en boucle, arrêter après un certain temps\n                setTimeout(() => {\n                  this.isPlaying[name] = false;\n                }, 3000);\n              } else if (name !== 'ringtone') {\n                // Pour les autres sons, arrêter après leur durée\n                setTimeout(\n                  () => {\n                    this.isPlaying[name] = false;\n                  },\n                  name === 'call-connected' ? 1200 : 1000\n                );\n              }\n            })\n            .catch((error: any) => {\n              console.error(\n                `❌ [CallService] Error playing synthetic sound ${name}:`,\n                error\n              );\n            });\n        } else {\n          // Son normal\n          sound.currentTime = 0;\n          sound\n            .play()\n            .then(() => {\n              console.log(\n                `✅ [CallService] Sound ${name} started successfully`\n              );\n              this.isPlaying[name] = true;\n            })\n            .catch((error) => {\n              console.error(\n                `❌ [CallService] Error playing sound ${name}:`,\n                error\n              );\n\n              // Essayer le son synthétique en cas d'échec\n              const syntheticName = `${name}-synthetic`;\n              const syntheticSound = this.sounds[syntheticName];\n              if (syntheticSound && (syntheticSound as any).playSynthetic) {\n                console.log(\n                  `🔄 [CallService] Falling back to synthetic sound for ${name}`\n                );\n                this.play(name, loop);\n              } else {\n                // Dernier recours : bip simple\n                this.playSimpleBeep(\n                  name === 'ringtone'\n                    ? 800\n                    : name === 'call-connected'\n                    ? 1000\n                    : 400\n                );\n              }\n            });\n        }\n      } else {\n        console.log(`🔊 [CallService] Sound ${name} already playing`);\n      }\n    } catch (error) {\n      console.error(\n        `❌ [CallService] Error in play method for ${name}:`,\n        error\n      );\n      // Dernier recours\n      this.playSimpleBeep(\n        name === 'ringtone' ? 800 : name === 'call-connected' ? 1000 : 400\n      );\n    }\n  }\n\n  /**\n   * Joue un bip simple comme dernier recours\n   */\n  private playSimpleBeep(frequency: number): void {\n    try {\n      const audioContext = new (window.AudioContext ||\n        (window as any).webkitAudioContext)();\n      const oscillator = audioContext.createOscillator();\n      const gainNode = audioContext.createGain();\n\n      oscillator.connect(gainNode);\n      gainNode.connect(audioContext.destination);\n\n      oscillator.frequency.value = frequency;\n      oscillator.type = 'sine';\n\n      gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);\n      gainNode.gain.exponentialRampToValueAtTime(\n        0.01,\n        audioContext.currentTime + 0.3\n      );\n\n      oscillator.start(audioContext.currentTime);\n      oscillator.stop(audioContext.currentTime + 0.3);\n\n      console.log(`🔊 [CallService] Playing simple beep at ${frequency}Hz`);\n    } catch (error) {\n      console.error('❌ [CallService] Could not play simple beep:', error);\n    }\n  }\n\n  /**\n   * Arrête un son\n   */\n  private stop(name: string): void {\n    try {\n      let sound = this.sounds[name];\n\n      // Essayer aussi la version synthétique\n      if (!sound) {\n        sound = this.sounds[`${name}-synthetic`];\n      }\n\n      if (!sound) {\n        console.warn(`🔊 [CallService] Sound ${name} not found for stopping`);\n        return;\n      }\n\n      if (this.isPlaying[name]) {\n        console.log(`🔇 [CallService] Stopping sound: ${name}`);\n\n        // Arrêter le son synthétique si c'est le cas\n        if ((sound as any).stopSynthetic) {\n          (sound as any).stopSynthetic();\n        } else {\n          sound.pause();\n          sound.currentTime = 0;\n        }\n\n        this.isPlaying[name] = false;\n      } else {\n        console.log(`🔇 [CallService] Sound ${name} was not playing`);\n      }\n    } catch (error) {\n      console.error(`❌ [CallService] Error stopping sound ${name}:`, error);\n    }\n  }\n\n  /**\n   * S'abonne aux appels entrants\n   */\n  private subscribeToIncomingCalls(): void {\n    // console.log('🔔 [CallService] Setting up incoming call subscription...');\n\n    try {\n      this.apollo\n        .subscribe<{ incomingCall: IncomingCall }>({\n          query: INCOMING_CALL_SUBSCRIPTION,\n          errorPolicy: 'all', // Continuer même en cas d'erreur\n        })\n        .subscribe({\n          next: ({ data, errors }) => {\n            if (errors) {\n              console.warn(\n                '⚠️ [CallService] GraphQL errors in subscription:',\n                errors\n              );\n            }\n\n            if (data?.incomingCall) {\n              // console.log('📞 [CallService] Incoming call received:', data.incomingCall.id);\n              this.handleIncomingCall(data.incomingCall);\n            }\n          },\n          error: (error) => {\n            console.error(\n              '❌ [CallService] Error in incoming call subscription:',\n              error\n            );\n\n            // Réessayer UNE SEULE FOIS après 10 secondes\n            setTimeout(() => {\n              this.subscribeToIncomingCalls();\n            }, 10000);\n          },\n          complete: () => {\n            // console.log('🔚 [CallService] Incoming call subscription completed');\n            // Réessayer si la subscription se ferme de manière inattendue\n            setTimeout(() => {\n              this.subscribeToIncomingCalls();\n            }, 5000);\n          },\n        });\n    } catch (error) {\n      console.error('❌ [CallService] Failed to create subscription:', error);\n      // Réessayer après 10 secondes\n      setTimeout(() => {\n        this.subscribeToIncomingCalls();\n      }, 10000);\n    }\n  }\n\n  /**\n   * Méthode publique pour forcer la réinitialisation de la subscription\n   */\n  public reinitializeSubscription(): void {\n    // console.log('🔄 [CallService] Manually reinitializing subscription...');\n    this.subscribeToIncomingCalls();\n    this.subscribeToCallStatusChanges();\n    this.subscribeToCallSignals();\n  }\n\n  /**\n   * S'abonne aux signaux WebRTC (offres, réponses, candidats ICE)\n   */\n  private subscribeToCallSignals(): void {\n    console.log('📡 [CallService] Setting up call signal subscription...');\n\n    try {\n      this.apollo\n        .subscribe<{ callSignal: any }>({\n          query: CALL_SIGNAL_SUBSCRIPTION,\n          variables: {\n            callId: this.currentCallId || null,\n          },\n          errorPolicy: 'all',\n        })\n        .subscribe({\n          next: ({ data, errors }) => {\n            if (errors) {\n              console.warn(\n                '⚠️ [CallService] Call signal subscription errors:',\n                errors\n              );\n            }\n\n            if (data?.callSignal) {\n              console.log(\n                '📡 [CallService] Received call signal:',\n                data.callSignal\n              );\n              this.handleCallSignal(data.callSignal);\n            }\n          },\n          error: (error) => {\n            console.error(\n              '❌ [CallService] Error in call signal subscription:',\n              error\n            );\n            // Réessayer après 5 secondes\n            setTimeout(() => {\n              this.subscribeToCallSignals();\n            }, 5000);\n          },\n          complete: () => {\n            console.log('🔚 [CallService] Call signal subscription completed');\n            // Réessayer si la subscription se ferme\n            setTimeout(() => {\n              this.subscribeToCallSignals();\n            }, 2000);\n          },\n        });\n    } catch (error) {\n      console.error(\n        '❌ [CallService] Failed to create call signal subscription:',\n        error\n      );\n      setTimeout(() => {\n        this.subscribeToCallSignals();\n      }, 3000);\n    }\n  }\n\n  /**\n   * Gère les signaux WebRTC reçus\n   */\n  private async handleCallSignal(signal: any): Promise<void> {\n    console.log('🔄 [CallService] Handling call signal:', signal.type);\n\n    try {\n      switch (signal.type) {\n        case 'offer':\n          await this.handleRemoteOffer(signal);\n          break;\n        case 'answer':\n          await this.handleRemoteAnswer(signal);\n          break;\n        case 'ice-candidate':\n          await this.handleRemoteIceCandidate(signal);\n          break;\n        default:\n          console.warn('⚠️ [CallService] Unknown signal type:', signal.type);\n      }\n    } catch (error) {\n      console.error('❌ [CallService] Error handling call signal:', error);\n    }\n  }\n\n  /**\n   * Gère une offre WebRTC distante\n   */\n  private async handleRemoteOffer(signal: any): Promise<void> {\n    console.log('📥 [CallService] Handling remote offer');\n\n    if (!this.peerConnection) {\n      console.log('🔧 [CallService] Initializing WebRTC for remote offer');\n      this.initializeWebRTC();\n    }\n\n    const offer = JSON.parse(signal.data);\n    await this.peerConnection!.setRemoteDescription(offer);\n    console.log('✅ [CallService] Remote offer set successfully');\n  }\n\n  /**\n   * Gère une réponse WebRTC distante\n   */\n  private async handleRemoteAnswer(signal: any): Promise<void> {\n    console.log('📥 [CallService] Handling remote answer');\n\n    if (!this.peerConnection) {\n      console.error('❌ [CallService] No peer connection for remote answer');\n      return;\n    }\n\n    const answer = JSON.parse(signal.data);\n    await this.peerConnection.setRemoteDescription(answer);\n    console.log('✅ [CallService] Remote answer set successfully');\n  }\n\n  /**\n   * Gère un candidat ICE distant\n   */\n  private async handleRemoteIceCandidate(signal: any): Promise<void> {\n    console.log('🧊 [CallService] Handling remote ICE candidate');\n\n    if (!this.peerConnection) {\n      console.error('❌ [CallService] No peer connection for ICE candidate');\n      return;\n    }\n\n    const candidate = JSON.parse(signal.data);\n    await this.peerConnection.addIceCandidate(candidate);\n    console.log('✅ [CallService] Remote ICE candidate added successfully');\n  }\n\n  /**\n   * Méthode de test pour vérifier les sons\n   */\n  public testSounds(): void {\n    console.log('🧪 [CallService] Testing sounds...');\n\n    // Test de la sonnerie\n    console.log('🔊 Testing ringtone...');\n    this.play('ringtone', true);\n\n    setTimeout(() => {\n      this.stop('ringtone');\n      console.log('🔊 Testing call-connected...');\n      this.play('call-connected');\n    }, 3000);\n\n    setTimeout(() => {\n      console.log('🔊 Testing call-end...');\n      this.play('call-end');\n    }, 5000);\n  }\n\n  /**\n   * Test spécifique pour la sonnerie\n   */\n  public testRingtone(): void {\n    console.log('🧪 [CallService] Testing ringtone specifically...');\n    console.log('🔊 [CallService] Available sounds:', Object.keys(this.sounds));\n\n    // Vérifier si le son est chargé\n    const ringtone = this.sounds['ringtone'];\n    const ringtoneSynthetic = this.sounds['ringtone-synthetic'];\n\n    if (ringtone) {\n      console.log('✅ [CallService] Ringtone MP3 found:', {\n        src: ringtone.src,\n        readyState: ringtone.readyState,\n        error: ringtone.error,\n        duration: ringtone.duration,\n      });\n    } else {\n      console.log('❌ [CallService] Ringtone MP3 not found');\n    }\n\n    if (ringtoneSynthetic) {\n      console.log('✅ [CallService] Ringtone synthetic found');\n    }\n\n    // Jouer la sonnerie (elle va automatiquement utiliser le fallback si nécessaire)\n    console.log('🎵 [CallService] Playing beautiful ringtone melody...');\n    this.play('ringtone', true);\n\n    // Arrêter après 8 secondes pour entendre plusieurs cycles de la mélodie\n    setTimeout(() => {\n      this.stop('ringtone');\n      console.log('🔇 [CallService] Ringtone test stopped');\n    }, 8000);\n  }\n\n  /**\n   * Test tous les nouveaux sons améliorés\n   */\n  public testBeautifulSounds(): void {\n    console.log('🧪 [CallService] Testing all beautiful sounds...');\n\n    // Test de la sonnerie (mélodie)\n    // console.log('🎵 Testing beautiful ringtone melody...');\n    this.play('ringtone', true);\n\n    setTimeout(() => {\n      this.stop('ringtone');\n      // console.log('🎵 Testing beautiful connection sound...');\n      this.play('call-connected');\n    }, 4000);\n\n    setTimeout(() => {\n      // console.log('🎵 Testing beautiful end sound...');\n      this.play('call-end');\n    }, 6000);\n\n    setTimeout(() => {\n      // console.log('🎵 Testing beautiful notification sound...');\n      this.play('notification');\n    }, 8000);\n\n    setTimeout(() => {\n      console.log('🎵 All sound tests completed!');\n    }, 10000);\n  }\n\n  /**\n   * Joue le son de notification (méthode publique)\n   */\n  public playNotification(): void {\n    console.log('🔔 [CallService] Playing notification sound...');\n    this.play('notification');\n  }\n\n  /**\n   * S'abonne aux changements de statut d'appel\n   */\n  private subscribeToCallStatusChanges(): void {\n    console.log(\n      '📞 [CallService] Setting up call status change subscription...'\n    );\n\n    try {\n      this.apollo\n        .subscribe<{ callStatusChanged: Call }>({\n          query: CALL_STATUS_CHANGED_SUBSCRIPTION,\n          errorPolicy: 'all',\n        })\n        .subscribe({\n          next: ({ data, errors }) => {\n            if (errors) {\n              console.warn(\n                '⚠️ [CallService] GraphQL errors in call status subscription:',\n                errors\n              );\n            }\n\n            if (data?.callStatusChanged) {\n              console.log(\n                '📞 [CallService] Call status changed:',\n                data.callStatusChanged\n              );\n              this.handleCallStatusChange(data.callStatusChanged);\n            }\n          },\n          error: (error) => {\n            console.error(\n              '❌ [CallService] Error in call status subscription:',\n              error\n            );\n            // Réessayer après 5 secondes\n            setTimeout(() => {\n              this.subscribeToCallStatusChanges();\n            }, 5000);\n          },\n          complete: () => {\n            console.log('🔚 [CallService] Call status subscription completed');\n            // Réessayer si la subscription se ferme\n            setTimeout(() => {\n              this.subscribeToCallStatusChanges();\n            }, 2000);\n          },\n        });\n    } catch (error) {\n      console.error(\n        '❌ [CallService] Failed to create call status subscription:',\n        error\n      );\n      setTimeout(() => {\n        this.subscribeToCallStatusChanges();\n      }, 3000);\n    }\n  }\n\n  /**\n   * Gère un appel entrant\n   */\n  private handleIncomingCall(call: IncomingCall): void {\n    console.log('🔔 [CallService] Handling incoming call:', {\n      callId: call.id,\n      callType: call.type,\n      caller: call.caller?.username,\n      conversationId: call.conversationId,\n    });\n\n    this.incomingCall.next(call);\n    this.play('ringtone', true);\n\n    console.log(\n      '🔊 [CallService] Ringtone started, call notification sent to UI'\n    );\n  }\n\n  /**\n   * Gère les changements de statut d'appel\n   */\n  private handleCallStatusChange(call: Call): void {\n    console.log('📞 [CallService] Call status changed:', call.status);\n\n    switch (call.status) {\n      case CallStatus.REJECTED:\n        console.log('❌ [CallService] Call was rejected');\n        this.stop('ringtone');\n        this.play('call-end');\n        this.activeCall.next(null);\n        this.incomingCall.next(null);\n        break;\n\n      case CallStatus.ENDED:\n        console.log('📞 [CallService] Call ended');\n        this.stopAllSounds();\n        this.play('call-end');\n        this.activeCall.next(null);\n        this.incomingCall.next(null);\n        break;\n\n      case CallStatus.CONNECTED:\n        console.log('✅ [CallService] Call connected');\n        this.stop('ringtone');\n        this.play('call-connected');\n        this.activeCall.next(call);\n        this.incomingCall.next(null);\n        break;\n\n      case CallStatus.RINGING:\n        console.log('📞 [CallService] Call is ringing');\n        this.play('ringtone', true);\n        break;\n\n      default:\n        console.log('📞 [CallService] Unknown call status:', call.status);\n        break;\n    }\n  }\n\n  /**\n   * Obtient les médias utilisateur (caméra/micro)\n   */\n  private async getUserMedia(callType: CallType): Promise<MediaStream> {\n    console.log('🎥 [CallService] Getting user media for:', callType);\n\n    const constraints: MediaStreamConstraints = {\n      audio: true,\n      video: callType === CallType.VIDEO,\n    };\n\n    try {\n      const stream = await navigator.mediaDevices.getUserMedia(constraints);\n      console.log('✅ [CallService] User media obtained');\n      console.log(\n        '🔍 [CallService] Stream tracks:',\n        stream.getTracks().map((t) => ({\n          kind: t.kind,\n          enabled: t.enabled,\n          readyState: t.readyState,\n        }))\n      );\n      this.localStream = stream;\n\n      // Afficher le stream local si on a un élément vidéo\n      if (this.localVideoElement && callType === CallType.VIDEO) {\n        this.localVideoElement.srcObject = stream;\n      }\n\n      return stream;\n    } catch (error) {\n      console.error('❌ [CallService] Failed to get user media:', error);\n      throw new Error(\"Impossible d'accéder à la caméra/microphone\");\n    }\n  }\n\n  /**\n   * Ajoute le stream local à la PeerConnection\n   */\n  private addLocalStreamToPeerConnection(stream: MediaStream): void {\n    if (!this.peerConnection) {\n      console.error('❌ [CallService] No peer connection available');\n      return;\n    }\n\n    console.log('📤 [CallService] Adding local stream to peer connection');\n    stream.getTracks().forEach((track) => {\n      this.peerConnection!.addTrack(track, stream);\n    });\n  }\n\n  /**\n   * Crée une offre WebRTC\n   */\n  private async createOffer(): Promise<RTCSessionDescriptionInit> {\n    if (!this.peerConnection) {\n      throw new Error('No peer connection available');\n    }\n\n    console.log('📝 [CallService] Creating WebRTC offer');\n    const offer = await this.peerConnection.createOffer();\n    await this.peerConnection.setLocalDescription(offer);\n    return offer;\n  }\n\n  /**\n   * Crée une réponse WebRTC\n   */\n  private async createAnswer(\n    offer: RTCSessionDescriptionInit\n  ): Promise<RTCSessionDescriptionInit> {\n    if (!this.peerConnection) {\n      throw new Error('No peer connection available');\n    }\n\n    console.log('📝 [CallService] Creating WebRTC answer');\n    await this.peerConnection.setRemoteDescription(offer);\n    const answer = await this.peerConnection.createAnswer();\n    await this.peerConnection.setLocalDescription(answer);\n    return answer;\n  }\n\n  /**\n   * Configure les éléments vidéo\n   */\n  public setVideoElements(\n    localVideo: HTMLVideoElement,\n    remoteVideo: HTMLVideoElement\n  ): void {\n    console.log('📺 [CallService] Setting video elements');\n    this.localVideoElement = localVideo;\n    this.remoteVideoElement = remoteVideo;\n\n    // Configurer les éléments pour la lecture audio\n    try {\n      this.setupAudioPlayback(localVideo, remoteVideo);\n    } catch (error) {\n      console.warn('⚠️ [CallService] setupAudioPlayback error:', error);\n    }\n\n    // Si on a déjà des streams, les connecter IMMÉDIATEMENT\n    if (this.localStream && localVideo) {\n      console.log('🔗 [CallService] Attaching local stream to video element');\n      localVideo.srcObject = this.localStream;\n      localVideo.muted = true; // Local toujours muet pour éviter l'écho\n      localVideo.volume = 0;\n      localVideo.autoplay = true;\n      try {\n        localVideo.play();\n        console.log('✅ [CallService] Local video playing');\n      } catch (error) {\n        console.warn('⚠️ [CallService] Local video play error:', error);\n      }\n    } else {\n      console.warn(\n        '⚠️ [CallService] No local stream or video element available'\n      );\n    }\n\n    if (this.remoteStream && remoteVideo) {\n      console.log('🔗 [CallService] Attaching remote stream to video element');\n      remoteVideo.srcObject = this.remoteStream;\n      remoteVideo.muted = false; // Remote avec audio\n      remoteVideo.volume = 1;\n      remoteVideo.autoplay = true;\n      try {\n        remoteVideo.play();\n        console.log('✅ [CallService] Remote video playing');\n      } catch (error) {\n        console.warn('⚠️ [CallService] Remote video play error:', error);\n      }\n    } else {\n      console.warn(\n        '⚠️ [CallService] No remote stream or video element available'\n      );\n    }\n\n    // Forcer l'attachement après un délai pour s'assurer que les streams sont prêts\n    setTimeout(() => {\n      this.forceStreamAttachment();\n    }, 1000);\n  }\n\n  /**\n   * Force l'attachement des streams aux éléments vidéo\n   */\n  private forceStreamAttachment(): void {\n    console.log('🔄 [CallService] Forcing stream attachment...');\n\n    // Forcer l'attachement du stream local\n    if (this.localStream && this.localVideoElement) {\n      if (!this.localVideoElement.srcObject) {\n        console.log('🔗 [CallService] Force attaching local stream');\n        this.localVideoElement.srcObject = this.localStream;\n        this.localVideoElement.muted = true;\n        this.localVideoElement.autoplay = true;\n        this.localVideoElement\n          .play()\n          .catch((e) =>\n            console.warn('⚠️ [CallService] Force local play failed:', e)\n          );\n      }\n    }\n\n    // Forcer l'attachement du stream distant\n    if (this.remoteStream && this.remoteVideoElement) {\n      if (!this.remoteVideoElement.srcObject) {\n        console.log('🔗 [CallService] Force attaching remote stream');\n        this.remoteVideoElement.srcObject = this.remoteStream;\n        this.remoteVideoElement.muted = false;\n        this.remoteVideoElement.volume = 1;\n        this.remoteVideoElement.autoplay = true;\n        this.remoteVideoElement\n          .play()\n          .catch((e) =>\n            console.warn('⚠️ [CallService] Force remote play failed:', e)\n          );\n      }\n    }\n\n    // Vérifier l'état après forçage\n    console.log('📊 [CallService] Stream attachment status:', {\n      localAttached: !!this.localVideoElement?.srcObject,\n      remoteAttached: !!this.remoteVideoElement?.srcObject,\n      localStreamExists: !!this.localStream,\n      remoteStreamExists: !!this.remoteStream,\n    });\n  }\n\n  /**\n   * Configure la lecture audio pour les éléments vidéo\n   */\n  private setupAudioPlayback(\n    localVideo: HTMLVideoElement,\n    remoteVideo: HTMLVideoElement\n  ): void {\n    console.log('🔊 [CallService] Setting up audio playback');\n\n    // Configurer les propriétés audio\n    localVideo.volume = 0; // Local toujours muet pour éviter l'écho\n    remoteVideo.volume = 1; // Remote avec volume maximum\n\n    // Forcer l'autoplay\n    localVideo.autoplay = true;\n    remoteVideo.autoplay = true;\n\n    // Événements pour débugger\n    remoteVideo.addEventListener('loadedmetadata', () => {\n      console.log('🎵 [CallService] Remote video metadata loaded');\n    });\n\n    remoteVideo.addEventListener('canplay', () => {\n      console.log('🎵 [CallService] Remote video can play');\n      this.ensureAudioPlayback(remoteVideo);\n    });\n\n    remoteVideo.addEventListener('play', () => {\n      console.log('🎵 [CallService] Remote video started playing');\n    });\n\n    remoteVideo.addEventListener('pause', () => {\n      console.log('⏸️ [CallService] Remote video paused');\n    });\n  }\n\n  /**\n   * Force la lecture audio\n   */\n  private ensureAudioPlayback(videoElement: HTMLVideoElement): void {\n    console.log(\n      '🔊 [CallService] Ensuring audio playback for element:',\n      videoElement === this.localVideoElement ? 'local' : 'remote'\n    );\n\n    // Forcer la lecture\n    videoElement\n      .play()\n      .then(() => {\n        console.log(\n          '✅ [CallService] Video/audio playback started successfully'\n        );\n      })\n      .catch((error) => {\n        console.warn(\n          '⚠️ [CallService] Autoplay prevented, user interaction required:',\n          error\n        );\n\n        // Essayer de jouer après interaction utilisateur\n        const playOnInteraction = () => {\n          videoElement\n            .play()\n            .then(() => {\n              console.log(\n                '✅ [CallService] Video/audio playback started after user interaction'\n              );\n              document.removeEventListener('click', playOnInteraction);\n              document.removeEventListener('keydown', playOnInteraction);\n            })\n            .catch((err) => {\n              console.error(\n                '❌ [CallService] Failed to play after interaction:',\n                err\n              );\n            });\n        };\n\n        document.addEventListener('click', playOnInteraction);\n        document.addEventListener('keydown', playOnInteraction);\n      });\n  }\n\n  /**\n   * Initie un appel WebRTC\n   */\n  initiateCall(\n    recipientId: string,\n    callType: CallType,\n    conversationId?: string\n  ): Observable<Call> {\n    console.log('🔄 [CallService] Initiating call:', {\n      recipientId,\n      callType,\n      conversationId,\n    });\n\n    if (!recipientId) {\n      const error = new Error('Recipient ID is required');\n      console.error('❌ [CallService] initiateCall error:', error);\n      return throwError(() => error);\n    }\n\n    // Générer un ID unique pour l'appel\n    const callId = `call_${Date.now()}_${Math.random()\n      .toString(36)\n      .substr(2, 9)}`;\n\n    // Créer une vraie offre WebRTC\n    return from(this.createWebRTCOffer(callType)).pipe(\n      switchMap((offer) => {\n        const variables = {\n          recipientId,\n          callType: callType,\n          callId,\n          offer: JSON.stringify(offer),\n          conversationId,\n        };\n\n        console.log(\n          '📤 [CallService] Sending initiate call mutation:',\n          variables\n        );\n\n        return this.apollo\n          .mutate<{ initiateCall: Call }>({\n            mutation: INITIATE_CALL_MUTATION,\n            variables,\n          })\n          .pipe(\n            map((result) => {\n              console.log(\n                '✅ [CallService] Call initiated successfully:',\n                result\n              );\n\n              if (!result.data?.initiateCall) {\n                throw new Error('No call data received from server');\n              }\n\n              const call = result.data.initiateCall;\n              console.log('📞 [CallService] Call details:', {\n                id: call.id,\n                type: call.type,\n                status: call.status,\n                caller: call.caller?.username,\n                recipient: call.recipient?.username,\n              });\n\n              // Mettre à jour l'état local\n              this.activeCall.next(call);\n\n              return call;\n            }),\n            catchError((error) => {\n              console.error('❌ [CallService] initiateCall error:', error);\n              this.logger.error('Error initiating call:', error);\n\n              let errorMessage = \"Erreur lors de l'initiation de l'appel\";\n              if (error.networkError) {\n                errorMessage = 'Erreur de connexion réseau';\n              } else if (error.graphQLErrors?.length > 0) {\n                errorMessage = error.graphQLErrors[0].message || errorMessage;\n              }\n\n              return throwError(() => new Error(errorMessage));\n            })\n          );\n      }),\n      catchError((error) => {\n        console.error('❌ [CallService] WebRTC error:', error);\n        return throwError(() => new Error('Erreur WebRTC: ' + error.message));\n      })\n    );\n  }\n\n  /**\n   * Crée une offre WebRTC complète avec médias\n   */\n  private async createWebRTCOffer(\n    callType: CallType\n  ): Promise<RTCSessionDescriptionInit> {\n    try {\n      // Réinitialiser la PeerConnection si nécessaire\n      if (!this.peerConnection) {\n        this.initializeWebRTC();\n      }\n\n      // Obtenir les médias utilisateur\n      const stream = await this.getUserMedia(callType);\n\n      // Ajouter le stream à la PeerConnection\n      this.addLocalStreamToPeerConnection(stream);\n\n      // Créer l'offre\n      const offer = await this.createOffer();\n\n      console.log('✅ [CallService] WebRTC offer created successfully');\n      return offer;\n    } catch (error) {\n      console.error('❌ [CallService] Failed to create WebRTC offer:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Accepte un appel entrant\n   */\n  acceptCall(incomingCall: IncomingCall): Observable<Call> {\n    console.log('🔄 [CallService] Accepting call:', incomingCall.id);\n\n    // Créer une vraie réponse WebRTC\n    return from(this.createWebRTCAnswer(incomingCall)).pipe(\n      switchMap((answer) => {\n        return this.apollo\n          .mutate<{ acceptCall: Call }>({\n            mutation: ACCEPT_CALL_MUTATION,\n            variables: {\n              callId: incomingCall.id,\n              answer: JSON.stringify(answer),\n            },\n          })\n          .pipe(\n            switchMap((result) => {\n              console.log(\n                '✅ [CallService] Call accepted successfully:',\n                result\n              );\n\n              if (!result.data?.acceptCall) {\n                throw new Error('No call data received from server');\n              }\n\n              const call = result.data.acceptCall;\n\n              // Arrêter la sonnerie\n              this.stop('ringtone');\n              this.play('call-connected');\n\n              // Démarrer les médias pour l'appel de manière asynchrone\n              return from(this.startMediaForCall(incomingCall, call));\n            }),\n            catchError((error) => {\n              console.error('❌ [CallService] acceptCall error:', error);\n              this.logger.error('Error accepting call:', error);\n              return throwError(\n                () => new Error(\"Erreur lors de l'acceptation de l'appel\")\n              );\n            })\n          );\n      }),\n      catchError((error) => {\n        console.error('❌ [CallService] WebRTC answer error:', error);\n        return throwError(() => new Error('Erreur WebRTC: ' + error.message));\n      })\n    );\n  }\n\n  /**\n   * Crée une réponse WebRTC complète avec médias\n   */\n  private async createWebRTCAnswer(\n    incomingCall: IncomingCall\n  ): Promise<RTCSessionDescriptionInit> {\n    try {\n      console.log(\n        '🔄 [CallService] Creating WebRTC answer for incoming call:',\n        {\n          callId: incomingCall.id,\n          callType: incomingCall.type,\n          hasOffer: !!incomingCall.offer,\n          offerLength: incomingCall.offer?.length || 0,\n        }\n      );\n\n      // Réinitialiser la PeerConnection si nécessaire\n      if (!this.peerConnection) {\n        console.log(\n          '🔧 [CallService] Initializing new PeerConnection for answer'\n        );\n        this.initializeWebRTC();\n      }\n\n      // Obtenir les médias utilisateur\n      console.log('🎥 [CallService] Getting user media for answer...');\n      const stream = await this.getUserMedia(incomingCall.type);\n\n      // Ajouter le stream à la PeerConnection\n      console.log(\n        '📤 [CallService] Adding local stream to PeerConnection for answer'\n      );\n      this.addLocalStreamToPeerConnection(stream);\n\n      // Récupérer l'offre depuis l'appel entrant\n      if (!incomingCall.offer) {\n        throw new Error('No offer received in incoming call');\n      }\n\n      const offer = JSON.parse(incomingCall.offer);\n\n      if (!offer || !offer.type || !offer.sdp) {\n        throw new Error('Invalid offer format received');\n      }\n\n      // Créer la réponse\n      const answer = await this.createAnswer(offer);\n\n      console.log('✅ [CallService] WebRTC answer created successfully');\n      return answer;\n    } catch (error) {\n      console.error('❌ [CallService] Failed to create WebRTC answer:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Rejette un appel entrant\n   */\n  rejectCall(callId: string, reason?: string): Observable<CallSuccess> {\n    console.log('🔄 [CallService] Rejecting call:', callId, reason);\n\n    return this.apollo\n      .mutate<{ rejectCall: CallSuccess }>({\n        mutation: REJECT_CALL_MUTATION,\n        variables: {\n          callId,\n          reason: reason || 'User rejected',\n        },\n      })\n      .pipe(\n        map((result) => {\n          console.log('✅ [CallService] Call rejected successfully:', result);\n\n          if (!result.data?.rejectCall) {\n            throw new Error('No response received from server');\n          }\n\n          // Mettre à jour l'état local\n          this.incomingCall.next(null);\n          this.activeCall.next(null);\n\n          // Arrêter la sonnerie\n          this.stop('ringtone');\n\n          return result.data.rejectCall;\n        }),\n        catchError((error) => {\n          console.error('❌ [CallService] rejectCall error:', error);\n          this.logger.error('Error rejecting call:', error);\n          return throwError(() => new Error(\"Erreur lors du rejet de l'appel\"));\n        })\n      );\n  }\n\n  /**\n   * Termine un appel en cours\n   */\n  endCall(callId: string): Observable<CallSuccess> {\n    console.log('🔄 [CallService] Ending call:', callId);\n\n    return this.apollo\n      .mutate<{ endCall: CallSuccess }>({\n        mutation: END_CALL_MUTATION,\n        variables: {\n          callId,\n          feedback: null, // Pas de feedback pour l'instant\n        },\n      })\n      .pipe(\n        map((result) => {\n          console.log('✅ [CallService] Call ended successfully:', result);\n\n          if (!result.data?.endCall) {\n            throw new Error('No response received from server');\n          }\n\n          // Mettre à jour l'état local\n          this.activeCall.next(null);\n          this.incomingCall.next(null);\n\n          // Arrêter tous les sons\n          this.stopAllSounds();\n          this.play('call-end');\n\n          // Nettoyer les ressources WebRTC\n          this.cleanupWebRTC();\n\n          return result.data.endCall;\n        }),\n        catchError((error) => {\n          console.error('❌ [CallService] endCall error:', error);\n          this.logger.error('Error ending call:', error);\n          return throwError(\n            () => new Error(\"Erreur lors de la fin de l'appel\")\n          );\n        })\n      );\n  }\n\n  /**\n   * Bascule l'état des médias (audio/vidéo) pendant un appel\n   */\n  toggleMedia(\n    callId: string,\n    enableVideo?: boolean,\n    enableAudio?: boolean\n  ): Observable<CallSuccess> {\n    console.log('🔄 [CallService] Toggling media:', {\n      callId,\n      enableVideo,\n      enableAudio,\n    });\n\n    return this.apollo\n      .mutate<{ toggleCallMedia: CallSuccess }>({\n        mutation: TOGGLE_CALL_MEDIA_MUTATION,\n        variables: {\n          callId,\n          video: enableVideo,\n          audio: enableAudio,\n        },\n      })\n      .pipe(\n        map((result) => {\n          console.log('✅ [CallService] Media toggled successfully:', result);\n\n          if (!result.data?.toggleCallMedia) {\n            throw new Error('No response received from server');\n          }\n\n          return result.data.toggleCallMedia;\n        }),\n        catchError((error) => {\n          console.error('❌ [CallService] toggleMedia error:', error);\n          this.logger.error('Error toggling media:', error);\n          return throwError(\n            () => new Error('Erreur lors du changement des médias')\n          );\n        })\n      );\n  }\n\n  /**\n   * Démarre les médias pour un appel accepté\n   */\n  private async startMediaForCall(\n    incomingCall: IncomingCall,\n    call: Call\n  ): Promise<Call> {\n    console.log('🎥 [CallService] Call connected - playing connection sound');\n\n    // Jouer le son de connexion\n    this.play('call-connected');\n\n    // Mettre à jour l'état local\n    this.activeCall.next(call);\n    this.incomingCall.next(null); // Supprimer l'appel entrant\n\n    return call;\n  }\n\n  /**\n   * Active/désactive la vidéo\n   */\n  toggleVideo(): boolean {\n    this.isVideoEnabled = !this.isVideoEnabled;\n    console.log('📹 [CallService] Video toggled:', this.isVideoEnabled);\n    return this.isVideoEnabled;\n  }\n\n  /**\n   * Active/désactive l'audio\n   */\n  toggleAudio(): boolean {\n    this.isAudioEnabled = !this.isAudioEnabled;\n    console.log('🎤 [CallService] Audio toggled:', this.isAudioEnabled);\n    return this.isAudioEnabled;\n  }\n\n  /**\n   * Obtient l'état de la vidéo\n   */\n  getVideoEnabled(): boolean {\n    return this.isVideoEnabled;\n  }\n\n  /**\n   * Obtient l'état de l'audio\n   */\n  getAudioEnabled(): boolean {\n    return this.isAudioEnabled;\n  }\n\n  /**\n   * Arrête tous les sons\n   */\n  private stopAllSounds(): void {\n    console.log('🔇 [CallService] Stopping all sounds');\n    Object.keys(this.sounds).forEach((name) => {\n      this.stop(name);\n    });\n  }\n\n  /**\n   * Active les sons après interaction utilisateur (pour contourner les restrictions du navigateur)\n   */\n  public enableSounds(): void {\n    console.log('🔊 [CallService] Enabling sounds after user interaction');\n    Object.values(this.sounds).forEach((sound) => {\n      if (sound) {\n        sound.muted = false;\n        sound.volume = 0.7;\n        // Jouer et arrêter immédiatement pour \"débloquer\" l'audio\n        sound\n          .play()\n          .then(() => {\n            sound.pause();\n            sound.currentTime = 0;\n          })\n          .catch(() => {\n            // Ignorer les erreurs ici\n          });\n      }\n    });\n  }\n\n  /**\n   * Nettoie les ressources WebRTC\n   */\n  private cleanupWebRTC(): void {\n    console.log('🧹 [CallService] Cleaning up WebRTC resources');\n\n    // Arrêter les tracks du stream local\n    if (this.localStream) {\n      this.localStream.getTracks().forEach((track) => {\n        track.stop();\n      });\n      this.localStream = null;\n    }\n\n    // Nettoyer les éléments vidéo\n    if (this.localVideoElement) {\n      this.localVideoElement.srcObject = null;\n    }\n    if (this.remoteVideoElement) {\n      this.remoteVideoElement.srcObject = null;\n    }\n\n    // Fermer la PeerConnection\n    if (this.peerConnection) {\n      this.peerConnection.close();\n      this.peerConnection = null;\n    }\n\n    this.remoteStream = null;\n  }\n\n  ngOnDestroy(): void {\n    this.stopAllSounds();\n    this.cleanupWebRTC();\n  }\n}\n"], "mappings": ";AAEA,SAASA,eAAe,EAAcC,UAAU,EAAMC,IAAI,QAAQ,MAAM;AACxE,SAASC,GAAG,EAAEC,UAAU,EAAEC,SAAS,QAAQ,gBAAgB;AAC3D,SAEEC,QAAQ,EACRC,UAAU,QAGL,yBAAyB;AAChC,SACEC,sBAAsB,EACtBC,oBAAoB,EACpBC,oBAAoB,EACpBC,iBAAiB,EACjBC,0BAA0B,EAC1BC,0BAA0B,EAC1BC,gCAAgC,EAChCC,wBAAwB,QACnB,4BAA4B;;;;AAMnC,OAAM,MAAOC,WAAW;EAyBtBC,YAAoBC,MAAc,EAAUC,MAAqB;IAA7C,KAAAD,MAAM,GAANA,MAAM;IAAkB,KAAAC,MAAM,GAANA,MAAM;IAxBlD;IACQ,KAAAC,UAAU,GAAG,IAAIpB,eAAe,CAAc,IAAI,CAAC;IACnD,KAAAqB,YAAY,GAAG,IAAIrB,eAAe,CAAsB,IAAI,CAAC;IAErE;IACO,KAAAsB,WAAW,GAAG,IAAI,CAACF,UAAU,CAACG,YAAY,EAAE;IAC5C,KAAAC,aAAa,GAAG,IAAI,CAACH,YAAY,CAACE,YAAY,EAAE;IAEvD;IACQ,KAAAE,MAAM,GAAwC,EAAE;IAChD,KAAAC,SAAS,GAA+B,EAAE;IAC1C,KAAAC,KAAK,GAAG,KAAK;IAErB;IACQ,KAAAC,cAAc,GAAG,IAAI;IACrB,KAAAC,cAAc,GAAG,IAAI;IAE7B;IACQ,KAAAC,cAAc,GAA6B,IAAI;IAC/C,KAAAC,WAAW,GAAuB,IAAI;IACtC,KAAAC,YAAY,GAAuB,IAAI;IACvC,KAAAC,iBAAiB,GAA4B,IAAI;IACjD,KAAAC,kBAAkB,GAA4B,IAAI;IAGxD,IAAI,CAACC,aAAa,EAAE;IACpB,IAAI,CAACC,uBAAuB,EAAE;IAC9B,IAAI,CAACC,gBAAgB,EAAE;EACzB;EAEA;;;EAGQD,uBAAuBA,CAAA;IAC7B;IACAE,UAAU,CAAC,MAAK;MACd,IAAI,CAACC,wBAAwB,EAAE;MAC/B,IAAI,CAACC,4BAA4B,EAAE;MACnC,IAAI,CAACC,sBAAsB,EAAE;IAC/B,CAAC,EAAE,IAAI,CAAC;IAER;IACAH,UAAU,CAAC,MAAK;MACd,IAAI,CAAC,IAAI,CAACjB,YAAY,CAACqB,KAAK,EAAE;QAC5B;QACA,IAAI,CAACH,wBAAwB,EAAE;QAC/B,IAAI,CAACC,4BAA4B,EAAE;QACnC,IAAI,CAACC,sBAAsB,EAAE;;IAEjC,CAAC,EAAE,KAAK,CAAC;EACX;EAEA;;;EAGQJ,gBAAgBA,CAAA;IACtBM,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;IAEtD;IACA,MAAMC,aAAa,GAAqB;MACtCC,UAAU,EAAE,CACV;QAAEC,IAAI,EAAE;MAA8B,CAAE,EACxC;QAAEA,IAAI,EAAE;MAA+B,CAAE;KAE5C;IAED,IAAI;MACF,IAAI,CAACjB,cAAc,GAAG,IAAIkB,iBAAiB,CAACH,aAAa,CAAC;MAC1D,IAAI,CAACI,yBAAyB,EAAE;MAChCN,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;KAC/D,CAAC,OAAOM,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CAAC,8CAA8C,EAAEA,KAAK,CAAC;;EAExE;EAEA;;;EAGQD,yBAAyBA,CAAA;IAC/B,IAAI,CAAC,IAAI,CAACnB,cAAc,EAAE;IAE1B;IACA,IAAI,CAACA,cAAc,CAACqB,OAAO,GAAIC,KAAK,IAAI;MACtCT,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;MACtDD,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE;QAC9CS,QAAQ,EAAED,KAAK,CAACE,OAAO,CAAC,CAAC,CAAC,EAAEC,EAAE;QAC9BC,MAAM,EAAEJ,KAAK,CAACE,OAAO,CAAC,CAAC,CAAC,EAAEG,SAAS,EAAE,CAACtD,GAAG,CAAEuD,CAAC,KAAM;UAChDC,IAAI,EAAED,CAAC,CAACC,IAAI;UACZC,OAAO,EAAEF,CAAC,CAACE,OAAO;UAClBC,UAAU,EAAEH,CAAC,CAACG;SACf,CAAC;OACH,CAAC;MAEF,IAAI,CAAC7B,YAAY,GAAGoB,KAAK,CAACE,OAAO,CAAC,CAAC,CAAC;MAEpC,IAAI,IAAI,CAACpB,kBAAkB,EAAE;QAC3BS,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC;QACtE,IAAI,CAACV,kBAAkB,CAAC4B,SAAS,GAAG,IAAI,CAAC9B,YAAY;QAErD;QACA,IAAI,CAACE,kBAAkB,CAAC6B,gBAAgB,GAAG,MAAK;UAC9CpB,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;QAC9D,CAAC;QAED,IAAI,CAACV,kBAAkB,CAAC8B,SAAS,GAAG,MAAK;UACvCrB,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;QACvD,CAAC;QAED,IAAI,CAACV,kBAAkB,CAAC+B,MAAM,GAAG,MAAK;UACpCtB,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;QAC9D,CAAC;QAED;QACA,IAAI;UACF,IAAI,CAACsB,mBAAmB,CAAC,IAAI,CAAChC,kBAAkB,CAAC;SAClD,CAAC,OAAOgB,KAAK,EAAE;UACdP,OAAO,CAACwB,IAAI,CACV,wDAAwD,EACxDjB,KAAK,CACN;UACD;UACAP,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;UAC9D,IAAI,CAACV,kBAAkB,CAACkC,IAAI,EAAE,CAACC,KAAK,CAAEC,CAAC,IAAI;YACzC3B,OAAO,CAACwB,IAAI,CAAC,sCAAsC,EAAEG,CAAC,CAAC;YACvD;YACA3B,OAAO,CAACC,GAAG,CACT,8DAA8D,CAC/D;UACH,CAAC,CAAC;;OAEL,MAAM;QACLD,OAAO,CAACwB,IAAI,CAAC,oDAAoD,CAAC;;IAEtE,CAAC;IAED;IACA,IAAI,CAACrC,cAAc,CAACyC,cAAc,GAAInB,KAAK,IAAI;MAC7C,IAAIA,KAAK,CAACoB,SAAS,EAAE;QACnB7B,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;QACvD;;IAEJ,CAAC;IAED;IACA,IAAI,CAACd,cAAc,CAAC2C,uBAAuB,GAAG,MAAK;MACjD9B,OAAO,CAACC,GAAG,CACT,oCAAoC,EACpC,IAAI,CAACd,cAAc,EAAE4C,eAAe,CACrC;IACH,CAAC;EACH;EAEA;;;EAGQvC,aAAaA,CAAA;IACnB;IACA,IAAI,CAACwC,qBAAqB,EAAE;IAE5B;IACA,IAAI,CAACC,SAAS,CAAC,cAAc,EAAE,gCAAgC,CAAC;IAEhEjC,OAAO,CAACC,GAAG,CACT,iEAAiE,CAClE;EACH;EAEA;;;EAGQ+B,qBAAqBA,CAAA;IAC3B,IAAI;MACF;MACA,MAAME,YAAY,GAAG,KAAKC,MAAM,CAACC,YAAY,IAC1CD,MAAc,CAACE,kBAAkB,EAAC,CAAE;MAEvC;MACA,IAAI,CAACvD,MAAM,CAAC,oBAAoB,CAAC,GAC/B,IAAI,CAACwD,mBAAmB,CAACJ,YAAY,CAAC;MAExC;MACA,IAAI,CAACpD,MAAM,CAAC,0BAA0B,CAAC,GACrC,IAAI,CAACyD,oBAAoB,CAACL,YAAY,CAAC;MAEzC;MACA,IAAI,CAACpD,MAAM,CAAC,oBAAoB,CAAC,GAAG,IAAI,CAAC0D,cAAc,CAACN,YAAY,CAAC;MAErE;MACA,IAAI,CAACpD,MAAM,CAAC,wBAAwB,CAAC,GACnC,IAAI,CAAC2D,uBAAuB,CAACP,YAAY,CAAC;MAE5ClC,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;KACrE,CAAC,OAAOM,KAAK,EAAE;MACdP,OAAO,CAACwB,IAAI,CACV,qDAAqD,EACrDjB,KAAK,CACN;;EAEL;EAEA;;;EAGQ+B,mBAAmBA,CAACJ,YAA0B;IACpD,MAAMQ,KAAK,GAAG,IAAIC,KAAK,EAAE;IACzBD,KAAK,CAACE,MAAM,GAAG,GAAG;IAElB,IAAI7D,SAAS,GAAG,KAAK;IACrB,IAAI8D,UAAU,GAAU,EAAE;IAEzBH,KAAa,CAACI,aAAa,GAAG,MAAK;MAClC,IAAI/D,SAAS,EAAE,OAAOgE,OAAO,CAACC,OAAO,EAAE;MAEvCjE,SAAS,GAAG,IAAI;MAEhB,MAAMkE,UAAU,GAAGA,CAAA,KAAK;QACtB,IAAI,CAAClE,SAAS,EAAE;QAEhB;QACA,MAAMmE,MAAM,GAAG,CACb;UAAEC,IAAI,EAAE,MAAM;UAAEC,QAAQ,EAAE;QAAK,CAAE,EACjC;UAAED,IAAI,EAAE,MAAM;UAAEC,QAAQ,EAAE;QAAK,CAAE,EACjC;UAAED,IAAI,EAAE,MAAM;UAAEC,QAAQ,EAAE;QAAI,CAAE,EAChC;UAAED,IAAI,EAAE,MAAM;UAAEC,QAAQ,EAAE;QAAI,CAAE,EAChC;UAAED,IAAI,EAAE,MAAM;UAAEC,QAAQ,EAAE;QAAK,CAAE,EACjC;UAAED,IAAI,EAAE,MAAM;UAAEC,QAAQ,EAAE;QAAK,CAAE,EACjC;UAAED,IAAI,EAAE,MAAM;UAAEC,QAAQ,EAAE;QAAI,CAAE,EAChC;UAAED,IAAI,EAAE,MAAM;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAE;QAAA,CACnC;;QAED,IAAIC,WAAW,GAAGnB,YAAY,CAACmB,WAAW;QAE1CH,MAAM,CAACI,OAAO,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAI;UAC7B,IAAI,CAACzE,SAAS,EAAE;UAEhB,MAAM0E,UAAU,GAAGvB,YAAY,CAACwB,gBAAgB,EAAE;UAClD,MAAMC,QAAQ,GAAGzB,YAAY,CAAC0B,UAAU,EAAE;UAE1CH,UAAU,CAACI,OAAO,CAACF,QAAQ,CAAC;UAC5BA,QAAQ,CAACE,OAAO,CAAC3B,YAAY,CAAC4B,WAAW,CAAC;UAE1CL,UAAU,CAACM,SAAS,CAAChE,KAAK,GAAGwD,IAAI,CAACJ,IAAI;UACtCM,UAAU,CAACO,IAAI,GAAG,QAAQ,CAAC,CAAC;UAE5B;UACAL,QAAQ,CAACM,IAAI,CAACC,cAAc,CAAC,CAAC,EAAEb,WAAW,CAAC;UAC5CM,QAAQ,CAACM,IAAI,CAACE,uBAAuB,CAAC,GAAG,EAAEd,WAAW,GAAG,IAAI,CAAC;UAC9DM,QAAQ,CAACM,IAAI,CAACE,uBAAuB,CACnC,GAAG,EACHd,WAAW,GAAGE,IAAI,CAACH,QAAQ,GAAG,GAAG,CAClC;UACDO,QAAQ,CAACM,IAAI,CAACG,4BAA4B,CACxC,IAAI,EACJf,WAAW,GAAGE,IAAI,CAACH,QAAQ,CAC5B;UAEDK,UAAU,CAACY,KAAK,CAAChB,WAAW,CAAC;UAC7BI,UAAU,CAACa,IAAI,CAACjB,WAAW,GAAGE,IAAI,CAACH,QAAQ,CAAC;UAE5CC,WAAW,IAAIE,IAAI,CAACH,QAAQ,GAAG,IAAI,CAAC,CAAC;QACvC,CAAC,CAAC;QAEF;QACA,MAAMmB,SAAS,GAAG5E,UAAU,CAAC,MAAK;UAChC,IAAIZ,SAAS,EAAE;YACbkE,UAAU,EAAE;;QAEhB,CAAC,EAAE,CAACI,WAAW,GAAGnB,YAAY,CAACmB,WAAW,GAAG,GAAG,IAAI,IAAI,CAAC;QAEzDR,UAAU,CAAC2B,IAAI,CAACD,SAAS,CAAC;MAC5B,CAAC;MAEDtB,UAAU,EAAE;MACZ,OAAOF,OAAO,CAACC,OAAO,EAAE;IAC1B,CAAC;IAEAN,KAAa,CAAC+B,aAAa,GAAG,MAAK;MAClC1F,SAAS,GAAG,KAAK;MACjB8D,UAAU,CAACS,OAAO,CAAE1C,EAAE,IAAK8D,YAAY,CAAC9D,EAAE,CAAC,CAAC;MAC5CiC,UAAU,GAAG,EAAE;IACjB,CAAC;IAED,OAAOH,KAAK;EACd;EAEA;;;EAGQH,oBAAoBA,CAACL,YAA0B;IACrD,MAAMQ,KAAK,GAAG,IAAIC,KAAK,EAAE;IACzBD,KAAK,CAACE,MAAM,GAAG,GAAG;IAEjBF,KAAa,CAACI,aAAa,GAAG,MAAK;MAClC;MACA,MAAMI,MAAM,GAAG,CACb;QAAEC,IAAI,EAAE,MAAM;QAAEC,QAAQ,EAAE;MAAI,CAAE,EAChC;QAAED,IAAI,EAAE,MAAM;QAAEC,QAAQ,EAAE;MAAI,CAAE,EAChC;QAAED,IAAI,EAAE,MAAM;QAAEC,QAAQ,EAAE;MAAI,CAAE,EAChC;QAAED,IAAI,EAAE,MAAM;QAAEC,QAAQ,EAAE;MAAG,CAAE,CAAE;MAAA,CAClC;;MAED,IAAIC,WAAW,GAAGnB,YAAY,CAACmB,WAAW;MAE1CH,MAAM,CAACI,OAAO,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAI;QAC7B,MAAMC,UAAU,GAAGvB,YAAY,CAACwB,gBAAgB,EAAE;QAClD,MAAMC,QAAQ,GAAGzB,YAAY,CAAC0B,UAAU,EAAE;QAE1CH,UAAU,CAACI,OAAO,CAACF,QAAQ,CAAC;QAC5BA,QAAQ,CAACE,OAAO,CAAC3B,YAAY,CAAC4B,WAAW,CAAC;QAE1CL,UAAU,CAACM,SAAS,CAAChE,KAAK,GAAGwD,IAAI,CAACJ,IAAI;QACtCM,UAAU,CAACO,IAAI,GAAG,UAAU,CAAC,CAAC;QAE9B;QACAL,QAAQ,CAACM,IAAI,CAACC,cAAc,CAAC,CAAC,EAAEb,WAAW,CAAC;QAC5CM,QAAQ,CAACM,IAAI,CAACE,uBAAuB,CAAC,IAAI,EAAEd,WAAW,GAAG,IAAI,CAAC;QAC/DM,QAAQ,CAACM,IAAI,CAACE,uBAAuB,CACnC,IAAI,EACJd,WAAW,GAAGE,IAAI,CAACH,QAAQ,GAAG,GAAG,CAClC;QACDO,QAAQ,CAACM,IAAI,CAACG,4BAA4B,CACxC,IAAI,EACJf,WAAW,GAAGE,IAAI,CAACH,QAAQ,CAC5B;QAEDK,UAAU,CAACY,KAAK,CAAChB,WAAW,CAAC;QAC7BI,UAAU,CAACa,IAAI,CAACjB,WAAW,GAAGE,IAAI,CAACH,QAAQ,CAAC;QAE5CC,WAAW,IAAIE,IAAI,CAACH,QAAQ,GAAG,GAAG,CAAC,CAAC;MACtC,CAAC,CAAC;;MAEF,OAAOL,OAAO,CAACC,OAAO,EAAE;IAC1B,CAAC;IAED,OAAON,KAAK;EACd;EAEA;;;EAGQF,cAAcA,CAACN,YAA0B;IAC/C,MAAMQ,KAAK,GAAG,IAAIC,KAAK,EAAE;IACzBD,KAAK,CAACE,MAAM,GAAG,GAAG;IAEjBF,KAAa,CAACI,aAAa,GAAG,MAAK;MAClC;MACA,MAAMI,MAAM,GAAG,CACb;QAAEC,IAAI,EAAE,MAAM;QAAEC,QAAQ,EAAE;MAAG,CAAE,EAC/B;QAAED,IAAI,EAAE,MAAM;QAAEC,QAAQ,EAAE;MAAG,CAAE,EAC/B;QAAED,IAAI,EAAE,MAAM;QAAEC,QAAQ,EAAE;MAAG,CAAE,EAC/B;QAAED,IAAI,EAAE,KAAK;QAAEC,QAAQ,EAAE;MAAG,CAAE,CAAE;MAAA,CACjC;;MAED,IAAIC,WAAW,GAAGnB,YAAY,CAACmB,WAAW;MAE1CH,MAAM,CAACI,OAAO,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAI;QAC7B,MAAMC,UAAU,GAAGvB,YAAY,CAACwB,gBAAgB,EAAE;QAClD,MAAMC,QAAQ,GAAGzB,YAAY,CAAC0B,UAAU,EAAE;QAE1CH,UAAU,CAACI,OAAO,CAACF,QAAQ,CAAC;QAC5BA,QAAQ,CAACE,OAAO,CAAC3B,YAAY,CAAC4B,WAAW,CAAC;QAE1CL,UAAU,CAACM,SAAS,CAAChE,KAAK,GAAGwD,IAAI,CAACJ,IAAI;QACtCM,UAAU,CAACO,IAAI,GAAG,MAAM,CAAC,CAAC;QAE1B;QACAL,QAAQ,CAACM,IAAI,CAACC,cAAc,CAAC,CAAC,EAAEb,WAAW,CAAC;QAC5CM,QAAQ,CAACM,IAAI,CAACE,uBAAuB,CAAC,GAAG,EAAEd,WAAW,GAAG,IAAI,CAAC;QAC9DM,QAAQ,CAACM,IAAI,CAACE,uBAAuB,CACnC,GAAG,EACHd,WAAW,GAAGE,IAAI,CAACH,QAAQ,GAAG,GAAG,CAClC;QACDO,QAAQ,CAACM,IAAI,CAACG,4BAA4B,CACxC,IAAI,EACJf,WAAW,GAAGE,IAAI,CAACH,QAAQ,CAC5B;QAEDK,UAAU,CAACY,KAAK,CAAChB,WAAW,CAAC;QAC7BI,UAAU,CAACa,IAAI,CAACjB,WAAW,GAAGE,IAAI,CAACH,QAAQ,CAAC;QAE5CC,WAAW,IAAIE,IAAI,CAACH,QAAQ,GAAG,GAAG,CAAC,CAAC;MACtC,CAAC,CAAC;;MAEF,OAAOL,OAAO,CAACC,OAAO,EAAE;IAC1B,CAAC;IAED,OAAON,KAAK;EACd;EAEA;;;EAGQD,uBAAuBA,CAC7BP,YAA0B;IAE1B,MAAMQ,KAAK,GAAG,IAAIC,KAAK,EAAE;IACzBD,KAAK,CAACE,MAAM,GAAG,GAAG;IAEjBF,KAAa,CAACI,aAAa,GAAG,MAAK;MAClC;MACA,MAAM6B,KAAK,GAAG,CACZ;QAAExB,IAAI,EAAE,MAAM;QAAEC,QAAQ,EAAE,IAAI;QAAEwB,KAAK,EAAE;MAAC,CAAE,EAC1C;QAAEzB,IAAI,EAAE,MAAM;QAAEC,QAAQ,EAAE,IAAI;QAAEwB,KAAK,EAAE;MAAG,CAAE,CAAE;MAAA,CAC/C;;MAEDD,KAAK,CAACrB,OAAO,CAAEC,IAAI,IAAI;QACrB5D,UAAU,CAAC,MAAK;UACd,MAAM8D,UAAU,GAAGvB,YAAY,CAACwB,gBAAgB,EAAE;UAClD,MAAMC,QAAQ,GAAGzB,YAAY,CAAC0B,UAAU,EAAE;UAE1CH,UAAU,CAACI,OAAO,CAACF,QAAQ,CAAC;UAC5BA,QAAQ,CAACE,OAAO,CAAC3B,YAAY,CAAC4B,WAAW,CAAC;UAE1CL,UAAU,CAACM,SAAS,CAAChE,KAAK,GAAGwD,IAAI,CAACJ,IAAI;UACtCM,UAAU,CAACO,IAAI,GAAG,UAAU,CAAC,CAAC;UAE9B,MAAMa,SAAS,GAAG3C,YAAY,CAACmB,WAAW;UAE1C;UACAM,QAAQ,CAACM,IAAI,CAACC,cAAc,CAAC,CAAC,EAAEW,SAAS,CAAC;UAC1ClB,QAAQ,CAACM,IAAI,CAACE,uBAAuB,CAAC,GAAG,EAAEU,SAAS,GAAG,IAAI,CAAC;UAC5DlB,QAAQ,CAACM,IAAI,CAACE,uBAAuB,CACnC,GAAG,EACHU,SAAS,GAAGtB,IAAI,CAACH,QAAQ,GAAG,GAAG,CAChC;UACDO,QAAQ,CAACM,IAAI,CAACG,4BAA4B,CACxC,IAAI,EACJS,SAAS,GAAGtB,IAAI,CAACH,QAAQ,CAC1B;UAEDK,UAAU,CAACY,KAAK,CAACQ,SAAS,CAAC;UAC3BpB,UAAU,CAACa,IAAI,CAACO,SAAS,GAAGtB,IAAI,CAACH,QAAQ,CAAC;QAC5C,CAAC,EAAEG,IAAI,CAACqB,KAAK,GAAG,IAAI,CAAC;MACvB,CAAC,CAAC;MAEF,OAAO7B,OAAO,CAACC,OAAO,EAAE;IAC1B,CAAC;IAED,OAAON,KAAK;EACd;EAEA;;;EAGQT,SAASA,CAAC6C,IAAY,EAAEC,IAAY;IAC1C,IAAI;MACF,MAAMrC,KAAK,GAAG,IAAIC,KAAK,EAAE;MAEzB;MACAD,KAAK,CAACsC,OAAO,GAAG,MAAM;MACtBtC,KAAK,CAACE,MAAM,GAAG,GAAG;MAElB;MACAF,KAAK,CAACuC,gBAAgB,CAAC,gBAAgB,EAAE,MAAK;QAC5CjF,OAAO,CAACC,GAAG,CACT,yBAAyB6E,IAAI,6BAA6BC,IAAI,EAAE,CACjE;MACH,CAAC,CAAC;MAEFrC,KAAK,CAACuC,gBAAgB,CAAC,OAAO,EAAGtD,CAAC,IAAI;QACpC3B,OAAO,CAACO,KAAK,CACX,uCAAuCuE,IAAI,SAASC,IAAI,GAAG,EAC3DpD,CAAC,CACF;QACD3B,OAAO,CAACC,GAAG,CACT,mCAAmC6E,IAAI,6BAA6B,CACrE;QAED;QACA,MAAMI,OAAO,GAAGH,IAAI,CAACI,UAAU,CAAC,GAAG,CAAC,GAAGJ,IAAI,CAACK,SAAS,CAAC,CAAC,CAAC,GAAGL,IAAI;QAC/D,IAAIG,OAAO,KAAKH,IAAI,EAAE;UACpBpF,UAAU,CAAC,MAAK;YACd+C,KAAK,CAAC2C,GAAG,GAAGH,OAAO;YACnBxC,KAAK,CAAC4C,IAAI,EAAE;UACd,CAAC,EAAE,GAAG,CAAC;;MAEX,CAAC,CAAC;MAEF5C,KAAK,CAACuC,gBAAgB,CAAC,OAAO,EAAE,MAAK;QACnC,IAAI,CAAClG,SAAS,CAAC+F,IAAI,CAAC,GAAG,KAAK;QAC5B9E,OAAO,CAACC,GAAG,CAAC,0BAA0B6E,IAAI,QAAQ,CAAC;MACrD,CAAC,CAAC;MAEF;MACApC,KAAK,CAAC2C,GAAG,GAAGN,IAAI;MAChBrC,KAAK,CAAC4C,IAAI,EAAE;MAEZ,IAAI,CAACxG,MAAM,CAACgG,IAAI,CAAC,GAAGpC,KAAK;MACzB,IAAI,CAAC3D,SAAS,CAAC+F,IAAI,CAAC,GAAG,KAAK;MAE5B9E,OAAO,CAACC,GAAG,CAAC,kCAAkC6E,IAAI,SAASC,IAAI,EAAE,CAAC;KACnE,CAAC,OAAOxE,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CACX,oDAAoDuE,IAAI,GAAG,EAC3DvE,KAAK,CACN;;EAEL;EAEA;;;EAGQkB,IAAIA,CAACqD,IAAY,EAAES,IAAA,GAAgB,KAAK;IAC9C,IAAI,IAAI,CAACvG,KAAK,EAAE;MACdgB,OAAO,CAACC,GAAG,CAAC,0BAA0B6E,IAAI,QAAQ,CAAC;MACnD;;IAGF,IAAI;MACF;MACA,IAAIU,KAAK;MACT,IACEV,IAAI,KAAK,UAAU,IACnBA,IAAI,KAAK,gBAAgB,IACzBA,IAAI,KAAK,UAAU,EACnB;QACA,MAAMW,aAAa,GAAG,GAAGX,IAAI,YAAY;QACzCU,KAAK,GAAG,IAAI,CAAC1G,MAAM,CAAC2G,aAAa,CAAC;QAClC,IAAID,KAAK,EAAE;UACTxF,OAAO,CAACC,GAAG,CACT,yDAAyD6E,IAAI,EAAE,CAChE;;OAEJ,MAAM;QACL;QACAU,KAAK,GAAG,IAAI,CAAC1G,MAAM,CAACgG,IAAI,CAAC;QACzB,IAAI,CAACU,KAAK,IAAIA,KAAK,CAACjF,KAAK,EAAE;UACzB,MAAMkF,aAAa,GAAG,GAAGX,IAAI,YAAY;UACzCU,KAAK,GAAG,IAAI,CAAC1G,MAAM,CAAC2G,aAAa,CAAC;UAClC,IAAID,KAAK,EAAE;YACTxF,OAAO,CAACC,GAAG,CAAC,8CAA8C6E,IAAI,EAAE,CAAC;;;;MAKvE,IAAI,CAACU,KAAK,EAAE;QACVxF,OAAO,CAACwB,IAAI,CACV,0BAA0BsD,IAAI,6CAA6C,CAC5E;QACD;QACA,IAAI,CAACY,cAAc,CACjBZ,IAAI,KAAK,UAAU,GAAG,GAAG,GAAGA,IAAI,KAAK,gBAAgB,GAAG,IAAI,GAAG,GAAG,CACnE;QACD;;MAGFU,KAAK,CAACD,IAAI,GAAGA,IAAI;MACjBC,KAAK,CAAC5C,MAAM,GAAG,GAAG;MAElB,IAAI,CAAC,IAAI,CAAC7D,SAAS,CAAC+F,IAAI,CAAC,EAAE;QACzB9E,OAAO,CAACC,GAAG,CAAC,mCAAmC6E,IAAI,WAAWS,IAAI,GAAG,CAAC;QAEtE;QACA,IAAKC,KAAa,CAAC1C,aAAa,EAAE;UAC/B0C,KAAa,CACX1C,aAAa,EAAE,CACf6C,IAAI,CAAC,MAAK;YACT3F,OAAO,CAACC,GAAG,CACT,mCAAmC6E,IAAI,uBAAuB,CAC/D;YACD,IAAI,CAAC/F,SAAS,CAAC+F,IAAI,CAAC,GAAG,IAAI;YAE3B;YACA,IAAIA,IAAI,KAAK,UAAU,IAAI,CAACS,IAAI,EAAE;cAChC;cACA5F,UAAU,CAAC,MAAK;gBACd,IAAI,CAACZ,SAAS,CAAC+F,IAAI,CAAC,GAAG,KAAK;cAC9B,CAAC,EAAE,IAAI,CAAC;aACT,MAAM,IAAIA,IAAI,KAAK,UAAU,EAAE;cAC9B;cACAnF,UAAU,CACR,MAAK;gBACH,IAAI,CAACZ,SAAS,CAAC+F,IAAI,CAAC,GAAG,KAAK;cAC9B,CAAC,EACDA,IAAI,KAAK,gBAAgB,GAAG,IAAI,GAAG,IAAI,CACxC;;UAEL,CAAC,CAAC,CACDpD,KAAK,CAAEnB,KAAU,IAAI;YACpBP,OAAO,CAACO,KAAK,CACX,iDAAiDuE,IAAI,GAAG,EACxDvE,KAAK,CACN;UACH,CAAC,CAAC;SACL,MAAM;UACL;UACAiF,KAAK,CAACnC,WAAW,GAAG,CAAC;UACrBmC,KAAK,CACF/D,IAAI,EAAE,CACNkE,IAAI,CAAC,MAAK;YACT3F,OAAO,CAACC,GAAG,CACT,yBAAyB6E,IAAI,uBAAuB,CACrD;YACD,IAAI,CAAC/F,SAAS,CAAC+F,IAAI,CAAC,GAAG,IAAI;UAC7B,CAAC,CAAC,CACDpD,KAAK,CAAEnB,KAAK,IAAI;YACfP,OAAO,CAACO,KAAK,CACX,uCAAuCuE,IAAI,GAAG,EAC9CvE,KAAK,CACN;YAED;YACA,MAAMkF,aAAa,GAAG,GAAGX,IAAI,YAAY;YACzC,MAAMc,cAAc,GAAG,IAAI,CAAC9G,MAAM,CAAC2G,aAAa,CAAC;YACjD,IAAIG,cAAc,IAAKA,cAAsB,CAAC9C,aAAa,EAAE;cAC3D9C,OAAO,CAACC,GAAG,CACT,wDAAwD6E,IAAI,EAAE,CAC/D;cACD,IAAI,CAACrD,IAAI,CAACqD,IAAI,EAAES,IAAI,CAAC;aACtB,MAAM;cACL;cACA,IAAI,CAACG,cAAc,CACjBZ,IAAI,KAAK,UAAU,GACf,GAAG,GACHA,IAAI,KAAK,gBAAgB,GACzB,IAAI,GACJ,GAAG,CACR;;UAEL,CAAC,CAAC;;OAEP,MAAM;QACL9E,OAAO,CAACC,GAAG,CAAC,0BAA0B6E,IAAI,kBAAkB,CAAC;;KAEhE,CAAC,OAAOvE,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CACX,4CAA4CuE,IAAI,GAAG,EACnDvE,KAAK,CACN;MACD;MACA,IAAI,CAACmF,cAAc,CACjBZ,IAAI,KAAK,UAAU,GAAG,GAAG,GAAGA,IAAI,KAAK,gBAAgB,GAAG,IAAI,GAAG,GAAG,CACnE;;EAEL;EAEA;;;EAGQY,cAAcA,CAAC3B,SAAiB;IACtC,IAAI;MACF,MAAM7B,YAAY,GAAG,KAAKC,MAAM,CAACC,YAAY,IAC1CD,MAAc,CAACE,kBAAkB,EAAC,CAAE;MACvC,MAAMoB,UAAU,GAAGvB,YAAY,CAACwB,gBAAgB,EAAE;MAClD,MAAMC,QAAQ,GAAGzB,YAAY,CAAC0B,UAAU,EAAE;MAE1CH,UAAU,CAACI,OAAO,CAACF,QAAQ,CAAC;MAC5BA,QAAQ,CAACE,OAAO,CAAC3B,YAAY,CAAC4B,WAAW,CAAC;MAE1CL,UAAU,CAACM,SAAS,CAAChE,KAAK,GAAGgE,SAAS;MACtCN,UAAU,CAACO,IAAI,GAAG,MAAM;MAExBL,QAAQ,CAACM,IAAI,CAACC,cAAc,CAAC,GAAG,EAAEhC,YAAY,CAACmB,WAAW,CAAC;MAC3DM,QAAQ,CAACM,IAAI,CAACG,4BAA4B,CACxC,IAAI,EACJlC,YAAY,CAACmB,WAAW,GAAG,GAAG,CAC/B;MAEDI,UAAU,CAACY,KAAK,CAACnC,YAAY,CAACmB,WAAW,CAAC;MAC1CI,UAAU,CAACa,IAAI,CAACpC,YAAY,CAACmB,WAAW,GAAG,GAAG,CAAC;MAE/CrD,OAAO,CAACC,GAAG,CAAC,2CAA2C8D,SAAS,IAAI,CAAC;KACtE,CAAC,OAAOxD,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CAAC,6CAA6C,EAAEA,KAAK,CAAC;;EAEvE;EAEA;;;EAGQ+D,IAAIA,CAACQ,IAAY;IACvB,IAAI;MACF,IAAIU,KAAK,GAAG,IAAI,CAAC1G,MAAM,CAACgG,IAAI,CAAC;MAE7B;MACA,IAAI,CAACU,KAAK,EAAE;QACVA,KAAK,GAAG,IAAI,CAAC1G,MAAM,CAAC,GAAGgG,IAAI,YAAY,CAAC;;MAG1C,IAAI,CAACU,KAAK,EAAE;QACVxF,OAAO,CAACwB,IAAI,CAAC,0BAA0BsD,IAAI,yBAAyB,CAAC;QACrE;;MAGF,IAAI,IAAI,CAAC/F,SAAS,CAAC+F,IAAI,CAAC,EAAE;QACxB9E,OAAO,CAACC,GAAG,CAAC,oCAAoC6E,IAAI,EAAE,CAAC;QAEvD;QACA,IAAKU,KAAa,CAACf,aAAa,EAAE;UAC/Be,KAAa,CAACf,aAAa,EAAE;SAC/B,MAAM;UACLe,KAAK,CAACK,KAAK,EAAE;UACbL,KAAK,CAACnC,WAAW,GAAG,CAAC;;QAGvB,IAAI,CAACtE,SAAS,CAAC+F,IAAI,CAAC,GAAG,KAAK;OAC7B,MAAM;QACL9E,OAAO,CAACC,GAAG,CAAC,0BAA0B6E,IAAI,kBAAkB,CAAC;;KAEhE,CAAC,OAAOvE,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CAAC,wCAAwCuE,IAAI,GAAG,EAAEvE,KAAK,CAAC;;EAEzE;EAEA;;;EAGQX,wBAAwBA,CAAA;IAC9B;IAEA,IAAI;MACF,IAAI,CAACrB,MAAM,CACRuH,SAAS,CAAiC;QACzCC,KAAK,EAAE7H,0BAA0B;QACjC8H,WAAW,EAAE,KAAK,CAAE;OACrB,CAAC,CACDF,SAAS,CAAC;QACTG,IAAI,EAAEA,CAAC;UAAEC,IAAI;UAAEC;QAAM,CAAE,KAAI;UACzB,IAAIA,MAAM,EAAE;YACVnG,OAAO,CAACwB,IAAI,CACV,kDAAkD,EAClD2E,MAAM,CACP;;UAGH,IAAID,IAAI,EAAExH,YAAY,EAAE;YACtB;YACA,IAAI,CAAC0H,kBAAkB,CAACF,IAAI,CAACxH,YAAY,CAAC;;QAE9C,CAAC;QACD6B,KAAK,EAAGA,KAAK,IAAI;UACfP,OAAO,CAACO,KAAK,CACX,sDAAsD,EACtDA,KAAK,CACN;UAED;UACAZ,UAAU,CAAC,MAAK;YACd,IAAI,CAACC,wBAAwB,EAAE;UACjC,CAAC,EAAE,KAAK,CAAC;QACX,CAAC;QACDyG,QAAQ,EAAEA,CAAA,KAAK;UACb;UACA;UACA1G,UAAU,CAAC,MAAK;YACd,IAAI,CAACC,wBAAwB,EAAE;UACjC,CAAC,EAAE,IAAI,CAAC;QACV;OACD,CAAC;KACL,CAAC,OAAOW,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CAAC,gDAAgD,EAAEA,KAAK,CAAC;MACtE;MACAZ,UAAU,CAAC,MAAK;QACd,IAAI,CAACC,wBAAwB,EAAE;MACjC,CAAC,EAAE,KAAK,CAAC;;EAEb;EAEA;;;EAGO0G,wBAAwBA,CAAA;IAC7B;IACA,IAAI,CAAC1G,wBAAwB,EAAE;IAC/B,IAAI,CAACC,4BAA4B,EAAE;IACnC,IAAI,CAACC,sBAAsB,EAAE;EAC/B;EAEA;;;EAGQA,sBAAsBA,CAAA;IAC5BE,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC;IAEtE,IAAI;MACF,IAAI,CAAC1B,MAAM,CACRuH,SAAS,CAAsB;QAC9BC,KAAK,EAAE3H,wBAAwB;QAC/BmI,SAAS,EAAE;UACTC,MAAM,EAAE,IAAI,CAACC,aAAa,IAAI;SAC/B;QACDT,WAAW,EAAE;OACd,CAAC,CACDF,SAAS,CAAC;QACTG,IAAI,EAAEA,CAAC;UAAEC,IAAI;UAAEC;QAAM,CAAE,KAAI;UACzB,IAAIA,MAAM,EAAE;YACVnG,OAAO,CAACwB,IAAI,CACV,mDAAmD,EACnD2E,MAAM,CACP;;UAGH,IAAID,IAAI,EAAEQ,UAAU,EAAE;YACpB1G,OAAO,CAACC,GAAG,CACT,wCAAwC,EACxCiG,IAAI,CAACQ,UAAU,CAChB;YACD,IAAI,CAACC,gBAAgB,CAACT,IAAI,CAACQ,UAAU,CAAC;;QAE1C,CAAC;QACDnG,KAAK,EAAGA,KAAK,IAAI;UACfP,OAAO,CAACO,KAAK,CACX,oDAAoD,EACpDA,KAAK,CACN;UACD;UACAZ,UAAU,CAAC,MAAK;YACd,IAAI,CAACG,sBAAsB,EAAE;UAC/B,CAAC,EAAE,IAAI,CAAC;QACV,CAAC;QACDuG,QAAQ,EAAEA,CAAA,KAAK;UACbrG,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;UAClE;UACAN,UAAU,CAAC,MAAK;YACd,IAAI,CAACG,sBAAsB,EAAE;UAC/B,CAAC,EAAE,IAAI,CAAC;QACV;OACD,CAAC;KACL,CAAC,OAAOS,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CACX,4DAA4D,EAC5DA,KAAK,CACN;MACDZ,UAAU,CAAC,MAAK;QACd,IAAI,CAACG,sBAAsB,EAAE;MAC/B,CAAC,EAAE,IAAI,CAAC;;EAEZ;EAEA;;;EAGc6G,gBAAgBA,CAACC,MAAW;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACxC9G,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAE2G,MAAM,CAAC5C,IAAI,CAAC;MAElE,IAAI;QACF,QAAQ4C,MAAM,CAAC5C,IAAI;UACjB,KAAK,OAAO;YACV,MAAM6C,KAAI,CAACE,iBAAiB,CAACH,MAAM,CAAC;YACpC;UACF,KAAK,QAAQ;YACX,MAAMC,KAAI,CAACG,kBAAkB,CAACJ,MAAM,CAAC;YACrC;UACF,KAAK,eAAe;YAClB,MAAMC,KAAI,CAACI,wBAAwB,CAACL,MAAM,CAAC;YAC3C;UACF;YACE5G,OAAO,CAACwB,IAAI,CAAC,uCAAuC,EAAEoF,MAAM,CAAC5C,IAAI,CAAC;;OAEvE,CAAC,OAAOzD,KAAK,EAAE;QACdP,OAAO,CAACO,KAAK,CAAC,6CAA6C,EAAEA,KAAK,CAAC;;IACpE;EACH;EAEA;;;EAGcwG,iBAAiBA,CAACH,MAAW;IAAA,IAAAM,MAAA;IAAA,OAAAJ,iBAAA;MACzC9G,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;MAErD,IAAI,CAACiH,MAAI,CAAC/H,cAAc,EAAE;QACxBa,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;QACpEiH,MAAI,CAACxH,gBAAgB,EAAE;;MAGzB,MAAMyH,KAAK,GAAGC,IAAI,CAACC,KAAK,CAACT,MAAM,CAACV,IAAI,CAAC;MACrC,MAAMgB,MAAI,CAAC/H,cAAe,CAACmI,oBAAoB,CAACH,KAAK,CAAC;MACtDnH,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;IAAC;EAC/D;EAEA;;;EAGc+G,kBAAkBA,CAACJ,MAAW;IAAA,IAAAW,MAAA;IAAA,OAAAT,iBAAA;MAC1C9G,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;MAEtD,IAAI,CAACsH,MAAI,CAACpI,cAAc,EAAE;QACxBa,OAAO,CAACO,KAAK,CAAC,sDAAsD,CAAC;QACrE;;MAGF,MAAMiH,MAAM,GAAGJ,IAAI,CAACC,KAAK,CAACT,MAAM,CAACV,IAAI,CAAC;MACtC,MAAMqB,MAAI,CAACpI,cAAc,CAACmI,oBAAoB,CAACE,MAAM,CAAC;MACtDxH,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;IAAC;EAChE;EAEA;;;EAGcgH,wBAAwBA,CAACL,MAAW;IAAA,IAAAa,MAAA;IAAA,OAAAX,iBAAA;MAChD9G,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;MAE7D,IAAI,CAACwH,MAAI,CAACtI,cAAc,EAAE;QACxBa,OAAO,CAACO,KAAK,CAAC,sDAAsD,CAAC;QACrE;;MAGF,MAAMsB,SAAS,GAAGuF,IAAI,CAACC,KAAK,CAACT,MAAM,CAACV,IAAI,CAAC;MACzC,MAAMuB,MAAI,CAACtI,cAAc,CAACuI,eAAe,CAAC7F,SAAS,CAAC;MACpD7B,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC;IAAC;EACzE;EAEA;;;EAGO0H,UAAUA,CAAA;IACf3H,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;IAEjD;IACAD,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;IACrC,IAAI,CAACwB,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC;IAE3B9B,UAAU,CAAC,MAAK;MACd,IAAI,CAAC2E,IAAI,CAAC,UAAU,CAAC;MACrBtE,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;MAC3C,IAAI,CAACwB,IAAI,CAAC,gBAAgB,CAAC;IAC7B,CAAC,EAAE,IAAI,CAAC;IAER9B,UAAU,CAAC,MAAK;MACdK,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;MACrC,IAAI,CAACwB,IAAI,CAAC,UAAU,CAAC;IACvB,CAAC,EAAE,IAAI,CAAC;EACV;EAEA;;;EAGOmG,YAAYA,CAAA;IACjB5H,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC;IAChED,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAE4H,MAAM,CAACC,IAAI,CAAC,IAAI,CAAChJ,MAAM,CAAC,CAAC;IAE3E;IACA,MAAMiJ,QAAQ,GAAG,IAAI,CAACjJ,MAAM,CAAC,UAAU,CAAC;IACxC,MAAMkJ,iBAAiB,GAAG,IAAI,CAAClJ,MAAM,CAAC,oBAAoB,CAAC;IAE3D,IAAIiJ,QAAQ,EAAE;MACZ/H,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAE;QACjDoF,GAAG,EAAE0C,QAAQ,CAAC1C,GAAG;QACjBnE,UAAU,EAAE6G,QAAQ,CAAC7G,UAAU;QAC/BX,KAAK,EAAEwH,QAAQ,CAACxH,KAAK;QACrB6C,QAAQ,EAAE2E,QAAQ,CAAC3E;OACpB,CAAC;KACH,MAAM;MACLpD,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;;IAGvD,IAAI+H,iBAAiB,EAAE;MACrBhI,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;;IAGzD;IACAD,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;IACpE,IAAI,CAACwB,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC;IAE3B;IACA9B,UAAU,CAAC,MAAK;MACd,IAAI,CAAC2E,IAAI,CAAC,UAAU,CAAC;MACrBtE,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;IACvD,CAAC,EAAE,IAAI,CAAC;EACV;EAEA;;;EAGOgI,mBAAmBA,CAAA;IACxBjI,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;IAE/D;IACA;IACA,IAAI,CAACwB,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC;IAE3B9B,UAAU,CAAC,MAAK;MACd,IAAI,CAAC2E,IAAI,CAAC,UAAU,CAAC;MACrB;MACA,IAAI,CAAC7C,IAAI,CAAC,gBAAgB,CAAC;IAC7B,CAAC,EAAE,IAAI,CAAC;IAER9B,UAAU,CAAC,MAAK;MACd;MACA,IAAI,CAAC8B,IAAI,CAAC,UAAU,CAAC;IACvB,CAAC,EAAE,IAAI,CAAC;IAER9B,UAAU,CAAC,MAAK;MACd;MACA,IAAI,CAAC8B,IAAI,CAAC,cAAc,CAAC;IAC3B,CAAC,EAAE,IAAI,CAAC;IAER9B,UAAU,CAAC,MAAK;MACdK,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;IAC9C,CAAC,EAAE,KAAK,CAAC;EACX;EAEA;;;EAGOiI,gBAAgBA,CAAA;IACrBlI,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;IAC7D,IAAI,CAACwB,IAAI,CAAC,cAAc,CAAC;EAC3B;EAEA;;;EAGQ5B,4BAA4BA,CAAA;IAClCG,OAAO,CAACC,GAAG,CACT,gEAAgE,CACjE;IAED,IAAI;MACF,IAAI,CAAC1B,MAAM,CACRuH,SAAS,CAA8B;QACtCC,KAAK,EAAE5H,gCAAgC;QACvC6H,WAAW,EAAE;OACd,CAAC,CACDF,SAAS,CAAC;QACTG,IAAI,EAAEA,CAAC;UAAEC,IAAI;UAAEC;QAAM,CAAE,KAAI;UACzB,IAAIA,MAAM,EAAE;YACVnG,OAAO,CAACwB,IAAI,CACV,8DAA8D,EAC9D2E,MAAM,CACP;;UAGH,IAAID,IAAI,EAAEiC,iBAAiB,EAAE;YAC3BnI,OAAO,CAACC,GAAG,CACT,uCAAuC,EACvCiG,IAAI,CAACiC,iBAAiB,CACvB;YACD,IAAI,CAACC,sBAAsB,CAAClC,IAAI,CAACiC,iBAAiB,CAAC;;QAEvD,CAAC;QACD5H,KAAK,EAAGA,KAAK,IAAI;UACfP,OAAO,CAACO,KAAK,CACX,oDAAoD,EACpDA,KAAK,CACN;UACD;UACAZ,UAAU,CAAC,MAAK;YACd,IAAI,CAACE,4BAA4B,EAAE;UACrC,CAAC,EAAE,IAAI,CAAC;QACV,CAAC;QACDwG,QAAQ,EAAEA,CAAA,KAAK;UACbrG,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;UAClE;UACAN,UAAU,CAAC,MAAK;YACd,IAAI,CAACE,4BAA4B,EAAE;UACrC,CAAC,EAAE,IAAI,CAAC;QACV;OACD,CAAC;KACL,CAAC,OAAOU,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CACX,4DAA4D,EAC5DA,KAAK,CACN;MACDZ,UAAU,CAAC,MAAK;QACd,IAAI,CAACE,4BAA4B,EAAE;MACrC,CAAC,EAAE,IAAI,CAAC;;EAEZ;EAEA;;;EAGQuG,kBAAkBA,CAACiC,IAAkB;IAC3CrI,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAE;MACtDuG,MAAM,EAAE6B,IAAI,CAACzH,EAAE;MACf0H,QAAQ,EAAED,IAAI,CAACrE,IAAI;MACnBuE,MAAM,EAAEF,IAAI,CAACE,MAAM,EAAEC,QAAQ;MAC7BC,cAAc,EAAEJ,IAAI,CAACI;KACtB,CAAC;IAEF,IAAI,CAAC/J,YAAY,CAACuH,IAAI,CAACoC,IAAI,CAAC;IAC5B,IAAI,CAAC5G,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC;IAE3BzB,OAAO,CAACC,GAAG,CACT,iEAAiE,CAClE;EACH;EAEA;;;EAGQmI,sBAAsBA,CAACC,IAAU;IACvCrI,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEoI,IAAI,CAACK,MAAM,CAAC;IAEjE,QAAQL,IAAI,CAACK,MAAM;MACjB,KAAK9K,UAAU,CAAC+K,QAAQ;QACtB3I,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;QAChD,IAAI,CAACqE,IAAI,CAAC,UAAU,CAAC;QACrB,IAAI,CAAC7C,IAAI,CAAC,UAAU,CAAC;QACrB,IAAI,CAAChD,UAAU,CAACwH,IAAI,CAAC,IAAI,CAAC;QAC1B,IAAI,CAACvH,YAAY,CAACuH,IAAI,CAAC,IAAI,CAAC;QAC5B;MAEF,KAAKrI,UAAU,CAACgL,KAAK;QACnB5I,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;QAC1C,IAAI,CAAC4I,aAAa,EAAE;QACpB,IAAI,CAACpH,IAAI,CAAC,UAAU,CAAC;QACrB,IAAI,CAAChD,UAAU,CAACwH,IAAI,CAAC,IAAI,CAAC;QAC1B,IAAI,CAACvH,YAAY,CAACuH,IAAI,CAAC,IAAI,CAAC;QAC5B;MAEF,KAAKrI,UAAU,CAACkL,SAAS;QACvB9I,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;QAC7C,IAAI,CAACqE,IAAI,CAAC,UAAU,CAAC;QACrB,IAAI,CAAC7C,IAAI,CAAC,gBAAgB,CAAC;QAC3B,IAAI,CAAChD,UAAU,CAACwH,IAAI,CAACoC,IAAI,CAAC;QAC1B,IAAI,CAAC3J,YAAY,CAACuH,IAAI,CAAC,IAAI,CAAC;QAC5B;MAEF,KAAKrI,UAAU,CAACmL,OAAO;QACrB/I,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;QAC/C,IAAI,CAACwB,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC;QAC3B;MAEF;QACEzB,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEoI,IAAI,CAACK,MAAM,CAAC;QACjE;;EAEN;EAEA;;;EAGcM,YAAYA,CAACV,QAAkB;IAAA,IAAAW,MAAA;IAAA,OAAAnC,iBAAA;MAC3C9G,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAEqI,QAAQ,CAAC;MAEjE,MAAMY,WAAW,GAA2B;QAC1CxG,KAAK,EAAE,IAAI;QACXyG,KAAK,EAAEb,QAAQ,KAAK3K,QAAQ,CAACyL;OAC9B;MAED,IAAI;QACF,MAAMC,MAAM,SAASC,SAAS,CAACC,YAAY,CAACP,YAAY,CAACE,WAAW,CAAC;QACrElJ,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;QAClDD,OAAO,CAACC,GAAG,CACT,iCAAiC,EACjCoJ,MAAM,CAACvI,SAAS,EAAE,CAACtD,GAAG,CAAEuD,CAAC,KAAM;UAC7BC,IAAI,EAAED,CAAC,CAACC,IAAI;UACZC,OAAO,EAAEF,CAAC,CAACE,OAAO;UAClBC,UAAU,EAAEH,CAAC,CAACG;SACf,CAAC,CAAC,CACJ;QACD+H,MAAI,CAAC7J,WAAW,GAAGiK,MAAM;QAEzB;QACA,IAAIJ,MAAI,CAAC3J,iBAAiB,IAAIgJ,QAAQ,KAAK3K,QAAQ,CAACyL,KAAK,EAAE;UACzDH,MAAI,CAAC3J,iBAAiB,CAAC6B,SAAS,GAAGkI,MAAM;;QAG3C,OAAOA,MAAM;OACd,CAAC,OAAO9I,KAAK,EAAE;QACdP,OAAO,CAACO,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;QACjE,MAAM,IAAIiJ,KAAK,CAAC,6CAA6C,CAAC;;IAC/D;EACH;EAEA;;;EAGQC,8BAA8BA,CAACJ,MAAmB;IACxD,IAAI,CAAC,IAAI,CAAClK,cAAc,EAAE;MACxBa,OAAO,CAACO,KAAK,CAAC,8CAA8C,CAAC;MAC7D;;IAGFP,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC;IACtEoJ,MAAM,CAACvI,SAAS,EAAE,CAACwC,OAAO,CAAEoG,KAAK,IAAI;MACnC,IAAI,CAACvK,cAAe,CAACwK,QAAQ,CAACD,KAAK,EAAEL,MAAM,CAAC;IAC9C,CAAC,CAAC;EACJ;EAEA;;;EAGcO,WAAWA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAA/C,iBAAA;MACvB,IAAI,CAAC+C,MAAI,CAAC1K,cAAc,EAAE;QACxB,MAAM,IAAIqK,KAAK,CAAC,8BAA8B,CAAC;;MAGjDxJ,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;MACrD,MAAMkH,KAAK,SAAS0C,MAAI,CAAC1K,cAAc,CAACyK,WAAW,EAAE;MACrD,MAAMC,MAAI,CAAC1K,cAAc,CAAC2K,mBAAmB,CAAC3C,KAAK,CAAC;MACpD,OAAOA,KAAK;IAAC;EACf;EAEA;;;EAGc4C,YAAYA,CACxB5C,KAAgC;IAAA,IAAA6C,MAAA;IAAA,OAAAlD,iBAAA;MAEhC,IAAI,CAACkD,MAAI,CAAC7K,cAAc,EAAE;QACxB,MAAM,IAAIqK,KAAK,CAAC,8BAA8B,CAAC;;MAGjDxJ,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;MACtD,MAAM+J,MAAI,CAAC7K,cAAc,CAACmI,oBAAoB,CAACH,KAAK,CAAC;MACrD,MAAMK,MAAM,SAASwC,MAAI,CAAC7K,cAAc,CAAC4K,YAAY,EAAE;MACvD,MAAMC,MAAI,CAAC7K,cAAc,CAAC2K,mBAAmB,CAACtC,MAAM,CAAC;MACrD,OAAOA,MAAM;IAAC;EAChB;EAEA;;;EAGOyC,gBAAgBA,CACrBC,UAA4B,EAC5BC,WAA6B;IAE7BnK,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;IACtD,IAAI,CAACX,iBAAiB,GAAG4K,UAAU;IACnC,IAAI,CAAC3K,kBAAkB,GAAG4K,WAAW;IAErC;IACA,IAAI;MACF,IAAI,CAACC,kBAAkB,CAACF,UAAU,EAAEC,WAAW,CAAC;KACjD,CAAC,OAAO5J,KAAK,EAAE;MACdP,OAAO,CAACwB,IAAI,CAAC,4CAA4C,EAAEjB,KAAK,CAAC;;IAGnE;IACA,IAAI,IAAI,CAACnB,WAAW,IAAI8K,UAAU,EAAE;MAClClK,OAAO,CAACC,GAAG,CAAC,0DAA0D,CAAC;MACvEiK,UAAU,CAAC/I,SAAS,GAAG,IAAI,CAAC/B,WAAW;MACvC8K,UAAU,CAAClL,KAAK,GAAG,IAAI,CAAC,CAAC;MACzBkL,UAAU,CAACtH,MAAM,GAAG,CAAC;MACrBsH,UAAU,CAACG,QAAQ,GAAG,IAAI;MAC1B,IAAI;QACFH,UAAU,CAACzI,IAAI,EAAE;QACjBzB,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;OACnD,CAAC,OAAOM,KAAK,EAAE;QACdP,OAAO,CAACwB,IAAI,CAAC,0CAA0C,EAAEjB,KAAK,CAAC;;KAElE,MAAM;MACLP,OAAO,CAACwB,IAAI,CACV,6DAA6D,CAC9D;;IAGH,IAAI,IAAI,CAACnC,YAAY,IAAI8K,WAAW,EAAE;MACpCnK,OAAO,CAACC,GAAG,CAAC,2DAA2D,CAAC;MACxEkK,WAAW,CAAChJ,SAAS,GAAG,IAAI,CAAC9B,YAAY;MACzC8K,WAAW,CAACnL,KAAK,GAAG,KAAK,CAAC,CAAC;MAC3BmL,WAAW,CAACvH,MAAM,GAAG,CAAC;MACtBuH,WAAW,CAACE,QAAQ,GAAG,IAAI;MAC3B,IAAI;QACFF,WAAW,CAAC1I,IAAI,EAAE;QAClBzB,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;OACpD,CAAC,OAAOM,KAAK,EAAE;QACdP,OAAO,CAACwB,IAAI,CAAC,2CAA2C,EAAEjB,KAAK,CAAC;;KAEnE,MAAM;MACLP,OAAO,CAACwB,IAAI,CACV,8DAA8D,CAC/D;;IAGH;IACA7B,UAAU,CAAC,MAAK;MACd,IAAI,CAAC2K,qBAAqB,EAAE;IAC9B,CAAC,EAAE,IAAI,CAAC;EACV;EAEA;;;EAGQA,qBAAqBA,CAAA;IAC3BtK,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;IAE5D;IACA,IAAI,IAAI,CAACb,WAAW,IAAI,IAAI,CAACE,iBAAiB,EAAE;MAC9C,IAAI,CAAC,IAAI,CAACA,iBAAiB,CAAC6B,SAAS,EAAE;QACrCnB,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;QAC5D,IAAI,CAACX,iBAAiB,CAAC6B,SAAS,GAAG,IAAI,CAAC/B,WAAW;QACnD,IAAI,CAACE,iBAAiB,CAACN,KAAK,GAAG,IAAI;QACnC,IAAI,CAACM,iBAAiB,CAAC+K,QAAQ,GAAG,IAAI;QACtC,IAAI,CAAC/K,iBAAiB,CACnBmC,IAAI,EAAE,CACNC,KAAK,CAAEC,CAAC,IACP3B,OAAO,CAACwB,IAAI,CAAC,2CAA2C,EAAEG,CAAC,CAAC,CAC7D;;;IAIP;IACA,IAAI,IAAI,CAACtC,YAAY,IAAI,IAAI,CAACE,kBAAkB,EAAE;MAChD,IAAI,CAAC,IAAI,CAACA,kBAAkB,CAAC4B,SAAS,EAAE;QACtCnB,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;QAC7D,IAAI,CAACV,kBAAkB,CAAC4B,SAAS,GAAG,IAAI,CAAC9B,YAAY;QACrD,IAAI,CAACE,kBAAkB,CAACP,KAAK,GAAG,KAAK;QACrC,IAAI,CAACO,kBAAkB,CAACqD,MAAM,GAAG,CAAC;QAClC,IAAI,CAACrD,kBAAkB,CAAC8K,QAAQ,GAAG,IAAI;QACvC,IAAI,CAAC9K,kBAAkB,CACpBkC,IAAI,EAAE,CACNC,KAAK,CAAEC,CAAC,IACP3B,OAAO,CAACwB,IAAI,CAAC,4CAA4C,EAAEG,CAAC,CAAC,CAC9D;;;IAIP;IACA3B,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAE;MACxDsK,aAAa,EAAE,CAAC,CAAC,IAAI,CAACjL,iBAAiB,EAAE6B,SAAS;MAClDqJ,cAAc,EAAE,CAAC,CAAC,IAAI,CAACjL,kBAAkB,EAAE4B,SAAS;MACpDsJ,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAACrL,WAAW;MACrCsL,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAACrL;KAC5B,CAAC;EACJ;EAEA;;;EAGQ+K,kBAAkBA,CACxBF,UAA4B,EAC5BC,WAA6B;IAE7BnK,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;IAEzD;IACAiK,UAAU,CAACtH,MAAM,GAAG,CAAC,CAAC,CAAC;IACvBuH,WAAW,CAACvH,MAAM,GAAG,CAAC,CAAC,CAAC;IAExB;IACAsH,UAAU,CAACG,QAAQ,GAAG,IAAI;IAC1BF,WAAW,CAACE,QAAQ,GAAG,IAAI;IAE3B;IACAF,WAAW,CAAClF,gBAAgB,CAAC,gBAAgB,EAAE,MAAK;MAClDjF,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;IAC9D,CAAC,CAAC;IAEFkK,WAAW,CAAClF,gBAAgB,CAAC,SAAS,EAAE,MAAK;MAC3CjF,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;MACrD,IAAI,CAACsB,mBAAmB,CAAC4I,WAAW,CAAC;IACvC,CAAC,CAAC;IAEFA,WAAW,CAAClF,gBAAgB,CAAC,MAAM,EAAE,MAAK;MACxCjF,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;IAC9D,CAAC,CAAC;IAEFkK,WAAW,CAAClF,gBAAgB,CAAC,OAAO,EAAE,MAAK;MACzCjF,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;IACrD,CAAC,CAAC;EACJ;EAEA;;;EAGQsB,mBAAmBA,CAACoJ,YAA8B;IACxD3K,OAAO,CAACC,GAAG,CACT,uDAAuD,EACvD0K,YAAY,KAAK,IAAI,CAACrL,iBAAiB,GAAG,OAAO,GAAG,QAAQ,CAC7D;IAED;IACAqL,YAAY,CACTlJ,IAAI,EAAE,CACNkE,IAAI,CAAC,MAAK;MACT3F,OAAO,CAACC,GAAG,CACT,2DAA2D,CAC5D;IACH,CAAC,CAAC,CACDyB,KAAK,CAAEnB,KAAK,IAAI;MACfP,OAAO,CAACwB,IAAI,CACV,iEAAiE,EACjEjB,KAAK,CACN;MAED;MACA,MAAMqK,iBAAiB,GAAGA,CAAA,KAAK;QAC7BD,YAAY,CACTlJ,IAAI,EAAE,CACNkE,IAAI,CAAC,MAAK;UACT3F,OAAO,CAACC,GAAG,CACT,qEAAqE,CACtE;UACD4K,QAAQ,CAACC,mBAAmB,CAAC,OAAO,EAAEF,iBAAiB,CAAC;UACxDC,QAAQ,CAACC,mBAAmB,CAAC,SAAS,EAAEF,iBAAiB,CAAC;QAC5D,CAAC,CAAC,CACDlJ,KAAK,CAAEqJ,GAAG,IAAI;UACb/K,OAAO,CAACO,KAAK,CACX,mDAAmD,EACnDwK,GAAG,CACJ;QACH,CAAC,CAAC;MACN,CAAC;MAEDF,QAAQ,CAAC5F,gBAAgB,CAAC,OAAO,EAAE2F,iBAAiB,CAAC;MACrDC,QAAQ,CAAC5F,gBAAgB,CAAC,SAAS,EAAE2F,iBAAiB,CAAC;IACzD,CAAC,CAAC;EACN;EAEA;;;EAGAI,YAAYA,CACVC,WAAmB,EACnB3C,QAAkB,EAClBG,cAAuB;IAEvBzI,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAE;MAC/CgL,WAAW;MACX3C,QAAQ;MACRG;KACD,CAAC;IAEF,IAAI,CAACwC,WAAW,EAAE;MAChB,MAAM1K,KAAK,GAAG,IAAIiJ,KAAK,CAAC,0BAA0B,CAAC;MACnDxJ,OAAO,CAACO,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3D,OAAOjD,UAAU,CAAC,MAAMiD,KAAK,CAAC;;IAGhC;IACA,MAAMiG,MAAM,GAAG,QAAQ0E,IAAI,CAACC,GAAG,EAAE,IAAIC,IAAI,CAACC,MAAM,EAAE,CAC/CC,QAAQ,CAAC,EAAE,CAAC,CACZC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;IAEjB;IACA,OAAOhO,IAAI,CAAC,IAAI,CAACiO,iBAAiB,CAAClD,QAAQ,CAAC,CAAC,CAACmD,IAAI,CAChD/N,SAAS,CAAEyJ,KAAK,IAAI;MAClB,MAAMZ,SAAS,GAAG;QAChB0E,WAAW;QACX3C,QAAQ,EAAEA,QAAQ;QAClB9B,MAAM;QACNW,KAAK,EAAEC,IAAI,CAACsE,SAAS,CAACvE,KAAK,CAAC;QAC5BsB;OACD;MAEDzI,OAAO,CAACC,GAAG,CACT,kDAAkD,EAClDsG,SAAS,CACV;MAED,OAAO,IAAI,CAAChI,MAAM,CACfoN,MAAM,CAAyB;QAC9BC,QAAQ,EAAE/N,sBAAsB;QAChC0I;OACD,CAAC,CACDkF,IAAI,CACHjO,GAAG,CAAEqO,MAAM,IAAI;QACb7L,OAAO,CAACC,GAAG,CACT,8CAA8C,EAC9C4L,MAAM,CACP;QAED,IAAI,CAACA,MAAM,CAAC3F,IAAI,EAAE8E,YAAY,EAAE;UAC9B,MAAM,IAAIxB,KAAK,CAAC,mCAAmC,CAAC;;QAGtD,MAAMnB,IAAI,GAAGwD,MAAM,CAAC3F,IAAI,CAAC8E,YAAY;QACrChL,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE;UAC5CW,EAAE,EAAEyH,IAAI,CAACzH,EAAE;UACXoD,IAAI,EAAEqE,IAAI,CAACrE,IAAI;UACf0E,MAAM,EAAEL,IAAI,CAACK,MAAM;UACnBH,MAAM,EAAEF,IAAI,CAACE,MAAM,EAAEC,QAAQ;UAC7BsD,SAAS,EAAEzD,IAAI,CAACyD,SAAS,EAAEtD;SAC5B,CAAC;QAEF;QACA,IAAI,CAAC/J,UAAU,CAACwH,IAAI,CAACoC,IAAI,CAAC;QAE1B,OAAOA,IAAI;MACb,CAAC,CAAC,EACF5K,UAAU,CAAE8C,KAAK,IAAI;QACnBP,OAAO,CAACO,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;QAC3D,IAAI,CAAC/B,MAAM,CAAC+B,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAElD,IAAIwL,YAAY,GAAG,wCAAwC;QAC3D,IAAIxL,KAAK,CAACyL,YAAY,EAAE;UACtBD,YAAY,GAAG,4BAA4B;SAC5C,MAAM,IAAIxL,KAAK,CAAC0L,aAAa,EAAEC,MAAM,GAAG,CAAC,EAAE;UAC1CH,YAAY,GAAGxL,KAAK,CAAC0L,aAAa,CAAC,CAAC,CAAC,CAACE,OAAO,IAAIJ,YAAY;;QAG/D,OAAOzO,UAAU,CAAC,MAAM,IAAIkM,KAAK,CAACuC,YAAY,CAAC,CAAC;MAClD,CAAC,CAAC,CACH;IACL,CAAC,CAAC,EACFtO,UAAU,CAAE8C,KAAK,IAAI;MACnBP,OAAO,CAACO,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD,OAAOjD,UAAU,CAAC,MAAM,IAAIkM,KAAK,CAAC,iBAAiB,GAAGjJ,KAAK,CAAC4L,OAAO,CAAC,CAAC;IACvE,CAAC,CAAC,CACH;EACH;EAEA;;;EAGcX,iBAAiBA,CAC7BlD,QAAkB;IAAA,IAAA8D,MAAA;IAAA,OAAAtF,iBAAA;MAElB,IAAI;QACF;QACA,IAAI,CAACsF,MAAI,CAACjN,cAAc,EAAE;UACxBiN,MAAI,CAAC1M,gBAAgB,EAAE;;QAGzB;QACA,MAAM2J,MAAM,SAAS+C,MAAI,CAACpD,YAAY,CAACV,QAAQ,CAAC;QAEhD;QACA8D,MAAI,CAAC3C,8BAA8B,CAACJ,MAAM,CAAC;QAE3C;QACA,MAAMlC,KAAK,SAASiF,MAAI,CAACxC,WAAW,EAAE;QAEtC5J,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC;QAChE,OAAOkH,KAAK;OACb,CAAC,OAAO5G,KAAK,EAAE;QACdP,OAAO,CAACO,KAAK,CAAC,gDAAgD,EAAEA,KAAK,CAAC;QACtE,MAAMA,KAAK;;IACZ;EACH;EAEA;;;EAGA8L,UAAUA,CAAC3N,YAA0B;IACnCsB,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEvB,YAAY,CAACkC,EAAE,CAAC;IAEhE;IACA,OAAOrD,IAAI,CAAC,IAAI,CAAC+O,kBAAkB,CAAC5N,YAAY,CAAC,CAAC,CAAC+M,IAAI,CACrD/N,SAAS,CAAE8J,MAAM,IAAI;MACnB,OAAO,IAAI,CAACjJ,MAAM,CACfoN,MAAM,CAAuB;QAC5BC,QAAQ,EAAE9N,oBAAoB;QAC9ByI,SAAS,EAAE;UACTC,MAAM,EAAE9H,YAAY,CAACkC,EAAE;UACvB4G,MAAM,EAAEJ,IAAI,CAACsE,SAAS,CAAClE,MAAM;;OAEhC,CAAC,CACDiE,IAAI,CACH/N,SAAS,CAAEmO,MAAM,IAAI;QACnB7L,OAAO,CAACC,GAAG,CACT,6CAA6C,EAC7C4L,MAAM,CACP;QAED,IAAI,CAACA,MAAM,CAAC3F,IAAI,EAAEmG,UAAU,EAAE;UAC5B,MAAM,IAAI7C,KAAK,CAAC,mCAAmC,CAAC;;QAGtD,MAAMnB,IAAI,GAAGwD,MAAM,CAAC3F,IAAI,CAACmG,UAAU;QAEnC;QACA,IAAI,CAAC/H,IAAI,CAAC,UAAU,CAAC;QACrB,IAAI,CAAC7C,IAAI,CAAC,gBAAgB,CAAC;QAE3B;QACA,OAAOlE,IAAI,CAAC,IAAI,CAACgP,iBAAiB,CAAC7N,YAAY,EAAE2J,IAAI,CAAC,CAAC;MACzD,CAAC,CAAC,EACF5K,UAAU,CAAE8C,KAAK,IAAI;QACnBP,OAAO,CAACO,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;QACzD,IAAI,CAAC/B,MAAM,CAAC+B,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;QACjD,OAAOjD,UAAU,CACf,MAAM,IAAIkM,KAAK,CAAC,yCAAyC,CAAC,CAC3D;MACH,CAAC,CAAC,CACH;IACL,CAAC,CAAC,EACF/L,UAAU,CAAE8C,KAAK,IAAI;MACnBP,OAAO,CAACO,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5D,OAAOjD,UAAU,CAAC,MAAM,IAAIkM,KAAK,CAAC,iBAAiB,GAAGjJ,KAAK,CAAC4L,OAAO,CAAC,CAAC;IACvE,CAAC,CAAC,CACH;EACH;EAEA;;;EAGcG,kBAAkBA,CAC9B5N,YAA0B;IAAA,IAAA8N,MAAA;IAAA,OAAA1F,iBAAA;MAE1B,IAAI;QACF9G,OAAO,CAACC,GAAG,CACT,4DAA4D,EAC5D;UACEuG,MAAM,EAAE9H,YAAY,CAACkC,EAAE;UACvB0H,QAAQ,EAAE5J,YAAY,CAACsF,IAAI;UAC3ByI,QAAQ,EAAE,CAAC,CAAC/N,YAAY,CAACyI,KAAK;UAC9BuF,WAAW,EAAEhO,YAAY,CAACyI,KAAK,EAAE+E,MAAM,IAAI;SAC5C,CACF;QAED;QACA,IAAI,CAACM,MAAI,CAACrN,cAAc,EAAE;UACxBa,OAAO,CAACC,GAAG,CACT,6DAA6D,CAC9D;UACDuM,MAAI,CAAC9M,gBAAgB,EAAE;;QAGzB;QACAM,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC;QAChE,MAAMoJ,MAAM,SAASmD,MAAI,CAACxD,YAAY,CAACtK,YAAY,CAACsF,IAAI,CAAC;QAEzD;QACAhE,OAAO,CAACC,GAAG,CACT,mEAAmE,CACpE;QACDuM,MAAI,CAAC/C,8BAA8B,CAACJ,MAAM,CAAC;QAE3C;QACA,IAAI,CAAC3K,YAAY,CAACyI,KAAK,EAAE;UACvB,MAAM,IAAIqC,KAAK,CAAC,oCAAoC,CAAC;;QAGvD,MAAMrC,KAAK,GAAGC,IAAI,CAACC,KAAK,CAAC3I,YAAY,CAACyI,KAAK,CAAC;QAE5C,IAAI,CAACA,KAAK,IAAI,CAACA,KAAK,CAACnD,IAAI,IAAI,CAACmD,KAAK,CAACwF,GAAG,EAAE;UACvC,MAAM,IAAInD,KAAK,CAAC,+BAA+B,CAAC;;QAGlD;QACA,MAAMhC,MAAM,SAASgF,MAAI,CAACzC,YAAY,CAAC5C,KAAK,CAAC;QAE7CnH,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;QACjE,OAAOuH,MAAM;OACd,CAAC,OAAOjH,KAAK,EAAE;QACdP,OAAO,CAACO,KAAK,CAAC,iDAAiD,EAAEA,KAAK,CAAC;QACvE,MAAMA,KAAK;;IACZ;EACH;EAEA;;;EAGAqM,UAAUA,CAACpG,MAAc,EAAEqG,MAAe;IACxC7M,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEuG,MAAM,EAAEqG,MAAM,CAAC;IAE/D,OAAO,IAAI,CAACtO,MAAM,CACfoN,MAAM,CAA8B;MACnCC,QAAQ,EAAE7N,oBAAoB;MAC9BwI,SAAS,EAAE;QACTC,MAAM;QACNqG,MAAM,EAAEA,MAAM,IAAI;;KAErB,CAAC,CACDpB,IAAI,CACHjO,GAAG,CAAEqO,MAAM,IAAI;MACb7L,OAAO,CAACC,GAAG,CAAC,6CAA6C,EAAE4L,MAAM,CAAC;MAElE,IAAI,CAACA,MAAM,CAAC3F,IAAI,EAAE0G,UAAU,EAAE;QAC5B,MAAM,IAAIpD,KAAK,CAAC,kCAAkC,CAAC;;MAGrD;MACA,IAAI,CAAC9K,YAAY,CAACuH,IAAI,CAAC,IAAI,CAAC;MAC5B,IAAI,CAACxH,UAAU,CAACwH,IAAI,CAAC,IAAI,CAAC;MAE1B;MACA,IAAI,CAAC3B,IAAI,CAAC,UAAU,CAAC;MAErB,OAAOuH,MAAM,CAAC3F,IAAI,CAAC0G,UAAU;IAC/B,CAAC,CAAC,EACFnP,UAAU,CAAE8C,KAAK,IAAI;MACnBP,OAAO,CAACO,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzD,IAAI,CAAC/B,MAAM,CAAC+B,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MACjD,OAAOjD,UAAU,CAAC,MAAM,IAAIkM,KAAK,CAAC,iCAAiC,CAAC,CAAC;IACvE,CAAC,CAAC,CACH;EACL;EAEA;;;EAGAsD,OAAOA,CAACtG,MAAc;IACpBxG,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEuG,MAAM,CAAC;IAEpD,OAAO,IAAI,CAACjI,MAAM,CACfoN,MAAM,CAA2B;MAChCC,QAAQ,EAAE5N,iBAAiB;MAC3BuI,SAAS,EAAE;QACTC,MAAM;QACNuG,QAAQ,EAAE,IAAI,CAAE;;KAEnB,CAAC,CACDtB,IAAI,CACHjO,GAAG,CAAEqO,MAAM,IAAI;MACb7L,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAE4L,MAAM,CAAC;MAE/D,IAAI,CAACA,MAAM,CAAC3F,IAAI,EAAE4G,OAAO,EAAE;QACzB,MAAM,IAAItD,KAAK,CAAC,kCAAkC,CAAC;;MAGrD;MACA,IAAI,CAAC/K,UAAU,CAACwH,IAAI,CAAC,IAAI,CAAC;MAC1B,IAAI,CAACvH,YAAY,CAACuH,IAAI,CAAC,IAAI,CAAC;MAE5B;MACA,IAAI,CAAC4C,aAAa,EAAE;MACpB,IAAI,CAACpH,IAAI,CAAC,UAAU,CAAC;MAErB;MACA,IAAI,CAACuL,aAAa,EAAE;MAEpB,OAAOnB,MAAM,CAAC3F,IAAI,CAAC4G,OAAO;IAC5B,CAAC,CAAC,EACFrP,UAAU,CAAE8C,KAAK,IAAI;MACnBP,OAAO,CAACO,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD,IAAI,CAAC/B,MAAM,CAAC+B,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC9C,OAAOjD,UAAU,CACf,MAAM,IAAIkM,KAAK,CAAC,kCAAkC,CAAC,CACpD;IACH,CAAC,CAAC,CACH;EACL;EAEA;;;EAGAyD,WAAWA,CACTzG,MAAc,EACd0G,WAAqB,EACrBC,WAAqB;IAErBnN,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE;MAC9CuG,MAAM;MACN0G,WAAW;MACXC;KACD,CAAC;IAEF,OAAO,IAAI,CAAC5O,MAAM,CACfoN,MAAM,CAAmC;MACxCC,QAAQ,EAAE3N,0BAA0B;MACpCsI,SAAS,EAAE;QACTC,MAAM;QACN2C,KAAK,EAAE+D,WAAW;QAClBxK,KAAK,EAAEyK;;KAEV,CAAC,CACD1B,IAAI,CACHjO,GAAG,CAAEqO,MAAM,IAAI;MACb7L,OAAO,CAACC,GAAG,CAAC,6CAA6C,EAAE4L,MAAM,CAAC;MAElE,IAAI,CAACA,MAAM,CAAC3F,IAAI,EAAEkH,eAAe,EAAE;QACjC,MAAM,IAAI5D,KAAK,CAAC,kCAAkC,CAAC;;MAGrD,OAAOqC,MAAM,CAAC3F,IAAI,CAACkH,eAAe;IACpC,CAAC,CAAC,EACF3P,UAAU,CAAE8C,KAAK,IAAI;MACnBP,OAAO,CAACO,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;MAC1D,IAAI,CAAC/B,MAAM,CAAC+B,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MACjD,OAAOjD,UAAU,CACf,MAAM,IAAIkM,KAAK,CAAC,sCAAsC,CAAC,CACxD;IACH,CAAC,CAAC,CACH;EACL;EAEA;;;EAGc+C,iBAAiBA,CAC7B7N,YAA0B,EAC1B2J,IAAU;IAAA,IAAAgF,MAAA;IAAA,OAAAvG,iBAAA;MAEV9G,OAAO,CAACC,GAAG,CAAC,4DAA4D,CAAC;MAEzE;MACAoN,MAAI,CAAC5L,IAAI,CAAC,gBAAgB,CAAC;MAE3B;MACA4L,MAAI,CAAC5O,UAAU,CAACwH,IAAI,CAACoC,IAAI,CAAC;MAC1BgF,MAAI,CAAC3O,YAAY,CAACuH,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;MAE9B,OAAOoC,IAAI;IAAC;EACd;EAEA;;;EAGAiF,WAAWA,CAAA;IACT,IAAI,CAACrO,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;IAC1Ce,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE,IAAI,CAAChB,cAAc,CAAC;IACnE,OAAO,IAAI,CAACA,cAAc;EAC5B;EAEA;;;EAGAsO,WAAWA,CAAA;IACT,IAAI,CAACrO,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;IAC1Cc,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE,IAAI,CAACf,cAAc,CAAC;IACnE,OAAO,IAAI,CAACA,cAAc;EAC5B;EAEA;;;EAGAsO,eAAeA,CAAA;IACb,OAAO,IAAI,CAACvO,cAAc;EAC5B;EAEA;;;EAGAwO,eAAeA,CAAA;IACb,OAAO,IAAI,CAACvO,cAAc;EAC5B;EAEA;;;EAGQ2J,aAAaA,CAAA;IACnB7I,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;IACnD4H,MAAM,CAACC,IAAI,CAAC,IAAI,CAAChJ,MAAM,CAAC,CAACwE,OAAO,CAAEwB,IAAI,IAAI;MACxC,IAAI,CAACR,IAAI,CAACQ,IAAI,CAAC;IACjB,CAAC,CAAC;EACJ;EAEA;;;EAGO4I,YAAYA,CAAA;IACjB1N,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC;IACtE4H,MAAM,CAAC8F,MAAM,CAAC,IAAI,CAAC7O,MAAM,CAAC,CAACwE,OAAO,CAAEkC,KAAK,IAAI;MAC3C,IAAIA,KAAK,EAAE;QACTA,KAAK,CAACxG,KAAK,GAAG,KAAK;QACnBwG,KAAK,CAAC5C,MAAM,GAAG,GAAG;QAClB;QACA4C,KAAK,CACF/D,IAAI,EAAE,CACNkE,IAAI,CAAC,MAAK;UACTH,KAAK,CAACK,KAAK,EAAE;UACbL,KAAK,CAACnC,WAAW,GAAG,CAAC;QACvB,CAAC,CAAC,CACD3B,KAAK,CAAC,MAAK;UACV;QAAA,CACD,CAAC;;IAER,CAAC,CAAC;EACJ;EAEA;;;EAGQsL,aAAaA,CAAA;IACnBhN,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;IAE5D;IACA,IAAI,IAAI,CAACb,WAAW,EAAE;MACpB,IAAI,CAACA,WAAW,CAAC0B,SAAS,EAAE,CAACwC,OAAO,CAAEoG,KAAK,IAAI;QAC7CA,KAAK,CAACpF,IAAI,EAAE;MACd,CAAC,CAAC;MACF,IAAI,CAAClF,WAAW,GAAG,IAAI;;IAGzB;IACA,IAAI,IAAI,CAACE,iBAAiB,EAAE;MAC1B,IAAI,CAACA,iBAAiB,CAAC6B,SAAS,GAAG,IAAI;;IAEzC,IAAI,IAAI,CAAC5B,kBAAkB,EAAE;MAC3B,IAAI,CAACA,kBAAkB,CAAC4B,SAAS,GAAG,IAAI;;IAG1C;IACA,IAAI,IAAI,CAAChC,cAAc,EAAE;MACvB,IAAI,CAACA,cAAc,CAACyO,KAAK,EAAE;MAC3B,IAAI,CAACzO,cAAc,GAAG,IAAI;;IAG5B,IAAI,CAACE,YAAY,GAAG,IAAI;EAC1B;EAEAwO,WAAWA,CAAA;IACT,IAAI,CAAChF,aAAa,EAAE;IACpB,IAAI,CAACmE,aAAa,EAAE;EACtB;;;uBA/0DW3O,WAAW,EAAAyP,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,aAAA;IAAA;EAAA;;;aAAX9P,WAAW;MAAA+P,OAAA,EAAX/P,WAAW,CAAAgQ,IAAA;MAAAC,UAAA,EAFV;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}