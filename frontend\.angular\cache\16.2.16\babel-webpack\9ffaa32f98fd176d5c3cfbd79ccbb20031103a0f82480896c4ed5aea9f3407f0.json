{"ast": null, "code": "import { CallType } from '../../models/message.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/call.service\";\nimport * as i2 from \"../../services/message.service\";\nimport * as i3 from \"../../services/toast.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nfunction CallTestComponent_div_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const log_r1 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"color\", ctx_r0.getLogColor(log_r1));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", log_r1, \" \");\n  }\n}\nexport class CallTestComponent {\n  constructor(callService, messageService, toastService) {\n    this.callService = callService;\n    this.messageService = messageService;\n    this.toastService = toastService;\n    this.testRecipientId = '';\n    this.isTestRunning = false;\n    this.hasActiveCall = false;\n    this.callServiceStatus = 'Initialisation...';\n    this.subscriptionStatus = 'En attente...';\n    this.activeCallStatus = 'Aucun';\n    this.testLogs = [];\n    this.subscriptions = [];\n  }\n  ngOnInit() {\n    this.initializeTestPanel();\n    this.setupSubscriptions();\n  }\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n  }\n  initializeTestPanel() {\n    this.addLog('🧪 Panel de test initialisé');\n    this.callServiceStatus = 'Actif';\n    this.subscriptionStatus = 'Connecté';\n  }\n  setupSubscriptions() {\n    // Surveiller les appels actifs\n    const activeCallSub = this.callService.activeCall$.subscribe(call => {\n      this.hasActiveCall = !!call;\n      this.activeCallStatus = call ? `${call.type} - ${call.status}` : 'Aucun';\n      if (call) {\n        this.addLog(`📞 Appel actif: ${call.type} (${call.status})`);\n      }\n    });\n    // Surveiller les appels entrants\n    const incomingCallSub = this.callService.incomingCall$.subscribe(call => {\n      if (call) {\n        this.addLog(`📲 Appel entrant: ${call.type} de ${call.caller?.username}`);\n      }\n    });\n    this.subscriptions.push(activeCallSub, incomingCallSub);\n  }\n  testVideoCall() {\n    if (!this.testRecipientId) {\n      this.toastService.showError('Veuillez saisir un ID destinataire');\n      return;\n    }\n    this.isTestRunning = true;\n    this.addLog(`📹 Test appel vidéo vers ${this.testRecipientId}`);\n    this.callService.initiateCall(this.testRecipientId, CallType.VIDEO).subscribe({\n      next: call => {\n        this.addLog(`✅ Appel vidéo initié: ${call.id}`);\n        this.isTestRunning = false;\n      },\n      error: error => {\n        this.addLog(`❌ Erreur appel vidéo: ${error.message}`);\n        this.isTestRunning = false;\n      }\n    });\n  }\n  testAudioCall() {\n    if (!this.testRecipientId) {\n      this.toastService.showError('Veuillez saisir un ID destinataire');\n      return;\n    }\n    this.isTestRunning = true;\n    this.addLog(`📞 Test appel audio vers ${this.testRecipientId}`);\n    this.callService.initiateCall(this.testRecipientId, CallType.AUDIO).subscribe({\n      next: call => {\n        this.addLog(`✅ Appel audio initié: ${call.id}`);\n        this.isTestRunning = false;\n      },\n      error: error => {\n        this.addLog(`❌ Erreur appel audio: ${error.message}`);\n        this.isTestRunning = false;\n      }\n    });\n  }\n  simulateIncomingCall() {\n    this.addLog(\"📲 Simulation d'appel entrant...\");\n    // Cette méthode simule un appel entrant pour les tests\n    const mockIncomingCall = {\n      id: `test_call_${Date.now()}`,\n      type: CallType.VIDEO,\n      caller: {\n        id: 'test_user',\n        username: 'Utilisateur Test',\n        image: '/assets/images/default-avatar.png'\n      },\n      conversationId: 'test_conversation'\n    };\n    // Simuler la réception d'un appel entrant\n    this.callService['handleIncomingCall'](mockIncomingCall);\n    this.addLog('✅ Appel entrant simulé');\n  }\n  reinitializeSubscription() {\n    this.addLog('🔄 Réinitialisation des subscriptions...');\n    // Vérifier si la méthode existe\n    if (typeof this.callService['reinitializeSubscription'] === 'function') {\n      this.callService['reinitializeSubscription']();\n    } else {\n      this.addLog('⚠️ Méthode reinitializeSubscription non trouvée');\n      // Réinitialiser manuellement\n      this.callService['subscribeToIncomingCalls']();\n    }\n    this.subscriptionStatus = 'Reconnexion...';\n    setTimeout(() => {\n      this.subscriptionStatus = 'Connecté';\n      this.addLog('✅ Subscriptions réinitialisées');\n    }, 2000);\n  }\n  endCurrentCall() {\n    this.addLog(\"❌ Fin de l'appel en cours...\");\n    // Logique pour terminer l'appel actuel\n    this.hasActiveCall = false;\n    this.activeCallStatus = 'Aucun';\n  }\n  runDiagnostic() {\n    this.addLog('🔍 Démarrage du diagnostic complet...');\n    // 1. Vérifier l'authentification\n    const token = localStorage.getItem('token');\n    const userStr = localStorage.getItem('user');\n    let userId = null;\n    if (!token) {\n      this.addLog(\"❌ Token d'authentification manquant\");\n    } else {\n      this.addLog(\"✅ Token d'authentification présent\");\n    }\n    if (!userStr) {\n      this.addLog('❌ Données utilisateur manquantes');\n    } else {\n      try {\n        const user = JSON.parse(userStr);\n        userId = user._id || user.id;\n        if (userId) {\n          this.addLog(`✅ ID utilisateur: ${userId}`);\n          // Stocker l'ID pour les tests\n          localStorage.setItem('userId', userId);\n        } else {\n          this.addLog('❌ ID utilisateur non trouvé dans les données');\n        }\n      } catch (error) {\n        this.addLog('❌ Erreur parsing données utilisateur');\n      }\n    }\n    // 2. Vérifier la connexion GraphQL\n    this.addLog('🔄 Test de connexion GraphQL...');\n    // 3. Vérifier les services\n    this.addLog('🔄 Vérification des services...');\n    if (this.callService) {\n      this.addLog('✅ CallService disponible');\n    } else {\n      this.addLog('❌ CallService indisponible');\n    }\n    if (this.messageService) {\n      this.addLog('✅ MessageService disponible');\n    } else {\n      this.addLog('❌ MessageService indisponible');\n    }\n    // 4. Test de mutation simple\n    this.testSimpleGraphQLQuery();\n  }\n  testSimpleGraphQLQuery() {\n    this.addLog('🔄 Test de requête GraphQL simple...');\n    // Test avec une query simple pour vérifier la connexion\n    this.messageService.getConversations().subscribe({\n      next: conversations => {\n        this.addLog(`✅ GraphQL fonctionne - ${conversations.length} conversations trouvées`);\n        this.addLog(\"🎯 Diagnostic terminé - Prêt pour les tests d'appel\");\n      },\n      error: error => {\n        this.addLog(`❌ Erreur GraphQL: ${error.message}`);\n        this.addLog(\"⚠️ Vérifiez la connexion backend et l'authentification\");\n      }\n    });\n  }\n  closePanel() {\n    // Masquer le panel de test\n    const panel = document.querySelector('.call-test-panel');\n    if (panel) {\n      panel.style.display = 'none';\n    }\n  }\n  addLog(message) {\n    const timestamp = new Date().toLocaleTimeString();\n    this.testLogs.unshift(`[${timestamp}] ${message}`);\n    // Garder seulement les 20 derniers logs\n    if (this.testLogs.length > 20) {\n      this.testLogs = this.testLogs.slice(0, 20);\n    }\n  }\n  getLogColor(log) {\n    if (log.includes('❌')) return '#ef4444';\n    if (log.includes('✅')) return '#10b981';\n    if (log.includes('📞') || log.includes('📹')) return '#3b82f6';\n    if (log.includes('📲')) return '#f59e0b';\n    if (log.includes('🔄')) return '#6b7280';\n    return '#9ca3af';\n  }\n  static {\n    this.ɵfac = function CallTestComponent_Factory(t) {\n      return new (t || CallTestComponent)(i0.ɵɵdirectiveInject(i1.CallService), i0.ɵɵdirectiveInject(i2.MessageService), i0.ɵɵdirectiveInject(i3.ToastService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: CallTestComponent,\n      selectors: [[\"app-call-test\"]],\n      decls: 36,\n      vars: 9,\n      consts: [[1, \"call-test-panel\", 2, \"position\", \"fixed\", \"top\", \"20px\", \"right\", \"20px\", \"width\", \"300px\", \"background\", \"linear-gradient(135deg, #1f2937 0%, #111827 100%)\", \"border\", \"2px solid #3b82f6\", \"border-radius\", \"12px\", \"padding\", \"16px\", \"color\", \"white\", \"z-index\", \"10000\", \"box-shadow\", \"0 10px 25px rgba(0, 0, 0, 0.5)\"], [2, \"margin\", \"0 0 16px 0\", \"color\", \"#3b82f6\", \"font-size\", \"16px\"], [2, \"margin-bottom\", \"16px\", \"font-size\", \"12px\"], [2, \"color\", \"#10b981\"], [2, \"color\", \"#f59e0b\"], [2, \"margin-bottom\", \"16px\"], [2, \"display\", \"block\", \"margin-bottom\", \"4px\", \"font-size\", \"12px\"], [\"placeholder\", \"ID utilisateur\", 2, \"width\", \"100%\", \"padding\", \"6px\", \"border\", \"1px solid #374151\", \"border-radius\", \"6px\", \"background\", \"#374151\", \"color\", \"white\", \"font-size\", \"12px\", 3, \"ngModel\", \"ngModelChange\"], [2, \"display\", \"flex\", \"flex-direction\", \"column\", \"gap\", \"8px\"], [\"onmouseover\", \"this.style.background='#2563eb'\", \"onmouseout\", \"this.style.background='#3b82f6'\", 2, \"padding\", \"8px 12px\", \"background\", \"#3b82f6\", \"color\", \"white\", \"border\", \"none\", \"border-radius\", \"6px\", \"cursor\", \"pointer\", \"font-size\", \"12px\", \"transition\", \"all 0.2s\", 3, \"disabled\", \"click\"], [\"onmouseover\", \"this.style.background='#059669'\", \"onmouseout\", \"this.style.background='#10b981'\", 2, \"padding\", \"8px 12px\", \"background\", \"#10b981\", \"color\", \"white\", \"border\", \"none\", \"border-radius\", \"6px\", \"cursor\", \"pointer\", \"font-size\", \"12px\", \"transition\", \"all 0.2s\", 3, \"disabled\", \"click\"], [\"onmouseover\", \"this.style.background='#d97706'\", \"onmouseout\", \"this.style.background='#f59e0b'\", 2, \"padding\", \"8px 12px\", \"background\", \"#f59e0b\", \"color\", \"white\", \"border\", \"none\", \"border-radius\", \"6px\", \"cursor\", \"pointer\", \"font-size\", \"12px\", \"transition\", \"all 0.2s\", 3, \"disabled\", \"click\"], [\"onmouseover\", \"this.style.background='#4b5563'\", \"onmouseout\", \"this.style.background='#6b7280'\", 2, \"padding\", \"8px 12px\", \"background\", \"#6b7280\", \"color\", \"white\", \"border\", \"none\", \"border-radius\", \"6px\", \"cursor\", \"pointer\", \"font-size\", \"12px\", \"transition\", \"all 0.2s\", 3, \"click\"], [\"onmouseover\", \"this.style.background='#dc2626'\", \"onmouseout\", \"this.style.background='#ef4444'\", 2, \"padding\", \"8px 12px\", \"background\", \"#ef4444\", \"color\", \"white\", \"border\", \"none\", \"border-radius\", \"6px\", \"cursor\", \"pointer\", \"font-size\", \"12px\", \"transition\", \"all 0.2s\", 3, \"disabled\", \"click\"], [\"onmouseover\", \"this.style.background='#7c3aed'\", \"onmouseout\", \"this.style.background='#8b5cf6'\", 2, \"padding\", \"8px 12px\", \"background\", \"#8b5cf6\", \"color\", \"white\", \"border\", \"none\", \"border-radius\", \"6px\", \"cursor\", \"pointer\", \"font-size\", \"12px\", \"transition\", \"all 0.2s\", 3, \"click\"], [\"onmouseover\", \"this.style.background='#047857'\", \"onmouseout\", \"this.style.background='#059669'\", 2, \"padding\", \"8px 12px\", \"background\", \"#059669\", \"color\", \"white\", \"border\", \"none\", \"border-radius\", \"6px\", \"cursor\", \"pointer\", \"font-size\", \"12px\", \"transition\", \"all 0.2s\", 3, \"click\"], [2, \"margin-top\", \"16px\"], [2, \"font-size\", \"12px\", \"color\", \"#9ca3af\", \"margin-bottom\", \"8px\"], [2, \"max-height\", \"120px\", \"overflow-y\", \"auto\", \"background\", \"#000\", \"padding\", \"8px\", \"border-radius\", \"4px\", \"font-family\", \"monospace\", \"font-size\", \"10px\"], [3, \"color\", 4, \"ngFor\", \"ngForOf\"], [2, \"position\", \"absolute\", \"top\", \"8px\", \"right\", \"8px\", \"width\", \"20px\", \"height\", \"20px\", \"background\", \"#ef4444\", \"color\", \"white\", \"border\", \"none\", \"border-radius\", \"50%\", \"cursor\", \"pointer\", \"font-size\", \"12px\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"center\", 3, \"click\"]],\n      template: function CallTestComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"h3\", 1);\n          i0.ɵɵtext(2, \" \\uD83E\\uDDEA Test d'Appels \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"div\", 2)(4, \"div\", 3);\n          i0.ɵɵtext(5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"div\", 3);\n          i0.ɵɵtext(7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"div\", 4);\n          i0.ɵɵtext(9);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"div\", 5)(11, \"label\", 6);\n          i0.ɵɵtext(12, \" ID Destinataire: \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"input\", 7);\n          i0.ɵɵlistener(\"ngModelChange\", function CallTestComponent_Template_input_ngModelChange_13_listener($event) {\n            return ctx.testRecipientId = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(14, \"div\", 8)(15, \"button\", 9);\n          i0.ɵɵlistener(\"click\", function CallTestComponent_Template_button_click_15_listener() {\n            return ctx.testVideoCall();\n          });\n          i0.ɵɵtext(16, \" \\uD83D\\uDCF9 Test Appel Vid\\u00E9o \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"button\", 10);\n          i0.ɵɵlistener(\"click\", function CallTestComponent_Template_button_click_17_listener() {\n            return ctx.testAudioCall();\n          });\n          i0.ɵɵtext(18, \" \\uD83D\\uDCDE Test Appel Audio \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"button\", 11);\n          i0.ɵɵlistener(\"click\", function CallTestComponent_Template_button_click_19_listener() {\n            return ctx.simulateIncomingCall();\n          });\n          i0.ɵɵtext(20, \" \\uD83D\\uDCF2 Simuler Appel Entrant \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"button\", 12);\n          i0.ɵɵlistener(\"click\", function CallTestComponent_Template_button_click_21_listener() {\n            return ctx.reinitializeSubscription();\n          });\n          i0.ɵɵtext(22, \" \\uD83D\\uDD04 R\\u00E9initialiser Subscription \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"button\", 13);\n          i0.ɵɵlistener(\"click\", function CallTestComponent_Template_button_click_23_listener() {\n            return ctx.endCurrentCall();\n          });\n          i0.ɵɵtext(24, \" \\u274C Terminer Appel \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"button\", 14);\n          i0.ɵɵlistener(\"click\", function CallTestComponent_Template_button_click_25_listener() {\n            return ctx.runDiagnostic();\n          });\n          i0.ɵɵtext(26, \" \\uD83D\\uDD0D Diagnostic Complet \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"button\", 15);\n          i0.ɵɵlistener(\"click\", function CallTestComponent_Template_button_click_27_listener() {\n            return ctx.useCurrentUserId();\n          });\n          i0.ɵɵtext(28, \" \\uD83D\\uDC64 Utiliser mon ID \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(29, \"div\", 16)(30, \"div\", 17);\n          i0.ɵɵtext(31, \" \\uD83D\\uDCCB Logs de test: \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"div\", 18);\n          i0.ɵɵtemplate(33, CallTestComponent_div_33_Template, 2, 3, \"div\", 19);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(34, \"button\", 20);\n          i0.ɵɵlistener(\"click\", function CallTestComponent_Template_button_click_34_listener() {\n            return ctx.closePanel();\n          });\n          i0.ɵɵtext(35, \" \\u00D7 \");\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate1(\" \\u2705 CallService: \", ctx.callServiceStatus, \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \\u2705 Subscription: \", ctx.subscriptionStatus, \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \\uD83D\\uDCDE Appel actif: \", ctx.activeCallStatus, \" \");\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.testRecipientId);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", ctx.isTestRunning);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", ctx.isTestRunning);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", ctx.isTestRunning);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"disabled\", !ctx.hasActiveCall);\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"ngForOf\", ctx.testLogs);\n        }\n      },\n      dependencies: [i4.NgForOf, i5.DefaultValueAccessor, i5.NgControlStatus, i5.NgModel],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["CallType", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵstyleProp", "ctx_r0", "getLogColor", "log_r1", "ɵɵadvance", "ɵɵtextInterpolate1", "CallTestComponent", "constructor", "callService", "messageService", "toastService", "testRecipientId", "isTestRunning", "hasActiveCall", "callServiceStatus", "subscriptionStatus", "activeCallStatus", "testLogs", "subscriptions", "ngOnInit", "initializeTestPanel", "setupSubscriptions", "ngOnDestroy", "for<PERSON>ach", "sub", "unsubscribe", "addLog", "activeCallSub", "activeCall$", "subscribe", "call", "type", "status", "incomingCallSub", "incomingCall$", "caller", "username", "push", "testVideoCall", "showError", "initiateCall", "VIDEO", "next", "id", "error", "message", "testAudioCall", "AUDIO", "simulateIncomingCall", "mockIncomingCall", "Date", "now", "image", "conversationId", "reinitializeSubscription", "setTimeout", "endCurrentCall", "runDiagnostic", "token", "localStorage", "getItem", "userStr", "userId", "user", "JSON", "parse", "_id", "setItem", "testSimpleGraphQLQuery", "getConversations", "conversations", "length", "closePanel", "panel", "document", "querySelector", "style", "display", "timestamp", "toLocaleTimeString", "unshift", "slice", "log", "includes", "ɵɵdirectiveInject", "i1", "CallService", "i2", "MessageService", "i3", "ToastService", "selectors", "decls", "vars", "consts", "template", "CallTestComponent_Template", "rf", "ctx", "ɵɵlistener", "CallTestComponent_Template_input_ngModelChange_13_listener", "$event", "CallTestComponent_Template_button_click_15_listener", "CallTestComponent_Template_button_click_17_listener", "CallTestComponent_Template_button_click_19_listener", "CallTestComponent_Template_button_click_21_listener", "CallTestComponent_Template_button_click_23_listener", "CallTestComponent_Template_button_click_25_listener", "CallTestComponent_Template_button_click_27_listener", "useCurrentUserId", "ɵɵtemplate", "CallTestComponent_div_33_Template", "CallTestComponent_Template_button_click_34_listener", "ɵɵproperty"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\components\\call-test\\call-test.component.ts"], "sourcesContent": ["import { Component, OnIni<PERSON>, On<PERSON><PERSON>roy } from '@angular/core';\nimport { CallService } from '../../services/call.service';\nimport { MessageService } from '../../services/message.service';\nimport { ToastService } from '../../services/toast.service';\nimport { CallType, CallStatus } from '../../models/message.model';\nimport { Subscription } from 'rxjs';\n\n@Component({\n  selector: 'app-call-test',\n  template: `\n    <div\n      class=\"call-test-panel\"\n      style=\"\n      position: fixed;\n      top: 20px;\n      right: 20px;\n      width: 300px;\n      background: linear-gradient(135deg, #1f2937 0%, #111827 100%);\n      border: 2px solid #3b82f6;\n      border-radius: 12px;\n      padding: 16px;\n      color: white;\n      z-index: 10000;\n      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);\n    \"\n    >\n      <h3 style=\"margin: 0 0 16px 0; color: #3b82f6; font-size: 16px;\">\n        🧪 Test d'Appels\n      </h3>\n\n      <!-- État des services -->\n      <div style=\"margin-bottom: 16px; font-size: 12px;\">\n        <div style=\"color: #10b981;\">\n          ✅ CallService: {{ callServiceStatus }}\n        </div>\n        <div style=\"color: #10b981;\">\n          ✅ Subscription: {{ subscriptionStatus }}\n        </div>\n        <div style=\"color: #f59e0b;\">\n          📞 Appel actif: {{ activeCallStatus }}\n        </div>\n      </div>\n\n      <!-- Simulation d'utilisateurs -->\n      <div style=\"margin-bottom: 16px;\">\n        <label style=\"display: block; margin-bottom: 4px; font-size: 12px;\">\n          ID Destinataire:\n        </label>\n        <input\n          [(ngModel)]=\"testRecipientId\"\n          placeholder=\"ID utilisateur\"\n          style=\"\n            width: 100%;\n            padding: 6px;\n            border: 1px solid #374151;\n            border-radius: 6px;\n            background: #374151;\n            color: white;\n            font-size: 12px;\n          \"\n        />\n      </div>\n\n      <!-- Boutons de test -->\n      <div style=\"display: flex; flex-direction: column; gap: 8px;\">\n        <button\n          (click)=\"testVideoCall()\"\n          [disabled]=\"isTestRunning\"\n          style=\"\n            padding: 8px 12px;\n            background: #3b82f6;\n            color: white;\n            border: none;\n            border-radius: 6px;\n            cursor: pointer;\n            font-size: 12px;\n            transition: all 0.2s;\n          \"\n          onmouseover=\"this.style.background='#2563eb'\"\n          onmouseout=\"this.style.background='#3b82f6'\"\n        >\n          📹 Test Appel Vidéo\n        </button>\n\n        <button\n          (click)=\"testAudioCall()\"\n          [disabled]=\"isTestRunning\"\n          style=\"\n            padding: 8px 12px;\n            background: #10b981;\n            color: white;\n            border: none;\n            border-radius: 6px;\n            cursor: pointer;\n            font-size: 12px;\n            transition: all 0.2s;\n          \"\n          onmouseover=\"this.style.background='#059669'\"\n          onmouseout=\"this.style.background='#10b981'\"\n        >\n          📞 Test Appel Audio\n        </button>\n\n        <button\n          (click)=\"simulateIncomingCall()\"\n          [disabled]=\"isTestRunning\"\n          style=\"\n            padding: 8px 12px;\n            background: #f59e0b;\n            color: white;\n            border: none;\n            border-radius: 6px;\n            cursor: pointer;\n            font-size: 12px;\n            transition: all 0.2s;\n          \"\n          onmouseover=\"this.style.background='#d97706'\"\n          onmouseout=\"this.style.background='#f59e0b'\"\n        >\n          📲 Simuler Appel Entrant\n        </button>\n\n        <button\n          (click)=\"reinitializeSubscription()\"\n          style=\"\n            padding: 8px 12px;\n            background: #6b7280;\n            color: white;\n            border: none;\n            border-radius: 6px;\n            cursor: pointer;\n            font-size: 12px;\n            transition: all 0.2s;\n          \"\n          onmouseover=\"this.style.background='#4b5563'\"\n          onmouseout=\"this.style.background='#6b7280'\"\n        >\n          🔄 Réinitialiser Subscription\n        </button>\n\n        <button\n          (click)=\"endCurrentCall()\"\n          [disabled]=\"!hasActiveCall\"\n          style=\"\n            padding: 8px 12px;\n            background: #ef4444;\n            color: white;\n            border: none;\n            border-radius: 6px;\n            cursor: pointer;\n            font-size: 12px;\n            transition: all 0.2s;\n          \"\n          onmouseover=\"this.style.background='#dc2626'\"\n          onmouseout=\"this.style.background='#ef4444'\"\n        >\n          ❌ Terminer Appel\n        </button>\n\n        <button\n          (click)=\"runDiagnostic()\"\n          style=\"\n            padding: 8px 12px;\n            background: #8b5cf6;\n            color: white;\n            border: none;\n            border-radius: 6px;\n            cursor: pointer;\n            font-size: 12px;\n            transition: all 0.2s;\n          \"\n          onmouseover=\"this.style.background='#7c3aed'\"\n          onmouseout=\"this.style.background='#8b5cf6'\"\n        >\n          🔍 Diagnostic Complet\n        </button>\n\n        <button\n          (click)=\"useCurrentUserId()\"\n          style=\"\n            padding: 8px 12px;\n            background: #059669;\n            color: white;\n            border: none;\n            border-radius: 6px;\n            cursor: pointer;\n            font-size: 12px;\n            transition: all 0.2s;\n          \"\n          onmouseover=\"this.style.background='#047857'\"\n          onmouseout=\"this.style.background='#059669'\"\n        >\n          👤 Utiliser mon ID\n        </button>\n      </div>\n\n      <!-- Logs de test -->\n      <div style=\"margin-top: 16px;\">\n        <div style=\"font-size: 12px; color: #9ca3af; margin-bottom: 8px;\">\n          📋 Logs de test:\n        </div>\n        <div\n          style=\"\n          max-height: 120px;\n          overflow-y: auto;\n          background: #000;\n          padding: 8px;\n          border-radius: 4px;\n          font-family: monospace;\n          font-size: 10px;\n        \"\n        >\n          <div *ngFor=\"let log of testLogs\" [style.color]=\"getLogColor(log)\">\n            {{ log }}\n          </div>\n        </div>\n      </div>\n\n      <!-- Bouton fermer -->\n      <button\n        (click)=\"closePanel()\"\n        style=\"\n          position: absolute;\n          top: 8px;\n          right: 8px;\n          width: 20px;\n          height: 20px;\n          background: #ef4444;\n          color: white;\n          border: none;\n          border-radius: 50%;\n          cursor: pointer;\n          font-size: 12px;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n        \"\n      >\n        ×\n      </button>\n    </div>\n  `,\n  styles: [],\n})\nexport class CallTestComponent implements OnInit, OnDestroy {\n  testRecipientId = '';\n  isTestRunning = false;\n  hasActiveCall = false;\n  callServiceStatus = 'Initialisation...';\n  subscriptionStatus = 'En attente...';\n  activeCallStatus = 'Aucun';\n  testLogs: string[] = [];\n\n  private subscriptions: Subscription[] = [];\n\n  constructor(\n    private callService: CallService,\n    private messageService: MessageService,\n    private toastService: ToastService\n  ) {}\n\n  ngOnInit(): void {\n    this.initializeTestPanel();\n    this.setupSubscriptions();\n  }\n\n  ngOnDestroy(): void {\n    this.subscriptions.forEach((sub) => sub.unsubscribe());\n  }\n\n  private initializeTestPanel(): void {\n    this.addLog('🧪 Panel de test initialisé');\n    this.callServiceStatus = 'Actif';\n    this.subscriptionStatus = 'Connecté';\n  }\n\n  private setupSubscriptions(): void {\n    // Surveiller les appels actifs\n    const activeCallSub = this.callService.activeCall$.subscribe((call) => {\n      this.hasActiveCall = !!call;\n      this.activeCallStatus = call ? `${call.type} - ${call.status}` : 'Aucun';\n      if (call) {\n        this.addLog(`📞 Appel actif: ${call.type} (${call.status})`);\n      }\n    });\n\n    // Surveiller les appels entrants\n    const incomingCallSub = this.callService.incomingCall$.subscribe((call) => {\n      if (call) {\n        this.addLog(\n          `📲 Appel entrant: ${call.type} de ${call.caller?.username}`\n        );\n      }\n    });\n\n    this.subscriptions.push(activeCallSub, incomingCallSub);\n  }\n\n  testVideoCall(): void {\n    if (!this.testRecipientId) {\n      this.toastService.showError('Veuillez saisir un ID destinataire');\n      return;\n    }\n\n    this.isTestRunning = true;\n    this.addLog(`📹 Test appel vidéo vers ${this.testRecipientId}`);\n\n    this.callService\n      .initiateCall(this.testRecipientId, CallType.VIDEO)\n      .subscribe({\n        next: (call) => {\n          this.addLog(`✅ Appel vidéo initié: ${call.id}`);\n          this.isTestRunning = false;\n        },\n        error: (error) => {\n          this.addLog(`❌ Erreur appel vidéo: ${error.message}`);\n          this.isTestRunning = false;\n        },\n      });\n  }\n\n  testAudioCall(): void {\n    if (!this.testRecipientId) {\n      this.toastService.showError('Veuillez saisir un ID destinataire');\n      return;\n    }\n\n    this.isTestRunning = true;\n    this.addLog(`📞 Test appel audio vers ${this.testRecipientId}`);\n\n    this.callService\n      .initiateCall(this.testRecipientId, CallType.AUDIO)\n      .subscribe({\n        next: (call) => {\n          this.addLog(`✅ Appel audio initié: ${call.id}`);\n          this.isTestRunning = false;\n        },\n        error: (error) => {\n          this.addLog(`❌ Erreur appel audio: ${error.message}`);\n          this.isTestRunning = false;\n        },\n      });\n  }\n\n  simulateIncomingCall(): void {\n    this.addLog(\"📲 Simulation d'appel entrant...\");\n    // Cette méthode simule un appel entrant pour les tests\n    const mockIncomingCall = {\n      id: `test_call_${Date.now()}`,\n      type: CallType.VIDEO,\n      caller: {\n        id: 'test_user',\n        username: 'Utilisateur Test',\n        image: '/assets/images/default-avatar.png',\n      },\n      conversationId: 'test_conversation',\n    };\n\n    // Simuler la réception d'un appel entrant\n    this.callService['handleIncomingCall'](mockIncomingCall as any);\n    this.addLog('✅ Appel entrant simulé');\n  }\n\n  reinitializeSubscription(): void {\n    this.addLog('🔄 Réinitialisation des subscriptions...');\n\n    // Vérifier si la méthode existe\n    if (typeof this.callService['reinitializeSubscription'] === 'function') {\n      this.callService['reinitializeSubscription']();\n    } else {\n      this.addLog('⚠️ Méthode reinitializeSubscription non trouvée');\n      // Réinitialiser manuellement\n      this.callService['subscribeToIncomingCalls']();\n    }\n\n    this.subscriptionStatus = 'Reconnexion...';\n\n    setTimeout(() => {\n      this.subscriptionStatus = 'Connecté';\n      this.addLog('✅ Subscriptions réinitialisées');\n    }, 2000);\n  }\n\n  endCurrentCall(): void {\n    this.addLog(\"❌ Fin de l'appel en cours...\");\n    // Logique pour terminer l'appel actuel\n    this.hasActiveCall = false;\n    this.activeCallStatus = 'Aucun';\n  }\n\n  runDiagnostic(): void {\n    this.addLog('🔍 Démarrage du diagnostic complet...');\n\n    // 1. Vérifier l'authentification\n    const token = localStorage.getItem('token');\n    const userStr = localStorage.getItem('user');\n    let userId = null;\n\n    if (!token) {\n      this.addLog(\"❌ Token d'authentification manquant\");\n    } else {\n      this.addLog(\"✅ Token d'authentification présent\");\n    }\n\n    if (!userStr) {\n      this.addLog('❌ Données utilisateur manquantes');\n    } else {\n      try {\n        const user = JSON.parse(userStr);\n        userId = user._id || user.id;\n        if (userId) {\n          this.addLog(`✅ ID utilisateur: ${userId}`);\n          // Stocker l'ID pour les tests\n          localStorage.setItem('userId', userId);\n        } else {\n          this.addLog('❌ ID utilisateur non trouvé dans les données');\n        }\n      } catch (error) {\n        this.addLog('❌ Erreur parsing données utilisateur');\n      }\n    }\n\n    // 2. Vérifier la connexion GraphQL\n    this.addLog('🔄 Test de connexion GraphQL...');\n\n    // 3. Vérifier les services\n    this.addLog('🔄 Vérification des services...');\n\n    if (this.callService) {\n      this.addLog('✅ CallService disponible');\n    } else {\n      this.addLog('❌ CallService indisponible');\n    }\n\n    if (this.messageService) {\n      this.addLog('✅ MessageService disponible');\n    } else {\n      this.addLog('❌ MessageService indisponible');\n    }\n\n    // 4. Test de mutation simple\n    this.testSimpleGraphQLQuery();\n  }\n\n  private testSimpleGraphQLQuery(): void {\n    this.addLog('🔄 Test de requête GraphQL simple...');\n\n    // Test avec une query simple pour vérifier la connexion\n    this.messageService.getConversations().subscribe({\n      next: (conversations) => {\n        this.addLog(\n          `✅ GraphQL fonctionne - ${conversations.length} conversations trouvées`\n        );\n        this.addLog(\"🎯 Diagnostic terminé - Prêt pour les tests d'appel\");\n      },\n      error: (error) => {\n        this.addLog(`❌ Erreur GraphQL: ${error.message}`);\n        this.addLog(\"⚠️ Vérifiez la connexion backend et l'authentification\");\n      },\n    });\n  }\n\n  closePanel(): void {\n    // Masquer le panel de test\n    const panel = document.querySelector('.call-test-panel') as HTMLElement;\n    if (panel) {\n      panel.style.display = 'none';\n    }\n  }\n\n  private addLog(message: string): void {\n    const timestamp = new Date().toLocaleTimeString();\n    this.testLogs.unshift(`[${timestamp}] ${message}`);\n\n    // Garder seulement les 20 derniers logs\n    if (this.testLogs.length > 20) {\n      this.testLogs = this.testLogs.slice(0, 20);\n    }\n  }\n\n  getLogColor(log: string): string {\n    if (log.includes('❌')) return '#ef4444';\n    if (log.includes('✅')) return '#10b981';\n    if (log.includes('📞') || log.includes('📹')) return '#3b82f6';\n    if (log.includes('📲')) return '#f59e0b';\n    if (log.includes('🔄')) return '#6b7280';\n    return '#9ca3af';\n  }\n}\n"], "mappings": "AAIA,SAASA,QAAQ,QAAoB,4BAA4B;;;;;;;;;IAgNvDC,EAAA,CAAAC,cAAA,UAAmE;IACjED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAF4BH,EAAA,CAAAI,WAAA,UAAAC,MAAA,CAAAC,WAAA,CAAAC,MAAA,EAAgC;IAChEP,EAAA,CAAAQ,SAAA,GACF;IADER,EAAA,CAAAS,kBAAA,MAAAF,MAAA,MACF;;;AA8BV,OAAM,MAAOG,iBAAiB;EAW5BC,YACUC,WAAwB,EACxBC,cAA8B,EAC9BC,YAA0B;IAF1B,KAAAF,WAAW,GAAXA,WAAW;IACX,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,YAAY,GAAZA,YAAY;IAbtB,KAAAC,eAAe,GAAG,EAAE;IACpB,KAAAC,aAAa,GAAG,KAAK;IACrB,KAAAC,aAAa,GAAG,KAAK;IACrB,KAAAC,iBAAiB,GAAG,mBAAmB;IACvC,KAAAC,kBAAkB,GAAG,eAAe;IACpC,KAAAC,gBAAgB,GAAG,OAAO;IAC1B,KAAAC,QAAQ,GAAa,EAAE;IAEf,KAAAC,aAAa,GAAmB,EAAE;EAMvC;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,mBAAmB,EAAE;IAC1B,IAAI,CAACC,kBAAkB,EAAE;EAC3B;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACJ,aAAa,CAACK,OAAO,CAAEC,GAAG,IAAKA,GAAG,CAACC,WAAW,EAAE,CAAC;EACxD;EAEQL,mBAAmBA,CAAA;IACzB,IAAI,CAACM,MAAM,CAAC,6BAA6B,CAAC;IAC1C,IAAI,CAACZ,iBAAiB,GAAG,OAAO;IAChC,IAAI,CAACC,kBAAkB,GAAG,UAAU;EACtC;EAEQM,kBAAkBA,CAAA;IACxB;IACA,MAAMM,aAAa,GAAG,IAAI,CAACnB,WAAW,CAACoB,WAAW,CAACC,SAAS,CAAEC,IAAI,IAAI;MACpE,IAAI,CAACjB,aAAa,GAAG,CAAC,CAACiB,IAAI;MAC3B,IAAI,CAACd,gBAAgB,GAAGc,IAAI,GAAG,GAAGA,IAAI,CAACC,IAAI,MAAMD,IAAI,CAACE,MAAM,EAAE,GAAG,OAAO;MACxE,IAAIF,IAAI,EAAE;QACR,IAAI,CAACJ,MAAM,CAAC,mBAAmBI,IAAI,CAACC,IAAI,KAAKD,IAAI,CAACE,MAAM,GAAG,CAAC;;IAEhE,CAAC,CAAC;IAEF;IACA,MAAMC,eAAe,GAAG,IAAI,CAACzB,WAAW,CAAC0B,aAAa,CAACL,SAAS,CAAEC,IAAI,IAAI;MACxE,IAAIA,IAAI,EAAE;QACR,IAAI,CAACJ,MAAM,CACT,qBAAqBI,IAAI,CAACC,IAAI,OAAOD,IAAI,CAACK,MAAM,EAAEC,QAAQ,EAAE,CAC7D;;IAEL,CAAC,CAAC;IAEF,IAAI,CAAClB,aAAa,CAACmB,IAAI,CAACV,aAAa,EAAEM,eAAe,CAAC;EACzD;EAEAK,aAAaA,CAAA;IACX,IAAI,CAAC,IAAI,CAAC3B,eAAe,EAAE;MACzB,IAAI,CAACD,YAAY,CAAC6B,SAAS,CAAC,oCAAoC,CAAC;MACjE;;IAGF,IAAI,CAAC3B,aAAa,GAAG,IAAI;IACzB,IAAI,CAACc,MAAM,CAAC,4BAA4B,IAAI,CAACf,eAAe,EAAE,CAAC;IAE/D,IAAI,CAACH,WAAW,CACbgC,YAAY,CAAC,IAAI,CAAC7B,eAAe,EAAEhB,QAAQ,CAAC8C,KAAK,CAAC,CAClDZ,SAAS,CAAC;MACTa,IAAI,EAAGZ,IAAI,IAAI;QACb,IAAI,CAACJ,MAAM,CAAC,yBAAyBI,IAAI,CAACa,EAAE,EAAE,CAAC;QAC/C,IAAI,CAAC/B,aAAa,GAAG,KAAK;MAC5B,CAAC;MACDgC,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAClB,MAAM,CAAC,yBAAyBkB,KAAK,CAACC,OAAO,EAAE,CAAC;QACrD,IAAI,CAACjC,aAAa,GAAG,KAAK;MAC5B;KACD,CAAC;EACN;EAEAkC,aAAaA,CAAA;IACX,IAAI,CAAC,IAAI,CAACnC,eAAe,EAAE;MACzB,IAAI,CAACD,YAAY,CAAC6B,SAAS,CAAC,oCAAoC,CAAC;MACjE;;IAGF,IAAI,CAAC3B,aAAa,GAAG,IAAI;IACzB,IAAI,CAACc,MAAM,CAAC,4BAA4B,IAAI,CAACf,eAAe,EAAE,CAAC;IAE/D,IAAI,CAACH,WAAW,CACbgC,YAAY,CAAC,IAAI,CAAC7B,eAAe,EAAEhB,QAAQ,CAACoD,KAAK,CAAC,CAClDlB,SAAS,CAAC;MACTa,IAAI,EAAGZ,IAAI,IAAI;QACb,IAAI,CAACJ,MAAM,CAAC,yBAAyBI,IAAI,CAACa,EAAE,EAAE,CAAC;QAC/C,IAAI,CAAC/B,aAAa,GAAG,KAAK;MAC5B,CAAC;MACDgC,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAClB,MAAM,CAAC,yBAAyBkB,KAAK,CAACC,OAAO,EAAE,CAAC;QACrD,IAAI,CAACjC,aAAa,GAAG,KAAK;MAC5B;KACD,CAAC;EACN;EAEAoC,oBAAoBA,CAAA;IAClB,IAAI,CAACtB,MAAM,CAAC,kCAAkC,CAAC;IAC/C;IACA,MAAMuB,gBAAgB,GAAG;MACvBN,EAAE,EAAE,aAAaO,IAAI,CAACC,GAAG,EAAE,EAAE;MAC7BpB,IAAI,EAAEpC,QAAQ,CAAC8C,KAAK;MACpBN,MAAM,EAAE;QACNQ,EAAE,EAAE,WAAW;QACfP,QAAQ,EAAE,kBAAkB;QAC5BgB,KAAK,EAAE;OACR;MACDC,cAAc,EAAE;KACjB;IAED;IACA,IAAI,CAAC7C,WAAW,CAAC,oBAAoB,CAAC,CAACyC,gBAAuB,CAAC;IAC/D,IAAI,CAACvB,MAAM,CAAC,wBAAwB,CAAC;EACvC;EAEA4B,wBAAwBA,CAAA;IACtB,IAAI,CAAC5B,MAAM,CAAC,0CAA0C,CAAC;IAEvD;IACA,IAAI,OAAO,IAAI,CAAClB,WAAW,CAAC,0BAA0B,CAAC,KAAK,UAAU,EAAE;MACtE,IAAI,CAACA,WAAW,CAAC,0BAA0B,CAAC,EAAE;KAC/C,MAAM;MACL,IAAI,CAACkB,MAAM,CAAC,iDAAiD,CAAC;MAC9D;MACA,IAAI,CAAClB,WAAW,CAAC,0BAA0B,CAAC,EAAE;;IAGhD,IAAI,CAACO,kBAAkB,GAAG,gBAAgB;IAE1CwC,UAAU,CAAC,MAAK;MACd,IAAI,CAACxC,kBAAkB,GAAG,UAAU;MACpC,IAAI,CAACW,MAAM,CAAC,gCAAgC,CAAC;IAC/C,CAAC,EAAE,IAAI,CAAC;EACV;EAEA8B,cAAcA,CAAA;IACZ,IAAI,CAAC9B,MAAM,CAAC,8BAA8B,CAAC;IAC3C;IACA,IAAI,CAACb,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACG,gBAAgB,GAAG,OAAO;EACjC;EAEAyC,aAAaA,CAAA;IACX,IAAI,CAAC/B,MAAM,CAAC,uCAAuC,CAAC;IAEpD;IACA,MAAMgC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,MAAMC,OAAO,GAAGF,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;IAC5C,IAAIE,MAAM,GAAG,IAAI;IAEjB,IAAI,CAACJ,KAAK,EAAE;MACV,IAAI,CAAChC,MAAM,CAAC,qCAAqC,CAAC;KACnD,MAAM;MACL,IAAI,CAACA,MAAM,CAAC,oCAAoC,CAAC;;IAGnD,IAAI,CAACmC,OAAO,EAAE;MACZ,IAAI,CAACnC,MAAM,CAAC,kCAAkC,CAAC;KAChD,MAAM;MACL,IAAI;QACF,MAAMqC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACJ,OAAO,CAAC;QAChCC,MAAM,GAAGC,IAAI,CAACG,GAAG,IAAIH,IAAI,CAACpB,EAAE;QAC5B,IAAImB,MAAM,EAAE;UACV,IAAI,CAACpC,MAAM,CAAC,qBAAqBoC,MAAM,EAAE,CAAC;UAC1C;UACAH,YAAY,CAACQ,OAAO,CAAC,QAAQ,EAAEL,MAAM,CAAC;SACvC,MAAM;UACL,IAAI,CAACpC,MAAM,CAAC,8CAA8C,CAAC;;OAE9D,CAAC,OAAOkB,KAAK,EAAE;QACd,IAAI,CAAClB,MAAM,CAAC,sCAAsC,CAAC;;;IAIvD;IACA,IAAI,CAACA,MAAM,CAAC,iCAAiC,CAAC;IAE9C;IACA,IAAI,CAACA,MAAM,CAAC,iCAAiC,CAAC;IAE9C,IAAI,IAAI,CAAClB,WAAW,EAAE;MACpB,IAAI,CAACkB,MAAM,CAAC,0BAA0B,CAAC;KACxC,MAAM;MACL,IAAI,CAACA,MAAM,CAAC,4BAA4B,CAAC;;IAG3C,IAAI,IAAI,CAACjB,cAAc,EAAE;MACvB,IAAI,CAACiB,MAAM,CAAC,6BAA6B,CAAC;KAC3C,MAAM;MACL,IAAI,CAACA,MAAM,CAAC,+BAA+B,CAAC;;IAG9C;IACA,IAAI,CAAC0C,sBAAsB,EAAE;EAC/B;EAEQA,sBAAsBA,CAAA;IAC5B,IAAI,CAAC1C,MAAM,CAAC,sCAAsC,CAAC;IAEnD;IACA,IAAI,CAACjB,cAAc,CAAC4D,gBAAgB,EAAE,CAACxC,SAAS,CAAC;MAC/Ca,IAAI,EAAG4B,aAAa,IAAI;QACtB,IAAI,CAAC5C,MAAM,CACT,0BAA0B4C,aAAa,CAACC,MAAM,yBAAyB,CACxE;QACD,IAAI,CAAC7C,MAAM,CAAC,qDAAqD,CAAC;MACpE,CAAC;MACDkB,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAClB,MAAM,CAAC,qBAAqBkB,KAAK,CAACC,OAAO,EAAE,CAAC;QACjD,IAAI,CAACnB,MAAM,CAAC,wDAAwD,CAAC;MACvE;KACD,CAAC;EACJ;EAEA8C,UAAUA,CAAA;IACR;IACA,MAAMC,KAAK,GAAGC,QAAQ,CAACC,aAAa,CAAC,kBAAkB,CAAgB;IACvE,IAAIF,KAAK,EAAE;MACTA,KAAK,CAACG,KAAK,CAACC,OAAO,GAAG,MAAM;;EAEhC;EAEQnD,MAAMA,CAACmB,OAAe;IAC5B,MAAMiC,SAAS,GAAG,IAAI5B,IAAI,EAAE,CAAC6B,kBAAkB,EAAE;IACjD,IAAI,CAAC9D,QAAQ,CAAC+D,OAAO,CAAC,IAAIF,SAAS,KAAKjC,OAAO,EAAE,CAAC;IAElD;IACA,IAAI,IAAI,CAAC5B,QAAQ,CAACsD,MAAM,GAAG,EAAE,EAAE;MAC7B,IAAI,CAACtD,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACgE,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;;EAE9C;EAEA/E,WAAWA,CAACgF,GAAW;IACrB,IAAIA,GAAG,CAACC,QAAQ,CAAC,GAAG,CAAC,EAAE,OAAO,SAAS;IACvC,IAAID,GAAG,CAACC,QAAQ,CAAC,GAAG,CAAC,EAAE,OAAO,SAAS;IACvC,IAAID,GAAG,CAACC,QAAQ,CAAC,IAAI,CAAC,IAAID,GAAG,CAACC,QAAQ,CAAC,IAAI,CAAC,EAAE,OAAO,SAAS;IAC9D,IAAID,GAAG,CAACC,QAAQ,CAAC,IAAI,CAAC,EAAE,OAAO,SAAS;IACxC,IAAID,GAAG,CAACC,QAAQ,CAAC,IAAI,CAAC,EAAE,OAAO,SAAS;IACxC,OAAO,SAAS;EAClB;;;uBAnPW7E,iBAAiB,EAAAV,EAAA,CAAAwF,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA1F,EAAA,CAAAwF,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAA5F,EAAA,CAAAwF,iBAAA,CAAAK,EAAA,CAAAC,YAAA;IAAA;EAAA;;;YAAjBpF,iBAAiB;MAAAqF,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA1O1BrG,EAAA,CAAAC,cAAA,aAeC;UAEGD,EAAA,CAAAE,MAAA,mCACF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAGLH,EAAA,CAAAC,cAAA,aAAmD;UAE/CD,EAAA,CAAAE,MAAA,GACF;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,aAA6B;UAC3BD,EAAA,CAAAE,MAAA,GACF;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,aAA6B;UAC3BD,EAAA,CAAAE,MAAA,GACF;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAIRH,EAAA,CAAAC,cAAA,cAAkC;UAE9BD,EAAA,CAAAE,MAAA,0BACF;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAC,cAAA,gBAYE;UAXAD,EAAA,CAAAuG,UAAA,2BAAAC,2DAAAC,MAAA;YAAA,OAAAH,GAAA,CAAAvF,eAAA,GAAA0F,MAAA;UAAA,EAA6B;UAD/BzG,EAAA,CAAAG,YAAA,EAYE;UAIJH,EAAA,CAAAC,cAAA,cAA8D;UAE1DD,EAAA,CAAAuG,UAAA,mBAAAG,oDAAA;YAAA,OAASJ,GAAA,CAAA5D,aAAA,EAAe;UAAA,EAAC;UAezB1C,EAAA,CAAAE,MAAA,4CACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAETH,EAAA,CAAAC,cAAA,kBAeC;UAdCD,EAAA,CAAAuG,UAAA,mBAAAI,oDAAA;YAAA,OAASL,GAAA,CAAApD,aAAA,EAAe;UAAA,EAAC;UAezBlD,EAAA,CAAAE,MAAA,uCACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAETH,EAAA,CAAAC,cAAA,kBAeC;UAdCD,EAAA,CAAAuG,UAAA,mBAAAK,oDAAA;YAAA,OAASN,GAAA,CAAAlD,oBAAA,EAAsB;UAAA,EAAC;UAehCpD,EAAA,CAAAE,MAAA,4CACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAETH,EAAA,CAAAC,cAAA,kBAcC;UAbCD,EAAA,CAAAuG,UAAA,mBAAAM,oDAAA;YAAA,OAASP,GAAA,CAAA5C,wBAAA,EAA0B;UAAA,EAAC;UAcpC1D,EAAA,CAAAE,MAAA,sDACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAETH,EAAA,CAAAC,cAAA,kBAeC;UAdCD,EAAA,CAAAuG,UAAA,mBAAAO,oDAAA;YAAA,OAASR,GAAA,CAAA1C,cAAA,EAAgB;UAAA,EAAC;UAe1B5D,EAAA,CAAAE,MAAA,+BACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAETH,EAAA,CAAAC,cAAA,kBAcC;UAbCD,EAAA,CAAAuG,UAAA,mBAAAQ,oDAAA;YAAA,OAAST,GAAA,CAAAzC,aAAA,EAAe;UAAA,EAAC;UAczB7D,EAAA,CAAAE,MAAA,yCACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAETH,EAAA,CAAAC,cAAA,kBAcC;UAbCD,EAAA,CAAAuG,UAAA,mBAAAS,oDAAA;YAAA,OAASV,GAAA,CAAAW,gBAAA,EAAkB;UAAA,EAAC;UAc5BjH,EAAA,CAAAE,MAAA,sCACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAIXH,EAAA,CAAAC,cAAA,eAA+B;UAE3BD,EAAA,CAAAE,MAAA,oCACF;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,eAUC;UACCD,EAAA,CAAAkH,UAAA,KAAAC,iCAAA,kBAEM;UACRnH,EAAA,CAAAG,YAAA,EAAM;UAIRH,EAAA,CAAAC,cAAA,kBAkBC;UAjBCD,EAAA,CAAAuG,UAAA,mBAAAa,oDAAA;YAAA,OAASd,GAAA,CAAA1B,UAAA,EAAY;UAAA,EAAC;UAkBtB5E,EAAA,CAAAE,MAAA,gBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;;;UA9MLH,EAAA,CAAAQ,SAAA,GACF;UADER,EAAA,CAAAS,kBAAA,0BAAA6F,GAAA,CAAApF,iBAAA,MACF;UAEElB,EAAA,CAAAQ,SAAA,GACF;UADER,EAAA,CAAAS,kBAAA,2BAAA6F,GAAA,CAAAnF,kBAAA,MACF;UAEEnB,EAAA,CAAAQ,SAAA,GACF;UADER,EAAA,CAAAS,kBAAA,gCAAA6F,GAAA,CAAAlF,gBAAA,MACF;UASEpB,EAAA,CAAAQ,SAAA,GAA6B;UAA7BR,EAAA,CAAAqH,UAAA,YAAAf,GAAA,CAAAvF,eAAA,CAA6B;UAkB7Bf,EAAA,CAAAQ,SAAA,GAA0B;UAA1BR,EAAA,CAAAqH,UAAA,aAAAf,GAAA,CAAAtF,aAAA,CAA0B;UAmB1BhB,EAAA,CAAAQ,SAAA,GAA0B;UAA1BR,EAAA,CAAAqH,UAAA,aAAAf,GAAA,CAAAtF,aAAA,CAA0B;UAmB1BhB,EAAA,CAAAQ,SAAA,GAA0B;UAA1BR,EAAA,CAAAqH,UAAA,aAAAf,GAAA,CAAAtF,aAAA,CAA0B;UAqC1BhB,EAAA,CAAAQ,SAAA,GAA2B;UAA3BR,EAAA,CAAAqH,UAAA,cAAAf,GAAA,CAAArF,aAAA,CAA2B;UAsENjB,EAAA,CAAAQ,SAAA,IAAW;UAAXR,EAAA,CAAAqH,UAAA,YAAAf,GAAA,CAAAjF,QAAA,CAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}