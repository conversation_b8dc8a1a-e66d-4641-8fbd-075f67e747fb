"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[320],{6939:(en,b,d)=>{d.r(b),d.d(b,{PlanningsModule:()=>Q});var p=d(177),u=d(6647),o=d(9969),n=d(7705),f=d(6543),x=d(9271),v=d(8397),_=d(8448);function C(i,s){1&i&&(n.qSk(),n.joV(),n.j41(0,"div",12)(1,"div",13),n.nrm(2,"div",14)(3,"div",15),n.j41(4,"div",16),n.qSk(),n.j41(5,"svg",17),n.nrm(6,"path",18),n.k0s()()(),n.joV(),n.j41(7,"p",19),n.<PERSON><PERSON>(8,"Chargement des plannings..."),n.k0s()())}function w(i,s){1&i&&(n.qSk(),n.joV(),n.j41(0,"div",20),n.qSk(),n.j41(1,"svg",21),n.nrm(2,"path",22),n.k0s(),n.joV(),n.j41(3,"h3",23),n.EFF(4,"Aucun planning disponible"),n.k0s(),n.j41(5,"p",24),n.EFF(6,"Cr\xe9ez votre premier planning pour commencer \xe0 organiser vos r\xe9unions."),n.k0s(),n.j41(7,"a",25),n.qSk(),n.j41(8,"svg",26),n.nrm(9,"path",8),n.k0s(),n.EFF(10," Cr\xe9er un planning "),n.k0s()())}const k=function(i,s){return{"text-gray-800":i,"text-gray-400":s}};function P(i,s){if(1&i){const e=n.RV6();n.j41(0,"div",29),n.bIt("mouseenter",function(){const a=n.eBV(e).index,l=n.XpG(2);return n.Njj(l.onMouseEnter(a))})("mouseleave",function(){n.eBV(e);const t=n.XpG(2);return n.Njj(t.onMouseLeave())}),n.j41(1,"div",30)(2,"div")(3,"h3",31)(4,"a",32),n.EFF(5),n.k0s()(),n.nrm(6,"p",33),n.nI1(7,"highlightPresence"),n.k0s(),n.j41(8,"button",34),n.bIt("click",function(t){const l=n.eBV(e).$implicit;return n.XpG(2).deletePlanning(l._id),n.Njj(t.stopPropagation())}),n.qSk(),n.j41(9,"svg",35),n.nrm(10,"path",36),n.k0s()()(),n.joV(),n.j41(11,"div",37),n.qSk(),n.j41(12,"svg",38),n.nrm(13,"path",18),n.k0s(),n.EFF(14),n.nI1(15,"date"),n.nI1(16,"date"),n.k0s(),n.joV(),n.j41(17,"div",39)(18,"span",40)(19,"span",6),n.qSk(),n.j41(20,"svg",41),n.nrm(21,"path",18)(22,"circle",42)(23,"path",43),n.k0s(),n.joV(),n.j41(24,"strong"),n.EFF(25),n.k0s(),n.EFF(26,"\xa0r\xe9union(s) "),n.k0s()(),n.j41(27,"a",44),n.bIt("click",function(){const a=n.eBV(e).$implicit,l=n.XpG(2);return n.Njj(l.GotoDetail(a._id))}),n.EFF(28," Voir d\xe9tails \u2192 "),n.k0s()()()}if(2&i){const e=s.$implicit,r=s.index,t=n.XpG(2);n.Y8G("@cardHover",t.getCardState(r)),n.R7$(5),n.SpI(" ",e.titre," "),n.R7$(1),n.Y8G("innerHTML",n.bMT(7,8,e.description||"Aucune description"),n.npT),n.R7$(8),n.Lme(" ",n.i5U(15,10,e.dateDebut,"mediumDate")," - ",n.i5U(16,13,e.dateFin,"mediumDate")," "),n.R7$(4),n.Y8G("ngClass",n.l_i(16,k,((null==e.reunions?null:e.reunions.length)||0)>0,0===((null==e.reunions?null:e.reunions.length)||0))),n.R7$(2),n.Y8G("ngClass",n.l_i(19,k,((null==e.reunions?null:e.reunions.length)||0)>0,0===((null==e.reunions?null:e.reunions.length)||0))),n.R7$(5),n.JRh((null==e.reunions?null:e.reunions.length)||0)}}function F(i,s){if(1&i&&(n.qSk(),n.joV(),n.j41(0,"div",27),n.DNE(1,P,29,22,"div",28),n.k0s()),2&i){const e=n.XpG();n.Y8G("@staggerAnimation",e.plannings.length),n.R7$(1),n.Y8G("ngForOf",e.plannings)("ngForTrackBy",e.trackByFn)}}let M=(()=>{class i{constructor(e,r,t,a,l){this.planningService=e,this.authService=r,this.router=t,this.route=a,this.toastService=l,this.plannings=[],this.loading=!0,this.error=null,this.hoveredIndex=null}ngOnInit(){console.log("PlanningListComponent initialized"),this.router.events.subscribe(e=>{e instanceof u.wF&&(console.log("Navigation termin\xe9e, rechargement des plannings"),this.loadPlannings())}),this.loadPlannings()}loadPlannings(){this.loading=!0,console.log("Loading plannings..."),this.planningService.getAllPlannings().subscribe({next:e=>{if(console.log("Response received:",e),e.success){let r=e.plannings;r.sort((t,a)=>{const l=t.reunions?.length||0;return(a.reunions?.length||0)-l}),this.plannings=r,console.log("Plannings loaded and sorted by reunion count:",this.plannings.length),this.plannings.length>0&&(console.log("First planning:",this.plannings[0]),console.log("Reunion counts:",this.plannings.map(t=>({titre:t.titre,reunions:t.reunions?.length||0}))))}else console.error("Error in response:",e),this.toastService.showError("Erreur lors du chargement des plannings");this.loading=!1},error:e=>{console.error("Error loading plannings:",e),this.loading=!1,this.toastService.showError(`Erreur lors du chargement des plannings: ${e.message||e.statusText||"Erreur inconnue"}`)}})}deletePlanning(e){confirm("Supprimer ce planning ?")&&this.planningService.deletePlanning(e).subscribe({next:()=>{this.plannings=this.plannings.filter(r=>r._id!==e),this.toastService.showSuccess("Le planning a \xe9t\xe9 supprim\xe9 avec succ\xe8s")},error:r=>{console.error("Erreur lors de la suppression du planning:",r),403===r.status?this.toastService.showError("Acc\xe8s refus\xe9 : vous n'avez pas les droits pour supprimer ce planning"):401===r.status?this.toastService.showError("Vous devez \xeatre connect\xe9 pour supprimer un planning"):this.toastService.showError(r.error?.message||"Erreur lors de la suppression du planning",8e3)}})}GotoDetail(e){e&&this.router.navigate([e],{relativeTo:this.route})}onMouseEnter(e){this.hoveredIndex=e}onMouseLeave(){this.hoveredIndex=null}getCardState(e){return this.hoveredIndex===e?"hovered":"default"}trackByFn(e,r){return r._id||e.toString()}static{this.\u0275fac=function(r){return new(r||i)(n.rXU(f.z),n.rXU(x.V),n.rXU(u.Ix),n.rXU(u.nX),n.rXU(v.f))}}static{this.\u0275cmp=n.VBU({type:i,selectors:[["app-planning-list"]],decls:14,vars:4,consts:[[1,"container","mx-auto","px-4","py-6"],[1,"flex","justify-between","items-center","mb-6"],[1,"text-2xl","font-bold","text-gray-800","relative","planning-header"],[1,"bg-clip-text","text-transparent","bg-gradient-to-r","from-purple-600","to-blue-500"],[1,"underline-animation"],["routerLink","/plannings/nouveau",1,"px-4","py-2","bg-gradient-to-r","from-purple-600","to-blue-500","text-white","rounded-md","hover:from-purple-700","hover:to-blue-600","transition-all","duration-300","transform","hover:scale-105","hover:shadow-lg","add-button"],[1,"flex","items-center"],["xmlns","http://www.w3.org/2000/svg","fill","none","viewBox","0 0 24 24","stroke","currentColor",1,"h-5","w-5","mr-1"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M12 6v6m0 0v6m0-6h6m-6 0H6"],["class","text-center py-12",4,"ngIf"],["class","text-center py-12 bg-white rounded-lg shadow-md",4,"ngIf"],["class","grid gap-4 md:grid-cols-2 lg:grid-cols-3",4,"ngIf"],[1,"text-center","py-12"],[1,"relative","mx-auto","w-20","h-20"],[1,"absolute","top-0","left-0","w-full","h-full","border-4","border-purple-200","rounded-full"],[1,"absolute","top-0","left-0","w-full","h-full","border-4","border-transparent","border-t-purple-600","rounded-full","animate-spin"],[1,"absolute","top-1/2","left-1/2","transform","-translate-x-1/2","-translate-y-1/2","text-purple-600","font-semibold"],["xmlns","http://www.w3.org/2000/svg","fill","none","viewBox","0 0 24 24","stroke","currentColor",1,"h-8","w-8"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"],[1,"mt-4","text-gray-600","animate-pulse"],[1,"text-center","py-12","bg-white","rounded-lg","shadow-md"],["fill","none","viewBox","0 0 24 24","stroke","currentColor",1,"mx-auto","h-16","w-16","text-purple-300"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"],[1,"mt-4","text-xl","font-medium","text-gray-900"],[1,"mt-2","text-gray-600"],["routerLink","/plannings/nouveau",1,"mt-6","inline-flex","items-center","px-4","py-2","bg-purple-600","text-white","rounded-md","hover:bg-purple-700","transition-all","duration-300","transform","hover:scale-105"],["xmlns","http://www.w3.org/2000/svg","fill","none","viewBox","0 0 24 24","stroke","currentColor",1,"h-5","w-5","mr-2"],[1,"grid","gap-4","md:grid-cols-2","lg:grid-cols-3"],["class","bg-white rounded-lg shadow-md p-4 cursor-pointer transform transition-all duration-300 relative",3,"mouseenter","mouseleave",4,"ngFor","ngForOf","ngForTrackBy"],[1,"bg-white","rounded-lg","shadow-md","p-4","cursor-pointer","transform","transition-all","duration-300","relative",3,"mouseenter","mouseleave"],[1,"flex","justify-between","items-start"],[1,"text-lg","font-semibold","text-gray-800"],[1,"hover:text-purple-600","planning-title"],[1,"text-sm","mt-1",3,"innerHTML"],[1,"text-red-500","hover:text-red-700","transition-colors","duration-300",3,"click"],["xmlns","http://www.w3.org/2000/svg","fill","none","viewBox","0 0 24 24","stroke","currentColor",1,"h-5","w-5"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"],[1,"mt-3","flex","items-center","text-sm","text-purple-700","font-medium"],["fill","none","viewBox","0 0 24 24","stroke","currentColor",1,"h-4","w-4","mr-1","text-purple-700"],[1,"mt-4","pt-3","border-t","border-gray-100","flex","justify-between","items-center"],[1,"text-sm","font-medium","reunion-count",3,"ngClass"],["xmlns","http://www.w3.org/2000/svg","fill","none","viewBox","0 0 24 24",1,"h-4","w-4","mr-1",3,"ngClass"],["cx","12","cy","14","r","3","stroke-width","1.5"],["stroke-linecap","round","stroke-width","1.5","d","M12 12v2h2"],[1,"text-sm","hover:text-purple-900","font-medium","details-link",2,"color","#6b46c1 !important",3,"click"]],template:function(r,t){1&r&&(n.j41(0,"div",0)(1,"div",1)(2,"h1",2)(3,"span",3),n.EFF(4,"Mes Plannings"),n.k0s(),n.nrm(5,"span",4),n.k0s(),n.j41(6,"a",5)(7,"span",6),n.qSk(),n.j41(8,"svg",7),n.nrm(9,"path",8),n.k0s(),n.EFF(10," Nouveau Planning "),n.k0s()()(),n.DNE(11,C,9,0,"div",9),n.DNE(12,w,11,0,"div",10),n.DNE(13,F,2,3,"div",11),n.k0s()),2&r&&(n.R7$(1),n.Y8G("@fadeInDown",void 0),n.R7$(10),n.Y8G("ngIf",t.loading),n.R7$(1),n.Y8G("ngIf",!t.loading&&0===t.plannings.length),n.R7$(1),n.Y8G("ngIf",!t.loading&&t.plannings.length>0))},dependencies:[p.YU,p.Sq,p.bT,u.Wk,p.vh,_.I],styles:['@keyframes _ngcontent-%COMP%_pulse{0%{box-shadow:0 0 #7c3aed66}70%{box-shadow:0 0 0 10px #7c3aed00}to{box-shadow:0 0 #7c3aed00}}.card-hover[_ngcontent-%COMP%]{transition:all .3s ease}.card-hover[_ngcontent-%COMP%]:hover{transform:translateY(-5px);box-shadow:0 10px 20px #0000001a}.fade-in[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_fadeIn .5s ease-in-out}@keyframes _ngcontent-%COMP%_fadeIn{0%{opacity:0;transform:translateY(20px)}to{opacity:1;transform:translateY(0)}}.stagger-item[_ngcontent-%COMP%]{opacity:0;transform:translateY(20px)}.planning-header[_ngcontent-%COMP%]{position:relative;display:inline-block}.underline-animation[_ngcontent-%COMP%]{position:absolute;bottom:-8px;left:0;width:100%;height:3px;background:linear-gradient(90deg,#7c3aed,#3b82f6);border-radius:3px;transition:all .4s cubic-bezier(.68,-.55,.265,1.55)}.planning-header[_ngcontent-%COMP%]:hover   .underline-animation[_ngcontent-%COMP%]{transform:scaleX(1.05) translateY(-1px);box-shadow:0 2px 8px #7c3aed80}.add-button[_ngcontent-%COMP%]{transition:all .3s ease;position:relative;overflow:hidden}.add-button[_ngcontent-%COMP%]:before{content:"";position:absolute;top:0;left:-100%;width:100%;height:100%;background:linear-gradient(90deg,transparent,rgba(255,255,255,.2),transparent);transition:all .5s ease}.add-button[_ngcontent-%COMP%]:hover:before{left:100%}.grid[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]{transition:all .3s cubic-bezier(.25,.8,.25,1);backface-visibility:hidden;perspective:1000px}.grid[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:before{content:"";position:absolute;inset:-1px;background:linear-gradient(45deg,#7c3aed,#4f46e5,#3b82f6,#7c3aed);z-index:-1;border-radius:.5rem;opacity:0;transition:opacity .4s ease}.grid[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:hover:before{opacity:.08}.grid[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:hover   h3[_ngcontent-%COMP%], .grid[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:hover   a.hover\\:text-purple-600[_ngcontent-%COMP%]{color:#4a5568!important;font-weight:600}@keyframes _ngcontent-%COMP%_attention-pulse{0%{box-shadow:0 0 #7c3aed66}70%{box-shadow:0 0 0 8px #7c3aed00}to{box-shadow:0 0 #7c3aed00}}.grid[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(1){background:rgba(255,255,255,.95)!important;border:2px solid rgba(124,58,237,.1)}.grid[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(1)   h3[_ngcontent-%COMP%], .grid[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(1)   p[_ngcontent-%COMP%], .grid[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(1)   span[_ngcontent-%COMP%], .grid[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(1) a{color:#4a5568!important;font-weight:600}.grid[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]{position:relative;overflow:hidden}.grid[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:after{content:"";display:block;position:absolute;width:100%;height:100%;top:0;left:0;pointer-events:none;background-image:radial-gradient(circle,#fff 10%,transparent 10.01%);background-repeat:no-repeat;background-position:50%;transform:scale(10);opacity:0;transition:transform .5s,opacity 1s}.grid[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:active:after{transform:scale(0);opacity:.3;transition:0s}.grid[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:hover{transform:rotateX(.5deg) rotateY(.5deg) translateY(-2px)}.planning-title[_ngcontent-%COMP%]{display:block;color:#2d3748;font-weight:600;text-shadow:0 1px 2px rgba(0,0,0,.05);transition:all .3s ease;padding:2px 0}.planning-title[_ngcontent-%COMP%]:hover{color:#6b46c1!important;text-decoration:none}.details-link[_ngcontent-%COMP%]{position:relative;transition:all .3s ease;padding-right:5px;color:#6b46c1!important;font-weight:600}.details-link[_ngcontent-%COMP%]:after{content:"";position:absolute;bottom:-2px;left:0;width:0;height:2px;background-color:#6b46c1;transition:width .3s ease}.details-link[_ngcontent-%COMP%]:hover:after{width:100%}.reunion-count[_ngcontent-%COMP%]{display:flex;align-items:center}.reunion-count[_ngcontent-%COMP%]:before{content:"";display:inline-block;width:8px;height:8px;border-radius:50%;background-color:#4a5568;margin-right:6px}'],data:{animation:[(0,o.hZ)("staggerAnimation",[(0,o.kY)("* => *",[(0,o.P)(":enter",[(0,o.iF)({opacity:0,transform:"translateY(20px) scale(0.95)"}),(0,o.yc)("100ms",[(0,o.i0)("0.6s cubic-bezier(0.25, 0.8, 0.25, 1)",(0,o.i7)([(0,o.iF)({opacity:0,transform:"translateY(20px) scale(0.95)",offset:0}),(0,o.iF)({opacity:.6,transform:"translateY(10px) scale(0.98)",offset:.4}),(0,o.iF)({opacity:1,transform:"translateY(0) scale(1)",offset:1})]))])],{optional:!0})])]),(0,o.hZ)("cardHover",[(0,o.wk)("default",(0,o.iF)({transform:"scale(1) translateY(0)",boxShadow:"0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)"})),(0,o.wk)("hovered",(0,o.iF)({transform:"scale(1.02) translateY(-3px)",boxShadow:"0 15px 20px -5px rgba(0, 0, 0, 0.08), 0 8px 8px -5px rgba(0, 0, 0, 0.03)"})),(0,o.kY)("default => hovered",[(0,o.i0)("0.4s cubic-bezier(0.25, 0.8, 0.25, 1)")]),(0,o.kY)("hovered => default",[(0,o.i0)("0.3s cubic-bezier(0.25, 0.8, 0.25, 1)")])]),(0,o.hZ)("fadeInDown",[(0,o.kY)(":enter",[(0,o.iF)({opacity:0,transform:"translateY(-20px)"}),(0,o.i0)("0.5s ease-out",(0,o.iF)({opacity:1,transform:"translateY(0)"}))])])]}})}}return i})();var m=d(4533),j=d(78),y=d(345);function E(i,s){1&i&&(n.qSk(),n.joV(),n.j41(0,"div",7),n.nrm(1,"div",8),n.j41(2,"p",9),n.EFF(3,"Chargement des d\xe9tails..."),n.k0s()())}function O(i,s){if(1&i&&(n.qSk(),n.joV(),n.j41(0,"div",10)(1,"div",11),n.qSk(),n.j41(2,"svg",12),n.nrm(3,"path",13),n.k0s(),n.joV(),n.j41(4,"span"),n.EFF(5),n.k0s()()()),2&i){const e=n.XpG();n.R7$(5),n.JRh(e.error)}}function S(i,s){if(1&i&&(n.j41(0,"div",21),n.qSk(),n.j41(1,"svg",19),n.nrm(2,"path",54)(3,"path",55),n.k0s(),n.joV(),n.j41(4,"span"),n.EFF(5),n.k0s()()),2&i){const e=n.XpG(2);n.R7$(5),n.JRh(e.planning.lieu)}}function I(i,s){if(1&i&&(n.j41(0,"div",56)(1,"span"),n.EFF(2),n.k0s()()),2&i){const e=s.$implicit;n.xc7("animation-delay",.1*s.index+"s"),n.R7$(2),n.JRh(e.username)}}function D(i,s){if(1&i&&(n.qSk(),n.joV(),n.j41(0,"div",73),n.EFF(1),n.k0s()),2&i){const e=n.XpG().$implicit;n.R7$(1),n.SpI(" ",e.meta.description," ")}}function R(i,s){if(1&i){const e=n.RV6();n.j41(0,"li",61)(1,"div",62)(2,"div",63),n.nrm(3,"strong",64),n.nI1(4,"highlightPresence"),n.j41(5,"div",65),n.qSk(),n.j41(6,"svg",66),n.nrm(7,"path",67),n.k0s(),n.EFF(8),n.nI1(9,"date"),n.nI1(10,"date"),n.k0s(),n.DNE(11,D,2,1,"div",68),n.k0s(),n.joV(),n.j41(12,"div",69)(13,"button",70),n.bIt("click",function(){const a=n.eBV(e).$implicit,l=n.XpG(3);return n.Njj(l.editReunion(a.meta.id))}),n.qSk(),n.j41(14,"svg",71),n.nrm(15,"path",33),n.k0s()(),n.joV(),n.j41(16,"button",72),n.bIt("click",function(t){const l=n.eBV(e).$implicit;return n.XpG(3).deleteReunion(l.meta.id),n.Njj(t.stopPropagation())}),n.qSk(),n.j41(17,"svg",71),n.nrm(18,"path",35),n.k0s()()()()()}if(2&i){const e=s.$implicit;n.xc7("animation-delay",.1*s.index+"s"),n.R7$(3),n.Y8G("innerHTML",n.bMT(4,6,e.title),n.npT),n.R7$(5),n.Lme(" ",n.i5U(9,8,e.start,"shortTime")," - ",n.i5U(10,11,e.end,"shortTime")," "),n.R7$(3),n.Y8G("ngIf",null==e.meta?null:e.meta.description)}}function T(i,s){if(1&i&&(n.j41(0,"div",57)(1,"h3")(2,"span",11),n.qSk(),n.j41(3,"svg",58),n.nrm(4,"path",22),n.k0s(),n.EFF(5),n.nI1(6,"date"),n.k0s()(),n.joV(),n.j41(7,"ul",59),n.DNE(8,R,19,14,"li",60),n.k0s()()),2&i){const e=n.XpG(2);n.Y8G("@fadeInUp",void 0),n.R7$(5),n.SpI(" D\xe9tails pour le ",n.i5U(6,3,e.selectedDate,"fullDate")," "),n.R7$(3),n.Y8G("ngForOf",e.selectedDayEvents)}}function $(i,s){if(1&i){const e=n.RV6();n.qSk(),n.joV(),n.j41(0,"div",14),n.bIt("mouseenter",function(){n.eBV(e);const t=n.XpG();return n.Njj(t.onCardMouseEnter())})("mouseleave",function(){n.eBV(e);const t=n.XpG();return n.Njj(t.onCardMouseLeave())}),n.j41(1,"div",15)(2,"h1",16),n.EFF(3),n.k0s(),n.nrm(4,"p",17),n.nI1(5,"highlightPresence"),n.k0s(),n.j41(6,"div",18)(7,"h2"),n.qSk(),n.j41(8,"svg",19),n.nrm(9,"path",20),n.k0s(),n.EFF(10," Informations "),n.k0s(),n.joV(),n.j41(11,"div",21),n.qSk(),n.j41(12,"svg",19),n.nrm(13,"path",22),n.k0s(),n.joV(),n.j41(14,"span"),n.EFF(15," Du "),n.j41(16,"strong"),n.EFF(17),n.nI1(18,"date"),n.k0s(),n.EFF(19," au "),n.j41(20,"strong"),n.EFF(21),n.nI1(22,"date"),n.k0s()()(),n.DNE(23,S,6,1,"div",23),n.k0s(),n.j41(24,"div",18)(25,"h2"),n.qSk(),n.j41(26,"svg",19),n.nrm(27,"path",24),n.k0s(),n.EFF(28," Participants "),n.k0s(),n.joV(),n.j41(29,"div",25),n.DNE(30,I,3,3,"div",26),n.k0s()(),n.j41(31,"div",18)(32,"div",27)(33,"h2"),n.qSk(),n.j41(34,"svg",19),n.nrm(35,"path",22),n.k0s(),n.EFF(36," R\xe9unions associ\xe9es "),n.k0s(),n.joV(),n.j41(37,"button",28),n.bIt("click",function(){n.eBV(e);const t=n.XpG();return n.Njj(t.nouvelleReunion())}),n.j41(38,"span",11),n.qSk(),n.j41(39,"svg",29),n.nrm(40,"path",30),n.k0s(),n.EFF(41," Nouvelle R\xe9union "),n.k0s()()(),n.joV(),n.j41(42,"div",31)(43,"button",32),n.bIt("click",function(){n.eBV(e);const t=n.XpG();return n.Njj(t.editPlanning())}),n.j41(44,"span",11),n.qSk(),n.j41(45,"svg",29),n.nrm(46,"path",33),n.k0s(),n.EFF(47," Modifier Planning "),n.k0s()(),n.joV(),n.j41(48,"button",34),n.bIt("click",function(){n.eBV(e);const t=n.XpG();return n.Njj(t.deletePlanning())}),n.j41(49,"span",11),n.qSk(),n.j41(50,"svg",29),n.nrm(51,"path",35),n.k0s(),n.EFF(52," Supprimer Planning "),n.k0s()()(),n.joV(),n.j41(53,"div",36)(54,"div",37)(55,"div",38)(56,"div")(57,"p",39),n.EFF(58,"Total R\xe9unions"),n.k0s(),n.j41(59,"p",40),n.EFF(60),n.k0s()(),n.j41(61,"div",41),n.qSk(),n.j41(62,"svg",42),n.nrm(63,"path",43),n.k0s()()()(),n.joV(),n.j41(64,"div",44)(65,"div",38)(66,"div")(67,"p",39),n.EFF(68,"P\xe9riode"),n.k0s(),n.j41(69,"p",45),n.EFF(70),n.nI1(71,"date"),n.nI1(72,"date"),n.k0s()(),n.j41(73,"div",46),n.qSk(),n.j41(74,"svg",47),n.nrm(75,"path",22),n.k0s()()()(),n.joV(),n.j41(76,"div",48)(77,"div",38)(78,"div")(79,"p",39),n.EFF(80,"Participants"),n.k0s(),n.j41(81,"p",40),n.EFF(82),n.k0s()(),n.j41(83,"div",49),n.qSk(),n.j41(84,"svg",50),n.nrm(85,"path",24),n.k0s()()()()(),n.joV(),n.j41(86,"div",51)(87,"mwl-calendar-month-view",52),n.bIt("dayClicked",function(t){n.eBV(e);const a=n.XpG();return n.Njj(a.handleDayClick(t.day))}),n.k0s()(),n.DNE(88,T,9,6,"div",53),n.k0s()()}if(2&i){const e=n.XpG();n.Y8G("@cardHover",e.cardState),n.R7$(1),n.Y8G("@fadeInUp",void 0),n.R7$(2),n.JRh(e.planning.titre),n.R7$(1),n.Y8G("innerHTML",n.bMT(5,18,e.planning.description),n.npT),n.R7$(2),n.Y8G("@fadeInUp",e.sectionStates.info),n.R7$(11),n.JRh(n.i5U(18,20,e.planning.dateDebut,"mediumDate")),n.R7$(4),n.JRh(n.i5U(22,23,e.planning.dateFin,"mediumDate")),n.R7$(2),n.Y8G("ngIf",e.planning.lieu),n.R7$(1),n.Y8G("@fadeInUp",e.sectionStates.participants),n.R7$(6),n.Y8G("ngForOf",e.planning.participants),n.R7$(1),n.Y8G("@fadeInUp",e.sectionStates.reunions),n.R7$(29),n.JRh((null==e.planning.reunions?null:e.planning.reunions.length)||0),n.R7$(10),n.Lme(" ",n.i5U(71,26,e.planning.dateDebut,"shortDate")," - ",n.i5U(72,29,e.planning.dateFin,"shortDate")," "),n.R7$(12),n.JRh((null==e.planning.participants?null:e.planning.participants.length)||0),n.R7$(5),n.Y8G("viewDate",e.viewDate)("events",e.events),n.R7$(1),n.Y8G("ngIf",e.selectedDayEvents.length>0)}}let Y=(()=>{class i{constructor(e,r,t,a,l,g,h,nn){this.route=e,this.router=r,this.planningService=t,this.reunionService=a,this.authService=l,this.cdr=g,this.sanitizer=h,this.toastService=nn,this.planning=null,this.loading=!0,this.error=null,this.isCreator=!1,this.selectedDayEvents=[],this.selectedDate=null,this.cardState="default",this.view=m.St.Month,this.viewDate=new Date,this.events=[],this.sectionStates={info:!1,participants:!1,reunions:!1}}ngOnInit(){this.loadPlanningDetails(),setTimeout(()=>{this.sectionStates.info=!0},300),setTimeout(()=>{this.sectionStates.participants=!0},600),setTimeout(()=>{this.sectionStates.reunions=!0},900)}loadPlanningDetails(){const e=this.route.snapshot.paramMap.get("id");if(!e)return this.loading=!1,void this.toastService.error("Erreur de navigation","ID de planning non fourni");this.planningService.getPlanningById(e).subscribe({next:r=>{this.planning=r.planning,this.isCreator=r.planning.createur._id===this.authService.getCurrentUserId(),this.loading=!1,this.events=this.planning.reunions.map((t,a)=>{const l=`${t.date.substring(0,10)}T${t.heureDebut}:00`,g=`${t.date.substring(0,10)}T${t.heureFin}:00`,h=137*a%360;return{start:new Date(l),end:new Date(g),title:t.titre,allDay:!1,color:{primary:`hsl(${h}, 70%, 50%)`,secondary:`hsl(${h}, 70%, 90%)`},meta:{description:t.description||"",id:t._id}}}),this.cdr.detectChanges()},error:r=>{this.loading=!1,console.error("Erreur:",r),403===r.status?this.toastService.accessDenied("acc\xe9der \xe0 ce planning",r.status):404===r.status?this.toastService.error("Planning introuvable","Le planning demand\xe9 n'existe pas ou a \xe9t\xe9 supprim\xe9"):this.toastService.error("Erreur de chargement",r.error?.message||"Erreur lors du chargement du planning")}})}handleDayClick(e){this.selectedDate=e.date,this.selectedDayEvents=e.events,e.events.length>0&&setTimeout(()=>{const r=document.querySelector(".day-events");r&&r.scrollIntoView({behavior:"smooth",block:"nearest"})},100)}onCardMouseEnter(){this.cardState="hovered"}onCardMouseLeave(){this.cardState="default"}editPlanning(){this.planning&&this.router.navigate(["/plannings/edit",this.planning._id])}deletePlanning(){this.planning&&confirm("Supprimer d\xe9finitivement ce planning ?")&&this.planningService.deletePlanning(this.planning._id).subscribe({next:()=>{this.toastService.success("Planning supprim\xe9","Le planning a \xe9t\xe9 supprim\xe9 avec succ\xe8s"),this.router.navigate(["/plannings"])},error:e=>{console.error("Erreur lors de la suppression du planning:",e),403===e.status?this.toastService.accessDenied("supprimer ce planning",e.status):401===e.status?this.toastService.error("Non autoris\xe9","Vous devez \xeatre connect\xe9 pour supprimer un planning"):this.toastService.error("Erreur de suppression",e.error?.message||"Erreur lors de la suppression du planning",8e3)}})}nouvelleReunion(){this.planning&&this.router.navigate(["/reunions/nouvelleReunion"],{queryParams:{planningId:this.planning._id}})}editReunion(e){e&&this.router.navigate(["/reunions/modifier",e])}deleteReunion(e){confirm("\xcates-vous s\xfbr de vouloir supprimer cette r\xe9union ?")&&this.reunionService.deleteReunion(e).subscribe({next:r=>{console.log("R\xe9union supprim\xe9e avec succ\xe8s:",r),this.toastService.success("R\xe9union supprim\xe9e","La r\xe9union a \xe9t\xe9 supprim\xe9e avec succ\xe8s"),this.loadPlanningDetails(),this.selectedDayEvents=this.selectedDayEvents.filter(t=>t.meta?.id!==e)},error:r=>{console.error("Erreur lors de la suppression:",r),403===r.status?this.toastService.accessDenied("supprimer cette r\xe9union",r.status):401===r.status?this.toastService.error("Non autoris\xe9","Vous devez \xeatre connect\xe9 pour supprimer une r\xe9union"):this.toastService.error("Erreur de suppression",r.error?.message||"Erreur lors de la suppression de la r\xe9union",8e3)}})}formatDescription(e){const r=e.replace(/\(presence obligatoire\)/gi,'<span class="text-red-600 font-semibold">(presence obligatoire)</span>');return this.sanitizer.bypassSecurityTrustHtml(r)}static{this.\u0275fac=function(r){return new(r||i)(n.rXU(u.nX),n.rXU(u.Ix),n.rXU(f.z),n.rXU(j.C),n.rXU(x.V),n.rXU(n.gRc),n.rXU(y.up),n.rXU(v.f))}}static{this.\u0275cmp=n.VBU({type:i,selectors:[["app-planning-detail"]],decls:8,vars:3,consts:[[1,"container","mx-auto","px-4","py-6"],[1,"back-button","mb-4","flex","items-center",3,"click"],["xmlns","http://www.w3.org/2000/svg","viewBox","0 0 20 20","fill","currentColor",1,"h-5","w-5"],["fill-rule","evenodd","d","M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z","clip-rule","evenodd"],["class","text-center py-8",4,"ngIf"],["class","bg-red-100 border-l-4 border-red-500 text-red-700 p-4 rounded-lg shadow-md mb-6 animate__animated animate__fadeIn",4,"ngIf"],["class","planning-card",3,"mouseenter","mouseleave",4,"ngIf"],[1,"text-center","py-8"],[1,"loading-spinner"],[1,"text-purple-600","mt-3","font-medium"],[1,"bg-red-100","border-l-4","border-red-500","text-red-700","p-4","rounded-lg","shadow-md","mb-6","animate__animated","animate__fadeIn"],[1,"flex","items-center"],["fill","none","viewBox","0 0 24 24","stroke","currentColor",1,"h-6","w-6","text-red-500","mr-3"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"],[1,"planning-card",3,"mouseenter","mouseleave"],[1,"planning-header"],[1,"mb-2"],[1,"text-base",3,"innerHTML"],[1,"planning-section"],["fill","none","viewBox","0 0 24 24","stroke","currentColor",1,"h-5","w-5"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"],[1,"info-item"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"],["class","info-item",4,"ngIf"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"],[1,"participants-list"],["class","participant-badge",3,"animation-delay",4,"ngFor","ngForOf"],[1,"flex","justify-between","items-center","mb-4"],[1,"btn","btn-primary",3,"click"],["fill","none","viewBox","0 0 24 24","stroke","currentColor",1,"h-5","w-5","mr-1"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M12 6v6m0 0v6m0-6h6m-6 0H6"],[1,"flex","justify-end","space-x-3","mb-4"],[1,"btn","btn-secondary",3,"click"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"],[1,"btn","btn-danger",3,"click"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"],[1,"grid","grid-cols-1","md:grid-cols-3","gap-4","mb-6"],[1,"bg-gradient-to-br","from-purple-50","to-indigo-50","p-4","rounded-lg","shadow-sm"],[1,"flex","items-center","justify-between"],[1,"text-sm","text-gray-500"],[1,"text-2xl","font-bold","text-gray-800"],[1,"bg-purple-100","p-3","rounded-full"],["fill","none","viewBox","0 0 24 24","stroke","currentColor",1,"h-6","w-6","text-purple-600"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"],[1,"bg-gradient-to-br","from-blue-50","to-cyan-50","p-4","rounded-lg","shadow-sm"],[1,"text-lg","font-bold","text-gray-800"],[1,"bg-blue-100","p-3","rounded-full"],["fill","none","viewBox","0 0 24 24","stroke","currentColor",1,"h-6","w-6","text-blue-600"],[1,"bg-gradient-to-br","from-green-50","to-emerald-50","p-4","rounded-lg","shadow-sm"],[1,"bg-green-100","p-3","rounded-full"],["fill","none","viewBox","0 0 24 24","stroke","currentColor",1,"h-6","w-6","text-green-600"],[1,"calendar-container"],[3,"viewDate","events","dayClicked"],["class","day-events",4,"ngIf"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M15 11a3 3 0 11-6 0 3 3 0 016 0z"],[1,"participant-badge"],[1,"day-events"],["fill","none","viewBox","0 0 24 24","stroke","currentColor",1,"h-5","w-5","mr-2"],[1,"space-y-3"],["class","event-item bg-white p-4 rounded-lg shadow-sm border border-gray-100",3,"animation-delay",4,"ngFor","ngForOf"],[1,"event-item","bg-white","p-4","rounded-lg","shadow-sm","border","border-gray-100"],[1,"flex","justify-between","items-start"],[1,"flex-1"],[3,"innerHTML"],[1,"flex","items-center","text-gray-600","mt-1"],["fill","none","viewBox","0 0 24 24","stroke","currentColor",1,"h-4","w-4","mr-1"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"],["class","mt-2 text-sm text-gray-500",4,"ngIf"],[1,"flex","space-x-2","ml-4"],["title","Modifier la r\xe9union",1,"text-blue-500","hover:text-blue-700","transition-colors","duration-300","p-1","rounded-full","hover:bg-blue-50",3,"click"],["fill","none","viewBox","0 0 24 24","stroke","currentColor",1,"h-4","w-4"],["title","Supprimer la r\xe9union",1,"text-red-500","hover:text-red-700","transition-colors","duration-300","p-1","rounded-full","hover:bg-red-50",3,"click"],[1,"mt-2","text-sm","text-gray-500"]],template:function(r,t){1&r&&(n.j41(0,"div",0)(1,"button",1),n.bIt("click",function(){return t.router.navigate(["/plannings"])}),n.qSk(),n.j41(2,"svg",2),n.nrm(3,"path",3),n.k0s(),n.EFF(4," Retour aux plannings "),n.k0s(),n.DNE(5,E,4,0,"div",4),n.DNE(6,O,6,1,"div",5),n.DNE(7,$,89,32,"div",6),n.k0s()),2&r&&(n.R7$(5),n.Y8G("ngIf",t.loading),n.R7$(1),n.Y8G("ngIf",t.error),n.R7$(1),n.Y8G("ngIf",!t.loading&&t.planning))},dependencies:[p.Sq,p.bT,m.jD,p.vh,_.I],styles:['@keyframes _ngcontent-%COMP%_fadeInUp{0%{opacity:0;transform:translateY(20px)}to{opacity:1;transform:translateY(0)}}@keyframes _ngcontent-%COMP%_pulse{0%{box-shadow:0 0 #7c3aed66}70%{box-shadow:0 0 0 10px #7c3aed00}to{box-shadow:0 0 #7c3aed00}}@keyframes _ngcontent-%COMP%_rotate{0%{transform:rotate(0)}to{transform:rotate(360deg)}}.container[_ngcontent-%COMP%]{max-width:1200px;animation:_ngcontent-%COMP%_fadeInUp .5s ease-out}.planning-card[_ngcontent-%COMP%]{background:linear-gradient(135deg,#ffffff 0%,#f8f9fa 100%);border-radius:12px;box-shadow:0 10px 30px #00000014;overflow:hidden;transition:all .3s ease;position:relative}.planning-card[_ngcontent-%COMP%]:before{content:"";position:absolute;top:0;left:0;width:100%;height:5px;background:linear-gradient(90deg,#7c3aed,#4f46e5,#3b82f6)}.planning-header[_ngcontent-%COMP%]{padding:2rem;position:relative;overflow:hidden;background:linear-gradient(135deg,rgba(124,58,237,.05) 0%,rgba(79,70,229,.1) 100%);border-bottom:1px solid rgba(0,0,0,.05)}.planning-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:2rem;font-weight:700;color:#2d3748;margin-bottom:.5rem;position:relative;display:inline-block}.planning-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]:after{content:"";position:absolute;bottom:-5px;left:0;width:40px;height:3px;background:linear-gradient(90deg,#7c3aed,#4f46e5);transition:width .3s ease}.planning-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]:hover:after{width:100%}.planning-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#4a5568;font-size:1.1rem;line-height:1.6}.planning-section[_ngcontent-%COMP%]{padding:1.5rem 2rem;border-bottom:1px solid rgba(0,0,0,.05);animation:_ngcontent-%COMP%_fadeInUp .5s ease-out;animation-fill-mode:both}.planning-section[_ngcontent-%COMP%]:nth-child(2){animation-delay:.1s}.planning-section[_ngcontent-%COMP%]:nth-child(3){animation-delay:.2s}.planning-section[_ngcontent-%COMP%]:nth-child(4){animation-delay:.3s}.planning-section[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:1.25rem;font-weight:600;color:#2d3748;margin-bottom:1rem;display:flex;align-items:center}.planning-section[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%]{margin-right:.5rem;color:#7c3aed}.info-item[_ngcontent-%COMP%]{display:flex;align-items:center;margin-bottom:.75rem;padding:.5rem;border-radius:8px;transition:all .2s ease}.info-item[_ngcontent-%COMP%]:hover{background-color:#7c3aed0d}.info-item[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%]{color:#7c3aed;margin-right:.75rem;flex-shrink:0}.info-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{color:#4a5568;font-size:1rem}.info-item[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%]{color:#2d3748;font-weight:600}.participants-list[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;gap:.5rem}.participant-badge[_ngcontent-%COMP%]{display:flex;align-items:center;padding:.5rem 1rem;background:linear-gradient(135deg,#f9fafb 0%,#f3f4f6 100%);border:1px solid #e5e7eb;border-radius:9999px;transition:all .2s ease}.participant-badge[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 4px 6px -1px #0000001a;background:linear-gradient(135deg,#f3f4f6 0%,#e5e7eb 100%)}.participant-badge[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{color:#4b5563;font-weight:500}.btn[_ngcontent-%COMP%]{padding:.625rem 1.25rem;font-weight:500;border-radius:8px;transition:all .3s ease;position:relative;overflow:hidden;z-index:1}.btn[_ngcontent-%COMP%]:before{content:"";position:absolute;top:0;left:-100%;width:100%;height:100%;background:linear-gradient(90deg,transparent,rgba(255,255,255,.2),transparent);transition:all .5s ease;z-index:-1}.btn[_ngcontent-%COMP%]:hover:before{left:100%}.btn-primary[_ngcontent-%COMP%]{background:linear-gradient(135deg,#7c3aed 0%,#6d28d9 100%);color:#fff}.btn-primary[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,#6d28d9 0%,#5b21b6 100%);transform:translateY(-2px);box-shadow:0 4px 12px #6d28d94d}.btn-secondary[_ngcontent-%COMP%]{background:linear-gradient(135deg,#3b82f6 0%,#2563eb 100%);color:#fff}.btn-secondary[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,#2563eb 0%,#1d4ed8 100%);transform:translateY(-2px);box-shadow:0 4px 12px #2563eb4d}.btn-danger[_ngcontent-%COMP%]{background:linear-gradient(135deg,#ef4444 0%,#dc2626 100%);color:#fff}.btn-danger[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,#dc2626 0%,#b91c1c 100%);transform:translateY(-2px);box-shadow:0 4px 12px #dc26264d}.calendar-container[_ngcontent-%COMP%]{margin-top:1.5rem;border-radius:12px;overflow:hidden;box-shadow:0 4px 6px -1px #0000001a}.day-events[_ngcontent-%COMP%]{margin-top:1.5rem;padding:1.5rem;background:linear-gradient(135deg,#f9fafb 0%,#f3f4f6 100%);border-radius:12px;box-shadow:0 4px 6px -1px #0000000d;animation:_ngcontent-%COMP%_fadeInUp .4s ease-out}.day-events[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{color:#4b5563;font-weight:600;margin-bottom:1rem;padding-bottom:.5rem;border-bottom:2px solid rgba(124,58,237,.2)}.event-item[_ngcontent-%COMP%]{padding:1rem;margin-bottom:.75rem;background:white;border-radius:8px;border-left:4px solid #7c3aed;box-shadow:0 2px 4px #0000000d;transition:all .2s ease}.event-item[_ngcontent-%COMP%]:hover{transform:translate(5px);box-shadow:0 4px 8px #0000001a}.event-item[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%]{display:block;color:#2d3748;margin-bottom:.25rem}.event-item[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]{color:#6b7280;font-size:.875rem}.back-button[_ngcontent-%COMP%]{display:inline-flex;align-items:center;padding:.5rem 1rem;color:#7c3aed;font-weight:500;border-radius:8px;transition:all .2s ease}.back-button[_ngcontent-%COMP%]:hover{background-color:#7c3aed0d;color:#6d28d9;transform:translate(-5px)}.back-button[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%]{margin-right:.5rem}.loading-spinner[_ngcontent-%COMP%]{width:50px;height:50px;border:3px solid rgba(124,58,237,.1);border-radius:50%;border-top-color:#7c3aed;animation:_ngcontent-%COMP%_rotate 1s linear infinite;margin:2rem auto}'],data:{animation:[(0,o.hZ)("fadeInUp",[(0,o.kY)(":enter",[(0,o.iF)({opacity:0,transform:"translateY(20px)"}),(0,o.i0)("0.5s ease-out",(0,o.iF)({opacity:1,transform:"translateY(0)"}))])]),(0,o.hZ)("cardHover",[(0,o.wk)("default",(0,o.iF)({transform:"scale(1)",boxShadow:"0 4px 6px -1px rgba(0, 0, 0, 0.1)"})),(0,o.wk)("hovered",(0,o.iF)({transform:"scale(1.02)",boxShadow:"0 10px 15px -3px rgba(0, 0, 0, 0.1)"})),(0,o.kY)("default => hovered",[(0,o.i0)("0.2s ease-in-out")]),(0,o.kY)("hovered => default",[(0,o.i0)("0.2s ease-in-out")])])]}})}}return i})();var c=d(4341),V=d(8490);function G(i,s){if(1&i&&(n.j41(0,"div",49),n.EFF(1),n.k0s()),2&i){const e=n.XpG();n.R7$(1),n.SpI(" ",e.errorMessage," ")}}function L(i,s){1&i&&(n.j41(0,"span"),n.EFF(1,"Le titre est obligatoire"),n.k0s())}function z(i,s){1&i&&(n.j41(0,"span"),n.EFF(1,"Au moins 3 caract\xe8res requis"),n.k0s())}function N(i,s){if(1&i&&(n.j41(0,"div",50),n.nrm(1,"i",51),n.DNE(2,L,2,0,"span",52),n.DNE(3,z,2,0,"span",52),n.k0s()),2&i){const e=n.XpG();let r,t;n.R7$(2),n.Y8G("ngIf",null==(r=e.planningForm.get("titre"))||null==r.errors?null:r.errors.required),n.R7$(1),n.Y8G("ngIf",null==(t=e.planningForm.get("titre"))||null==t.errors?null:t.errors.minlength)}}function U(i,s){if(1&i&&(n.j41(0,"option",53),n.EFF(1),n.k0s()),2&i){const e=s.$implicit;n.Y8G("value",e._id),n.R7$(1),n.SpI(" ",e.username," ")}}function X(i,s){1&i&&(n.j41(0,"div",50),n.nrm(1,"i",51),n.EFF(2," Veuillez s\xe9lectionner au moins un participant "),n.k0s())}function B(i,s){1&i&&n.nrm(0,"i",54)}function q(i,s){1&i&&n.nrm(0,"i",55)}const J=[{path:"",component:M},{path:"nouveau",component:(()=>{class i{constructor(e,r,t,a,l){this.fb=e,this.userService=r,this.planningService=t,this.router=a,this.toastService=l,this.isLoading=!1,this.errorMessage=null,this.users$=this.userService.getAllUsers()}ngOnInit(){this.planningForm=this.fb.group({titre:["",[c.k0.required,c.k0.minLength(3)]],description:[""],lieu:[""],dateDebut:["",c.k0.required],dateFin:["",c.k0.required],participants:[[],c.k0.required]})}submit(){if(console.log("Submit method called"),console.log("Form valid:",this.planningForm.valid),console.log("Form values:",this.planningForm.value),this.planningForm.valid){this.isLoading=!0,this.errorMessage=null;const e=this.planningForm.value,r={titre:e.titre,description:e.description||"",dateDebut:e.dateDebut,dateFin:e.dateFin,lieu:e.lieu||"",participants:e.participants||[]};console.log("Planning data to submit:",r),this.planningService.createPlanning(r).subscribe({next:t=>{console.log("Planning created successfully:",t),this.isLoading=!1,this.toastService.showSuccess("Le planning a \xe9t\xe9 cr\xe9\xe9 avec succ\xe8s"),this.router.navigate(["/plannings"])},error:t=>{console.error("Error creating planning:",t),console.error("Error details:",t.error||t.message||t),this.isLoading=!1,403===t.status?this.toastService.showError("Acc\xe8s refus\xe9 : vous n'avez pas les droits pour cr\xe9er un planning"):401===t.status?this.toastService.showError("Vous devez \xeatre connect\xe9 pour cr\xe9er un planning"):this.toastService.showError(t.error?.message||"Une erreur est survenue lors de la cr\xe9ation du planning",8e3)}})}else console.log("Form validation errors:",this.getFormValidationErrors()),this.markFormGroupTouched(),this.toastService.showWarning("Veuillez corriger les erreurs avant de soumettre le formulaire")}getFormValidationErrors(){const e={};return Object.keys(this.planningForm.controls).forEach(r=>{const t=this.planningForm.get(r);t&&t.errors&&(e[r]=t.errors)}),e}markFormGroupTouched(){Object.keys(this.planningForm.controls).forEach(e=>{const r=this.planningForm.get(e);r&&r.markAsTouched()})}static{this.\u0275fac=function(r){return new(r||i)(n.rXU(c.ok),n.rXU(V.u),n.rXU(f.z),n.rXU(u.Ix),n.rXU(v.f))}}static{this.\u0275cmp=n.VBU({type:i,selectors:[["app-planning-form"]],decls:71,vars:13,consts:[[1,"container","mx-auto","px-4","py-6","max-w-3xl"],[1,"bg-gradient-to-r","from-purple-600","to-indigo-600","rounded-t-lg","p-6","text-white","mb-0"],[1,"text-2xl","font-bold","flex","items-center"],[1,"fas","fa-calendar-plus","mr-3","text-purple-200"],[1,"text-purple-100","mt-2"],["novalidate","",1,"bg-white","rounded-b-lg","shadow-lg","p-6","border-t-0",3,"formGroup","ngSubmit"],["class","mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded",4,"ngIf"],[1,"grid","grid-cols-1","gap-6"],[1,"bg-gradient-to-r","from-purple-50","to-pink-50","p-4","rounded-lg","border","border-purple-200"],[1,"text-lg","font-semibold","text-purple-800","mb-4","flex","items-center"],[1,"fas","fa-info-circle","mr-2","text-purple-600"],[1,"grid","grid-cols-1","md:grid-cols-2","gap-6"],[1,"block","text-sm","font-medium","text-purple-700","mb-2"],[1,"fas","fa-tag","mr-2","text-purple-500"],["type","text","formControlName","titre","placeholder","Nom de votre planning...",1,"mt-1","block","w-full","px-4","py-3","border-2","border-purple-200","rounded-lg","shadow-sm","focus:ring-purple-500","focus:border-purple-500","focus:ring-2","transition-all","duration-200"],["class","text-red-500 text-sm mt-2 flex items-center",4,"ngIf"],[1,"block","text-sm","font-medium","text-orange-700","mb-2"],[1,"fas","fa-map-marker-alt","mr-2","text-orange-500"],["type","text","formControlName","lieu","placeholder","Salle, bureau, lieu de l'\xe9v\xe9nement...",1,"mt-1","block","w-full","px-4","py-3","border-2","border-orange-200","rounded-lg","shadow-sm","focus:ring-orange-500","focus:border-orange-500","focus:ring-2","transition-all","duration-200"],[1,"bg-gradient-to-r","from-blue-50","to-cyan-50","p-4","rounded-lg","border","border-blue-200"],[1,"text-lg","font-semibold","text-blue-800","mb-4","flex","items-center"],[1,"fas","fa-calendar-week","mr-2","text-blue-600"],[1,"block","text-sm","font-medium","text-green-700","mb-2"],[1,"fas","fa-calendar-day","mr-2","text-green-500"],["type","date","formControlName","dateDebut",1,"mt-1","block","w-full","px-4","py-3","border-2","border-green-200","rounded-lg","shadow-sm","focus:ring-green-500","focus:border-green-500","focus:ring-2","transition-all","duration-200"],[1,"block","text-sm","font-medium","text-red-700","mb-2"],[1,"fas","fa-calendar-check","mr-2","text-red-500"],["type","date","formControlName","dateFin",1,"mt-1","block","w-full","px-4","py-3","border-2","border-red-200","rounded-lg","shadow-sm","focus:ring-red-500","focus:border-red-500","focus:ring-2","transition-all","duration-200"],[1,"bg-gradient-to-r","from-emerald-50","to-teal-50","p-4","rounded-lg","border","border-emerald-200"],[1,"text-lg","font-semibold","text-emerald-800","mb-4","flex","items-center"],[1,"fas","fa-users","mr-2","text-emerald-600"],[1,"block","text-sm","font-medium","text-emerald-700","mb-2"],[1,"fas","fa-user-friends","mr-2","text-emerald-500"],["formControlName","participants","multiple","",1,"mt-1","block","w-full","px-4","py-3","border-2","border-emerald-200","rounded-lg","shadow-sm","focus:ring-emerald-500","focus:border-emerald-500","focus:ring-2","transition-all","duration-200","text-sm","min-h-[120px]"],["class","py-2",3,"value",4,"ngFor","ngForOf"],[1,"text-xs","text-emerald-600","mt-2"],[1,"fas","fa-info-circle","mr-1"],[1,"bg-gradient-to-r","from-indigo-50","to-purple-50","p-4","rounded-lg","border","border-indigo-200"],[1,"text-lg","font-semibold","text-indigo-800","mb-4","flex","items-center"],[1,"fas","fa-align-left","mr-2","text-indigo-600"],[1,"block","text-sm","font-medium","text-indigo-700","mb-2"],[1,"fas","fa-edit","mr-2","text-indigo-500"],["formControlName","description","rows","4","placeholder","D\xe9crivez les objectifs, le contexte ou les d\xe9tails de ce planning...",1,"mt-1","block","w-full","px-4","py-3","border-2","border-indigo-200","rounded-lg","shadow-sm","focus:ring-indigo-500","focus:border-indigo-500","focus:ring-2","transition-all","duration-200"],[1,"mt-8","flex","justify-end","space-x-4","bg-gray-50","p-4","rounded-lg","border-t","border-gray-200"],["type","button","routerLink","/plannings",1,"px-6","py-3","border-2","border-gray-300","rounded-lg","text-sm","font-medium","text-gray-700","hover:bg-gray-100","hover:border-gray-400","transition-all","duration-200","flex","items-center"],[1,"fas","fa-times","mr-2"],["type","button",1,"px-6","py-3","rounded-lg","text-sm","font-medium","text-white","bg-gradient-to-r","from-purple-600","to-indigo-600","hover:from-purple-700","hover:to-indigo-700","disabled:opacity-50","disabled:cursor-not-allowed","transition-all","duration-200","flex","items-center","shadow-lg",3,"disabled","click"],["class","fas fa-save mr-2",4,"ngIf"],["class","fas fa-spinner fa-spin mr-2",4,"ngIf"],[1,"mb-4","bg-red-100","border","border-red-400","text-red-700","px-4","py-3","rounded"],[1,"text-red-500","text-sm","mt-2","flex","items-center"],[1,"fas","fa-exclamation-circle","mr-1"],[4,"ngIf"],[1,"py-2",3,"value"],[1,"fas","fa-save","mr-2"],[1,"fas","fa-spinner","fa-spin","mr-2"]],template:function(r,t){if(1&r&&(n.j41(0,"div",0)(1,"div",1)(2,"h1",2),n.nrm(3,"i",3),n.EFF(4," Nouveau Planning "),n.k0s(),n.j41(5,"p",4),n.EFF(6,"Cr\xe9ez un nouveau planning pour organiser vos \xe9v\xe9nements"),n.k0s()(),n.j41(7,"form",5),n.bIt("ngSubmit",function(){return t.submit()}),n.DNE(8,G,2,1,"div",6),n.j41(9,"div",7)(10,"div",8)(11,"h3",9),n.nrm(12,"i",10),n.EFF(13," Informations g\xe9n\xe9rales "),n.k0s(),n.j41(14,"div",11)(15,"div")(16,"label",12),n.nrm(17,"i",13),n.EFF(18," Titre * "),n.k0s(),n.nrm(19,"input",14),n.DNE(20,N,4,2,"div",15),n.k0s(),n.j41(21,"div")(22,"label",16),n.nrm(23,"i",17),n.EFF(24," Lieu / Salle "),n.k0s(),n.nrm(25,"input",18),n.k0s()()(),n.j41(26,"div",19)(27,"h3",20),n.nrm(28,"i",21),n.EFF(29," P\xe9riode du planning "),n.k0s(),n.j41(30,"div",11)(31,"div")(32,"label",22),n.nrm(33,"i",23),n.EFF(34," Date de d\xe9but * "),n.k0s(),n.nrm(35,"input",24),n.k0s(),n.j41(36,"div")(37,"label",25),n.nrm(38,"i",26),n.EFF(39," Date de fin * "),n.k0s(),n.nrm(40,"input",27),n.k0s()()(),n.j41(41,"div",28)(42,"h3",29),n.nrm(43,"i",30),n.EFF(44," Participants "),n.k0s(),n.j41(45,"label",31),n.nrm(46,"i",32),n.EFF(47," S\xe9lectionnez les participants * "),n.k0s(),n.j41(48,"select",33),n.DNE(49,U,2,2,"option",34),n.nI1(50,"async"),n.k0s(),n.DNE(51,X,3,0,"div",15),n.j41(52,"p",35),n.nrm(53,"i",36),n.EFF(54," Maintenez Ctrl (ou Cmd) pour s\xe9lectionner plusieurs participants "),n.k0s()(),n.j41(55,"div",37)(56,"h3",38),n.nrm(57,"i",39),n.EFF(58," Description "),n.k0s(),n.j41(59,"label",40),n.nrm(60,"i",41),n.EFF(61," D\xe9crivez votre planning "),n.k0s(),n.nrm(62,"textarea",42),n.k0s()(),n.j41(63,"div",43)(64,"button",44),n.nrm(65,"i",45),n.EFF(66," Annuler "),n.k0s(),n.j41(67,"button",46),n.bIt("click",function(){return t.submit()}),n.DNE(68,B,1,0,"i",47),n.DNE(69,q,1,0,"i",48),n.EFF(70),n.k0s()()()()),2&r){let a,l,g;n.R7$(7),n.Y8G("formGroup",t.planningForm),n.R7$(1),n.Y8G("ngIf",t.errorMessage),n.R7$(11),n.AVh("border-red-300",(null==(a=t.planningForm.get("titre"))?null:a.invalid)&&(null==(a=t.planningForm.get("titre"))?null:a.touched)),n.R7$(1),n.Y8G("ngIf",(null==(l=t.planningForm.get("titre"))?null:l.invalid)&&(null==(l=t.planningForm.get("titre"))?null:l.touched)),n.R7$(29),n.Y8G("ngForOf",n.bMT(50,11,t.users$)),n.R7$(2),n.Y8G("ngIf",(null==(g=t.planningForm.get("participants"))?null:g.invalid)&&(null==(g=t.planningForm.get("participants"))?null:g.touched)),n.R7$(16),n.Y8G("disabled",t.isLoading||t.planningForm.invalid),n.R7$(1),n.Y8G("ngIf",!t.isLoading),n.R7$(1),n.Y8G("ngIf",t.isLoading),n.R7$(1),n.SpI(" ",t.isLoading?"Enregistrement...":"Cr\xe9er le planning"," ")}},dependencies:[p.Sq,p.bT,u.Wk,c.qT,c.xH,c.y7,c.me,c.W0,c.BC,c.cb,c.j4,c.JD,p.Jj]})}}return i})()},{path:"edit/:id",component:d(652).v},{path:":id",component:Y}];let Z=(()=>{class i{static{this.\u0275fac=function(r){return new(r||i)}}static{this.\u0275mod=n.$C({type:i})}static{this.\u0275inj=n.G2t({imports:[u.iI.forChild(J),u.iI]})}}return i})();var W=d(1163),K=d(1683);let Q=(()=>{class i{static{this.\u0275fac=function(r){return new(r||i)}}static{this.\u0275mod=n.$C({type:i})}static{this.\u0275inj=n.G2t({imports:[p.MD,Z,c.YN,c.X1,m.rO.forRoot({provide:m.MJ,useFactory:W.a}),K.Y]})}}return i})()}}]);