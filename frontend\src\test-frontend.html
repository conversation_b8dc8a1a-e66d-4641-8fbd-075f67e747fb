<!DOCTYPE html>
<html>
<head>
    <title>Test Frontend DevBridge</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: linear-gradient(135deg, #1a1a2e, #16213e);
            color: white;
            margin: 0;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .test-card {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            border: 1px solid #00ff88;
            backdrop-filter: blur(10px);
        }
        .success {
            border-color: #00ff88;
            background: rgba(0,255,136,0.1);
        }
        .error {
            border-color: #ff4757;
            background: rgba(255,71,87,0.1);
        }
        .warning {
            border-color: #ffa502;
            background: rgba(255,165,2,0.1);
        }
        button {
            background: linear-gradient(135deg, #00ff88, #00cc66);
            color: #1a1a2e;
            border: none;
            padding: 12px 24px;
            margin: 10px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: bold;
            font-size: 14px;
            transition: all 0.3s;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,255,136,0.3);
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }
        .icon {
            font-size: 24px;
            margin-right: 10px;
        }
        .test-result {
            display: flex;
            align-items: center;
            margin: 10px 0;
        }
        .loading {
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test Frontend DevBridge</h1>
        <p>Tests de fonctionnement de l'application</p>

        <!-- Test de base -->
        <div class="test-card success">
            <h2>✅ Tests de base</h2>
            <div class="test-result">
                <span class="icon">🌐</span>
                <span>Page chargée avec succès</span>
            </div>
            <div class="test-result">
                <span class="icon">🎨</span>
                <span>Styles CSS appliqués</span>
            </div>
            <div class="test-result">
                <span class="icon">📱</span>
                <span>Interface responsive</span>
            </div>
        </div>

        <!-- Test de connexion -->
        <div class="test-card" id="connectionTest">
            <h2>🔗 Test de connexion</h2>
            <div id="connectionStatus" class="status loading">
                <span class="icon">⏳</span>
                Test de connexion en cours...
            </div>
            <button onclick="testConnection()">🔄 Retester la connexion</button>
        </div>

        <!-- Test WebRTC -->
        <div class="test-card" id="webrtcTest">
            <h2>📹 Test WebRTC</h2>
            <div id="webrtcStatus" class="status">
                <span class="icon">📊</span>
                Prêt à tester WebRTC
            </div>
            <button onclick="testWebRTC()">🎥 Tester WebRTC</button>
            <button onclick="testAudio()">🔊 Tester Audio</button>
        </div>

        <!-- Test de navigation -->
        <div class="test-card">
            <h2>🧭 Navigation</h2>
            <div class="test-result">
                <span class="icon">🏠</span>
                <button onclick="goToHome()">Accueil</button>
            </div>
            <div class="test-result">
                <span class="icon">💬</span>
                <button onclick="goToMessages()">Messages</button>
            </div>
            <div class="test-result">
                <span class="icon">👥</span>
                <button onclick="goToConversations()">Conversations</button>
            </div>
        </div>

        <!-- Test des fonctionnalités -->
        <div class="test-card">
            <h2>⚡ Fonctionnalités</h2>
            <div class="test-result">
                <span class="icon">🔊</span>
                <button onclick="testAudioTransmission()">Test transmission audio</button>
            </div>
            <div class="test-result">
                <span class="icon">📞</span>
                <button onclick="testCallSystem()">Test système d'appel</button>
            </div>
            <div class="test-result">
                <span class="icon">🔄</span>
                <button onclick="testReload()">Test rechargement</button>
            </div>
        </div>

        <!-- Informations système -->
        <div class="test-card">
            <h2>💻 Informations système</h2>
            <div class="test-result">
                <span class="icon">🌐</span>
                <span>Navigateur: <span id="browserInfo">-</span></span>
            </div>
            <div class="test-result">
                <span class="icon">📱</span>
                <span>Plateforme: <span id="platformInfo">-</span></span>
            </div>
            <div class="test-result">
                <span class="icon">🔊</span>
                <span>WebRTC: <span id="webrtcSupport">-</span></span>
            </div>
            <div class="test-result">
                <span class="icon">🎤</span>
                <span>MediaDevices: <span id="mediaSupport">-</span></span>
            </div>
        </div>
    </div>

    <script>
        // Initialisation
        window.onload = function() {
            updateSystemInfo();
            testConnection();
        };

        function updateSystemInfo() {
            // Informations navigateur
            document.getElementById('browserInfo').textContent = navigator.userAgent.split(' ').pop();
            document.getElementById('platformInfo').textContent = navigator.platform;
            
            // Support WebRTC
            const hasWebRTC = !!(window.RTCPeerConnection && navigator.mediaDevices);
            document.getElementById('webrtcSupport').textContent = hasWebRTC ? '✅ Supporté' : '❌ Non supporté';
            
            // Support MediaDevices
            const hasMedia = !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia);
            document.getElementById('mediaSupport').textContent = hasMedia ? '✅ Supporté' : '❌ Non supporté';
        }

        function testConnection() {
            const statusEl = document.getElementById('connectionStatus');
            const cardEl = document.getElementById('connectionTest');
            
            statusEl.innerHTML = '<span class="icon">⏳</span>Test de connexion...';
            statusEl.className = 'status loading';
            cardEl.className = 'test-card';
            
            // Test de connexion au backend
            fetch('http://localhost:3000/graphql', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    query: '{ __typename }'
                })
            })
            .then(response => {
                if (response.ok) {
                    statusEl.innerHTML = '<span class="icon">✅</span>Connexion au backend réussie';
                    statusEl.className = 'status';
                    cardEl.className = 'test-card success';
                } else {
                    throw new Error('Réponse non OK');
                }
            })
            .catch(error => {
                statusEl.innerHTML = '<span class="icon">❌</span>Erreur de connexion au backend';
                statusEl.className = 'status';
                cardEl.className = 'test-card error';
                console.error('Erreur connexion:', error);
            });
        }

        function testWebRTC() {
            const statusEl = document.getElementById('webrtcStatus');
            const cardEl = document.getElementById('webrtcTest');
            
            if (!window.RTCPeerConnection) {
                statusEl.innerHTML = '<span class="icon">❌</span>WebRTC non supporté';
                cardEl.className = 'test-card error';
                return;
            }
            
            try {
                const pc = new RTCPeerConnection({
                    iceServers: [{ urls: 'stun:stun.l.google.com:19302' }]
                });
                
                statusEl.innerHTML = '<span class="icon">✅</span>WebRTC fonctionnel';
                cardEl.className = 'test-card success';
                
                pc.close();
            } catch (error) {
                statusEl.innerHTML = '<span class="icon">❌</span>Erreur WebRTC: ' + error.message;
                cardEl.className = 'test-card error';
            }
        }

        function testAudio() {
            const statusEl = document.getElementById('webrtcStatus');
            
            if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
                statusEl.innerHTML = '<span class="icon">❌</span>Accès microphone non supporté';
                return;
            }
            
            statusEl.innerHTML = '<span class="icon">⏳</span>Test d\'accès au microphone...';
            
            navigator.mediaDevices.getUserMedia({ audio: true })
                .then(stream => {
                    statusEl.innerHTML = '<span class="icon">✅</span>Accès microphone autorisé';
                    stream.getTracks().forEach(track => track.stop());
                })
                .catch(error => {
                    statusEl.innerHTML = '<span class="icon">❌</span>Accès microphone refusé';
                    console.error('Erreur microphone:', error);
                });
        }

        // Navigation
        function goToHome() {
            window.location.href = 'http://localhost:4200/';
        }

        function goToMessages() {
            window.location.href = 'http://localhost:4200/front/messages';
        }

        function goToConversations() {
            window.location.href = 'http://localhost:4200/front/messages/conversations';
        }

        // Tests fonctionnalités
        function testAudioTransmission() {
            window.open('http://localhost:4200/test-audio-transmission.html', '_blank');
        }

        function testCallSystem() {
            alert('Ouvrez deux onglets avec des comptes différents et testez les appels');
            goToMessages();
        }

        function testReload() {
            location.reload();
        }
    </script>
</body>
</html>
