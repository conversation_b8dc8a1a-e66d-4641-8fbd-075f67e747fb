"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[76],{4704:(_,l,r)=>{r.d(l,{E:()=>p});var n=r(5312),c=r(7705);let p=(()=>{class a{constructor(){}getDownloadUrl(s){if(!s)return"";let i=s;if(s.includes("C:")||s.includes("/")||s.includes("\\")){const o=s.split(/[\/\\]/);i=o[o.length-1]}return`${n.c.urlBackend}projets/telecharger/${i}`}getFileName(s){if(!s)return"fichier";if(s.includes("/")||s.includes("\\")){const i=s.split(/[\/\\]/);return i[i.length-1]}return s}static{this.\u0275fac=function(i){return new(i||a)}}static{this.\u0275prov=c.jDH({token:a,factory:a.\u0275fac,providedIn:"root"})}}return a})()},1873:(_,l,r)=>{r.d(l,{e:()=>i});var n=r(1626),c=r(8810),p=r(8141),a=r(9437),d=r(5312),s=r(7705);let i=(()=>{class o{constructor(t){this.http=t,this.apiUrl=`${d.c.urlBackend}projets`}getHeaders(){const t=localStorage.getItem("token");return new n.Lr({Authorization:`Bearer ${t}`,"Content-Type":"application/json"})}getProjets(){return console.log("Appel API pour r\xe9cup\xe9rer les projets:",this.apiUrl),this.http.get(this.apiUrl,{headers:this.getHeaders()}).pipe((0,p.M)(t=>console.log("Projets r\xe9cup\xe9r\xe9s:",t)),(0,a.W)(t=>(console.error("Erreur lors de la r\xe9cup\xe9ration des projets:",t),(0,c.$)(()=>t))))}getProjetById(t){return this.http.get(`${this.apiUrl}/${t}`,{headers:this.getHeaders()}).pipe((0,a.W)(u=>(0,c.$)(()=>u)))}addProjet(t){const u=new n.Lr({Authorization:`Bearer ${localStorage.getItem("token")}`});return this.http.post(`${this.apiUrl}/create`,t,{headers:u}).pipe((0,p.M)(h=>console.log("Projet ajout\xe9:",h)),(0,a.W)(h=>(console.error("Erreur lors de l'ajout du projet:",h),(0,c.$)(()=>h))))}updateProjet(t,u){return this.http.put(`${this.apiUrl}/update/${t}`,u,{headers:this.getHeaders()}).pipe((0,a.W)(h=>(0,c.$)(()=>h)))}deleteProjet(t){return this.http.delete(`${this.apiUrl}/delete/${t}`,{headers:this.getHeaders()}).pipe((0,p.M)(u=>console.log("Projet supprim\xe9:",u)),(0,a.W)(u=>(console.error("Erreur lors de la suppression du projet:",u),(0,c.$)(()=>u))))}uploadFile(t){const u=new FormData;u.append("file",t);const h=new n.Lr({Authorization:`Bearer ${localStorage.getItem("token")}`});return this.http.post(`${this.apiUrl}/uploads`,u,{headers:h}).pipe((0,a.W)(g=>(0,c.$)(()=>g)))}static{this.\u0275fac=function(u){return new(u||o)(s.KVO(n.Qq))}}static{this.\u0275prov=s.jDH({token:o,factory:o.\u0275fac,providedIn:"root"})}}return o})()},7169:(_,l,r)=>{r.d(l,{R:()=>s});var n=r(1626),c=r(7673),p=r(9437),a=r(5312),d=r(7705);let s=(()=>{class i{constructor(e){this.http=e,this.apiUrl=`${a.c.urlBackend}rendus`}getHeaders(){const e=localStorage.getItem("token");return new n.Lr({Authorization:`Bearer ${e}`,"Content-Type":"application/json"})}submitRendu(e){return this.http.post(`${this.apiUrl}/submit`,e)}checkRenduExists(e,t){return this.http.get(`${this.apiUrl}/check/${e}/${t}`,{headers:this.getHeaders()})}getAllRendus(){return this.http.get(this.apiUrl,{headers:this.getHeaders()}).pipe((0,p.W)(e=>(console.error("Erreur lors de la r\xe9cup\xe9ration des rendus:",e),(0,c.of)([]))))}getRenduById(e){return this.http.get(`${this.apiUrl}/${e}`,{headers:this.getHeaders()})}getRendusByProjet(e){return this.http.get(`${this.apiUrl}/projet/${e}`,{headers:this.getHeaders()}).pipe((0,p.W)(t=>(console.error("Erreur lors de la r\xe9cup\xe9ration des rendus par projet:",t),(0,c.of)([]))))}evaluateRendu(e,t){return this.http.post(`${this.apiUrl}/evaluations/${e}`,t,{headers:this.getHeaders()})}updateEvaluation(e,t){return this.http.put(`${this.apiUrl}/evaluations/${e}`,t,{headers:this.getHeaders()})}static{this.\u0275fac=function(t){return new(t||i)(d.KVO(n.Qq))}}static{this.\u0275prov=d.jDH({token:i,factory:i.\u0275fac,providedIn:"root"})}}return i})()},8076:(_,l,r)=>{r.d(l,{Z:()=>d});var n=r(7705),c=r(7455),p=r(6647),a=r(4798);let d=(()=>{class s{constructor(o,e,t){this.MessageService=o,this.route=e,this.logger=t,this.subscriptions=[],this.context="messages"}ngOnInit(){this.context=this.route.snapshot.data.context||"messages","messages"===this.context&&this.subscriptions.push(this.MessageService.activeConversation$.subscribe(o=>{o&&(this.conversationId=o,this.subscriptions.forEach(e=>e.unsubscribe()),this.subscriptions=[],this.subscriptions.push(this.MessageService.subscribeToNewMessages(o).subscribe({next:e=>{},error:e=>this.logger.error("MessageLayout","Error in message subscription",e)})))}))}ngOnDestroy(){this.subscriptions.forEach(o=>o.unsubscribe())}static{this.\u0275fac=function(e){return new(e||s)(n.rXU(c.b),n.rXU(p.nX),n.rXU(a.g))}}static{this.\u0275cmp=n.VBU({type:s,selectors:[["app-message-layout"]],decls:3,vars:0,consts:[[1,"layout-container"],[1,"main-content"]],template:function(e,t){1&e&&(n.j41(0,"div",0)(1,"div",1),n.nrm(2,"router-outlet"),n.k0s()())},dependencies:[p.n3],styles:[".layout-container[_ngcontent-%COMP%]{display:flex;height:100vh;width:100%;overflow:hidden}.main-content[_ngcontent-%COMP%]{flex:1;width:100%;height:100%;overflow:hidden}"]})}}return s})()}}]);