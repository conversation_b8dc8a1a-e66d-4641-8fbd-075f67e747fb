{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/OneDrive/Bureau/Project PI/devBridge/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { BehaviorSubject, throwError, from } from 'rxjs';\nimport { map, catchError, switchMap } from 'rxjs/operators';\nimport { CallType, CallStatus } from '../models/message.model';\nimport { INITIATE_CALL_MUTATION, ACCEPT_CALL_MUTATION, REJECT_CALL_MUTATION, END_CALL_MUTATION, TOGGLE_CALL_MEDIA_MUTATION, INCOMING_CALL_SUBSCRIPTION, CALL_STATUS_CHANGED_SUBSCRIPTION } from '../graphql/message.graphql';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"apollo-angular\";\nimport * as i2 from \"./logger.service\";\nexport class CallService {\n  constructor(apollo, logger) {\n    this.apollo = apollo;\n    this.logger = logger;\n    // État des appels\n    this.activeCall = new BehaviorSubject(null);\n    this.incomingCall = new BehaviorSubject(null);\n    // Observables publics\n    this.activeCall$ = this.activeCall.asObservable();\n    this.incomingCall$ = this.incomingCall.asObservable();\n    // Propriétés pour la gestion des sons\n    this.sounds = {};\n    this.isPlaying = {};\n    this.muted = false;\n    // États simples pour les médias\n    this.isVideoEnabled = true;\n    this.isAudioEnabled = true;\n    // WebRTC\n    this.peerConnection = null;\n    this.localStream = null;\n    this.remoteStream = null;\n    this.localVideoElement = null;\n    this.remoteVideoElement = null;\n    this.preloadSounds();\n    this.initializeSubscriptions();\n    this.initializeWebRTC();\n  }\n  /**\n   * Initialise les subscriptions avec un délai pour s'assurer que l'authentification est prête\n   */\n  initializeSubscriptions() {\n    // Attendre un peu pour s'assurer que l'authentification est prête\n    setTimeout(() => {\n      this.subscribeToIncomingCalls();\n      this.subscribeToCallStatusChanges();\n    }, 1000);\n    // Réessayer UNE SEULE FOIS après 10 secondes si nécessaire\n    setTimeout(() => {\n      if (!this.incomingCall.value) {\n        // console.log('🔄 [CallService] Retrying subscription initialization...');\n        this.subscribeToIncomingCalls();\n        this.subscribeToCallStatusChanges();\n      }\n    }, 10000);\n  }\n  /**\n   * Initialise WebRTC\n   */\n  initializeWebRTC() {\n    console.log('🔧 [CallService] Initializing WebRTC...');\n    // Configuration des serveurs STUN/TURN\n    const configuration = {\n      iceServers: [{\n        urls: 'stun:stun.l.google.com:19302'\n      }, {\n        urls: 'stun:stun1.l.google.com:19302'\n      }]\n    };\n    try {\n      this.peerConnection = new RTCPeerConnection(configuration);\n      this.setupPeerConnectionEvents();\n      console.log('✅ [CallService] WebRTC initialized successfully');\n    } catch (error) {\n      console.error('❌ [CallService] Failed to initialize WebRTC:', error);\n    }\n  }\n  /**\n   * Configure les événements de la PeerConnection\n   */\n  setupPeerConnectionEvents() {\n    if (!this.peerConnection) return;\n    // Quand on reçoit un stream distant\n    this.peerConnection.ontrack = event => {\n      console.log('📺 [CallService] Remote stream received');\n      this.remoteStream = event.streams[0];\n      if (this.remoteVideoElement) {\n        this.remoteVideoElement.srcObject = this.remoteStream;\n      }\n    };\n    // Gestion des candidats ICE\n    this.peerConnection.onicecandidate = event => {\n      if (event.candidate) {\n        console.log('🧊 [CallService] ICE candidate generated');\n        // TODO: Envoyer le candidat via WebSocket\n      }\n    };\n    // État de la connexion\n    this.peerConnection.onconnectionstatechange = () => {\n      console.log('🔗 [CallService] Connection state:', this.peerConnection?.connectionState);\n    };\n  }\n  /**\n   * Précharge les sons utilisés dans l'application\n   */\n  preloadSounds() {\n    // Créer des sons synthétiques de haute qualité\n    this.createSyntheticSounds();\n    // Charger le son de notification qui existe encore\n    this.loadSound('notification', 'assets/sounds/notification.mp3');\n    console.log('🎵 [CallService] Beautiful synthetic melodies created for calls');\n  }\n  /**\n   * Crée des sons synthétiques comme fallback\n   */\n  createSyntheticSounds() {\n    try {\n      // Créer un contexte audio pour les sons synthétiques\n      const audioContext = new (window.AudioContext || window.webkitAudioContext)();\n      // Son de sonnerie (mélodie agréable)\n      this.sounds['ringtone-synthetic'] = this.createRingtoneSound(audioContext);\n      // Son de connexion (accord agréable)\n      this.sounds['call-connected-synthetic'] = this.createConnectedSound(audioContext);\n      // Son de fin d'appel (ton descendant)\n      this.sounds['call-end-synthetic'] = this.createEndSound(audioContext);\n      // Son de notification (bip agréable)\n      this.sounds['notification-synthetic'] = this.createNotificationSound(audioContext);\n      console.log('🔊 [CallService] Synthetic sounds created as fallback');\n    } catch (error) {\n      console.warn('⚠️ [CallService] Could not create synthetic sounds:', error);\n    }\n  }\n  /**\n   * Crée une sonnerie agréable (mélodie)\n   */\n  createRingtoneSound(audioContext) {\n    const audio = new Audio();\n    audio.volume = 0.5;\n    let isPlaying = false;\n    let timeoutIds = [];\n    audio.playSynthetic = () => {\n      if (isPlaying) return Promise.resolve();\n      isPlaying = true;\n      const playMelody = () => {\n        if (!isPlaying) return;\n        // Mélodie inspirée de Nokia mais plus moderne : Mi-Ré-Fa#-Sol-Do#-Si-Ré-Do\n        const melody = [{\n          freq: 659.25,\n          duration: 0.125\n        }, {\n          freq: 587.33,\n          duration: 0.125\n        }, {\n          freq: 739.99,\n          duration: 0.25\n        }, {\n          freq: 783.99,\n          duration: 0.25\n        }, {\n          freq: 554.37,\n          duration: 0.125\n        }, {\n          freq: 493.88,\n          duration: 0.125\n        }, {\n          freq: 587.33,\n          duration: 0.25\n        }, {\n          freq: 523.25,\n          duration: 0.25\n        } // Do\n        ];\n\n        let currentTime = audioContext.currentTime;\n        melody.forEach((note, index) => {\n          if (!isPlaying) return;\n          const oscillator = audioContext.createOscillator();\n          const gainNode = audioContext.createGain();\n          oscillator.connect(gainNode);\n          gainNode.connect(audioContext.destination);\n          oscillator.frequency.value = note.freq;\n          oscillator.type = 'square'; // Son plus moderne\n          // Enveloppe ADSR pour un son plus naturel\n          gainNode.gain.setValueAtTime(0, currentTime);\n          gainNode.gain.linearRampToValueAtTime(0.3, currentTime + 0.02);\n          gainNode.gain.linearRampToValueAtTime(0.2, currentTime + note.duration * 0.7);\n          gainNode.gain.exponentialRampToValueAtTime(0.01, currentTime + note.duration);\n          oscillator.start(currentTime);\n          oscillator.stop(currentTime + note.duration);\n          currentTime += note.duration + 0.05; // Petite pause entre les notes\n        });\n        // Répéter la mélodie après une pause\n        const timeoutId = setTimeout(() => {\n          if (isPlaying) {\n            playMelody();\n          }\n        }, (currentTime - audioContext.currentTime + 0.8) * 1000);\n        timeoutIds.push(timeoutId);\n      };\n      playMelody();\n      return Promise.resolve();\n    };\n    audio.stopSynthetic = () => {\n      isPlaying = false;\n      timeoutIds.forEach(id => clearTimeout(id));\n      timeoutIds = [];\n    };\n    return audio;\n  }\n  /**\n   * Crée un son de connexion agréable (mélodie ascendante)\n   */\n  createConnectedSound(audioContext) {\n    const audio = new Audio();\n    audio.volume = 0.5;\n    audio.playSynthetic = () => {\n      // Mélodie ascendante positive : Do-Mi-Sol-Do (octave supérieure)\n      const melody = [{\n        freq: 523.25,\n        duration: 0.15\n      }, {\n        freq: 659.25,\n        duration: 0.15\n      }, {\n        freq: 783.99,\n        duration: 0.15\n      }, {\n        freq: 1046.5,\n        duration: 0.4\n      } // Do (octave supérieure)\n      ];\n\n      let currentTime = audioContext.currentTime;\n      melody.forEach((note, index) => {\n        const oscillator = audioContext.createOscillator();\n        const gainNode = audioContext.createGain();\n        oscillator.connect(gainNode);\n        gainNode.connect(audioContext.destination);\n        oscillator.frequency.value = note.freq;\n        oscillator.type = 'triangle'; // Son plus doux\n        // Enveloppe pour un son naturel\n        gainNode.gain.setValueAtTime(0, currentTime);\n        gainNode.gain.linearRampToValueAtTime(0.25, currentTime + 0.02);\n        gainNode.gain.linearRampToValueAtTime(0.15, currentTime + note.duration * 0.8);\n        gainNode.gain.exponentialRampToValueAtTime(0.01, currentTime + note.duration);\n        oscillator.start(currentTime);\n        oscillator.stop(currentTime + note.duration);\n        currentTime += note.duration * 0.8; // Chevauchement léger des notes\n      });\n\n      return Promise.resolve();\n    };\n    return audio;\n  }\n  /**\n   * Crée un son de fin d'appel (mélodie descendante douce)\n   */\n  createEndSound(audioContext) {\n    const audio = new Audio();\n    audio.volume = 0.4;\n    audio.playSynthetic = () => {\n      // Mélodie descendante douce : Sol-Mi-Do-Sol(grave)\n      const melody = [{\n        freq: 783.99,\n        duration: 0.2\n      }, {\n        freq: 659.25,\n        duration: 0.2\n      }, {\n        freq: 523.25,\n        duration: 0.2\n      }, {\n        freq: 392.0,\n        duration: 0.4\n      } // Sol (octave inférieure)\n      ];\n\n      let currentTime = audioContext.currentTime;\n      melody.forEach((note, index) => {\n        const oscillator = audioContext.createOscillator();\n        const gainNode = audioContext.createGain();\n        oscillator.connect(gainNode);\n        gainNode.connect(audioContext.destination);\n        oscillator.frequency.value = note.freq;\n        oscillator.type = 'sine'; // Son doux pour la fin\n        // Enveloppe douce pour un son apaisant\n        gainNode.gain.setValueAtTime(0, currentTime);\n        gainNode.gain.linearRampToValueAtTime(0.2, currentTime + 0.05);\n        gainNode.gain.linearRampToValueAtTime(0.1, currentTime + note.duration * 0.7);\n        gainNode.gain.exponentialRampToValueAtTime(0.01, currentTime + note.duration);\n        oscillator.start(currentTime);\n        oscillator.stop(currentTime + note.duration);\n        currentTime += note.duration * 0.9; // Léger chevauchement\n      });\n\n      return Promise.resolve();\n    };\n    return audio;\n  }\n  /**\n   * Crée un son de notification agréable (double bip)\n   */\n  createNotificationSound(audioContext) {\n    const audio = new Audio();\n    audio.volume = 0.6;\n    audio.playSynthetic = () => {\n      // Double bip agréable : Do-Sol\n      const notes = [{\n        freq: 523.25,\n        duration: 0.15,\n        delay: 0\n      }, {\n        freq: 783.99,\n        duration: 0.25,\n        delay: 0.2\n      } // Sol\n      ];\n\n      notes.forEach(note => {\n        setTimeout(() => {\n          const oscillator = audioContext.createOscillator();\n          const gainNode = audioContext.createGain();\n          oscillator.connect(gainNode);\n          gainNode.connect(audioContext.destination);\n          oscillator.frequency.value = note.freq;\n          oscillator.type = 'triangle'; // Son doux et agréable\n          const startTime = audioContext.currentTime;\n          // Enveloppe pour un son naturel\n          gainNode.gain.setValueAtTime(0, startTime);\n          gainNode.gain.linearRampToValueAtTime(0.4, startTime + 0.02);\n          gainNode.gain.linearRampToValueAtTime(0.2, startTime + note.duration * 0.7);\n          gainNode.gain.exponentialRampToValueAtTime(0.01, startTime + note.duration);\n          oscillator.start(startTime);\n          oscillator.stop(startTime + note.duration);\n        }, note.delay * 1000);\n      });\n      return Promise.resolve();\n    };\n    return audio;\n  }\n  /**\n   * Charge un fichier audio\n   */\n  loadSound(name, path) {\n    try {\n      const audio = new Audio();\n      // Configuration de l'audio\n      audio.preload = 'auto';\n      audio.volume = 0.7;\n      // Gérer les événements de chargement\n      audio.addEventListener('canplaythrough', () => {\n        console.log(`✅ [CallService] Sound ${name} loaded successfully from ${path}`);\n      });\n      audio.addEventListener('error', e => {\n        console.error(`❌ [CallService] Error loading sound ${name} from ${path}:`, e);\n        console.log(`🔄 [CallService] Trying to load ${name} with different approach...`);\n        // Essayer de charger avec un chemin relatif\n        const altPath = path.startsWith('/') ? path.substring(1) : path;\n        if (altPath !== path) {\n          setTimeout(() => {\n            audio.src = altPath;\n            audio.load();\n          }, 100);\n        }\n      });\n      audio.addEventListener('ended', () => {\n        this.isPlaying[name] = false;\n        console.log(`🔇 [CallService] Sound ${name} ended`);\n      });\n      // Définir la source et charger\n      audio.src = path;\n      audio.load();\n      this.sounds[name] = audio;\n      this.isPlaying[name] = false;\n      console.log(`🔊 [CallService] Loading sound ${name} from ${path}`);\n    } catch (error) {\n      console.error(`❌ [CallService] Error creating audio element for ${name}:`, error);\n    }\n  }\n  /**\n   * Joue un son\n   */\n  play(name, loop = false) {\n    if (this.muted) {\n      console.log(`🔇 [CallService] Sound ${name} muted`);\n      return;\n    }\n    try {\n      // Pour les sons d'appel, utiliser directement les versions synthétiques\n      let sound;\n      if (name === 'ringtone' || name === 'call-connected' || name === 'call-end') {\n        const syntheticName = `${name}-synthetic`;\n        sound = this.sounds[syntheticName];\n        if (sound) {\n          console.log(`🎵 [CallService] Using beautiful synthetic melody for ${name}`);\n        }\n      } else {\n        // Pour les autres sons (comme notification), essayer d'abord le fichier\n        sound = this.sounds[name];\n        if (!sound || sound.error) {\n          const syntheticName = `${name}-synthetic`;\n          sound = this.sounds[syntheticName];\n          if (sound) {\n            console.log(`🔊 [CallService] Using synthetic sound for ${name}`);\n          }\n        }\n      }\n      if (!sound) {\n        console.warn(`🔊 [CallService] Sound ${name} not found (neither original nor synthetic)`);\n        // Créer un bip simple comme dernier recours\n        this.playSimpleBeep(name === 'ringtone' ? 800 : name === 'call-connected' ? 1000 : 400);\n        return;\n      }\n      sound.loop = loop;\n      sound.volume = 0.7;\n      if (!this.isPlaying[name]) {\n        console.log(`🔊 [CallService] Playing sound: ${name} (loop: ${loop})`);\n        // Vérifier si c'est un son synthétique\n        if (sound.playSynthetic) {\n          sound.playSynthetic().then(() => {\n            console.log(`✅ [CallService] Synthetic sound ${name} started successfully`);\n            this.isPlaying[name] = true;\n            // Pour la sonnerie synthétique, elle gère sa propre boucle\n            if (name === 'ringtone' && !loop) {\n              // Si ce n'est pas en boucle, arrêter après un certain temps\n              setTimeout(() => {\n                this.isPlaying[name] = false;\n              }, 3000);\n            } else if (name !== 'ringtone') {\n              // Pour les autres sons, arrêter après leur durée\n              setTimeout(() => {\n                this.isPlaying[name] = false;\n              }, name === 'call-connected' ? 1200 : 1000);\n            }\n          }).catch(error => {\n            console.error(`❌ [CallService] Error playing synthetic sound ${name}:`, error);\n          });\n        } else {\n          // Son normal\n          sound.currentTime = 0;\n          sound.play().then(() => {\n            console.log(`✅ [CallService] Sound ${name} started successfully`);\n            this.isPlaying[name] = true;\n          }).catch(error => {\n            console.error(`❌ [CallService] Error playing sound ${name}:`, error);\n            // Essayer le son synthétique en cas d'échec\n            const syntheticName = `${name}-synthetic`;\n            const syntheticSound = this.sounds[syntheticName];\n            if (syntheticSound && syntheticSound.playSynthetic) {\n              console.log(`🔄 [CallService] Falling back to synthetic sound for ${name}`);\n              this.play(name, loop);\n            } else {\n              // Dernier recours : bip simple\n              this.playSimpleBeep(name === 'ringtone' ? 800 : name === 'call-connected' ? 1000 : 400);\n            }\n          });\n        }\n      } else {\n        console.log(`🔊 [CallService] Sound ${name} already playing`);\n      }\n    } catch (error) {\n      console.error(`❌ [CallService] Error in play method for ${name}:`, error);\n      // Dernier recours\n      this.playSimpleBeep(name === 'ringtone' ? 800 : name === 'call-connected' ? 1000 : 400);\n    }\n  }\n  /**\n   * Joue un bip simple comme dernier recours\n   */\n  playSimpleBeep(frequency) {\n    try {\n      const audioContext = new (window.AudioContext || window.webkitAudioContext)();\n      const oscillator = audioContext.createOscillator();\n      const gainNode = audioContext.createGain();\n      oscillator.connect(gainNode);\n      gainNode.connect(audioContext.destination);\n      oscillator.frequency.value = frequency;\n      oscillator.type = 'sine';\n      gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);\n      gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);\n      oscillator.start(audioContext.currentTime);\n      oscillator.stop(audioContext.currentTime + 0.3);\n      console.log(`🔊 [CallService] Playing simple beep at ${frequency}Hz`);\n    } catch (error) {\n      console.error('❌ [CallService] Could not play simple beep:', error);\n    }\n  }\n  /**\n   * Arrête un son\n   */\n  stop(name) {\n    try {\n      let sound = this.sounds[name];\n      // Essayer aussi la version synthétique\n      if (!sound) {\n        sound = this.sounds[`${name}-synthetic`];\n      }\n      if (!sound) {\n        console.warn(`🔊 [CallService] Sound ${name} not found for stopping`);\n        return;\n      }\n      if (this.isPlaying[name]) {\n        console.log(`🔇 [CallService] Stopping sound: ${name}`);\n        // Arrêter le son synthétique si c'est le cas\n        if (sound.stopSynthetic) {\n          sound.stopSynthetic();\n        } else {\n          sound.pause();\n          sound.currentTime = 0;\n        }\n        this.isPlaying[name] = false;\n      } else {\n        console.log(`🔇 [CallService] Sound ${name} was not playing`);\n      }\n    } catch (error) {\n      console.error(`❌ [CallService] Error stopping sound ${name}:`, error);\n    }\n  }\n  /**\n   * S'abonne aux appels entrants\n   */\n  subscribeToIncomingCalls() {\n    // console.log('🔔 [CallService] Setting up incoming call subscription...');\n    try {\n      this.apollo.subscribe({\n        query: INCOMING_CALL_SUBSCRIPTION,\n        errorPolicy: 'all' // Continuer même en cas d'erreur\n      }).subscribe({\n        next: ({\n          data,\n          errors\n        }) => {\n          if (errors) {\n            console.warn('⚠️ [CallService] GraphQL errors in subscription:', errors);\n          }\n          if (data?.incomingCall) {\n            // console.log('📞 [CallService] Incoming call received:', data.incomingCall.id);\n            this.handleIncomingCall(data.incomingCall);\n          }\n        },\n        error: error => {\n          console.error('❌ [CallService] Error in incoming call subscription:', error);\n          // Réessayer UNE SEULE FOIS après 10 secondes\n          setTimeout(() => {\n            this.subscribeToIncomingCalls();\n          }, 10000);\n        },\n        complete: () => {\n          // console.log('🔚 [CallService] Incoming call subscription completed');\n          // Réessayer si la subscription se ferme de manière inattendue\n          setTimeout(() => {\n            this.subscribeToIncomingCalls();\n          }, 5000);\n        }\n      });\n    } catch (error) {\n      console.error('❌ [CallService] Failed to create subscription:', error);\n      // Réessayer après 10 secondes\n      setTimeout(() => {\n        this.subscribeToIncomingCalls();\n      }, 10000);\n    }\n  }\n  /**\n   * Méthode publique pour forcer la réinitialisation de la subscription\n   */\n  reinitializeSubscription() {\n    // console.log('🔄 [CallService] Manually reinitializing subscription...');\n    this.subscribeToIncomingCalls();\n    this.subscribeToCallStatusChanges();\n  }\n  /**\n   * Méthode de test pour vérifier les sons\n   */\n  testSounds() {\n    console.log('🧪 [CallService] Testing sounds...');\n    // Test de la sonnerie\n    console.log('🔊 Testing ringtone...');\n    this.play('ringtone', true);\n    setTimeout(() => {\n      this.stop('ringtone');\n      console.log('🔊 Testing call-connected...');\n      this.play('call-connected');\n    }, 3000);\n    setTimeout(() => {\n      console.log('🔊 Testing call-end...');\n      this.play('call-end');\n    }, 5000);\n  }\n  /**\n   * Test spécifique pour la sonnerie\n   */\n  testRingtone() {\n    console.log('🧪 [CallService] Testing ringtone specifically...');\n    console.log('🔊 [CallService] Available sounds:', Object.keys(this.sounds));\n    // Vérifier si le son est chargé\n    const ringtone = this.sounds['ringtone'];\n    const ringtoneSynthetic = this.sounds['ringtone-synthetic'];\n    if (ringtone) {\n      console.log('✅ [CallService] Ringtone MP3 found:', {\n        src: ringtone.src,\n        readyState: ringtone.readyState,\n        error: ringtone.error,\n        duration: ringtone.duration\n      });\n    } else {\n      console.log('❌ [CallService] Ringtone MP3 not found');\n    }\n    if (ringtoneSynthetic) {\n      console.log('✅ [CallService] Ringtone synthetic found');\n    }\n    // Jouer la sonnerie (elle va automatiquement utiliser le fallback si nécessaire)\n    console.log('🎵 [CallService] Playing beautiful ringtone melody...');\n    this.play('ringtone', true);\n    // Arrêter après 8 secondes pour entendre plusieurs cycles de la mélodie\n    setTimeout(() => {\n      this.stop('ringtone');\n      console.log('🔇 [CallService] Ringtone test stopped');\n    }, 8000);\n  }\n  /**\n   * Test tous les nouveaux sons améliorés\n   */\n  testBeautifulSounds() {\n    console.log('🧪 [CallService] Testing all beautiful sounds...');\n    // Test de la sonnerie (mélodie)\n    // console.log('🎵 Testing beautiful ringtone melody...');\n    this.play('ringtone', true);\n    setTimeout(() => {\n      this.stop('ringtone');\n      // console.log('🎵 Testing beautiful connection sound...');\n      this.play('call-connected');\n    }, 4000);\n    setTimeout(() => {\n      // console.log('🎵 Testing beautiful end sound...');\n      this.play('call-end');\n    }, 6000);\n    setTimeout(() => {\n      // console.log('🎵 Testing beautiful notification sound...');\n      this.play('notification');\n    }, 8000);\n    setTimeout(() => {\n      console.log('🎵 All sound tests completed!');\n    }, 10000);\n  }\n  /**\n   * Joue le son de notification (méthode publique)\n   */\n  playNotification() {\n    console.log('🔔 [CallService] Playing notification sound...');\n    this.play('notification');\n  }\n  /**\n   * S'abonne aux changements de statut d'appel\n   */\n  subscribeToCallStatusChanges() {\n    console.log('📞 [CallService] Setting up call status change subscription...');\n    try {\n      this.apollo.subscribe({\n        query: CALL_STATUS_CHANGED_SUBSCRIPTION,\n        errorPolicy: 'all'\n      }).subscribe({\n        next: ({\n          data,\n          errors\n        }) => {\n          if (errors) {\n            console.warn('⚠️ [CallService] GraphQL errors in call status subscription:', errors);\n          }\n          if (data?.callStatusChanged) {\n            console.log('📞 [CallService] Call status changed:', data.callStatusChanged);\n            this.handleCallStatusChange(data.callStatusChanged);\n          }\n        },\n        error: error => {\n          console.error('❌ [CallService] Error in call status subscription:', error);\n          // Réessayer après 5 secondes\n          setTimeout(() => {\n            this.subscribeToCallStatusChanges();\n          }, 5000);\n        },\n        complete: () => {\n          console.log('🔚 [CallService] Call status subscription completed');\n          // Réessayer si la subscription se ferme\n          setTimeout(() => {\n            this.subscribeToCallStatusChanges();\n          }, 2000);\n        }\n      });\n    } catch (error) {\n      console.error('❌ [CallService] Failed to create call status subscription:', error);\n      setTimeout(() => {\n        this.subscribeToCallStatusChanges();\n      }, 3000);\n    }\n  }\n  /**\n   * Gère un appel entrant\n   */\n  handleIncomingCall(call) {\n    console.log('🔔 [CallService] Handling incoming call:', {\n      callId: call.id,\n      callType: call.type,\n      caller: call.caller?.username,\n      conversationId: call.conversationId\n    });\n    this.incomingCall.next(call);\n    this.play('ringtone', true);\n    console.log('🔊 [CallService] Ringtone started, call notification sent to UI');\n  }\n  /**\n   * Gère les changements de statut d'appel\n   */\n  handleCallStatusChange(call) {\n    console.log('📞 [CallService] Call status changed:', call.status);\n    switch (call.status) {\n      case CallStatus.REJECTED:\n        console.log('❌ [CallService] Call was rejected');\n        this.stop('ringtone');\n        this.play('call-end');\n        this.activeCall.next(null);\n        this.incomingCall.next(null);\n        break;\n      case CallStatus.ENDED:\n        console.log('📞 [CallService] Call ended');\n        this.stopAllSounds();\n        this.play('call-end');\n        this.activeCall.next(null);\n        this.incomingCall.next(null);\n        break;\n      case CallStatus.CONNECTED:\n        console.log('✅ [CallService] Call connected');\n        this.stop('ringtone');\n        this.play('call-connected');\n        this.activeCall.next(call);\n        this.incomingCall.next(null);\n        break;\n      case CallStatus.RINGING:\n        console.log('📞 [CallService] Call is ringing');\n        this.play('ringtone', true);\n        break;\n      default:\n        console.log('📞 [CallService] Unknown call status:', call.status);\n        break;\n    }\n  }\n  /**\n   * Obtient les médias utilisateur (caméra/micro)\n   */\n  getUserMedia(callType) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      console.log('🎥 [CallService] Getting user media for:', callType);\n      const constraints = {\n        audio: true,\n        video: callType === CallType.VIDEO\n      };\n      try {\n        const stream = yield navigator.mediaDevices.getUserMedia(constraints);\n        console.log('✅ [CallService] User media obtained');\n        _this.localStream = stream;\n        // Afficher le stream local si on a un élément vidéo\n        if (_this.localVideoElement && callType === CallType.VIDEO) {\n          _this.localVideoElement.srcObject = stream;\n        }\n        return stream;\n      } catch (error) {\n        console.error('❌ [CallService] Failed to get user media:', error);\n        throw new Error(\"Impossible d'accéder à la caméra/microphone\");\n      }\n    })();\n  }\n  /**\n   * Ajoute le stream local à la PeerConnection\n   */\n  addLocalStreamToPeerConnection(stream) {\n    if (!this.peerConnection) {\n      console.error('❌ [CallService] No peer connection available');\n      return;\n    }\n    console.log('📤 [CallService] Adding local stream to peer connection');\n    stream.getTracks().forEach(track => {\n      this.peerConnection.addTrack(track, stream);\n    });\n  }\n  /**\n   * Crée une offre WebRTC\n   */\n  createOffer() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this2.peerConnection) {\n        throw new Error('No peer connection available');\n      }\n      console.log('📝 [CallService] Creating WebRTC offer');\n      const offer = yield _this2.peerConnection.createOffer();\n      yield _this2.peerConnection.setLocalDescription(offer);\n      return offer;\n    })();\n  }\n  /**\n   * Crée une réponse WebRTC\n   */\n  createAnswer(offer) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this3.peerConnection) {\n        throw new Error('No peer connection available');\n      }\n      console.log('📝 [CallService] Creating WebRTC answer');\n      yield _this3.peerConnection.setRemoteDescription(offer);\n      const answer = yield _this3.peerConnection.createAnswer();\n      yield _this3.peerConnection.setLocalDescription(answer);\n      return answer;\n    })();\n  }\n  /**\n   * Configure les éléments vidéo\n   */\n  setVideoElements(localVideo, remoteVideo) {\n    console.log('📺 [CallService] Setting video elements');\n    this.localVideoElement = localVideo;\n    this.remoteVideoElement = remoteVideo;\n    // Si on a déjà des streams, les connecter\n    if (this.localStream && localVideo) {\n      localVideo.srcObject = this.localStream;\n    }\n    if (this.remoteStream && remoteVideo) {\n      remoteVideo.srcObject = this.remoteStream;\n    }\n  }\n  /**\n   * Initie un appel WebRTC\n   */\n  initiateCall(recipientId, callType, conversationId) {\n    console.log('🔄 [CallService] Initiating call:', {\n      recipientId,\n      callType,\n      conversationId\n    });\n    if (!recipientId) {\n      const error = new Error('Recipient ID is required');\n      console.error('❌ [CallService] initiateCall error:', error);\n      return throwError(() => error);\n    }\n    // Générer un ID unique pour l'appel\n    const callId = `call_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n    // Créer une vraie offre WebRTC\n    return from(this.createWebRTCOffer(callType)).pipe(switchMap(offer => {\n      const variables = {\n        recipientId,\n        callType: callType,\n        callId,\n        offer: JSON.stringify(offer),\n        conversationId\n      };\n      console.log('📤 [CallService] Sending initiate call mutation:', variables);\n      return this.apollo.mutate({\n        mutation: INITIATE_CALL_MUTATION,\n        variables\n      }).pipe(map(result => {\n        console.log('✅ [CallService] Call initiated successfully:', result);\n        if (!result.data?.initiateCall) {\n          throw new Error('No call data received from server');\n        }\n        const call = result.data.initiateCall;\n        console.log('📞 [CallService] Call details:', {\n          id: call.id,\n          type: call.type,\n          status: call.status,\n          caller: call.caller?.username,\n          recipient: call.recipient?.username\n        });\n        // Mettre à jour l'état local\n        this.activeCall.next(call);\n        return call;\n      }), catchError(error => {\n        console.error('❌ [CallService] initiateCall error:', error);\n        this.logger.error('Error initiating call:', error);\n        let errorMessage = \"Erreur lors de l'initiation de l'appel\";\n        if (error.networkError) {\n          errorMessage = 'Erreur de connexion réseau';\n        } else if (error.graphQLErrors?.length > 0) {\n          errorMessage = error.graphQLErrors[0].message || errorMessage;\n        }\n        return throwError(() => new Error(errorMessage));\n      }));\n    }), catchError(error => {\n      console.error('❌ [CallService] WebRTC error:', error);\n      return throwError(() => new Error('Erreur WebRTC: ' + error.message));\n    }));\n  }\n  /**\n   * Accepte un appel entrant\n   */\n  acceptCall(incomingCall) {\n    console.log('🔄 [CallService] Accepting call:', incomingCall.id);\n    // Générer une réponse WebRTC factice\n    const answer = JSON.stringify({\n      type: 'answer',\n      sdp: 'v=0\\r\\no=- 0 0 IN IP4 127.0.0.1\\r\\ns=-\\r\\nt=0 0\\r\\n'\n    });\n    return this.apollo.mutate({\n      mutation: ACCEPT_CALL_MUTATION,\n      variables: {\n        callId: incomingCall.id,\n        answer\n      }\n    }).pipe(switchMap(result => {\n      console.log('✅ [CallService] Call accepted successfully:', result);\n      if (!result.data?.acceptCall) {\n        throw new Error('No call data received from server');\n      }\n      const call = result.data.acceptCall;\n      // Arrêter la sonnerie\n      this.stop('ringtone');\n      this.play('call-connected');\n      // Démarrer les médias pour l'appel de manière asynchrone\n      return from(this.startMediaForCall(incomingCall, call));\n    }), catchError(error => {\n      console.error('❌ [CallService] acceptCall error:', error);\n      this.logger.error('Error accepting call:', error);\n      return throwError(() => new Error(\"Erreur lors de l'acceptation de l'appel\"));\n    }));\n  }\n  /**\n   * Rejette un appel entrant\n   */\n  rejectCall(callId, reason) {\n    console.log('🔄 [CallService] Rejecting call:', callId, reason);\n    return this.apollo.mutate({\n      mutation: REJECT_CALL_MUTATION,\n      variables: {\n        callId,\n        reason: reason || 'User rejected'\n      }\n    }).pipe(map(result => {\n      console.log('✅ [CallService] Call rejected successfully:', result);\n      if (!result.data?.rejectCall) {\n        throw new Error('No response received from server');\n      }\n      // Mettre à jour l'état local\n      this.incomingCall.next(null);\n      this.activeCall.next(null);\n      // Arrêter la sonnerie\n      this.stop('ringtone');\n      return result.data.rejectCall;\n    }), catchError(error => {\n      console.error('❌ [CallService] rejectCall error:', error);\n      this.logger.error('Error rejecting call:', error);\n      return throwError(() => new Error(\"Erreur lors du rejet de l'appel\"));\n    }));\n  }\n  /**\n   * Termine un appel en cours\n   */\n  endCall(callId) {\n    console.log('🔄 [CallService] Ending call:', callId);\n    return this.apollo.mutate({\n      mutation: END_CALL_MUTATION,\n      variables: {\n        callId,\n        feedback: null // Pas de feedback pour l'instant\n      }\n    }).pipe(map(result => {\n      console.log('✅ [CallService] Call ended successfully:', result);\n      if (!result.data?.endCall) {\n        throw new Error('No response received from server');\n      }\n      // Mettre à jour l'état local\n      this.activeCall.next(null);\n      this.incomingCall.next(null);\n      // Arrêter tous les sons\n      this.stopAllSounds();\n      this.play('call-end');\n      return result.data.endCall;\n    }), catchError(error => {\n      console.error('❌ [CallService] endCall error:', error);\n      this.logger.error('Error ending call:', error);\n      return throwError(() => new Error(\"Erreur lors de la fin de l'appel\"));\n    }));\n  }\n  /**\n   * Bascule l'état des médias (audio/vidéo) pendant un appel\n   */\n  toggleMedia(callId, enableVideo, enableAudio) {\n    console.log('🔄 [CallService] Toggling media:', {\n      callId,\n      enableVideo,\n      enableAudio\n    });\n    return this.apollo.mutate({\n      mutation: TOGGLE_CALL_MEDIA_MUTATION,\n      variables: {\n        callId,\n        video: enableVideo,\n        audio: enableAudio\n      }\n    }).pipe(map(result => {\n      console.log('✅ [CallService] Media toggled successfully:', result);\n      if (!result.data?.toggleCallMedia) {\n        throw new Error('No response received from server');\n      }\n      return result.data.toggleCallMedia;\n    }), catchError(error => {\n      console.error('❌ [CallService] toggleMedia error:', error);\n      this.logger.error('Error toggling media:', error);\n      return throwError(() => new Error('Erreur lors du changement des médias'));\n    }));\n  }\n  /**\n   * Démarre les médias pour un appel accepté\n   */\n  startMediaForCall(incomingCall, call) {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      console.log('🎥 [CallService] Call connected - playing connection sound');\n      // Jouer le son de connexion\n      _this4.play('call-connected');\n      // Mettre à jour l'état local\n      _this4.activeCall.next(call);\n      _this4.incomingCall.next(null); // Supprimer l'appel entrant\n      return call;\n    })();\n  }\n  /**\n   * Active/désactive la vidéo\n   */\n  toggleVideo() {\n    this.isVideoEnabled = !this.isVideoEnabled;\n    console.log('📹 [CallService] Video toggled:', this.isVideoEnabled);\n    return this.isVideoEnabled;\n  }\n  /**\n   * Active/désactive l'audio\n   */\n  toggleAudio() {\n    this.isAudioEnabled = !this.isAudioEnabled;\n    console.log('🎤 [CallService] Audio toggled:', this.isAudioEnabled);\n    return this.isAudioEnabled;\n  }\n  /**\n   * Obtient l'état de la vidéo\n   */\n  getVideoEnabled() {\n    return this.isVideoEnabled;\n  }\n  /**\n   * Obtient l'état de l'audio\n   */\n  getAudioEnabled() {\n    return this.isAudioEnabled;\n  }\n  /**\n   * Arrête tous les sons\n   */\n  stopAllSounds() {\n    console.log('🔇 [CallService] Stopping all sounds');\n    Object.keys(this.sounds).forEach(name => {\n      this.stop(name);\n    });\n  }\n  /**\n   * Active les sons après interaction utilisateur (pour contourner les restrictions du navigateur)\n   */\n  enableSounds() {\n    console.log('🔊 [CallService] Enabling sounds after user interaction');\n    Object.values(this.sounds).forEach(sound => {\n      if (sound) {\n        sound.muted = false;\n        sound.volume = 0.7;\n        // Jouer et arrêter immédiatement pour \"débloquer\" l'audio\n        sound.play().then(() => {\n          sound.pause();\n          sound.currentTime = 0;\n        }).catch(() => {\n          // Ignorer les erreurs ici\n        });\n      }\n    });\n  }\n  ngOnDestroy() {\n    this.stopAllSounds();\n  }\n  static {\n    this.ɵfac = function CallService_Factory(t) {\n      return new (t || CallService)(i0.ɵɵinject(i1.Apollo), i0.ɵɵinject(i2.LoggerService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: CallService,\n      factory: CallService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["BehaviorSubject", "throwError", "from", "map", "catchError", "switchMap", "CallType", "CallStatus", "INITIATE_CALL_MUTATION", "ACCEPT_CALL_MUTATION", "REJECT_CALL_MUTATION", "END_CALL_MUTATION", "TOGGLE_CALL_MEDIA_MUTATION", "INCOMING_CALL_SUBSCRIPTION", "CALL_STATUS_CHANGED_SUBSCRIPTION", "CallService", "constructor", "apollo", "logger", "activeCall", "incomingCall", "activeCall$", "asObservable", "incomingCall$", "sounds", "isPlaying", "muted", "isVideoEnabled", "isAudioEnabled", "peerConnection", "localStream", "remoteStream", "localVideoElement", "remoteVideoElement", "preloadSounds", "initializeSubscriptions", "initializeWebRTC", "setTimeout", "subscribeToIncomingCalls", "subscribeToCallStatusChanges", "value", "console", "log", "configuration", "iceServers", "urls", "RTCPeerConnection", "setupPeerConnectionEvents", "error", "ontrack", "event", "streams", "srcObject", "onicecandidate", "candidate", "onconnectionstatechange", "connectionState", "createSyntheticSounds", "loadSound", "audioContext", "window", "AudioContext", "webkitAudioContext", "createRingtoneSound", "createConnectedSound", "createEndSound", "createNotificationSound", "warn", "audio", "Audio", "volume", "timeoutIds", "playSynthetic", "Promise", "resolve", "playMelody", "melody", "freq", "duration", "currentTime", "for<PERSON>ach", "note", "index", "oscillator", "createOscillator", "gainNode", "createGain", "connect", "destination", "frequency", "type", "gain", "setValueAtTime", "linearRampToValueAtTime", "exponentialRampToValueAtTime", "start", "stop", "timeoutId", "push", "stopSynthetic", "id", "clearTimeout", "notes", "delay", "startTime", "name", "path", "preload", "addEventListener", "e", "altPath", "startsWith", "substring", "src", "load", "play", "loop", "sound", "syntheticName", "playSimpleBeep", "then", "catch", "syntheticSound", "pause", "subscribe", "query", "errorPolicy", "next", "data", "errors", "handleIncomingCall", "complete", "reinitializeSubscription", "testSounds", "testRingtone", "Object", "keys", "ringtone", "ringtoneSynthetic", "readyState", "testBeautifulSounds", "playNotification", "callStatusChanged", "handleCallStatusChange", "call", "callId", "callType", "caller", "username", "conversationId", "status", "REJECTED", "ENDED", "stopAllSounds", "CONNECTED", "RINGING", "getUserMedia", "_this", "_asyncToGenerator", "constraints", "video", "VIDEO", "stream", "navigator", "mediaDevices", "Error", "addLocalStreamToPeerConnection", "getTracks", "track", "addTrack", "createOffer", "_this2", "offer", "setLocalDescription", "createAnswer", "_this3", "setRemoteDescription", "answer", "setVideoElements", "localVideo", "remoteVideo", "initiateCall", "recipientId", "Date", "now", "Math", "random", "toString", "substr", "createWebRTCOffer", "pipe", "variables", "JSON", "stringify", "mutate", "mutation", "result", "recipient", "errorMessage", "networkError", "graphQLErrors", "length", "message", "acceptCall", "sdp", "startMediaForCall", "rejectCall", "reason", "endCall", "feedback", "toggleMedia", "enableVideo", "enableAudio", "toggleCallMedia", "_this4", "toggleVideo", "toggleAudio", "getVideoEnabled", "getAudioEnabled", "enableSounds", "values", "ngOnDestroy", "i0", "ɵɵinject", "i1", "Apollo", "i2", "LoggerService", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\services\\call.service.ts"], "sourcesContent": ["import { Injectable, <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';\nimport { Apollo } from 'apollo-angular';\nimport { BehaviorSubject, Observable, throwError, of, from } from 'rxjs';\nimport { map, catchError, switchMap } from 'rxjs/operators';\nimport {\n  Call,\n  CallType,\n  CallStatus,\n  IncomingCall,\n  CallSuccess,\n} from '../models/message.model';\nimport {\n  INITIATE_CALL_MUTATION,\n  ACCEPT_CALL_MUTATION,\n  REJECT_CALL_MUTATION,\n  END_CALL_MUTATION,\n  TOGGLE_CALL_MEDIA_MUTATION,\n  INCOMING_CALL_SUBSCRIPTION,\n  CALL_STATUS_CHANGED_SUBSCRIPTION,\n} from '../graphql/message.graphql';\nimport { LoggerService } from './logger.service';\n\n@Injectable({\n  providedIn: 'root',\n})\nexport class CallService implements OnDestroy {\n  // État des appels\n  private activeCall = new BehaviorSubject<Call | null>(null);\n  private incomingCall = new BehaviorSubject<IncomingCall | null>(null);\n\n  // Observables publics\n  public activeCall$ = this.activeCall.asObservable();\n  public incomingCall$ = this.incomingCall.asObservable();\n\n  // Propriétés pour la gestion des sons\n  private sounds: { [key: string]: HTMLAudioElement } = {};\n  private isPlaying: { [key: string]: boolean } = {};\n  private muted = false;\n\n  // États simples pour les médias\n  private isVideoEnabled = true;\n  private isAudioEnabled = true;\n\n  // WebRTC\n  private peerConnection: RTCPeerConnection | null = null;\n  private localStream: MediaStream | null = null;\n  private remoteStream: MediaStream | null = null;\n  private localVideoElement: HTMLVideoElement | null = null;\n  private remoteVideoElement: HTMLVideoElement | null = null;\n\n  constructor(private apollo: Apollo, private logger: LoggerService) {\n    this.preloadSounds();\n    this.initializeSubscriptions();\n    this.initializeWebRTC();\n  }\n\n  /**\n   * Initialise les subscriptions avec un délai pour s'assurer que l'authentification est prête\n   */\n  private initializeSubscriptions(): void {\n    // Attendre un peu pour s'assurer que l'authentification est prête\n    setTimeout(() => {\n      this.subscribeToIncomingCalls();\n      this.subscribeToCallStatusChanges();\n    }, 1000);\n\n    // Réessayer UNE SEULE FOIS après 10 secondes si nécessaire\n    setTimeout(() => {\n      if (!this.incomingCall.value) {\n        // console.log('🔄 [CallService] Retrying subscription initialization...');\n        this.subscribeToIncomingCalls();\n        this.subscribeToCallStatusChanges();\n      }\n    }, 10000);\n  }\n\n  /**\n   * Initialise WebRTC\n   */\n  private initializeWebRTC(): void {\n    console.log('🔧 [CallService] Initializing WebRTC...');\n\n    // Configuration des serveurs STUN/TURN\n    const configuration: RTCConfiguration = {\n      iceServers: [\n        { urls: 'stun:stun.l.google.com:19302' },\n        { urls: 'stun:stun1.l.google.com:19302' },\n      ],\n    };\n\n    try {\n      this.peerConnection = new RTCPeerConnection(configuration);\n      this.setupPeerConnectionEvents();\n      console.log('✅ [CallService] WebRTC initialized successfully');\n    } catch (error) {\n      console.error('❌ [CallService] Failed to initialize WebRTC:', error);\n    }\n  }\n\n  /**\n   * Configure les événements de la PeerConnection\n   */\n  private setupPeerConnectionEvents(): void {\n    if (!this.peerConnection) return;\n\n    // Quand on reçoit un stream distant\n    this.peerConnection.ontrack = (event) => {\n      console.log('📺 [CallService] Remote stream received');\n      this.remoteStream = event.streams[0];\n      if (this.remoteVideoElement) {\n        this.remoteVideoElement.srcObject = this.remoteStream;\n      }\n    };\n\n    // Gestion des candidats ICE\n    this.peerConnection.onicecandidate = (event) => {\n      if (event.candidate) {\n        console.log('🧊 [CallService] ICE candidate generated');\n        // TODO: Envoyer le candidat via WebSocket\n      }\n    };\n\n    // État de la connexion\n    this.peerConnection.onconnectionstatechange = () => {\n      console.log(\n        '🔗 [CallService] Connection state:',\n        this.peerConnection?.connectionState\n      );\n    };\n  }\n\n  /**\n   * Précharge les sons utilisés dans l'application\n   */\n  private preloadSounds(): void {\n    // Créer des sons synthétiques de haute qualité\n    this.createSyntheticSounds();\n\n    // Charger le son de notification qui existe encore\n    this.loadSound('notification', 'assets/sounds/notification.mp3');\n\n    console.log(\n      '🎵 [CallService] Beautiful synthetic melodies created for calls'\n    );\n  }\n\n  /**\n   * Crée des sons synthétiques comme fallback\n   */\n  private createSyntheticSounds(): void {\n    try {\n      // Créer un contexte audio pour les sons synthétiques\n      const audioContext = new (window.AudioContext ||\n        (window as any).webkitAudioContext)();\n\n      // Son de sonnerie (mélodie agréable)\n      this.sounds['ringtone-synthetic'] =\n        this.createRingtoneSound(audioContext);\n\n      // Son de connexion (accord agréable)\n      this.sounds['call-connected-synthetic'] =\n        this.createConnectedSound(audioContext);\n\n      // Son de fin d'appel (ton descendant)\n      this.sounds['call-end-synthetic'] = this.createEndSound(audioContext);\n\n      // Son de notification (bip agréable)\n      this.sounds['notification-synthetic'] =\n        this.createNotificationSound(audioContext);\n\n      console.log('🔊 [CallService] Synthetic sounds created as fallback');\n    } catch (error) {\n      console.warn(\n        '⚠️ [CallService] Could not create synthetic sounds:',\n        error\n      );\n    }\n  }\n\n  /**\n   * Crée une sonnerie agréable (mélodie)\n   */\n  private createRingtoneSound(audioContext: AudioContext): HTMLAudioElement {\n    const audio = new Audio();\n    audio.volume = 0.5;\n\n    let isPlaying = false;\n    let timeoutIds: any[] = [];\n\n    (audio as any).playSynthetic = () => {\n      if (isPlaying) return Promise.resolve();\n\n      isPlaying = true;\n\n      const playMelody = () => {\n        if (!isPlaying) return;\n\n        // Mélodie inspirée de Nokia mais plus moderne : Mi-Ré-Fa#-Sol-Do#-Si-Ré-Do\n        const melody = [\n          { freq: 659.25, duration: 0.125 }, // Mi\n          { freq: 587.33, duration: 0.125 }, // Ré\n          { freq: 739.99, duration: 0.25 }, // Fa#\n          { freq: 783.99, duration: 0.25 }, // Sol\n          { freq: 554.37, duration: 0.125 }, // Do#\n          { freq: 493.88, duration: 0.125 }, // Si\n          { freq: 587.33, duration: 0.25 }, // Ré\n          { freq: 523.25, duration: 0.25 }, // Do\n        ];\n\n        let currentTime = audioContext.currentTime;\n\n        melody.forEach((note, index) => {\n          if (!isPlaying) return;\n\n          const oscillator = audioContext.createOscillator();\n          const gainNode = audioContext.createGain();\n\n          oscillator.connect(gainNode);\n          gainNode.connect(audioContext.destination);\n\n          oscillator.frequency.value = note.freq;\n          oscillator.type = 'square'; // Son plus moderne\n\n          // Enveloppe ADSR pour un son plus naturel\n          gainNode.gain.setValueAtTime(0, currentTime);\n          gainNode.gain.linearRampToValueAtTime(0.3, currentTime + 0.02);\n          gainNode.gain.linearRampToValueAtTime(\n            0.2,\n            currentTime + note.duration * 0.7\n          );\n          gainNode.gain.exponentialRampToValueAtTime(\n            0.01,\n            currentTime + note.duration\n          );\n\n          oscillator.start(currentTime);\n          oscillator.stop(currentTime + note.duration);\n\n          currentTime += note.duration + 0.05; // Petite pause entre les notes\n        });\n\n        // Répéter la mélodie après une pause\n        const timeoutId = setTimeout(() => {\n          if (isPlaying) {\n            playMelody();\n          }\n        }, (currentTime - audioContext.currentTime + 0.8) * 1000);\n\n        timeoutIds.push(timeoutId);\n      };\n\n      playMelody();\n      return Promise.resolve();\n    };\n\n    (audio as any).stopSynthetic = () => {\n      isPlaying = false;\n      timeoutIds.forEach((id) => clearTimeout(id));\n      timeoutIds = [];\n    };\n\n    return audio;\n  }\n\n  /**\n   * Crée un son de connexion agréable (mélodie ascendante)\n   */\n  private createConnectedSound(audioContext: AudioContext): HTMLAudioElement {\n    const audio = new Audio();\n    audio.volume = 0.5;\n\n    (audio as any).playSynthetic = () => {\n      // Mélodie ascendante positive : Do-Mi-Sol-Do (octave supérieure)\n      const melody = [\n        { freq: 523.25, duration: 0.15 }, // Do\n        { freq: 659.25, duration: 0.15 }, // Mi\n        { freq: 783.99, duration: 0.15 }, // Sol\n        { freq: 1046.5, duration: 0.4 }, // Do (octave supérieure)\n      ];\n\n      let currentTime = audioContext.currentTime;\n\n      melody.forEach((note, index) => {\n        const oscillator = audioContext.createOscillator();\n        const gainNode = audioContext.createGain();\n\n        oscillator.connect(gainNode);\n        gainNode.connect(audioContext.destination);\n\n        oscillator.frequency.value = note.freq;\n        oscillator.type = 'triangle'; // Son plus doux\n\n        // Enveloppe pour un son naturel\n        gainNode.gain.setValueAtTime(0, currentTime);\n        gainNode.gain.linearRampToValueAtTime(0.25, currentTime + 0.02);\n        gainNode.gain.linearRampToValueAtTime(\n          0.15,\n          currentTime + note.duration * 0.8\n        );\n        gainNode.gain.exponentialRampToValueAtTime(\n          0.01,\n          currentTime + note.duration\n        );\n\n        oscillator.start(currentTime);\n        oscillator.stop(currentTime + note.duration);\n\n        currentTime += note.duration * 0.8; // Chevauchement léger des notes\n      });\n\n      return Promise.resolve();\n    };\n\n    return audio;\n  }\n\n  /**\n   * Crée un son de fin d'appel (mélodie descendante douce)\n   */\n  private createEndSound(audioContext: AudioContext): HTMLAudioElement {\n    const audio = new Audio();\n    audio.volume = 0.4;\n\n    (audio as any).playSynthetic = () => {\n      // Mélodie descendante douce : Sol-Mi-Do-Sol(grave)\n      const melody = [\n        { freq: 783.99, duration: 0.2 }, // Sol\n        { freq: 659.25, duration: 0.2 }, // Mi\n        { freq: 523.25, duration: 0.2 }, // Do\n        { freq: 392.0, duration: 0.4 }, // Sol (octave inférieure)\n      ];\n\n      let currentTime = audioContext.currentTime;\n\n      melody.forEach((note, index) => {\n        const oscillator = audioContext.createOscillator();\n        const gainNode = audioContext.createGain();\n\n        oscillator.connect(gainNode);\n        gainNode.connect(audioContext.destination);\n\n        oscillator.frequency.value = note.freq;\n        oscillator.type = 'sine'; // Son doux pour la fin\n\n        // Enveloppe douce pour un son apaisant\n        gainNode.gain.setValueAtTime(0, currentTime);\n        gainNode.gain.linearRampToValueAtTime(0.2, currentTime + 0.05);\n        gainNode.gain.linearRampToValueAtTime(\n          0.1,\n          currentTime + note.duration * 0.7\n        );\n        gainNode.gain.exponentialRampToValueAtTime(\n          0.01,\n          currentTime + note.duration\n        );\n\n        oscillator.start(currentTime);\n        oscillator.stop(currentTime + note.duration);\n\n        currentTime += note.duration * 0.9; // Léger chevauchement\n      });\n\n      return Promise.resolve();\n    };\n\n    return audio;\n  }\n\n  /**\n   * Crée un son de notification agréable (double bip)\n   */\n  private createNotificationSound(\n    audioContext: AudioContext\n  ): HTMLAudioElement {\n    const audio = new Audio();\n    audio.volume = 0.6;\n\n    (audio as any).playSynthetic = () => {\n      // Double bip agréable : Do-Sol\n      const notes = [\n        { freq: 523.25, duration: 0.15, delay: 0 }, // Do\n        { freq: 783.99, duration: 0.25, delay: 0.2 }, // Sol\n      ];\n\n      notes.forEach((note) => {\n        setTimeout(() => {\n          const oscillator = audioContext.createOscillator();\n          const gainNode = audioContext.createGain();\n\n          oscillator.connect(gainNode);\n          gainNode.connect(audioContext.destination);\n\n          oscillator.frequency.value = note.freq;\n          oscillator.type = 'triangle'; // Son doux et agréable\n\n          const startTime = audioContext.currentTime;\n\n          // Enveloppe pour un son naturel\n          gainNode.gain.setValueAtTime(0, startTime);\n          gainNode.gain.linearRampToValueAtTime(0.4, startTime + 0.02);\n          gainNode.gain.linearRampToValueAtTime(\n            0.2,\n            startTime + note.duration * 0.7\n          );\n          gainNode.gain.exponentialRampToValueAtTime(\n            0.01,\n            startTime + note.duration\n          );\n\n          oscillator.start(startTime);\n          oscillator.stop(startTime + note.duration);\n        }, note.delay * 1000);\n      });\n\n      return Promise.resolve();\n    };\n\n    return audio;\n  }\n\n  /**\n   * Charge un fichier audio\n   */\n  private loadSound(name: string, path: string): void {\n    try {\n      const audio = new Audio();\n\n      // Configuration de l'audio\n      audio.preload = 'auto';\n      audio.volume = 0.7;\n\n      // Gérer les événements de chargement\n      audio.addEventListener('canplaythrough', () => {\n        console.log(\n          `✅ [CallService] Sound ${name} loaded successfully from ${path}`\n        );\n      });\n\n      audio.addEventListener('error', (e) => {\n        console.error(\n          `❌ [CallService] Error loading sound ${name} from ${path}:`,\n          e\n        );\n        console.log(\n          `🔄 [CallService] Trying to load ${name} with different approach...`\n        );\n\n        // Essayer de charger avec un chemin relatif\n        const altPath = path.startsWith('/') ? path.substring(1) : path;\n        if (altPath !== path) {\n          setTimeout(() => {\n            audio.src = altPath;\n            audio.load();\n          }, 100);\n        }\n      });\n\n      audio.addEventListener('ended', () => {\n        this.isPlaying[name] = false;\n        console.log(`🔇 [CallService] Sound ${name} ended`);\n      });\n\n      // Définir la source et charger\n      audio.src = path;\n      audio.load();\n\n      this.sounds[name] = audio;\n      this.isPlaying[name] = false;\n\n      console.log(`🔊 [CallService] Loading sound ${name} from ${path}`);\n    } catch (error) {\n      console.error(\n        `❌ [CallService] Error creating audio element for ${name}:`,\n        error\n      );\n    }\n  }\n\n  /**\n   * Joue un son\n   */\n  private play(name: string, loop: boolean = false): void {\n    if (this.muted) {\n      console.log(`🔇 [CallService] Sound ${name} muted`);\n      return;\n    }\n\n    try {\n      // Pour les sons d'appel, utiliser directement les versions synthétiques\n      let sound;\n      if (\n        name === 'ringtone' ||\n        name === 'call-connected' ||\n        name === 'call-end'\n      ) {\n        const syntheticName = `${name}-synthetic`;\n        sound = this.sounds[syntheticName];\n        if (sound) {\n          console.log(\n            `🎵 [CallService] Using beautiful synthetic melody for ${name}`\n          );\n        }\n      } else {\n        // Pour les autres sons (comme notification), essayer d'abord le fichier\n        sound = this.sounds[name];\n        if (!sound || sound.error) {\n          const syntheticName = `${name}-synthetic`;\n          sound = this.sounds[syntheticName];\n          if (sound) {\n            console.log(`🔊 [CallService] Using synthetic sound for ${name}`);\n          }\n        }\n      }\n\n      if (!sound) {\n        console.warn(\n          `🔊 [CallService] Sound ${name} not found (neither original nor synthetic)`\n        );\n        // Créer un bip simple comme dernier recours\n        this.playSimpleBeep(\n          name === 'ringtone' ? 800 : name === 'call-connected' ? 1000 : 400\n        );\n        return;\n      }\n\n      sound.loop = loop;\n      sound.volume = 0.7;\n\n      if (!this.isPlaying[name]) {\n        console.log(`🔊 [CallService] Playing sound: ${name} (loop: ${loop})`);\n\n        // Vérifier si c'est un son synthétique\n        if ((sound as any).playSynthetic) {\n          (sound as any)\n            .playSynthetic()\n            .then(() => {\n              console.log(\n                `✅ [CallService] Synthetic sound ${name} started successfully`\n              );\n              this.isPlaying[name] = true;\n\n              // Pour la sonnerie synthétique, elle gère sa propre boucle\n              if (name === 'ringtone' && !loop) {\n                // Si ce n'est pas en boucle, arrêter après un certain temps\n                setTimeout(() => {\n                  this.isPlaying[name] = false;\n                }, 3000);\n              } else if (name !== 'ringtone') {\n                // Pour les autres sons, arrêter après leur durée\n                setTimeout(\n                  () => {\n                    this.isPlaying[name] = false;\n                  },\n                  name === 'call-connected' ? 1200 : 1000\n                );\n              }\n            })\n            .catch((error: any) => {\n              console.error(\n                `❌ [CallService] Error playing synthetic sound ${name}:`,\n                error\n              );\n            });\n        } else {\n          // Son normal\n          sound.currentTime = 0;\n          sound\n            .play()\n            .then(() => {\n              console.log(\n                `✅ [CallService] Sound ${name} started successfully`\n              );\n              this.isPlaying[name] = true;\n            })\n            .catch((error) => {\n              console.error(\n                `❌ [CallService] Error playing sound ${name}:`,\n                error\n              );\n\n              // Essayer le son synthétique en cas d'échec\n              const syntheticName = `${name}-synthetic`;\n              const syntheticSound = this.sounds[syntheticName];\n              if (syntheticSound && (syntheticSound as any).playSynthetic) {\n                console.log(\n                  `🔄 [CallService] Falling back to synthetic sound for ${name}`\n                );\n                this.play(name, loop);\n              } else {\n                // Dernier recours : bip simple\n                this.playSimpleBeep(\n                  name === 'ringtone'\n                    ? 800\n                    : name === 'call-connected'\n                    ? 1000\n                    : 400\n                );\n              }\n            });\n        }\n      } else {\n        console.log(`🔊 [CallService] Sound ${name} already playing`);\n      }\n    } catch (error) {\n      console.error(\n        `❌ [CallService] Error in play method for ${name}:`,\n        error\n      );\n      // Dernier recours\n      this.playSimpleBeep(\n        name === 'ringtone' ? 800 : name === 'call-connected' ? 1000 : 400\n      );\n    }\n  }\n\n  /**\n   * Joue un bip simple comme dernier recours\n   */\n  private playSimpleBeep(frequency: number): void {\n    try {\n      const audioContext = new (window.AudioContext ||\n        (window as any).webkitAudioContext)();\n      const oscillator = audioContext.createOscillator();\n      const gainNode = audioContext.createGain();\n\n      oscillator.connect(gainNode);\n      gainNode.connect(audioContext.destination);\n\n      oscillator.frequency.value = frequency;\n      oscillator.type = 'sine';\n\n      gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);\n      gainNode.gain.exponentialRampToValueAtTime(\n        0.01,\n        audioContext.currentTime + 0.3\n      );\n\n      oscillator.start(audioContext.currentTime);\n      oscillator.stop(audioContext.currentTime + 0.3);\n\n      console.log(`🔊 [CallService] Playing simple beep at ${frequency}Hz`);\n    } catch (error) {\n      console.error('❌ [CallService] Could not play simple beep:', error);\n    }\n  }\n\n  /**\n   * Arrête un son\n   */\n  private stop(name: string): void {\n    try {\n      let sound = this.sounds[name];\n\n      // Essayer aussi la version synthétique\n      if (!sound) {\n        sound = this.sounds[`${name}-synthetic`];\n      }\n\n      if (!sound) {\n        console.warn(`🔊 [CallService] Sound ${name} not found for stopping`);\n        return;\n      }\n\n      if (this.isPlaying[name]) {\n        console.log(`🔇 [CallService] Stopping sound: ${name}`);\n\n        // Arrêter le son synthétique si c'est le cas\n        if ((sound as any).stopSynthetic) {\n          (sound as any).stopSynthetic();\n        } else {\n          sound.pause();\n          sound.currentTime = 0;\n        }\n\n        this.isPlaying[name] = false;\n      } else {\n        console.log(`🔇 [CallService] Sound ${name} was not playing`);\n      }\n    } catch (error) {\n      console.error(`❌ [CallService] Error stopping sound ${name}:`, error);\n    }\n  }\n\n  /**\n   * S'abonne aux appels entrants\n   */\n  private subscribeToIncomingCalls(): void {\n    // console.log('🔔 [CallService] Setting up incoming call subscription...');\n\n    try {\n      this.apollo\n        .subscribe<{ incomingCall: IncomingCall }>({\n          query: INCOMING_CALL_SUBSCRIPTION,\n          errorPolicy: 'all', // Continuer même en cas d'erreur\n        })\n        .subscribe({\n          next: ({ data, errors }) => {\n            if (errors) {\n              console.warn(\n                '⚠️ [CallService] GraphQL errors in subscription:',\n                errors\n              );\n            }\n\n            if (data?.incomingCall) {\n              // console.log('📞 [CallService] Incoming call received:', data.incomingCall.id);\n              this.handleIncomingCall(data.incomingCall);\n            }\n          },\n          error: (error) => {\n            console.error(\n              '❌ [CallService] Error in incoming call subscription:',\n              error\n            );\n\n            // Réessayer UNE SEULE FOIS après 10 secondes\n            setTimeout(() => {\n              this.subscribeToIncomingCalls();\n            }, 10000);\n          },\n          complete: () => {\n            // console.log('🔚 [CallService] Incoming call subscription completed');\n            // Réessayer si la subscription se ferme de manière inattendue\n            setTimeout(() => {\n              this.subscribeToIncomingCalls();\n            }, 5000);\n          },\n        });\n    } catch (error) {\n      console.error('❌ [CallService] Failed to create subscription:', error);\n      // Réessayer après 10 secondes\n      setTimeout(() => {\n        this.subscribeToIncomingCalls();\n      }, 10000);\n    }\n  }\n\n  /**\n   * Méthode publique pour forcer la réinitialisation de la subscription\n   */\n  public reinitializeSubscription(): void {\n    // console.log('🔄 [CallService] Manually reinitializing subscription...');\n    this.subscribeToIncomingCalls();\n    this.subscribeToCallStatusChanges();\n  }\n\n  /**\n   * Méthode de test pour vérifier les sons\n   */\n  public testSounds(): void {\n    console.log('🧪 [CallService] Testing sounds...');\n\n    // Test de la sonnerie\n    console.log('🔊 Testing ringtone...');\n    this.play('ringtone', true);\n\n    setTimeout(() => {\n      this.stop('ringtone');\n      console.log('🔊 Testing call-connected...');\n      this.play('call-connected');\n    }, 3000);\n\n    setTimeout(() => {\n      console.log('🔊 Testing call-end...');\n      this.play('call-end');\n    }, 5000);\n  }\n\n  /**\n   * Test spécifique pour la sonnerie\n   */\n  public testRingtone(): void {\n    console.log('🧪 [CallService] Testing ringtone specifically...');\n    console.log('🔊 [CallService] Available sounds:', Object.keys(this.sounds));\n\n    // Vérifier si le son est chargé\n    const ringtone = this.sounds['ringtone'];\n    const ringtoneSynthetic = this.sounds['ringtone-synthetic'];\n\n    if (ringtone) {\n      console.log('✅ [CallService] Ringtone MP3 found:', {\n        src: ringtone.src,\n        readyState: ringtone.readyState,\n        error: ringtone.error,\n        duration: ringtone.duration,\n      });\n    } else {\n      console.log('❌ [CallService] Ringtone MP3 not found');\n    }\n\n    if (ringtoneSynthetic) {\n      console.log('✅ [CallService] Ringtone synthetic found');\n    }\n\n    // Jouer la sonnerie (elle va automatiquement utiliser le fallback si nécessaire)\n    console.log('🎵 [CallService] Playing beautiful ringtone melody...');\n    this.play('ringtone', true);\n\n    // Arrêter après 8 secondes pour entendre plusieurs cycles de la mélodie\n    setTimeout(() => {\n      this.stop('ringtone');\n      console.log('🔇 [CallService] Ringtone test stopped');\n    }, 8000);\n  }\n\n  /**\n   * Test tous les nouveaux sons améliorés\n   */\n  public testBeautifulSounds(): void {\n    console.log('🧪 [CallService] Testing all beautiful sounds...');\n\n    // Test de la sonnerie (mélodie)\n    // console.log('🎵 Testing beautiful ringtone melody...');\n    this.play('ringtone', true);\n\n    setTimeout(() => {\n      this.stop('ringtone');\n      // console.log('🎵 Testing beautiful connection sound...');\n      this.play('call-connected');\n    }, 4000);\n\n    setTimeout(() => {\n      // console.log('🎵 Testing beautiful end sound...');\n      this.play('call-end');\n    }, 6000);\n\n    setTimeout(() => {\n      // console.log('🎵 Testing beautiful notification sound...');\n      this.play('notification');\n    }, 8000);\n\n    setTimeout(() => {\n      console.log('🎵 All sound tests completed!');\n    }, 10000);\n  }\n\n  /**\n   * Joue le son de notification (méthode publique)\n   */\n  public playNotification(): void {\n    console.log('🔔 [CallService] Playing notification sound...');\n    this.play('notification');\n  }\n\n  /**\n   * S'abonne aux changements de statut d'appel\n   */\n  private subscribeToCallStatusChanges(): void {\n    console.log(\n      '📞 [CallService] Setting up call status change subscription...'\n    );\n\n    try {\n      this.apollo\n        .subscribe<{ callStatusChanged: Call }>({\n          query: CALL_STATUS_CHANGED_SUBSCRIPTION,\n          errorPolicy: 'all',\n        })\n        .subscribe({\n          next: ({ data, errors }) => {\n            if (errors) {\n              console.warn(\n                '⚠️ [CallService] GraphQL errors in call status subscription:',\n                errors\n              );\n            }\n\n            if (data?.callStatusChanged) {\n              console.log(\n                '📞 [CallService] Call status changed:',\n                data.callStatusChanged\n              );\n              this.handleCallStatusChange(data.callStatusChanged);\n            }\n          },\n          error: (error) => {\n            console.error(\n              '❌ [CallService] Error in call status subscription:',\n              error\n            );\n            // Réessayer après 5 secondes\n            setTimeout(() => {\n              this.subscribeToCallStatusChanges();\n            }, 5000);\n          },\n          complete: () => {\n            console.log('🔚 [CallService] Call status subscription completed');\n            // Réessayer si la subscription se ferme\n            setTimeout(() => {\n              this.subscribeToCallStatusChanges();\n            }, 2000);\n          },\n        });\n    } catch (error) {\n      console.error(\n        '❌ [CallService] Failed to create call status subscription:',\n        error\n      );\n      setTimeout(() => {\n        this.subscribeToCallStatusChanges();\n      }, 3000);\n    }\n  }\n\n  /**\n   * Gère un appel entrant\n   */\n  private handleIncomingCall(call: IncomingCall): void {\n    console.log('🔔 [CallService] Handling incoming call:', {\n      callId: call.id,\n      callType: call.type,\n      caller: call.caller?.username,\n      conversationId: call.conversationId,\n    });\n\n    this.incomingCall.next(call);\n    this.play('ringtone', true);\n\n    console.log(\n      '🔊 [CallService] Ringtone started, call notification sent to UI'\n    );\n  }\n\n  /**\n   * Gère les changements de statut d'appel\n   */\n  private handleCallStatusChange(call: Call): void {\n    console.log('📞 [CallService] Call status changed:', call.status);\n\n    switch (call.status) {\n      case CallStatus.REJECTED:\n        console.log('❌ [CallService] Call was rejected');\n        this.stop('ringtone');\n        this.play('call-end');\n        this.activeCall.next(null);\n        this.incomingCall.next(null);\n        break;\n\n      case CallStatus.ENDED:\n        console.log('📞 [CallService] Call ended');\n        this.stopAllSounds();\n        this.play('call-end');\n        this.activeCall.next(null);\n        this.incomingCall.next(null);\n        break;\n\n      case CallStatus.CONNECTED:\n        console.log('✅ [CallService] Call connected');\n        this.stop('ringtone');\n        this.play('call-connected');\n        this.activeCall.next(call);\n        this.incomingCall.next(null);\n        break;\n\n      case CallStatus.RINGING:\n        console.log('📞 [CallService] Call is ringing');\n        this.play('ringtone', true);\n        break;\n\n      default:\n        console.log('📞 [CallService] Unknown call status:', call.status);\n        break;\n    }\n  }\n\n  /**\n   * Obtient les médias utilisateur (caméra/micro)\n   */\n  private async getUserMedia(callType: CallType): Promise<MediaStream> {\n    console.log('🎥 [CallService] Getting user media for:', callType);\n\n    const constraints: MediaStreamConstraints = {\n      audio: true,\n      video: callType === CallType.VIDEO,\n    };\n\n    try {\n      const stream = await navigator.mediaDevices.getUserMedia(constraints);\n      console.log('✅ [CallService] User media obtained');\n      this.localStream = stream;\n\n      // Afficher le stream local si on a un élément vidéo\n      if (this.localVideoElement && callType === CallType.VIDEO) {\n        this.localVideoElement.srcObject = stream;\n      }\n\n      return stream;\n    } catch (error) {\n      console.error('❌ [CallService] Failed to get user media:', error);\n      throw new Error(\"Impossible d'accéder à la caméra/microphone\");\n    }\n  }\n\n  /**\n   * Ajoute le stream local à la PeerConnection\n   */\n  private addLocalStreamToPeerConnection(stream: MediaStream): void {\n    if (!this.peerConnection) {\n      console.error('❌ [CallService] No peer connection available');\n      return;\n    }\n\n    console.log('📤 [CallService] Adding local stream to peer connection');\n    stream.getTracks().forEach((track) => {\n      this.peerConnection!.addTrack(track, stream);\n    });\n  }\n\n  /**\n   * Crée une offre WebRTC\n   */\n  private async createOffer(): Promise<RTCSessionDescriptionInit> {\n    if (!this.peerConnection) {\n      throw new Error('No peer connection available');\n    }\n\n    console.log('📝 [CallService] Creating WebRTC offer');\n    const offer = await this.peerConnection.createOffer();\n    await this.peerConnection.setLocalDescription(offer);\n    return offer;\n  }\n\n  /**\n   * Crée une réponse WebRTC\n   */\n  private async createAnswer(\n    offer: RTCSessionDescriptionInit\n  ): Promise<RTCSessionDescriptionInit> {\n    if (!this.peerConnection) {\n      throw new Error('No peer connection available');\n    }\n\n    console.log('📝 [CallService] Creating WebRTC answer');\n    await this.peerConnection.setRemoteDescription(offer);\n    const answer = await this.peerConnection.createAnswer();\n    await this.peerConnection.setLocalDescription(answer);\n    return answer;\n  }\n\n  /**\n   * Configure les éléments vidéo\n   */\n  public setVideoElements(\n    localVideo: HTMLVideoElement,\n    remoteVideo: HTMLVideoElement\n  ): void {\n    console.log('📺 [CallService] Setting video elements');\n    this.localVideoElement = localVideo;\n    this.remoteVideoElement = remoteVideo;\n\n    // Si on a déjà des streams, les connecter\n    if (this.localStream && localVideo) {\n      localVideo.srcObject = this.localStream;\n    }\n    if (this.remoteStream && remoteVideo) {\n      remoteVideo.srcObject = this.remoteStream;\n    }\n  }\n\n  /**\n   * Initie un appel WebRTC\n   */\n  initiateCall(\n    recipientId: string,\n    callType: CallType,\n    conversationId?: string\n  ): Observable<Call> {\n    console.log('🔄 [CallService] Initiating call:', {\n      recipientId,\n      callType,\n      conversationId,\n    });\n\n    if (!recipientId) {\n      const error = new Error('Recipient ID is required');\n      console.error('❌ [CallService] initiateCall error:', error);\n      return throwError(() => error);\n    }\n\n    // Générer un ID unique pour l'appel\n    const callId = `call_${Date.now()}_${Math.random()\n      .toString(36)\n      .substr(2, 9)}`;\n\n    // Créer une vraie offre WebRTC\n    return from(this.createWebRTCOffer(callType)).pipe(\n      switchMap((offer) => {\n        const variables = {\n          recipientId,\n          callType: callType,\n          callId,\n          offer: JSON.stringify(offer),\n          conversationId,\n        };\n\n        console.log(\n          '📤 [CallService] Sending initiate call mutation:',\n          variables\n        );\n\n        return this.apollo\n          .mutate<{ initiateCall: Call }>({\n            mutation: INITIATE_CALL_MUTATION,\n            variables,\n          })\n          .pipe(\n            map((result) => {\n              console.log(\n                '✅ [CallService] Call initiated successfully:',\n                result\n              );\n\n              if (!result.data?.initiateCall) {\n                throw new Error('No call data received from server');\n              }\n\n              const call = result.data.initiateCall;\n              console.log('📞 [CallService] Call details:', {\n                id: call.id,\n                type: call.type,\n                status: call.status,\n                caller: call.caller?.username,\n                recipient: call.recipient?.username,\n              });\n\n              // Mettre à jour l'état local\n              this.activeCall.next(call);\n\n              return call;\n            }),\n            catchError((error) => {\n              console.error('❌ [CallService] initiateCall error:', error);\n              this.logger.error('Error initiating call:', error);\n\n              let errorMessage = \"Erreur lors de l'initiation de l'appel\";\n              if (error.networkError) {\n                errorMessage = 'Erreur de connexion réseau';\n              } else if (error.graphQLErrors?.length > 0) {\n                errorMessage = error.graphQLErrors[0].message || errorMessage;\n              }\n\n              return throwError(() => new Error(errorMessage));\n            })\n          );\n      }),\n      catchError((error) => {\n        console.error('❌ [CallService] WebRTC error:', error);\n        return throwError(() => new Error('Erreur WebRTC: ' + error.message));\n      })\n    );\n  }\n\n  /**\n   * Accepte un appel entrant\n   */\n  acceptCall(incomingCall: IncomingCall): Observable<Call> {\n    console.log('🔄 [CallService] Accepting call:', incomingCall.id);\n\n    // Générer une réponse WebRTC factice\n    const answer = JSON.stringify({\n      type: 'answer',\n      sdp: 'v=0\\r\\no=- 0 0 IN IP4 127.0.0.1\\r\\ns=-\\r\\nt=0 0\\r\\n',\n    });\n\n    return this.apollo\n      .mutate<{ acceptCall: Call }>({\n        mutation: ACCEPT_CALL_MUTATION,\n        variables: {\n          callId: incomingCall.id,\n          answer,\n        },\n      })\n      .pipe(\n        switchMap((result) => {\n          console.log('✅ [CallService] Call accepted successfully:', result);\n\n          if (!result.data?.acceptCall) {\n            throw new Error('No call data received from server');\n          }\n\n          const call = result.data.acceptCall;\n\n          // Arrêter la sonnerie\n          this.stop('ringtone');\n          this.play('call-connected');\n\n          // Démarrer les médias pour l'appel de manière asynchrone\n          return from(this.startMediaForCall(incomingCall, call));\n        }),\n        catchError((error) => {\n          console.error('❌ [CallService] acceptCall error:', error);\n          this.logger.error('Error accepting call:', error);\n          return throwError(\n            () => new Error(\"Erreur lors de l'acceptation de l'appel\")\n          );\n        })\n      );\n  }\n\n  /**\n   * Rejette un appel entrant\n   */\n  rejectCall(callId: string, reason?: string): Observable<CallSuccess> {\n    console.log('🔄 [CallService] Rejecting call:', callId, reason);\n\n    return this.apollo\n      .mutate<{ rejectCall: CallSuccess }>({\n        mutation: REJECT_CALL_MUTATION,\n        variables: {\n          callId,\n          reason: reason || 'User rejected',\n        },\n      })\n      .pipe(\n        map((result) => {\n          console.log('✅ [CallService] Call rejected successfully:', result);\n\n          if (!result.data?.rejectCall) {\n            throw new Error('No response received from server');\n          }\n\n          // Mettre à jour l'état local\n          this.incomingCall.next(null);\n          this.activeCall.next(null);\n\n          // Arrêter la sonnerie\n          this.stop('ringtone');\n\n          return result.data.rejectCall;\n        }),\n        catchError((error) => {\n          console.error('❌ [CallService] rejectCall error:', error);\n          this.logger.error('Error rejecting call:', error);\n          return throwError(() => new Error(\"Erreur lors du rejet de l'appel\"));\n        })\n      );\n  }\n\n  /**\n   * Termine un appel en cours\n   */\n  endCall(callId: string): Observable<CallSuccess> {\n    console.log('🔄 [CallService] Ending call:', callId);\n\n    return this.apollo\n      .mutate<{ endCall: CallSuccess }>({\n        mutation: END_CALL_MUTATION,\n        variables: {\n          callId,\n          feedback: null, // Pas de feedback pour l'instant\n        },\n      })\n      .pipe(\n        map((result) => {\n          console.log('✅ [CallService] Call ended successfully:', result);\n\n          if (!result.data?.endCall) {\n            throw new Error('No response received from server');\n          }\n\n          // Mettre à jour l'état local\n          this.activeCall.next(null);\n          this.incomingCall.next(null);\n\n          // Arrêter tous les sons\n          this.stopAllSounds();\n          this.play('call-end');\n\n          return result.data.endCall;\n        }),\n        catchError((error) => {\n          console.error('❌ [CallService] endCall error:', error);\n          this.logger.error('Error ending call:', error);\n          return throwError(\n            () => new Error(\"Erreur lors de la fin de l'appel\")\n          );\n        })\n      );\n  }\n\n  /**\n   * Bascule l'état des médias (audio/vidéo) pendant un appel\n   */\n  toggleMedia(\n    callId: string,\n    enableVideo?: boolean,\n    enableAudio?: boolean\n  ): Observable<CallSuccess> {\n    console.log('🔄 [CallService] Toggling media:', {\n      callId,\n      enableVideo,\n      enableAudio,\n    });\n\n    return this.apollo\n      .mutate<{ toggleCallMedia: CallSuccess }>({\n        mutation: TOGGLE_CALL_MEDIA_MUTATION,\n        variables: {\n          callId,\n          video: enableVideo,\n          audio: enableAudio,\n        },\n      })\n      .pipe(\n        map((result) => {\n          console.log('✅ [CallService] Media toggled successfully:', result);\n\n          if (!result.data?.toggleCallMedia) {\n            throw new Error('No response received from server');\n          }\n\n          return result.data.toggleCallMedia;\n        }),\n        catchError((error) => {\n          console.error('❌ [CallService] toggleMedia error:', error);\n          this.logger.error('Error toggling media:', error);\n          return throwError(\n            () => new Error('Erreur lors du changement des médias')\n          );\n        })\n      );\n  }\n\n  /**\n   * Démarre les médias pour un appel accepté\n   */\n  private async startMediaForCall(\n    incomingCall: IncomingCall,\n    call: Call\n  ): Promise<Call> {\n    console.log('🎥 [CallService] Call connected - playing connection sound');\n\n    // Jouer le son de connexion\n    this.play('call-connected');\n\n    // Mettre à jour l'état local\n    this.activeCall.next(call);\n    this.incomingCall.next(null); // Supprimer l'appel entrant\n\n    return call;\n  }\n\n  /**\n   * Active/désactive la vidéo\n   */\n  toggleVideo(): boolean {\n    this.isVideoEnabled = !this.isVideoEnabled;\n    console.log('📹 [CallService] Video toggled:', this.isVideoEnabled);\n    return this.isVideoEnabled;\n  }\n\n  /**\n   * Active/désactive l'audio\n   */\n  toggleAudio(): boolean {\n    this.isAudioEnabled = !this.isAudioEnabled;\n    console.log('🎤 [CallService] Audio toggled:', this.isAudioEnabled);\n    return this.isAudioEnabled;\n  }\n\n  /**\n   * Obtient l'état de la vidéo\n   */\n  getVideoEnabled(): boolean {\n    return this.isVideoEnabled;\n  }\n\n  /**\n   * Obtient l'état de l'audio\n   */\n  getAudioEnabled(): boolean {\n    return this.isAudioEnabled;\n  }\n\n  /**\n   * Arrête tous les sons\n   */\n  private stopAllSounds(): void {\n    console.log('🔇 [CallService] Stopping all sounds');\n    Object.keys(this.sounds).forEach((name) => {\n      this.stop(name);\n    });\n  }\n\n  /**\n   * Active les sons après interaction utilisateur (pour contourner les restrictions du navigateur)\n   */\n  public enableSounds(): void {\n    console.log('🔊 [CallService] Enabling sounds after user interaction');\n    Object.values(this.sounds).forEach((sound) => {\n      if (sound) {\n        sound.muted = false;\n        sound.volume = 0.7;\n        // Jouer et arrêter immédiatement pour \"débloquer\" l'audio\n        sound\n          .play()\n          .then(() => {\n            sound.pause();\n            sound.currentTime = 0;\n          })\n          .catch(() => {\n            // Ignorer les erreurs ici\n          });\n      }\n    });\n  }\n\n  ngOnDestroy(): void {\n    this.stopAllSounds();\n  }\n}\n"], "mappings": ";AAEA,SAASA,eAAe,EAAcC,UAAU,EAAMC,IAAI,QAAQ,MAAM;AACxE,SAASC,GAAG,EAAEC,UAAU,EAAEC,SAAS,QAAQ,gBAAgB;AAC3D,SAEEC,QAAQ,EACRC,UAAU,QAGL,yBAAyB;AAChC,SACEC,sBAAsB,EACtBC,oBAAoB,EACpBC,oBAAoB,EACpBC,iBAAiB,EACjBC,0BAA0B,EAC1BC,0BAA0B,EAC1BC,gCAAgC,QAC3B,4BAA4B;;;;AAMnC,OAAM,MAAOC,WAAW;EAyBtBC,YAAoBC,MAAc,EAAUC,MAAqB;IAA7C,KAAAD,MAAM,GAANA,MAAM;IAAkB,KAAAC,MAAM,GAANA,MAAM;IAxBlD;IACQ,KAAAC,UAAU,GAAG,IAAInB,eAAe,CAAc,IAAI,CAAC;IACnD,KAAAoB,YAAY,GAAG,IAAIpB,eAAe,CAAsB,IAAI,CAAC;IAErE;IACO,KAAAqB,WAAW,GAAG,IAAI,CAACF,UAAU,CAACG,YAAY,EAAE;IAC5C,KAAAC,aAAa,GAAG,IAAI,CAACH,YAAY,CAACE,YAAY,EAAE;IAEvD;IACQ,KAAAE,MAAM,GAAwC,EAAE;IAChD,KAAAC,SAAS,GAA+B,EAAE;IAC1C,KAAAC,KAAK,GAAG,KAAK;IAErB;IACQ,KAAAC,cAAc,GAAG,IAAI;IACrB,KAAAC,cAAc,GAAG,IAAI;IAE7B;IACQ,KAAAC,cAAc,GAA6B,IAAI;IAC/C,KAAAC,WAAW,GAAuB,IAAI;IACtC,KAAAC,YAAY,GAAuB,IAAI;IACvC,KAAAC,iBAAiB,GAA4B,IAAI;IACjD,KAAAC,kBAAkB,GAA4B,IAAI;IAGxD,IAAI,CAACC,aAAa,EAAE;IACpB,IAAI,CAACC,uBAAuB,EAAE;IAC9B,IAAI,CAACC,gBAAgB,EAAE;EACzB;EAEA;;;EAGQD,uBAAuBA,CAAA;IAC7B;IACAE,UAAU,CAAC,MAAK;MACd,IAAI,CAACC,wBAAwB,EAAE;MAC/B,IAAI,CAACC,4BAA4B,EAAE;IACrC,CAAC,EAAE,IAAI,CAAC;IAER;IACAF,UAAU,CAAC,MAAK;MACd,IAAI,CAAC,IAAI,CAACjB,YAAY,CAACoB,KAAK,EAAE;QAC5B;QACA,IAAI,CAACF,wBAAwB,EAAE;QAC/B,IAAI,CAACC,4BAA4B,EAAE;;IAEvC,CAAC,EAAE,KAAK,CAAC;EACX;EAEA;;;EAGQH,gBAAgBA,CAAA;IACtBK,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;IAEtD;IACA,MAAMC,aAAa,GAAqB;MACtCC,UAAU,EAAE,CACV;QAAEC,IAAI,EAAE;MAA8B,CAAE,EACxC;QAAEA,IAAI,EAAE;MAA+B,CAAE;KAE5C;IAED,IAAI;MACF,IAAI,CAAChB,cAAc,GAAG,IAAIiB,iBAAiB,CAACH,aAAa,CAAC;MAC1D,IAAI,CAACI,yBAAyB,EAAE;MAChCN,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;KAC/D,CAAC,OAAOM,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CAAC,8CAA8C,EAAEA,KAAK,CAAC;;EAExE;EAEA;;;EAGQD,yBAAyBA,CAAA;IAC/B,IAAI,CAAC,IAAI,CAAClB,cAAc,EAAE;IAE1B;IACA,IAAI,CAACA,cAAc,CAACoB,OAAO,GAAIC,KAAK,IAAI;MACtCT,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;MACtD,IAAI,CAACX,YAAY,GAAGmB,KAAK,CAACC,OAAO,CAAC,CAAC,CAAC;MACpC,IAAI,IAAI,CAAClB,kBAAkB,EAAE;QAC3B,IAAI,CAACA,kBAAkB,CAACmB,SAAS,GAAG,IAAI,CAACrB,YAAY;;IAEzD,CAAC;IAED;IACA,IAAI,CAACF,cAAc,CAACwB,cAAc,GAAIH,KAAK,IAAI;MAC7C,IAAIA,KAAK,CAACI,SAAS,EAAE;QACnBb,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;QACvD;;IAEJ,CAAC;IAED;IACA,IAAI,CAACb,cAAc,CAAC0B,uBAAuB,GAAG,MAAK;MACjDd,OAAO,CAACC,GAAG,CACT,oCAAoC,EACpC,IAAI,CAACb,cAAc,EAAE2B,eAAe,CACrC;IACH,CAAC;EACH;EAEA;;;EAGQtB,aAAaA,CAAA;IACnB;IACA,IAAI,CAACuB,qBAAqB,EAAE;IAE5B;IACA,IAAI,CAACC,SAAS,CAAC,cAAc,EAAE,gCAAgC,CAAC;IAEhEjB,OAAO,CAACC,GAAG,CACT,iEAAiE,CAClE;EACH;EAEA;;;EAGQe,qBAAqBA,CAAA;IAC3B,IAAI;MACF;MACA,MAAME,YAAY,GAAG,KAAKC,MAAM,CAACC,YAAY,IAC1CD,MAAc,CAACE,kBAAkB,EAAC,CAAE;MAEvC;MACA,IAAI,CAACtC,MAAM,CAAC,oBAAoB,CAAC,GAC/B,IAAI,CAACuC,mBAAmB,CAACJ,YAAY,CAAC;MAExC;MACA,IAAI,CAACnC,MAAM,CAAC,0BAA0B,CAAC,GACrC,IAAI,CAACwC,oBAAoB,CAACL,YAAY,CAAC;MAEzC;MACA,IAAI,CAACnC,MAAM,CAAC,oBAAoB,CAAC,GAAG,IAAI,CAACyC,cAAc,CAACN,YAAY,CAAC;MAErE;MACA,IAAI,CAACnC,MAAM,CAAC,wBAAwB,CAAC,GACnC,IAAI,CAAC0C,uBAAuB,CAACP,YAAY,CAAC;MAE5ClB,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;KACrE,CAAC,OAAOM,KAAK,EAAE;MACdP,OAAO,CAAC0B,IAAI,CACV,qDAAqD,EACrDnB,KAAK,CACN;;EAEL;EAEA;;;EAGQe,mBAAmBA,CAACJ,YAA0B;IACpD,MAAMS,KAAK,GAAG,IAAIC,KAAK,EAAE;IACzBD,KAAK,CAACE,MAAM,GAAG,GAAG;IAElB,IAAI7C,SAAS,GAAG,KAAK;IACrB,IAAI8C,UAAU,GAAU,EAAE;IAEzBH,KAAa,CAACI,aAAa,GAAG,MAAK;MAClC,IAAI/C,SAAS,EAAE,OAAOgD,OAAO,CAACC,OAAO,EAAE;MAEvCjD,SAAS,GAAG,IAAI;MAEhB,MAAMkD,UAAU,GAAGA,CAAA,KAAK;QACtB,IAAI,CAAClD,SAAS,EAAE;QAEhB;QACA,MAAMmD,MAAM,GAAG,CACb;UAAEC,IAAI,EAAE,MAAM;UAAEC,QAAQ,EAAE;QAAK,CAAE,EACjC;UAAED,IAAI,EAAE,MAAM;UAAEC,QAAQ,EAAE;QAAK,CAAE,EACjC;UAAED,IAAI,EAAE,MAAM;UAAEC,QAAQ,EAAE;QAAI,CAAE,EAChC;UAAED,IAAI,EAAE,MAAM;UAAEC,QAAQ,EAAE;QAAI,CAAE,EAChC;UAAED,IAAI,EAAE,MAAM;UAAEC,QAAQ,EAAE;QAAK,CAAE,EACjC;UAAED,IAAI,EAAE,MAAM;UAAEC,QAAQ,EAAE;QAAK,CAAE,EACjC;UAAED,IAAI,EAAE,MAAM;UAAEC,QAAQ,EAAE;QAAI,CAAE,EAChC;UAAED,IAAI,EAAE,MAAM;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAE;QAAA,CACnC;;QAED,IAAIC,WAAW,GAAGpB,YAAY,CAACoB,WAAW;QAE1CH,MAAM,CAACI,OAAO,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAI;UAC7B,IAAI,CAACzD,SAAS,EAAE;UAEhB,MAAM0D,UAAU,GAAGxB,YAAY,CAACyB,gBAAgB,EAAE;UAClD,MAAMC,QAAQ,GAAG1B,YAAY,CAAC2B,UAAU,EAAE;UAE1CH,UAAU,CAACI,OAAO,CAACF,QAAQ,CAAC;UAC5BA,QAAQ,CAACE,OAAO,CAAC5B,YAAY,CAAC6B,WAAW,CAAC;UAE1CL,UAAU,CAACM,SAAS,CAACjD,KAAK,GAAGyC,IAAI,CAACJ,IAAI;UACtCM,UAAU,CAACO,IAAI,GAAG,QAAQ,CAAC,CAAC;UAE5B;UACAL,QAAQ,CAACM,IAAI,CAACC,cAAc,CAAC,CAAC,EAAEb,WAAW,CAAC;UAC5CM,QAAQ,CAACM,IAAI,CAACE,uBAAuB,CAAC,GAAG,EAAEd,WAAW,GAAG,IAAI,CAAC;UAC9DM,QAAQ,CAACM,IAAI,CAACE,uBAAuB,CACnC,GAAG,EACHd,WAAW,GAAGE,IAAI,CAACH,QAAQ,GAAG,GAAG,CAClC;UACDO,QAAQ,CAACM,IAAI,CAACG,4BAA4B,CACxC,IAAI,EACJf,WAAW,GAAGE,IAAI,CAACH,QAAQ,CAC5B;UAEDK,UAAU,CAACY,KAAK,CAAChB,WAAW,CAAC;UAC7BI,UAAU,CAACa,IAAI,CAACjB,WAAW,GAAGE,IAAI,CAACH,QAAQ,CAAC;UAE5CC,WAAW,IAAIE,IAAI,CAACH,QAAQ,GAAG,IAAI,CAAC,CAAC;QACvC,CAAC,CAAC;QAEF;QACA,MAAMmB,SAAS,GAAG5D,UAAU,CAAC,MAAK;UAChC,IAAIZ,SAAS,EAAE;YACbkD,UAAU,EAAE;;QAEhB,CAAC,EAAE,CAACI,WAAW,GAAGpB,YAAY,CAACoB,WAAW,GAAG,GAAG,IAAI,IAAI,CAAC;QAEzDR,UAAU,CAAC2B,IAAI,CAACD,SAAS,CAAC;MAC5B,CAAC;MAEDtB,UAAU,EAAE;MACZ,OAAOF,OAAO,CAACC,OAAO,EAAE;IAC1B,CAAC;IAEAN,KAAa,CAAC+B,aAAa,GAAG,MAAK;MAClC1E,SAAS,GAAG,KAAK;MACjB8C,UAAU,CAACS,OAAO,CAAEoB,EAAE,IAAKC,YAAY,CAACD,EAAE,CAAC,CAAC;MAC5C7B,UAAU,GAAG,EAAE;IACjB,CAAC;IAED,OAAOH,KAAK;EACd;EAEA;;;EAGQJ,oBAAoBA,CAACL,YAA0B;IACrD,MAAMS,KAAK,GAAG,IAAIC,KAAK,EAAE;IACzBD,KAAK,CAACE,MAAM,GAAG,GAAG;IAEjBF,KAAa,CAACI,aAAa,GAAG,MAAK;MAClC;MACA,MAAMI,MAAM,GAAG,CACb;QAAEC,IAAI,EAAE,MAAM;QAAEC,QAAQ,EAAE;MAAI,CAAE,EAChC;QAAED,IAAI,EAAE,MAAM;QAAEC,QAAQ,EAAE;MAAI,CAAE,EAChC;QAAED,IAAI,EAAE,MAAM;QAAEC,QAAQ,EAAE;MAAI,CAAE,EAChC;QAAED,IAAI,EAAE,MAAM;QAAEC,QAAQ,EAAE;MAAG,CAAE,CAAE;MAAA,CAClC;;MAED,IAAIC,WAAW,GAAGpB,YAAY,CAACoB,WAAW;MAE1CH,MAAM,CAACI,OAAO,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAI;QAC7B,MAAMC,UAAU,GAAGxB,YAAY,CAACyB,gBAAgB,EAAE;QAClD,MAAMC,QAAQ,GAAG1B,YAAY,CAAC2B,UAAU,EAAE;QAE1CH,UAAU,CAACI,OAAO,CAACF,QAAQ,CAAC;QAC5BA,QAAQ,CAACE,OAAO,CAAC5B,YAAY,CAAC6B,WAAW,CAAC;QAE1CL,UAAU,CAACM,SAAS,CAACjD,KAAK,GAAGyC,IAAI,CAACJ,IAAI;QACtCM,UAAU,CAACO,IAAI,GAAG,UAAU,CAAC,CAAC;QAE9B;QACAL,QAAQ,CAACM,IAAI,CAACC,cAAc,CAAC,CAAC,EAAEb,WAAW,CAAC;QAC5CM,QAAQ,CAACM,IAAI,CAACE,uBAAuB,CAAC,IAAI,EAAEd,WAAW,GAAG,IAAI,CAAC;QAC/DM,QAAQ,CAACM,IAAI,CAACE,uBAAuB,CACnC,IAAI,EACJd,WAAW,GAAGE,IAAI,CAACH,QAAQ,GAAG,GAAG,CAClC;QACDO,QAAQ,CAACM,IAAI,CAACG,4BAA4B,CACxC,IAAI,EACJf,WAAW,GAAGE,IAAI,CAACH,QAAQ,CAC5B;QAEDK,UAAU,CAACY,KAAK,CAAChB,WAAW,CAAC;QAC7BI,UAAU,CAACa,IAAI,CAACjB,WAAW,GAAGE,IAAI,CAACH,QAAQ,CAAC;QAE5CC,WAAW,IAAIE,IAAI,CAACH,QAAQ,GAAG,GAAG,CAAC,CAAC;MACtC,CAAC,CAAC;;MAEF,OAAOL,OAAO,CAACC,OAAO,EAAE;IAC1B,CAAC;IAED,OAAON,KAAK;EACd;EAEA;;;EAGQH,cAAcA,CAACN,YAA0B;IAC/C,MAAMS,KAAK,GAAG,IAAIC,KAAK,EAAE;IACzBD,KAAK,CAACE,MAAM,GAAG,GAAG;IAEjBF,KAAa,CAACI,aAAa,GAAG,MAAK;MAClC;MACA,MAAMI,MAAM,GAAG,CACb;QAAEC,IAAI,EAAE,MAAM;QAAEC,QAAQ,EAAE;MAAG,CAAE,EAC/B;QAAED,IAAI,EAAE,MAAM;QAAEC,QAAQ,EAAE;MAAG,CAAE,EAC/B;QAAED,IAAI,EAAE,MAAM;QAAEC,QAAQ,EAAE;MAAG,CAAE,EAC/B;QAAED,IAAI,EAAE,KAAK;QAAEC,QAAQ,EAAE;MAAG,CAAE,CAAE;MAAA,CACjC;;MAED,IAAIC,WAAW,GAAGpB,YAAY,CAACoB,WAAW;MAE1CH,MAAM,CAACI,OAAO,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAI;QAC7B,MAAMC,UAAU,GAAGxB,YAAY,CAACyB,gBAAgB,EAAE;QAClD,MAAMC,QAAQ,GAAG1B,YAAY,CAAC2B,UAAU,EAAE;QAE1CH,UAAU,CAACI,OAAO,CAACF,QAAQ,CAAC;QAC5BA,QAAQ,CAACE,OAAO,CAAC5B,YAAY,CAAC6B,WAAW,CAAC;QAE1CL,UAAU,CAACM,SAAS,CAACjD,KAAK,GAAGyC,IAAI,CAACJ,IAAI;QACtCM,UAAU,CAACO,IAAI,GAAG,MAAM,CAAC,CAAC;QAE1B;QACAL,QAAQ,CAACM,IAAI,CAACC,cAAc,CAAC,CAAC,EAAEb,WAAW,CAAC;QAC5CM,QAAQ,CAACM,IAAI,CAACE,uBAAuB,CAAC,GAAG,EAAEd,WAAW,GAAG,IAAI,CAAC;QAC9DM,QAAQ,CAACM,IAAI,CAACE,uBAAuB,CACnC,GAAG,EACHd,WAAW,GAAGE,IAAI,CAACH,QAAQ,GAAG,GAAG,CAClC;QACDO,QAAQ,CAACM,IAAI,CAACG,4BAA4B,CACxC,IAAI,EACJf,WAAW,GAAGE,IAAI,CAACH,QAAQ,CAC5B;QAEDK,UAAU,CAACY,KAAK,CAAChB,WAAW,CAAC;QAC7BI,UAAU,CAACa,IAAI,CAACjB,WAAW,GAAGE,IAAI,CAACH,QAAQ,CAAC;QAE5CC,WAAW,IAAIE,IAAI,CAACH,QAAQ,GAAG,GAAG,CAAC,CAAC;MACtC,CAAC,CAAC;;MAEF,OAAOL,OAAO,CAACC,OAAO,EAAE;IAC1B,CAAC;IAED,OAAON,KAAK;EACd;EAEA;;;EAGQF,uBAAuBA,CAC7BP,YAA0B;IAE1B,MAAMS,KAAK,GAAG,IAAIC,KAAK,EAAE;IACzBD,KAAK,CAACE,MAAM,GAAG,GAAG;IAEjBF,KAAa,CAACI,aAAa,GAAG,MAAK;MAClC;MACA,MAAM8B,KAAK,GAAG,CACZ;QAAEzB,IAAI,EAAE,MAAM;QAAEC,QAAQ,EAAE,IAAI;QAAEyB,KAAK,EAAE;MAAC,CAAE,EAC1C;QAAE1B,IAAI,EAAE,MAAM;QAAEC,QAAQ,EAAE,IAAI;QAAEyB,KAAK,EAAE;MAAG,CAAE,CAAE;MAAA,CAC/C;;MAEDD,KAAK,CAACtB,OAAO,CAAEC,IAAI,IAAI;QACrB5C,UAAU,CAAC,MAAK;UACd,MAAM8C,UAAU,GAAGxB,YAAY,CAACyB,gBAAgB,EAAE;UAClD,MAAMC,QAAQ,GAAG1B,YAAY,CAAC2B,UAAU,EAAE;UAE1CH,UAAU,CAACI,OAAO,CAACF,QAAQ,CAAC;UAC5BA,QAAQ,CAACE,OAAO,CAAC5B,YAAY,CAAC6B,WAAW,CAAC;UAE1CL,UAAU,CAACM,SAAS,CAACjD,KAAK,GAAGyC,IAAI,CAACJ,IAAI;UACtCM,UAAU,CAACO,IAAI,GAAG,UAAU,CAAC,CAAC;UAE9B,MAAMc,SAAS,GAAG7C,YAAY,CAACoB,WAAW;UAE1C;UACAM,QAAQ,CAACM,IAAI,CAACC,cAAc,CAAC,CAAC,EAAEY,SAAS,CAAC;UAC1CnB,QAAQ,CAACM,IAAI,CAACE,uBAAuB,CAAC,GAAG,EAAEW,SAAS,GAAG,IAAI,CAAC;UAC5DnB,QAAQ,CAACM,IAAI,CAACE,uBAAuB,CACnC,GAAG,EACHW,SAAS,GAAGvB,IAAI,CAACH,QAAQ,GAAG,GAAG,CAChC;UACDO,QAAQ,CAACM,IAAI,CAACG,4BAA4B,CACxC,IAAI,EACJU,SAAS,GAAGvB,IAAI,CAACH,QAAQ,CAC1B;UAEDK,UAAU,CAACY,KAAK,CAACS,SAAS,CAAC;UAC3BrB,UAAU,CAACa,IAAI,CAACQ,SAAS,GAAGvB,IAAI,CAACH,QAAQ,CAAC;QAC5C,CAAC,EAAEG,IAAI,CAACsB,KAAK,GAAG,IAAI,CAAC;MACvB,CAAC,CAAC;MAEF,OAAO9B,OAAO,CAACC,OAAO,EAAE;IAC1B,CAAC;IAED,OAAON,KAAK;EACd;EAEA;;;EAGQV,SAASA,CAAC+C,IAAY,EAAEC,IAAY;IAC1C,IAAI;MACF,MAAMtC,KAAK,GAAG,IAAIC,KAAK,EAAE;MAEzB;MACAD,KAAK,CAACuC,OAAO,GAAG,MAAM;MACtBvC,KAAK,CAACE,MAAM,GAAG,GAAG;MAElB;MACAF,KAAK,CAACwC,gBAAgB,CAAC,gBAAgB,EAAE,MAAK;QAC5CnE,OAAO,CAACC,GAAG,CACT,yBAAyB+D,IAAI,6BAA6BC,IAAI,EAAE,CACjE;MACH,CAAC,CAAC;MAEFtC,KAAK,CAACwC,gBAAgB,CAAC,OAAO,EAAGC,CAAC,IAAI;QACpCpE,OAAO,CAACO,KAAK,CACX,uCAAuCyD,IAAI,SAASC,IAAI,GAAG,EAC3DG,CAAC,CACF;QACDpE,OAAO,CAACC,GAAG,CACT,mCAAmC+D,IAAI,6BAA6B,CACrE;QAED;QACA,MAAMK,OAAO,GAAGJ,IAAI,CAACK,UAAU,CAAC,GAAG,CAAC,GAAGL,IAAI,CAACM,SAAS,CAAC,CAAC,CAAC,GAAGN,IAAI;QAC/D,IAAII,OAAO,KAAKJ,IAAI,EAAE;UACpBrE,UAAU,CAAC,MAAK;YACd+B,KAAK,CAAC6C,GAAG,GAAGH,OAAO;YACnB1C,KAAK,CAAC8C,IAAI,EAAE;UACd,CAAC,EAAE,GAAG,CAAC;;MAEX,CAAC,CAAC;MAEF9C,KAAK,CAACwC,gBAAgB,CAAC,OAAO,EAAE,MAAK;QACnC,IAAI,CAACnF,SAAS,CAACgF,IAAI,CAAC,GAAG,KAAK;QAC5BhE,OAAO,CAACC,GAAG,CAAC,0BAA0B+D,IAAI,QAAQ,CAAC;MACrD,CAAC,CAAC;MAEF;MACArC,KAAK,CAAC6C,GAAG,GAAGP,IAAI;MAChBtC,KAAK,CAAC8C,IAAI,EAAE;MAEZ,IAAI,CAAC1F,MAAM,CAACiF,IAAI,CAAC,GAAGrC,KAAK;MACzB,IAAI,CAAC3C,SAAS,CAACgF,IAAI,CAAC,GAAG,KAAK;MAE5BhE,OAAO,CAACC,GAAG,CAAC,kCAAkC+D,IAAI,SAASC,IAAI,EAAE,CAAC;KACnE,CAAC,OAAO1D,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CACX,oDAAoDyD,IAAI,GAAG,EAC3DzD,KAAK,CACN;;EAEL;EAEA;;;EAGQmE,IAAIA,CAACV,IAAY,EAAEW,IAAA,GAAgB,KAAK;IAC9C,IAAI,IAAI,CAAC1F,KAAK,EAAE;MACde,OAAO,CAACC,GAAG,CAAC,0BAA0B+D,IAAI,QAAQ,CAAC;MACnD;;IAGF,IAAI;MACF;MACA,IAAIY,KAAK;MACT,IACEZ,IAAI,KAAK,UAAU,IACnBA,IAAI,KAAK,gBAAgB,IACzBA,IAAI,KAAK,UAAU,EACnB;QACA,MAAMa,aAAa,GAAG,GAAGb,IAAI,YAAY;QACzCY,KAAK,GAAG,IAAI,CAAC7F,MAAM,CAAC8F,aAAa,CAAC;QAClC,IAAID,KAAK,EAAE;UACT5E,OAAO,CAACC,GAAG,CACT,yDAAyD+D,IAAI,EAAE,CAChE;;OAEJ,MAAM;QACL;QACAY,KAAK,GAAG,IAAI,CAAC7F,MAAM,CAACiF,IAAI,CAAC;QACzB,IAAI,CAACY,KAAK,IAAIA,KAAK,CAACrE,KAAK,EAAE;UACzB,MAAMsE,aAAa,GAAG,GAAGb,IAAI,YAAY;UACzCY,KAAK,GAAG,IAAI,CAAC7F,MAAM,CAAC8F,aAAa,CAAC;UAClC,IAAID,KAAK,EAAE;YACT5E,OAAO,CAACC,GAAG,CAAC,8CAA8C+D,IAAI,EAAE,CAAC;;;;MAKvE,IAAI,CAACY,KAAK,EAAE;QACV5E,OAAO,CAAC0B,IAAI,CACV,0BAA0BsC,IAAI,6CAA6C,CAC5E;QACD;QACA,IAAI,CAACc,cAAc,CACjBd,IAAI,KAAK,UAAU,GAAG,GAAG,GAAGA,IAAI,KAAK,gBAAgB,GAAG,IAAI,GAAG,GAAG,CACnE;QACD;;MAGFY,KAAK,CAACD,IAAI,GAAGA,IAAI;MACjBC,KAAK,CAAC/C,MAAM,GAAG,GAAG;MAElB,IAAI,CAAC,IAAI,CAAC7C,SAAS,CAACgF,IAAI,CAAC,EAAE;QACzBhE,OAAO,CAACC,GAAG,CAAC,mCAAmC+D,IAAI,WAAWW,IAAI,GAAG,CAAC;QAEtE;QACA,IAAKC,KAAa,CAAC7C,aAAa,EAAE;UAC/B6C,KAAa,CACX7C,aAAa,EAAE,CACfgD,IAAI,CAAC,MAAK;YACT/E,OAAO,CAACC,GAAG,CACT,mCAAmC+D,IAAI,uBAAuB,CAC/D;YACD,IAAI,CAAChF,SAAS,CAACgF,IAAI,CAAC,GAAG,IAAI;YAE3B;YACA,IAAIA,IAAI,KAAK,UAAU,IAAI,CAACW,IAAI,EAAE;cAChC;cACA/E,UAAU,CAAC,MAAK;gBACd,IAAI,CAACZ,SAAS,CAACgF,IAAI,CAAC,GAAG,KAAK;cAC9B,CAAC,EAAE,IAAI,CAAC;aACT,MAAM,IAAIA,IAAI,KAAK,UAAU,EAAE;cAC9B;cACApE,UAAU,CACR,MAAK;gBACH,IAAI,CAACZ,SAAS,CAACgF,IAAI,CAAC,GAAG,KAAK;cAC9B,CAAC,EACDA,IAAI,KAAK,gBAAgB,GAAG,IAAI,GAAG,IAAI,CACxC;;UAEL,CAAC,CAAC,CACDgB,KAAK,CAAEzE,KAAU,IAAI;YACpBP,OAAO,CAACO,KAAK,CACX,iDAAiDyD,IAAI,GAAG,EACxDzD,KAAK,CACN;UACH,CAAC,CAAC;SACL,MAAM;UACL;UACAqE,KAAK,CAACtC,WAAW,GAAG,CAAC;UACrBsC,KAAK,CACFF,IAAI,EAAE,CACNK,IAAI,CAAC,MAAK;YACT/E,OAAO,CAACC,GAAG,CACT,yBAAyB+D,IAAI,uBAAuB,CACrD;YACD,IAAI,CAAChF,SAAS,CAACgF,IAAI,CAAC,GAAG,IAAI;UAC7B,CAAC,CAAC,CACDgB,KAAK,CAAEzE,KAAK,IAAI;YACfP,OAAO,CAACO,KAAK,CACX,uCAAuCyD,IAAI,GAAG,EAC9CzD,KAAK,CACN;YAED;YACA,MAAMsE,aAAa,GAAG,GAAGb,IAAI,YAAY;YACzC,MAAMiB,cAAc,GAAG,IAAI,CAAClG,MAAM,CAAC8F,aAAa,CAAC;YACjD,IAAII,cAAc,IAAKA,cAAsB,CAAClD,aAAa,EAAE;cAC3D/B,OAAO,CAACC,GAAG,CACT,wDAAwD+D,IAAI,EAAE,CAC/D;cACD,IAAI,CAACU,IAAI,CAACV,IAAI,EAAEW,IAAI,CAAC;aACtB,MAAM;cACL;cACA,IAAI,CAACG,cAAc,CACjBd,IAAI,KAAK,UAAU,GACf,GAAG,GACHA,IAAI,KAAK,gBAAgB,GACzB,IAAI,GACJ,GAAG,CACR;;UAEL,CAAC,CAAC;;OAEP,MAAM;QACLhE,OAAO,CAACC,GAAG,CAAC,0BAA0B+D,IAAI,kBAAkB,CAAC;;KAEhE,CAAC,OAAOzD,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CACX,4CAA4CyD,IAAI,GAAG,EACnDzD,KAAK,CACN;MACD;MACA,IAAI,CAACuE,cAAc,CACjBd,IAAI,KAAK,UAAU,GAAG,GAAG,GAAGA,IAAI,KAAK,gBAAgB,GAAG,IAAI,GAAG,GAAG,CACnE;;EAEL;EAEA;;;EAGQc,cAAcA,CAAC9B,SAAiB;IACtC,IAAI;MACF,MAAM9B,YAAY,GAAG,KAAKC,MAAM,CAACC,YAAY,IAC1CD,MAAc,CAACE,kBAAkB,EAAC,CAAE;MACvC,MAAMqB,UAAU,GAAGxB,YAAY,CAACyB,gBAAgB,EAAE;MAClD,MAAMC,QAAQ,GAAG1B,YAAY,CAAC2B,UAAU,EAAE;MAE1CH,UAAU,CAACI,OAAO,CAACF,QAAQ,CAAC;MAC5BA,QAAQ,CAACE,OAAO,CAAC5B,YAAY,CAAC6B,WAAW,CAAC;MAE1CL,UAAU,CAACM,SAAS,CAACjD,KAAK,GAAGiD,SAAS;MACtCN,UAAU,CAACO,IAAI,GAAG,MAAM;MAExBL,QAAQ,CAACM,IAAI,CAACC,cAAc,CAAC,GAAG,EAAEjC,YAAY,CAACoB,WAAW,CAAC;MAC3DM,QAAQ,CAACM,IAAI,CAACG,4BAA4B,CACxC,IAAI,EACJnC,YAAY,CAACoB,WAAW,GAAG,GAAG,CAC/B;MAEDI,UAAU,CAACY,KAAK,CAACpC,YAAY,CAACoB,WAAW,CAAC;MAC1CI,UAAU,CAACa,IAAI,CAACrC,YAAY,CAACoB,WAAW,GAAG,GAAG,CAAC;MAE/CtC,OAAO,CAACC,GAAG,CAAC,2CAA2C+C,SAAS,IAAI,CAAC;KACtE,CAAC,OAAOzC,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CAAC,6CAA6C,EAAEA,KAAK,CAAC;;EAEvE;EAEA;;;EAGQgD,IAAIA,CAACS,IAAY;IACvB,IAAI;MACF,IAAIY,KAAK,GAAG,IAAI,CAAC7F,MAAM,CAACiF,IAAI,CAAC;MAE7B;MACA,IAAI,CAACY,KAAK,EAAE;QACVA,KAAK,GAAG,IAAI,CAAC7F,MAAM,CAAC,GAAGiF,IAAI,YAAY,CAAC;;MAG1C,IAAI,CAACY,KAAK,EAAE;QACV5E,OAAO,CAAC0B,IAAI,CAAC,0BAA0BsC,IAAI,yBAAyB,CAAC;QACrE;;MAGF,IAAI,IAAI,CAAChF,SAAS,CAACgF,IAAI,CAAC,EAAE;QACxBhE,OAAO,CAACC,GAAG,CAAC,oCAAoC+D,IAAI,EAAE,CAAC;QAEvD;QACA,IAAKY,KAAa,CAAClB,aAAa,EAAE;UAC/BkB,KAAa,CAAClB,aAAa,EAAE;SAC/B,MAAM;UACLkB,KAAK,CAACM,KAAK,EAAE;UACbN,KAAK,CAACtC,WAAW,GAAG,CAAC;;QAGvB,IAAI,CAACtD,SAAS,CAACgF,IAAI,CAAC,GAAG,KAAK;OAC7B,MAAM;QACLhE,OAAO,CAACC,GAAG,CAAC,0BAA0B+D,IAAI,kBAAkB,CAAC;;KAEhE,CAAC,OAAOzD,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CAAC,wCAAwCyD,IAAI,GAAG,EAAEzD,KAAK,CAAC;;EAEzE;EAEA;;;EAGQV,wBAAwBA,CAAA;IAC9B;IAEA,IAAI;MACF,IAAI,CAACrB,MAAM,CACR2G,SAAS,CAAiC;QACzCC,KAAK,EAAEhH,0BAA0B;QACjCiH,WAAW,EAAE,KAAK,CAAE;OACrB,CAAC,CACDF,SAAS,CAAC;QACTG,IAAI,EAAEA,CAAC;UAAEC,IAAI;UAAEC;QAAM,CAAE,KAAI;UACzB,IAAIA,MAAM,EAAE;YACVxF,OAAO,CAAC0B,IAAI,CACV,kDAAkD,EAClD8D,MAAM,CACP;;UAGH,IAAID,IAAI,EAAE5G,YAAY,EAAE;YACtB;YACA,IAAI,CAAC8G,kBAAkB,CAACF,IAAI,CAAC5G,YAAY,CAAC;;QAE9C,CAAC;QACD4B,KAAK,EAAGA,KAAK,IAAI;UACfP,OAAO,CAACO,KAAK,CACX,sDAAsD,EACtDA,KAAK,CACN;UAED;UACAX,UAAU,CAAC,MAAK;YACd,IAAI,CAACC,wBAAwB,EAAE;UACjC,CAAC,EAAE,KAAK,CAAC;QACX,CAAC;QACD6F,QAAQ,EAAEA,CAAA,KAAK;UACb;UACA;UACA9F,UAAU,CAAC,MAAK;YACd,IAAI,CAACC,wBAAwB,EAAE;UACjC,CAAC,EAAE,IAAI,CAAC;QACV;OACD,CAAC;KACL,CAAC,OAAOU,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CAAC,gDAAgD,EAAEA,KAAK,CAAC;MACtE;MACAX,UAAU,CAAC,MAAK;QACd,IAAI,CAACC,wBAAwB,EAAE;MACjC,CAAC,EAAE,KAAK,CAAC;;EAEb;EAEA;;;EAGO8F,wBAAwBA,CAAA;IAC7B;IACA,IAAI,CAAC9F,wBAAwB,EAAE;IAC/B,IAAI,CAACC,4BAA4B,EAAE;EACrC;EAEA;;;EAGO8F,UAAUA,CAAA;IACf5F,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;IAEjD;IACAD,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;IACrC,IAAI,CAACyE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC;IAE3B9E,UAAU,CAAC,MAAK;MACd,IAAI,CAAC2D,IAAI,CAAC,UAAU,CAAC;MACrBvD,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;MAC3C,IAAI,CAACyE,IAAI,CAAC,gBAAgB,CAAC;IAC7B,CAAC,EAAE,IAAI,CAAC;IAER9E,UAAU,CAAC,MAAK;MACdI,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;MACrC,IAAI,CAACyE,IAAI,CAAC,UAAU,CAAC;IACvB,CAAC,EAAE,IAAI,CAAC;EACV;EAEA;;;EAGOmB,YAAYA,CAAA;IACjB7F,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC;IAChED,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAE6F,MAAM,CAACC,IAAI,CAAC,IAAI,CAAChH,MAAM,CAAC,CAAC;IAE3E;IACA,MAAMiH,QAAQ,GAAG,IAAI,CAACjH,MAAM,CAAC,UAAU,CAAC;IACxC,MAAMkH,iBAAiB,GAAG,IAAI,CAAClH,MAAM,CAAC,oBAAoB,CAAC;IAE3D,IAAIiH,QAAQ,EAAE;MACZhG,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAE;QACjDuE,GAAG,EAAEwB,QAAQ,CAACxB,GAAG;QACjB0B,UAAU,EAAEF,QAAQ,CAACE,UAAU;QAC/B3F,KAAK,EAAEyF,QAAQ,CAACzF,KAAK;QACrB8B,QAAQ,EAAE2D,QAAQ,CAAC3D;OACpB,CAAC;KACH,MAAM;MACLrC,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;;IAGvD,IAAIgG,iBAAiB,EAAE;MACrBjG,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;;IAGzD;IACAD,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;IACpE,IAAI,CAACyE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC;IAE3B;IACA9E,UAAU,CAAC,MAAK;MACd,IAAI,CAAC2D,IAAI,CAAC,UAAU,CAAC;MACrBvD,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;IACvD,CAAC,EAAE,IAAI,CAAC;EACV;EAEA;;;EAGOkG,mBAAmBA,CAAA;IACxBnG,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;IAE/D;IACA;IACA,IAAI,CAACyE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC;IAE3B9E,UAAU,CAAC,MAAK;MACd,IAAI,CAAC2D,IAAI,CAAC,UAAU,CAAC;MACrB;MACA,IAAI,CAACmB,IAAI,CAAC,gBAAgB,CAAC;IAC7B,CAAC,EAAE,IAAI,CAAC;IAER9E,UAAU,CAAC,MAAK;MACd;MACA,IAAI,CAAC8E,IAAI,CAAC,UAAU,CAAC;IACvB,CAAC,EAAE,IAAI,CAAC;IAER9E,UAAU,CAAC,MAAK;MACd;MACA,IAAI,CAAC8E,IAAI,CAAC,cAAc,CAAC;IAC3B,CAAC,EAAE,IAAI,CAAC;IAER9E,UAAU,CAAC,MAAK;MACdI,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;IAC9C,CAAC,EAAE,KAAK,CAAC;EACX;EAEA;;;EAGOmG,gBAAgBA,CAAA;IACrBpG,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;IAC7D,IAAI,CAACyE,IAAI,CAAC,cAAc,CAAC;EAC3B;EAEA;;;EAGQ5E,4BAA4BA,CAAA;IAClCE,OAAO,CAACC,GAAG,CACT,gEAAgE,CACjE;IAED,IAAI;MACF,IAAI,CAACzB,MAAM,CACR2G,SAAS,CAA8B;QACtCC,KAAK,EAAE/G,gCAAgC;QACvCgH,WAAW,EAAE;OACd,CAAC,CACDF,SAAS,CAAC;QACTG,IAAI,EAAEA,CAAC;UAAEC,IAAI;UAAEC;QAAM,CAAE,KAAI;UACzB,IAAIA,MAAM,EAAE;YACVxF,OAAO,CAAC0B,IAAI,CACV,8DAA8D,EAC9D8D,MAAM,CACP;;UAGH,IAAID,IAAI,EAAEc,iBAAiB,EAAE;YAC3BrG,OAAO,CAACC,GAAG,CACT,uCAAuC,EACvCsF,IAAI,CAACc,iBAAiB,CACvB;YACD,IAAI,CAACC,sBAAsB,CAACf,IAAI,CAACc,iBAAiB,CAAC;;QAEvD,CAAC;QACD9F,KAAK,EAAGA,KAAK,IAAI;UACfP,OAAO,CAACO,KAAK,CACX,oDAAoD,EACpDA,KAAK,CACN;UACD;UACAX,UAAU,CAAC,MAAK;YACd,IAAI,CAACE,4BAA4B,EAAE;UACrC,CAAC,EAAE,IAAI,CAAC;QACV,CAAC;QACD4F,QAAQ,EAAEA,CAAA,KAAK;UACb1F,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;UAClE;UACAL,UAAU,CAAC,MAAK;YACd,IAAI,CAACE,4BAA4B,EAAE;UACrC,CAAC,EAAE,IAAI,CAAC;QACV;OACD,CAAC;KACL,CAAC,OAAOS,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CACX,4DAA4D,EAC5DA,KAAK,CACN;MACDX,UAAU,CAAC,MAAK;QACd,IAAI,CAACE,4BAA4B,EAAE;MACrC,CAAC,EAAE,IAAI,CAAC;;EAEZ;EAEA;;;EAGQ2F,kBAAkBA,CAACc,IAAkB;IAC3CvG,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAE;MACtDuG,MAAM,EAAED,IAAI,CAAC5C,EAAE;MACf8C,QAAQ,EAAEF,IAAI,CAACtD,IAAI;MACnByD,MAAM,EAAEH,IAAI,CAACG,MAAM,EAAEC,QAAQ;MAC7BC,cAAc,EAAEL,IAAI,CAACK;KACtB,CAAC;IAEF,IAAI,CAACjI,YAAY,CAAC2G,IAAI,CAACiB,IAAI,CAAC;IAC5B,IAAI,CAAC7B,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC;IAE3B1E,OAAO,CAACC,GAAG,CACT,iEAAiE,CAClE;EACH;EAEA;;;EAGQqG,sBAAsBA,CAACC,IAAU;IACvCvG,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEsG,IAAI,CAACM,MAAM,CAAC;IAEjE,QAAQN,IAAI,CAACM,MAAM;MACjB,KAAK/I,UAAU,CAACgJ,QAAQ;QACtB9G,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;QAChD,IAAI,CAACsD,IAAI,CAAC,UAAU,CAAC;QACrB,IAAI,CAACmB,IAAI,CAAC,UAAU,CAAC;QACrB,IAAI,CAAChG,UAAU,CAAC4G,IAAI,CAAC,IAAI,CAAC;QAC1B,IAAI,CAAC3G,YAAY,CAAC2G,IAAI,CAAC,IAAI,CAAC;QAC5B;MAEF,KAAKxH,UAAU,CAACiJ,KAAK;QACnB/G,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;QAC1C,IAAI,CAAC+G,aAAa,EAAE;QACpB,IAAI,CAACtC,IAAI,CAAC,UAAU,CAAC;QACrB,IAAI,CAAChG,UAAU,CAAC4G,IAAI,CAAC,IAAI,CAAC;QAC1B,IAAI,CAAC3G,YAAY,CAAC2G,IAAI,CAAC,IAAI,CAAC;QAC5B;MAEF,KAAKxH,UAAU,CAACmJ,SAAS;QACvBjH,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;QAC7C,IAAI,CAACsD,IAAI,CAAC,UAAU,CAAC;QACrB,IAAI,CAACmB,IAAI,CAAC,gBAAgB,CAAC;QAC3B,IAAI,CAAChG,UAAU,CAAC4G,IAAI,CAACiB,IAAI,CAAC;QAC1B,IAAI,CAAC5H,YAAY,CAAC2G,IAAI,CAAC,IAAI,CAAC;QAC5B;MAEF,KAAKxH,UAAU,CAACoJ,OAAO;QACrBlH,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;QAC/C,IAAI,CAACyE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC;QAC3B;MAEF;QACE1E,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEsG,IAAI,CAACM,MAAM,CAAC;QACjE;;EAEN;EAEA;;;EAGcM,YAAYA,CAACV,QAAkB;IAAA,IAAAW,KAAA;IAAA,OAAAC,iBAAA;MAC3CrH,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAEwG,QAAQ,CAAC;MAEjE,MAAMa,WAAW,GAA2B;QAC1C3F,KAAK,EAAE,IAAI;QACX4F,KAAK,EAAEd,QAAQ,KAAK5I,QAAQ,CAAC2J;OAC9B;MAED,IAAI;QACF,MAAMC,MAAM,SAASC,SAAS,CAACC,YAAY,CAACR,YAAY,CAACG,WAAW,CAAC;QACrEtH,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;QAClDmH,KAAI,CAAC/H,WAAW,GAAGoI,MAAM;QAEzB;QACA,IAAIL,KAAI,CAAC7H,iBAAiB,IAAIkH,QAAQ,KAAK5I,QAAQ,CAAC2J,KAAK,EAAE;UACzDJ,KAAI,CAAC7H,iBAAiB,CAACoB,SAAS,GAAG8G,MAAM;;QAG3C,OAAOA,MAAM;OACd,CAAC,OAAOlH,KAAK,EAAE;QACdP,OAAO,CAACO,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;QACjE,MAAM,IAAIqH,KAAK,CAAC,6CAA6C,CAAC;;IAC/D;EACH;EAEA;;;EAGQC,8BAA8BA,CAACJ,MAAmB;IACxD,IAAI,CAAC,IAAI,CAACrI,cAAc,EAAE;MACxBY,OAAO,CAACO,KAAK,CAAC,8CAA8C,CAAC;MAC7D;;IAGFP,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC;IACtEwH,MAAM,CAACK,SAAS,EAAE,CAACvF,OAAO,CAAEwF,KAAK,IAAI;MACnC,IAAI,CAAC3I,cAAe,CAAC4I,QAAQ,CAACD,KAAK,EAAEN,MAAM,CAAC;IAC9C,CAAC,CAAC;EACJ;EAEA;;;EAGcQ,WAAWA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAb,iBAAA;MACvB,IAAI,CAACa,MAAI,CAAC9I,cAAc,EAAE;QACxB,MAAM,IAAIwI,KAAK,CAAC,8BAA8B,CAAC;;MAGjD5H,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;MACrD,MAAMkI,KAAK,SAASD,MAAI,CAAC9I,cAAc,CAAC6I,WAAW,EAAE;MACrD,MAAMC,MAAI,CAAC9I,cAAc,CAACgJ,mBAAmB,CAACD,KAAK,CAAC;MACpD,OAAOA,KAAK;IAAC;EACf;EAEA;;;EAGcE,YAAYA,CACxBF,KAAgC;IAAA,IAAAG,MAAA;IAAA,OAAAjB,iBAAA;MAEhC,IAAI,CAACiB,MAAI,CAAClJ,cAAc,EAAE;QACxB,MAAM,IAAIwI,KAAK,CAAC,8BAA8B,CAAC;;MAGjD5H,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;MACtD,MAAMqI,MAAI,CAAClJ,cAAc,CAACmJ,oBAAoB,CAACJ,KAAK,CAAC;MACrD,MAAMK,MAAM,SAASF,MAAI,CAAClJ,cAAc,CAACiJ,YAAY,EAAE;MACvD,MAAMC,MAAI,CAAClJ,cAAc,CAACgJ,mBAAmB,CAACI,MAAM,CAAC;MACrD,OAAOA,MAAM;IAAC;EAChB;EAEA;;;EAGOC,gBAAgBA,CACrBC,UAA4B,EAC5BC,WAA6B;IAE7B3I,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;IACtD,IAAI,CAACV,iBAAiB,GAAGmJ,UAAU;IACnC,IAAI,CAAClJ,kBAAkB,GAAGmJ,WAAW;IAErC;IACA,IAAI,IAAI,CAACtJ,WAAW,IAAIqJ,UAAU,EAAE;MAClCA,UAAU,CAAC/H,SAAS,GAAG,IAAI,CAACtB,WAAW;;IAEzC,IAAI,IAAI,CAACC,YAAY,IAAIqJ,WAAW,EAAE;MACpCA,WAAW,CAAChI,SAAS,GAAG,IAAI,CAACrB,YAAY;;EAE7C;EAEA;;;EAGAsJ,YAAYA,CACVC,WAAmB,EACnBpC,QAAkB,EAClBG,cAAuB;IAEvB5G,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAE;MAC/C4I,WAAW;MACXpC,QAAQ;MACRG;KACD,CAAC;IAEF,IAAI,CAACiC,WAAW,EAAE;MAChB,MAAMtI,KAAK,GAAG,IAAIqH,KAAK,CAAC,0BAA0B,CAAC;MACnD5H,OAAO,CAACO,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3D,OAAO/C,UAAU,CAAC,MAAM+C,KAAK,CAAC;;IAGhC;IACA,MAAMiG,MAAM,GAAG,QAAQsC,IAAI,CAACC,GAAG,EAAE,IAAIC,IAAI,CAACC,MAAM,EAAE,CAC/CC,QAAQ,CAAC,EAAE,CAAC,CACZC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;IAEjB;IACA,OAAO1L,IAAI,CAAC,IAAI,CAAC2L,iBAAiB,CAAC3C,QAAQ,CAAC,CAAC,CAAC4C,IAAI,CAChDzL,SAAS,CAAEuK,KAAK,IAAI;MAClB,MAAMmB,SAAS,GAAG;QAChBT,WAAW;QACXpC,QAAQ,EAAEA,QAAQ;QAClBD,MAAM;QACN2B,KAAK,EAAEoB,IAAI,CAACC,SAAS,CAACrB,KAAK,CAAC;QAC5BvB;OACD;MAED5G,OAAO,CAACC,GAAG,CACT,kDAAkD,EAClDqJ,SAAS,CACV;MAED,OAAO,IAAI,CAAC9K,MAAM,CACfiL,MAAM,CAAyB;QAC9BC,QAAQ,EAAE3L,sBAAsB;QAChCuL;OACD,CAAC,CACDD,IAAI,CACH3L,GAAG,CAAEiM,MAAM,IAAI;QACb3J,OAAO,CAACC,GAAG,CACT,8CAA8C,EAC9C0J,MAAM,CACP;QAED,IAAI,CAACA,MAAM,CAACpE,IAAI,EAAEqD,YAAY,EAAE;UAC9B,MAAM,IAAIhB,KAAK,CAAC,mCAAmC,CAAC;;QAGtD,MAAMrB,IAAI,GAAGoD,MAAM,CAACpE,IAAI,CAACqD,YAAY;QACrC5I,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE;UAC5C0D,EAAE,EAAE4C,IAAI,CAAC5C,EAAE;UACXV,IAAI,EAAEsD,IAAI,CAACtD,IAAI;UACf4D,MAAM,EAAEN,IAAI,CAACM,MAAM;UACnBH,MAAM,EAAEH,IAAI,CAACG,MAAM,EAAEC,QAAQ;UAC7BiD,SAAS,EAAErD,IAAI,CAACqD,SAAS,EAAEjD;SAC5B,CAAC;QAEF;QACA,IAAI,CAACjI,UAAU,CAAC4G,IAAI,CAACiB,IAAI,CAAC;QAE1B,OAAOA,IAAI;MACb,CAAC,CAAC,EACF5I,UAAU,CAAE4C,KAAK,IAAI;QACnBP,OAAO,CAACO,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;QAC3D,IAAI,CAAC9B,MAAM,CAAC8B,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAElD,IAAIsJ,YAAY,GAAG,wCAAwC;QAC3D,IAAItJ,KAAK,CAACuJ,YAAY,EAAE;UACtBD,YAAY,GAAG,4BAA4B;SAC5C,MAAM,IAAItJ,KAAK,CAACwJ,aAAa,EAAEC,MAAM,GAAG,CAAC,EAAE;UAC1CH,YAAY,GAAGtJ,KAAK,CAACwJ,aAAa,CAAC,CAAC,CAAC,CAACE,OAAO,IAAIJ,YAAY;;QAG/D,OAAOrM,UAAU,CAAC,MAAM,IAAIoK,KAAK,CAACiC,YAAY,CAAC,CAAC;MAClD,CAAC,CAAC,CACH;IACL,CAAC,CAAC,EACFlM,UAAU,CAAE4C,KAAK,IAAI;MACnBP,OAAO,CAACO,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD,OAAO/C,UAAU,CAAC,MAAM,IAAIoK,KAAK,CAAC,iBAAiB,GAAGrH,KAAK,CAAC0J,OAAO,CAAC,CAAC;IACvE,CAAC,CAAC,CACH;EACH;EAEA;;;EAGAC,UAAUA,CAACvL,YAA0B;IACnCqB,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEtB,YAAY,CAACgF,EAAE,CAAC;IAEhE;IACA,MAAM6E,MAAM,GAAGe,IAAI,CAACC,SAAS,CAAC;MAC5BvG,IAAI,EAAE,QAAQ;MACdkH,GAAG,EAAE;KACN,CAAC;IAEF,OAAO,IAAI,CAAC3L,MAAM,CACfiL,MAAM,CAAuB;MAC5BC,QAAQ,EAAE1L,oBAAoB;MAC9BsL,SAAS,EAAE;QACT9C,MAAM,EAAE7H,YAAY,CAACgF,EAAE;QACvB6E;;KAEH,CAAC,CACDa,IAAI,CACHzL,SAAS,CAAE+L,MAAM,IAAI;MACnB3J,OAAO,CAACC,GAAG,CAAC,6CAA6C,EAAE0J,MAAM,CAAC;MAElE,IAAI,CAACA,MAAM,CAACpE,IAAI,EAAE2E,UAAU,EAAE;QAC5B,MAAM,IAAItC,KAAK,CAAC,mCAAmC,CAAC;;MAGtD,MAAMrB,IAAI,GAAGoD,MAAM,CAACpE,IAAI,CAAC2E,UAAU;MAEnC;MACA,IAAI,CAAC3G,IAAI,CAAC,UAAU,CAAC;MACrB,IAAI,CAACmB,IAAI,CAAC,gBAAgB,CAAC;MAE3B;MACA,OAAOjH,IAAI,CAAC,IAAI,CAAC2M,iBAAiB,CAACzL,YAAY,EAAE4H,IAAI,CAAC,CAAC;IACzD,CAAC,CAAC,EACF5I,UAAU,CAAE4C,KAAK,IAAI;MACnBP,OAAO,CAACO,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzD,IAAI,CAAC9B,MAAM,CAAC8B,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MACjD,OAAO/C,UAAU,CACf,MAAM,IAAIoK,KAAK,CAAC,yCAAyC,CAAC,CAC3D;IACH,CAAC,CAAC,CACH;EACL;EAEA;;;EAGAyC,UAAUA,CAAC7D,MAAc,EAAE8D,MAAe;IACxCtK,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEuG,MAAM,EAAE8D,MAAM,CAAC;IAE/D,OAAO,IAAI,CAAC9L,MAAM,CACfiL,MAAM,CAA8B;MACnCC,QAAQ,EAAEzL,oBAAoB;MAC9BqL,SAAS,EAAE;QACT9C,MAAM;QACN8D,MAAM,EAAEA,MAAM,IAAI;;KAErB,CAAC,CACDjB,IAAI,CACH3L,GAAG,CAAEiM,MAAM,IAAI;MACb3J,OAAO,CAACC,GAAG,CAAC,6CAA6C,EAAE0J,MAAM,CAAC;MAElE,IAAI,CAACA,MAAM,CAACpE,IAAI,EAAE8E,UAAU,EAAE;QAC5B,MAAM,IAAIzC,KAAK,CAAC,kCAAkC,CAAC;;MAGrD;MACA,IAAI,CAACjJ,YAAY,CAAC2G,IAAI,CAAC,IAAI,CAAC;MAC5B,IAAI,CAAC5G,UAAU,CAAC4G,IAAI,CAAC,IAAI,CAAC;MAE1B;MACA,IAAI,CAAC/B,IAAI,CAAC,UAAU,CAAC;MAErB,OAAOoG,MAAM,CAACpE,IAAI,CAAC8E,UAAU;IAC/B,CAAC,CAAC,EACF1M,UAAU,CAAE4C,KAAK,IAAI;MACnBP,OAAO,CAACO,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzD,IAAI,CAAC9B,MAAM,CAAC8B,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MACjD,OAAO/C,UAAU,CAAC,MAAM,IAAIoK,KAAK,CAAC,iCAAiC,CAAC,CAAC;IACvE,CAAC,CAAC,CACH;EACL;EAEA;;;EAGA2C,OAAOA,CAAC/D,MAAc;IACpBxG,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEuG,MAAM,CAAC;IAEpD,OAAO,IAAI,CAAChI,MAAM,CACfiL,MAAM,CAA2B;MAChCC,QAAQ,EAAExL,iBAAiB;MAC3BoL,SAAS,EAAE;QACT9C,MAAM;QACNgE,QAAQ,EAAE,IAAI,CAAE;;KAEnB,CAAC,CACDnB,IAAI,CACH3L,GAAG,CAAEiM,MAAM,IAAI;MACb3J,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAE0J,MAAM,CAAC;MAE/D,IAAI,CAACA,MAAM,CAACpE,IAAI,EAAEgF,OAAO,EAAE;QACzB,MAAM,IAAI3C,KAAK,CAAC,kCAAkC,CAAC;;MAGrD;MACA,IAAI,CAAClJ,UAAU,CAAC4G,IAAI,CAAC,IAAI,CAAC;MAC1B,IAAI,CAAC3G,YAAY,CAAC2G,IAAI,CAAC,IAAI,CAAC;MAE5B;MACA,IAAI,CAAC0B,aAAa,EAAE;MACpB,IAAI,CAACtC,IAAI,CAAC,UAAU,CAAC;MAErB,OAAOiF,MAAM,CAACpE,IAAI,CAACgF,OAAO;IAC5B,CAAC,CAAC,EACF5M,UAAU,CAAE4C,KAAK,IAAI;MACnBP,OAAO,CAACO,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD,IAAI,CAAC9B,MAAM,CAAC8B,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC9C,OAAO/C,UAAU,CACf,MAAM,IAAIoK,KAAK,CAAC,kCAAkC,CAAC,CACpD;IACH,CAAC,CAAC,CACH;EACL;EAEA;;;EAGA6C,WAAWA,CACTjE,MAAc,EACdkE,WAAqB,EACrBC,WAAqB;IAErB3K,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE;MAC9CuG,MAAM;MACNkE,WAAW;MACXC;KACD,CAAC;IAEF,OAAO,IAAI,CAACnM,MAAM,CACfiL,MAAM,CAAmC;MACxCC,QAAQ,EAAEvL,0BAA0B;MACpCmL,SAAS,EAAE;QACT9C,MAAM;QACNe,KAAK,EAAEmD,WAAW;QAClB/I,KAAK,EAAEgJ;;KAEV,CAAC,CACDtB,IAAI,CACH3L,GAAG,CAAEiM,MAAM,IAAI;MACb3J,OAAO,CAACC,GAAG,CAAC,6CAA6C,EAAE0J,MAAM,CAAC;MAElE,IAAI,CAACA,MAAM,CAACpE,IAAI,EAAEqF,eAAe,EAAE;QACjC,MAAM,IAAIhD,KAAK,CAAC,kCAAkC,CAAC;;MAGrD,OAAO+B,MAAM,CAACpE,IAAI,CAACqF,eAAe;IACpC,CAAC,CAAC,EACFjN,UAAU,CAAE4C,KAAK,IAAI;MACnBP,OAAO,CAACO,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;MAC1D,IAAI,CAAC9B,MAAM,CAAC8B,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MACjD,OAAO/C,UAAU,CACf,MAAM,IAAIoK,KAAK,CAAC,sCAAsC,CAAC,CACxD;IACH,CAAC,CAAC,CACH;EACL;EAEA;;;EAGcwC,iBAAiBA,CAC7BzL,YAA0B,EAC1B4H,IAAU;IAAA,IAAAsE,MAAA;IAAA,OAAAxD,iBAAA;MAEVrH,OAAO,CAACC,GAAG,CAAC,4DAA4D,CAAC;MAEzE;MACA4K,MAAI,CAACnG,IAAI,CAAC,gBAAgB,CAAC;MAE3B;MACAmG,MAAI,CAACnM,UAAU,CAAC4G,IAAI,CAACiB,IAAI,CAAC;MAC1BsE,MAAI,CAAClM,YAAY,CAAC2G,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;MAE9B,OAAOiB,IAAI;IAAC;EACd;EAEA;;;EAGAuE,WAAWA,CAAA;IACT,IAAI,CAAC5L,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;IAC1Cc,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE,IAAI,CAACf,cAAc,CAAC;IACnE,OAAO,IAAI,CAACA,cAAc;EAC5B;EAEA;;;EAGA6L,WAAWA,CAAA;IACT,IAAI,CAAC5L,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;IAC1Ca,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE,IAAI,CAACd,cAAc,CAAC;IACnE,OAAO,IAAI,CAACA,cAAc;EAC5B;EAEA;;;EAGA6L,eAAeA,CAAA;IACb,OAAO,IAAI,CAAC9L,cAAc;EAC5B;EAEA;;;EAGA+L,eAAeA,CAAA;IACb,OAAO,IAAI,CAAC9L,cAAc;EAC5B;EAEA;;;EAGQ6H,aAAaA,CAAA;IACnBhH,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;IACnD6F,MAAM,CAACC,IAAI,CAAC,IAAI,CAAChH,MAAM,CAAC,CAACwD,OAAO,CAAEyB,IAAI,IAAI;MACxC,IAAI,CAACT,IAAI,CAACS,IAAI,CAAC;IACjB,CAAC,CAAC;EACJ;EAEA;;;EAGOkH,YAAYA,CAAA;IACjBlL,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC;IACtE6F,MAAM,CAACqF,MAAM,CAAC,IAAI,CAACpM,MAAM,CAAC,CAACwD,OAAO,CAAEqC,KAAK,IAAI;MAC3C,IAAIA,KAAK,EAAE;QACTA,KAAK,CAAC3F,KAAK,GAAG,KAAK;QACnB2F,KAAK,CAAC/C,MAAM,GAAG,GAAG;QAClB;QACA+C,KAAK,CACFF,IAAI,EAAE,CACNK,IAAI,CAAC,MAAK;UACTH,KAAK,CAACM,KAAK,EAAE;UACbN,KAAK,CAACtC,WAAW,GAAG,CAAC;QACvB,CAAC,CAAC,CACD0C,KAAK,CAAC,MAAK;UACV;QAAA,CACD,CAAC;;IAER,CAAC,CAAC;EACJ;EAEAoG,WAAWA,CAAA;IACT,IAAI,CAACpE,aAAa,EAAE;EACtB;;;uBAx2CW1I,WAAW,EAAA+M,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,aAAA;IAAA;EAAA;;;aAAXpN,WAAW;MAAAqN,OAAA,EAAXrN,WAAW,CAAAsN,IAAA;MAAAC,UAAA,EAFV;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}