{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/OneDrive/Bureau/Project PI/devBridge/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { BehaviorSubject, throwError, from } from 'rxjs';\nimport { map, catchError, switchMap } from 'rxjs/operators';\nimport { CallStatus } from '../models/message.model';\nimport { INITIATE_CALL_MUTATION, ACCEPT_CALL_MUTATION, REJECT_CALL_MUTATION, END_CALL_MUTATION, TOGGLE_CALL_MEDIA_MUTATION, INCOMING_CALL_SUBSCRIPTION, CALL_STATUS_CHANGED_SUBSCRIPTION } from '../graphql/message.graphql';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"apollo-angular\";\nimport * as i2 from \"./logger.service\";\nexport class CallService {\n  constructor(apollo, logger) {\n    this.apollo = apollo;\n    this.logger = logger;\n    // État des appels\n    this.activeCall = new BehaviorSubject(null);\n    this.incomingCall = new BehaviorSubject(null);\n    // Observables publics\n    this.activeCall$ = this.activeCall.asObservable();\n    this.incomingCall$ = this.incomingCall.asObservable();\n    // Propriétés pour la gestion des sons\n    this.sounds = {};\n    this.isPlaying = {};\n    this.muted = false;\n    // États simples pour les médias\n    this.isVideoEnabled = true;\n    this.isAudioEnabled = true;\n    this.preloadSounds();\n    this.initializeSubscriptions();\n  }\n  /**\n   * Initialise les subscriptions avec un délai pour s'assurer que l'authentification est prête\n   */\n  initializeSubscriptions() {\n    // Attendre un peu pour s'assurer que l'authentification est prête\n    setTimeout(() => {\n      this.subscribeToIncomingCalls();\n      this.subscribeToCallStatusChanges();\n    }, 1000);\n    // Réessayer après 5 secondes si la première tentative échoue\n    setTimeout(() => {\n      if (!this.incomingCall.value) {\n        console.log('🔄 [CallService] Retrying subscription initialization...');\n        this.subscribeToIncomingCalls();\n        this.subscribeToCallStatusChanges();\n      }\n    }, 5000);\n  }\n  /**\n   * Précharge les sons utilisés dans l'application\n   */\n  preloadSounds() {\n    this.loadSound('ringtone', '/assets/sounds/ringtone.mp3');\n    this.loadSound('call-end', '/assets/sounds/call-end.mp3');\n    this.loadSound('call-connected', '/assets/sounds/call-connected.mp3');\n  }\n  /**\n   * Charge un fichier audio\n   */\n  loadSound(name, path) {\n    try {\n      const audio = new Audio();\n      // Configuration de l'audio\n      audio.preload = 'auto';\n      audio.volume = 0.7;\n      // Gérer les événements de chargement\n      audio.addEventListener('canplaythrough', () => {\n        console.log(`✅ [CallService] Sound ${name} loaded successfully from ${path}`);\n      });\n      audio.addEventListener('error', e => {\n        console.error(`❌ [CallService] Error loading sound ${name} from ${path}:`, e);\n        console.log(`🔄 [CallService] Trying to load ${name} with different approach...`);\n        // Essayer de charger avec un chemin relatif\n        const altPath = path.startsWith('/') ? path.substring(1) : path;\n        if (altPath !== path) {\n          setTimeout(() => {\n            audio.src = altPath;\n            audio.load();\n          }, 100);\n        }\n      });\n      audio.addEventListener('ended', () => {\n        this.isPlaying[name] = false;\n        console.log(`🔇 [CallService] Sound ${name} ended`);\n      });\n      // Définir la source et charger\n      audio.src = path;\n      audio.load();\n      this.sounds[name] = audio;\n      this.isPlaying[name] = false;\n      console.log(`🔊 [CallService] Loading sound ${name} from ${path}`);\n    } catch (error) {\n      console.error(`❌ [CallService] Error creating audio element for ${name}:`, error);\n    }\n  }\n  /**\n   * Joue un son\n   */\n  play(name, loop = false) {\n    if (this.muted) {\n      console.log(`🔇 [CallService] Sound ${name} muted`);\n      return;\n    }\n    try {\n      const sound = this.sounds[name];\n      if (!sound) {\n        console.warn(`🔊 [CallService] Sound ${name} not found`);\n        return;\n      }\n      sound.loop = loop;\n      sound.volume = 0.7; // Volume à 70%\n      if (!this.isPlaying[name]) {\n        sound.currentTime = 0;\n        console.log(`🔊 [CallService] Playing sound: ${name} (loop: ${loop})`);\n        sound.play().then(() => {\n          console.log(`✅ [CallService] Sound ${name} started successfully`);\n          this.isPlaying[name] = true;\n        }).catch(error => {\n          console.error(`❌ [CallService] Error playing sound ${name}:`, error);\n          // Réessayer après interaction utilisateur\n          if (error.name === 'NotAllowedError') {\n            console.log(`🔊 [CallService] Sound ${name} blocked by browser, will retry after user interaction`);\n          }\n        });\n      } else {\n        console.log(`🔊 [CallService] Sound ${name} already playing`);\n      }\n    } catch (error) {\n      console.error(`❌ [CallService] Error in play method for ${name}:`, error);\n    }\n  }\n  /**\n   * Arrête un son\n   */\n  stop(name) {\n    try {\n      const sound = this.sounds[name];\n      if (!sound) {\n        console.warn(`🔊 [CallService] Sound ${name} not found for stopping`);\n        return;\n      }\n      if (this.isPlaying[name]) {\n        console.log(`🔇 [CallService] Stopping sound: ${name}`);\n        sound.pause();\n        sound.currentTime = 0;\n        this.isPlaying[name] = false;\n      } else {\n        console.log(`🔇 [CallService] Sound ${name} was not playing`);\n      }\n    } catch (error) {\n      console.error(`❌ [CallService] Error stopping sound ${name}:`, error);\n    }\n  }\n  /**\n   * S'abonne aux appels entrants\n   */\n  subscribeToIncomingCalls() {\n    console.log('🔔 [CallService] Setting up incoming call subscription...');\n    try {\n      this.apollo.subscribe({\n        query: INCOMING_CALL_SUBSCRIPTION,\n        errorPolicy: 'all' // Continuer même en cas d'erreur\n      }).subscribe({\n        next: ({\n          data,\n          errors\n        }) => {\n          if (errors) {\n            console.warn('⚠️ [CallService] GraphQL errors in subscription:', errors);\n          }\n          if (data?.incomingCall) {\n            console.log('📞 [CallService] Incoming call received:', {\n              callId: data.incomingCall.id,\n              callType: data.incomingCall.type,\n              caller: data.incomingCall.caller?.username,\n              conversationId: data.incomingCall.conversationId\n            });\n            this.handleIncomingCall(data.incomingCall);\n          }\n        },\n        error: error => {\n          console.error('❌ [CallService] Error in incoming call subscription:', error);\n          // Réessayer après 5 secondes en cas d'erreur\n          setTimeout(() => {\n            console.log('🔄 [CallService] Retrying incoming call subscription...');\n            this.subscribeToIncomingCalls();\n          }, 5000);\n        },\n        complete: () => {\n          console.log('🔚 [CallService] Incoming call subscription completed');\n          // Réessayer si la subscription se ferme de manière inattendue\n          setTimeout(() => {\n            console.log('🔄 [CallService] Restarting subscription after completion...');\n            this.subscribeToIncomingCalls();\n          }, 2000);\n        }\n      });\n    } catch (error) {\n      console.error('❌ [CallService] Failed to create subscription:', error);\n      // Réessayer après 3 secondes\n      setTimeout(() => {\n        this.subscribeToIncomingCalls();\n      }, 3000);\n    }\n  }\n  /**\n   * Méthode publique pour forcer la réinitialisation de la subscription\n   */\n  reinitializeSubscription() {\n    console.log('🔄 [CallService] Manually reinitializing subscription...');\n    this.subscribeToIncomingCalls();\n    this.subscribeToCallStatusChanges();\n  }\n  /**\n   * S'abonne aux changements de statut d'appel\n   */\n  subscribeToCallStatusChanges() {\n    console.log('📞 [CallService] Setting up call status change subscription...');\n    try {\n      this.apollo.subscribe({\n        query: CALL_STATUS_CHANGED_SUBSCRIPTION,\n        errorPolicy: 'all'\n      }).subscribe({\n        next: ({\n          data,\n          errors\n        }) => {\n          if (errors) {\n            console.warn('⚠️ [CallService] GraphQL errors in call status subscription:', errors);\n          }\n          if (data?.callStatusChanged) {\n            console.log('📞 [CallService] Call status changed:', data.callStatusChanged);\n            this.handleCallStatusChange(data.callStatusChanged);\n          }\n        },\n        error: error => {\n          console.error('❌ [CallService] Error in call status subscription:', error);\n          // Réessayer après 5 secondes\n          setTimeout(() => {\n            this.subscribeToCallStatusChanges();\n          }, 5000);\n        },\n        complete: () => {\n          console.log('🔚 [CallService] Call status subscription completed');\n          // Réessayer si la subscription se ferme\n          setTimeout(() => {\n            this.subscribeToCallStatusChanges();\n          }, 2000);\n        }\n      });\n    } catch (error) {\n      console.error('❌ [CallService] Failed to create call status subscription:', error);\n      setTimeout(() => {\n        this.subscribeToCallStatusChanges();\n      }, 3000);\n    }\n  }\n  /**\n   * Gère un appel entrant\n   */\n  handleIncomingCall(call) {\n    console.log('🔔 [CallService] Handling incoming call:', {\n      callId: call.id,\n      callType: call.type,\n      caller: call.caller?.username,\n      conversationId: call.conversationId\n    });\n    this.incomingCall.next(call);\n    this.play('ringtone', true);\n    console.log('🔊 [CallService] Ringtone started, call notification sent to UI');\n  }\n  /**\n   * Gère les changements de statut d'appel\n   */\n  handleCallStatusChange(call) {\n    console.log('📞 [CallService] Call status changed:', call.status);\n    switch (call.status) {\n      case CallStatus.REJECTED:\n        console.log('❌ [CallService] Call was rejected');\n        this.stop('ringtone');\n        this.play('call-end');\n        this.activeCall.next(null);\n        this.incomingCall.next(null);\n        break;\n      case CallStatus.ENDED:\n        console.log('📞 [CallService] Call ended');\n        this.stopAllSounds();\n        this.play('call-end');\n        this.activeCall.next(null);\n        this.incomingCall.next(null);\n        break;\n      case CallStatus.CONNECTED:\n        console.log('✅ [CallService] Call connected');\n        this.stop('ringtone');\n        this.play('call-connected');\n        this.activeCall.next(call);\n        this.incomingCall.next(null);\n        break;\n      case CallStatus.RINGING:\n        console.log('📞 [CallService] Call is ringing');\n        this.play('ringtone', true);\n        break;\n      default:\n        console.log('📞 [CallService] Unknown call status:', call.status);\n        break;\n    }\n  }\n  /**\n   * Initie un appel WebRTC\n   */\n  initiateCall(recipientId, callType, conversationId) {\n    console.log('🔄 [CallService] Initiating call:', {\n      recipientId,\n      callType,\n      conversationId\n    });\n    if (!recipientId) {\n      const error = new Error('Recipient ID is required');\n      console.error('❌ [CallService] initiateCall error:', error);\n      return throwError(() => error);\n    }\n    // Générer un ID unique pour l'appel\n    const callId = `call_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n    // Pour l'instant, utiliser une offre WebRTC factice\n    const offer = JSON.stringify({\n      type: 'offer',\n      sdp: 'v=0\\r\\no=- 0 0 IN IP4 127.0.0.1\\r\\ns=-\\r\\nt=0 0\\r\\n'\n    });\n    const variables = {\n      recipientId,\n      callType: callType,\n      callId,\n      offer,\n      conversationId\n    };\n    console.log('📤 [CallService] Sending initiate call mutation:', variables);\n    return this.apollo.mutate({\n      mutation: INITIATE_CALL_MUTATION,\n      variables\n    }).pipe(map(result => {\n      console.log('✅ [CallService] Call initiated successfully:', result);\n      if (!result.data?.initiateCall) {\n        throw new Error('No call data received from server');\n      }\n      const call = result.data.initiateCall;\n      console.log('📞 [CallService] Call details:', {\n        id: call.id,\n        type: call.type,\n        status: call.status,\n        caller: call.caller?.username,\n        recipient: call.recipient?.username\n      });\n      // Mettre à jour l'état local\n      this.activeCall.next(call);\n      return call;\n    }), catchError(error => {\n      console.error('❌ [CallService] initiateCall error:', error);\n      this.logger.error('Error initiating call:', error);\n      let errorMessage = \"Erreur lors de l'initiation de l'appel\";\n      if (error.networkError) {\n        errorMessage = 'Erreur de connexion réseau';\n      } else if (error.graphQLErrors?.length > 0) {\n        errorMessage = error.graphQLErrors[0].message || errorMessage;\n      }\n      return throwError(() => new Error(errorMessage));\n    }));\n  }\n  /**\n   * Accepte un appel entrant\n   */\n  acceptCall(incomingCall) {\n    console.log('🔄 [CallService] Accepting call:', incomingCall.id);\n    // Générer une réponse WebRTC factice\n    const answer = JSON.stringify({\n      type: 'answer',\n      sdp: 'v=0\\r\\no=- 0 0 IN IP4 127.0.0.1\\r\\ns=-\\r\\nt=0 0\\r\\n'\n    });\n    return this.apollo.mutate({\n      mutation: ACCEPT_CALL_MUTATION,\n      variables: {\n        callId: incomingCall.id,\n        answer\n      }\n    }).pipe(switchMap(result => {\n      console.log('✅ [CallService] Call accepted successfully:', result);\n      if (!result.data?.acceptCall) {\n        throw new Error('No call data received from server');\n      }\n      const call = result.data.acceptCall;\n      // Arrêter la sonnerie\n      this.stop('ringtone');\n      this.play('call-connected');\n      // Démarrer les médias pour l'appel de manière asynchrone\n      return from(this.startMediaForCall(incomingCall, call));\n    }), catchError(error => {\n      console.error('❌ [CallService] acceptCall error:', error);\n      this.logger.error('Error accepting call:', error);\n      return throwError(() => new Error(\"Erreur lors de l'acceptation de l'appel\"));\n    }));\n  }\n  /**\n   * Rejette un appel entrant\n   */\n  rejectCall(callId, reason) {\n    console.log('🔄 [CallService] Rejecting call:', callId, reason);\n    return this.apollo.mutate({\n      mutation: REJECT_CALL_MUTATION,\n      variables: {\n        callId,\n        reason: reason || 'User rejected'\n      }\n    }).pipe(map(result => {\n      console.log('✅ [CallService] Call rejected successfully:', result);\n      if (!result.data?.rejectCall) {\n        throw new Error('No response received from server');\n      }\n      // Mettre à jour l'état local\n      this.incomingCall.next(null);\n      this.activeCall.next(null);\n      // Arrêter la sonnerie\n      this.stop('ringtone');\n      return result.data.rejectCall;\n    }), catchError(error => {\n      console.error('❌ [CallService] rejectCall error:', error);\n      this.logger.error('Error rejecting call:', error);\n      return throwError(() => new Error(\"Erreur lors du rejet de l'appel\"));\n    }));\n  }\n  /**\n   * Termine un appel en cours\n   */\n  endCall(callId) {\n    console.log('🔄 [CallService] Ending call:', callId);\n    return this.apollo.mutate({\n      mutation: END_CALL_MUTATION,\n      variables: {\n        callId,\n        feedback: null // Pas de feedback pour l'instant\n      }\n    }).pipe(map(result => {\n      console.log('✅ [CallService] Call ended successfully:', result);\n      if (!result.data?.endCall) {\n        throw new Error('No response received from server');\n      }\n      // Mettre à jour l'état local\n      this.activeCall.next(null);\n      this.incomingCall.next(null);\n      // Arrêter tous les sons\n      this.stopAllSounds();\n      this.play('call-end');\n      return result.data.endCall;\n    }), catchError(error => {\n      console.error('❌ [CallService] endCall error:', error);\n      this.logger.error('Error ending call:', error);\n      return throwError(() => new Error(\"Erreur lors de la fin de l'appel\"));\n    }));\n  }\n  /**\n   * Bascule l'état des médias (audio/vidéo) pendant un appel\n   */\n  toggleMedia(callId, enableVideo, enableAudio) {\n    console.log('🔄 [CallService] Toggling media:', {\n      callId,\n      enableVideo,\n      enableAudio\n    });\n    return this.apollo.mutate({\n      mutation: TOGGLE_CALL_MEDIA_MUTATION,\n      variables: {\n        callId,\n        video: enableVideo,\n        audio: enableAudio\n      }\n    }).pipe(map(result => {\n      console.log('✅ [CallService] Media toggled successfully:', result);\n      if (!result.data?.toggleCallMedia) {\n        throw new Error('No response received from server');\n      }\n      return result.data.toggleCallMedia;\n    }), catchError(error => {\n      console.error('❌ [CallService] toggleMedia error:', error);\n      this.logger.error('Error toggling media:', error);\n      return throwError(() => new Error('Erreur lors du changement des médias'));\n    }));\n  }\n  /**\n   * Démarre les médias pour un appel accepté\n   */\n  startMediaForCall(incomingCall, call) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      console.log('🎥 [CallService] Call connected - playing connection sound');\n      // Jouer le son de connexion\n      _this.play('call-connected');\n      // Mettre à jour l'état local\n      _this.activeCall.next(call);\n      _this.incomingCall.next(null); // Supprimer l'appel entrant\n      return call;\n    })();\n  }\n  /**\n   * Active/désactive la vidéo\n   */\n  toggleVideo() {\n    this.isVideoEnabled = !this.isVideoEnabled;\n    console.log('📹 [CallService] Video toggled:', this.isVideoEnabled);\n    return this.isVideoEnabled;\n  }\n  /**\n   * Active/désactive l'audio\n   */\n  toggleAudio() {\n    this.isAudioEnabled = !this.isAudioEnabled;\n    console.log('🎤 [CallService] Audio toggled:', this.isAudioEnabled);\n    return this.isAudioEnabled;\n  }\n  /**\n   * Obtient l'état de la vidéo\n   */\n  getVideoEnabled() {\n    return this.isVideoEnabled;\n  }\n  /**\n   * Obtient l'état de l'audio\n   */\n  getAudioEnabled() {\n    return this.isAudioEnabled;\n  }\n  /**\n   * Arrête tous les sons\n   */\n  stopAllSounds() {\n    console.log('🔇 [CallService] Stopping all sounds');\n    Object.keys(this.sounds).forEach(name => {\n      this.stop(name);\n    });\n  }\n  /**\n   * Active les sons après interaction utilisateur (pour contourner les restrictions du navigateur)\n   */\n  enableSounds() {\n    console.log('🔊 [CallService] Enabling sounds after user interaction');\n    Object.values(this.sounds).forEach(sound => {\n      if (sound) {\n        sound.muted = false;\n        sound.volume = 0.7;\n        // Jouer et arrêter immédiatement pour \"débloquer\" l'audio\n        sound.play().then(() => {\n          sound.pause();\n          sound.currentTime = 0;\n        }).catch(() => {\n          // Ignorer les erreurs ici\n        });\n      }\n    });\n  }\n  ngOnDestroy() {\n    this.stopAllSounds();\n  }\n  static {\n    this.ɵfac = function CallService_Factory(t) {\n      return new (t || CallService)(i0.ɵɵinject(i1.Apollo), i0.ɵɵinject(i2.LoggerService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: CallService,\n      factory: CallService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["BehaviorSubject", "throwError", "from", "map", "catchError", "switchMap", "CallStatus", "INITIATE_CALL_MUTATION", "ACCEPT_CALL_MUTATION", "REJECT_CALL_MUTATION", "END_CALL_MUTATION", "TOGGLE_CALL_MEDIA_MUTATION", "INCOMING_CALL_SUBSCRIPTION", "CALL_STATUS_CHANGED_SUBSCRIPTION", "CallService", "constructor", "apollo", "logger", "activeCall", "incomingCall", "activeCall$", "asObservable", "incomingCall$", "sounds", "isPlaying", "muted", "isVideoEnabled", "isAudioEnabled", "preloadSounds", "initializeSubscriptions", "setTimeout", "subscribeToIncomingCalls", "subscribeToCallStatusChanges", "value", "console", "log", "loadSound", "name", "path", "audio", "Audio", "preload", "volume", "addEventListener", "e", "error", "altPath", "startsWith", "substring", "src", "load", "play", "loop", "sound", "warn", "currentTime", "then", "catch", "stop", "pause", "subscribe", "query", "errorPolicy", "next", "data", "errors", "callId", "id", "callType", "type", "caller", "username", "conversationId", "handleIncomingCall", "complete", "reinitializeSubscription", "callStatusChanged", "handleCallStatusChange", "call", "status", "REJECTED", "ENDED", "stopAllSounds", "CONNECTED", "RINGING", "initiateCall", "recipientId", "Error", "Date", "now", "Math", "random", "toString", "substr", "offer", "JSON", "stringify", "sdp", "variables", "mutate", "mutation", "pipe", "result", "recipient", "errorMessage", "networkError", "graphQLErrors", "length", "message", "acceptCall", "answer", "startMediaForCall", "rejectCall", "reason", "endCall", "feedback", "toggleMedia", "enableVideo", "enableAudio", "video", "toggleCallMedia", "_this", "_asyncToGenerator", "toggleVideo", "toggleAudio", "getVideoEnabled", "getAudioEnabled", "Object", "keys", "for<PERSON>ach", "enableSounds", "values", "ngOnDestroy", "i0", "ɵɵinject", "i1", "Apollo", "i2", "LoggerService", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\services\\call.service.ts"], "sourcesContent": ["import { Injectable, <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';\nimport { Apollo } from 'apollo-angular';\nimport { BehaviorSubject, Observable, throwError, of, from } from 'rxjs';\nimport { map, catchError, switchMap } from 'rxjs/operators';\nimport {\n  Call,\n  CallType,\n  CallStatus,\n  IncomingCall,\n  CallSuccess,\n} from '../models/message.model';\nimport {\n  INITIATE_CALL_MUTATION,\n  ACCEPT_CALL_MUTATION,\n  REJECT_CALL_MUTATION,\n  END_CALL_MUTATION,\n  TOGGLE_CALL_MEDIA_MUTATION,\n  INCOMING_CALL_SUBSCRIPTION,\n  CALL_STATUS_CHANGED_SUBSCRIPTION,\n} from '../graphql/message.graphql';\nimport { LoggerService } from './logger.service';\n\n@Injectable({\n  providedIn: 'root',\n})\nexport class CallService implements OnDestroy {\n  // État des appels\n  private activeCall = new BehaviorSubject<Call | null>(null);\n  private incomingCall = new BehaviorSubject<IncomingCall | null>(null);\n\n  // Observables publics\n  public activeCall$ = this.activeCall.asObservable();\n  public incomingCall$ = this.incomingCall.asObservable();\n\n  // Propriétés pour la gestion des sons\n  private sounds: { [key: string]: HTMLAudioElement } = {};\n  private isPlaying: { [key: string]: boolean } = {};\n  private muted = false;\n\n  // États simples pour les médias\n  private isVideoEnabled = true;\n  private isAudioEnabled = true;\n\n  constructor(private apollo: Apollo, private logger: LoggerService) {\n    this.preloadSounds();\n    this.initializeSubscriptions();\n  }\n\n  /**\n   * Initialise les subscriptions avec un délai pour s'assurer que l'authentification est prête\n   */\n  private initializeSubscriptions(): void {\n    // Attendre un peu pour s'assurer que l'authentification est prête\n    setTimeout(() => {\n      this.subscribeToIncomingCalls();\n      this.subscribeToCallStatusChanges();\n    }, 1000);\n\n    // Réessayer après 5 secondes si la première tentative échoue\n    setTimeout(() => {\n      if (!this.incomingCall.value) {\n        console.log('🔄 [CallService] Retrying subscription initialization...');\n        this.subscribeToIncomingCalls();\n        this.subscribeToCallStatusChanges();\n      }\n    }, 5000);\n  }\n\n  /**\n   * Précharge les sons utilisés dans l'application\n   */\n  private preloadSounds(): void {\n    this.loadSound('ringtone', '/assets/sounds/ringtone.mp3');\n    this.loadSound('call-end', '/assets/sounds/call-end.mp3');\n    this.loadSound('call-connected', '/assets/sounds/call-connected.mp3');\n  }\n\n  /**\n   * Charge un fichier audio\n   */\n  private loadSound(name: string, path: string): void {\n    try {\n      const audio = new Audio();\n\n      // Configuration de l'audio\n      audio.preload = 'auto';\n      audio.volume = 0.7;\n\n      // Gérer les événements de chargement\n      audio.addEventListener('canplaythrough', () => {\n        console.log(\n          `✅ [CallService] Sound ${name} loaded successfully from ${path}`\n        );\n      });\n\n      audio.addEventListener('error', (e) => {\n        console.error(\n          `❌ [CallService] Error loading sound ${name} from ${path}:`,\n          e\n        );\n        console.log(\n          `🔄 [CallService] Trying to load ${name} with different approach...`\n        );\n\n        // Essayer de charger avec un chemin relatif\n        const altPath = path.startsWith('/') ? path.substring(1) : path;\n        if (altPath !== path) {\n          setTimeout(() => {\n            audio.src = altPath;\n            audio.load();\n          }, 100);\n        }\n      });\n\n      audio.addEventListener('ended', () => {\n        this.isPlaying[name] = false;\n        console.log(`🔇 [CallService] Sound ${name} ended`);\n      });\n\n      // Définir la source et charger\n      audio.src = path;\n      audio.load();\n\n      this.sounds[name] = audio;\n      this.isPlaying[name] = false;\n\n      console.log(`🔊 [CallService] Loading sound ${name} from ${path}`);\n    } catch (error) {\n      console.error(\n        `❌ [CallService] Error creating audio element for ${name}:`,\n        error\n      );\n    }\n  }\n\n  /**\n   * Joue un son\n   */\n  private play(name: string, loop: boolean = false): void {\n    if (this.muted) {\n      console.log(`🔇 [CallService] Sound ${name} muted`);\n      return;\n    }\n\n    try {\n      const sound = this.sounds[name];\n      if (!sound) {\n        console.warn(`🔊 [CallService] Sound ${name} not found`);\n        return;\n      }\n\n      sound.loop = loop;\n      sound.volume = 0.7; // Volume à 70%\n\n      if (!this.isPlaying[name]) {\n        sound.currentTime = 0;\n        console.log(`🔊 [CallService] Playing sound: ${name} (loop: ${loop})`);\n\n        sound\n          .play()\n          .then(() => {\n            console.log(`✅ [CallService] Sound ${name} started successfully`);\n            this.isPlaying[name] = true;\n          })\n          .catch((error) => {\n            console.error(\n              `❌ [CallService] Error playing sound ${name}:`,\n              error\n            );\n            // Réessayer après interaction utilisateur\n            if (error.name === 'NotAllowedError') {\n              console.log(\n                `🔊 [CallService] Sound ${name} blocked by browser, will retry after user interaction`\n              );\n            }\n          });\n      } else {\n        console.log(`🔊 [CallService] Sound ${name} already playing`);\n      }\n    } catch (error) {\n      console.error(\n        `❌ [CallService] Error in play method for ${name}:`,\n        error\n      );\n    }\n  }\n\n  /**\n   * Arrête un son\n   */\n  private stop(name: string): void {\n    try {\n      const sound = this.sounds[name];\n      if (!sound) {\n        console.warn(`🔊 [CallService] Sound ${name} not found for stopping`);\n        return;\n      }\n\n      if (this.isPlaying[name]) {\n        console.log(`🔇 [CallService] Stopping sound: ${name}`);\n        sound.pause();\n        sound.currentTime = 0;\n        this.isPlaying[name] = false;\n      } else {\n        console.log(`🔇 [CallService] Sound ${name} was not playing`);\n      }\n    } catch (error) {\n      console.error(`❌ [CallService] Error stopping sound ${name}:`, error);\n    }\n  }\n\n  /**\n   * S'abonne aux appels entrants\n   */\n  private subscribeToIncomingCalls(): void {\n    console.log('🔔 [CallService] Setting up incoming call subscription...');\n\n    try {\n      this.apollo\n        .subscribe<{ incomingCall: IncomingCall }>({\n          query: INCOMING_CALL_SUBSCRIPTION,\n          errorPolicy: 'all', // Continuer même en cas d'erreur\n        })\n        .subscribe({\n          next: ({ data, errors }) => {\n            if (errors) {\n              console.warn(\n                '⚠️ [CallService] GraphQL errors in subscription:',\n                errors\n              );\n            }\n\n            if (data?.incomingCall) {\n              console.log('📞 [CallService] Incoming call received:', {\n                callId: data.incomingCall.id,\n                callType: data.incomingCall.type,\n                caller: data.incomingCall.caller?.username,\n                conversationId: data.incomingCall.conversationId,\n              });\n              this.handleIncomingCall(data.incomingCall);\n            }\n          },\n          error: (error) => {\n            console.error(\n              '❌ [CallService] Error in incoming call subscription:',\n              error\n            );\n\n            // Réessayer après 5 secondes en cas d'erreur\n            setTimeout(() => {\n              console.log(\n                '🔄 [CallService] Retrying incoming call subscription...'\n              );\n              this.subscribeToIncomingCalls();\n            }, 5000);\n          },\n          complete: () => {\n            console.log(\n              '🔚 [CallService] Incoming call subscription completed'\n            );\n            // Réessayer si la subscription se ferme de manière inattendue\n            setTimeout(() => {\n              console.log(\n                '🔄 [CallService] Restarting subscription after completion...'\n              );\n              this.subscribeToIncomingCalls();\n            }, 2000);\n          },\n        });\n    } catch (error) {\n      console.error('❌ [CallService] Failed to create subscription:', error);\n      // Réessayer après 3 secondes\n      setTimeout(() => {\n        this.subscribeToIncomingCalls();\n      }, 3000);\n    }\n  }\n\n  /**\n   * Méthode publique pour forcer la réinitialisation de la subscription\n   */\n  public reinitializeSubscription(): void {\n    console.log('🔄 [CallService] Manually reinitializing subscription...');\n    this.subscribeToIncomingCalls();\n    this.subscribeToCallStatusChanges();\n  }\n\n  /**\n   * S'abonne aux changements de statut d'appel\n   */\n  private subscribeToCallStatusChanges(): void {\n    console.log(\n      '📞 [CallService] Setting up call status change subscription...'\n    );\n\n    try {\n      this.apollo\n        .subscribe<{ callStatusChanged: Call }>({\n          query: CALL_STATUS_CHANGED_SUBSCRIPTION,\n          errorPolicy: 'all',\n        })\n        .subscribe({\n          next: ({ data, errors }) => {\n            if (errors) {\n              console.warn(\n                '⚠️ [CallService] GraphQL errors in call status subscription:',\n                errors\n              );\n            }\n\n            if (data?.callStatusChanged) {\n              console.log(\n                '📞 [CallService] Call status changed:',\n                data.callStatusChanged\n              );\n              this.handleCallStatusChange(data.callStatusChanged);\n            }\n          },\n          error: (error) => {\n            console.error(\n              '❌ [CallService] Error in call status subscription:',\n              error\n            );\n            // Réessayer après 5 secondes\n            setTimeout(() => {\n              this.subscribeToCallStatusChanges();\n            }, 5000);\n          },\n          complete: () => {\n            console.log('🔚 [CallService] Call status subscription completed');\n            // Réessayer si la subscription se ferme\n            setTimeout(() => {\n              this.subscribeToCallStatusChanges();\n            }, 2000);\n          },\n        });\n    } catch (error) {\n      console.error(\n        '❌ [CallService] Failed to create call status subscription:',\n        error\n      );\n      setTimeout(() => {\n        this.subscribeToCallStatusChanges();\n      }, 3000);\n    }\n  }\n\n  /**\n   * Gère un appel entrant\n   */\n  private handleIncomingCall(call: IncomingCall): void {\n    console.log('🔔 [CallService] Handling incoming call:', {\n      callId: call.id,\n      callType: call.type,\n      caller: call.caller?.username,\n      conversationId: call.conversationId,\n    });\n\n    this.incomingCall.next(call);\n    this.play('ringtone', true);\n\n    console.log(\n      '🔊 [CallService] Ringtone started, call notification sent to UI'\n    );\n  }\n\n  /**\n   * Gère les changements de statut d'appel\n   */\n  private handleCallStatusChange(call: Call): void {\n    console.log('📞 [CallService] Call status changed:', call.status);\n\n    switch (call.status) {\n      case CallStatus.REJECTED:\n        console.log('❌ [CallService] Call was rejected');\n        this.stop('ringtone');\n        this.play('call-end');\n        this.activeCall.next(null);\n        this.incomingCall.next(null);\n        break;\n\n      case CallStatus.ENDED:\n        console.log('📞 [CallService] Call ended');\n        this.stopAllSounds();\n        this.play('call-end');\n        this.activeCall.next(null);\n        this.incomingCall.next(null);\n        break;\n\n      case CallStatus.CONNECTED:\n        console.log('✅ [CallService] Call connected');\n        this.stop('ringtone');\n        this.play('call-connected');\n        this.activeCall.next(call);\n        this.incomingCall.next(null);\n        break;\n\n      case CallStatus.RINGING:\n        console.log('📞 [CallService] Call is ringing');\n        this.play('ringtone', true);\n        break;\n\n      default:\n        console.log('📞 [CallService] Unknown call status:', call.status);\n        break;\n    }\n  }\n\n  /**\n   * Initie un appel WebRTC\n   */\n  initiateCall(\n    recipientId: string,\n    callType: CallType,\n    conversationId?: string\n  ): Observable<Call> {\n    console.log('🔄 [CallService] Initiating call:', {\n      recipientId,\n      callType,\n      conversationId,\n    });\n\n    if (!recipientId) {\n      const error = new Error('Recipient ID is required');\n      console.error('❌ [CallService] initiateCall error:', error);\n      return throwError(() => error);\n    }\n\n    // Générer un ID unique pour l'appel\n    const callId = `call_${Date.now()}_${Math.random()\n      .toString(36)\n      .substr(2, 9)}`;\n\n    // Pour l'instant, utiliser une offre WebRTC factice\n    const offer = JSON.stringify({\n      type: 'offer',\n      sdp: 'v=0\\r\\no=- 0 0 IN IP4 127.0.0.1\\r\\ns=-\\r\\nt=0 0\\r\\n',\n    });\n\n    const variables = {\n      recipientId,\n      callType: callType,\n      callId,\n      offer,\n      conversationId,\n    };\n\n    console.log('📤 [CallService] Sending initiate call mutation:', variables);\n\n    return this.apollo\n      .mutate<{ initiateCall: Call }>({\n        mutation: INITIATE_CALL_MUTATION,\n        variables,\n      })\n      .pipe(\n        map((result) => {\n          console.log('✅ [CallService] Call initiated successfully:', result);\n\n          if (!result.data?.initiateCall) {\n            throw new Error('No call data received from server');\n          }\n\n          const call = result.data.initiateCall;\n          console.log('📞 [CallService] Call details:', {\n            id: call.id,\n            type: call.type,\n            status: call.status,\n            caller: call.caller?.username,\n            recipient: call.recipient?.username,\n          });\n\n          // Mettre à jour l'état local\n          this.activeCall.next(call);\n\n          return call;\n        }),\n        catchError((error) => {\n          console.error('❌ [CallService] initiateCall error:', error);\n          this.logger.error('Error initiating call:', error);\n\n          let errorMessage = \"Erreur lors de l'initiation de l'appel\";\n          if (error.networkError) {\n            errorMessage = 'Erreur de connexion réseau';\n          } else if (error.graphQLErrors?.length > 0) {\n            errorMessage = error.graphQLErrors[0].message || errorMessage;\n          }\n\n          return throwError(() => new Error(errorMessage));\n        })\n      );\n  }\n\n  /**\n   * Accepte un appel entrant\n   */\n  acceptCall(incomingCall: IncomingCall): Observable<Call> {\n    console.log('🔄 [CallService] Accepting call:', incomingCall.id);\n\n    // Générer une réponse WebRTC factice\n    const answer = JSON.stringify({\n      type: 'answer',\n      sdp: 'v=0\\r\\no=- 0 0 IN IP4 127.0.0.1\\r\\ns=-\\r\\nt=0 0\\r\\n',\n    });\n\n    return this.apollo\n      .mutate<{ acceptCall: Call }>({\n        mutation: ACCEPT_CALL_MUTATION,\n        variables: {\n          callId: incomingCall.id,\n          answer,\n        },\n      })\n      .pipe(\n        switchMap((result) => {\n          console.log('✅ [CallService] Call accepted successfully:', result);\n\n          if (!result.data?.acceptCall) {\n            throw new Error('No call data received from server');\n          }\n\n          const call = result.data.acceptCall;\n\n          // Arrêter la sonnerie\n          this.stop('ringtone');\n          this.play('call-connected');\n\n          // Démarrer les médias pour l'appel de manière asynchrone\n          return from(this.startMediaForCall(incomingCall, call));\n        }),\n        catchError((error) => {\n          console.error('❌ [CallService] acceptCall error:', error);\n          this.logger.error('Error accepting call:', error);\n          return throwError(\n            () => new Error(\"Erreur lors de l'acceptation de l'appel\")\n          );\n        })\n      );\n  }\n\n  /**\n   * Rejette un appel entrant\n   */\n  rejectCall(callId: string, reason?: string): Observable<CallSuccess> {\n    console.log('🔄 [CallService] Rejecting call:', callId, reason);\n\n    return this.apollo\n      .mutate<{ rejectCall: CallSuccess }>({\n        mutation: REJECT_CALL_MUTATION,\n        variables: {\n          callId,\n          reason: reason || 'User rejected',\n        },\n      })\n      .pipe(\n        map((result) => {\n          console.log('✅ [CallService] Call rejected successfully:', result);\n\n          if (!result.data?.rejectCall) {\n            throw new Error('No response received from server');\n          }\n\n          // Mettre à jour l'état local\n          this.incomingCall.next(null);\n          this.activeCall.next(null);\n\n          // Arrêter la sonnerie\n          this.stop('ringtone');\n\n          return result.data.rejectCall;\n        }),\n        catchError((error) => {\n          console.error('❌ [CallService] rejectCall error:', error);\n          this.logger.error('Error rejecting call:', error);\n          return throwError(() => new Error(\"Erreur lors du rejet de l'appel\"));\n        })\n      );\n  }\n\n  /**\n   * Termine un appel en cours\n   */\n  endCall(callId: string): Observable<CallSuccess> {\n    console.log('🔄 [CallService] Ending call:', callId);\n\n    return this.apollo\n      .mutate<{ endCall: CallSuccess }>({\n        mutation: END_CALL_MUTATION,\n        variables: {\n          callId,\n          feedback: null, // Pas de feedback pour l'instant\n        },\n      })\n      .pipe(\n        map((result) => {\n          console.log('✅ [CallService] Call ended successfully:', result);\n\n          if (!result.data?.endCall) {\n            throw new Error('No response received from server');\n          }\n\n          // Mettre à jour l'état local\n          this.activeCall.next(null);\n          this.incomingCall.next(null);\n\n          // Arrêter tous les sons\n          this.stopAllSounds();\n          this.play('call-end');\n\n          return result.data.endCall;\n        }),\n        catchError((error) => {\n          console.error('❌ [CallService] endCall error:', error);\n          this.logger.error('Error ending call:', error);\n          return throwError(\n            () => new Error(\"Erreur lors de la fin de l'appel\")\n          );\n        })\n      );\n  }\n\n  /**\n   * Bascule l'état des médias (audio/vidéo) pendant un appel\n   */\n  toggleMedia(\n    callId: string,\n    enableVideo?: boolean,\n    enableAudio?: boolean\n  ): Observable<CallSuccess> {\n    console.log('🔄 [CallService] Toggling media:', {\n      callId,\n      enableVideo,\n      enableAudio,\n    });\n\n    return this.apollo\n      .mutate<{ toggleCallMedia: CallSuccess }>({\n        mutation: TOGGLE_CALL_MEDIA_MUTATION,\n        variables: {\n          callId,\n          video: enableVideo,\n          audio: enableAudio,\n        },\n      })\n      .pipe(\n        map((result) => {\n          console.log('✅ [CallService] Media toggled successfully:', result);\n\n          if (!result.data?.toggleCallMedia) {\n            throw new Error('No response received from server');\n          }\n\n          return result.data.toggleCallMedia;\n        }),\n        catchError((error) => {\n          console.error('❌ [CallService] toggleMedia error:', error);\n          this.logger.error('Error toggling media:', error);\n          return throwError(\n            () => new Error('Erreur lors du changement des médias')\n          );\n        })\n      );\n  }\n\n  /**\n   * Démarre les médias pour un appel accepté\n   */\n  private async startMediaForCall(\n    incomingCall: IncomingCall,\n    call: Call\n  ): Promise<Call> {\n    console.log('🎥 [CallService] Call connected - playing connection sound');\n\n    // Jouer le son de connexion\n    this.play('call-connected');\n\n    // Mettre à jour l'état local\n    this.activeCall.next(call);\n    this.incomingCall.next(null); // Supprimer l'appel entrant\n\n    return call;\n  }\n\n  /**\n   * Active/désactive la vidéo\n   */\n  toggleVideo(): boolean {\n    this.isVideoEnabled = !this.isVideoEnabled;\n    console.log('📹 [CallService] Video toggled:', this.isVideoEnabled);\n    return this.isVideoEnabled;\n  }\n\n  /**\n   * Active/désactive l'audio\n   */\n  toggleAudio(): boolean {\n    this.isAudioEnabled = !this.isAudioEnabled;\n    console.log('🎤 [CallService] Audio toggled:', this.isAudioEnabled);\n    return this.isAudioEnabled;\n  }\n\n  /**\n   * Obtient l'état de la vidéo\n   */\n  getVideoEnabled(): boolean {\n    return this.isVideoEnabled;\n  }\n\n  /**\n   * Obtient l'état de l'audio\n   */\n  getAudioEnabled(): boolean {\n    return this.isAudioEnabled;\n  }\n\n  /**\n   * Arrête tous les sons\n   */\n  private stopAllSounds(): void {\n    console.log('🔇 [CallService] Stopping all sounds');\n    Object.keys(this.sounds).forEach((name) => {\n      this.stop(name);\n    });\n  }\n\n  /**\n   * Active les sons après interaction utilisateur (pour contourner les restrictions du navigateur)\n   */\n  public enableSounds(): void {\n    console.log('🔊 [CallService] Enabling sounds after user interaction');\n    Object.values(this.sounds).forEach((sound) => {\n      if (sound) {\n        sound.muted = false;\n        sound.volume = 0.7;\n        // Jouer et arrêter immédiatement pour \"débloquer\" l'audio\n        sound\n          .play()\n          .then(() => {\n            sound.pause();\n            sound.currentTime = 0;\n          })\n          .catch(() => {\n            // Ignorer les erreurs ici\n          });\n      }\n    });\n  }\n\n  ngOnDestroy(): void {\n    this.stopAllSounds();\n  }\n}\n"], "mappings": ";AAEA,SAASA,eAAe,EAAcC,UAAU,EAAMC,IAAI,QAAQ,MAAM;AACxE,SAASC,GAAG,EAAEC,UAAU,EAAEC,SAAS,QAAQ,gBAAgB;AAC3D,SAGEC,UAAU,QAGL,yBAAyB;AAChC,SACEC,sBAAsB,EACtBC,oBAAoB,EACpBC,oBAAoB,EACpBC,iBAAiB,EACjBC,0BAA0B,EAC1BC,0BAA0B,EAC1BC,gCAAgC,QAC3B,4BAA4B;;;;AAMnC,OAAM,MAAOC,WAAW;EAkBtBC,YAAoBC,MAAc,EAAUC,MAAqB;IAA7C,KAAAD,MAAM,GAANA,MAAM;IAAkB,KAAAC,MAAM,GAANA,MAAM;IAjBlD;IACQ,KAAAC,UAAU,GAAG,IAAIlB,eAAe,CAAc,IAAI,CAAC;IACnD,KAAAmB,YAAY,GAAG,IAAInB,eAAe,CAAsB,IAAI,CAAC;IAErE;IACO,KAAAoB,WAAW,GAAG,IAAI,CAACF,UAAU,CAACG,YAAY,EAAE;IAC5C,KAAAC,aAAa,GAAG,IAAI,CAACH,YAAY,CAACE,YAAY,EAAE;IAEvD;IACQ,KAAAE,MAAM,GAAwC,EAAE;IAChD,KAAAC,SAAS,GAA+B,EAAE;IAC1C,KAAAC,KAAK,GAAG,KAAK;IAErB;IACQ,KAAAC,cAAc,GAAG,IAAI;IACrB,KAAAC,cAAc,GAAG,IAAI;IAG3B,IAAI,CAACC,aAAa,EAAE;IACpB,IAAI,CAACC,uBAAuB,EAAE;EAChC;EAEA;;;EAGQA,uBAAuBA,CAAA;IAC7B;IACAC,UAAU,CAAC,MAAK;MACd,IAAI,CAACC,wBAAwB,EAAE;MAC/B,IAAI,CAACC,4BAA4B,EAAE;IACrC,CAAC,EAAE,IAAI,CAAC;IAER;IACAF,UAAU,CAAC,MAAK;MACd,IAAI,CAAC,IAAI,CAACX,YAAY,CAACc,KAAK,EAAE;QAC5BC,OAAO,CAACC,GAAG,CAAC,0DAA0D,CAAC;QACvE,IAAI,CAACJ,wBAAwB,EAAE;QAC/B,IAAI,CAACC,4BAA4B,EAAE;;IAEvC,CAAC,EAAE,IAAI,CAAC;EACV;EAEA;;;EAGQJ,aAAaA,CAAA;IACnB,IAAI,CAACQ,SAAS,CAAC,UAAU,EAAE,6BAA6B,CAAC;IACzD,IAAI,CAACA,SAAS,CAAC,UAAU,EAAE,6BAA6B,CAAC;IACzD,IAAI,CAACA,SAAS,CAAC,gBAAgB,EAAE,mCAAmC,CAAC;EACvE;EAEA;;;EAGQA,SAASA,CAACC,IAAY,EAAEC,IAAY;IAC1C,IAAI;MACF,MAAMC,KAAK,GAAG,IAAIC,KAAK,EAAE;MAEzB;MACAD,KAAK,CAACE,OAAO,GAAG,MAAM;MACtBF,KAAK,CAACG,MAAM,GAAG,GAAG;MAElB;MACAH,KAAK,CAACI,gBAAgB,CAAC,gBAAgB,EAAE,MAAK;QAC5CT,OAAO,CAACC,GAAG,CACT,yBAAyBE,IAAI,6BAA6BC,IAAI,EAAE,CACjE;MACH,CAAC,CAAC;MAEFC,KAAK,CAACI,gBAAgB,CAAC,OAAO,EAAGC,CAAC,IAAI;QACpCV,OAAO,CAACW,KAAK,CACX,uCAAuCR,IAAI,SAASC,IAAI,GAAG,EAC3DM,CAAC,CACF;QACDV,OAAO,CAACC,GAAG,CACT,mCAAmCE,IAAI,6BAA6B,CACrE;QAED;QACA,MAAMS,OAAO,GAAGR,IAAI,CAACS,UAAU,CAAC,GAAG,CAAC,GAAGT,IAAI,CAACU,SAAS,CAAC,CAAC,CAAC,GAAGV,IAAI;QAC/D,IAAIQ,OAAO,KAAKR,IAAI,EAAE;UACpBR,UAAU,CAAC,MAAK;YACdS,KAAK,CAACU,GAAG,GAAGH,OAAO;YACnBP,KAAK,CAACW,IAAI,EAAE;UACd,CAAC,EAAE,GAAG,CAAC;;MAEX,CAAC,CAAC;MAEFX,KAAK,CAACI,gBAAgB,CAAC,OAAO,EAAE,MAAK;QACnC,IAAI,CAACnB,SAAS,CAACa,IAAI,CAAC,GAAG,KAAK;QAC5BH,OAAO,CAACC,GAAG,CAAC,0BAA0BE,IAAI,QAAQ,CAAC;MACrD,CAAC,CAAC;MAEF;MACAE,KAAK,CAACU,GAAG,GAAGX,IAAI;MAChBC,KAAK,CAACW,IAAI,EAAE;MAEZ,IAAI,CAAC3B,MAAM,CAACc,IAAI,CAAC,GAAGE,KAAK;MACzB,IAAI,CAACf,SAAS,CAACa,IAAI,CAAC,GAAG,KAAK;MAE5BH,OAAO,CAACC,GAAG,CAAC,kCAAkCE,IAAI,SAASC,IAAI,EAAE,CAAC;KACnE,CAAC,OAAOO,KAAK,EAAE;MACdX,OAAO,CAACW,KAAK,CACX,oDAAoDR,IAAI,GAAG,EAC3DQ,KAAK,CACN;;EAEL;EAEA;;;EAGQM,IAAIA,CAACd,IAAY,EAAEe,IAAA,GAAgB,KAAK;IAC9C,IAAI,IAAI,CAAC3B,KAAK,EAAE;MACdS,OAAO,CAACC,GAAG,CAAC,0BAA0BE,IAAI,QAAQ,CAAC;MACnD;;IAGF,IAAI;MACF,MAAMgB,KAAK,GAAG,IAAI,CAAC9B,MAAM,CAACc,IAAI,CAAC;MAC/B,IAAI,CAACgB,KAAK,EAAE;QACVnB,OAAO,CAACoB,IAAI,CAAC,0BAA0BjB,IAAI,YAAY,CAAC;QACxD;;MAGFgB,KAAK,CAACD,IAAI,GAAGA,IAAI;MACjBC,KAAK,CAACX,MAAM,GAAG,GAAG,CAAC,CAAC;MAEpB,IAAI,CAAC,IAAI,CAAClB,SAAS,CAACa,IAAI,CAAC,EAAE;QACzBgB,KAAK,CAACE,WAAW,GAAG,CAAC;QACrBrB,OAAO,CAACC,GAAG,CAAC,mCAAmCE,IAAI,WAAWe,IAAI,GAAG,CAAC;QAEtEC,KAAK,CACFF,IAAI,EAAE,CACNK,IAAI,CAAC,MAAK;UACTtB,OAAO,CAACC,GAAG,CAAC,yBAAyBE,IAAI,uBAAuB,CAAC;UACjE,IAAI,CAACb,SAAS,CAACa,IAAI,CAAC,GAAG,IAAI;QAC7B,CAAC,CAAC,CACDoB,KAAK,CAAEZ,KAAK,IAAI;UACfX,OAAO,CAACW,KAAK,CACX,uCAAuCR,IAAI,GAAG,EAC9CQ,KAAK,CACN;UACD;UACA,IAAIA,KAAK,CAACR,IAAI,KAAK,iBAAiB,EAAE;YACpCH,OAAO,CAACC,GAAG,CACT,0BAA0BE,IAAI,wDAAwD,CACvF;;QAEL,CAAC,CAAC;OACL,MAAM;QACLH,OAAO,CAACC,GAAG,CAAC,0BAA0BE,IAAI,kBAAkB,CAAC;;KAEhE,CAAC,OAAOQ,KAAK,EAAE;MACdX,OAAO,CAACW,KAAK,CACX,4CAA4CR,IAAI,GAAG,EACnDQ,KAAK,CACN;;EAEL;EAEA;;;EAGQa,IAAIA,CAACrB,IAAY;IACvB,IAAI;MACF,MAAMgB,KAAK,GAAG,IAAI,CAAC9B,MAAM,CAACc,IAAI,CAAC;MAC/B,IAAI,CAACgB,KAAK,EAAE;QACVnB,OAAO,CAACoB,IAAI,CAAC,0BAA0BjB,IAAI,yBAAyB,CAAC;QACrE;;MAGF,IAAI,IAAI,CAACb,SAAS,CAACa,IAAI,CAAC,EAAE;QACxBH,OAAO,CAACC,GAAG,CAAC,oCAAoCE,IAAI,EAAE,CAAC;QACvDgB,KAAK,CAACM,KAAK,EAAE;QACbN,KAAK,CAACE,WAAW,GAAG,CAAC;QACrB,IAAI,CAAC/B,SAAS,CAACa,IAAI,CAAC,GAAG,KAAK;OAC7B,MAAM;QACLH,OAAO,CAACC,GAAG,CAAC,0BAA0BE,IAAI,kBAAkB,CAAC;;KAEhE,CAAC,OAAOQ,KAAK,EAAE;MACdX,OAAO,CAACW,KAAK,CAAC,wCAAwCR,IAAI,GAAG,EAAEQ,KAAK,CAAC;;EAEzE;EAEA;;;EAGQd,wBAAwBA,CAAA;IAC9BG,OAAO,CAACC,GAAG,CAAC,2DAA2D,CAAC;IAExE,IAAI;MACF,IAAI,CAACnB,MAAM,CACR4C,SAAS,CAAiC;QACzCC,KAAK,EAAEjD,0BAA0B;QACjCkD,WAAW,EAAE,KAAK,CAAE;OACrB,CAAC,CACDF,SAAS,CAAC;QACTG,IAAI,EAAEA,CAAC;UAAEC,IAAI;UAAEC;QAAM,CAAE,KAAI;UACzB,IAAIA,MAAM,EAAE;YACV/B,OAAO,CAACoB,IAAI,CACV,kDAAkD,EAClDW,MAAM,CACP;;UAGH,IAAID,IAAI,EAAE7C,YAAY,EAAE;YACtBe,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAE;cACtD+B,MAAM,EAAEF,IAAI,CAAC7C,YAAY,CAACgD,EAAE;cAC5BC,QAAQ,EAAEJ,IAAI,CAAC7C,YAAY,CAACkD,IAAI;cAChCC,MAAM,EAAEN,IAAI,CAAC7C,YAAY,CAACmD,MAAM,EAAEC,QAAQ;cAC1CC,cAAc,EAAER,IAAI,CAAC7C,YAAY,CAACqD;aACnC,CAAC;YACF,IAAI,CAACC,kBAAkB,CAACT,IAAI,CAAC7C,YAAY,CAAC;;QAE9C,CAAC;QACD0B,KAAK,EAAGA,KAAK,IAAI;UACfX,OAAO,CAACW,KAAK,CACX,sDAAsD,EACtDA,KAAK,CACN;UAED;UACAf,UAAU,CAAC,MAAK;YACdI,OAAO,CAACC,GAAG,CACT,yDAAyD,CAC1D;YACD,IAAI,CAACJ,wBAAwB,EAAE;UACjC,CAAC,EAAE,IAAI,CAAC;QACV,CAAC;QACD2C,QAAQ,EAAEA,CAAA,KAAK;UACbxC,OAAO,CAACC,GAAG,CACT,uDAAuD,CACxD;UACD;UACAL,UAAU,CAAC,MAAK;YACdI,OAAO,CAACC,GAAG,CACT,8DAA8D,CAC/D;YACD,IAAI,CAACJ,wBAAwB,EAAE;UACjC,CAAC,EAAE,IAAI,CAAC;QACV;OACD,CAAC;KACL,CAAC,OAAOc,KAAK,EAAE;MACdX,OAAO,CAACW,KAAK,CAAC,gDAAgD,EAAEA,KAAK,CAAC;MACtE;MACAf,UAAU,CAAC,MAAK;QACd,IAAI,CAACC,wBAAwB,EAAE;MACjC,CAAC,EAAE,IAAI,CAAC;;EAEZ;EAEA;;;EAGO4C,wBAAwBA,CAAA;IAC7BzC,OAAO,CAACC,GAAG,CAAC,0DAA0D,CAAC;IACvE,IAAI,CAACJ,wBAAwB,EAAE;IAC/B,IAAI,CAACC,4BAA4B,EAAE;EACrC;EAEA;;;EAGQA,4BAA4BA,CAAA;IAClCE,OAAO,CAACC,GAAG,CACT,gEAAgE,CACjE;IAED,IAAI;MACF,IAAI,CAACnB,MAAM,CACR4C,SAAS,CAA8B;QACtCC,KAAK,EAAEhD,gCAAgC;QACvCiD,WAAW,EAAE;OACd,CAAC,CACDF,SAAS,CAAC;QACTG,IAAI,EAAEA,CAAC;UAAEC,IAAI;UAAEC;QAAM,CAAE,KAAI;UACzB,IAAIA,MAAM,EAAE;YACV/B,OAAO,CAACoB,IAAI,CACV,8DAA8D,EAC9DW,MAAM,CACP;;UAGH,IAAID,IAAI,EAAEY,iBAAiB,EAAE;YAC3B1C,OAAO,CAACC,GAAG,CACT,uCAAuC,EACvC6B,IAAI,CAACY,iBAAiB,CACvB;YACD,IAAI,CAACC,sBAAsB,CAACb,IAAI,CAACY,iBAAiB,CAAC;;QAEvD,CAAC;QACD/B,KAAK,EAAGA,KAAK,IAAI;UACfX,OAAO,CAACW,KAAK,CACX,oDAAoD,EACpDA,KAAK,CACN;UACD;UACAf,UAAU,CAAC,MAAK;YACd,IAAI,CAACE,4BAA4B,EAAE;UACrC,CAAC,EAAE,IAAI,CAAC;QACV,CAAC;QACD0C,QAAQ,EAAEA,CAAA,KAAK;UACbxC,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;UAClE;UACAL,UAAU,CAAC,MAAK;YACd,IAAI,CAACE,4BAA4B,EAAE;UACrC,CAAC,EAAE,IAAI,CAAC;QACV;OACD,CAAC;KACL,CAAC,OAAOa,KAAK,EAAE;MACdX,OAAO,CAACW,KAAK,CACX,4DAA4D,EAC5DA,KAAK,CACN;MACDf,UAAU,CAAC,MAAK;QACd,IAAI,CAACE,4BAA4B,EAAE;MACrC,CAAC,EAAE,IAAI,CAAC;;EAEZ;EAEA;;;EAGQyC,kBAAkBA,CAACK,IAAkB;IAC3C5C,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAE;MACtD+B,MAAM,EAAEY,IAAI,CAACX,EAAE;MACfC,QAAQ,EAAEU,IAAI,CAACT,IAAI;MACnBC,MAAM,EAAEQ,IAAI,CAACR,MAAM,EAAEC,QAAQ;MAC7BC,cAAc,EAAEM,IAAI,CAACN;KACtB,CAAC;IAEF,IAAI,CAACrD,YAAY,CAAC4C,IAAI,CAACe,IAAI,CAAC;IAC5B,IAAI,CAAC3B,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC;IAE3BjB,OAAO,CAACC,GAAG,CACT,iEAAiE,CAClE;EACH;EAEA;;;EAGQ0C,sBAAsBA,CAACC,IAAU;IACvC5C,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE2C,IAAI,CAACC,MAAM,CAAC;IAEjE,QAAQD,IAAI,CAACC,MAAM;MACjB,KAAKzE,UAAU,CAAC0E,QAAQ;QACtB9C,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;QAChD,IAAI,CAACuB,IAAI,CAAC,UAAU,CAAC;QACrB,IAAI,CAACP,IAAI,CAAC,UAAU,CAAC;QACrB,IAAI,CAACjC,UAAU,CAAC6C,IAAI,CAAC,IAAI,CAAC;QAC1B,IAAI,CAAC5C,YAAY,CAAC4C,IAAI,CAAC,IAAI,CAAC;QAC5B;MAEF,KAAKzD,UAAU,CAAC2E,KAAK;QACnB/C,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;QAC1C,IAAI,CAAC+C,aAAa,EAAE;QACpB,IAAI,CAAC/B,IAAI,CAAC,UAAU,CAAC;QACrB,IAAI,CAACjC,UAAU,CAAC6C,IAAI,CAAC,IAAI,CAAC;QAC1B,IAAI,CAAC5C,YAAY,CAAC4C,IAAI,CAAC,IAAI,CAAC;QAC5B;MAEF,KAAKzD,UAAU,CAAC6E,SAAS;QACvBjD,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;QAC7C,IAAI,CAACuB,IAAI,CAAC,UAAU,CAAC;QACrB,IAAI,CAACP,IAAI,CAAC,gBAAgB,CAAC;QAC3B,IAAI,CAACjC,UAAU,CAAC6C,IAAI,CAACe,IAAI,CAAC;QAC1B,IAAI,CAAC3D,YAAY,CAAC4C,IAAI,CAAC,IAAI,CAAC;QAC5B;MAEF,KAAKzD,UAAU,CAAC8E,OAAO;QACrBlD,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;QAC/C,IAAI,CAACgB,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC;QAC3B;MAEF;QACEjB,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE2C,IAAI,CAACC,MAAM,CAAC;QACjE;;EAEN;EAEA;;;EAGAM,YAAYA,CACVC,WAAmB,EACnBlB,QAAkB,EAClBI,cAAuB;IAEvBtC,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAE;MAC/CmD,WAAW;MACXlB,QAAQ;MACRI;KACD,CAAC;IAEF,IAAI,CAACc,WAAW,EAAE;MAChB,MAAMzC,KAAK,GAAG,IAAI0C,KAAK,CAAC,0BAA0B,CAAC;MACnDrD,OAAO,CAACW,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3D,OAAO5C,UAAU,CAAC,MAAM4C,KAAK,CAAC;;IAGhC;IACA,MAAMqB,MAAM,GAAG,QAAQsB,IAAI,CAACC,GAAG,EAAE,IAAIC,IAAI,CAACC,MAAM,EAAE,CAC/CC,QAAQ,CAAC,EAAE,CAAC,CACZC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;IAEjB;IACA,MAAMC,KAAK,GAAGC,IAAI,CAACC,SAAS,CAAC;MAC3B3B,IAAI,EAAE,OAAO;MACb4B,GAAG,EAAE;KACN,CAAC;IAEF,MAAMC,SAAS,GAAG;MAChBZ,WAAW;MACXlB,QAAQ,EAAEA,QAAQ;MAClBF,MAAM;MACN4B,KAAK;MACLtB;KACD;IAEDtC,OAAO,CAACC,GAAG,CAAC,kDAAkD,EAAE+D,SAAS,CAAC;IAE1E,OAAO,IAAI,CAAClF,MAAM,CACfmF,MAAM,CAAyB;MAC9BC,QAAQ,EAAE7F,sBAAsB;MAChC2F;KACD,CAAC,CACDG,IAAI,CACHlG,GAAG,CAAEmG,MAAM,IAAI;MACbpE,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAEmE,MAAM,CAAC;MAEnE,IAAI,CAACA,MAAM,CAACtC,IAAI,EAAEqB,YAAY,EAAE;QAC9B,MAAM,IAAIE,KAAK,CAAC,mCAAmC,CAAC;;MAGtD,MAAMT,IAAI,GAAGwB,MAAM,CAACtC,IAAI,CAACqB,YAAY;MACrCnD,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE;QAC5CgC,EAAE,EAAEW,IAAI,CAACX,EAAE;QACXE,IAAI,EAAES,IAAI,CAACT,IAAI;QACfU,MAAM,EAAED,IAAI,CAACC,MAAM;QACnBT,MAAM,EAAEQ,IAAI,CAACR,MAAM,EAAEC,QAAQ;QAC7BgC,SAAS,EAAEzB,IAAI,CAACyB,SAAS,EAAEhC;OAC5B,CAAC;MAEF;MACA,IAAI,CAACrD,UAAU,CAAC6C,IAAI,CAACe,IAAI,CAAC;MAE1B,OAAOA,IAAI;IACb,CAAC,CAAC,EACF1E,UAAU,CAAEyC,KAAK,IAAI;MACnBX,OAAO,CAACW,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3D,IAAI,CAAC5B,MAAM,CAAC4B,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAElD,IAAI2D,YAAY,GAAG,wCAAwC;MAC3D,IAAI3D,KAAK,CAAC4D,YAAY,EAAE;QACtBD,YAAY,GAAG,4BAA4B;OAC5C,MAAM,IAAI3D,KAAK,CAAC6D,aAAa,EAAEC,MAAM,GAAG,CAAC,EAAE;QAC1CH,YAAY,GAAG3D,KAAK,CAAC6D,aAAa,CAAC,CAAC,CAAC,CAACE,OAAO,IAAIJ,YAAY;;MAG/D,OAAOvG,UAAU,CAAC,MAAM,IAAIsF,KAAK,CAACiB,YAAY,CAAC,CAAC;IAClD,CAAC,CAAC,CACH;EACL;EAEA;;;EAGAK,UAAUA,CAAC1F,YAA0B;IACnCe,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEhB,YAAY,CAACgD,EAAE,CAAC;IAEhE;IACA,MAAM2C,MAAM,GAAGf,IAAI,CAACC,SAAS,CAAC;MAC5B3B,IAAI,EAAE,QAAQ;MACd4B,GAAG,EAAE;KACN,CAAC;IAEF,OAAO,IAAI,CAACjF,MAAM,CACfmF,MAAM,CAAuB;MAC5BC,QAAQ,EAAE5F,oBAAoB;MAC9B0F,SAAS,EAAE;QACThC,MAAM,EAAE/C,YAAY,CAACgD,EAAE;QACvB2C;;KAEH,CAAC,CACDT,IAAI,CACHhG,SAAS,CAAEiG,MAAM,IAAI;MACnBpE,OAAO,CAACC,GAAG,CAAC,6CAA6C,EAAEmE,MAAM,CAAC;MAElE,IAAI,CAACA,MAAM,CAACtC,IAAI,EAAE6C,UAAU,EAAE;QAC5B,MAAM,IAAItB,KAAK,CAAC,mCAAmC,CAAC;;MAGtD,MAAMT,IAAI,GAAGwB,MAAM,CAACtC,IAAI,CAAC6C,UAAU;MAEnC;MACA,IAAI,CAACnD,IAAI,CAAC,UAAU,CAAC;MACrB,IAAI,CAACP,IAAI,CAAC,gBAAgB,CAAC;MAE3B;MACA,OAAOjD,IAAI,CAAC,IAAI,CAAC6G,iBAAiB,CAAC5F,YAAY,EAAE2D,IAAI,CAAC,CAAC;IACzD,CAAC,CAAC,EACF1E,UAAU,CAAEyC,KAAK,IAAI;MACnBX,OAAO,CAACW,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzD,IAAI,CAAC5B,MAAM,CAAC4B,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MACjD,OAAO5C,UAAU,CACf,MAAM,IAAIsF,KAAK,CAAC,yCAAyC,CAAC,CAC3D;IACH,CAAC,CAAC,CACH;EACL;EAEA;;;EAGAyB,UAAUA,CAAC9C,MAAc,EAAE+C,MAAe;IACxC/E,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE+B,MAAM,EAAE+C,MAAM,CAAC;IAE/D,OAAO,IAAI,CAACjG,MAAM,CACfmF,MAAM,CAA8B;MACnCC,QAAQ,EAAE3F,oBAAoB;MAC9ByF,SAAS,EAAE;QACThC,MAAM;QACN+C,MAAM,EAAEA,MAAM,IAAI;;KAErB,CAAC,CACDZ,IAAI,CACHlG,GAAG,CAAEmG,MAAM,IAAI;MACbpE,OAAO,CAACC,GAAG,CAAC,6CAA6C,EAAEmE,MAAM,CAAC;MAElE,IAAI,CAACA,MAAM,CAACtC,IAAI,EAAEgD,UAAU,EAAE;QAC5B,MAAM,IAAIzB,KAAK,CAAC,kCAAkC,CAAC;;MAGrD;MACA,IAAI,CAACpE,YAAY,CAAC4C,IAAI,CAAC,IAAI,CAAC;MAC5B,IAAI,CAAC7C,UAAU,CAAC6C,IAAI,CAAC,IAAI,CAAC;MAE1B;MACA,IAAI,CAACL,IAAI,CAAC,UAAU,CAAC;MAErB,OAAO4C,MAAM,CAACtC,IAAI,CAACgD,UAAU;IAC/B,CAAC,CAAC,EACF5G,UAAU,CAAEyC,KAAK,IAAI;MACnBX,OAAO,CAACW,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzD,IAAI,CAAC5B,MAAM,CAAC4B,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MACjD,OAAO5C,UAAU,CAAC,MAAM,IAAIsF,KAAK,CAAC,iCAAiC,CAAC,CAAC;IACvE,CAAC,CAAC,CACH;EACL;EAEA;;;EAGA2B,OAAOA,CAAChD,MAAc;IACpBhC,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE+B,MAAM,CAAC;IAEpD,OAAO,IAAI,CAAClD,MAAM,CACfmF,MAAM,CAA2B;MAChCC,QAAQ,EAAE1F,iBAAiB;MAC3BwF,SAAS,EAAE;QACThC,MAAM;QACNiD,QAAQ,EAAE,IAAI,CAAE;;KAEnB,CAAC,CACDd,IAAI,CACHlG,GAAG,CAAEmG,MAAM,IAAI;MACbpE,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAEmE,MAAM,CAAC;MAE/D,IAAI,CAACA,MAAM,CAACtC,IAAI,EAAEkD,OAAO,EAAE;QACzB,MAAM,IAAI3B,KAAK,CAAC,kCAAkC,CAAC;;MAGrD;MACA,IAAI,CAACrE,UAAU,CAAC6C,IAAI,CAAC,IAAI,CAAC;MAC1B,IAAI,CAAC5C,YAAY,CAAC4C,IAAI,CAAC,IAAI,CAAC;MAE5B;MACA,IAAI,CAACmB,aAAa,EAAE;MACpB,IAAI,CAAC/B,IAAI,CAAC,UAAU,CAAC;MAErB,OAAOmD,MAAM,CAACtC,IAAI,CAACkD,OAAO;IAC5B,CAAC,CAAC,EACF9G,UAAU,CAAEyC,KAAK,IAAI;MACnBX,OAAO,CAACW,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD,IAAI,CAAC5B,MAAM,CAAC4B,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC9C,OAAO5C,UAAU,CACf,MAAM,IAAIsF,KAAK,CAAC,kCAAkC,CAAC,CACpD;IACH,CAAC,CAAC,CACH;EACL;EAEA;;;EAGA6B,WAAWA,CACTlD,MAAc,EACdmD,WAAqB,EACrBC,WAAqB;IAErBpF,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE;MAC9C+B,MAAM;MACNmD,WAAW;MACXC;KACD,CAAC;IAEF,OAAO,IAAI,CAACtG,MAAM,CACfmF,MAAM,CAAmC;MACxCC,QAAQ,EAAEzF,0BAA0B;MACpCuF,SAAS,EAAE;QACThC,MAAM;QACNqD,KAAK,EAAEF,WAAW;QAClB9E,KAAK,EAAE+E;;KAEV,CAAC,CACDjB,IAAI,CACHlG,GAAG,CAAEmG,MAAM,IAAI;MACbpE,OAAO,CAACC,GAAG,CAAC,6CAA6C,EAAEmE,MAAM,CAAC;MAElE,IAAI,CAACA,MAAM,CAACtC,IAAI,EAAEwD,eAAe,EAAE;QACjC,MAAM,IAAIjC,KAAK,CAAC,kCAAkC,CAAC;;MAGrD,OAAOe,MAAM,CAACtC,IAAI,CAACwD,eAAe;IACpC,CAAC,CAAC,EACFpH,UAAU,CAAEyC,KAAK,IAAI;MACnBX,OAAO,CAACW,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;MAC1D,IAAI,CAAC5B,MAAM,CAAC4B,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MACjD,OAAO5C,UAAU,CACf,MAAM,IAAIsF,KAAK,CAAC,sCAAsC,CAAC,CACxD;IACH,CAAC,CAAC,CACH;EACL;EAEA;;;EAGcwB,iBAAiBA,CAC7B5F,YAA0B,EAC1B2D,IAAU;IAAA,IAAA2C,KAAA;IAAA,OAAAC,iBAAA;MAEVxF,OAAO,CAACC,GAAG,CAAC,4DAA4D,CAAC;MAEzE;MACAsF,KAAI,CAACtE,IAAI,CAAC,gBAAgB,CAAC;MAE3B;MACAsE,KAAI,CAACvG,UAAU,CAAC6C,IAAI,CAACe,IAAI,CAAC;MAC1B2C,KAAI,CAACtG,YAAY,CAAC4C,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;MAE9B,OAAOe,IAAI;IAAC;EACd;EAEA;;;EAGA6C,WAAWA,CAAA;IACT,IAAI,CAACjG,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;IAC1CQ,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE,IAAI,CAACT,cAAc,CAAC;IACnE,OAAO,IAAI,CAACA,cAAc;EAC5B;EAEA;;;EAGAkG,WAAWA,CAAA;IACT,IAAI,CAACjG,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;IAC1CO,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE,IAAI,CAACR,cAAc,CAAC;IACnE,OAAO,IAAI,CAACA,cAAc;EAC5B;EAEA;;;EAGAkG,eAAeA,CAAA;IACb,OAAO,IAAI,CAACnG,cAAc;EAC5B;EAEA;;;EAGAoG,eAAeA,CAAA;IACb,OAAO,IAAI,CAACnG,cAAc;EAC5B;EAEA;;;EAGQuD,aAAaA,CAAA;IACnBhD,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;IACnD4F,MAAM,CAACC,IAAI,CAAC,IAAI,CAACzG,MAAM,CAAC,CAAC0G,OAAO,CAAE5F,IAAI,IAAI;MACxC,IAAI,CAACqB,IAAI,CAACrB,IAAI,CAAC;IACjB,CAAC,CAAC;EACJ;EAEA;;;EAGO6F,YAAYA,CAAA;IACjBhG,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC;IACtE4F,MAAM,CAACI,MAAM,CAAC,IAAI,CAAC5G,MAAM,CAAC,CAAC0G,OAAO,CAAE5E,KAAK,IAAI;MAC3C,IAAIA,KAAK,EAAE;QACTA,KAAK,CAAC5B,KAAK,GAAG,KAAK;QACnB4B,KAAK,CAACX,MAAM,GAAG,GAAG;QAClB;QACAW,KAAK,CACFF,IAAI,EAAE,CACNK,IAAI,CAAC,MAAK;UACTH,KAAK,CAACM,KAAK,EAAE;UACbN,KAAK,CAACE,WAAW,GAAG,CAAC;QACvB,CAAC,CAAC,CACDE,KAAK,CAAC,MAAK;UACV;QAAA,CACD,CAAC;;IAER,CAAC,CAAC;EACJ;EAEA2E,WAAWA,CAAA;IACT,IAAI,CAAClD,aAAa,EAAE;EACtB;;;uBAptBWpE,WAAW,EAAAuH,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,aAAA;IAAA;EAAA;;;aAAX5H,WAAW;MAAA6H,OAAA,EAAX7H,WAAW,CAAA8H,IAAA;MAAAC,UAAA,EAFV;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}