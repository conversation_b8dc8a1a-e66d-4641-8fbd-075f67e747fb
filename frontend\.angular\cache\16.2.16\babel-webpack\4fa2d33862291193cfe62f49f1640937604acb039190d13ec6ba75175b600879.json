{"ast": null, "code": "import { CallType, CallStatus } from '../../models/message.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/call.service\";\nimport * as i2 from \"../../services/logger.service\";\nimport * as i3 from \"@angular/common\";\nfunction ActiveCallComponent_div_0_div_9_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32)(1, \"div\", 22);\n    i0.ɵɵelement(2, \"i\", 33);\n    i0.ɵɵelementStart(3, \"p\", 34);\n    i0.ɵɵtext(4, \"Cam\\u00E9ra d\\u00E9sactiv\\u00E9e\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ActiveCallComponent_div_0_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 19)(1, \"div\", 20)(2, \"div\", 21)(3, \"div\", 22)(4, \"div\", 23);\n    i0.ɵɵelement(5, \"i\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 25);\n    i0.ɵɵtext(7, \"Vid\\u00E9o en cours...\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 26);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(10, ActiveCallComponent_div_0_div_9_div_10_Template, 5, 0, \"div\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 28)(12, \"div\", 29)(13, \"div\", 22);\n    i0.ɵɵelement(14, \"img\", 30);\n    i0.ɵɵelementStart(15, \"p\", 31);\n    i0.ɵɵtext(16, \"Vous\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getOtherParticipantName(), \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isVideoMuted);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"src\", ctx_r1.getOtherParticipantAvatar(), i0.ɵɵsanitizeUrl)(\"alt\", ctx_r1.getOtherParticipantName());\n  }\n}\nfunction ActiveCallComponent_div_0_div_10_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46);\n    i0.ɵɵelement(1, \"i\", 47);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActiveCallComponent_div_0_div_10_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 48);\n  }\n  if (rf & 2) {\n    const i_r7 = ctx.$implicit;\n    const ctx_r6 = i0.ɵɵnextContext(3);\n    i0.ɵɵstyleProp(\"height\", 15 + i_r7 % 5 * 8, \"px\")(\"animation-delay\", i_r7 * 100, \"ms\");\n    i0.ɵɵclassProp(\"animate-pulse\", !ctx_r6.isAudioMuted);\n  }\n}\nconst _c0 = function () {\n  return [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15];\n};\nfunction ActiveCallComponent_div_0_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"div\", 35);\n    i0.ɵɵelement(2, \"div\", 36)(3, \"div\", 37)(4, \"div\", 38);\n    i0.ɵɵelementStart(5, \"div\", 39);\n    i0.ɵɵelement(6, \"img\", 40);\n    i0.ɵɵtemplate(7, ActiveCallComponent_div_0_div_10_div_7_Template, 2, 0, \"div\", 41);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"h2\", 42);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"p\", 43);\n    i0.ɵɵtext(11, \"Appel audio en cours\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 44);\n    i0.ɵɵtemplate(13, ActiveCallComponent_div_0_div_10_div_13_Template, 1, 6, \"div\", 45);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"src\", ctx_r2.getOtherParticipantAvatar(), i0.ɵɵsanitizeUrl)(\"alt\", ctx_r2.getOtherParticipantName());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isAudioMuted);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.getOtherParticipantName(), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(5, _c0));\n  }\n}\nfunction ActiveCallComponent_div_0_button_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 12);\n    i0.ɵɵlistener(\"click\", function ActiveCallComponent_div_0_button_15_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r8 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r8.toggleCamera());\n    });\n    i0.ɵɵelement(1, \"i\", 13);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMap(ctx_r3.isVideoMuted ? \"bg-red-500 hover:bg-red-600 shadow-red-500/50\" : \"bg-blue-500 hover:bg-blue-600 shadow-blue-500/50\");\n    i0.ɵɵproperty(\"title\", ctx_r3.isVideoMuted ? \"Activer la cam\\u00E9ra\" : \"D\\u00E9sactiver la cam\\u00E9ra\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(ctx_r3.isVideoMuted ? \"fa-video-slash\" : \"fa-video\");\n  }\n}\nfunction ActiveCallComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2)(2, \"div\", 3);\n    i0.ɵɵelement(3, \"div\", 4);\n    i0.ɵɵelementStart(4, \"span\", 5);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 6);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(8, \"div\", 7);\n    i0.ɵɵtemplate(9, ActiveCallComponent_div_0_div_9_Template, 17, 4, \"div\", 8);\n    i0.ɵɵtemplate(10, ActiveCallComponent_div_0_div_10_Template, 14, 6, \"div\", 9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 10)(12, \"div\", 11)(13, \"button\", 12);\n    i0.ɵɵlistener(\"click\", function ActiveCallComponent_div_0_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.toggleMicrophone());\n    });\n    i0.ɵɵelement(14, \"i\", 13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(15, ActiveCallComponent_div_0_button_15_Template, 2, 5, \"button\", 14);\n    i0.ɵɵelementStart(16, \"button\", 12);\n    i0.ɵɵlistener(\"click\", function ActiveCallComponent_div_0_Template_button_click_16_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r12 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r12.toggleSpeaker());\n    });\n    i0.ɵɵelement(17, \"i\", 13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"button\", 15);\n    i0.ɵɵlistener(\"click\", function ActiveCallComponent_div_0_Template_button_click_18_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r13 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r13.testAudio());\n    });\n    i0.ɵɵelement(19, \"i\", 16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function ActiveCallComponent_div_0_Template_button_click_20_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r14 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r14.endCall());\n    });\n    i0.ɵɵelement(21, \"i\", 18);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r0.getCallStatusText());\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.callDuration);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isVideoCall());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.isVideoCall());\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassMap(ctx_r0.isAudioMuted ? \"bg-red-500 hover:bg-red-600 shadow-red-500/50\" : \"bg-blue-500 hover:bg-blue-600 shadow-blue-500/50\");\n    i0.ɵɵproperty(\"title\", ctx_r0.isAudioMuted ? \"Activer le micro\" : \"Couper le micro\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(ctx_r0.isAudioMuted ? \"fa-microphone-slash\" : \"fa-microphone\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isVideoCall());\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(ctx_r0.isSpeakerOn ? \"bg-blue-500 hover:bg-blue-600 shadow-blue-500/50\" : \"bg-gray-600 hover:bg-gray-700 shadow-gray-600/50\");\n    i0.ɵɵproperty(\"title\", ctx_r0.isSpeakerOn ? \"D\\u00E9sactiver le haut-parleur\" : \"Activer le haut-parleur\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(ctx_r0.isSpeakerOn ? \"fa-volume-up\" : \"fa-volume-mute\");\n  }\n}\nexport class ActiveCallComponent {\n  constructor(callService, logger) {\n    this.callService = callService;\n    this.logger = logger;\n    this.activeCall = null;\n    this.callDuration = '00:00';\n    this.isAudioMuted = false;\n    this.isVideoMuted = false;\n    this.isSpeakerOn = true;\n    this.callStartTime = null;\n    this.subscriptions = [];\n    // Exposer les énums au template\n    this.CallType = CallType;\n    this.CallStatus = CallStatus;\n  }\n  ngOnInit() {\n    // S'abonner à l'appel actif\n    const activeCallSub = this.callService.activeCall$.subscribe(call => {\n      const previousCall = this.activeCall;\n      this.activeCall = call;\n      console.log('📞 [ActiveCall] Active call updated:', call);\n      if (call && call.status === CallStatus.CONNECTED) {\n        if (!previousCall || previousCall.id !== call.id) {\n          console.log('✅ [ActiveCall] Call connected, starting timer');\n          this.startCallTimer();\n        }\n      } else if (!call || call.status !== CallStatus.CONNECTED) {\n        this.stopCallTimer();\n      }\n    });\n    this.subscriptions.push(activeCallSub);\n  }\n  ngOnDestroy() {\n    this.stopCallTimer();\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n  }\n  // Démarrer le minuteur d'appel\n  startCallTimer() {\n    this.callStartTime = new Date();\n    this.stopCallTimer(); // Arrêter tout minuteur existant\n    this.durationInterval = setInterval(() => {\n      if (this.callStartTime) {\n        const now = new Date();\n        const duration = Math.floor((now.getTime() - this.callStartTime.getTime()) / 1000);\n        this.callDuration = this.formatDuration(duration);\n      }\n    }, 1000);\n  }\n  // Arrêter le minuteur d'appel\n  stopCallTimer() {\n    if (this.durationInterval) {\n      clearInterval(this.durationInterval);\n      this.durationInterval = null;\n    }\n    this.callStartTime = null;\n    this.callDuration = '00:00';\n  }\n  // Formater la durée en MM:SS\n  formatDuration(seconds) {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;\n  }\n  // Basculer le micro\n  toggleMicrophone() {\n    if (!this.activeCall) return;\n    this.isAudioMuted = !this.isAudioMuted;\n    console.log('🎤 [ActiveCall] Audio muted:', this.isAudioMuted);\n  }\n  // Basculer la caméra\n  toggleCamera() {\n    if (!this.activeCall || this.activeCall.type === CallType.AUDIO) return;\n    this.isVideoMuted = !this.isVideoMuted;\n    console.log('📹 [ActiveCall] Video muted:', this.isVideoMuted);\n  }\n  // Basculer le haut-parleur\n  toggleSpeaker() {\n    this.isSpeakerOn = !this.isSpeakerOn;\n    console.log('🔊 [ActiveCall] Speaker on:', this.isSpeakerOn);\n  }\n  // Test audio WebRTC (temporaire pour debug)\n  testAudio() {\n    console.log('🧪 [ActiveCall] Testing WebRTC audio...');\n    // Vérifier les éléments vidéo\n    const videos = document.querySelectorAll('video');\n    console.log('🔍 [ActiveCall] Found video elements:', videos.length);\n    videos.forEach((video, index) => {\n      console.log(`📺 [ActiveCall] Video ${index}:`, {\n        srcObject: !!video.srcObject,\n        muted: video.muted,\n        volume: video.volume,\n        paused: video.paused,\n        tracks: video.srcObject ? video.srcObject.getTracks().map(t => ({\n          kind: t.kind,\n          enabled: t.enabled,\n          readyState: t.readyState\n        })) : []\n      });\n      // Forcer la lecture si pas en cours\n      if (video.srcObject && video.paused) {\n        console.log(`▶️ [ActiveCall] Forcing play for video ${index}`);\n        video.play().catch(error => {\n          console.warn(`⚠️ [ActiveCall] Failed to play video ${index}:`, error);\n        });\n      }\n    });\n    // Test du CallService\n    if (this.callService) {\n      console.log('🔧 [ActiveCall] CallService state:', {\n        hasLocalStream: !!this.callService.localStream,\n        hasRemoteStream: !!this.callService.remoteStream,\n        hasLocalVideoElement: !!this.callService.localVideoElement,\n        hasRemoteVideoElement: !!this.callService.remoteVideoElement\n      });\n    }\n  }\n  // Terminer l'appel\n  endCall() {\n    if (!this.activeCall) return;\n    console.log('📞 [ActiveCall] Ending call:', this.activeCall.id);\n    this.callService.endCall(this.activeCall.id).subscribe({\n      next: () => {\n        console.log('✅ [ActiveCall] Call ended successfully');\n      },\n      error: error => {\n        console.error('❌ [ActiveCall] Error ending call:', error);\n      }\n    });\n  }\n  // Méthodes utilitaires pour le template\n  isVideoCall() {\n    return this.activeCall?.type === CallType.VIDEO;\n  }\n  getCallStatusText() {\n    if (!this.activeCall) return '';\n    switch (this.activeCall.status) {\n      case CallStatus.RINGING:\n        return 'Sonnerie...';\n      case CallStatus.CONNECTED:\n        return 'Connecté';\n      case CallStatus.ENDED:\n        return 'Terminé';\n      default:\n        return 'En cours...';\n    }\n  }\n  getOtherParticipantName() {\n    if (!this.activeCall) return '';\n    // Logique pour obtenir le nom de l'autre participant\n    // Si on est l'appelant, on affiche le destinataire, sinon l'appelant\n    return this.activeCall.recipient?.username || this.activeCall.caller?.username || 'Utilisateur';\n  }\n  getOtherParticipantAvatar() {\n    if (!this.activeCall) return '/assets/images/default-avatar.png';\n    // Logique pour obtenir l'avatar de l'autre participant\n    return this.activeCall.recipient?.image || this.activeCall.caller?.image || '/assets/images/default-avatar.png';\n  }\n  static {\n    this.ɵfac = function ActiveCallComponent_Factory(t) {\n      return new (t || ActiveCallComponent)(i0.ɵɵdirectiveInject(i1.CallService), i0.ɵɵdirectiveInject(i2.LoggerService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ActiveCallComponent,\n      selectors: [[\"app-active-call\"]],\n      decls: 1,\n      vars: 1,\n      consts: [[\"class\", \"fixed inset-0 z-50 bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 flex flex-col\", 4, \"ngIf\"], [1, \"fixed\", \"inset-0\", \"z-50\", \"bg-gradient-to-br\", \"from-gray-900\", \"via-blue-900\", \"to-purple-900\", \"flex\", \"flex-col\"], [1, \"flex-shrink-0\", \"p-6\", \"text-center\"], [1, \"inline-flex\", \"items-center\", \"space-x-3\", \"px-6\", \"py-3\", \"rounded-full\", \"bg-black/30\", \"backdrop-blur-sm\", \"border\", \"border-blue-500/20\"], [1, \"w-3\", \"h-3\", \"rounded-full\", \"bg-green-400\", \"animate-pulse\"], [1, \"text-white\", \"text-lg\", \"font-medium\"], [1, \"text-blue-300\", \"text-lg\", \"font-mono\"], [1, \"flex-1\", \"flex\", \"items-center\", \"justify-center\", \"px-6\"], [\"class\", \"w-full max-w-4xl mx-auto relative\", 4, \"ngIf\"], [\"class\", \"text-center\", 4, \"ngIf\"], [1, \"flex-shrink-0\", \"p-8\"], [1, \"flex\", \"justify-center\", \"items-center\", \"space-x-8\"], [1, \"w-16\", \"h-16\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\", \"transition-all\", \"duration-300\", \"transform\", \"hover:scale-110\", \"shadow-lg\", 3, \"title\", \"click\"], [1, \"fas\", \"text-white\", \"text-xl\"], [\"class\", \"w-16 h-16 rounded-full flex items-center justify-center transition-all duration-300 transform hover:scale-110 shadow-lg\", 3, \"class\", \"title\", \"click\", 4, \"ngIf\"], [\"title\", \"Test Audio WebRTC\", 1, \"w-16\", \"h-16\", \"rounded-full\", \"bg-yellow-500\", \"hover:bg-yellow-600\", \"flex\", \"items-center\", \"justify-center\", \"transition-all\", \"duration-300\", \"transform\", \"hover:scale-110\", \"shadow-lg\", \"shadow-yellow-500/50\", 3, \"click\"], [1, \"fas\", \"fa-headphones\", \"text-white\", \"text-xl\"], [\"title\", \"Raccrocher\", 1, \"w-16\", \"h-16\", \"rounded-full\", \"bg-red-500\", \"hover:bg-red-600\", \"flex\", \"items-center\", \"justify-center\", \"transition-all\", \"duration-300\", \"transform\", \"hover:scale-110\", \"shadow-lg\", \"shadow-red-500/50\", 3, \"click\"], [1, \"fas\", \"fa-phone-slash\", \"text-white\", \"text-xl\"], [1, \"w-full\", \"max-w-4xl\", \"mx-auto\", \"relative\"], [1, \"relative\", \"w-full\", \"aspect-video\", \"rounded-3xl\", \"overflow-hidden\", \"bg-gray-900\", \"border-2\", \"border-blue-500/30\", \"shadow-2xl\"], [1, \"w-full\", \"h-full\", \"bg-gradient-to-br\", \"from-gray-800\", \"to-gray-900\", \"flex\", \"items-center\", \"justify-center\"], [1, \"text-center\"], [1, \"w-32\", \"h-32\", \"rounded-full\", \"bg-blue-500/20\", \"flex\", \"items-center\", \"justify-center\", \"mx-auto\", \"mb-6\", \"border-4\", \"border-blue-400/50\"], [1, \"fas\", \"fa-video\", \"text-5xl\", \"text-blue-400\"], [1, \"text-white\", \"text-xl\", \"font-medium\"], [1, \"text-blue-300\", \"text-sm\", \"mt-2\"], [\"class\", \"absolute inset-0 bg-black/70 flex items-center justify-center\", 4, \"ngIf\"], [1, \"absolute\", \"top-4\", \"right-4\", \"w-48\", \"h-36\", \"rounded-2xl\", \"overflow-hidden\", \"bg-gray-800\", \"border-2\", \"border-blue-400/50\", \"shadow-lg\"], [1, \"w-full\", \"h-full\", \"bg-gradient-to-br\", \"from-gray-700\", \"to-gray-800\", \"flex\", \"items-center\", \"justify-center\"], [1, \"w-20\", \"h-20\", \"rounded-full\", \"object-cover\", \"mx-auto\", \"mb-2\", \"border-2\", \"border-blue-400\", 3, \"src\", \"alt\"], [1, \"text-white\", \"text-sm\"], [1, \"absolute\", \"inset-0\", \"bg-black/70\", \"flex\", \"items-center\", \"justify-center\"], [1, \"fas\", \"fa-video-slash\", \"text-6xl\", \"text-red-400\", \"mb-4\"], [1, \"text-white\", \"text-lg\"], [1, \"relative\", \"mb-8\"], [1, \"absolute\", \"inset-0\", \"rounded-full\", \"border-4\", \"border-blue-400/30\", \"animate-ping\"], [1, \"absolute\", \"-inset-4\", \"rounded-full\", \"border-2\", \"border-blue-400/20\", \"animate-ping\", 2, \"animation-delay\", \"0.5s\"], [1, \"absolute\", \"-inset-8\", \"rounded-full\", \"border\", \"border-blue-400/10\", \"animate-ping\", 2, \"animation-delay\", \"1s\"], [1, \"relative\", \"w-48\", \"h-48\", \"rounded-full\", \"overflow-hidden\", \"border-4\", \"border-blue-500/50\", \"mx-auto\", \"shadow-2xl\"], [1, \"w-full\", \"h-full\", \"object-cover\", 3, \"src\", \"alt\"], [\"class\", \"absolute inset-0 bg-red-500/20 flex items-center justify-center\", 4, \"ngIf\"], [1, \"text-4xl\", \"font-bold\", \"text-white\", \"mb-4\", \"drop-shadow-lg\"], [1, \"text-blue-300\", \"text-xl\", \"mb-6\"], [1, \"flex\", \"items-center\", \"justify-center\", \"space-x-1\", \"h-16\", \"mb-8\"], [\"class\", \"w-1 bg-gradient-to-t from-blue-600 to-blue-400 rounded-full transition-all duration-300\", 3, \"height\", \"animation-delay\", \"animate-pulse\", 4, \"ngFor\", \"ngForOf\"], [1, \"absolute\", \"inset-0\", \"bg-red-500/20\", \"flex\", \"items-center\", \"justify-center\"], [1, \"fas\", \"fa-microphone-slash\", \"text-4xl\", \"text-red-400\"], [1, \"w-1\", \"bg-gradient-to-t\", \"from-blue-600\", \"to-blue-400\", \"rounded-full\", \"transition-all\", \"duration-300\"]],\n      template: function ActiveCallComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, ActiveCallComponent_div_0_Template, 22, 15, \"div\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.activeCall);\n        }\n      },\n      dependencies: [i3.NgForOf, i3.NgIf],\n      styles: [\"\\n\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_pulse-ring {\\n  0% {\\n    transform: scale(1);\\n    opacity: 1;\\n  }\\n  100% {\\n    transform: scale(1.5);\\n    opacity: 0;\\n  }\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_audio-wave {\\n  0%, 100% {\\n    height: 15px;\\n  }\\n  50% {\\n    height: 40px;\\n  }\\n}\\n\\n\\n\\n.glow-effect[_ngcontent-%COMP%] {\\n  box-shadow: 0 0 20px rgba(59, 130, 246, 0.5);\\n}\\n\\n.glow-effect[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 0 30px rgba(59, 130, 246, 0.7);\\n}\\n\\n\\n\\n.audio-bar[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_audio-wave 1.5s ease-in-out infinite;\\n}\\n\\n.audio-bar[_ngcontent-%COMP%]:nth-child(2) { animation-delay: 0.1s; }\\n.audio-bar[_ngcontent-%COMP%]:nth-child(3) { animation-delay: 0.2s; }\\n.audio-bar[_ngcontent-%COMP%]:nth-child(4) { animation-delay: 0.3s; }\\n.audio-bar[_ngcontent-%COMP%]:nth-child(5) { animation-delay: 0.4s; }\\n.audio-bar[_ngcontent-%COMP%]:nth-child(6) { animation-delay: 0.5s; }\\n.audio-bar[_ngcontent-%COMP%]:nth-child(7) { animation-delay: 0.6s; }\\n.audio-bar[_ngcontent-%COMP%]:nth-child(8) { animation-delay: 0.7s; }\\n.audio-bar[_ngcontent-%COMP%]:nth-child(9) { animation-delay: 0.8s; }\\n.audio-bar[_ngcontent-%COMP%]:nth-child(10) { animation-delay: 0.9s; }\\n.audio-bar[_ngcontent-%COMP%]:nth-child(11) { animation-delay: 1.0s; }\\n.audio-bar[_ngcontent-%COMP%]:nth-child(12) { animation-delay: 1.1s; }\\n.audio-bar[_ngcontent-%COMP%]:nth-child(13) { animation-delay: 1.2s; }\\n.audio-bar[_ngcontent-%COMP%]:nth-child(14) { animation-delay: 1.3s; }\\n.audio-bar[_ngcontent-%COMP%]:nth-child(15) { animation-delay: 1.4s; }\\n\\n\\n\\n.backdrop-blur-custom[_ngcontent-%COMP%] {\\n  backdrop-filter: blur(10px);\\n  -webkit-backdrop-filter: blur(10px);\\n}\\n\\n\\n\\nbutton[_ngcontent-%COMP%] {\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n\\nbutton[_ngcontent-%COMP%]:active {\\n  transform: scale(0.95);\\n}\\n\\n\\n\\nbutton[_ngcontent-%COMP%]:hover {\\n  filter: brightness(1.1);\\n}\\n\\n\\n\\n.fas[_ngcontent-%COMP%] {\\n  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_spin {\\n  from {\\n    transform: rotate(0deg);\\n  }\\n  to {\\n    transform: rotate(360deg);\\n  }\\n}\\n\\n.animate-spin[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_custom-pulse {\\n  0%, 100% {\\n    opacity: 1;\\n    transform: scale(1);\\n  }\\n  50% {\\n    opacity: 0.7;\\n    transform: scale(1.05);\\n  }\\n}\\n\\n.custom-pulse[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_custom-pulse 2s ease-in-out infinite;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .w-48[_ngcontent-%COMP%] {\\n    width: 8rem;\\n  }\\n  \\n  .h-36[_ngcontent-%COMP%] {\\n    height: 6rem;\\n  }\\n  \\n  .text-4xl[_ngcontent-%COMP%] {\\n    font-size: 2rem;\\n  }\\n  \\n  .w-16[_ngcontent-%COMP%] {\\n    width: 3rem;\\n  }\\n  \\n  .h-16[_ngcontent-%COMP%] {\\n    height: 3rem;\\n  }\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_gradient-shift {\\n  0% {\\n    background-position: 0% 50%;\\n  }\\n  50% {\\n    background-position: 100% 50%;\\n  }\\n  100% {\\n    background-position: 0% 50%;\\n  }\\n}\\n\\n.animated-gradient[_ngcontent-%COMP%] {\\n  background: linear-gradient(-45deg, #1f2937, #1e3a8a, #7c3aed, #1f2937);\\n  background-size: 400% 400%;\\n  animation: _ngcontent-%COMP%_gradient-shift 15s ease infinite;\\n}\\n\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CallType", "CallStatus", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵtemplate", "ActiveCallComponent_div_0_div_9_div_10_Template", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r1", "getOtherParticipantName", "ɵɵproperty", "isVideoMuted", "getOtherParticipantAvatar", "ɵɵsanitizeUrl", "ɵɵstyleProp", "i_r7", "ɵɵclassProp", "ctx_r6", "isAudioMuted", "ActiveCallComponent_div_0_div_10_div_7_Template", "ActiveCallComponent_div_0_div_10_div_13_Template", "ctx_r2", "ɵɵpureFunction0", "_c0", "ɵɵlistener", "ActiveCallComponent_div_0_button_15_Template_button_click_0_listener", "ɵɵrestoreView", "_r9", "ctx_r8", "ɵɵnextContext", "ɵɵresetView", "toggleCamera", "ɵɵclassMap", "ctx_r3", "ActiveCallComponent_div_0_div_9_Template", "ActiveCallComponent_div_0_div_10_Template", "ActiveCallComponent_div_0_Template_button_click_13_listener", "_r11", "ctx_r10", "toggleMicrophone", "ActiveCallComponent_div_0_button_15_Template", "ActiveCallComponent_div_0_Template_button_click_16_listener", "ctx_r12", "toggleSpeaker", "ActiveCallComponent_div_0_Template_button_click_18_listener", "ctx_r13", "testAudio", "ActiveCallComponent_div_0_Template_button_click_20_listener", "ctx_r14", "endCall", "ɵɵtextInterpolate", "ctx_r0", "getCallStatusText", "callDuration", "isVideoCall", "isSpeakerOn", "ActiveCallComponent", "constructor", "callService", "logger", "activeCall", "callStartTime", "subscriptions", "ngOnInit", "activeCallSub", "activeCall$", "subscribe", "call", "previousCall", "console", "log", "status", "CONNECTED", "id", "startCallTimer", "stopCallTimer", "push", "ngOnDestroy", "for<PERSON>ach", "sub", "unsubscribe", "Date", "durationInterval", "setInterval", "now", "duration", "Math", "floor", "getTime", "formatDuration", "clearInterval", "seconds", "minutes", "remainingSeconds", "toString", "padStart", "type", "AUDIO", "videos", "document", "querySelectorAll", "length", "video", "index", "srcObject", "muted", "volume", "paused", "tracks", "getTracks", "map", "t", "kind", "enabled", "readyState", "play", "catch", "error", "warn", "hasLocalStream", "localStream", "hasRemoteStream", "remoteStream", "hasLocalVideoElement", "localVideoElement", "hasRemoteVideoElement", "remoteVideoElement", "next", "VIDEO", "RINGING", "ENDED", "recipient", "username", "caller", "image", "ɵɵdirectiveInject", "i1", "CallService", "i2", "LoggerService", "selectors", "decls", "vars", "consts", "template", "ActiveCallComponent_Template", "rf", "ctx", "ActiveCallComponent_div_0_Template"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\components\\active-call\\active-call.component.ts", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\components\\active-call\\active-call.component.html"], "sourcesContent": ["import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';\nimport { Subscription } from 'rxjs';\nimport { CallService } from '../../services/call.service';\nimport { LoggerService } from '../../services/logger.service';\nimport { Call, CallType, CallStatus } from '../../models/message.model';\n\n@Component({\n  selector: 'app-active-call',\n  templateUrl: './active-call.component.html',\n  styleUrls: ['./active-call.component.css'],\n})\nexport class ActiveCallComponent implements OnInit, OnDestroy {\n  activeCall: Call | null = null;\n  callDuration: string = '00:00';\n  isAudioMuted: boolean = false;\n  isVideoMuted: boolean = false;\n  isSpeakerOn: boolean = true;\n\n  private durationInterval: any;\n  private callStartTime: Date | null = null;\n  private subscriptions: Subscription[] = [];\n\n  // Exposer les énums au template\n  CallType = CallType;\n  CallStatus = CallStatus;\n\n  constructor(\n    private callService: CallService,\n    private logger: LoggerService\n  ) {}\n\n  ngOnInit(): void {\n    // S'abonner à l'appel actif\n    const activeCallSub = this.callService.activeCall$.subscribe((call) => {\n      const previousCall = this.activeCall;\n      this.activeCall = call;\n\n      console.log('📞 [ActiveCall] Active call updated:', call);\n\n      if (call && call.status === CallStatus.CONNECTED) {\n        if (!previousCall || previousCall.id !== call.id) {\n          console.log('✅ [ActiveCall] Call connected, starting timer');\n          this.startCallTimer();\n        }\n      } else if (!call || call.status !== CallStatus.CONNECTED) {\n        this.stopCallTimer();\n      }\n    });\n\n    this.subscriptions.push(activeCallSub);\n  }\n\n  ngOnDestroy(): void {\n    this.stopCallTimer();\n    this.subscriptions.forEach((sub) => sub.unsubscribe());\n  }\n\n  // Démarrer le minuteur d'appel\n  private startCallTimer(): void {\n    this.callStartTime = new Date();\n    this.stopCallTimer(); // Arrêter tout minuteur existant\n\n    this.durationInterval = setInterval(() => {\n      if (this.callStartTime) {\n        const now = new Date();\n        const duration = Math.floor(\n          (now.getTime() - this.callStartTime.getTime()) / 1000\n        );\n        this.callDuration = this.formatDuration(duration);\n      }\n    }, 1000);\n  }\n\n  // Arrêter le minuteur d'appel\n  private stopCallTimer(): void {\n    if (this.durationInterval) {\n      clearInterval(this.durationInterval);\n      this.durationInterval = null;\n    }\n    this.callStartTime = null;\n    this.callDuration = '00:00';\n  }\n\n  // Formater la durée en MM:SS\n  private formatDuration(seconds: number): string {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds\n      .toString()\n      .padStart(2, '0')}`;\n  }\n\n  // Basculer le micro\n  toggleMicrophone(): void {\n    if (!this.activeCall) return;\n\n    this.isAudioMuted = !this.isAudioMuted;\n    console.log('🎤 [ActiveCall] Audio muted:', this.isAudioMuted);\n  }\n\n  // Basculer la caméra\n  toggleCamera(): void {\n    if (!this.activeCall || this.activeCall.type === CallType.AUDIO) return;\n\n    this.isVideoMuted = !this.isVideoMuted;\n    console.log('📹 [ActiveCall] Video muted:', this.isVideoMuted);\n  }\n\n  // Basculer le haut-parleur\n  toggleSpeaker(): void {\n    this.isSpeakerOn = !this.isSpeakerOn;\n    console.log('🔊 [ActiveCall] Speaker on:', this.isSpeakerOn);\n  }\n\n  // Test audio WebRTC (temporaire pour debug)\n  testAudio(): void {\n    console.log('🧪 [ActiveCall] Testing WebRTC audio...');\n\n    // Vérifier les éléments vidéo\n    const videos = document.querySelectorAll('video');\n    console.log('🔍 [ActiveCall] Found video elements:', videos.length);\n\n    videos.forEach((video, index) => {\n      console.log(`📺 [ActiveCall] Video ${index}:`, {\n        srcObject: !!video.srcObject,\n        muted: video.muted,\n        volume: video.volume,\n        paused: video.paused,\n        tracks: video.srcObject\n          ? video.srcObject.getTracks().map((t) => ({\n              kind: t.kind,\n              enabled: t.enabled,\n              readyState: t.readyState,\n            }))\n          : [],\n      });\n\n      // Forcer la lecture si pas en cours\n      if (video.srcObject && video.paused) {\n        console.log(`▶️ [ActiveCall] Forcing play for video ${index}`);\n        video.play().catch((error) => {\n          console.warn(`⚠️ [ActiveCall] Failed to play video ${index}:`, error);\n        });\n      }\n    });\n\n    // Test du CallService\n    if (this.callService) {\n      console.log('🔧 [ActiveCall] CallService state:', {\n        hasLocalStream: !!(this.callService as any).localStream,\n        hasRemoteStream: !!(this.callService as any).remoteStream,\n        hasLocalVideoElement: !!(this.callService as any).localVideoElement,\n        hasRemoteVideoElement: !!(this.callService as any).remoteVideoElement,\n      });\n    }\n  }\n\n  // Terminer l'appel\n  endCall(): void {\n    if (!this.activeCall) return;\n\n    console.log('📞 [ActiveCall] Ending call:', this.activeCall.id);\n\n    this.callService.endCall(this.activeCall.id).subscribe({\n      next: () => {\n        console.log('✅ [ActiveCall] Call ended successfully');\n      },\n      error: (error) => {\n        console.error('❌ [ActiveCall] Error ending call:', error);\n      },\n    });\n  }\n\n  // Méthodes utilitaires pour le template\n  isVideoCall(): boolean {\n    return this.activeCall?.type === CallType.VIDEO;\n  }\n\n  getCallStatusText(): string {\n    if (!this.activeCall) return '';\n\n    switch (this.activeCall.status) {\n      case CallStatus.RINGING:\n        return 'Sonnerie...';\n      case CallStatus.CONNECTED:\n        return 'Connecté';\n      case CallStatus.ENDED:\n        return 'Terminé';\n      default:\n        return 'En cours...';\n    }\n  }\n\n  getOtherParticipantName(): string {\n    if (!this.activeCall) return '';\n\n    // Logique pour obtenir le nom de l'autre participant\n    // Si on est l'appelant, on affiche le destinataire, sinon l'appelant\n    return (\n      this.activeCall.recipient?.username ||\n      this.activeCall.caller?.username ||\n      'Utilisateur'\n    );\n  }\n\n  getOtherParticipantAvatar(): string {\n    if (!this.activeCall) return '/assets/images/default-avatar.png';\n\n    // Logique pour obtenir l'avatar de l'autre participant\n    return (\n      this.activeCall.recipient?.image ||\n      this.activeCall.caller?.image ||\n      '/assets/images/default-avatar.png'\n    );\n  }\n}\n", "<!-- Interface d'appel actif moderne -->\n<div\n  *ngIf=\"activeCall\"\n  class=\"fixed inset-0 z-50 bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 flex flex-col\"\n>\n  <!-- Header avec informations d'appel -->\n  <div class=\"flex-shrink-0 p-6 text-center\">\n    <div\n      class=\"inline-flex items-center space-x-3 px-6 py-3 rounded-full bg-black/30 backdrop-blur-sm border border-blue-500/20\"\n    >\n      <div class=\"w-3 h-3 rounded-full bg-green-400 animate-pulse\"></div>\n      <span class=\"text-white text-lg font-medium\">{{\n        getCallStatusText()\n      }}</span>\n      <span class=\"text-blue-300 text-lg font-mono\">{{ callDuration }}</span>\n    </div>\n  </div>\n\n  <!-- Zone principale d'appel -->\n  <div class=\"flex-1 flex items-center justify-center px-6\">\n    <!-- Appel vidéo -->\n    <div *ngIf=\"isVideoCall()\" class=\"w-full max-w-4xl mx-auto relative\">\n      <!-- Zone vidéo principale -->\n      <div\n        class=\"relative w-full aspect-video rounded-3xl overflow-hidden bg-gray-900 border-2 border-blue-500/30 shadow-2xl\"\n      >\n        <!-- Placeholder pour vidéo locale -->\n        <div\n          class=\"w-full h-full bg-gradient-to-br from-gray-800 to-gray-900 flex items-center justify-center\"\n        >\n          <div class=\"text-center\">\n            <div\n              class=\"w-32 h-32 rounded-full bg-blue-500/20 flex items-center justify-center mx-auto mb-6 border-4 border-blue-400/50\"\n            >\n              <i class=\"fas fa-video text-5xl text-blue-400\"></i>\n            </div>\n            <p class=\"text-white text-xl font-medium\">Vidéo en cours...</p>\n            <p class=\"text-blue-300 text-sm mt-2\">\n              {{ getOtherParticipantName() }}\n            </p>\n          </div>\n        </div>\n\n        <!-- Overlay pour vidéo coupée -->\n        <div\n          *ngIf=\"isVideoMuted\"\n          class=\"absolute inset-0 bg-black/70 flex items-center justify-center\"\n        >\n          <div class=\"text-center\">\n            <i class=\"fas fa-video-slash text-6xl text-red-400 mb-4\"></i>\n            <p class=\"text-white text-lg\">Caméra désactivée</p>\n          </div>\n        </div>\n      </div>\n\n      <!-- Vidéo locale (petite, en overlay) -->\n      <div\n        class=\"absolute top-4 right-4 w-48 h-36 rounded-2xl overflow-hidden bg-gray-800 border-2 border-blue-400/50 shadow-lg\"\n      >\n        <div\n          class=\"w-full h-full bg-gradient-to-br from-gray-700 to-gray-800 flex items-center justify-center\"\n        >\n          <div class=\"text-center\">\n            <img\n              [src]=\"getOtherParticipantAvatar()\"\n              [alt]=\"getOtherParticipantName()\"\n              class=\"w-20 h-20 rounded-full object-cover mx-auto mb-2 border-2 border-blue-400\"\n            />\n            <p class=\"text-white text-sm\">Vous</p>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Appel audio -->\n    <div *ngIf=\"!isVideoCall()\" class=\"text-center\">\n      <!-- Avatar principal avec effets -->\n      <div class=\"relative mb-8\">\n        <!-- Cercles d'animation -->\n        <div\n          class=\"absolute inset-0 rounded-full border-4 border-blue-400/30 animate-ping\"\n        ></div>\n        <div\n          class=\"absolute -inset-4 rounded-full border-2 border-blue-400/20 animate-ping\"\n          style=\"animation-delay: 0.5s\"\n        ></div>\n        <div\n          class=\"absolute -inset-8 rounded-full border border-blue-400/10 animate-ping\"\n          style=\"animation-delay: 1s\"\n        ></div>\n\n        <!-- Avatar -->\n        <div\n          class=\"relative w-48 h-48 rounded-full overflow-hidden border-4 border-blue-500/50 mx-auto shadow-2xl\"\n        >\n          <img\n            [src]=\"getOtherParticipantAvatar()\"\n            [alt]=\"getOtherParticipantName()\"\n            class=\"w-full h-full object-cover\"\n          />\n          <!-- Overlay pour micro coupé -->\n          <div\n            *ngIf=\"isAudioMuted\"\n            class=\"absolute inset-0 bg-red-500/20 flex items-center justify-center\"\n          >\n            <i class=\"fas fa-microphone-slash text-4xl text-red-400\"></i>\n          </div>\n        </div>\n      </div>\n\n      <!-- Informations utilisateur -->\n      <h2 class=\"text-4xl font-bold text-white mb-4 drop-shadow-lg\">\n        {{ getOtherParticipantName() }}\n      </h2>\n      <p class=\"text-blue-300 text-xl mb-6\">Appel audio en cours</p>\n\n      <!-- Visualiseur audio -->\n      <div class=\"flex items-center justify-center space-x-1 h-16 mb-8\">\n        <div\n          *ngFor=\"let i of [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]\"\n          class=\"w-1 bg-gradient-to-t from-blue-600 to-blue-400 rounded-full transition-all duration-300\"\n          [style.height.px]=\"15 + (i % 5) * 8\"\n          [style.animation-delay.ms]=\"i * 100\"\n          [class.animate-pulse]=\"!isAudioMuted\"\n        ></div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Contrôles d'appel -->\n  <div class=\"flex-shrink-0 p-8\">\n    <div class=\"flex justify-center items-center space-x-8\">\n      <!-- Bouton Microphone -->\n      <button\n        (click)=\"toggleMicrophone()\"\n        class=\"w-16 h-16 rounded-full flex items-center justify-center transition-all duration-300 transform hover:scale-110 shadow-lg\"\n        [class]=\"\n          isAudioMuted\n            ? 'bg-red-500 hover:bg-red-600 shadow-red-500/50'\n            : 'bg-blue-500 hover:bg-blue-600 shadow-blue-500/50'\n        \"\n        [title]=\"isAudioMuted ? 'Activer le micro' : 'Couper le micro'\"\n      >\n        <i\n          class=\"fas text-white text-xl\"\n          [class]=\"isAudioMuted ? 'fa-microphone-slash' : 'fa-microphone'\"\n        ></i>\n      </button>\n\n      <!-- Bouton Caméra (seulement pour appels vidéo) -->\n      <button\n        *ngIf=\"isVideoCall()\"\n        (click)=\"toggleCamera()\"\n        class=\"w-16 h-16 rounded-full flex items-center justify-center transition-all duration-300 transform hover:scale-110 shadow-lg\"\n        [class]=\"\n          isVideoMuted\n            ? 'bg-red-500 hover:bg-red-600 shadow-red-500/50'\n            : 'bg-blue-500 hover:bg-blue-600 shadow-blue-500/50'\n        \"\n        [title]=\"isVideoMuted ? 'Activer la caméra' : 'Désactiver la caméra'\"\n      >\n        <i\n          class=\"fas text-white text-xl\"\n          [class]=\"isVideoMuted ? 'fa-video-slash' : 'fa-video'\"\n        ></i>\n      </button>\n\n      <!-- Bouton Haut-parleur -->\n      <button\n        (click)=\"toggleSpeaker()\"\n        class=\"w-16 h-16 rounded-full flex items-center justify-center transition-all duration-300 transform hover:scale-110 shadow-lg\"\n        [class]=\"\n          isSpeakerOn\n            ? 'bg-blue-500 hover:bg-blue-600 shadow-blue-500/50'\n            : 'bg-gray-600 hover:bg-gray-700 shadow-gray-600/50'\n        \"\n        [title]=\"\n          isSpeakerOn ? 'Désactiver le haut-parleur' : 'Activer le haut-parleur'\n        \"\n      >\n        <i\n          class=\"fas text-white text-xl\"\n          [class]=\"isSpeakerOn ? 'fa-volume-up' : 'fa-volume-mute'\"\n        ></i>\n      </button>\n\n      <!-- Bouton Test Audio (temporaire pour debug) -->\n      <button\n        (click)=\"testAudio()\"\n        class=\"w-16 h-16 rounded-full bg-yellow-500 hover:bg-yellow-600 flex items-center justify-center transition-all duration-300 transform hover:scale-110 shadow-lg shadow-yellow-500/50\"\n        title=\"Test Audio WebRTC\"\n      >\n        <i class=\"fas fa-headphones text-white text-xl\"></i>\n      </button>\n\n      <!-- Bouton Raccrocher -->\n      <button\n        (click)=\"endCall()\"\n        class=\"w-16 h-16 rounded-full bg-red-500 hover:bg-red-600 flex items-center justify-center transition-all duration-300 transform hover:scale-110 shadow-lg shadow-red-500/50\"\n        title=\"Raccrocher\"\n      >\n        <i class=\"fas fa-phone-slash text-white text-xl\"></i>\n      </button>\n    </div>\n  </div>\n</div>\n"], "mappings": "AAIA,SAAeA,QAAQ,EAAEC,UAAU,QAAQ,4BAA4B;;;;;;;ICwC/DC,EAAA,CAAAC,cAAA,cAGC;IAEGD,EAAA,CAAAE,SAAA,YAA6D;IAC7DF,EAAA,CAAAC,cAAA,YAA8B;IAAAD,EAAA,CAAAG,MAAA,uCAAiB;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;IA7B3DJ,EAAA,CAAAC,cAAA,cAAqE;IAa3DD,EAAA,CAAAE,SAAA,YAAmD;IACrDF,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,YAA0C;IAAAD,EAAA,CAAAG,MAAA,6BAAiB;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAC/DJ,EAAA,CAAAC,cAAA,YAAsC;IACpCD,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAKRJ,EAAA,CAAAK,UAAA,KAAAC,+CAAA,kBAQM;IACRN,EAAA,CAAAI,YAAA,EAAM;IAGNJ,EAAA,CAAAC,cAAA,eAEC;IAKKD,EAAA,CAAAE,SAAA,eAIE;IACFF,EAAA,CAAAC,cAAA,aAA8B;IAAAD,EAAA,CAAAG,MAAA,YAAI;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;IA9BpCJ,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAC,MAAA,CAAAC,uBAAA,QACF;IAMDV,EAAA,CAAAO,SAAA,GAAkB;IAAlBP,EAAA,CAAAW,UAAA,SAAAF,MAAA,CAAAG,YAAA,CAAkB;IAmBfZ,EAAA,CAAAO,SAAA,GAAmC;IAAnCP,EAAA,CAAAW,UAAA,QAAAF,MAAA,CAAAI,yBAAA,IAAAb,EAAA,CAAAc,aAAA,CAAmC,QAAAL,MAAA,CAAAC,uBAAA;;;;;IAqCvCV,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAE,SAAA,YAA6D;IAC/DF,EAAA,CAAAI,YAAA,EAAM;;;;;IAYRJ,EAAA,CAAAE,SAAA,cAMO;;;;;IAHLF,EAAA,CAAAe,WAAA,gBAAAC,IAAA,eAAoC,oBAAAA,IAAA;IAEpChB,EAAA,CAAAiB,WAAA,mBAAAC,MAAA,CAAAC,YAAA,CAAqC;;;;;;;;IAhD3CnB,EAAA,CAAAC,cAAA,cAAgD;IAI5CD,EAAA,CAAAE,SAAA,cAEO;IAWPF,EAAA,CAAAC,cAAA,cAEC;IACCD,EAAA,CAAAE,SAAA,cAIE;IAEFF,EAAA,CAAAK,UAAA,IAAAe,+CAAA,kBAKM;IACRpB,EAAA,CAAAI,YAAA,EAAM;IAIRJ,EAAA,CAAAC,cAAA,aAA8D;IAC5DD,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAG,MAAA,4BAAoB;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAG9DJ,EAAA,CAAAC,cAAA,eAAkE;IAChED,EAAA,CAAAK,UAAA,KAAAgB,gDAAA,kBAMO;IACTrB,EAAA,CAAAI,YAAA,EAAM;;;;IA7BAJ,EAAA,CAAAO,SAAA,GAAmC;IAAnCP,EAAA,CAAAW,UAAA,QAAAW,MAAA,CAAAT,yBAAA,IAAAb,EAAA,CAAAc,aAAA,CAAmC,QAAAQ,MAAA,CAAAZ,uBAAA;IAMlCV,EAAA,CAAAO,SAAA,GAAkB;IAAlBP,EAAA,CAAAW,UAAA,SAAAW,MAAA,CAAAH,YAAA,CAAkB;IAUvBnB,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAc,MAAA,CAAAZ,uBAAA,QACF;IAMkBV,EAAA,CAAAO,SAAA,GAAsD;IAAtDP,EAAA,CAAAW,UAAA,YAAAX,EAAA,CAAAuB,eAAA,IAAAC,GAAA,EAAsD;;;;;;IA+BxExB,EAAA,CAAAC,cAAA,iBAUC;IARCD,EAAA,CAAAyB,UAAA,mBAAAC,qEAAA;MAAA1B,EAAA,CAAA2B,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAA7B,EAAA,CAAA8B,aAAA;MAAA,OAAS9B,EAAA,CAAA+B,WAAA,CAAAF,MAAA,CAAAG,YAAA,EAAc;IAAA,EAAC;IASxBhC,EAAA,CAAAE,SAAA,YAGK;IACPF,EAAA,CAAAI,YAAA,EAAS;;;;IAXPJ,EAAA,CAAAiC,UAAA,CAAAC,MAAA,CAAAtB,YAAA,wGAIC;IACDZ,EAAA,CAAAW,UAAA,UAAAuB,MAAA,CAAAtB,YAAA,+DAAqE;IAInEZ,EAAA,CAAAO,SAAA,GAAsD;IAAtDP,EAAA,CAAAiC,UAAA,CAAAC,MAAA,CAAAtB,YAAA,iCAAsD;;;;;;IAlKhEZ,EAAA,CAAAC,cAAA,aAGC;IAMKD,EAAA,CAAAE,SAAA,aAAmE;IACnEF,EAAA,CAAAC,cAAA,cAA6C;IAAAD,EAAA,CAAAG,MAAA,GAE3C;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACTJ,EAAA,CAAAC,cAAA,cAA8C;IAAAD,EAAA,CAAAG,MAAA,GAAkB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAK3EJ,EAAA,CAAAC,cAAA,aAA0D;IAExDD,EAAA,CAAAK,UAAA,IAAA8B,wCAAA,kBAmDM;IAGNnC,EAAA,CAAAK,UAAA,KAAA+B,yCAAA,kBAmDM;IACRpC,EAAA,CAAAI,YAAA,EAAM;IAGNJ,EAAA,CAAAC,cAAA,eAA+B;IAIzBD,EAAA,CAAAyB,UAAA,mBAAAY,4DAAA;MAAArC,EAAA,CAAA2B,aAAA,CAAAW,IAAA;MAAA,MAAAC,OAAA,GAAAvC,EAAA,CAAA8B,aAAA;MAAA,OAAS9B,EAAA,CAAA+B,WAAA,CAAAQ,OAAA,CAAAC,gBAAA,EAAkB;IAAA,EAAC;IAS5BxC,EAAA,CAAAE,SAAA,aAGK;IACPF,EAAA,CAAAI,YAAA,EAAS;IAGTJ,EAAA,CAAAK,UAAA,KAAAoC,4CAAA,qBAeS;IAGTzC,EAAA,CAAAC,cAAA,kBAWC;IAVCD,EAAA,CAAAyB,UAAA,mBAAAiB,4DAAA;MAAA1C,EAAA,CAAA2B,aAAA,CAAAW,IAAA;MAAA,MAAAK,OAAA,GAAA3C,EAAA,CAAA8B,aAAA;MAAA,OAAS9B,EAAA,CAAA+B,WAAA,CAAAY,OAAA,CAAAC,aAAA,EAAe;IAAA,EAAC;IAWzB5C,EAAA,CAAAE,SAAA,aAGK;IACPF,EAAA,CAAAI,YAAA,EAAS;IAGTJ,EAAA,CAAAC,cAAA,kBAIC;IAHCD,EAAA,CAAAyB,UAAA,mBAAAoB,4DAAA;MAAA7C,EAAA,CAAA2B,aAAA,CAAAW,IAAA;MAAA,MAAAQ,OAAA,GAAA9C,EAAA,CAAA8B,aAAA;MAAA,OAAS9B,EAAA,CAAA+B,WAAA,CAAAe,OAAA,CAAAC,SAAA,EAAW;IAAA,EAAC;IAIrB/C,EAAA,CAAAE,SAAA,aAAoD;IACtDF,EAAA,CAAAI,YAAA,EAAS;IAGTJ,EAAA,CAAAC,cAAA,kBAIC;IAHCD,EAAA,CAAAyB,UAAA,mBAAAuB,4DAAA;MAAAhD,EAAA,CAAA2B,aAAA,CAAAW,IAAA;MAAA,MAAAW,OAAA,GAAAjD,EAAA,CAAA8B,aAAA;MAAA,OAAS9B,EAAA,CAAA+B,WAAA,CAAAkB,OAAA,CAAAC,OAAA,EAAS;IAAA,EAAC;IAInBlD,EAAA,CAAAE,SAAA,aAAqD;IACvDF,EAAA,CAAAI,YAAA,EAAS;;;;IA/LoCJ,EAAA,CAAAO,SAAA,GAE3C;IAF2CP,EAAA,CAAAmD,iBAAA,CAAAC,MAAA,CAAAC,iBAAA,GAE3C;IAC4CrD,EAAA,CAAAO,SAAA,GAAkB;IAAlBP,EAAA,CAAAmD,iBAAA,CAAAC,MAAA,CAAAE,YAAA,CAAkB;IAO5DtD,EAAA,CAAAO,SAAA,GAAmB;IAAnBP,EAAA,CAAAW,UAAA,SAAAyC,MAAA,CAAAG,WAAA,GAAmB;IAsDnBvD,EAAA,CAAAO,SAAA,GAAoB;IAApBP,EAAA,CAAAW,UAAA,UAAAyC,MAAA,CAAAG,WAAA,GAAoB;IA6DtBvD,EAAA,CAAAO,SAAA,GAIC;IAJDP,EAAA,CAAAiC,UAAA,CAAAmB,MAAA,CAAAjC,YAAA,wGAIC;IACDnB,EAAA,CAAAW,UAAA,UAAAyC,MAAA,CAAAjC,YAAA,0CAA+D;IAI7DnB,EAAA,CAAAO,SAAA,GAAgE;IAAhEP,EAAA,CAAAiC,UAAA,CAAAmB,MAAA,CAAAjC,YAAA,2CAAgE;IAMjEnB,EAAA,CAAAO,SAAA,GAAmB;IAAnBP,EAAA,CAAAW,UAAA,SAAAyC,MAAA,CAAAG,WAAA,GAAmB;IAoBpBvD,EAAA,CAAAO,SAAA,GAIC;IAJDP,EAAA,CAAAiC,UAAA,CAAAmB,MAAA,CAAAI,WAAA,2GAIC;IACDxD,EAAA,CAAAW,UAAA,UAAAyC,MAAA,CAAAI,WAAA,iEAEC;IAICxD,EAAA,CAAAO,SAAA,GAAyD;IAAzDP,EAAA,CAAAiC,UAAA,CAAAmB,MAAA,CAAAI,WAAA,qCAAyD;;;AD3KnE,OAAM,MAAOC,mBAAmB;EAe9BC,YACUC,WAAwB,EACxBC,MAAqB;IADrB,KAAAD,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IAhBhB,KAAAC,UAAU,GAAgB,IAAI;IAC9B,KAAAP,YAAY,GAAW,OAAO;IAC9B,KAAAnC,YAAY,GAAY,KAAK;IAC7B,KAAAP,YAAY,GAAY,KAAK;IAC7B,KAAA4C,WAAW,GAAY,IAAI;IAGnB,KAAAM,aAAa,GAAgB,IAAI;IACjC,KAAAC,aAAa,GAAmB,EAAE;IAE1C;IACA,KAAAjE,QAAQ,GAAGA,QAAQ;IACnB,KAAAC,UAAU,GAAGA,UAAU;EAKpB;EAEHiE,QAAQA,CAAA;IACN;IACA,MAAMC,aAAa,GAAG,IAAI,CAACN,WAAW,CAACO,WAAW,CAACC,SAAS,CAAEC,IAAI,IAAI;MACpE,MAAMC,YAAY,GAAG,IAAI,CAACR,UAAU;MACpC,IAAI,CAACA,UAAU,GAAGO,IAAI;MAEtBE,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEH,IAAI,CAAC;MAEzD,IAAIA,IAAI,IAAIA,IAAI,CAACI,MAAM,KAAKzE,UAAU,CAAC0E,SAAS,EAAE;QAChD,IAAI,CAACJ,YAAY,IAAIA,YAAY,CAACK,EAAE,KAAKN,IAAI,CAACM,EAAE,EAAE;UAChDJ,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;UAC5D,IAAI,CAACI,cAAc,EAAE;;OAExB,MAAM,IAAI,CAACP,IAAI,IAAIA,IAAI,CAACI,MAAM,KAAKzE,UAAU,CAAC0E,SAAS,EAAE;QACxD,IAAI,CAACG,aAAa,EAAE;;IAExB,CAAC,CAAC;IAEF,IAAI,CAACb,aAAa,CAACc,IAAI,CAACZ,aAAa,CAAC;EACxC;EAEAa,WAAWA,CAAA;IACT,IAAI,CAACF,aAAa,EAAE;IACpB,IAAI,CAACb,aAAa,CAACgB,OAAO,CAAEC,GAAG,IAAKA,GAAG,CAACC,WAAW,EAAE,CAAC;EACxD;EAEA;EACQN,cAAcA,CAAA;IACpB,IAAI,CAACb,aAAa,GAAG,IAAIoB,IAAI,EAAE;IAC/B,IAAI,CAACN,aAAa,EAAE,CAAC,CAAC;IAEtB,IAAI,CAACO,gBAAgB,GAAGC,WAAW,CAAC,MAAK;MACvC,IAAI,IAAI,CAACtB,aAAa,EAAE;QACtB,MAAMuB,GAAG,GAAG,IAAIH,IAAI,EAAE;QACtB,MAAMI,QAAQ,GAAGC,IAAI,CAACC,KAAK,CACzB,CAACH,GAAG,CAACI,OAAO,EAAE,GAAG,IAAI,CAAC3B,aAAa,CAAC2B,OAAO,EAAE,IAAI,IAAI,CACtD;QACD,IAAI,CAACnC,YAAY,GAAG,IAAI,CAACoC,cAAc,CAACJ,QAAQ,CAAC;;IAErD,CAAC,EAAE,IAAI,CAAC;EACV;EAEA;EACQV,aAAaA,CAAA;IACnB,IAAI,IAAI,CAACO,gBAAgB,EAAE;MACzBQ,aAAa,CAAC,IAAI,CAACR,gBAAgB,CAAC;MACpC,IAAI,CAACA,gBAAgB,GAAG,IAAI;;IAE9B,IAAI,CAACrB,aAAa,GAAG,IAAI;IACzB,IAAI,CAACR,YAAY,GAAG,OAAO;EAC7B;EAEA;EACQoC,cAAcA,CAACE,OAAe;IACpC,MAAMC,OAAO,GAAGN,IAAI,CAACC,KAAK,CAACI,OAAO,GAAG,EAAE,CAAC;IACxC,MAAME,gBAAgB,GAAGF,OAAO,GAAG,EAAE;IACrC,OAAO,GAAGC,OAAO,CAACE,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIF,gBAAgB,CAC9DC,QAAQ,EAAE,CACVC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EACvB;EAEA;EACAxD,gBAAgBA,CAAA;IACd,IAAI,CAAC,IAAI,CAACqB,UAAU,EAAE;IAEtB,IAAI,CAAC1C,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;IACtCmD,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE,IAAI,CAACpD,YAAY,CAAC;EAChE;EAEA;EACAa,YAAYA,CAAA;IACV,IAAI,CAAC,IAAI,CAAC6B,UAAU,IAAI,IAAI,CAACA,UAAU,CAACoC,IAAI,KAAKnG,QAAQ,CAACoG,KAAK,EAAE;IAEjE,IAAI,CAACtF,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;IACtC0D,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE,IAAI,CAAC3D,YAAY,CAAC;EAChE;EAEA;EACAgC,aAAaA,CAAA;IACX,IAAI,CAACY,WAAW,GAAG,CAAC,IAAI,CAACA,WAAW;IACpCc,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE,IAAI,CAACf,WAAW,CAAC;EAC9D;EAEA;EACAT,SAASA,CAAA;IACPuB,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;IAEtD;IACA,MAAM4B,MAAM,GAAGC,QAAQ,CAACC,gBAAgB,CAAC,OAAO,CAAC;IACjD/B,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE4B,MAAM,CAACG,MAAM,CAAC;IAEnEH,MAAM,CAACpB,OAAO,CAAC,CAACwB,KAAK,EAAEC,KAAK,KAAI;MAC9BlC,OAAO,CAACC,GAAG,CAAC,yBAAyBiC,KAAK,GAAG,EAAE;QAC7CC,SAAS,EAAE,CAAC,CAACF,KAAK,CAACE,SAAS;QAC5BC,KAAK,EAAEH,KAAK,CAACG,KAAK;QAClBC,MAAM,EAAEJ,KAAK,CAACI,MAAM;QACpBC,MAAM,EAAEL,KAAK,CAACK,MAAM;QACpBC,MAAM,EAAEN,KAAK,CAACE,SAAS,GACnBF,KAAK,CAACE,SAAS,CAACK,SAAS,EAAE,CAACC,GAAG,CAAEC,CAAC,KAAM;UACtCC,IAAI,EAAED,CAAC,CAACC,IAAI;UACZC,OAAO,EAAEF,CAAC,CAACE,OAAO;UAClBC,UAAU,EAAEH,CAAC,CAACG;SACf,CAAC,CAAC,GACH;OACL,CAAC;MAEF;MACA,IAAIZ,KAAK,CAACE,SAAS,IAAIF,KAAK,CAACK,MAAM,EAAE;QACnCtC,OAAO,CAACC,GAAG,CAAC,0CAA0CiC,KAAK,EAAE,CAAC;QAC9DD,KAAK,CAACa,IAAI,EAAE,CAACC,KAAK,CAAEC,KAAK,IAAI;UAC3BhD,OAAO,CAACiD,IAAI,CAAC,wCAAwCf,KAAK,GAAG,EAAEc,KAAK,CAAC;QACvE,CAAC,CAAC;;IAEN,CAAC,CAAC;IAEF;IACA,IAAI,IAAI,CAAC3D,WAAW,EAAE;MACpBW,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAE;QAChDiD,cAAc,EAAE,CAAC,CAAE,IAAI,CAAC7D,WAAmB,CAAC8D,WAAW;QACvDC,eAAe,EAAE,CAAC,CAAE,IAAI,CAAC/D,WAAmB,CAACgE,YAAY;QACzDC,oBAAoB,EAAE,CAAC,CAAE,IAAI,CAACjE,WAAmB,CAACkE,iBAAiB;QACnEC,qBAAqB,EAAE,CAAC,CAAE,IAAI,CAACnE,WAAmB,CAACoE;OACpD,CAAC;;EAEN;EAEA;EACA7E,OAAOA,CAAA;IACL,IAAI,CAAC,IAAI,CAACW,UAAU,EAAE;IAEtBS,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE,IAAI,CAACV,UAAU,CAACa,EAAE,CAAC;IAE/D,IAAI,CAACf,WAAW,CAACT,OAAO,CAAC,IAAI,CAACW,UAAU,CAACa,EAAE,CAAC,CAACP,SAAS,CAAC;MACrD6D,IAAI,EAAEA,CAAA,KAAK;QACT1D,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;MACvD,CAAC;MACD+C,KAAK,EAAGA,KAAK,IAAI;QACfhD,OAAO,CAACgD,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MAC3D;KACD,CAAC;EACJ;EAEA;EACA/D,WAAWA,CAAA;IACT,OAAO,IAAI,CAACM,UAAU,EAAEoC,IAAI,KAAKnG,QAAQ,CAACmI,KAAK;EACjD;EAEA5E,iBAAiBA,CAAA;IACf,IAAI,CAAC,IAAI,CAACQ,UAAU,EAAE,OAAO,EAAE;IAE/B,QAAQ,IAAI,CAACA,UAAU,CAACW,MAAM;MAC5B,KAAKzE,UAAU,CAACmI,OAAO;QACrB,OAAO,aAAa;MACtB,KAAKnI,UAAU,CAAC0E,SAAS;QACvB,OAAO,UAAU;MACnB,KAAK1E,UAAU,CAACoI,KAAK;QACnB,OAAO,SAAS;MAClB;QACE,OAAO,aAAa;;EAE1B;EAEAzH,uBAAuBA,CAAA;IACrB,IAAI,CAAC,IAAI,CAACmD,UAAU,EAAE,OAAO,EAAE;IAE/B;IACA;IACA,OACE,IAAI,CAACA,UAAU,CAACuE,SAAS,EAAEC,QAAQ,IACnC,IAAI,CAACxE,UAAU,CAACyE,MAAM,EAAED,QAAQ,IAChC,aAAa;EAEjB;EAEAxH,yBAAyBA,CAAA;IACvB,IAAI,CAAC,IAAI,CAACgD,UAAU,EAAE,OAAO,mCAAmC;IAEhE;IACA,OACE,IAAI,CAACA,UAAU,CAACuE,SAAS,EAAEG,KAAK,IAChC,IAAI,CAAC1E,UAAU,CAACyE,MAAM,EAAEC,KAAK,IAC7B,mCAAmC;EAEvC;;;uBA3MW9E,mBAAmB,EAAAzD,EAAA,CAAAwI,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA1I,EAAA,CAAAwI,iBAAA,CAAAG,EAAA,CAAAC,aAAA;IAAA;EAAA;;;YAAnBnF,mBAAmB;MAAAoF,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCVhCnJ,EAAA,CAAAK,UAAA,IAAAgJ,kCAAA,mBA4MM;;;UA3MHrJ,EAAA,CAAAW,UAAA,SAAAyI,GAAA,CAAAvF,UAAA,CAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}