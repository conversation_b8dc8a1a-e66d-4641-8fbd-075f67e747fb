{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/OneDrive/Bureau/Project PI/devBridge/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { __decorate } from \"tslib\";\nimport { Component, ViewChild } from '@angular/core';\nimport { Validators } from '@angular/forms';\nimport { Subscription } from 'rxjs';\nimport { CallType } from '../../../../models/message.model';\nexport let MessageChatComponent = class MessageChatComponent {\n  constructor(fb, route, router, MessageService, callService, toastService, cdr) {\n    this.fb = fb;\n    this.route = route;\n    this.router = router;\n    this.MessageService = MessageService;\n    this.callService = callService;\n    this.toastService = toastService;\n    this.cdr = cdr;\n    // === DONNÉES PRINCIPALES ===\n    this.conversation = null;\n    this.messages = [];\n    this.currentUserId = null;\n    this.currentUsername = 'You';\n    this.otherParticipant = null;\n    // === ÉTATS DE L'INTERFACE ===\n    this.isLoading = false;\n    this.isLoadingMore = false;\n    this.hasMoreMessages = true;\n    this.showEmojiPicker = false;\n    this.showAttachmentMenu = false;\n    this.showSearch = false;\n    this.searchQuery = '';\n    this.searchResults = [];\n    this.searchMode = false;\n    this.isSendingMessage = false;\n    this.otherUserIsTyping = false;\n    this.showMainMenu = false;\n    this.showMessageContextMenu = false;\n    this.selectedMessage = null;\n    this.contextMenuPosition = {\n      x: 0,\n      y: 0\n    };\n    this.showReactionPicker = false;\n    this.reactionPickerMessage = null;\n    this.showImageViewer = false;\n    this.selectedImage = null;\n    this.uploadProgress = 0;\n    this.isUploading = false;\n    this.isDragOver = false;\n    // === GESTION VOCALE OPTIMISÉE ===\n    this.isRecordingVoice = false;\n    this.voiceRecordingDuration = 0;\n    this.voiceRecordingState = 'idle';\n    this.mediaRecorder = null;\n    this.audioChunks = [];\n    this.recordingTimer = null;\n    this.voiceWaves = [4, 8, 12, 16, 20, 16, 12, 8, 4, 8, 12, 16, 20, 16, 12, 8];\n    // Lecture des messages vocaux\n    this.currentAudio = null;\n    this.playingMessageId = null;\n    this.voicePlayback = {};\n    // === APPELS WEBRTC ===\n    this.isInCall = false;\n    this.callType = null;\n    this.callDuration = 0;\n    this.callTimer = null;\n    // État de l'appel WebRTC\n    this.activeCall = null;\n    this.isCallConnected = false;\n    this.isMuted = false;\n    this.isVideoEnabled = true;\n    this.localVideoElement = null;\n    this.remoteVideoElement = null;\n    // === ÉMOJIS ===\n    this.emojiCategories = [{\n      id: 'smileys',\n      name: 'Smileys',\n      icon: '😀',\n      emojis: [{\n        emoji: '😀',\n        name: 'grinning face'\n      }, {\n        emoji: '😃',\n        name: 'grinning face with big eyes'\n      }, {\n        emoji: '😄',\n        name: 'grinning face with smiling eyes'\n      }, {\n        emoji: '😁',\n        name: 'beaming face with smiling eyes'\n      }, {\n        emoji: '😆',\n        name: 'grinning squinting face'\n      }, {\n        emoji: '😅',\n        name: 'grinning face with sweat'\n      }, {\n        emoji: '😂',\n        name: 'face with tears of joy'\n      }, {\n        emoji: '🤣',\n        name: 'rolling on the floor laughing'\n      }, {\n        emoji: '😊',\n        name: 'smiling face with smiling eyes'\n      }, {\n        emoji: '😇',\n        name: 'smiling face with halo'\n      }]\n    }, {\n      id: 'people',\n      name: 'People',\n      icon: '👤',\n      emojis: [{\n        emoji: '👶',\n        name: 'baby'\n      }, {\n        emoji: '🧒',\n        name: 'child'\n      }, {\n        emoji: '👦',\n        name: 'boy'\n      }, {\n        emoji: '👧',\n        name: 'girl'\n      }, {\n        emoji: '🧑',\n        name: 'person'\n      }, {\n        emoji: '👨',\n        name: 'man'\n      }, {\n        emoji: '👩',\n        name: 'woman'\n      }, {\n        emoji: '👴',\n        name: 'old man'\n      }, {\n        emoji: '👵',\n        name: 'old woman'\n      }]\n    }, {\n      id: 'nature',\n      name: 'Nature',\n      icon: '🌿',\n      emojis: [{\n        emoji: '🐶',\n        name: 'dog face'\n      }, {\n        emoji: '🐱',\n        name: 'cat face'\n      }, {\n        emoji: '🐭',\n        name: 'mouse face'\n      }, {\n        emoji: '🐹',\n        name: 'hamster'\n      }, {\n        emoji: '🐰',\n        name: 'rabbit face'\n      }, {\n        emoji: '🦊',\n        name: 'fox'\n      }, {\n        emoji: '🐻',\n        name: 'bear'\n      }, {\n        emoji: '🐼',\n        name: 'panda'\n      }]\n    }];\n    this.selectedEmojiCategory = this.emojiCategories[0];\n    // === PAGINATION ===\n    this.MAX_MESSAGES_TO_LOAD = 10;\n    this.currentPage = 1;\n    // === AUTRES ÉTATS ===\n    this.isTyping = false;\n    this.isUserTyping = false;\n    this.typingTimeout = null;\n    this.subscriptions = new Subscription();\n    this.messageForm = this.fb.group({\n      content: ['', [Validators.required, Validators.minLength(1)]]\n    });\n  }\n  // Méthode pour vérifier si le champ de saisie doit être désactivé\n  isInputDisabled() {\n    return !this.otherParticipant || this.isRecordingVoice || this.isSendingMessage;\n  }\n  // Méthode pour gérer l'état du contrôle de saisie\n  updateInputState() {\n    const contentControl = this.messageForm.get('content');\n    if (this.isInputDisabled()) {\n      contentControl?.disable();\n    } else {\n      contentControl?.enable();\n    }\n  }\n  ngOnInit() {\n    this.initializeComponent();\n    // Activer les sons après interaction utilisateur\n    this.enableSoundsOnFirstInteraction();\n  }\n  ngAfterViewInit() {\n    // Configurer les éléments vidéo pour WebRTC après que la vue soit initialisée\n    setTimeout(() => {\n      this.setupVideoElements();\n    }, 100);\n    // Réessayer plusieurs fois pour s'assurer que les éléments sont configurés\n    setTimeout(() => {\n      this.setupVideoElements();\n    }, 500);\n    setTimeout(() => {\n      this.setupVideoElements();\n    }, 1000);\n    setTimeout(() => {\n      this.setupVideoElements();\n    }, 2000);\n  }\n  /**\n   * Configure les éléments vidéo pour WebRTC\n   */\n  setupVideoElements() {\n    // Essayer d'abord les éléments visibles (pour appels vidéo)\n    if (this.localVideo && this.remoteVideo) {\n      this.callService.setVideoElements(this.localVideo.nativeElement, this.remoteVideo.nativeElement);\n    }\n    // Sinon utiliser les éléments cachés (pour appels audio)\n    else if (this.localVideoHidden && this.remoteVideoHidden) {\n      this.callService.setVideoElements(this.localVideoHidden.nativeElement, this.remoteVideoHidden.nativeElement);\n    } else {\n      this.createVideoElementsManually();\n      // Réessayer après un délai\n      setTimeout(() => {\n        this.setupVideoElements();\n      }, 500);\n      // Réessayer encore plus tard\n      setTimeout(() => {\n        this.setupVideoElements();\n      }, 1500);\n    }\n  }\n  /**\n   * Crée les éléments vidéo manuellement si les ViewChild ne fonctionnent pas\n   */\n  createVideoElementsManually() {\n    // Chercher les éléments dans le DOM\n    const localVideoEl = document.getElementById('localVideo');\n    const remoteVideoEl = document.getElementById('remoteVideo');\n    if (localVideoEl && remoteVideoEl) {\n      console.log('✅ [MessageChat] Found video elements in DOM, configuring...');\n      this.callService.setVideoElements(localVideoEl, remoteVideoEl);\n    } else {\n      console.warn('⚠️ [MessageChat] Video elements not found in DOM either');\n      // Créer les éléments dynamiquement\n      const localVideo = document.createElement('video');\n      localVideo.id = 'localVideo';\n      localVideo.autoplay = true;\n      localVideo.muted = true;\n      localVideo.playsInline = true;\n      localVideo.style.cssText = 'position: absolute; top: -9999px; left: -9999px; width: 1px; height: 1px;';\n      const remoteVideo = document.createElement('video');\n      remoteVideo.id = 'remoteVideo';\n      remoteVideo.autoplay = true;\n      remoteVideo.playsInline = true;\n      remoteVideo.style.cssText = 'position: absolute; top: -9999px; left: -9999px; width: 1px; height: 1px;';\n      document.body.appendChild(localVideo);\n      document.body.appendChild(remoteVideo);\n      this.callService.setVideoElements(localVideo, remoteVideo);\n    }\n  }\n  enableSoundsOnFirstInteraction() {\n    const enableSounds = () => {\n      this.callService.enableSounds();\n      document.removeEventListener('click', enableSounds);\n      document.removeEventListener('keydown', enableSounds);\n      document.removeEventListener('touchstart', enableSounds);\n    };\n    document.addEventListener('click', enableSounds, {\n      once: true\n    });\n    document.addEventListener('keydown', enableSounds, {\n      once: true\n    });\n    document.addEventListener('touchstart', enableSounds, {\n      once: true\n    });\n  }\n  initializeComponent() {\n    this.loadCurrentUser();\n    this.loadConversation();\n    this.setupCallSubscriptions();\n  }\n  setupCallSubscriptions() {\n    // S'abonner aux appels entrants\n    this.subscriptions.add(this.callService.incomingCall$.subscribe({\n      next: incomingCall => {\n        if (incomingCall) {\n          this.handleIncomingCall(incomingCall);\n        }\n      },\n      error: error => {\n        console.error('❌ Error in incoming call subscription:', error);\n      }\n    }));\n    // S'abonner aux changements d'état d'appel\n    this.subscriptions.add(this.callService.activeCall$.subscribe({\n      next: call => {\n        if (call) {\n          this.activeCall = call;\n        }\n      },\n      error: error => {\n        console.error('❌ Error in active call subscription:', error);\n      }\n    }));\n  }\n  handleIncomingCall(incomingCall) {\n    // Afficher une notification ou modal d'appel entrant\n    // Pour l'instant, on log juste\n    console.log('🔔 Handling incoming call from:', incomingCall.caller.username);\n    // Jouer la sonnerie\n    this.MessageService.play('ringtone');\n    // Ici on pourrait afficher une modal ou notification\n    // Pour l'instant, on accepte automatiquement pour tester\n    // this.acceptCall(incomingCall);\n  }\n\n  loadCurrentUser() {\n    try {\n      const userString = localStorage.getItem('user');\n      if (!userString || userString === 'null' || userString === 'undefined') {\n        console.error('❌ No user data in localStorage');\n        this.currentUserId = null;\n        this.currentUsername = 'You';\n        return;\n      }\n      const user = JSON.parse(userString);\n      // Essayer différentes propriétés pour l'ID utilisateur\n      const userId = user._id || user.id || user.userId;\n      if (userId) {\n        this.currentUserId = userId;\n        this.currentUsername = user.username || user.name || 'You';\n      } else {\n        console.error('❌ No valid user ID found in user object:', user);\n        this.currentUserId = null;\n        this.currentUsername = 'You';\n      }\n    } catch (error) {\n      console.error('❌ Error parsing user from localStorage:', error);\n      this.currentUserId = null;\n      this.currentUsername = 'You';\n    }\n  }\n  loadConversation() {\n    const conversationId = this.route.snapshot.paramMap.get('id');\n    if (!conversationId) {\n      this.toastService.showError('ID de conversation manquant');\n      return;\n    }\n    this.isLoading = true;\n    // Nettoyer les subscriptions existantes avant de recharger\n    this.cleanupSubscriptions();\n    this.MessageService.getConversation(conversationId).subscribe({\n      next: conversation => {\n        this.conversation = conversation;\n        this.setOtherParticipant();\n        this.loadMessages();\n        // Configurer les subscriptions temps réel après le chargement de la conversation\n        this.setupSubscriptions();\n        this.isLoading = false;\n      },\n      error: error => {\n        console.error('Erreur lors du chargement de la conversation:', error);\n        this.toastService.showError('Erreur lors du chargement de la conversation');\n        this.isLoading = false;\n        // Réessayer après 2 secondes en cas d'erreur\n        setTimeout(() => {\n          this.loadConversation();\n        }, 2000);\n      }\n    });\n  }\n  setOtherParticipant() {\n    if (!this.conversation?.participants || this.conversation.participants.length === 0) {\n      console.warn('No participants found in conversation');\n      this.otherParticipant = null;\n      return;\n    }\n    // Dans une conversation 1-à-1, on veut afficher l'autre personne (pas l'utilisateur actuel)\n    // Dans une conversation de groupe, on peut afficher le nom du groupe ou le premier autre participant\n    if (this.conversation.isGroup) {\n      // Pour les groupes, on pourrait afficher le nom du groupe\n      // Mais pour l'instant, on prend le premier participant qui n'est pas l'utilisateur actuel\n      this.otherParticipant = this.conversation.participants.find(p => {\n        const participantId = p.id || p._id;\n        return String(participantId) !== String(this.currentUserId);\n      });\n    } else {\n      // Pour les conversations 1-à-1, on prend l'autre participant\n      this.otherParticipant = this.conversation.participants.find(p => {\n        const participantId = p.id || p._id;\n        console.log('Comparing participant ID:', participantId, 'with current user ID:', this.currentUserId);\n        return String(participantId) !== String(this.currentUserId);\n      });\n    }\n    // Fallback si aucun autre participant n'est trouvé\n    if (!this.otherParticipant && this.conversation.participants.length > 0) {\n      this.otherParticipant = this.conversation.participants[0];\n      // Si le premier participant est l'utilisateur actuel et qu'il y en a d'autres\n      if (this.conversation.participants.length > 1) {\n        const firstParticipantId = this.otherParticipant.id || this.otherParticipant._id;\n        if (String(firstParticipantId) === String(this.currentUserId)) {\n          console.log('First participant is current user, using second participant');\n          this.otherParticipant = this.conversation.participants[1];\n        }\n      }\n    }\n    // Vérification finale et logs\n    if (this.otherParticipant) {\n      // Log très visible pour debug\n      console.log('🎯 FINAL RESULT: otherParticipant =', this.otherParticipant.username);\n      console.log('🎯 Should display in sidebar:', this.otherParticipant.username);\n    } else {\n      console.error('❌ No other participant found! This should not happen.');\n      // Log très visible pour debug\n    }\n    // Mettre à jour l'état du champ de saisie\n    this.updateInputState();\n  }\n  loadMessages() {\n    if (!this.conversation?.id) return;\n    // Les messages sont déjà chargés avec la conversation\n    let messages = this.conversation.messages || [];\n    // Trier les messages par timestamp (plus anciens en premier)\n    this.messages = messages.sort((a, b) => {\n      const dateA = new Date(a.timestamp || a.createdAt).getTime();\n      const dateB = new Date(b.timestamp || b.createdAt).getTime();\n      return dateA - dateB; // Ordre croissant (plus anciens en premier)\n    });\n\n    console.log('📋 Messages loaded and sorted:', {\n      total: this.messages.length,\n      first: this.messages[0]?.content,\n      last: this.messages[this.messages.length - 1]?.content\n    });\n    this.hasMoreMessages = this.messages.length === this.MAX_MESSAGES_TO_LOAD;\n    this.isLoading = false;\n    this.scrollToBottom();\n  }\n  loadMoreMessages() {\n    if (this.isLoadingMore || !this.hasMoreMessages || !this.conversation?.id) return;\n    this.isLoadingMore = true;\n    this.currentPage++;\n    // Calculer l'offset basé sur les messages déjà chargés\n    const offset = this.messages.length;\n    this.MessageService.getMessages(this.currentUserId,\n    // senderId\n    this.otherParticipant?.id || this.otherParticipant?._id,\n    // receiverId\n    this.conversation.id, this.currentPage, this.MAX_MESSAGES_TO_LOAD).subscribe({\n      next: newMessages => {\n        if (newMessages && newMessages.length > 0) {\n          // Ajouter les nouveaux messages au début de la liste\n          this.messages = [...newMessages.reverse(), ...this.messages];\n          this.hasMoreMessages = newMessages.length === this.MAX_MESSAGES_TO_LOAD;\n        } else {\n          this.hasMoreMessages = false;\n        }\n        this.isLoadingMore = false;\n      },\n      error: error => {\n        console.error('Erreur lors du chargement des messages:', error);\n        this.toastService.showError('Erreur lors du chargement des messages');\n        this.isLoadingMore = false;\n        this.currentPage--; // Revenir à la page précédente en cas d'erreur\n      }\n    });\n  }\n  /**\n   * Nettoie les subscriptions existantes\n   */\n  cleanupSubscriptions() {\n    this.subscriptions.unsubscribe();\n    this.subscriptions = new Subscription();\n  }\n  /**\n   * Recharge la conversation actuelle\n   */\n  reloadConversation() {\n    if (this.conversation?.id) {\n      // Réinitialiser l'état\n      this.messages = [];\n      this.currentPage = 1;\n      this.hasMoreMessages = true;\n      // Recharger\n      this.loadConversation();\n    }\n  }\n  setupSubscriptions() {\n    if (!this.conversation?.id) {\n      console.warn('❌ Cannot setup subscriptions: no conversation ID');\n      return;\n    }\n    console.log('🔄 Setting up real-time subscriptions for conversation:', this.conversation.id);\n    // Subscription pour les nouveaux messages\n    this.subscriptions.add(this.MessageService.subscribeToNewMessages(this.conversation.id).subscribe({\n      next: newMessage => {\n        console.log('📨 Message structure:', {\n          id: newMessage.id,\n          type: newMessage.type,\n          content: newMessage.content,\n          sender: newMessage.sender,\n          senderId: newMessage.senderId,\n          receiverId: newMessage.receiverId,\n          attachments: newMessage.attachments\n        });\n        // Debug des attachments\n        console.log('📨 [Debug] Message type detected:', this.getMessageType(newMessage));\n        if (newMessage.attachments) {\n          newMessage.attachments.forEach((att, index) => {\n            console.log(`📨 [Debug] Attachment ${index}:`, {\n              type: att.type,\n              url: att.url,\n              path: att.path,\n              name: att.name,\n              size: att.size\n            });\n          });\n        }\n        // Ajouter le message à la liste s'il n'existe pas déjà\n        const messageExists = this.messages.some(msg => msg.id === newMessage.id);\n        if (!messageExists) {\n          // Ajouter le nouveau message à la fin (en bas)\n          this.messages.push(newMessage);\n          console.log('✅ Message added to list, total messages:', this.messages.length);\n          // Forcer la détection de changements\n          this.cdr.detectChanges();\n          // Scroll vers le bas après un court délai\n          setTimeout(() => {\n            this.scrollToBottom();\n          }, 50);\n          // Marquer comme lu si ce n'est pas notre message\n          const senderId = newMessage.sender?.id || newMessage.senderId;\n          console.log('📨 Checking if message should be marked as read:', {\n            senderId,\n            currentUserId: this.currentUserId,\n            shouldMarkAsRead: senderId !== this.currentUserId\n          });\n          if (senderId && senderId !== this.currentUserId) {\n            this.markMessageAsRead(newMessage.id);\n          }\n        }\n      },\n      error: error => {\n        console.error('❌ Error in message subscription:', error);\n        this.toastService.showError('Connexion temps réel interrompue');\n        // Réessayer la connexion après 5 secondes\n        setTimeout(() => {\n          this.setupSubscriptions();\n        }, 5000);\n      }\n    }));\n    // Subscription pour les indicateurs de frappe\n    this.subscriptions.add(this.MessageService.subscribeToTypingIndicator(this.conversation.id).subscribe({\n      next: typingData => {\n        // Afficher l'indicateur seulement si c'est l'autre utilisateur qui tape\n        if (typingData.userId !== this.currentUserId) {\n          this.otherUserIsTyping = typingData.isTyping;\n          this.isUserTyping = typingData.isTyping; // Pour compatibilité avec le template\n          console.log('📝 Other user typing status updated:', this.otherUserIsTyping);\n          this.cdr.detectChanges();\n        }\n      },\n      error: error => {\n        console.error('❌ Error in typing subscription:', error);\n      }\n    }));\n    // Subscription pour les mises à jour de conversation\n    this.subscriptions.add(this.MessageService.subscribeToConversationUpdates(this.conversation.id).subscribe({\n      next: conversationUpdate => {\n        // Mettre à jour la conversation si nécessaire\n        if (conversationUpdate.id === this.conversation.id) {\n          this.conversation = {\n            ...this.conversation,\n            ...conversationUpdate\n          };\n          this.cdr.detectChanges();\n        }\n      },\n      error: error => {\n        console.error('❌ Error in conversation subscription:', error);\n      }\n    }));\n  }\n  markMessageAsRead(messageId) {\n    this.MessageService.markMessageAsRead(messageId).subscribe({\n      next: () => {},\n      error: error => {\n        console.error('❌ Error marking message as read:', error);\n      }\n    });\n  }\n  // === ENVOI DE MESSAGES ===\n  sendMessage() {\n    if (!this.messageForm.valid || !this.conversation?.id) return;\n    const content = this.messageForm.get('content')?.value?.trim();\n    if (!content) return;\n    const receiverId = this.otherParticipant?.id || this.otherParticipant?._id;\n    if (!receiverId) {\n      this.toastService.showError('Destinataire introuvable');\n      return;\n    }\n    // Désactiver le bouton d'envoi\n    this.isSendingMessage = true;\n    this.updateInputState();\n    console.log('📤 Sending message:', {\n      content,\n      receiverId,\n      conversationId: this.conversation.id\n    });\n    this.MessageService.sendMessage(receiverId, content, undefined, 'TEXT', this.conversation.id).subscribe({\n      next: message => {\n        // Ajouter le message à la liste s'il n'y est pas déjà\n        const messageExists = this.messages.some(msg => msg.id === message.id);\n        if (!messageExists) {\n          this.messages.push(message);\n          console.log('📋 Message added to local list, total:', this.messages.length);\n        }\n        // Réinitialiser le formulaire\n        this.messageForm.reset();\n        this.isSendingMessage = false;\n        this.updateInputState();\n        // Forcer la détection de changements et scroll\n        this.cdr.detectChanges();\n        setTimeout(() => {\n          this.scrollToBottom();\n        }, 50);\n      },\n      error: error => {\n        console.error(\"❌ Erreur lors de l'envoi du message:\", error);\n        this.toastService.showError(\"Erreur lors de l'envoi du message\");\n        this.isSendingMessage = false;\n        this.updateInputState();\n      }\n    });\n  }\n  scrollToBottom() {\n    setTimeout(() => {\n      if (this.messagesContainer) {\n        const element = this.messagesContainer.nativeElement;\n        element.scrollTop = element.scrollHeight;\n      }\n    }, 100);\n  }\n  // === MÉTHODES UTILITAIRES OPTIMISÉES ===\n  formatLastActive(lastActive) {\n    if (!lastActive) return 'Hors ligne';\n    const diffMins = Math.floor((Date.now() - new Date(lastActive).getTime()) / 60000);\n    if (diffMins < 1) return \"À l'instant\";\n    if (diffMins < 60) return `Il y a ${diffMins} min`;\n    if (diffMins < 1440) return `Il y a ${Math.floor(diffMins / 60)}h`;\n    return `Il y a ${Math.floor(diffMins / 1440)}j`;\n  }\n  // Méthodes utilitaires pour les messages vocaux\n  getVoicePlaybackData(messageId) {\n    return this.voicePlayback[messageId] || {\n      progress: 0,\n      duration: 0,\n      currentTime: 0,\n      speed: 1\n    };\n  }\n  setVoicePlaybackData(messageId, data) {\n    this.voicePlayback[messageId] = {\n      ...this.getVoicePlaybackData(messageId),\n      ...data\n    };\n  }\n  // === MÉTHODES POUR LES MESSAGES VOCAUX (TEMPLATE) ===\n  // === MÉTHODES POUR LES APPELS (TEMPLATE) ===\n  startVideoCall() {\n    if (!this.otherParticipant?.id) {\n      this.toastService.showError(\"Impossible de démarrer l'appel\");\n      return;\n    }\n    this.initiateCall(CallType.VIDEO);\n  }\n  startVoiceCall() {\n    if (!this.otherParticipant?.id) {\n      this.toastService.showError(\"Impossible de démarrer l'appel\");\n      return;\n    }\n    // Forcer la configuration des éléments vidéo avant l'appel\n    this.setupVideoElements();\n    this.initiateCall(CallType.AUDIO);\n  }\n  /**\n   * Bascule l'état du microphone\n   */\n  toggleMicrophone() {\n    this.isMuted = !this.isMuted;\n    if (this.callService.toggleAudio) {\n      this.callService.toggleAudio();\n    }\n    this.toastService.showSuccess(this.isMuted ? 'Microphone coupé' : 'Microphone activé');\n  }\n  /**\n   * Bascule l'état de la caméra\n   */\n  toggleCamera() {\n    this.isVideoEnabled = !this.isVideoEnabled;\n    console.log('📹 Camera toggled:', this.isVideoEnabled ? 'ENABLED' : 'DISABLED');\n    if (this.callService.toggleVideo) {\n      this.callService.toggleVideo();\n    }\n    this.toastService.showSuccess(this.isVideoEnabled ? 'Caméra activée' : 'Caméra désactivée');\n  }\n  /**\n   * Termine l'appel en cours\n   */\n  endCall() {\n    if (this.activeCall) {\n      if (this.callService.endCall) {\n        this.callService.endCall(this.activeCall.id).subscribe({\n          next: () => {\n            this.activeCall = null;\n            this.isCallConnected = false;\n          },\n          error: error => {\n            console.error('❌ Error ending call:', error);\n            this.toastService.showError(\"Erreur lors de la fin de l'appel\");\n          }\n        });\n      } else {\n        // Fallback si la méthode n'existe pas\n        this.activeCall = null;\n        this.isCallConnected = false;\n        this.toastService.showSuccess('Appel terminé');\n      }\n    }\n  }\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // onCallAccepted, onCallRejected - définies plus loin\n  // === MÉTHODES POUR LES FICHIERS (TEMPLATE) ===\n  formatFileSize(bytes) {\n    if (bytes < 1024) return bytes + ' B';\n    if (bytes < 1048576) return Math.round(bytes / 1024) + ' KB';\n    return Math.round(bytes / 1048576) + ' MB';\n  }\n  downloadFile(message) {\n    const fileAttachment = message.attachments?.find(att => !att.type?.startsWith('image/'));\n    if (fileAttachment?.url) {\n      const link = document.createElement('a');\n      link.href = fileAttachment.url;\n      link.download = fileAttachment.name || 'file';\n      link.target = '_blank';\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      this.toastService.showSuccess('Téléchargement démarré');\n    }\n  }\n  // === MÉTHODES POUR L'INTERFACE UTILISATEUR (TEMPLATE) ===\n  toggleSearch() {\n    this.searchMode = !this.searchMode;\n    this.showSearch = this.searchMode;\n  }\n  toggleMainMenu() {\n    this.showMainMenu = !this.showMainMenu;\n  }\n  goBackToConversations() {\n    // Naviguer vers la liste des conversations\n    this.router.navigate(['/front/messages/conversations']).then(() => {}).catch(error => {\n      console.error('❌ Navigation error:', error);\n      // Fallback: essayer la route parent\n      this.router.navigate(['/front/messages']).catch(() => {\n        // Dernier recours: recharger la page\n        window.location.href = '/front/messages/conversations';\n      });\n    });\n  }\n  // === MÉTHODES POUR LES MENUS ET INTERACTIONS ===\n  closeAllMenus() {\n    this.showEmojiPicker = false;\n    this.showAttachmentMenu = false;\n    this.showMainMenu = false;\n    this.showMessageContextMenu = false;\n    this.showReactionPicker = false;\n  }\n  onMessageContextMenu(message, event) {\n    event.preventDefault();\n    this.selectedMessage = message;\n    this.contextMenuPosition = {\n      x: event.clientX,\n      y: event.clientY\n    };\n    this.showMessageContextMenu = true;\n  }\n  showQuickReactions(message, event) {\n    event.stopPropagation();\n    this.reactionPickerMessage = message;\n    this.contextMenuPosition = {\n      x: event.clientX,\n      y: event.clientY\n    };\n    this.showReactionPicker = true;\n  }\n  quickReact(emoji) {\n    if (this.reactionPickerMessage) {\n      this.toggleReaction(this.reactionPickerMessage.id, emoji);\n    }\n    this.showReactionPicker = false;\n  }\n  toggleReaction(messageId, emoji) {\n    if (!messageId || !emoji) {\n      console.error('❌ Missing messageId or emoji for reaction');\n      return;\n    }\n    // Appeler le service pour ajouter/supprimer la réaction\n    this.MessageService.reactToMessage(messageId, emoji).subscribe({\n      next: result => {\n        // Mettre à jour le message local avec les nouvelles réactions\n        const messageIndex = this.messages.findIndex(msg => msg.id === messageId);\n        if (messageIndex !== -1) {\n          this.messages[messageIndex] = result;\n          this.cdr.detectChanges();\n        }\n        this.toastService.showSuccess(`Réaction ${emoji} ajoutée`);\n      },\n      error: error => {\n        console.error('❌ Error toggling reaction:', error);\n        this.toastService.showError(\"Erreur lors de l'ajout de la réaction\");\n      }\n    });\n  }\n  hasUserReacted(reaction, userId) {\n    return reaction.userId === userId;\n  }\n  replyToMessage(message) {\n    this.closeAllMenus();\n  }\n  forwardMessage(message) {\n    this.closeAllMenus();\n  }\n  deleteMessage(message) {\n    if (!message.id) {\n      console.error('❌ No message ID provided for deletion');\n      this.toastService.showError('Erreur: ID du message manquant');\n      return;\n    }\n    // Vérifier si l'utilisateur peut supprimer ce message\n    const canDelete = message.sender?.id === this.currentUserId || message.senderId === this.currentUserId;\n    if (!canDelete) {\n      this.toastService.showError('Vous ne pouvez supprimer que vos propres messages');\n      this.closeAllMenus();\n      return;\n    }\n    // Demander confirmation\n    if (!confirm('Êtes-vous sûr de vouloir supprimer ce message ?')) {\n      this.closeAllMenus();\n      return;\n    }\n    // Appeler le service pour supprimer le message\n    this.MessageService.deleteMessage(message.id).subscribe({\n      next: result => {\n        // Supprimer le message de la liste locale\n        this.messages = this.messages.filter(msg => msg.id !== message.id);\n        this.toastService.showSuccess('Message supprimé');\n        this.cdr.detectChanges();\n      },\n      error: error => {\n        console.error('❌ Error deleting message:', error);\n        this.toastService.showError('Erreur lors de la suppression du message');\n      }\n    });\n    this.closeAllMenus();\n  }\n  // === MÉTHODES POUR LES ÉMOJIS ET PIÈCES JOINTES ===\n  toggleEmojiPicker() {\n    this.showEmojiPicker = !this.showEmojiPicker;\n  }\n  selectEmojiCategory(category) {\n    this.selectedEmojiCategory = category;\n  }\n  getEmojisForCategory(category) {\n    return category?.emojis || [];\n  }\n  insertEmoji(emoji) {\n    const currentContent = this.messageForm.get('content')?.value || '';\n    const newContent = currentContent + emoji.emoji;\n    this.messageForm.patchValue({\n      content: newContent\n    });\n    this.showEmojiPicker = false;\n  }\n  toggleAttachmentMenu() {\n    this.showAttachmentMenu = !this.showAttachmentMenu;\n  }\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // triggerFileInput, getFileAcceptTypes, onFileSelected - définies plus loin\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // Ces méthodes sont déjà définies plus loin dans le fichier\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // handleTypingIndicator - définie plus loin\n  // === MÉTHODES UTILITAIRES POUR LE TEMPLATE ===\n  trackByMessageId(index, message) {\n    return message.id || message._id || index.toString();\n  }\n  // === MÉTHODES MANQUANTES POUR LE TEMPLATE ===\n  testAddMessage() {\n    const testMessage = {\n      id: `test-${Date.now()}`,\n      content: `Message de test ${new Date().toLocaleTimeString()}`,\n      timestamp: new Date().toISOString(),\n      sender: {\n        id: this.otherParticipant?.id || 'test-user',\n        username: this.otherParticipant?.username || 'Test User',\n        image: this.otherParticipant?.image || 'assets/images/default-avatar.png'\n      },\n      type: 'TEXT',\n      isRead: false\n    };\n    this.messages.push(testMessage);\n    this.cdr.detectChanges();\n    setTimeout(() => this.scrollToBottom(), 50);\n  }\n  isGroupConversation() {\n    return this.conversation?.isGroup || this.conversation?.participants?.length > 2 || false;\n  }\n  openCamera() {\n    this.showAttachmentMenu = false;\n    // TODO: Implémenter l'ouverture de la caméra\n  }\n\n  zoomImage(factor) {\n    const imageElement = document.querySelector('.image-viewer-zoom');\n    if (imageElement) {\n      const currentTransform = imageElement.style.transform || 'scale(1)';\n      const currentScale = parseFloat(currentTransform.match(/scale\\(([^)]+)\\)/)?.[1] || '1');\n      const newScale = Math.max(0.5, Math.min(3, currentScale * factor));\n      imageElement.style.transform = `scale(${newScale})`;\n      if (newScale > 1) {\n        imageElement.classList.add('zoomed');\n      } else {\n        imageElement.classList.remove('zoomed');\n      }\n    }\n  }\n  resetZoom() {\n    const imageElement = document.querySelector('.image-viewer-zoom');\n    if (imageElement) {\n      imageElement.style.transform = 'scale(1)';\n      imageElement.classList.remove('zoomed');\n    }\n  }\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // formatFileSize, downloadFile, toggleReaction, hasUserReacted, showQuickReactions\n  // toggleEmojiPicker, toggleAttachmentMenu, selectEmojiCategory, getEmojisForCategory, insertEmoji\n  // Ces méthodes sont déjà définies plus loin dans le fichier\n  triggerFileInput(type) {\n    const input = this.fileInput?.nativeElement;\n    if (!input) {\n      console.error('File input element not found');\n      return;\n    }\n    // Configurer le type de fichier accepté\n    if (type === 'image') {\n      input.accept = 'image/*';\n    } else if (type === 'video') {\n      input.accept = 'video/*';\n    } else if (type === 'document') {\n      input.accept = '.pdf,.doc,.docx,.xls,.xlsx,.txt';\n    } else {\n      input.accept = '*/*';\n    }\n    // Réinitialiser la valeur pour permettre la sélection du même fichier\n    input.value = '';\n    // Déclencher la sélection de fichier\n    input.click();\n    this.showAttachmentMenu = false;\n  }\n  formatMessageTime(timestamp) {\n    if (!timestamp) return '';\n    const date = new Date(timestamp);\n    return date.toLocaleTimeString('fr-FR', {\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  }\n  formatDateSeparator(timestamp) {\n    if (!timestamp) return '';\n    const date = new Date(timestamp);\n    const today = new Date();\n    const yesterday = new Date(today);\n    yesterday.setDate(yesterday.getDate() - 1);\n    if (date.toDateString() === today.toDateString()) {\n      return \"Aujourd'hui\";\n    } else if (date.toDateString() === yesterday.toDateString()) {\n      return 'Hier';\n    } else {\n      return date.toLocaleDateString('fr-FR');\n    }\n  }\n  formatMessageContent(content) {\n    if (!content) return '';\n    // Remplacer les URLs par des liens\n    const urlRegex = /(https?:\\/\\/[^\\s]+)/g;\n    return content.replace(urlRegex, '<a href=\"$1\" target=\"_blank\" class=\"text-blue-500 underline\">$1</a>');\n  }\n  shouldShowDateSeparator(index) {\n    if (index === 0) return true;\n    const currentMessage = this.messages[index];\n    const previousMessage = this.messages[index - 1];\n    if (!currentMessage?.timestamp || !previousMessage?.timestamp) return false;\n    const currentDate = new Date(currentMessage.timestamp).toDateString();\n    const previousDate = new Date(previousMessage.timestamp).toDateString();\n    return currentDate !== previousDate;\n  }\n  shouldShowAvatar(index) {\n    const currentMessage = this.messages[index];\n    const nextMessage = this.messages[index + 1];\n    if (!nextMessage) return true;\n    return currentMessage.sender?.id !== nextMessage.sender?.id;\n  }\n  shouldShowSenderName(index) {\n    const currentMessage = this.messages[index];\n    const previousMessage = this.messages[index - 1];\n    if (!previousMessage) return true;\n    return currentMessage.sender?.id !== previousMessage.sender?.id;\n  }\n  getMessageType(message) {\n    // Vérifier d'abord le type de message explicite\n    if (message.type) {\n      if (message.type === 'IMAGE' || message.type === 'image') return 'image';\n      if (message.type === 'VIDEO' || message.type === 'video') return 'video';\n      if (message.type === 'AUDIO' || message.type === 'audio') return 'audio';\n      if (message.type === 'VOICE_MESSAGE') return 'audio';\n      if (message.type === 'FILE' || message.type === 'file') return 'file';\n    }\n    // Ensuite vérifier les attachments\n    if (message.attachments && message.attachments.length > 0) {\n      const attachment = message.attachments[0];\n      if (attachment.type?.startsWith('image/')) return 'image';\n      if (attachment.type?.startsWith('video/')) return 'video';\n      if (attachment.type?.startsWith('audio/')) return 'audio';\n      return 'file';\n    }\n    // Vérifier si c'est un message vocal basé sur les propriétés\n    if (message.voiceUrl || message.audioUrl || message.voice) return 'audio';\n    return 'text';\n  }\n  hasImage(message) {\n    // Vérifier le type de message\n    if (message.type === 'IMAGE' || message.type === 'image') {\n      return true;\n    }\n    // Vérifier les attachments\n    const hasImageAttachment = message.attachments?.some(att => {\n      return att.type?.startsWith('image/') || att.type === 'IMAGE';\n    }) || false;\n    // Vérifier les propriétés directes d'image\n    const hasImageUrl = !!(message.imageUrl || message.image);\n    return hasImageAttachment || hasImageUrl;\n  }\n  hasFile(message) {\n    // Vérifier le type de message\n    if (message.type === 'FILE' || message.type === 'file') {\n      return true;\n    }\n    // Vérifier les attachments non-image\n    const hasFileAttachment = message.attachments?.some(att => {\n      return !att.type?.startsWith('image/') && att.type !== 'IMAGE';\n    }) || false;\n    return hasFileAttachment;\n  }\n  getImageUrl(message) {\n    // Vérifier les propriétés directes d'image\n    if (message.imageUrl) {\n      return message.imageUrl;\n    }\n    if (message.image) {\n      return message.image;\n    }\n    // Vérifier les attachments\n    const imageAttachment = message.attachments?.find(att => att.type?.startsWith('image/') || att.type === 'IMAGE');\n    if (imageAttachment) {\n      return imageAttachment.url || imageAttachment.path || '';\n    }\n    return '';\n  }\n  getFileName(message) {\n    const fileAttachment = message.attachments?.find(att => !att.type?.startsWith('image/'));\n    return fileAttachment?.name || 'Fichier';\n  }\n  getFileSize(message) {\n    const fileAttachment = message.attachments?.find(att => !att.type?.startsWith('image/'));\n    if (!fileAttachment?.size) return '';\n    const bytes = fileAttachment.size;\n    if (bytes < 1024) return bytes + ' B';\n    if (bytes < 1048576) return Math.round(bytes / 1024) + ' KB';\n    return Math.round(bytes / 1048576) + ' MB';\n  }\n  getFileIcon(message) {\n    const fileAttachment = message.attachments?.find(att => !att.type?.startsWith('image/'));\n    if (!fileAttachment?.type) return 'fas fa-file';\n    if (fileAttachment.type.startsWith('audio/')) return 'fas fa-file-audio';\n    if (fileAttachment.type.startsWith('video/')) return 'fas fa-file-video';\n    if (fileAttachment.type.includes('pdf')) return 'fas fa-file-pdf';\n    if (fileAttachment.type.includes('word')) return 'fas fa-file-word';\n    if (fileAttachment.type.includes('excel')) return 'fas fa-file-excel';\n    return 'fas fa-file';\n  }\n  getUserColor(userId) {\n    // Générer une couleur basée sur l'ID utilisateur\n    const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8'];\n    const index = userId.charCodeAt(0) % colors.length;\n    return colors[index];\n  }\n  // === MÉTHODES D'INTERACTION ===\n  onMessageClick(message, event) {}\n  onInputChange(event) {\n    // Gérer les changements dans le champ de saisie\n    this.handleTypingIndicator();\n  }\n  onInputKeyDown(event) {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n  onInputFocus() {\n    // Gérer le focus sur le champ de saisie\n  }\n  onInputBlur() {\n    // Gérer la perte de focus sur le champ de saisie\n  }\n  onScroll(event) {\n    // Gérer le scroll pour charger plus de messages\n    const element = event.target;\n    if (element.scrollTop === 0 && this.hasMoreMessages && !this.isLoadingMore) {\n      this.loadMoreMessages();\n    }\n  }\n  openUserProfile(userId) {}\n  onImageLoad(event, message) {\n    console.log('🖼️ [Debug] Image loaded successfully for message:', message.id, event.target.src);\n  }\n  onImageError(event, message) {\n    console.error('🖼️ [Debug] Image failed to load for message:', message.id, {\n      src: event.target.src,\n      error: event\n    });\n    // Optionnel : afficher une image de remplacement\n    event.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjNmNGY2Ii8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzZiNzI4MCIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkltYWdlIG5vbiBkaXNwb25pYmxlPC90ZXh0Pjwvc3ZnPg==';\n  }\n  openImageViewer(message) {\n    const imageAttachment = message.attachments?.find(att => att.type?.startsWith('image/'));\n    if (imageAttachment?.url) {\n      this.selectedImage = {\n        url: imageAttachment.url,\n        name: imageAttachment.name || 'Image',\n        size: this.formatFileSize(imageAttachment.size || 0),\n        message: message\n      };\n      this.showImageViewer = true;\n    }\n  }\n  closeImageViewer() {\n    this.showImageViewer = false;\n    this.selectedImage = null;\n  }\n  downloadImage() {\n    if (this.selectedImage?.url) {\n      const link = document.createElement('a');\n      link.href = this.selectedImage.url;\n      link.download = this.selectedImage.name || 'image';\n      link.target = '_blank';\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      this.toastService.showSuccess('Téléchargement démarré');\n      console.log('🖼️ [ImageViewer] Download started:', this.selectedImage.name);\n    }\n  }\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // zoomImage, resetZoom, formatFileSize, downloadFile, toggleReaction, hasUserReacted, toggleSearch\n  // Ces méthodes sont déjà définies plus loin dans le fichier\n  searchMessages() {\n    if (!this.searchQuery.trim()) {\n      this.searchResults = [];\n      return;\n    }\n    this.searchResults = this.messages.filter(message => message.content?.toLowerCase().includes(this.searchQuery.toLowerCase()) || message.sender?.username?.toLowerCase().includes(this.searchQuery.toLowerCase()));\n  }\n  onSearchQueryChange() {\n    this.searchMessages();\n  }\n  clearSearch() {\n    this.searchQuery = '';\n    this.searchResults = [];\n  }\n  jumpToMessage(messageId) {\n    const messageElement = document.getElementById(`message-${messageId}`);\n    if (messageElement) {\n      messageElement.scrollIntoView({\n        behavior: 'smooth',\n        block: 'center'\n      });\n      // Highlight temporairement le message\n      messageElement.classList.add('highlight');\n      setTimeout(() => {\n        messageElement.classList.remove('highlight');\n      }, 2000);\n    }\n  }\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // toggleMainMenu, toggleTheme, onMessageContextMenu\n  // Ces méthodes sont déjà définies plus loin dans le fichier\n  closeContextMenu() {\n    this.showMessageContextMenu = false;\n    this.selectedMessage = null;\n  }\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // replyToMessage, forwardMessage, deleteMessage, showQuickReactions, closeReactionPicker\n  // quickReact, closeAllMenus, testAddMessage, toggleAttachmentMenu, toggleEmojiPicker\n  // Ces méthodes sont déjà définies plus loin dans le fichier\n  // === MÉTHODE SUPPRIMÉE (DUPLICATION) ===\n  // triggerFileInput - définie plus loin\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // openCamera, getEmojisForCategory, selectEmojiCategory, insertEmoji\n  // goBackToConversations, startVideoCall, startVoiceCall\n  // Ces méthodes sont déjà définies plus loin dans le fichier\n  initiateCall(callType) {\n    console.log('📋 [MessageChat] Call details:', {\n      callType,\n      otherParticipant: this.otherParticipant,\n      conversation: this.conversation?.id,\n      currentUserId: this.currentUserId\n    });\n    if (!this.otherParticipant) {\n      console.error('❌ [MessageChat] No recipient selected');\n      this.toastService.showError('Aucun destinataire sélectionné');\n      return;\n    }\n    const recipientId = this.otherParticipant.id || this.otherParticipant._id;\n    if (!recipientId) {\n      console.error('❌ [MessageChat] Recipient ID not found');\n      this.toastService.showError('ID du destinataire introuvable');\n      return;\n    }\n    console.log(`📞 [MessageChat] Initiating ${callType} call to user:`, {\n      recipientId,\n      recipientName: this.otherParticipant.username || this.otherParticipant.name,\n      conversationId: this.conversation?.id\n    });\n    this.isInCall = true;\n    this.callType = callType === CallType.VIDEO ? 'VIDEO' : 'AUDIO';\n    this.callDuration = 0;\n    // Démarrer le timer d'appel\n    this.startCallTimer();\n    // Utiliser le CallService\n    this.callService.initiateCall(recipientId, callType, this.conversation?.id).subscribe({\n      next: call => {\n        this.activeCall = call;\n        this.isCallConnected = false;\n        this.toastService.showSuccess(`Appel ${callType === CallType.VIDEO ? 'vidéo' : 'audio'} initié`);\n        console.log('📡 [MessageChat] Call should now be sent to recipient via WebSocket');\n      },\n      error: error => {\n        console.error('❌ [MessageChat] Error initiating call:', {\n          error: error.message || error,\n          recipientId,\n          callType,\n          conversationId: this.conversation?.id\n        });\n        this.endCall();\n        this.toastService.showError(\"Erreur lors de l'initiation de l'appel\");\n      }\n    });\n  }\n  acceptCall(incomingCall) {\n    this.callService.acceptCall(incomingCall).subscribe({\n      next: call => {\n        this.activeCall = call;\n        this.isInCall = true;\n        this.isCallConnected = true;\n        this.callType = call.type === CallType.VIDEO ? 'VIDEO' : 'AUDIO';\n        this.startCallTimer();\n        this.toastService.showSuccess('Appel accepté');\n      },\n      error: error => {\n        console.error('❌ Error accepting call:', error);\n        this.toastService.showError(\"Erreur lors de l'acceptation de l'appel\");\n      }\n    });\n  }\n  rejectCall(incomingCall) {\n    this.callService.rejectCall(incomingCall.id, 'User rejected').subscribe({\n      next: () => {\n        this.toastService.showSuccess('Appel rejeté');\n      },\n      error: error => {\n        console.error('❌ Error rejecting call:', error);\n        this.toastService.showError(\"Erreur lors du rejet de l'appel\");\n      }\n    });\n  }\n  startCallTimer() {\n    this.callDuration = 0;\n    this.callTimer = setInterval(() => {\n      this.callDuration++;\n      this.cdr.detectChanges();\n    }, 1000);\n  }\n  resetCallState() {\n    if (this.callTimer) {\n      clearInterval(this.callTimer);\n      this.callTimer = null;\n    }\n    this.isInCall = false;\n    this.callType = null;\n    this.callDuration = 0;\n    this.activeCall = null;\n    this.isCallConnected = false;\n    this.isMuted = false;\n    this.isVideoEnabled = true;\n  }\n  // === CONTRÔLES D'APPEL ===\n  toggleMute() {\n    if (!this.activeCall) return;\n    this.isMuted = !this.isMuted;\n    // Utiliser la méthode toggleMedia du CallService\n    this.callService.toggleMedia(this.activeCall.id, undefined,\n    // video unchanged\n    !this.isMuted // audio state\n    ).subscribe({\n      next: () => {\n        this.toastService.showSuccess(this.isMuted ? 'Micro coupé' : 'Micro activé');\n      },\n      error: error => {\n        console.error('❌ Error toggling mute:', error);\n        // Revert state on error\n        this.isMuted = !this.isMuted;\n        this.toastService.showError('Erreur lors du changement du micro');\n      }\n    });\n  }\n  toggleVideo() {\n    if (!this.activeCall) return;\n    this.isVideoEnabled = !this.isVideoEnabled;\n    // Utiliser la méthode toggleMedia du CallService\n    this.callService.toggleMedia(this.activeCall.id, this.isVideoEnabled,\n    // video state\n    undefined // audio unchanged\n    ).subscribe({\n      next: () => {\n        this.toastService.showSuccess(this.isVideoEnabled ? 'Caméra activée' : 'Caméra désactivée');\n      },\n      error: error => {\n        console.error('❌ Error toggling video:', error);\n        // Revert state on error\n        this.isVideoEnabled = !this.isVideoEnabled;\n        this.toastService.showError('Erreur lors du changement de la caméra');\n      }\n    });\n  }\n  formatCallDuration(duration) {\n    const hours = Math.floor(duration / 3600);\n    const minutes = Math.floor(duration % 3600 / 60);\n    const seconds = duration % 60;\n    if (hours > 0) {\n      return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;\n    }\n    return `${minutes}:${seconds.toString().padStart(2, '0')}`;\n  }\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // trackByMessageId, isGroupConversation - Ces méthodes sont déjà définies plus loin dans le fichier\n  startVoiceRecording() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        // Vérifier le support du navigateur\n        if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {\n          throw new Error(\"Votre navigateur ne supporte pas l'enregistrement audio\");\n        }\n        // Vérifier si MediaRecorder est supporté\n        if (!window.MediaRecorder) {\n          throw new Error(\"MediaRecorder n'est pas supporté par votre navigateur\");\n        }\n        // Demander l'accès au microphone avec des contraintes optimisées\n        const stream = yield navigator.mediaDevices.getUserMedia({\n          audio: {\n            echoCancellation: true,\n            noiseSuppression: true,\n            autoGainControl: true,\n            sampleRate: 44100,\n            channelCount: 1\n          }\n        });\n        // Vérifier les types MIME supportés\n        let mimeType = 'audio/webm;codecs=opus';\n        if (!MediaRecorder.isTypeSupported(mimeType)) {\n          mimeType = 'audio/webm';\n          if (!MediaRecorder.isTypeSupported(mimeType)) {\n            mimeType = 'audio/mp4';\n            if (!MediaRecorder.isTypeSupported(mimeType)) {\n              mimeType = ''; // Laisser le navigateur choisir\n            }\n          }\n        }\n        // Créer le MediaRecorder\n        _this.mediaRecorder = new MediaRecorder(stream, {\n          mimeType: mimeType || undefined\n        });\n        // Initialiser les variables\n        _this.audioChunks = [];\n        _this.isRecordingVoice = true;\n        _this.voiceRecordingDuration = 0;\n        _this.voiceRecordingState = 'recording';\n        // Démarrer le timer\n        _this.recordingTimer = setInterval(() => {\n          _this.voiceRecordingDuration++;\n          // Animer les waves\n          _this.animateVoiceWaves();\n          _this.cdr.detectChanges();\n        }, 1000);\n        // Gérer les événements du MediaRecorder\n        _this.mediaRecorder.ondataavailable = event => {\n          if (event.data.size > 0) {\n            _this.audioChunks.push(event.data);\n          }\n        };\n        _this.mediaRecorder.onstop = () => {\n          _this.processRecordedAudio();\n        };\n        _this.mediaRecorder.onerror = event => {\n          console.error('🎤 [Voice] MediaRecorder error:', event.error);\n          _this.toastService.showError(\"Erreur lors de l'enregistrement\");\n          _this.cancelVoiceRecording();\n        };\n        // Démarrer l'enregistrement\n        _this.mediaRecorder.start(100); // Collecter les données toutes les 100ms\n        _this.toastService.showSuccess('🎤 Enregistrement vocal démarré');\n      } catch (error) {\n        console.error('🎤 [Voice] Error starting recording:', error);\n        let errorMessage = \"Impossible de démarrer l'enregistrement vocal\";\n        if (error.name === 'NotAllowedError') {\n          errorMessage = \"Accès au microphone refusé. Veuillez autoriser l'accès dans les paramètres de votre navigateur.\";\n        } else if (error.name === 'NotFoundError') {\n          errorMessage = 'Aucun microphone détecté. Veuillez connecter un microphone.';\n        } else if (error.name === 'NotSupportedError') {\n          errorMessage = \"Votre navigateur ne supporte pas l'enregistrement audio.\";\n        } else if (error.message) {\n          errorMessage = error.message;\n        }\n        _this.toastService.showError(errorMessage);\n        _this.cancelVoiceRecording();\n      }\n    })();\n  }\n  stopVoiceRecording() {\n    if (this.mediaRecorder && this.mediaRecorder.state === 'recording') {\n      this.mediaRecorder.stop();\n      this.mediaRecorder.stream.getTracks().forEach(track => track.stop());\n    }\n    if (this.recordingTimer) {\n      clearInterval(this.recordingTimer);\n      this.recordingTimer = null;\n    }\n    this.isRecordingVoice = false;\n    this.voiceRecordingState = 'processing';\n  }\n  cancelVoiceRecording() {\n    if (this.mediaRecorder) {\n      if (this.mediaRecorder.state === 'recording') {\n        this.mediaRecorder.stop();\n      }\n      this.mediaRecorder.stream.getTracks().forEach(track => track.stop());\n      this.mediaRecorder = null;\n    }\n    if (this.recordingTimer) {\n      clearInterval(this.recordingTimer);\n      this.recordingTimer = null;\n    }\n    this.isRecordingVoice = false;\n    this.voiceRecordingDuration = 0;\n    this.voiceRecordingState = 'idle';\n    this.audioChunks = [];\n  }\n  processRecordedAudio() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        // Vérifier qu'on a des données audio\n        if (_this2.audioChunks.length === 0) {\n          console.error('🎤 [Voice] No audio chunks available');\n          _this2.toastService.showError('Aucun audio enregistré');\n          _this2.cancelVoiceRecording();\n          return;\n        }\n        console.log('🎤 [Voice] Audio chunks:', _this2.audioChunks.length, 'Duration:', _this2.voiceRecordingDuration);\n        // Vérifier la durée minimale\n        if (_this2.voiceRecordingDuration < 1) {\n          console.error('🎤 [Voice] Recording too short:', _this2.voiceRecordingDuration);\n          _this2.toastService.showError('Enregistrement trop court (minimum 1 seconde)');\n          _this2.cancelVoiceRecording();\n          return;\n        }\n        // Déterminer le type MIME du blob\n        let mimeType = 'audio/webm;codecs=opus';\n        if (_this2.mediaRecorder?.mimeType) {\n          mimeType = _this2.mediaRecorder.mimeType;\n        }\n        // Créer le blob audio\n        const audioBlob = new Blob(_this2.audioChunks, {\n          type: mimeType\n        });\n        console.log('🎤 [Voice] Audio blob created:', {\n          size: audioBlob.size,\n          type: audioBlob.type\n        });\n        // Déterminer l'extension du fichier\n        let extension = '.webm';\n        if (mimeType.includes('mp4')) {\n          extension = '.mp4';\n        } else if (mimeType.includes('wav')) {\n          extension = '.wav';\n        } else if (mimeType.includes('ogg')) {\n          extension = '.ogg';\n        }\n        // Créer le fichier\n        const audioFile = new File([audioBlob], `voice_${Date.now()}${extension}`, {\n          type: mimeType\n        });\n        console.log('🎤 [Voice] Audio file created:', {\n          name: audioFile.name,\n          size: audioFile.size,\n          type: audioFile.type\n        });\n        // Envoyer le message vocal\n        _this2.voiceRecordingState = 'processing';\n        yield _this2.sendVoiceMessage(audioFile);\n        _this2.toastService.showSuccess('🎤 Message vocal envoyé');\n      } catch (error) {\n        console.error('🎤 [Voice] Error processing audio:', error);\n        _this2.toastService.showError(\"Erreur lors de l'envoi du message vocal: \" + (error.message || 'Erreur inconnue'));\n      } finally {\n        // Nettoyer l'état\n        _this2.voiceRecordingState = 'idle';\n        _this2.voiceRecordingDuration = 0;\n        _this2.audioChunks = [];\n        _this2.isRecordingVoice = false;\n      }\n    })();\n  }\n  sendVoiceMessage(audioFile) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      const receiverId = _this3.otherParticipant?.id || _this3.otherParticipant?._id;\n      if (!receiverId) {\n        throw new Error('Destinataire introuvable');\n      }\n      return new Promise((resolve, reject) => {\n        _this3.MessageService.sendMessage(receiverId, '', audioFile, 'AUDIO', _this3.conversation.id).subscribe({\n          next: message => {\n            _this3.messages.push(message);\n            _this3.scrollToBottom();\n            resolve();\n          },\n          error: error => {\n            console.error(\"Erreur lors de l'envoi du message vocal:\", error);\n            reject(error);\n          }\n        });\n      });\n    })();\n  }\n  formatRecordingDuration(duration) {\n    const minutes = Math.floor(duration / 60);\n    const seconds = duration % 60;\n    return `${minutes}:${seconds.toString().padStart(2, '0')}`;\n  }\n  // === MÉTHODES D'ENREGISTREMENT VOCAL AMÉLIORÉES ===\n  onRecordStart(event) {\n    event.preventDefault();\n    console.log('🎤 [Voice] Current state:', {\n      isRecordingVoice: this.isRecordingVoice,\n      voiceRecordingState: this.voiceRecordingState,\n      voiceRecordingDuration: this.voiceRecordingDuration,\n      mediaRecorder: !!this.mediaRecorder\n    });\n    // Vérifier si on peut enregistrer\n    if (this.voiceRecordingState === 'processing') {\n      this.toastService.showWarning('Traitement en cours...');\n      return;\n    }\n    if (this.isRecordingVoice) {\n      this.toastService.showWarning('Enregistrement déjà en cours...');\n      return;\n    }\n    // Afficher un message de début\n    this.toastService.showInfo(\"🎤 Démarrage de l'enregistrement vocal...\");\n    // Démarrer l'enregistrement\n    this.startVoiceRecording().catch(error => {\n      console.error('🎤 [Voice] Failed to start recording:', error);\n      this.toastService.showError(\"Impossible de démarrer l'enregistrement vocal: \" + (error.message || 'Erreur inconnue'));\n    });\n  }\n  onRecordEnd(event) {\n    event.preventDefault();\n    if (!this.isRecordingVoice) {\n      return;\n    }\n    // Arrêter l'enregistrement et envoyer\n    this.stopVoiceRecording();\n  }\n  onRecordCancel(event) {\n    event.preventDefault();\n    if (!this.isRecordingVoice) {\n      return;\n    }\n    // Annuler l'enregistrement\n    this.cancelVoiceRecording();\n  }\n  getRecordingFormat() {\n    if (this.mediaRecorder?.mimeType) {\n      if (this.mediaRecorder.mimeType.includes('webm')) return 'WebM';\n      if (this.mediaRecorder.mimeType.includes('mp4')) return 'MP4';\n      if (this.mediaRecorder.mimeType.includes('wav')) return 'WAV';\n      if (this.mediaRecorder.mimeType.includes('ogg')) return 'OGG';\n    }\n    return 'Auto';\n  }\n  // === ANIMATION DES WAVES VOCALES ===\n  animateVoiceWaves() {\n    // Animer les waves pendant l'enregistrement\n    this.voiceWaves = this.voiceWaves.map(() => {\n      return Math.floor(Math.random() * 20) + 4; // Hauteur entre 4 et 24px\n    });\n  }\n\n  onFileSelected(event) {\n    const files = event.target.files;\n    if (!files || files.length === 0) {\n      return;\n    }\n    for (let file of files) {\n      console.log(`📁 [Upload] Processing file: ${file.name}, size: ${file.size}, type: ${file.type}`);\n      this.uploadFile(file);\n    }\n  }\n  uploadFile(file) {\n    const receiverId = this.otherParticipant?.id || this.otherParticipant?._id;\n    if (!receiverId) {\n      console.error('📁 [Upload] No receiver ID found');\n      this.toastService.showError('Destinataire introuvable');\n      return;\n    }\n    // Vérifier la taille du fichier (max 50MB)\n    const maxSize = 50 * 1024 * 1024; // 50MB\n    if (file.size > maxSize) {\n      console.error(`📁 [Upload] File too large: ${file.size} bytes`);\n      this.toastService.showError('Fichier trop volumineux (max 50MB)');\n      return;\n    }\n    // 🖼️ Compression d'image si nécessaire\n    if (file.type.startsWith('image/') && file.size > 1024 * 1024) {\n      // > 1MB\n      console.log('🖼️ [Compression] Compressing image:', file.name, 'Original size:', file.size);\n      this.compressImage(file).then(compressedFile => {\n        console.log('🖼️ [Compression] ✅ Image compressed successfully. New size:', compressedFile.size);\n        this.sendFileToServer(compressedFile, receiverId);\n      }).catch(error => {\n        console.error('🖼️ [Compression] ❌ Error compressing image:', error);\n        // Envoyer le fichier original en cas d'erreur\n        this.sendFileToServer(file, receiverId);\n      });\n      return;\n    }\n    // Envoyer le fichier sans compression\n    this.sendFileToServer(file, receiverId);\n  }\n  sendFileToServer(file, receiverId) {\n    const messageType = this.getFileMessageType(file);\n    this.isSendingMessage = true;\n    this.isUploading = true;\n    this.uploadProgress = 0;\n    // Simuler la progression d'upload\n    const progressInterval = setInterval(() => {\n      this.uploadProgress += Math.random() * 15;\n      if (this.uploadProgress >= 90) {\n        clearInterval(progressInterval);\n      }\n      this.cdr.detectChanges();\n    }, 300);\n    this.MessageService.sendMessage(receiverId, '', file, messageType, this.conversation.id).subscribe({\n      next: message => {\n        console.log('📁 [Debug] Sent message structure:', {\n          id: message.id,\n          type: message.type,\n          attachments: message.attachments,\n          hasImage: this.hasImage(message),\n          hasFile: this.hasFile(message),\n          imageUrl: this.getImageUrl(message)\n        });\n        clearInterval(progressInterval);\n        this.uploadProgress = 100;\n        setTimeout(() => {\n          this.messages.push(message);\n          this.scrollToBottom();\n          this.toastService.showSuccess('Fichier envoyé avec succès');\n          this.resetUploadState();\n        }, 500);\n      },\n      error: error => {\n        console.error('📁 [Upload] ❌ Error sending file:', error);\n        clearInterval(progressInterval);\n        this.toastService.showError(\"Erreur lors de l'envoi du fichier\");\n        this.resetUploadState();\n      }\n    });\n  }\n  getFileMessageType(file) {\n    if (file.type.startsWith('image/')) return 'IMAGE';\n    if (file.type.startsWith('video/')) return 'VIDEO';\n    if (file.type.startsWith('audio/')) return 'AUDIO';\n    return 'FILE';\n  }\n  getFileAcceptTypes() {\n    return '*/*';\n  }\n  resetUploadState() {\n    this.isSendingMessage = false;\n    this.isUploading = false;\n    this.uploadProgress = 0;\n  }\n  // === DRAG & DROP ===\n  onDragOver(event) {\n    event.preventDefault();\n    event.stopPropagation();\n    this.isDragOver = true;\n  }\n  onDragLeave(event) {\n    event.preventDefault();\n    event.stopPropagation();\n    // Vérifier si on quitte vraiment la zone (pas un enfant)\n    const rect = event.currentTarget.getBoundingClientRect();\n    const x = event.clientX;\n    const y = event.clientY;\n    if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {\n      this.isDragOver = false;\n    }\n  }\n  onDrop(event) {\n    event.preventDefault();\n    event.stopPropagation();\n    this.isDragOver = false;\n    const files = event.dataTransfer?.files;\n    if (files && files.length > 0) {\n      // Traiter chaque fichier\n      Array.from(files).forEach(file => {\n        console.log('📁 [Drag&Drop] Processing file:', file.name, file.type, file.size);\n        this.uploadFile(file);\n      });\n      this.toastService.showSuccess(`${files.length} fichier(s) en cours d'envoi`);\n    }\n  }\n  // === COMPRESSION D'IMAGES ===\n  compressImage(file, quality = 0.8) {\n    return new Promise((resolve, reject) => {\n      const canvas = document.createElement('canvas');\n      const ctx = canvas.getContext('2d');\n      const img = new Image();\n      img.onload = () => {\n        // Calculer les nouvelles dimensions (max 1920x1080)\n        const maxWidth = 1920;\n        const maxHeight = 1080;\n        let {\n          width,\n          height\n        } = img;\n        if (width > maxWidth || height > maxHeight) {\n          const ratio = Math.min(maxWidth / width, maxHeight / height);\n          width *= ratio;\n          height *= ratio;\n        }\n        canvas.width = width;\n        canvas.height = height;\n        // Dessiner l'image redimensionnée\n        ctx?.drawImage(img, 0, 0, width, height);\n        // Convertir en blob avec compression\n        canvas.toBlob(blob => {\n          if (blob) {\n            const compressedFile = new File([blob], file.name, {\n              type: file.type,\n              lastModified: Date.now()\n            });\n            resolve(compressedFile);\n          } else {\n            reject(new Error('Failed to compress image'));\n          }\n        }, file.type, quality);\n      };\n      img.onerror = () => reject(new Error('Failed to load image'));\n      img.src = URL.createObjectURL(file);\n    });\n  }\n  // === MÉTHODES DE FORMATAGE SUPPLÉMENTAIRES ===\n  handleTypingIndicator() {\n    if (!this.isTyping) {\n      this.isTyping = true;\n      // Envoyer l'indicateur de frappe à l'autre utilisateur\n      this.sendTypingIndicator(true);\n    }\n    // Reset le timer\n    if (this.typingTimeout) {\n      clearTimeout(this.typingTimeout);\n    }\n    this.typingTimeout = setTimeout(() => {\n      this.isTyping = false;\n      // Arrêter l'indicateur de frappe\n      this.sendTypingIndicator(false);\n    }, 2000);\n  }\n  sendTypingIndicator(isTyping) {\n    // Envoyer l'indicateur de frappe via WebSocket/GraphQL\n    const receiverId = this.otherParticipant?.id || this.otherParticipant?._id;\n    if (receiverId && this.conversation?.id) {\n      console.log(`📝 Sending typing indicator: ${isTyping} to user ${receiverId}`);\n      // TODO: Implémenter l'envoi via WebSocket/GraphQL subscription\n      // this.MessageService.sendTypingIndicator(this.conversation.id, receiverId, isTyping);\n    }\n  }\n  // === MÉTHODES POUR L'INTERFACE D'APPEL ===\n  onCallAccepted(call) {\n    this.activeCall = call;\n    this.isInCall = true;\n    this.isCallConnected = true;\n    this.startCallTimer();\n    this.toastService.showSuccess('Appel accepté');\n  }\n  onCallRejected() {\n    this.endCall();\n    this.toastService.showInfo('Appel rejeté');\n  }\n  // === MÉTHODES POUR LA LECTURE DES MESSAGES VOCAUX ===\n  playVoiceMessage(message) {\n    this.toggleVoicePlayback(message);\n  }\n  isVoicePlaying(messageId) {\n    return this.playingMessageId === messageId;\n  }\n  toggleVoicePlayback(message) {\n    const messageId = message.id;\n    const audioUrl = this.getVoiceUrl(message);\n    if (!audioUrl) {\n      console.error('🎵 [Voice] No audio URL found for message:', messageId);\n      this.toastService.showError('Fichier audio introuvable');\n      return;\n    }\n    // Si c'est déjà en cours de lecture, arrêter\n    if (this.isVoicePlaying(messageId)) {\n      this.stopVoicePlayback();\n      return;\n    }\n    // Arrêter toute autre lecture en cours\n    this.stopVoicePlayback();\n    // Démarrer la nouvelle lecture\n    this.startVoicePlayback(message, audioUrl);\n  }\n  startVoicePlayback(message, audioUrl) {\n    const messageId = message.id;\n    try {\n      console.log('🎵 [Voice] Starting playback for:', messageId, 'URL:', audioUrl);\n      this.currentAudio = new Audio(audioUrl);\n      this.playingMessageId = messageId;\n      // Initialiser les valeurs par défaut avec la nouvelle structure\n      const currentData = this.getVoicePlaybackData(messageId);\n      this.setVoicePlaybackData(messageId, {\n        progress: 0,\n        currentTime: 0,\n        speed: currentData.speed || 1,\n        duration: currentData.duration || 0\n      });\n      // Configurer la vitesse de lecture\n      this.currentAudio.playbackRate = currentData.speed || 1;\n      // Événements audio\n      this.currentAudio.addEventListener('loadedmetadata', () => {\n        if (this.currentAudio) {\n          this.setVoicePlaybackData(messageId, {\n            duration: this.currentAudio.duration\n          });\n          console.log('🎵 [Voice] Audio loaded, duration:', this.currentAudio.duration);\n        }\n      });\n      this.currentAudio.addEventListener('timeupdate', () => {\n        if (this.currentAudio && this.playingMessageId === messageId) {\n          const currentTime = this.currentAudio.currentTime;\n          const progress = currentTime / this.currentAudio.duration * 100;\n          this.setVoicePlaybackData(messageId, {\n            currentTime,\n            progress\n          });\n          this.cdr.detectChanges();\n        }\n      });\n      this.currentAudio.addEventListener('ended', () => {\n        this.stopVoicePlayback();\n      });\n      this.currentAudio.addEventListener('error', error => {\n        console.error('🎵 [Voice] Audio error:', error);\n        this.toastService.showError('Erreur lors de la lecture audio');\n        this.stopVoicePlayback();\n      });\n      // Démarrer la lecture\n      this.currentAudio.play().then(() => {\n        this.toastService.showSuccess('🎵 Lecture du message vocal');\n      }).catch(error => {\n        console.error('🎵 [Voice] Error starting playback:', error);\n        this.toastService.showError('Impossible de lire le message vocal');\n        this.stopVoicePlayback();\n      });\n    } catch (error) {\n      console.error('🎵 [Voice] Error creating audio:', error);\n      this.toastService.showError('Erreur lors de la lecture audio');\n      this.stopVoicePlayback();\n    }\n  }\n  stopVoicePlayback() {\n    if (this.currentAudio) {\n      this.currentAudio.pause();\n      this.currentAudio.currentTime = 0;\n      this.currentAudio = null;\n    }\n    this.playingMessageId = null;\n    this.cdr.detectChanges();\n  }\n  getVoiceUrl(message) {\n    // Vérifier les propriétés directes d'audio\n    if (message.voiceUrl) return message.voiceUrl;\n    if (message.audioUrl) return message.audioUrl;\n    if (message.voice) return message.voice;\n    // Vérifier les attachments audio\n    const audioAttachment = message.attachments?.find(att => att.type?.startsWith('audio/') || att.type === 'AUDIO');\n    if (audioAttachment) {\n      return audioAttachment.url || audioAttachment.path || '';\n    }\n    return '';\n  }\n  getVoiceWaves(message) {\n    // Générer des waves basées sur l'ID du message pour la cohérence\n    const messageId = message.id || '';\n    const seed = messageId.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);\n    const waves = [];\n    for (let i = 0; i < 16; i++) {\n      const height = 4 + (seed + i * 7) % 20;\n      waves.push(height);\n    }\n    return waves;\n  }\n  getVoiceProgress(message) {\n    const data = this.getVoicePlaybackData(message.id);\n    const totalWaves = 16;\n    return Math.floor(data.progress / 100 * totalWaves);\n  }\n  getVoiceCurrentTime(message) {\n    const data = this.getVoicePlaybackData(message.id);\n    return this.formatAudioTime(data.currentTime);\n  }\n  getVoiceDuration(message) {\n    const data = this.getVoicePlaybackData(message.id);\n    const duration = data.duration || message.metadata?.duration || 0;\n    if (typeof duration === 'string') {\n      return duration; // Déjà formaté\n    }\n\n    return this.formatAudioTime(duration);\n  }\n  formatAudioTime(seconds) {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = Math.floor(seconds % 60);\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  }\n  seekVoiceMessage(message, waveIndex) {\n    const messageId = message.id;\n    if (!this.currentAudio || this.playingMessageId !== messageId) {\n      return;\n    }\n    const totalWaves = 16;\n    const seekPercentage = waveIndex / totalWaves * 100;\n    const seekTime = seekPercentage / 100 * this.currentAudio.duration;\n    this.currentAudio.currentTime = seekTime;\n  }\n  toggleVoiceSpeed(message) {\n    const messageId = message.id;\n    const data = this.getVoicePlaybackData(messageId);\n    // Cycle entre 1x, 1.5x, 2x\n    const newSpeed = data.speed === 1 ? 1.5 : data.speed === 1.5 ? 2 : 1;\n    this.setVoicePlaybackData(messageId, {\n      speed: newSpeed\n    });\n    if (this.currentAudio && this.playingMessageId === messageId) {\n      this.currentAudio.playbackRate = newSpeed;\n    }\n    this.toastService.showSuccess(`Vitesse: ${newSpeed}x`);\n  }\n  // === MÉTHODES MANQUANTES POUR LE TEMPLATE ===\n  changeVoiceSpeed(message) {\n    this.toggleVoiceSpeed(message);\n  }\n  getVoiceSpeed(message) {\n    const data = this.getVoicePlaybackData(message.id);\n    return data.speed || 1;\n  }\n  ngOnDestroy() {\n    this.subscriptions.unsubscribe();\n    // Nettoyer les timers\n    if (this.callTimer) {\n      clearInterval(this.callTimer);\n    }\n    if (this.recordingTimer) {\n      clearInterval(this.recordingTimer);\n    }\n    if (this.typingTimeout) {\n      clearTimeout(this.typingTimeout);\n    }\n    // Nettoyer les ressources audio\n    if (this.mediaRecorder) {\n      if (this.mediaRecorder.state === 'recording') {\n        this.mediaRecorder.stop();\n      }\n      this.mediaRecorder.stream?.getTracks().forEach(track => track.stop());\n    }\n    // Nettoyer la lecture audio\n    this.stopVoicePlayback();\n  }\n};\n__decorate([ViewChild('messagesContainer')], MessageChatComponent.prototype, \"messagesContainer\", void 0);\n__decorate([ViewChild('fileInput', {\n  static: false\n})], MessageChatComponent.prototype, \"fileInput\", void 0);\n__decorate([ViewChild('localVideo', {\n  static: false\n})], MessageChatComponent.prototype, \"localVideo\", void 0);\n__decorate([ViewChild('remoteVideo', {\n  static: false\n})], MessageChatComponent.prototype, \"remoteVideo\", void 0);\n__decorate([ViewChild('localVideoHidden', {\n  static: false\n})], MessageChatComponent.prototype, \"localVideoHidden\", void 0);\n__decorate([ViewChild('remoteVideoHidden', {\n  static: false\n})], MessageChatComponent.prototype, \"remoteVideoHidden\", void 0);\nMessageChatComponent = __decorate([Component({\n  selector: 'app-message-chat',\n  templateUrl: './message-chat.component.html'\n})], MessageChatComponent);", "map": {"version": 3, "names": ["Component", "ViewChild", "Validators", "Subscription", "CallType", "MessageChatComponent", "constructor", "fb", "route", "router", "MessageService", "callService", "toastService", "cdr", "conversation", "messages", "currentUserId", "currentUsername", "otherParticipant", "isLoading", "isLoadingMore", "hasMoreMessages", "showEmojiPicker", "showAttachmentMenu", "showSearch", "searchQuery", "searchResults", "searchMode", "isSendingMessage", "otherUserIsTyping", "showMainMenu", "showMessageContextMenu", "selectedMessage", "contextMenuPosition", "x", "y", "showReactionPicker", "reactionPickerMessage", "showImageViewer", "selectedImage", "uploadProgress", "isUploading", "isDragOver", "isRecordingVoice", "voiceRecordingDuration", "voiceRecordingState", "mediaRecorder", "audioChunks", "recordingTimer", "voiceWaves", "currentAudio", "playingMessageId", "voicePlayback", "isInCall", "callType", "callDuration", "callTimer", "activeCall", "isCallConnected", "isMuted", "isVideoEnabled", "localVideoElement", "remoteVideoElement", "emojiCategories", "id", "name", "icon", "emojis", "emoji", "selectedEmojiCategory", "MAX_MESSAGES_TO_LOAD", "currentPage", "isTyping", "isUserTyping", "typingTimeout", "subscriptions", "messageForm", "group", "content", "required", "<PERSON><PERSON><PERSON><PERSON>", "isInputDisabled", "updateInputState", "contentControl", "get", "disable", "enable", "ngOnInit", "initializeComponent", "enableSoundsOnFirstInteraction", "ngAfterViewInit", "setTimeout", "setupVideoElements", "localVideo", "remoteVideo", "setVideoElements", "nativeElement", "localVideoHidden", "remoteVideoHidden", "createVideoElementsManually", "localVideoEl", "document", "getElementById", "remoteVideoEl", "console", "log", "warn", "createElement", "autoplay", "muted", "playsInline", "style", "cssText", "body", "append<PERSON><PERSON><PERSON>", "enableSounds", "removeEventListener", "addEventListener", "once", "loadCurrentUser", "loadConversation", "setupCallSubscriptions", "add", "incomingCall$", "subscribe", "next", "incomingCall", "handleIncomingCall", "error", "activeCall$", "call", "caller", "username", "play", "userString", "localStorage", "getItem", "user", "JSON", "parse", "userId", "_id", "conversationId", "snapshot", "paramMap", "showError", "cleanupSubscriptions", "getConversation", "setOtherParticipant", "loadMessages", "setupSubscriptions", "participants", "length", "isGroup", "find", "p", "participantId", "String", "firstParticipantId", "sort", "a", "b", "dateA", "Date", "timestamp", "createdAt", "getTime", "dateB", "total", "first", "last", "scrollToBottom", "loadMoreMessages", "offset", "getMessages", "newMessages", "reverse", "unsubscribe", "reloadConversation", "subscribeToNewMessages", "newMessage", "type", "sender", "senderId", "receiverId", "attachments", "getMessageType", "for<PERSON>ach", "att", "index", "url", "path", "size", "messageExists", "some", "msg", "push", "detectChanges", "shouldMarkAsRead", "markMessageAsRead", "subscribeToTypingIndicator", "typingData", "subscribeToConversationUpdates", "conversationUpdate", "messageId", "sendMessage", "valid", "value", "trim", "undefined", "message", "reset", "messagesContainer", "element", "scrollTop", "scrollHeight", "formatLastActive", "lastActive", "diffMins", "Math", "floor", "now", "getVoicePlaybackData", "progress", "duration", "currentTime", "speed", "setVoicePlaybackData", "data", "startVideoCall", "initiateCall", "VIDEO", "startVoiceCall", "AUDIO", "toggleMicrophone", "toggleAudio", "showSuccess", "toggleCamera", "toggleVideo", "endCall", "formatFileSize", "bytes", "round", "downloadFile", "fileAttachment", "startsWith", "link", "href", "download", "target", "click", "<PERSON><PERSON><PERSON><PERSON>", "toggleSearch", "toggleMainMenu", "goBackToConversations", "navigate", "then", "catch", "window", "location", "closeAllMenus", "onMessageContextMenu", "event", "preventDefault", "clientX", "clientY", "showQuickReactions", "stopPropagation", "quickReact", "toggleReaction", "reactToMessage", "result", "messageIndex", "findIndex", "hasUserReacted", "reaction", "replyToMessage", "forwardMessage", "deleteMessage", "canDelete", "confirm", "filter", "toggleEmojiPicker", "selectEmojiCategory", "category", "getEmojisForCategory", "insert<PERSON><PERSON><PERSON>", "currentC<PERSON>nt", "newContent", "patchValue", "toggleAttachmentMenu", "trackByMessageId", "toString", "testAddMessage", "testMessage", "toLocaleTimeString", "toISOString", "image", "isRead", "isGroupConversation", "openCamera", "zoomImage", "factor", "imageElement", "querySelector", "currentTransform", "transform", "currentScale", "parseFloat", "match", "newScale", "max", "min", "classList", "remove", "resetZoom", "triggerFileInput", "input", "fileInput", "accept", "formatMessageTime", "date", "hour", "minute", "formatDateSeparator", "today", "yesterday", "setDate", "getDate", "toDateString", "toLocaleDateString", "formatMessageContent", "urlRegex", "replace", "shouldShowDateSeparator", "currentMessage", "previousMessage", "currentDate", "previousDate", "shouldShowAvatar", "nextMessage", "shouldShowSenderName", "attachment", "voiceUrl", "audioUrl", "voice", "hasImage", "hasImageAttachment", "hasImageUrl", "imageUrl", "hasFile", "hasFileAttachment", "getImageUrl", "imageAttachment", "getFileName", "getFileSize", "getFileIcon", "includes", "getUserColor", "colors", "charCodeAt", "onMessageClick", "onInputChange", "handleTypingIndicator", "onInputKeyDown", "key", "shift<PERSON>ey", "onInputFocus", "onInputBlur", "onScroll", "openUserProfile", "onImageLoad", "src", "onImageError", "openImageViewer", "closeImageViewer", "downloadImage", "searchMessages", "toLowerCase", "onSearchQueryChange", "clearSearch", "jumpToMessage", "messageElement", "scrollIntoView", "behavior", "block", "closeContextMenu", "recipientId", "<PERSON><PERSON><PERSON>", "startCallTimer", "acceptCall", "rejectCall", "setInterval", "resetCallState", "clearInterval", "toggleMute", "toggleMedia", "formatCallDuration", "hours", "minutes", "seconds", "padStart", "startVoiceRecording", "_this", "_asyncToGenerator", "navigator", "mediaDevices", "getUserMedia", "Error", "MediaRecorder", "stream", "audio", "echoCancellation", "noiseSuppression", "autoGainControl", "sampleRate", "channelCount", "mimeType", "isTypeSupported", "animateVoice<PERSON>aves", "ondataavailable", "onstop", "processRecordedAudio", "onerror", "cancelVoiceRecording", "start", "errorMessage", "stopVoiceRecording", "state", "stop", "getTracks", "track", "_this2", "audioBlob", "Blob", "extension", "audioFile", "File", "sendVoiceMessage", "_this3", "Promise", "resolve", "reject", "formatRecordingDuration", "onRecordStart", "showWarning", "showInfo", "onRecordEnd", "onRecordCancel", "getRecordingFormat", "map", "random", "onFileSelected", "files", "file", "uploadFile", "maxSize", "compressImage", "compressedFile", "sendFileToServer", "messageType", "getFileMessageType", "progressInterval", "resetUploadState", "getFileAcceptTypes", "onDragOver", "onDragLeave", "rect", "currentTarget", "getBoundingClientRect", "left", "right", "top", "bottom", "onDrop", "dataTransfer", "Array", "from", "quality", "canvas", "ctx", "getContext", "img", "Image", "onload", "max<PERSON><PERSON><PERSON>", "maxHeight", "width", "height", "ratio", "drawImage", "toBlob", "blob", "lastModified", "URL", "createObjectURL", "sendTypingIndicator", "clearTimeout", "onCallAccepted", "onCallRejected", "playVoiceMessage", "toggleVoicePlayback", "isVoicePlaying", "getVoiceUrl", "stopVoicePlayback", "startVoicePlayback", "Audio", "currentData", "playbackRate", "pause", "audioAttachment", "getVoiceWaves", "seed", "split", "reduce", "acc", "char", "waves", "i", "getVoiceProgress", "totalWaves", "getVoiceCurrentTime", "formatAudioTime", "getVoiceDuration", "metadata", "remainingSeconds", "seekVoiceMessage", "waveIndex", "seekPercentage", "seekTime", "toggleVoiceSpeed", "newSpeed", "changeVoiceSpeed", "getVoiceSpeed", "ngOnDestroy", "__decorate", "static", "selector", "templateUrl"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\messages\\message-chat\\message-chat.component.ts"], "sourcesContent": ["import {\r\n  Compo<PERSON>,\r\n  On<PERSON><PERSON><PERSON>,\r\n  <PERSON><PERSON><PERSON><PERSON>,\r\n  AfterViewInit,\r\n  ViewChild,\r\n  ElementRef,\r\n  ChangeDetectorRef,\r\n} from '@angular/core';\r\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { Subscription } from 'rxjs';\r\nimport { MessageService } from '../../../../services/message.service';\r\nimport { CallService } from '../../../../services/call.service';\r\nimport { ToastService } from '../../../../services/toast.service';\r\nimport { CallType, Call, IncomingCall } from '../../../../models/message.model';\r\n\r\n@Component({\r\n  selector: 'app-message-chat',\r\n  templateUrl: './message-chat.component.html',\r\n})\r\nexport class MessageChatComponent implements OnInit, AfterViewInit, OnDestroy {\r\n  // === RÉFÉRENCES DOM ===\r\n  @ViewChild('messagesContainer') private messagesContainer!: ElementRef;\r\n  @ViewChild('fileInput', { static: false })\r\n  fileInput!: ElementRef<HTMLInputElement>;\r\n  @ViewChild('localVideo', { static: false })\r\n  localVideo!: ElementRef<HTMLVideoElement>;\r\n  @ViewChild('remoteVideo', { static: false })\r\n  remoteVideo!: ElementRef<HTMLVideoElement>;\r\n  @ViewChild('localVideoHidden', { static: false })\r\n  localVideoHidden!: ElementRef<HTMLVideoElement>;\r\n  @ViewChild('remoteVideoHidden', { static: false })\r\n  remoteVideoHidden!: ElementRef<HTMLVideoElement>;\r\n\r\n  // === DONNÉES PRINCIPALES ===\r\n  conversation: any = null;\r\n  messages: any[] = [];\r\n  currentUserId: string | null = null;\r\n  currentUsername = 'You';\r\n  messageForm: FormGroup;\r\n  otherParticipant: any = null;\r\n\r\n  // === ÉTATS DE L'INTERFACE ===\r\n  isLoading = false;\r\n  isLoadingMore = false;\r\n  hasMoreMessages = true;\r\n  showEmojiPicker = false;\r\n  showAttachmentMenu = false;\r\n  showSearch = false;\r\n  searchQuery = '';\r\n  searchResults: any[] = [];\r\n  searchMode = false;\r\n  isSendingMessage = false;\r\n  otherUserIsTyping = false;\r\n  showMainMenu = false;\r\n  showMessageContextMenu = false;\r\n  selectedMessage: any = null;\r\n  contextMenuPosition = { x: 0, y: 0 };\r\n  showReactionPicker = false;\r\n  reactionPickerMessage: any = null;\r\n\r\n  showImageViewer = false;\r\n  selectedImage: any = null;\r\n  uploadProgress = 0;\r\n  isUploading = false;\r\n  isDragOver = false;\r\n\r\n  // === GESTION VOCALE OPTIMISÉE ===\r\n  isRecordingVoice = false;\r\n  voiceRecordingDuration = 0;\r\n  voiceRecordingState: 'idle' | 'recording' | 'processing' = 'idle';\r\n  private mediaRecorder: MediaRecorder | null = null;\r\n  private audioChunks: Blob[] = [];\r\n  private recordingTimer: any = null;\r\n  voiceWaves: number[] = [\r\n    4, 8, 12, 16, 20, 16, 12, 8, 4, 8, 12, 16, 20, 16, 12, 8,\r\n  ];\r\n\r\n  // Lecture des messages vocaux\r\n  private currentAudio: HTMLAudioElement | null = null;\r\n  private playingMessageId: string | null = null;\r\n  private voicePlayback: {\r\n    [messageId: string]: {\r\n      progress: number;\r\n      duration: number;\r\n      currentTime: number;\r\n      speed: number;\r\n    };\r\n  } = {};\r\n\r\n  // === APPELS WEBRTC ===\r\n  isInCall = false;\r\n  callType: 'VIDEO' | 'AUDIO' | null = null;\r\n  callDuration = 0;\r\n  private callTimer: any = null;\r\n\r\n  // État de l'appel WebRTC\r\n  activeCall: any = null;\r\n  isCallConnected = false;\r\n  isMuted = false;\r\n  isVideoEnabled = true;\r\n  localVideoElement: HTMLVideoElement | null = null;\r\n  remoteVideoElement: HTMLVideoElement | null = null;\r\n\r\n  // === ÉMOJIS ===\r\n  emojiCategories: any[] = [\r\n    {\r\n      id: 'smileys',\r\n      name: 'Smileys',\r\n      icon: '😀',\r\n      emojis: [\r\n        { emoji: '😀', name: 'grinning face' },\r\n        { emoji: '😃', name: 'grinning face with big eyes' },\r\n        { emoji: '😄', name: 'grinning face with smiling eyes' },\r\n        { emoji: '😁', name: 'beaming face with smiling eyes' },\r\n        { emoji: '😆', name: 'grinning squinting face' },\r\n        { emoji: '😅', name: 'grinning face with sweat' },\r\n        { emoji: '😂', name: 'face with tears of joy' },\r\n        { emoji: '🤣', name: 'rolling on the floor laughing' },\r\n        { emoji: '😊', name: 'smiling face with smiling eyes' },\r\n        { emoji: '😇', name: 'smiling face with halo' },\r\n      ],\r\n    },\r\n    {\r\n      id: 'people',\r\n      name: 'People',\r\n      icon: '👤',\r\n      emojis: [\r\n        { emoji: '👶', name: 'baby' },\r\n        { emoji: '🧒', name: 'child' },\r\n        { emoji: '👦', name: 'boy' },\r\n        { emoji: '👧', name: 'girl' },\r\n        { emoji: '🧑', name: 'person' },\r\n        { emoji: '👨', name: 'man' },\r\n        { emoji: '👩', name: 'woman' },\r\n        { emoji: '👴', name: 'old man' },\r\n        { emoji: '👵', name: 'old woman' },\r\n      ],\r\n    },\r\n    {\r\n      id: 'nature',\r\n      name: 'Nature',\r\n      icon: '🌿',\r\n      emojis: [\r\n        { emoji: '🐶', name: 'dog face' },\r\n        { emoji: '🐱', name: 'cat face' },\r\n        { emoji: '🐭', name: 'mouse face' },\r\n        { emoji: '🐹', name: 'hamster' },\r\n        { emoji: '🐰', name: 'rabbit face' },\r\n        { emoji: '🦊', name: 'fox' },\r\n        { emoji: '🐻', name: 'bear' },\r\n        { emoji: '🐼', name: 'panda' },\r\n      ],\r\n    },\r\n  ];\r\n  selectedEmojiCategory = this.emojiCategories[0];\r\n\r\n  // === PAGINATION ===\r\n  private readonly MAX_MESSAGES_TO_LOAD = 10;\r\n  private currentPage = 1;\r\n\r\n  // === AUTRES ÉTATS ===\r\n  isTyping = false;\r\n  isUserTyping = false;\r\n  private typingTimeout: any = null;\r\n  private subscriptions = new Subscription();\r\n\r\n  constructor(\r\n    private fb: FormBuilder,\r\n    private route: ActivatedRoute,\r\n    private router: Router,\r\n    private MessageService: MessageService,\r\n    private callService: CallService,\r\n    private toastService: ToastService,\r\n    private cdr: ChangeDetectorRef\r\n  ) {\r\n    this.messageForm = this.fb.group({\r\n      content: ['', [Validators.required, Validators.minLength(1)]],\r\n    });\r\n  }\r\n\r\n  // Méthode pour vérifier si le champ de saisie doit être désactivé\r\n  isInputDisabled(): boolean {\r\n    return (\r\n      !this.otherParticipant || this.isRecordingVoice || this.isSendingMessage\r\n    );\r\n  }\r\n\r\n  // Méthode pour gérer l'état du contrôle de saisie\r\n  private updateInputState(): void {\r\n    const contentControl = this.messageForm.get('content');\r\n    if (this.isInputDisabled()) {\r\n      contentControl?.disable();\r\n    } else {\r\n      contentControl?.enable();\r\n    }\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.initializeComponent();\r\n\r\n    // Activer les sons après interaction utilisateur\r\n    this.enableSoundsOnFirstInteraction();\r\n  }\r\n\r\n  ngAfterViewInit(): void {\r\n    // Configurer les éléments vidéo pour WebRTC après que la vue soit initialisée\r\n    setTimeout(() => {\r\n      this.setupVideoElements();\r\n    }, 100);\r\n\r\n    // Réessayer plusieurs fois pour s'assurer que les éléments sont configurés\r\n    setTimeout(() => {\r\n      this.setupVideoElements();\r\n    }, 500);\r\n\r\n    setTimeout(() => {\r\n      this.setupVideoElements();\r\n    }, 1000);\r\n\r\n    setTimeout(() => {\r\n      this.setupVideoElements();\r\n    }, 2000);\r\n  }\r\n\r\n  /**\r\n   * Configure les éléments vidéo pour WebRTC\r\n   */\r\n  private setupVideoElements(): void {\r\n    // Essayer d'abord les éléments visibles (pour appels vidéo)\r\n    if (this.localVideo && this.remoteVideo) {\r\n      this.callService.setVideoElements(\r\n        this.localVideo.nativeElement,\r\n        this.remoteVideo.nativeElement\r\n      );\r\n    }\r\n    // Sinon utiliser les éléments cachés (pour appels audio)\r\n    else if (this.localVideoHidden && this.remoteVideoHidden) {\r\n      this.callService.setVideoElements(\r\n        this.localVideoHidden.nativeElement,\r\n        this.remoteVideoHidden.nativeElement\r\n      );\r\n    } else {\r\n      this.createVideoElementsManually();\r\n\r\n      // Réessayer après un délai\r\n      setTimeout(() => {\r\n        this.setupVideoElements();\r\n      }, 500);\r\n\r\n      // Réessayer encore plus tard\r\n      setTimeout(() => {\r\n        this.setupVideoElements();\r\n      }, 1500);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Crée les éléments vidéo manuellement si les ViewChild ne fonctionnent pas\r\n   */\r\n  private createVideoElementsManually(): void {\r\n    // Chercher les éléments dans le DOM\r\n    const localVideoEl = document.getElementById(\r\n      'localVideo'\r\n    ) as HTMLVideoElement;\r\n    const remoteVideoEl = document.getElementById(\r\n      'remoteVideo'\r\n    ) as HTMLVideoElement;\r\n\r\n    if (localVideoEl && remoteVideoEl) {\r\n      console.log(\r\n        '✅ [MessageChat] Found video elements in DOM, configuring...'\r\n      );\r\n      this.callService.setVideoElements(localVideoEl, remoteVideoEl);\r\n    } else {\r\n      console.warn('⚠️ [MessageChat] Video elements not found in DOM either');\r\n\r\n      // Créer les éléments dynamiquement\r\n      const localVideo = document.createElement('video');\r\n      localVideo.id = 'localVideo';\r\n      localVideo.autoplay = true;\r\n      localVideo.muted = true;\r\n      localVideo.playsInline = true;\r\n      localVideo.style.cssText =\r\n        'position: absolute; top: -9999px; left: -9999px; width: 1px; height: 1px;';\r\n\r\n      const remoteVideo = document.createElement('video');\r\n      remoteVideo.id = 'remoteVideo';\r\n      remoteVideo.autoplay = true;\r\n      remoteVideo.playsInline = true;\r\n      remoteVideo.style.cssText =\r\n        'position: absolute; top: -9999px; left: -9999px; width: 1px; height: 1px;';\r\n\r\n      document.body.appendChild(localVideo);\r\n      document.body.appendChild(remoteVideo);\r\n\r\n      this.callService.setVideoElements(localVideo, remoteVideo);\r\n    }\r\n  }\r\n\r\n  private enableSoundsOnFirstInteraction(): void {\r\n    const enableSounds = () => {\r\n      this.callService.enableSounds();\r\n      document.removeEventListener('click', enableSounds);\r\n      document.removeEventListener('keydown', enableSounds);\r\n      document.removeEventListener('touchstart', enableSounds);\r\n    };\r\n\r\n    document.addEventListener('click', enableSounds, { once: true });\r\n    document.addEventListener('keydown', enableSounds, { once: true });\r\n    document.addEventListener('touchstart', enableSounds, { once: true });\r\n  }\r\n\r\n  private initializeComponent(): void {\r\n    this.loadCurrentUser();\r\n    this.loadConversation();\r\n    this.setupCallSubscriptions();\r\n  }\r\n\r\n  private setupCallSubscriptions(): void {\r\n    // S'abonner aux appels entrants\r\n    this.subscriptions.add(\r\n      this.callService.incomingCall$.subscribe({\r\n        next: (incomingCall: any) => {\r\n          if (incomingCall) {\r\n            this.handleIncomingCall(incomingCall);\r\n          }\r\n        },\r\n        error: (error: any) => {\r\n          console.error('❌ Error in incoming call subscription:', error);\r\n        },\r\n      })\r\n    );\r\n\r\n    // S'abonner aux changements d'état d'appel\r\n    this.subscriptions.add(\r\n      this.callService.activeCall$.subscribe({\r\n        next: (call: any) => {\r\n          if (call) {\r\n            this.activeCall = call;\r\n          }\r\n        },\r\n        error: (error: any) => {\r\n          console.error('❌ Error in active call subscription:', error);\r\n        },\r\n      })\r\n    );\r\n  }\r\n\r\n  private handleIncomingCall(incomingCall: IncomingCall): void {\r\n    // Afficher une notification ou modal d'appel entrant\r\n    // Pour l'instant, on log juste\r\n    console.log(\r\n      '🔔 Handling incoming call from:',\r\n      incomingCall.caller.username\r\n    );\r\n\r\n    // Jouer la sonnerie\r\n    this.MessageService.play('ringtone');\r\n\r\n    // Ici on pourrait afficher une modal ou notification\r\n    // Pour l'instant, on accepte automatiquement pour tester\r\n    // this.acceptCall(incomingCall);\r\n  }\r\n\r\n  private loadCurrentUser(): void {\r\n    try {\r\n      const userString = localStorage.getItem('user');\r\n\r\n      if (!userString || userString === 'null' || userString === 'undefined') {\r\n        console.error('❌ No user data in localStorage');\r\n        this.currentUserId = null;\r\n        this.currentUsername = 'You';\r\n        return;\r\n      }\r\n\r\n      const user = JSON.parse(userString);\r\n\r\n      // Essayer différentes propriétés pour l'ID utilisateur\r\n      const userId = user._id || user.id || user.userId;\r\n\r\n      if (userId) {\r\n        this.currentUserId = userId;\r\n        this.currentUsername = user.username || user.name || 'You';\r\n      } else {\r\n        console.error('❌ No valid user ID found in user object:', user);\r\n        this.currentUserId = null;\r\n        this.currentUsername = 'You';\r\n      }\r\n    } catch (error) {\r\n      console.error('❌ Error parsing user from localStorage:', error);\r\n      this.currentUserId = null;\r\n      this.currentUsername = 'You';\r\n    }\r\n  }\r\n\r\n  private loadConversation(): void {\r\n    const conversationId = this.route.snapshot.paramMap.get('id');\r\n\r\n    if (!conversationId) {\r\n      this.toastService.showError('ID de conversation manquant');\r\n      return;\r\n    }\r\n\r\n    this.isLoading = true;\r\n\r\n    // Nettoyer les subscriptions existantes avant de recharger\r\n    this.cleanupSubscriptions();\r\n\r\n    this.MessageService.getConversation(conversationId).subscribe({\r\n      next: (conversation) => {\r\n        this.conversation = conversation;\r\n        this.setOtherParticipant();\r\n        this.loadMessages();\r\n\r\n        // Configurer les subscriptions temps réel après le chargement de la conversation\r\n        this.setupSubscriptions();\r\n        this.isLoading = false;\r\n      },\r\n      error: (error) => {\r\n        console.error('Erreur lors du chargement de la conversation:', error);\r\n        this.toastService.showError(\r\n          'Erreur lors du chargement de la conversation'\r\n        );\r\n        this.isLoading = false;\r\n\r\n        // Réessayer après 2 secondes en cas d'erreur\r\n        setTimeout(() => {\r\n          this.loadConversation();\r\n        }, 2000);\r\n      },\r\n    });\r\n  }\r\n\r\n  private setOtherParticipant(): void {\r\n    if (\r\n      !this.conversation?.participants ||\r\n      this.conversation.participants.length === 0\r\n    ) {\r\n      console.warn('No participants found in conversation');\r\n      this.otherParticipant = null;\r\n      return;\r\n    }\r\n\r\n    // Dans une conversation 1-à-1, on veut afficher l'autre personne (pas l'utilisateur actuel)\r\n    // Dans une conversation de groupe, on peut afficher le nom du groupe ou le premier autre participant\r\n\r\n    if (this.conversation.isGroup) {\r\n      // Pour les groupes, on pourrait afficher le nom du groupe\r\n      // Mais pour l'instant, on prend le premier participant qui n'est pas l'utilisateur actuel\r\n      this.otherParticipant = this.conversation.participants.find((p: any) => {\r\n        const participantId = p.id || p._id;\r\n        return String(participantId) !== String(this.currentUserId);\r\n      });\r\n    } else {\r\n      // Pour les conversations 1-à-1, on prend l'autre participant\r\n      this.otherParticipant = this.conversation.participants.find((p: any) => {\r\n        const participantId = p.id || p._id;\r\n        console.log(\r\n          'Comparing participant ID:',\r\n          participantId,\r\n          'with current user ID:',\r\n          this.currentUserId\r\n        );\r\n        return String(participantId) !== String(this.currentUserId);\r\n      });\r\n    }\r\n\r\n    // Fallback si aucun autre participant n'est trouvé\r\n    if (!this.otherParticipant && this.conversation.participants.length > 0) {\r\n      this.otherParticipant = this.conversation.participants[0];\r\n\r\n      // Si le premier participant est l'utilisateur actuel et qu'il y en a d'autres\r\n      if (this.conversation.participants.length > 1) {\r\n        const firstParticipantId =\r\n          this.otherParticipant.id || this.otherParticipant._id;\r\n        if (String(firstParticipantId) === String(this.currentUserId)) {\r\n          console.log(\r\n            'First participant is current user, using second participant'\r\n          );\r\n          this.otherParticipant = this.conversation.participants[1];\r\n        }\r\n      }\r\n    }\r\n\r\n    // Vérification finale et logs\r\n    if (this.otherParticipant) {\r\n      // Log très visible pour debug\r\n      console.log(\r\n        '🎯 FINAL RESULT: otherParticipant =',\r\n        this.otherParticipant.username\r\n      );\r\n      console.log(\r\n        '🎯 Should display in sidebar:',\r\n        this.otherParticipant.username\r\n      );\r\n    } else {\r\n      console.error('❌ No other participant found! This should not happen.');\r\n\r\n      // Log très visible pour debug\r\n    }\r\n\r\n    // Mettre à jour l'état du champ de saisie\r\n    this.updateInputState();\r\n  }\r\n\r\n  private loadMessages(): void {\r\n    if (!this.conversation?.id) return;\r\n\r\n    // Les messages sont déjà chargés avec la conversation\r\n    let messages = this.conversation.messages || [];\r\n\r\n    // Trier les messages par timestamp (plus anciens en premier)\r\n    this.messages = messages.sort((a: any, b: any) => {\r\n      const dateA = new Date(a.timestamp || a.createdAt).getTime();\r\n      const dateB = new Date(b.timestamp || b.createdAt).getTime();\r\n      return dateA - dateB; // Ordre croissant (plus anciens en premier)\r\n    });\r\n\r\n    console.log('📋 Messages loaded and sorted:', {\r\n      total: this.messages.length,\r\n      first: this.messages[0]?.content,\r\n      last: this.messages[this.messages.length - 1]?.content,\r\n    });\r\n\r\n    this.hasMoreMessages = this.messages.length === this.MAX_MESSAGES_TO_LOAD;\r\n    this.isLoading = false;\r\n    this.scrollToBottom();\r\n  }\r\n\r\n  loadMoreMessages(): void {\r\n    if (this.isLoadingMore || !this.hasMoreMessages || !this.conversation?.id)\r\n      return;\r\n\r\n    this.isLoadingMore = true;\r\n    this.currentPage++;\r\n\r\n    // Calculer l'offset basé sur les messages déjà chargés\r\n    const offset = this.messages.length;\r\n\r\n    this.MessageService.getMessages(\r\n      this.currentUserId!, // senderId\r\n      this.otherParticipant?.id || this.otherParticipant?._id!, // receiverId\r\n      this.conversation.id,\r\n      this.currentPage,\r\n      this.MAX_MESSAGES_TO_LOAD\r\n    ).subscribe({\r\n      next: (newMessages: any[]) => {\r\n        if (newMessages && newMessages.length > 0) {\r\n          // Ajouter les nouveaux messages au début de la liste\r\n          this.messages = [...newMessages.reverse(), ...this.messages];\r\n          this.hasMoreMessages =\r\n            newMessages.length === this.MAX_MESSAGES_TO_LOAD;\r\n        } else {\r\n          this.hasMoreMessages = false;\r\n        }\r\n        this.isLoadingMore = false;\r\n      },\r\n      error: (error) => {\r\n        console.error('Erreur lors du chargement des messages:', error);\r\n        this.toastService.showError('Erreur lors du chargement des messages');\r\n        this.isLoadingMore = false;\r\n        this.currentPage--; // Revenir à la page précédente en cas d'erreur\r\n      },\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Nettoie les subscriptions existantes\r\n   */\r\n  private cleanupSubscriptions(): void {\r\n    this.subscriptions.unsubscribe();\r\n    this.subscriptions = new Subscription();\r\n  }\r\n\r\n  /**\r\n   * Recharge la conversation actuelle\r\n   */\r\n  public reloadConversation(): void {\r\n    if (this.conversation?.id) {\r\n      // Réinitialiser l'état\r\n      this.messages = [];\r\n      this.currentPage = 1;\r\n      this.hasMoreMessages = true;\r\n\r\n      // Recharger\r\n      this.loadConversation();\r\n    }\r\n  }\r\n\r\n  private setupSubscriptions(): void {\r\n    if (!this.conversation?.id) {\r\n      console.warn('❌ Cannot setup subscriptions: no conversation ID');\r\n      return;\r\n    }\r\n\r\n    console.log(\r\n      '🔄 Setting up real-time subscriptions for conversation:',\r\n      this.conversation.id\r\n    );\r\n\r\n    // Subscription pour les nouveaux messages\r\n\r\n    this.subscriptions.add(\r\n      this.MessageService.subscribeToNewMessages(\r\n        this.conversation.id\r\n      ).subscribe({\r\n        next: (newMessage: any) => {\r\n          console.log('📨 Message structure:', {\r\n            id: newMessage.id,\r\n            type: newMessage.type,\r\n            content: newMessage.content,\r\n            sender: newMessage.sender,\r\n            senderId: newMessage.senderId,\r\n            receiverId: newMessage.receiverId,\r\n            attachments: newMessage.attachments,\r\n          });\r\n\r\n          // Debug des attachments\r\n          console.log(\r\n            '📨 [Debug] Message type detected:',\r\n            this.getMessageType(newMessage)\r\n          );\r\n\r\n          if (newMessage.attachments) {\r\n            newMessage.attachments.forEach((att: any, index: number) => {\r\n              console.log(`📨 [Debug] Attachment ${index}:`, {\r\n                type: att.type,\r\n                url: att.url,\r\n                path: att.path,\r\n                name: att.name,\r\n                size: att.size,\r\n              });\r\n            });\r\n          }\r\n\r\n          // Ajouter le message à la liste s'il n'existe pas déjà\r\n          const messageExists = this.messages.some(\r\n            (msg) => msg.id === newMessage.id\r\n          );\r\n          if (!messageExists) {\r\n            // Ajouter le nouveau message à la fin (en bas)\r\n            this.messages.push(newMessage);\r\n            console.log(\r\n              '✅ Message added to list, total messages:',\r\n              this.messages.length\r\n            );\r\n\r\n            // Forcer la détection de changements\r\n            this.cdr.detectChanges();\r\n\r\n            // Scroll vers le bas après un court délai\r\n            setTimeout(() => {\r\n              this.scrollToBottom();\r\n            }, 50);\r\n\r\n            // Marquer comme lu si ce n'est pas notre message\r\n            const senderId = newMessage.sender?.id || newMessage.senderId;\r\n            console.log('📨 Checking if message should be marked as read:', {\r\n              senderId,\r\n              currentUserId: this.currentUserId,\r\n              shouldMarkAsRead: senderId !== this.currentUserId,\r\n            });\r\n\r\n            if (senderId && senderId !== this.currentUserId) {\r\n              this.markMessageAsRead(newMessage.id);\r\n            }\r\n          }\r\n        },\r\n        error: (error) => {\r\n          console.error('❌ Error in message subscription:', error);\r\n          this.toastService.showError('Connexion temps réel interrompue');\r\n\r\n          // Réessayer la connexion après 5 secondes\r\n          setTimeout(() => {\r\n            this.setupSubscriptions();\r\n          }, 5000);\r\n        },\r\n      })\r\n    );\r\n\r\n    // Subscription pour les indicateurs de frappe\r\n\r\n    this.subscriptions.add(\r\n      this.MessageService.subscribeToTypingIndicator(\r\n        this.conversation.id\r\n      ).subscribe({\r\n        next: (typingData: any) => {\r\n          // Afficher l'indicateur seulement si c'est l'autre utilisateur qui tape\r\n          if (typingData.userId !== this.currentUserId) {\r\n            this.otherUserIsTyping = typingData.isTyping;\r\n            this.isUserTyping = typingData.isTyping; // Pour compatibilité avec le template\r\n            console.log(\r\n              '📝 Other user typing status updated:',\r\n              this.otherUserIsTyping\r\n            );\r\n            this.cdr.detectChanges();\r\n          }\r\n        },\r\n        error: (error: any) => {\r\n          console.error('❌ Error in typing subscription:', error);\r\n        },\r\n      })\r\n    );\r\n\r\n    // Subscription pour les mises à jour de conversation\r\n    this.subscriptions.add(\r\n      this.MessageService.subscribeToConversationUpdates(\r\n        this.conversation.id\r\n      ).subscribe({\r\n        next: (conversationUpdate: any) => {\r\n          // Mettre à jour la conversation si nécessaire\r\n          if (conversationUpdate.id === this.conversation.id) {\r\n            this.conversation = { ...this.conversation, ...conversationUpdate };\r\n            this.cdr.detectChanges();\r\n          }\r\n        },\r\n        error: (error: any) => {\r\n          console.error('❌ Error in conversation subscription:', error);\r\n        },\r\n      })\r\n    );\r\n  }\r\n\r\n  private markMessageAsRead(messageId: string): void {\r\n    this.MessageService.markMessageAsRead(messageId).subscribe({\r\n      next: () => {},\r\n      error: (error) => {\r\n        console.error('❌ Error marking message as read:', error);\r\n      },\r\n    });\r\n  }\r\n\r\n  // === ENVOI DE MESSAGES ===\r\n  sendMessage(): void {\r\n    if (!this.messageForm.valid || !this.conversation?.id) return;\r\n\r\n    const content = this.messageForm.get('content')?.value?.trim();\r\n    if (!content) return;\r\n\r\n    const receiverId = this.otherParticipant?.id || this.otherParticipant?._id;\r\n\r\n    if (!receiverId) {\r\n      this.toastService.showError('Destinataire introuvable');\r\n      return;\r\n    }\r\n\r\n    // Désactiver le bouton d'envoi\r\n    this.isSendingMessage = true;\r\n    this.updateInputState();\r\n\r\n    console.log('📤 Sending message:', {\r\n      content,\r\n      receiverId,\r\n      conversationId: this.conversation.id,\r\n    });\r\n\r\n    this.MessageService.sendMessage(\r\n      receiverId,\r\n      content,\r\n      undefined,\r\n      'TEXT' as any,\r\n      this.conversation.id\r\n    ).subscribe({\r\n      next: (message: any) => {\r\n        // Ajouter le message à la liste s'il n'y est pas déjà\r\n        const messageExists = this.messages.some(\r\n          (msg) => msg.id === message.id\r\n        );\r\n        if (!messageExists) {\r\n          this.messages.push(message);\r\n          console.log(\r\n            '📋 Message added to local list, total:',\r\n            this.messages.length\r\n          );\r\n        }\r\n\r\n        // Réinitialiser le formulaire\r\n        this.messageForm.reset();\r\n        this.isSendingMessage = false;\r\n        this.updateInputState();\r\n\r\n        // Forcer la détection de changements et scroll\r\n        this.cdr.detectChanges();\r\n        setTimeout(() => {\r\n          this.scrollToBottom();\r\n        }, 50);\r\n      },\r\n      error: (error: any) => {\r\n        console.error(\"❌ Erreur lors de l'envoi du message:\", error);\r\n        this.toastService.showError(\"Erreur lors de l'envoi du message\");\r\n        this.isSendingMessage = false;\r\n        this.updateInputState();\r\n      },\r\n    });\r\n  }\r\n\r\n  scrollToBottom(): void {\r\n    setTimeout(() => {\r\n      if (this.messagesContainer) {\r\n        const element = this.messagesContainer.nativeElement;\r\n        element.scrollTop = element.scrollHeight;\r\n      }\r\n    }, 100);\r\n  }\r\n\r\n  // === MÉTHODES UTILITAIRES OPTIMISÉES ===\r\n  formatLastActive(lastActive: string | Date | null): string {\r\n    if (!lastActive) return 'Hors ligne';\r\n\r\n    const diffMins = Math.floor(\r\n      (Date.now() - new Date(lastActive).getTime()) / 60000\r\n    );\r\n\r\n    if (diffMins < 1) return \"À l'instant\";\r\n    if (diffMins < 60) return `Il y a ${diffMins} min`;\r\n    if (diffMins < 1440) return `Il y a ${Math.floor(diffMins / 60)}h`;\r\n    return `Il y a ${Math.floor(diffMins / 1440)}j`;\r\n  }\r\n\r\n  // Méthodes utilitaires pour les messages vocaux\r\n  getVoicePlaybackData(messageId: string) {\r\n    return (\r\n      this.voicePlayback[messageId] || {\r\n        progress: 0,\r\n        duration: 0,\r\n        currentTime: 0,\r\n        speed: 1,\r\n      }\r\n    );\r\n  }\r\n\r\n  private setVoicePlaybackData(\r\n    messageId: string,\r\n    data: Partial<(typeof this.voicePlayback)[string]>\r\n  ) {\r\n    this.voicePlayback[messageId] = {\r\n      ...this.getVoicePlaybackData(messageId),\r\n      ...data,\r\n    };\r\n  }\r\n\r\n  // === MÉTHODES POUR LES MESSAGES VOCAUX (TEMPLATE) ===\r\n\r\n  // === MÉTHODES POUR LES APPELS (TEMPLATE) ===\r\n\r\n  startVideoCall(): void {\r\n    if (!this.otherParticipant?.id) {\r\n      this.toastService.showError(\"Impossible de démarrer l'appel\");\r\n      return;\r\n    }\r\n\r\n    this.initiateCall(CallType.VIDEO);\r\n  }\r\n\r\n  startVoiceCall(): void {\r\n    if (!this.otherParticipant?.id) {\r\n      this.toastService.showError(\"Impossible de démarrer l'appel\");\r\n      return;\r\n    }\r\n\r\n    // Forcer la configuration des éléments vidéo avant l'appel\r\n    this.setupVideoElements();\r\n\r\n    this.initiateCall(CallType.AUDIO);\r\n  }\r\n\r\n  /**\r\n   * Bascule l'état du microphone\r\n   */\r\n  toggleMicrophone(): void {\r\n    this.isMuted = !this.isMuted;\r\n\r\n    if (this.callService.toggleAudio) {\r\n      this.callService.toggleAudio();\r\n    }\r\n\r\n    this.toastService.showSuccess(\r\n      this.isMuted ? 'Microphone coupé' : 'Microphone activé'\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Bascule l'état de la caméra\r\n   */\r\n  toggleCamera(): void {\r\n    this.isVideoEnabled = !this.isVideoEnabled;\r\n    console.log(\r\n      '📹 Camera toggled:',\r\n      this.isVideoEnabled ? 'ENABLED' : 'DISABLED'\r\n    );\r\n\r\n    if (this.callService.toggleVideo) {\r\n      this.callService.toggleVideo();\r\n    }\r\n\r\n    this.toastService.showSuccess(\r\n      this.isVideoEnabled ? 'Caméra activée' : 'Caméra désactivée'\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Termine l'appel en cours\r\n   */\r\n  endCall(): void {\r\n    if (this.activeCall) {\r\n      if (this.callService.endCall) {\r\n        this.callService.endCall(this.activeCall.id).subscribe({\r\n          next: () => {\r\n            this.activeCall = null;\r\n            this.isCallConnected = false;\r\n          },\r\n          error: (error) => {\r\n            console.error('❌ Error ending call:', error);\r\n            this.toastService.showError(\"Erreur lors de la fin de l'appel\");\r\n          },\r\n        });\r\n      } else {\r\n        // Fallback si la méthode n'existe pas\r\n        this.activeCall = null;\r\n        this.isCallConnected = false;\r\n        this.toastService.showSuccess('Appel terminé');\r\n      }\r\n    }\r\n  }\r\n\r\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\r\n  // onCallAccepted, onCallRejected - définies plus loin\r\n\r\n  // === MÉTHODES POUR LES FICHIERS (TEMPLATE) ===\r\n\r\n  formatFileSize(bytes: number): string {\r\n    if (bytes < 1024) return bytes + ' B';\r\n    if (bytes < 1048576) return Math.round(bytes / 1024) + ' KB';\r\n    return Math.round(bytes / 1048576) + ' MB';\r\n  }\r\n\r\n  downloadFile(message: any): void {\r\n    const fileAttachment = message.attachments?.find(\r\n      (att: any) => !att.type?.startsWith('image/')\r\n    );\r\n    if (fileAttachment?.url) {\r\n      const link = document.createElement('a');\r\n      link.href = fileAttachment.url;\r\n      link.download = fileAttachment.name || 'file';\r\n      link.target = '_blank';\r\n      document.body.appendChild(link);\r\n      link.click();\r\n      document.body.removeChild(link);\r\n      this.toastService.showSuccess('Téléchargement démarré');\r\n    }\r\n  }\r\n\r\n  // === MÉTHODES POUR L'INTERFACE UTILISATEUR (TEMPLATE) ===\r\n\r\n  toggleSearch(): void {\r\n    this.searchMode = !this.searchMode;\r\n    this.showSearch = this.searchMode;\r\n  }\r\n\r\n  toggleMainMenu(): void {\r\n    this.showMainMenu = !this.showMainMenu;\r\n  }\r\n\r\n  goBackToConversations(): void {\r\n    // Naviguer vers la liste des conversations\r\n    this.router\r\n      .navigate(['/front/messages/conversations'])\r\n      .then(() => {})\r\n      .catch((error) => {\r\n        console.error('❌ Navigation error:', error);\r\n        // Fallback: essayer la route parent\r\n        this.router.navigate(['/front/messages']).catch(() => {\r\n          // Dernier recours: recharger la page\r\n          window.location.href = '/front/messages/conversations';\r\n        });\r\n      });\r\n  }\r\n\r\n  // === MÉTHODES POUR LES MENUS ET INTERACTIONS ===\r\n\r\n  closeAllMenus(): void {\r\n    this.showEmojiPicker = false;\r\n    this.showAttachmentMenu = false;\r\n    this.showMainMenu = false;\r\n    this.showMessageContextMenu = false;\r\n    this.showReactionPicker = false;\r\n  }\r\n\r\n  onMessageContextMenu(message: any, event: MouseEvent): void {\r\n    event.preventDefault();\r\n    this.selectedMessage = message;\r\n    this.contextMenuPosition = { x: event.clientX, y: event.clientY };\r\n    this.showMessageContextMenu = true;\r\n  }\r\n\r\n  showQuickReactions(message: any, event: MouseEvent): void {\r\n    event.stopPropagation();\r\n    this.reactionPickerMessage = message;\r\n    this.contextMenuPosition = { x: event.clientX, y: event.clientY };\r\n    this.showReactionPicker = true;\r\n  }\r\n\r\n  quickReact(emoji: string): void {\r\n    if (this.reactionPickerMessage) {\r\n      this.toggleReaction(this.reactionPickerMessage.id, emoji);\r\n    }\r\n    this.showReactionPicker = false;\r\n  }\r\n\r\n  toggleReaction(messageId: string, emoji: string): void {\r\n    if (!messageId || !emoji) {\r\n      console.error('❌ Missing messageId or emoji for reaction');\r\n      return;\r\n    }\r\n\r\n    // Appeler le service pour ajouter/supprimer la réaction\r\n    this.MessageService.reactToMessage(messageId, emoji).subscribe({\r\n      next: (result) => {\r\n        // Mettre à jour le message local avec les nouvelles réactions\r\n        const messageIndex = this.messages.findIndex(\r\n          (msg) => msg.id === messageId\r\n        );\r\n        if (messageIndex !== -1) {\r\n          this.messages[messageIndex] = result;\r\n          this.cdr.detectChanges();\r\n        }\r\n\r\n        this.toastService.showSuccess(`Réaction ${emoji} ajoutée`);\r\n      },\r\n      error: (error) => {\r\n        console.error('❌ Error toggling reaction:', error);\r\n        this.toastService.showError(\"Erreur lors de l'ajout de la réaction\");\r\n      },\r\n    });\r\n  }\r\n\r\n  hasUserReacted(reaction: any, userId: string): boolean {\r\n    return reaction.userId === userId;\r\n  }\r\n\r\n  replyToMessage(message: any): void {\r\n    this.closeAllMenus();\r\n  }\r\n\r\n  forwardMessage(message: any): void {\r\n    this.closeAllMenus();\r\n  }\r\n\r\n  deleteMessage(message: any): void {\r\n    if (!message.id) {\r\n      console.error('❌ No message ID provided for deletion');\r\n      this.toastService.showError('Erreur: ID du message manquant');\r\n      return;\r\n    }\r\n\r\n    // Vérifier si l'utilisateur peut supprimer ce message\r\n    const canDelete =\r\n      message.sender?.id === this.currentUserId ||\r\n      message.senderId === this.currentUserId;\r\n\r\n    if (!canDelete) {\r\n      this.toastService.showError(\r\n        'Vous ne pouvez supprimer que vos propres messages'\r\n      );\r\n      this.closeAllMenus();\r\n      return;\r\n    }\r\n\r\n    // Demander confirmation\r\n    if (!confirm('Êtes-vous sûr de vouloir supprimer ce message ?')) {\r\n      this.closeAllMenus();\r\n      return;\r\n    }\r\n\r\n    // Appeler le service pour supprimer le message\r\n    this.MessageService.deleteMessage(message.id).subscribe({\r\n      next: (result) => {\r\n        // Supprimer le message de la liste locale\r\n        this.messages = this.messages.filter((msg) => msg.id !== message.id);\r\n\r\n        this.toastService.showSuccess('Message supprimé');\r\n        this.cdr.detectChanges();\r\n      },\r\n      error: (error) => {\r\n        console.error('❌ Error deleting message:', error);\r\n        this.toastService.showError('Erreur lors de la suppression du message');\r\n      },\r\n    });\r\n\r\n    this.closeAllMenus();\r\n  }\r\n\r\n  // === MÉTHODES POUR LES ÉMOJIS ET PIÈCES JOINTES ===\r\n\r\n  toggleEmojiPicker(): void {\r\n    this.showEmojiPicker = !this.showEmojiPicker;\r\n  }\r\n\r\n  selectEmojiCategory(category: any): void {\r\n    this.selectedEmojiCategory = category;\r\n  }\r\n\r\n  getEmojisForCategory(category: any): any[] {\r\n    return category?.emojis || [];\r\n  }\r\n\r\n  insertEmoji(emoji: any): void {\r\n    const currentContent = this.messageForm.get('content')?.value || '';\r\n    const newContent = currentContent + emoji.emoji;\r\n    this.messageForm.patchValue({ content: newContent });\r\n    this.showEmojiPicker = false;\r\n  }\r\n\r\n  toggleAttachmentMenu(): void {\r\n    this.showAttachmentMenu = !this.showAttachmentMenu;\r\n  }\r\n\r\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\r\n  // triggerFileInput, getFileAcceptTypes, onFileSelected - définies plus loin\r\n\r\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\r\n  // Ces méthodes sont déjà définies plus loin dans le fichier\r\n\r\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\r\n  // handleTypingIndicator - définie plus loin\r\n\r\n  // === MÉTHODES UTILITAIRES POUR LE TEMPLATE ===\r\n\r\n  trackByMessageId(index: number, message: any): string {\r\n    return message.id || message._id || index.toString();\r\n  }\r\n\r\n  // === MÉTHODES MANQUANTES POUR LE TEMPLATE ===\r\n\r\n  testAddMessage(): void {\r\n    const testMessage = {\r\n      id: `test-${Date.now()}`,\r\n      content: `Message de test ${new Date().toLocaleTimeString()}`,\r\n      timestamp: new Date().toISOString(),\r\n      sender: {\r\n        id: this.otherParticipant?.id || 'test-user',\r\n        username: this.otherParticipant?.username || 'Test User',\r\n        image:\r\n          this.otherParticipant?.image || 'assets/images/default-avatar.png',\r\n      },\r\n      type: 'TEXT',\r\n      isRead: false,\r\n    };\r\n    this.messages.push(testMessage);\r\n    this.cdr.detectChanges();\r\n    setTimeout(() => this.scrollToBottom(), 50);\r\n  }\r\n\r\n  isGroupConversation(): boolean {\r\n    return (\r\n      this.conversation?.isGroup ||\r\n      this.conversation?.participants?.length > 2 ||\r\n      false\r\n    );\r\n  }\r\n\r\n  openCamera(): void {\r\n    this.showAttachmentMenu = false;\r\n    // TODO: Implémenter l'ouverture de la caméra\r\n  }\r\n\r\n  zoomImage(factor: number): void {\r\n    const imageElement = document.querySelector(\r\n      '.image-viewer-zoom'\r\n    ) as HTMLElement;\r\n    if (imageElement) {\r\n      const currentTransform = imageElement.style.transform || 'scale(1)';\r\n      const currentScale = parseFloat(\r\n        currentTransform.match(/scale\\(([^)]+)\\)/)?.[1] || '1'\r\n      );\r\n      const newScale = Math.max(0.5, Math.min(3, currentScale * factor));\r\n      imageElement.style.transform = `scale(${newScale})`;\r\n      if (newScale > 1) {\r\n        imageElement.classList.add('zoomed');\r\n      } else {\r\n        imageElement.classList.remove('zoomed');\r\n      }\r\n    }\r\n  }\r\n\r\n  resetZoom(): void {\r\n    const imageElement = document.querySelector(\r\n      '.image-viewer-zoom'\r\n    ) as HTMLElement;\r\n    if (imageElement) {\r\n      imageElement.style.transform = 'scale(1)';\r\n      imageElement.classList.remove('zoomed');\r\n    }\r\n  }\r\n\r\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\r\n  // formatFileSize, downloadFile, toggleReaction, hasUserReacted, showQuickReactions\r\n  // toggleEmojiPicker, toggleAttachmentMenu, selectEmojiCategory, getEmojisForCategory, insertEmoji\r\n  // Ces méthodes sont déjà définies plus loin dans le fichier\r\n\r\n  triggerFileInput(type?: string): void {\r\n    const input = this.fileInput?.nativeElement;\r\n    if (!input) {\r\n      console.error('File input element not found');\r\n      return;\r\n    }\r\n\r\n    // Configurer le type de fichier accepté\r\n    if (type === 'image') {\r\n      input.accept = 'image/*';\r\n    } else if (type === 'video') {\r\n      input.accept = 'video/*';\r\n    } else if (type === 'document') {\r\n      input.accept = '.pdf,.doc,.docx,.xls,.xlsx,.txt';\r\n    } else {\r\n      input.accept = '*/*';\r\n    }\r\n\r\n    // Réinitialiser la valeur pour permettre la sélection du même fichier\r\n    input.value = '';\r\n\r\n    // Déclencher la sélection de fichier\r\n    input.click();\r\n    this.showAttachmentMenu = false;\r\n  }\r\n\r\n  formatMessageTime(timestamp: string | Date): string {\r\n    if (!timestamp) return '';\r\n\r\n    const date = new Date(timestamp);\r\n    return date.toLocaleTimeString('fr-FR', {\r\n      hour: '2-digit',\r\n      minute: '2-digit',\r\n    });\r\n  }\r\n\r\n  formatDateSeparator(timestamp: string | Date): string {\r\n    if (!timestamp) return '';\r\n\r\n    const date = new Date(timestamp);\r\n    const today = new Date();\r\n    const yesterday = new Date(today);\r\n    yesterday.setDate(yesterday.getDate() - 1);\r\n\r\n    if (date.toDateString() === today.toDateString()) {\r\n      return \"Aujourd'hui\";\r\n    } else if (date.toDateString() === yesterday.toDateString()) {\r\n      return 'Hier';\r\n    } else {\r\n      return date.toLocaleDateString('fr-FR');\r\n    }\r\n  }\r\n\r\n  formatMessageContent(content: string): string {\r\n    if (!content) return '';\r\n\r\n    // Remplacer les URLs par des liens\r\n    const urlRegex = /(https?:\\/\\/[^\\s]+)/g;\r\n    return content.replace(\r\n      urlRegex,\r\n      '<a href=\"$1\" target=\"_blank\" class=\"text-blue-500 underline\">$1</a>'\r\n    );\r\n  }\r\n\r\n  shouldShowDateSeparator(index: number): boolean {\r\n    if (index === 0) return true;\r\n\r\n    const currentMessage = this.messages[index];\r\n    const previousMessage = this.messages[index - 1];\r\n\r\n    if (!currentMessage?.timestamp || !previousMessage?.timestamp) return false;\r\n\r\n    const currentDate = new Date(currentMessage.timestamp).toDateString();\r\n    const previousDate = new Date(previousMessage.timestamp).toDateString();\r\n\r\n    return currentDate !== previousDate;\r\n  }\r\n\r\n  shouldShowAvatar(index: number): boolean {\r\n    const currentMessage = this.messages[index];\r\n    const nextMessage = this.messages[index + 1];\r\n\r\n    if (!nextMessage) return true;\r\n\r\n    return currentMessage.sender?.id !== nextMessage.sender?.id;\r\n  }\r\n\r\n  shouldShowSenderName(index: number): boolean {\r\n    const currentMessage = this.messages[index];\r\n    const previousMessage = this.messages[index - 1];\r\n\r\n    if (!previousMessage) return true;\r\n\r\n    return currentMessage.sender?.id !== previousMessage.sender?.id;\r\n  }\r\n\r\n  getMessageType(message: any): string {\r\n    // Vérifier d'abord le type de message explicite\r\n    if (message.type) {\r\n      if (message.type === 'IMAGE' || message.type === 'image') return 'image';\r\n      if (message.type === 'VIDEO' || message.type === 'video') return 'video';\r\n      if (message.type === 'AUDIO' || message.type === 'audio') return 'audio';\r\n      if (message.type === 'VOICE_MESSAGE') return 'audio';\r\n      if (message.type === 'FILE' || message.type === 'file') return 'file';\r\n    }\r\n\r\n    // Ensuite vérifier les attachments\r\n    if (message.attachments && message.attachments.length > 0) {\r\n      const attachment = message.attachments[0];\r\n      if (attachment.type?.startsWith('image/')) return 'image';\r\n      if (attachment.type?.startsWith('video/')) return 'video';\r\n      if (attachment.type?.startsWith('audio/')) return 'audio';\r\n      return 'file';\r\n    }\r\n\r\n    // Vérifier si c'est un message vocal basé sur les propriétés\r\n    if (message.voiceUrl || message.audioUrl || message.voice) return 'audio';\r\n\r\n    return 'text';\r\n  }\r\n\r\n  hasImage(message: any): boolean {\r\n    // Vérifier le type de message\r\n    if (message.type === 'IMAGE' || message.type === 'image') {\r\n      return true;\r\n    }\r\n\r\n    // Vérifier les attachments\r\n    const hasImageAttachment =\r\n      message.attachments?.some((att: any) => {\r\n        return att.type?.startsWith('image/') || att.type === 'IMAGE';\r\n      }) || false;\r\n\r\n    // Vérifier les propriétés directes d'image\r\n    const hasImageUrl = !!(message.imageUrl || message.image);\r\n\r\n    return hasImageAttachment || hasImageUrl;\r\n  }\r\n\r\n  hasFile(message: any): boolean {\r\n    // Vérifier le type de message\r\n    if (message.type === 'FILE' || message.type === 'file') {\r\n      return true;\r\n    }\r\n\r\n    // Vérifier les attachments non-image\r\n    const hasFileAttachment =\r\n      message.attachments?.some((att: any) => {\r\n        return !att.type?.startsWith('image/') && att.type !== 'IMAGE';\r\n      }) || false;\r\n\r\n    return hasFileAttachment;\r\n  }\r\n\r\n  getImageUrl(message: any): string {\r\n    // Vérifier les propriétés directes d'image\r\n    if (message.imageUrl) {\r\n      return message.imageUrl;\r\n    }\r\n    if (message.image) {\r\n      return message.image;\r\n    }\r\n\r\n    // Vérifier les attachments\r\n    const imageAttachment = message.attachments?.find(\r\n      (att: any) => att.type?.startsWith('image/') || att.type === 'IMAGE'\r\n    );\r\n\r\n    if (imageAttachment) {\r\n      return imageAttachment.url || imageAttachment.path || '';\r\n    }\r\n\r\n    return '';\r\n  }\r\n\r\n  getFileName(message: any): string {\r\n    const fileAttachment = message.attachments?.find(\r\n      (att: any) => !att.type?.startsWith('image/')\r\n    );\r\n    return fileAttachment?.name || 'Fichier';\r\n  }\r\n\r\n  getFileSize(message: any): string {\r\n    const fileAttachment = message.attachments?.find(\r\n      (att: any) => !att.type?.startsWith('image/')\r\n    );\r\n    if (!fileAttachment?.size) return '';\r\n\r\n    const bytes = fileAttachment.size;\r\n    if (bytes < 1024) return bytes + ' B';\r\n    if (bytes < 1048576) return Math.round(bytes / 1024) + ' KB';\r\n    return Math.round(bytes / 1048576) + ' MB';\r\n  }\r\n\r\n  getFileIcon(message: any): string {\r\n    const fileAttachment = message.attachments?.find(\r\n      (att: any) => !att.type?.startsWith('image/')\r\n    );\r\n    if (!fileAttachment?.type) return 'fas fa-file';\r\n\r\n    if (fileAttachment.type.startsWith('audio/')) return 'fas fa-file-audio';\r\n    if (fileAttachment.type.startsWith('video/')) return 'fas fa-file-video';\r\n    if (fileAttachment.type.includes('pdf')) return 'fas fa-file-pdf';\r\n    if (fileAttachment.type.includes('word')) return 'fas fa-file-word';\r\n    if (fileAttachment.type.includes('excel')) return 'fas fa-file-excel';\r\n    return 'fas fa-file';\r\n  }\r\n\r\n  getUserColor(userId: string): string {\r\n    // Générer une couleur basée sur l'ID utilisateur\r\n    const colors = [\r\n      '#FF6B6B',\r\n      '#4ECDC4',\r\n      '#45B7D1',\r\n      '#96CEB4',\r\n      '#FFEAA7',\r\n      '#DDA0DD',\r\n      '#98D8C8',\r\n    ];\r\n    const index = userId.charCodeAt(0) % colors.length;\r\n    return colors[index];\r\n  }\r\n\r\n  // === MÉTHODES D'INTERACTION ===\r\n  onMessageClick(message: any, event: any): void {}\r\n\r\n  onInputChange(event: any): void {\r\n    // Gérer les changements dans le champ de saisie\r\n    this.handleTypingIndicator();\r\n  }\r\n\r\n  onInputKeyDown(event: KeyboardEvent): void {\r\n    if (event.key === 'Enter' && !event.shiftKey) {\r\n      event.preventDefault();\r\n      this.sendMessage();\r\n    }\r\n  }\r\n\r\n  onInputFocus(): void {\r\n    // Gérer le focus sur le champ de saisie\r\n  }\r\n\r\n  onInputBlur(): void {\r\n    // Gérer la perte de focus sur le champ de saisie\r\n  }\r\n\r\n  onScroll(event: any): void {\r\n    // Gérer le scroll pour charger plus de messages\r\n    const element = event.target;\r\n    if (\r\n      element.scrollTop === 0 &&\r\n      this.hasMoreMessages &&\r\n      !this.isLoadingMore\r\n    ) {\r\n      this.loadMoreMessages();\r\n    }\r\n  }\r\n\r\n  openUserProfile(userId: string): void {}\r\n\r\n  onImageLoad(event: any, message: any): void {\r\n    console.log(\r\n      '🖼️ [Debug] Image loaded successfully for message:',\r\n      message.id,\r\n      event.target.src\r\n    );\r\n  }\r\n\r\n  onImageError(event: any, message: any): void {\r\n    console.error('🖼️ [Debug] Image failed to load for message:', message.id, {\r\n      src: event.target.src,\r\n      error: event,\r\n    });\r\n    // Optionnel : afficher une image de remplacement\r\n    event.target.src =\r\n      'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjNmNGY2Ii8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzZiNzI4MCIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkltYWdlIG5vbiBkaXNwb25pYmxlPC90ZXh0Pjwvc3ZnPg==';\r\n  }\r\n\r\n  openImageViewer(message: any): void {\r\n    const imageAttachment = message.attachments?.find((att: any) =>\r\n      att.type?.startsWith('image/')\r\n    );\r\n    if (imageAttachment?.url) {\r\n      this.selectedImage = {\r\n        url: imageAttachment.url,\r\n        name: imageAttachment.name || 'Image',\r\n        size: this.formatFileSize(imageAttachment.size || 0),\r\n        message: message,\r\n      };\r\n      this.showImageViewer = true;\r\n    }\r\n  }\r\n\r\n  closeImageViewer(): void {\r\n    this.showImageViewer = false;\r\n    this.selectedImage = null;\r\n  }\r\n\r\n  downloadImage(): void {\r\n    if (this.selectedImage?.url) {\r\n      const link = document.createElement('a');\r\n      link.href = this.selectedImage.url;\r\n      link.download = this.selectedImage.name || 'image';\r\n      link.target = '_blank';\r\n      document.body.appendChild(link);\r\n      link.click();\r\n      document.body.removeChild(link);\r\n      this.toastService.showSuccess('Téléchargement démarré');\r\n      console.log(\r\n        '🖼️ [ImageViewer] Download started:',\r\n        this.selectedImage.name\r\n      );\r\n    }\r\n  }\r\n\r\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\r\n  // zoomImage, resetZoom, formatFileSize, downloadFile, toggleReaction, hasUserReacted, toggleSearch\r\n  // Ces méthodes sont déjà définies plus loin dans le fichier\r\n\r\n  searchMessages(): void {\r\n    if (!this.searchQuery.trim()) {\r\n      this.searchResults = [];\r\n      return;\r\n    }\r\n\r\n    this.searchResults = this.messages.filter(\r\n      (message) =>\r\n        message.content\r\n          ?.toLowerCase()\r\n          .includes(this.searchQuery.toLowerCase()) ||\r\n        message.sender?.username\r\n          ?.toLowerCase()\r\n          .includes(this.searchQuery.toLowerCase())\r\n    );\r\n  }\r\n\r\n  onSearchQueryChange(): void {\r\n    this.searchMessages();\r\n  }\r\n\r\n  clearSearch(): void {\r\n    this.searchQuery = '';\r\n    this.searchResults = [];\r\n  }\r\n\r\n  jumpToMessage(messageId: string): void {\r\n    const messageElement = document.getElementById(`message-${messageId}`);\r\n    if (messageElement) {\r\n      messageElement.scrollIntoView({ behavior: 'smooth', block: 'center' });\r\n      // Highlight temporairement le message\r\n      messageElement.classList.add('highlight');\r\n      setTimeout(() => {\r\n        messageElement.classList.remove('highlight');\r\n      }, 2000);\r\n    }\r\n  }\r\n\r\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\r\n  // toggleMainMenu, toggleTheme, onMessageContextMenu\r\n  // Ces méthodes sont déjà définies plus loin dans le fichier\r\n\r\n  closeContextMenu(): void {\r\n    this.showMessageContextMenu = false;\r\n    this.selectedMessage = null;\r\n  }\r\n\r\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\r\n  // replyToMessage, forwardMessage, deleteMessage, showQuickReactions, closeReactionPicker\r\n  // quickReact, closeAllMenus, testAddMessage, toggleAttachmentMenu, toggleEmojiPicker\r\n  // Ces méthodes sont déjà définies plus loin dans le fichier\r\n\r\n  // === MÉTHODE SUPPRIMÉE (DUPLICATION) ===\r\n  // triggerFileInput - définie plus loin\r\n\r\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\r\n  // openCamera, getEmojisForCategory, selectEmojiCategory, insertEmoji\r\n  // goBackToConversations, startVideoCall, startVoiceCall\r\n  // Ces méthodes sont déjà définies plus loin dans le fichier\r\n\r\n  private initiateCall(callType: CallType): void {\r\n    console.log('📋 [MessageChat] Call details:', {\r\n      callType,\r\n      otherParticipant: this.otherParticipant,\r\n      conversation: this.conversation?.id,\r\n      currentUserId: this.currentUserId,\r\n    });\r\n\r\n    if (!this.otherParticipant) {\r\n      console.error('❌ [MessageChat] No recipient selected');\r\n      this.toastService.showError('Aucun destinataire sélectionné');\r\n      return;\r\n    }\r\n\r\n    const recipientId = this.otherParticipant.id || this.otherParticipant._id;\r\n    if (!recipientId) {\r\n      console.error('❌ [MessageChat] Recipient ID not found');\r\n      this.toastService.showError('ID du destinataire introuvable');\r\n      return;\r\n    }\r\n\r\n    console.log(`📞 [MessageChat] Initiating ${callType} call to user:`, {\r\n      recipientId,\r\n      recipientName:\r\n        this.otherParticipant.username || this.otherParticipant.name,\r\n      conversationId: this.conversation?.id,\r\n    });\r\n\r\n    this.isInCall = true;\r\n    this.callType = callType === CallType.VIDEO ? 'VIDEO' : 'AUDIO';\r\n    this.callDuration = 0;\r\n\r\n    // Démarrer le timer d'appel\r\n    this.startCallTimer();\r\n\r\n    // Utiliser le CallService\r\n    this.callService\r\n      .initiateCall(recipientId, callType, this.conversation?.id)\r\n      .subscribe({\r\n        next: (call: Call) => {\r\n          this.activeCall = call;\r\n          this.isCallConnected = false;\r\n          this.toastService.showSuccess(\r\n            `Appel ${callType === CallType.VIDEO ? 'vidéo' : 'audio'} initié`\r\n          );\r\n\r\n          console.log(\r\n            '📡 [MessageChat] Call should now be sent to recipient via WebSocket'\r\n          );\r\n        },\r\n        error: (error: any) => {\r\n          console.error('❌ [MessageChat] Error initiating call:', {\r\n            error: error.message || error,\r\n            recipientId,\r\n            callType,\r\n            conversationId: this.conversation?.id,\r\n          });\r\n\r\n          this.endCall();\r\n          this.toastService.showError(\"Erreur lors de l'initiation de l'appel\");\r\n        },\r\n      });\r\n  }\r\n\r\n  acceptCall(incomingCall: IncomingCall): void {\r\n    this.callService.acceptCall(incomingCall).subscribe({\r\n      next: (call: Call) => {\r\n        this.activeCall = call;\r\n        this.isInCall = true;\r\n        this.isCallConnected = true;\r\n        this.callType = call.type === CallType.VIDEO ? 'VIDEO' : 'AUDIO';\r\n        this.startCallTimer();\r\n        this.toastService.showSuccess('Appel accepté');\r\n      },\r\n      error: (error: any) => {\r\n        console.error('❌ Error accepting call:', error);\r\n        this.toastService.showError(\"Erreur lors de l'acceptation de l'appel\");\r\n      },\r\n    });\r\n  }\r\n\r\n  rejectCall(incomingCall: IncomingCall): void {\r\n    this.callService.rejectCall(incomingCall.id, 'User rejected').subscribe({\r\n      next: () => {\r\n        this.toastService.showSuccess('Appel rejeté');\r\n      },\r\n      error: (error) => {\r\n        console.error('❌ Error rejecting call:', error);\r\n        this.toastService.showError(\"Erreur lors du rejet de l'appel\");\r\n      },\r\n    });\r\n  }\r\n\r\n  private startCallTimer(): void {\r\n    this.callDuration = 0;\r\n    this.callTimer = setInterval(() => {\r\n      this.callDuration++;\r\n      this.cdr.detectChanges();\r\n    }, 1000);\r\n  }\r\n\r\n  private resetCallState(): void {\r\n    if (this.callTimer) {\r\n      clearInterval(this.callTimer);\r\n      this.callTimer = null;\r\n    }\r\n\r\n    this.isInCall = false;\r\n    this.callType = null;\r\n    this.callDuration = 0;\r\n    this.activeCall = null;\r\n    this.isCallConnected = false;\r\n    this.isMuted = false;\r\n    this.isVideoEnabled = true;\r\n  }\r\n\r\n  // === CONTRÔLES D'APPEL ===\r\n  toggleMute(): void {\r\n    if (!this.activeCall) return;\r\n\r\n    this.isMuted = !this.isMuted;\r\n\r\n    // Utiliser la méthode toggleMedia du CallService\r\n    this.callService\r\n      .toggleMedia(\r\n        this.activeCall.id,\r\n        undefined, // video unchanged\r\n        !this.isMuted // audio state\r\n      )\r\n      .subscribe({\r\n        next: () => {\r\n          this.toastService.showSuccess(\r\n            this.isMuted ? 'Micro coupé' : 'Micro activé'\r\n          );\r\n        },\r\n        error: (error) => {\r\n          console.error('❌ Error toggling mute:', error);\r\n          // Revert state on error\r\n          this.isMuted = !this.isMuted;\r\n          this.toastService.showError('Erreur lors du changement du micro');\r\n        },\r\n      });\r\n  }\r\n\r\n  toggleVideo(): void {\r\n    if (!this.activeCall) return;\r\n\r\n    this.isVideoEnabled = !this.isVideoEnabled;\r\n\r\n    // Utiliser la méthode toggleMedia du CallService\r\n    this.callService\r\n      .toggleMedia(\r\n        this.activeCall.id,\r\n        this.isVideoEnabled, // video state\r\n        undefined // audio unchanged\r\n      )\r\n      .subscribe({\r\n        next: () => {\r\n          this.toastService.showSuccess(\r\n            this.isVideoEnabled ? 'Caméra activée' : 'Caméra désactivée'\r\n          );\r\n        },\r\n        error: (error) => {\r\n          console.error('❌ Error toggling video:', error);\r\n          // Revert state on error\r\n          this.isVideoEnabled = !this.isVideoEnabled;\r\n          this.toastService.showError('Erreur lors du changement de la caméra');\r\n        },\r\n      });\r\n  }\r\n\r\n  formatCallDuration(duration: number): string {\r\n    const hours = Math.floor(duration / 3600);\r\n    const minutes = Math.floor((duration % 3600) / 60);\r\n    const seconds = duration % 60;\r\n\r\n    if (hours > 0) {\r\n      return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds\r\n        .toString()\r\n        .padStart(2, '0')}`;\r\n    }\r\n    return `${minutes}:${seconds.toString().padStart(2, '0')}`;\r\n  }\r\n\r\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\r\n  // trackByMessageId, isGroupConversation - Ces méthodes sont déjà définies plus loin dans le fichier\r\n\r\n  async startVoiceRecording(): Promise<void> {\r\n    try {\r\n      // Vérifier le support du navigateur\r\n      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {\r\n        throw new Error(\r\n          \"Votre navigateur ne supporte pas l'enregistrement audio\"\r\n        );\r\n      }\r\n\r\n      // Vérifier si MediaRecorder est supporté\r\n      if (!window.MediaRecorder) {\r\n        throw new Error(\r\n          \"MediaRecorder n'est pas supporté par votre navigateur\"\r\n        );\r\n      }\r\n\r\n      // Demander l'accès au microphone avec des contraintes optimisées\r\n      const stream = await navigator.mediaDevices.getUserMedia({\r\n        audio: {\r\n          echoCancellation: true,\r\n          noiseSuppression: true,\r\n          autoGainControl: true,\r\n          sampleRate: 44100,\r\n          channelCount: 1,\r\n        },\r\n      });\r\n\r\n      // Vérifier les types MIME supportés\r\n      let mimeType = 'audio/webm;codecs=opus';\r\n      if (!MediaRecorder.isTypeSupported(mimeType)) {\r\n        mimeType = 'audio/webm';\r\n        if (!MediaRecorder.isTypeSupported(mimeType)) {\r\n          mimeType = 'audio/mp4';\r\n          if (!MediaRecorder.isTypeSupported(mimeType)) {\r\n            mimeType = ''; // Laisser le navigateur choisir\r\n          }\r\n        }\r\n      }\r\n\r\n      // Créer le MediaRecorder\r\n      this.mediaRecorder = new MediaRecorder(stream, {\r\n        mimeType: mimeType || undefined,\r\n      });\r\n\r\n      // Initialiser les variables\r\n      this.audioChunks = [];\r\n      this.isRecordingVoice = true;\r\n      this.voiceRecordingDuration = 0;\r\n      this.voiceRecordingState = 'recording';\r\n\r\n      // Démarrer le timer\r\n      this.recordingTimer = setInterval(() => {\r\n        this.voiceRecordingDuration++;\r\n        // Animer les waves\r\n        this.animateVoiceWaves();\r\n        this.cdr.detectChanges();\r\n      }, 1000);\r\n\r\n      // Gérer les événements du MediaRecorder\r\n      this.mediaRecorder.ondataavailable = (event) => {\r\n        if (event.data.size > 0) {\r\n          this.audioChunks.push(event.data);\r\n        }\r\n      };\r\n\r\n      this.mediaRecorder.onstop = () => {\r\n        this.processRecordedAudio();\r\n      };\r\n\r\n      this.mediaRecorder.onerror = (event: any) => {\r\n        console.error('🎤 [Voice] MediaRecorder error:', event.error);\r\n        this.toastService.showError(\"Erreur lors de l'enregistrement\");\r\n        this.cancelVoiceRecording();\r\n      };\r\n\r\n      // Démarrer l'enregistrement\r\n      this.mediaRecorder.start(100); // Collecter les données toutes les 100ms\r\n\r\n      this.toastService.showSuccess('🎤 Enregistrement vocal démarré');\r\n    } catch (error: any) {\r\n      console.error('🎤 [Voice] Error starting recording:', error);\r\n\r\n      let errorMessage = \"Impossible de démarrer l'enregistrement vocal\";\r\n\r\n      if (error.name === 'NotAllowedError') {\r\n        errorMessage =\r\n          \"Accès au microphone refusé. Veuillez autoriser l'accès dans les paramètres de votre navigateur.\";\r\n      } else if (error.name === 'NotFoundError') {\r\n        errorMessage =\r\n          'Aucun microphone détecté. Veuillez connecter un microphone.';\r\n      } else if (error.name === 'NotSupportedError') {\r\n        errorMessage =\r\n          \"Votre navigateur ne supporte pas l'enregistrement audio.\";\r\n      } else if (error.message) {\r\n        errorMessage = error.message;\r\n      }\r\n\r\n      this.toastService.showError(errorMessage);\r\n      this.cancelVoiceRecording();\r\n    }\r\n  }\r\n\r\n  stopVoiceRecording(): void {\r\n    if (this.mediaRecorder && this.mediaRecorder.state === 'recording') {\r\n      this.mediaRecorder.stop();\r\n      this.mediaRecorder.stream.getTracks().forEach((track) => track.stop());\r\n    }\r\n\r\n    if (this.recordingTimer) {\r\n      clearInterval(this.recordingTimer);\r\n      this.recordingTimer = null;\r\n    }\r\n\r\n    this.isRecordingVoice = false;\r\n    this.voiceRecordingState = 'processing';\r\n  }\r\n\r\n  cancelVoiceRecording(): void {\r\n    if (this.mediaRecorder) {\r\n      if (this.mediaRecorder.state === 'recording') {\r\n        this.mediaRecorder.stop();\r\n      }\r\n      this.mediaRecorder.stream.getTracks().forEach((track) => track.stop());\r\n      this.mediaRecorder = null;\r\n    }\r\n\r\n    if (this.recordingTimer) {\r\n      clearInterval(this.recordingTimer);\r\n      this.recordingTimer = null;\r\n    }\r\n\r\n    this.isRecordingVoice = false;\r\n    this.voiceRecordingDuration = 0;\r\n    this.voiceRecordingState = 'idle';\r\n    this.audioChunks = [];\r\n  }\r\n\r\n  private async processRecordedAudio(): Promise<void> {\r\n    try {\r\n      // Vérifier qu'on a des données audio\r\n      if (this.audioChunks.length === 0) {\r\n        console.error('🎤 [Voice] No audio chunks available');\r\n        this.toastService.showError('Aucun audio enregistré');\r\n        this.cancelVoiceRecording();\r\n        return;\r\n      }\r\n\r\n      console.log(\r\n        '🎤 [Voice] Audio chunks:',\r\n        this.audioChunks.length,\r\n        'Duration:',\r\n        this.voiceRecordingDuration\r\n      );\r\n\r\n      // Vérifier la durée minimale\r\n      if (this.voiceRecordingDuration < 1) {\r\n        console.error(\r\n          '🎤 [Voice] Recording too short:',\r\n          this.voiceRecordingDuration\r\n        );\r\n        this.toastService.showError(\r\n          'Enregistrement trop court (minimum 1 seconde)'\r\n        );\r\n        this.cancelVoiceRecording();\r\n        return;\r\n      }\r\n\r\n      // Déterminer le type MIME du blob\r\n      let mimeType = 'audio/webm;codecs=opus';\r\n      if (this.mediaRecorder?.mimeType) {\r\n        mimeType = this.mediaRecorder.mimeType;\r\n      }\r\n\r\n      // Créer le blob audio\r\n      const audioBlob = new Blob(this.audioChunks, {\r\n        type: mimeType,\r\n      });\r\n\r\n      console.log('🎤 [Voice] Audio blob created:', {\r\n        size: audioBlob.size,\r\n        type: audioBlob.type,\r\n      });\r\n\r\n      // Déterminer l'extension du fichier\r\n      let extension = '.webm';\r\n      if (mimeType.includes('mp4')) {\r\n        extension = '.mp4';\r\n      } else if (mimeType.includes('wav')) {\r\n        extension = '.wav';\r\n      } else if (mimeType.includes('ogg')) {\r\n        extension = '.ogg';\r\n      }\r\n\r\n      // Créer le fichier\r\n      const audioFile = new File(\r\n        [audioBlob],\r\n        `voice_${Date.now()}${extension}`,\r\n        {\r\n          type: mimeType,\r\n        }\r\n      );\r\n\r\n      console.log('🎤 [Voice] Audio file created:', {\r\n        name: audioFile.name,\r\n        size: audioFile.size,\r\n        type: audioFile.type,\r\n      });\r\n\r\n      // Envoyer le message vocal\r\n      this.voiceRecordingState = 'processing';\r\n      await this.sendVoiceMessage(audioFile);\r\n\r\n      this.toastService.showSuccess('🎤 Message vocal envoyé');\r\n    } catch (error: any) {\r\n      console.error('🎤 [Voice] Error processing audio:', error);\r\n      this.toastService.showError(\r\n        \"Erreur lors de l'envoi du message vocal: \" +\r\n          (error.message || 'Erreur inconnue')\r\n      );\r\n    } finally {\r\n      // Nettoyer l'état\r\n      this.voiceRecordingState = 'idle';\r\n      this.voiceRecordingDuration = 0;\r\n      this.audioChunks = [];\r\n      this.isRecordingVoice = false;\r\n    }\r\n  }\r\n\r\n  private async sendVoiceMessage(audioFile: File): Promise<void> {\r\n    const receiverId = this.otherParticipant?.id || this.otherParticipant?._id;\r\n\r\n    if (!receiverId) {\r\n      throw new Error('Destinataire introuvable');\r\n    }\r\n\r\n    return new Promise((resolve, reject) => {\r\n      this.MessageService.sendMessage(\r\n        receiverId,\r\n        '',\r\n        audioFile,\r\n        'AUDIO' as any,\r\n        this.conversation.id\r\n      ).subscribe({\r\n        next: (message: any) => {\r\n          this.messages.push(message);\r\n          this.scrollToBottom();\r\n          resolve();\r\n        },\r\n        error: (error: any) => {\r\n          console.error(\"Erreur lors de l'envoi du message vocal:\", error);\r\n          reject(error);\r\n        },\r\n      });\r\n    });\r\n  }\r\n\r\n  formatRecordingDuration(duration: number): string {\r\n    const minutes = Math.floor(duration / 60);\r\n    const seconds = duration % 60;\r\n    return `${minutes}:${seconds.toString().padStart(2, '0')}`;\r\n  }\r\n\r\n  // === MÉTHODES D'ENREGISTREMENT VOCAL AMÉLIORÉES ===\r\n\r\n  onRecordStart(event: Event): void {\r\n    event.preventDefault();\r\n\r\n    console.log('🎤 [Voice] Current state:', {\r\n      isRecordingVoice: this.isRecordingVoice,\r\n      voiceRecordingState: this.voiceRecordingState,\r\n      voiceRecordingDuration: this.voiceRecordingDuration,\r\n      mediaRecorder: !!this.mediaRecorder,\r\n    });\r\n\r\n    // Vérifier si on peut enregistrer\r\n    if (this.voiceRecordingState === 'processing') {\r\n      this.toastService.showWarning('Traitement en cours...');\r\n      return;\r\n    }\r\n\r\n    if (this.isRecordingVoice) {\r\n      this.toastService.showWarning('Enregistrement déjà en cours...');\r\n      return;\r\n    }\r\n\r\n    // Afficher un message de début\r\n    this.toastService.showInfo(\"🎤 Démarrage de l'enregistrement vocal...\");\r\n\r\n    // Démarrer l'enregistrement\r\n    this.startVoiceRecording().catch((error) => {\r\n      console.error('🎤 [Voice] Failed to start recording:', error);\r\n      this.toastService.showError(\r\n        \"Impossible de démarrer l'enregistrement vocal: \" +\r\n          (error.message || 'Erreur inconnue')\r\n      );\r\n    });\r\n  }\r\n\r\n  onRecordEnd(event: Event): void {\r\n    event.preventDefault();\r\n\r\n    if (!this.isRecordingVoice) {\r\n      return;\r\n    }\r\n\r\n    // Arrêter l'enregistrement et envoyer\r\n    this.stopVoiceRecording();\r\n  }\r\n\r\n  onRecordCancel(event: Event): void {\r\n    event.preventDefault();\r\n\r\n    if (!this.isRecordingVoice) {\r\n      return;\r\n    }\r\n\r\n    // Annuler l'enregistrement\r\n    this.cancelVoiceRecording();\r\n  }\r\n\r\n  getRecordingFormat(): string {\r\n    if (this.mediaRecorder?.mimeType) {\r\n      if (this.mediaRecorder.mimeType.includes('webm')) return 'WebM';\r\n      if (this.mediaRecorder.mimeType.includes('mp4')) return 'MP4';\r\n      if (this.mediaRecorder.mimeType.includes('wav')) return 'WAV';\r\n      if (this.mediaRecorder.mimeType.includes('ogg')) return 'OGG';\r\n    }\r\n    return 'Auto';\r\n  }\r\n\r\n  // === ANIMATION DES WAVES VOCALES ===\r\n\r\n  private animateVoiceWaves(): void {\r\n    // Animer les waves pendant l'enregistrement\r\n    this.voiceWaves = this.voiceWaves.map(() => {\r\n      return Math.floor(Math.random() * 20) + 4; // Hauteur entre 4 et 24px\r\n    });\r\n  }\r\n\r\n  onFileSelected(event: any): void {\r\n    const files = event.target.files;\r\n\r\n    if (!files || files.length === 0) {\r\n      return;\r\n    }\r\n\r\n    for (let file of files) {\r\n      console.log(\r\n        `📁 [Upload] Processing file: ${file.name}, size: ${file.size}, type: ${file.type}`\r\n      );\r\n      this.uploadFile(file);\r\n    }\r\n  }\r\n\r\n  private uploadFile(file: File): void {\r\n    const receiverId = this.otherParticipant?.id || this.otherParticipant?._id;\r\n\r\n    if (!receiverId) {\r\n      console.error('📁 [Upload] No receiver ID found');\r\n      this.toastService.showError('Destinataire introuvable');\r\n      return;\r\n    }\r\n\r\n    // Vérifier la taille du fichier (max 50MB)\r\n    const maxSize = 50 * 1024 * 1024; // 50MB\r\n    if (file.size > maxSize) {\r\n      console.error(`📁 [Upload] File too large: ${file.size} bytes`);\r\n      this.toastService.showError('Fichier trop volumineux (max 50MB)');\r\n      return;\r\n    }\r\n\r\n    // 🖼️ Compression d'image si nécessaire\r\n    if (file.type.startsWith('image/') && file.size > 1024 * 1024) {\r\n      // > 1MB\r\n      console.log(\r\n        '🖼️ [Compression] Compressing image:',\r\n        file.name,\r\n        'Original size:',\r\n        file.size\r\n      );\r\n      this.compressImage(file)\r\n        .then((compressedFile) => {\r\n          console.log(\r\n            '🖼️ [Compression] ✅ Image compressed successfully. New size:',\r\n            compressedFile.size\r\n          );\r\n          this.sendFileToServer(compressedFile, receiverId);\r\n        })\r\n        .catch((error) => {\r\n          console.error('🖼️ [Compression] ❌ Error compressing image:', error);\r\n          // Envoyer le fichier original en cas d'erreur\r\n          this.sendFileToServer(file, receiverId);\r\n        });\r\n      return;\r\n    }\r\n\r\n    // Envoyer le fichier sans compression\r\n    this.sendFileToServer(file, receiverId);\r\n  }\r\n\r\n  private sendFileToServer(file: File, receiverId: string): void {\r\n    const messageType = this.getFileMessageType(file);\r\n\r\n    this.isSendingMessage = true;\r\n    this.isUploading = true;\r\n    this.uploadProgress = 0;\r\n\r\n    // Simuler la progression d'upload\r\n    const progressInterval = setInterval(() => {\r\n      this.uploadProgress += Math.random() * 15;\r\n      if (this.uploadProgress >= 90) {\r\n        clearInterval(progressInterval);\r\n      }\r\n      this.cdr.detectChanges();\r\n    }, 300);\r\n\r\n    this.MessageService.sendMessage(\r\n      receiverId,\r\n      '',\r\n      file,\r\n      messageType,\r\n      this.conversation.id\r\n    ).subscribe({\r\n      next: (message: any) => {\r\n        console.log('📁 [Debug] Sent message structure:', {\r\n          id: message.id,\r\n          type: message.type,\r\n          attachments: message.attachments,\r\n          hasImage: this.hasImage(message),\r\n          hasFile: this.hasFile(message),\r\n          imageUrl: this.getImageUrl(message),\r\n        });\r\n\r\n        clearInterval(progressInterval);\r\n        this.uploadProgress = 100;\r\n\r\n        setTimeout(() => {\r\n          this.messages.push(message);\r\n          this.scrollToBottom();\r\n          this.toastService.showSuccess('Fichier envoyé avec succès');\r\n          this.resetUploadState();\r\n        }, 500);\r\n      },\r\n      error: (error: any) => {\r\n        console.error('📁 [Upload] ❌ Error sending file:', error);\r\n        clearInterval(progressInterval);\r\n        this.toastService.showError(\"Erreur lors de l'envoi du fichier\");\r\n        this.resetUploadState();\r\n      },\r\n    });\r\n  }\r\n\r\n  private getFileMessageType(file: File): any {\r\n    if (file.type.startsWith('image/')) return 'IMAGE' as any;\r\n    if (file.type.startsWith('video/')) return 'VIDEO' as any;\r\n    if (file.type.startsWith('audio/')) return 'AUDIO' as any;\r\n    return 'FILE' as any;\r\n  }\r\n\r\n  getFileAcceptTypes(): string {\r\n    return '*/*';\r\n  }\r\n\r\n  resetUploadState(): void {\r\n    this.isSendingMessage = false;\r\n    this.isUploading = false;\r\n    this.uploadProgress = 0;\r\n  }\r\n\r\n  // === DRAG & DROP ===\r\n\r\n  onDragOver(event: DragEvent): void {\r\n    event.preventDefault();\r\n    event.stopPropagation();\r\n    this.isDragOver = true;\r\n  }\r\n\r\n  onDragLeave(event: DragEvent): void {\r\n    event.preventDefault();\r\n    event.stopPropagation();\r\n    // Vérifier si on quitte vraiment la zone (pas un enfant)\r\n    const rect = (event.currentTarget as HTMLElement).getBoundingClientRect();\r\n    const x = event.clientX;\r\n    const y = event.clientY;\r\n\r\n    if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {\r\n      this.isDragOver = false;\r\n    }\r\n  }\r\n\r\n  onDrop(event: DragEvent): void {\r\n    event.preventDefault();\r\n    event.stopPropagation();\r\n    this.isDragOver = false;\r\n\r\n    const files = event.dataTransfer?.files;\r\n    if (files && files.length > 0) {\r\n      // Traiter chaque fichier\r\n      Array.from(files).forEach((file) => {\r\n        console.log(\r\n          '📁 [Drag&Drop] Processing file:',\r\n          file.name,\r\n          file.type,\r\n          file.size\r\n        );\r\n        this.uploadFile(file);\r\n      });\r\n\r\n      this.toastService.showSuccess(\r\n        `${files.length} fichier(s) en cours d'envoi`\r\n      );\r\n    }\r\n  }\r\n\r\n  // === COMPRESSION D'IMAGES ===\r\n\r\n  private compressImage(file: File, quality: number = 0.8): Promise<File> {\r\n    return new Promise((resolve, reject) => {\r\n      const canvas = document.createElement('canvas');\r\n      const ctx = canvas.getContext('2d');\r\n      const img = new Image();\r\n\r\n      img.onload = () => {\r\n        // Calculer les nouvelles dimensions (max 1920x1080)\r\n        const maxWidth = 1920;\r\n        const maxHeight = 1080;\r\n        let { width, height } = img;\r\n\r\n        if (width > maxWidth || height > maxHeight) {\r\n          const ratio = Math.min(maxWidth / width, maxHeight / height);\r\n          width *= ratio;\r\n          height *= ratio;\r\n        }\r\n\r\n        canvas.width = width;\r\n        canvas.height = height;\r\n\r\n        // Dessiner l'image redimensionnée\r\n        ctx?.drawImage(img, 0, 0, width, height);\r\n\r\n        // Convertir en blob avec compression\r\n        canvas.toBlob(\r\n          (blob) => {\r\n            if (blob) {\r\n              const compressedFile = new File([blob], file.name, {\r\n                type: file.type,\r\n                lastModified: Date.now(),\r\n              });\r\n              resolve(compressedFile);\r\n            } else {\r\n              reject(new Error('Failed to compress image'));\r\n            }\r\n          },\r\n          file.type,\r\n          quality\r\n        );\r\n      };\r\n\r\n      img.onerror = () => reject(new Error('Failed to load image'));\r\n      img.src = URL.createObjectURL(file);\r\n    });\r\n  }\r\n\r\n  // === MÉTHODES DE FORMATAGE SUPPLÉMENTAIRES ===\r\n\r\n  private handleTypingIndicator(): void {\r\n    if (!this.isTyping) {\r\n      this.isTyping = true;\r\n      // Envoyer l'indicateur de frappe à l'autre utilisateur\r\n      this.sendTypingIndicator(true);\r\n    }\r\n\r\n    // Reset le timer\r\n    if (this.typingTimeout) {\r\n      clearTimeout(this.typingTimeout);\r\n    }\r\n\r\n    this.typingTimeout = setTimeout(() => {\r\n      this.isTyping = false;\r\n      // Arrêter l'indicateur de frappe\r\n      this.sendTypingIndicator(false);\r\n    }, 2000);\r\n  }\r\n\r\n  private sendTypingIndicator(isTyping: boolean): void {\r\n    // Envoyer l'indicateur de frappe via WebSocket/GraphQL\r\n    const receiverId = this.otherParticipant?.id || this.otherParticipant?._id;\r\n    if (receiverId && this.conversation?.id) {\r\n      console.log(\r\n        `📝 Sending typing indicator: ${isTyping} to user ${receiverId}`\r\n      );\r\n      // TODO: Implémenter l'envoi via WebSocket/GraphQL subscription\r\n      // this.MessageService.sendTypingIndicator(this.conversation.id, receiverId, isTyping);\r\n    }\r\n  }\r\n\r\n  // === MÉTHODES POUR L'INTERFACE D'APPEL ===\r\n\r\n  onCallAccepted(call: Call): void {\r\n    this.activeCall = call;\r\n    this.isInCall = true;\r\n    this.isCallConnected = true;\r\n    this.startCallTimer();\r\n    this.toastService.showSuccess('Appel accepté');\r\n  }\r\n\r\n  onCallRejected(): void {\r\n    this.endCall();\r\n    this.toastService.showInfo('Appel rejeté');\r\n  }\r\n\r\n  // === MÉTHODES POUR LA LECTURE DES MESSAGES VOCAUX ===\r\n\r\n  playVoiceMessage(message: any): void {\r\n    this.toggleVoicePlayback(message);\r\n  }\r\n\r\n  isVoicePlaying(messageId: string): boolean {\r\n    return this.playingMessageId === messageId;\r\n  }\r\n\r\n  toggleVoicePlayback(message: any): void {\r\n    const messageId = message.id;\r\n    const audioUrl = this.getVoiceUrl(message);\r\n\r\n    if (!audioUrl) {\r\n      console.error('🎵 [Voice] No audio URL found for message:', messageId);\r\n      this.toastService.showError('Fichier audio introuvable');\r\n      return;\r\n    }\r\n\r\n    // Si c'est déjà en cours de lecture, arrêter\r\n    if (this.isVoicePlaying(messageId)) {\r\n      this.stopVoicePlayback();\r\n      return;\r\n    }\r\n\r\n    // Arrêter toute autre lecture en cours\r\n    this.stopVoicePlayback();\r\n\r\n    // Démarrer la nouvelle lecture\r\n    this.startVoicePlayback(message, audioUrl);\r\n  }\r\n\r\n  private startVoicePlayback(message: any, audioUrl: string): void {\r\n    const messageId = message.id;\r\n\r\n    try {\r\n      console.log(\r\n        '🎵 [Voice] Starting playback for:',\r\n        messageId,\r\n        'URL:',\r\n        audioUrl\r\n      );\r\n\r\n      this.currentAudio = new Audio(audioUrl);\r\n      this.playingMessageId = messageId;\r\n\r\n      // Initialiser les valeurs par défaut avec la nouvelle structure\r\n      const currentData = this.getVoicePlaybackData(messageId);\r\n      this.setVoicePlaybackData(messageId, {\r\n        progress: 0,\r\n        currentTime: 0,\r\n        speed: currentData.speed || 1,\r\n        duration: currentData.duration || 0,\r\n      });\r\n\r\n      // Configurer la vitesse de lecture\r\n      this.currentAudio.playbackRate = currentData.speed || 1;\r\n\r\n      // Événements audio\r\n      this.currentAudio.addEventListener('loadedmetadata', () => {\r\n        if (this.currentAudio) {\r\n          this.setVoicePlaybackData(messageId, {\r\n            duration: this.currentAudio.duration,\r\n          });\r\n          console.log(\r\n            '🎵 [Voice] Audio loaded, duration:',\r\n            this.currentAudio.duration\r\n          );\r\n        }\r\n      });\r\n\r\n      this.currentAudio.addEventListener('timeupdate', () => {\r\n        if (this.currentAudio && this.playingMessageId === messageId) {\r\n          const currentTime = this.currentAudio.currentTime;\r\n          const progress = (currentTime / this.currentAudio.duration) * 100;\r\n          this.setVoicePlaybackData(messageId, { currentTime, progress });\r\n          this.cdr.detectChanges();\r\n        }\r\n      });\r\n\r\n      this.currentAudio.addEventListener('ended', () => {\r\n        this.stopVoicePlayback();\r\n      });\r\n\r\n      this.currentAudio.addEventListener('error', (error) => {\r\n        console.error('🎵 [Voice] Audio error:', error);\r\n        this.toastService.showError('Erreur lors de la lecture audio');\r\n        this.stopVoicePlayback();\r\n      });\r\n\r\n      // Démarrer la lecture\r\n      this.currentAudio\r\n        .play()\r\n        .then(() => {\r\n          this.toastService.showSuccess('🎵 Lecture du message vocal');\r\n        })\r\n        .catch((error) => {\r\n          console.error('🎵 [Voice] Error starting playback:', error);\r\n          this.toastService.showError('Impossible de lire le message vocal');\r\n          this.stopVoicePlayback();\r\n        });\r\n    } catch (error) {\r\n      console.error('🎵 [Voice] Error creating audio:', error);\r\n      this.toastService.showError('Erreur lors de la lecture audio');\r\n      this.stopVoicePlayback();\r\n    }\r\n  }\r\n\r\n  private stopVoicePlayback(): void {\r\n    if (this.currentAudio) {\r\n      this.currentAudio.pause();\r\n      this.currentAudio.currentTime = 0;\r\n      this.currentAudio = null;\r\n    }\r\n    this.playingMessageId = null;\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  getVoiceUrl(message: any): string {\r\n    // Vérifier les propriétés directes d'audio\r\n    if (message.voiceUrl) return message.voiceUrl;\r\n    if (message.audioUrl) return message.audioUrl;\r\n    if (message.voice) return message.voice;\r\n\r\n    // Vérifier les attachments audio\r\n    const audioAttachment = message.attachments?.find(\r\n      (att: any) => att.type?.startsWith('audio/') || att.type === 'AUDIO'\r\n    );\r\n\r\n    if (audioAttachment) {\r\n      return audioAttachment.url || audioAttachment.path || '';\r\n    }\r\n\r\n    return '';\r\n  }\r\n\r\n  getVoiceWaves(message: any): number[] {\r\n    // Générer des waves basées sur l'ID du message pour la cohérence\r\n    const messageId = message.id || '';\r\n    const seed = messageId\r\n      .split('')\r\n      .reduce((acc: number, char: string) => acc + char.charCodeAt(0), 0);\r\n    const waves: number[] = [];\r\n\r\n    for (let i = 0; i < 16; i++) {\r\n      const height = 4 + ((seed + i * 7) % 20);\r\n      waves.push(height);\r\n    }\r\n\r\n    return waves;\r\n  }\r\n\r\n  getVoiceProgress(message: any): number {\r\n    const data = this.getVoicePlaybackData(message.id);\r\n    const totalWaves = 16;\r\n    return Math.floor((data.progress / 100) * totalWaves);\r\n  }\r\n\r\n  getVoiceCurrentTime(message: any): string {\r\n    const data = this.getVoicePlaybackData(message.id);\r\n    return this.formatAudioTime(data.currentTime);\r\n  }\r\n\r\n  getVoiceDuration(message: any): string {\r\n    const data = this.getVoicePlaybackData(message.id);\r\n    const duration = data.duration || message.metadata?.duration || 0;\r\n\r\n    if (typeof duration === 'string') {\r\n      return duration; // Déjà formaté\r\n    }\r\n\r\n    return this.formatAudioTime(duration);\r\n  }\r\n\r\n  private formatAudioTime(seconds: number): string {\r\n    const minutes = Math.floor(seconds / 60);\r\n    const remainingSeconds = Math.floor(seconds % 60);\r\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\r\n  }\r\n\r\n  seekVoiceMessage(message: any, waveIndex: number): void {\r\n    const messageId = message.id;\r\n\r\n    if (!this.currentAudio || this.playingMessageId !== messageId) {\r\n      return;\r\n    }\r\n\r\n    const totalWaves = 16;\r\n    const seekPercentage = (waveIndex / totalWaves) * 100;\r\n    const seekTime = (seekPercentage / 100) * this.currentAudio.duration;\r\n\r\n    this.currentAudio.currentTime = seekTime;\r\n  }\r\n\r\n  toggleVoiceSpeed(message: any): void {\r\n    const messageId = message.id;\r\n    const data = this.getVoicePlaybackData(messageId);\r\n\r\n    // Cycle entre 1x, 1.5x, 2x\r\n    const newSpeed = data.speed === 1 ? 1.5 : data.speed === 1.5 ? 2 : 1;\r\n\r\n    this.setVoicePlaybackData(messageId, { speed: newSpeed });\r\n\r\n    if (this.currentAudio && this.playingMessageId === messageId) {\r\n      this.currentAudio.playbackRate = newSpeed;\r\n    }\r\n\r\n    this.toastService.showSuccess(`Vitesse: ${newSpeed}x`);\r\n  }\r\n\r\n  // === MÉTHODES MANQUANTES POUR LE TEMPLATE ===\r\n\r\n  changeVoiceSpeed(message: any): void {\r\n    this.toggleVoiceSpeed(message);\r\n  }\r\n\r\n  getVoiceSpeed(message: any): number {\r\n    const data = this.getVoicePlaybackData(message.id);\r\n    return data.speed || 1;\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.subscriptions.unsubscribe();\r\n\r\n    // Nettoyer les timers\r\n    if (this.callTimer) {\r\n      clearInterval(this.callTimer);\r\n    }\r\n    if (this.recordingTimer) {\r\n      clearInterval(this.recordingTimer);\r\n    }\r\n    if (this.typingTimeout) {\r\n      clearTimeout(this.typingTimeout);\r\n    }\r\n\r\n    // Nettoyer les ressources audio\r\n    if (this.mediaRecorder) {\r\n      if (this.mediaRecorder.state === 'recording') {\r\n        this.mediaRecorder.stop();\r\n      }\r\n      this.mediaRecorder.stream?.getTracks().forEach((track) => track.stop());\r\n    }\r\n\r\n    // Nettoyer la lecture audio\r\n    this.stopVoicePlayback();\r\n  }\r\n}\r\n"], "mappings": ";;AAAA,SACEA,SAAS,EAITC,SAAS,QAGJ,eAAe;AACtB,SAAiCC,UAAU,QAAQ,gBAAgB;AAEnE,SAASC,YAAY,QAAQ,MAAM;AAInC,SAASC,QAAQ,QAA4B,kCAAkC;AAMxE,WAAMC,oBAAoB,GAA1B,MAAMA,oBAAoB;EAmJ/BC,YACUC,EAAe,EACfC,KAAqB,EACrBC,MAAc,EACdC,cAA8B,EAC9BC,WAAwB,EACxBC,YAA0B,EAC1BC,GAAsB;IANtB,KAAAN,EAAE,GAAFA,EAAE;IACF,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,GAAG,GAAHA,GAAG;IA5Ib;IACA,KAAAC,YAAY,GAAQ,IAAI;IACxB,KAAAC,QAAQ,GAAU,EAAE;IACpB,KAAAC,aAAa,GAAkB,IAAI;IACnC,KAAAC,eAAe,GAAG,KAAK;IAEvB,KAAAC,gBAAgB,GAAQ,IAAI;IAE5B;IACA,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAC,aAAa,GAAG,KAAK;IACrB,KAAAC,eAAe,GAAG,IAAI;IACtB,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAC,kBAAkB,GAAG,KAAK;IAC1B,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAC,WAAW,GAAG,EAAE;IAChB,KAAAC,aAAa,GAAU,EAAE;IACzB,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAC,gBAAgB,GAAG,KAAK;IACxB,KAAAC,iBAAiB,GAAG,KAAK;IACzB,KAAAC,YAAY,GAAG,KAAK;IACpB,KAAAC,sBAAsB,GAAG,KAAK;IAC9B,KAAAC,eAAe,GAAQ,IAAI;IAC3B,KAAAC,mBAAmB,GAAG;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAC,CAAE;IACpC,KAAAC,kBAAkB,GAAG,KAAK;IAC1B,KAAAC,qBAAqB,GAAQ,IAAI;IAEjC,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAC,aAAa,GAAQ,IAAI;IACzB,KAAAC,cAAc,GAAG,CAAC;IAClB,KAAAC,WAAW,GAAG,KAAK;IACnB,KAAAC,UAAU,GAAG,KAAK;IAElB;IACA,KAAAC,gBAAgB,GAAG,KAAK;IACxB,KAAAC,sBAAsB,GAAG,CAAC;IAC1B,KAAAC,mBAAmB,GAAwC,MAAM;IACzD,KAAAC,aAAa,GAAyB,IAAI;IAC1C,KAAAC,WAAW,GAAW,EAAE;IACxB,KAAAC,cAAc,GAAQ,IAAI;IAClC,KAAAC,UAAU,GAAa,CACrB,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CACzD;IAED;IACQ,KAAAC,YAAY,GAA4B,IAAI;IAC5C,KAAAC,gBAAgB,GAAkB,IAAI;IACtC,KAAAC,aAAa,GAOjB,EAAE;IAEN;IACA,KAAAC,QAAQ,GAAG,KAAK;IAChB,KAAAC,QAAQ,GAA6B,IAAI;IACzC,KAAAC,YAAY,GAAG,CAAC;IACR,KAAAC,SAAS,GAAQ,IAAI;IAE7B;IACA,KAAAC,UAAU,GAAQ,IAAI;IACtB,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAC,OAAO,GAAG,KAAK;IACf,KAAAC,cAAc,GAAG,IAAI;IACrB,KAAAC,iBAAiB,GAA4B,IAAI;IACjD,KAAAC,kBAAkB,GAA4B,IAAI;IAElD;IACA,KAAAC,eAAe,GAAU,CACvB;MACEC,EAAE,EAAE,SAAS;MACbC,IAAI,EAAE,SAAS;MACfC,IAAI,EAAE,IAAI;MACVC,MAAM,EAAE,CACN;QAAEC,KAAK,EAAE,IAAI;QAAEH,IAAI,EAAE;MAAe,CAAE,EACtC;QAAEG,KAAK,EAAE,IAAI;QAAEH,IAAI,EAAE;MAA6B,CAAE,EACpD;QAAEG,KAAK,EAAE,IAAI;QAAEH,IAAI,EAAE;MAAiC,CAAE,EACxD;QAAEG,KAAK,EAAE,IAAI;QAAEH,IAAI,EAAE;MAAgC,CAAE,EACvD;QAAEG,KAAK,EAAE,IAAI;QAAEH,IAAI,EAAE;MAAyB,CAAE,EAChD;QAAEG,KAAK,EAAE,IAAI;QAAEH,IAAI,EAAE;MAA0B,CAAE,EACjD;QAAEG,KAAK,EAAE,IAAI;QAAEH,IAAI,EAAE;MAAwB,CAAE,EAC/C;QAAEG,KAAK,EAAE,IAAI;QAAEH,IAAI,EAAE;MAA+B,CAAE,EACtD;QAAEG,KAAK,EAAE,IAAI;QAAEH,IAAI,EAAE;MAAgC,CAAE,EACvD;QAAEG,KAAK,EAAE,IAAI;QAAEH,IAAI,EAAE;MAAwB,CAAE;KAElD,EACD;MACED,EAAE,EAAE,QAAQ;MACZC,IAAI,EAAE,QAAQ;MACdC,IAAI,EAAE,IAAI;MACVC,MAAM,EAAE,CACN;QAAEC,KAAK,EAAE,IAAI;QAAEH,IAAI,EAAE;MAAM,CAAE,EAC7B;QAAEG,KAAK,EAAE,IAAI;QAAEH,IAAI,EAAE;MAAO,CAAE,EAC9B;QAAEG,KAAK,EAAE,IAAI;QAAEH,IAAI,EAAE;MAAK,CAAE,EAC5B;QAAEG,KAAK,EAAE,IAAI;QAAEH,IAAI,EAAE;MAAM,CAAE,EAC7B;QAAEG,KAAK,EAAE,IAAI;QAAEH,IAAI,EAAE;MAAQ,CAAE,EAC/B;QAAEG,KAAK,EAAE,IAAI;QAAEH,IAAI,EAAE;MAAK,CAAE,EAC5B;QAAEG,KAAK,EAAE,IAAI;QAAEH,IAAI,EAAE;MAAO,CAAE,EAC9B;QAAEG,KAAK,EAAE,IAAI;QAAEH,IAAI,EAAE;MAAS,CAAE,EAChC;QAAEG,KAAK,EAAE,IAAI;QAAEH,IAAI,EAAE;MAAW,CAAE;KAErC,EACD;MACED,EAAE,EAAE,QAAQ;MACZC,IAAI,EAAE,QAAQ;MACdC,IAAI,EAAE,IAAI;MACVC,MAAM,EAAE,CACN;QAAEC,KAAK,EAAE,IAAI;QAAEH,IAAI,EAAE;MAAU,CAAE,EACjC;QAAEG,KAAK,EAAE,IAAI;QAAEH,IAAI,EAAE;MAAU,CAAE,EACjC;QAAEG,KAAK,EAAE,IAAI;QAAEH,IAAI,EAAE;MAAY,CAAE,EACnC;QAAEG,KAAK,EAAE,IAAI;QAAEH,IAAI,EAAE;MAAS,CAAE,EAChC;QAAEG,KAAK,EAAE,IAAI;QAAEH,IAAI,EAAE;MAAa,CAAE,EACpC;QAAEG,KAAK,EAAE,IAAI;QAAEH,IAAI,EAAE;MAAK,CAAE,EAC5B;QAAEG,KAAK,EAAE,IAAI;QAAEH,IAAI,EAAE;MAAM,CAAE,EAC7B;QAAEG,KAAK,EAAE,IAAI;QAAEH,IAAI,EAAE;MAAO,CAAE;KAEjC,CACF;IACD,KAAAI,qBAAqB,GAAG,IAAI,CAACN,eAAe,CAAC,CAAC,CAAC;IAE/C;IACiB,KAAAO,oBAAoB,GAAG,EAAE;IAClC,KAAAC,WAAW,GAAG,CAAC;IAEvB;IACA,KAAAC,QAAQ,GAAG,KAAK;IAChB,KAAAC,YAAY,GAAG,KAAK;IACZ,KAAAC,aAAa,GAAQ,IAAI;IACzB,KAAAC,aAAa,GAAG,IAAIxE,YAAY,EAAE;IAWxC,IAAI,CAACyE,WAAW,GAAG,IAAI,CAACrE,EAAE,CAACsE,KAAK,CAAC;MAC/BC,OAAO,EAAE,CAAC,EAAE,EAAE,CAAC5E,UAAU,CAAC6E,QAAQ,EAAE7E,UAAU,CAAC8E,SAAS,CAAC,CAAC,CAAC,CAAC;KAC7D,CAAC;EACJ;EAEA;EACAC,eAAeA,CAAA;IACb,OACE,CAAC,IAAI,CAAC/D,gBAAgB,IAAI,IAAI,CAACyB,gBAAgB,IAAI,IAAI,CAACf,gBAAgB;EAE5E;EAEA;EACQsD,gBAAgBA,CAAA;IACtB,MAAMC,cAAc,GAAG,IAAI,CAACP,WAAW,CAACQ,GAAG,CAAC,SAAS,CAAC;IACtD,IAAI,IAAI,CAACH,eAAe,EAAE,EAAE;MAC1BE,cAAc,EAAEE,OAAO,EAAE;KAC1B,MAAM;MACLF,cAAc,EAAEG,MAAM,EAAE;;EAE5B;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACC,mBAAmB,EAAE;IAE1B;IACA,IAAI,CAACC,8BAA8B,EAAE;EACvC;EAEAC,eAAeA,CAAA;IACb;IACAC,UAAU,CAAC,MAAK;MACd,IAAI,CAACC,kBAAkB,EAAE;IAC3B,CAAC,EAAE,GAAG,CAAC;IAEP;IACAD,UAAU,CAAC,MAAK;MACd,IAAI,CAACC,kBAAkB,EAAE;IAC3B,CAAC,EAAE,GAAG,CAAC;IAEPD,UAAU,CAAC,MAAK;MACd,IAAI,CAACC,kBAAkB,EAAE;IAC3B,CAAC,EAAE,IAAI,CAAC;IAERD,UAAU,CAAC,MAAK;MACd,IAAI,CAACC,kBAAkB,EAAE;IAC3B,CAAC,EAAE,IAAI,CAAC;EACV;EAEA;;;EAGQA,kBAAkBA,CAAA;IACxB;IACA,IAAI,IAAI,CAACC,UAAU,IAAI,IAAI,CAACC,WAAW,EAAE;MACvC,IAAI,CAACnF,WAAW,CAACoF,gBAAgB,CAC/B,IAAI,CAACF,UAAU,CAACG,aAAa,EAC7B,IAAI,CAACF,WAAW,CAACE,aAAa,CAC/B;;IAEH;IAAA,KACK,IAAI,IAAI,CAACC,gBAAgB,IAAI,IAAI,CAACC,iBAAiB,EAAE;MACxD,IAAI,CAACvF,WAAW,CAACoF,gBAAgB,CAC/B,IAAI,CAACE,gBAAgB,CAACD,aAAa,EACnC,IAAI,CAACE,iBAAiB,CAACF,aAAa,CACrC;KACF,MAAM;MACL,IAAI,CAACG,2BAA2B,EAAE;MAElC;MACAR,UAAU,CAAC,MAAK;QACd,IAAI,CAACC,kBAAkB,EAAE;MAC3B,CAAC,EAAE,GAAG,CAAC;MAEP;MACAD,UAAU,CAAC,MAAK;QACd,IAAI,CAACC,kBAAkB,EAAE;MAC3B,CAAC,EAAE,IAAI,CAAC;;EAEZ;EAEA;;;EAGQO,2BAA2BA,CAAA;IACjC;IACA,MAAMC,YAAY,GAAGC,QAAQ,CAACC,cAAc,CAC1C,YAAY,CACO;IACrB,MAAMC,aAAa,GAAGF,QAAQ,CAACC,cAAc,CAC3C,aAAa,CACM;IAErB,IAAIF,YAAY,IAAIG,aAAa,EAAE;MACjCC,OAAO,CAACC,GAAG,CACT,6DAA6D,CAC9D;MACD,IAAI,CAAC9F,WAAW,CAACoF,gBAAgB,CAACK,YAAY,EAAEG,aAAa,CAAC;KAC/D,MAAM;MACLC,OAAO,CAACE,IAAI,CAAC,yDAAyD,CAAC;MAEvE;MACA,MAAMb,UAAU,GAAGQ,QAAQ,CAACM,aAAa,CAAC,OAAO,CAAC;MAClDd,UAAU,CAAC7B,EAAE,GAAG,YAAY;MAC5B6B,UAAU,CAACe,QAAQ,GAAG,IAAI;MAC1Bf,UAAU,CAACgB,KAAK,GAAG,IAAI;MACvBhB,UAAU,CAACiB,WAAW,GAAG,IAAI;MAC7BjB,UAAU,CAACkB,KAAK,CAACC,OAAO,GACtB,2EAA2E;MAE7E,MAAMlB,WAAW,GAAGO,QAAQ,CAACM,aAAa,CAAC,OAAO,CAAC;MACnDb,WAAW,CAAC9B,EAAE,GAAG,aAAa;MAC9B8B,WAAW,CAACc,QAAQ,GAAG,IAAI;MAC3Bd,WAAW,CAACgB,WAAW,GAAG,IAAI;MAC9BhB,WAAW,CAACiB,KAAK,CAACC,OAAO,GACvB,2EAA2E;MAE7EX,QAAQ,CAACY,IAAI,CAACC,WAAW,CAACrB,UAAU,CAAC;MACrCQ,QAAQ,CAACY,IAAI,CAACC,WAAW,CAACpB,WAAW,CAAC;MAEtC,IAAI,CAACnF,WAAW,CAACoF,gBAAgB,CAACF,UAAU,EAAEC,WAAW,CAAC;;EAE9D;EAEQL,8BAA8BA,CAAA;IACpC,MAAM0B,YAAY,GAAGA,CAAA,KAAK;MACxB,IAAI,CAACxG,WAAW,CAACwG,YAAY,EAAE;MAC/Bd,QAAQ,CAACe,mBAAmB,CAAC,OAAO,EAAED,YAAY,CAAC;MACnDd,QAAQ,CAACe,mBAAmB,CAAC,SAAS,EAAED,YAAY,CAAC;MACrDd,QAAQ,CAACe,mBAAmB,CAAC,YAAY,EAAED,YAAY,CAAC;IAC1D,CAAC;IAEDd,QAAQ,CAACgB,gBAAgB,CAAC,OAAO,EAAEF,YAAY,EAAE;MAAEG,IAAI,EAAE;IAAI,CAAE,CAAC;IAChEjB,QAAQ,CAACgB,gBAAgB,CAAC,SAAS,EAAEF,YAAY,EAAE;MAAEG,IAAI,EAAE;IAAI,CAAE,CAAC;IAClEjB,QAAQ,CAACgB,gBAAgB,CAAC,YAAY,EAAEF,YAAY,EAAE;MAAEG,IAAI,EAAE;IAAI,CAAE,CAAC;EACvE;EAEQ9B,mBAAmBA,CAAA;IACzB,IAAI,CAAC+B,eAAe,EAAE;IACtB,IAAI,CAACC,gBAAgB,EAAE;IACvB,IAAI,CAACC,sBAAsB,EAAE;EAC/B;EAEQA,sBAAsBA,CAAA;IAC5B;IACA,IAAI,CAAC9C,aAAa,CAAC+C,GAAG,CACpB,IAAI,CAAC/G,WAAW,CAACgH,aAAa,CAACC,SAAS,CAAC;MACvCC,IAAI,EAAGC,YAAiB,IAAI;QAC1B,IAAIA,YAAY,EAAE;UAChB,IAAI,CAACC,kBAAkB,CAACD,YAAY,CAAC;;MAEzC,CAAC;MACDE,KAAK,EAAGA,KAAU,IAAI;QACpBxB,OAAO,CAACwB,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;MAChE;KACD,CAAC,CACH;IAED;IACA,IAAI,CAACrD,aAAa,CAAC+C,GAAG,CACpB,IAAI,CAAC/G,WAAW,CAACsH,WAAW,CAACL,SAAS,CAAC;MACrCC,IAAI,EAAGK,IAAS,IAAI;QAClB,IAAIA,IAAI,EAAE;UACR,IAAI,CAACzE,UAAU,GAAGyE,IAAI;;MAE1B,CAAC;MACDF,KAAK,EAAGA,KAAU,IAAI;QACpBxB,OAAO,CAACwB,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC9D;KACD,CAAC,CACH;EACH;EAEQD,kBAAkBA,CAACD,YAA0B;IACnD;IACA;IACAtB,OAAO,CAACC,GAAG,CACT,iCAAiC,EACjCqB,YAAY,CAACK,MAAM,CAACC,QAAQ,CAC7B;IAED;IACA,IAAI,CAAC1H,cAAc,CAAC2H,IAAI,CAAC,UAAU,CAAC;IAEpC;IACA;IACA;EACF;;EAEQd,eAAeA,CAAA;IACrB,IAAI;MACF,MAAMe,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;MAE/C,IAAI,CAACF,UAAU,IAAIA,UAAU,KAAK,MAAM,IAAIA,UAAU,KAAK,WAAW,EAAE;QACtE9B,OAAO,CAACwB,KAAK,CAAC,gCAAgC,CAAC;QAC/C,IAAI,CAAChH,aAAa,GAAG,IAAI;QACzB,IAAI,CAACC,eAAe,GAAG,KAAK;QAC5B;;MAGF,MAAMwH,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACL,UAAU,CAAC;MAEnC;MACA,MAAMM,MAAM,GAAGH,IAAI,CAACI,GAAG,IAAIJ,IAAI,CAACzE,EAAE,IAAIyE,IAAI,CAACG,MAAM;MAEjD,IAAIA,MAAM,EAAE;QACV,IAAI,CAAC5H,aAAa,GAAG4H,MAAM;QAC3B,IAAI,CAAC3H,eAAe,GAAGwH,IAAI,CAACL,QAAQ,IAAIK,IAAI,CAACxE,IAAI,IAAI,KAAK;OAC3D,MAAM;QACLuC,OAAO,CAACwB,KAAK,CAAC,0CAA0C,EAAES,IAAI,CAAC;QAC/D,IAAI,CAACzH,aAAa,GAAG,IAAI;QACzB,IAAI,CAACC,eAAe,GAAG,KAAK;;KAE/B,CAAC,OAAO+G,KAAK,EAAE;MACdxB,OAAO,CAACwB,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;MAC/D,IAAI,CAAChH,aAAa,GAAG,IAAI;MACzB,IAAI,CAACC,eAAe,GAAG,KAAK;;EAEhC;EAEQuG,gBAAgBA,CAAA;IACtB,MAAMsB,cAAc,GAAG,IAAI,CAACtI,KAAK,CAACuI,QAAQ,CAACC,QAAQ,CAAC5D,GAAG,CAAC,IAAI,CAAC;IAE7D,IAAI,CAAC0D,cAAc,EAAE;MACnB,IAAI,CAAClI,YAAY,CAACqI,SAAS,CAAC,6BAA6B,CAAC;MAC1D;;IAGF,IAAI,CAAC9H,SAAS,GAAG,IAAI;IAErB;IACA,IAAI,CAAC+H,oBAAoB,EAAE;IAE3B,IAAI,CAACxI,cAAc,CAACyI,eAAe,CAACL,cAAc,CAAC,CAAClB,SAAS,CAAC;MAC5DC,IAAI,EAAG/G,YAAY,IAAI;QACrB,IAAI,CAACA,YAAY,GAAGA,YAAY;QAChC,IAAI,CAACsI,mBAAmB,EAAE;QAC1B,IAAI,CAACC,YAAY,EAAE;QAEnB;QACA,IAAI,CAACC,kBAAkB,EAAE;QACzB,IAAI,CAACnI,SAAS,GAAG,KAAK;MACxB,CAAC;MACD6G,KAAK,EAAGA,KAAK,IAAI;QACfxB,OAAO,CAACwB,KAAK,CAAC,+CAA+C,EAAEA,KAAK,CAAC;QACrE,IAAI,CAACpH,YAAY,CAACqI,SAAS,CACzB,8CAA8C,CAC/C;QACD,IAAI,CAAC9H,SAAS,GAAG,KAAK;QAEtB;QACAwE,UAAU,CAAC,MAAK;UACd,IAAI,CAAC6B,gBAAgB,EAAE;QACzB,CAAC,EAAE,IAAI,CAAC;MACV;KACD,CAAC;EACJ;EAEQ4B,mBAAmBA,CAAA;IACzB,IACE,CAAC,IAAI,CAACtI,YAAY,EAAEyI,YAAY,IAChC,IAAI,CAACzI,YAAY,CAACyI,YAAY,CAACC,MAAM,KAAK,CAAC,EAC3C;MACAhD,OAAO,CAACE,IAAI,CAAC,uCAAuC,CAAC;MACrD,IAAI,CAACxF,gBAAgB,GAAG,IAAI;MAC5B;;IAGF;IACA;IAEA,IAAI,IAAI,CAACJ,YAAY,CAAC2I,OAAO,EAAE;MAC7B;MACA;MACA,IAAI,CAACvI,gBAAgB,GAAG,IAAI,CAACJ,YAAY,CAACyI,YAAY,CAACG,IAAI,CAAEC,CAAM,IAAI;QACrE,MAAMC,aAAa,GAAGD,CAAC,CAAC3F,EAAE,IAAI2F,CAAC,CAACd,GAAG;QACnC,OAAOgB,MAAM,CAACD,aAAa,CAAC,KAAKC,MAAM,CAAC,IAAI,CAAC7I,aAAa,CAAC;MAC7D,CAAC,CAAC;KACH,MAAM;MACL;MACA,IAAI,CAACE,gBAAgB,GAAG,IAAI,CAACJ,YAAY,CAACyI,YAAY,CAACG,IAAI,CAAEC,CAAM,IAAI;QACrE,MAAMC,aAAa,GAAGD,CAAC,CAAC3F,EAAE,IAAI2F,CAAC,CAACd,GAAG;QACnCrC,OAAO,CAACC,GAAG,CACT,2BAA2B,EAC3BmD,aAAa,EACb,uBAAuB,EACvB,IAAI,CAAC5I,aAAa,CACnB;QACD,OAAO6I,MAAM,CAACD,aAAa,CAAC,KAAKC,MAAM,CAAC,IAAI,CAAC7I,aAAa,CAAC;MAC7D,CAAC,CAAC;;IAGJ;IACA,IAAI,CAAC,IAAI,CAACE,gBAAgB,IAAI,IAAI,CAACJ,YAAY,CAACyI,YAAY,CAACC,MAAM,GAAG,CAAC,EAAE;MACvE,IAAI,CAACtI,gBAAgB,GAAG,IAAI,CAACJ,YAAY,CAACyI,YAAY,CAAC,CAAC,CAAC;MAEzD;MACA,IAAI,IAAI,CAACzI,YAAY,CAACyI,YAAY,CAACC,MAAM,GAAG,CAAC,EAAE;QAC7C,MAAMM,kBAAkB,GACtB,IAAI,CAAC5I,gBAAgB,CAAC8C,EAAE,IAAI,IAAI,CAAC9C,gBAAgB,CAAC2H,GAAG;QACvD,IAAIgB,MAAM,CAACC,kBAAkB,CAAC,KAAKD,MAAM,CAAC,IAAI,CAAC7I,aAAa,CAAC,EAAE;UAC7DwF,OAAO,CAACC,GAAG,CACT,6DAA6D,CAC9D;UACD,IAAI,CAACvF,gBAAgB,GAAG,IAAI,CAACJ,YAAY,CAACyI,YAAY,CAAC,CAAC,CAAC;;;;IAK/D;IACA,IAAI,IAAI,CAACrI,gBAAgB,EAAE;MACzB;MACAsF,OAAO,CAACC,GAAG,CACT,qCAAqC,EACrC,IAAI,CAACvF,gBAAgB,CAACkH,QAAQ,CAC/B;MACD5B,OAAO,CAACC,GAAG,CACT,+BAA+B,EAC/B,IAAI,CAACvF,gBAAgB,CAACkH,QAAQ,CAC/B;KACF,MAAM;MACL5B,OAAO,CAACwB,KAAK,CAAC,uDAAuD,CAAC;MAEtE;;IAGF;IACA,IAAI,CAAC9C,gBAAgB,EAAE;EACzB;EAEQmE,YAAYA,CAAA;IAClB,IAAI,CAAC,IAAI,CAACvI,YAAY,EAAEkD,EAAE,EAAE;IAE5B;IACA,IAAIjD,QAAQ,GAAG,IAAI,CAACD,YAAY,CAACC,QAAQ,IAAI,EAAE;IAE/C;IACA,IAAI,CAACA,QAAQ,GAAGA,QAAQ,CAACgJ,IAAI,CAAC,CAACC,CAAM,EAAEC,CAAM,KAAI;MAC/C,MAAMC,KAAK,GAAG,IAAIC,IAAI,CAACH,CAAC,CAACI,SAAS,IAAIJ,CAAC,CAACK,SAAS,CAAC,CAACC,OAAO,EAAE;MAC5D,MAAMC,KAAK,GAAG,IAAIJ,IAAI,CAACF,CAAC,CAACG,SAAS,IAAIH,CAAC,CAACI,SAAS,CAAC,CAACC,OAAO,EAAE;MAC5D,OAAOJ,KAAK,GAAGK,KAAK,CAAC,CAAC;IACxB,CAAC,CAAC;;IAEF/D,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE;MAC5C+D,KAAK,EAAE,IAAI,CAACzJ,QAAQ,CAACyI,MAAM;MAC3BiB,KAAK,EAAE,IAAI,CAAC1J,QAAQ,CAAC,CAAC,CAAC,EAAE+D,OAAO;MAChC4F,IAAI,EAAE,IAAI,CAAC3J,QAAQ,CAAC,IAAI,CAACA,QAAQ,CAACyI,MAAM,GAAG,CAAC,CAAC,EAAE1E;KAChD,CAAC;IAEF,IAAI,CAACzD,eAAe,GAAG,IAAI,CAACN,QAAQ,CAACyI,MAAM,KAAK,IAAI,CAAClF,oBAAoB;IACzE,IAAI,CAACnD,SAAS,GAAG,KAAK;IACtB,IAAI,CAACwJ,cAAc,EAAE;EACvB;EAEAC,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAACxJ,aAAa,IAAI,CAAC,IAAI,CAACC,eAAe,IAAI,CAAC,IAAI,CAACP,YAAY,EAAEkD,EAAE,EACvE;IAEF,IAAI,CAAC5C,aAAa,GAAG,IAAI;IACzB,IAAI,CAACmD,WAAW,EAAE;IAElB;IACA,MAAMsG,MAAM,GAAG,IAAI,CAAC9J,QAAQ,CAACyI,MAAM;IAEnC,IAAI,CAAC9I,cAAc,CAACoK,WAAW,CAC7B,IAAI,CAAC9J,aAAc;IAAE;IACrB,IAAI,CAACE,gBAAgB,EAAE8C,EAAE,IAAI,IAAI,CAAC9C,gBAAgB,EAAE2H,GAAI;IAAE;IAC1D,IAAI,CAAC/H,YAAY,CAACkD,EAAE,EACpB,IAAI,CAACO,WAAW,EAChB,IAAI,CAACD,oBAAoB,CAC1B,CAACsD,SAAS,CAAC;MACVC,IAAI,EAAGkD,WAAkB,IAAI;QAC3B,IAAIA,WAAW,IAAIA,WAAW,CAACvB,MAAM,GAAG,CAAC,EAAE;UACzC;UACA,IAAI,CAACzI,QAAQ,GAAG,CAAC,GAAGgK,WAAW,CAACC,OAAO,EAAE,EAAE,GAAG,IAAI,CAACjK,QAAQ,CAAC;UAC5D,IAAI,CAACM,eAAe,GAClB0J,WAAW,CAACvB,MAAM,KAAK,IAAI,CAAClF,oBAAoB;SACnD,MAAM;UACL,IAAI,CAACjD,eAAe,GAAG,KAAK;;QAE9B,IAAI,CAACD,aAAa,GAAG,KAAK;MAC5B,CAAC;MACD4G,KAAK,EAAGA,KAAK,IAAI;QACfxB,OAAO,CAACwB,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;QAC/D,IAAI,CAACpH,YAAY,CAACqI,SAAS,CAAC,wCAAwC,CAAC;QACrE,IAAI,CAAC7H,aAAa,GAAG,KAAK;QAC1B,IAAI,CAACmD,WAAW,EAAE,CAAC,CAAC;MACtB;KACD,CAAC;EACJ;EAEA;;;EAGQ2E,oBAAoBA,CAAA;IAC1B,IAAI,CAACvE,aAAa,CAACsG,WAAW,EAAE;IAChC,IAAI,CAACtG,aAAa,GAAG,IAAIxE,YAAY,EAAE;EACzC;EAEA;;;EAGO+K,kBAAkBA,CAAA;IACvB,IAAI,IAAI,CAACpK,YAAY,EAAEkD,EAAE,EAAE;MACzB;MACA,IAAI,CAACjD,QAAQ,GAAG,EAAE;MAClB,IAAI,CAACwD,WAAW,GAAG,CAAC;MACpB,IAAI,CAAClD,eAAe,GAAG,IAAI;MAE3B;MACA,IAAI,CAACmG,gBAAgB,EAAE;;EAE3B;EAEQ8B,kBAAkBA,CAAA;IACxB,IAAI,CAAC,IAAI,CAACxI,YAAY,EAAEkD,EAAE,EAAE;MAC1BwC,OAAO,CAACE,IAAI,CAAC,kDAAkD,CAAC;MAChE;;IAGFF,OAAO,CAACC,GAAG,CACT,yDAAyD,EACzD,IAAI,CAAC3F,YAAY,CAACkD,EAAE,CACrB;IAED;IAEA,IAAI,CAACW,aAAa,CAAC+C,GAAG,CACpB,IAAI,CAAChH,cAAc,CAACyK,sBAAsB,CACxC,IAAI,CAACrK,YAAY,CAACkD,EAAE,CACrB,CAAC4D,SAAS,CAAC;MACVC,IAAI,EAAGuD,UAAe,IAAI;QACxB5E,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE;UACnCzC,EAAE,EAAEoH,UAAU,CAACpH,EAAE;UACjBqH,IAAI,EAAED,UAAU,CAACC,IAAI;UACrBvG,OAAO,EAAEsG,UAAU,CAACtG,OAAO;UAC3BwG,MAAM,EAAEF,UAAU,CAACE,MAAM;UACzBC,QAAQ,EAAEH,UAAU,CAACG,QAAQ;UAC7BC,UAAU,EAAEJ,UAAU,CAACI,UAAU;UACjCC,WAAW,EAAEL,UAAU,CAACK;SACzB,CAAC;QAEF;QACAjF,OAAO,CAACC,GAAG,CACT,mCAAmC,EACnC,IAAI,CAACiF,cAAc,CAACN,UAAU,CAAC,CAChC;QAED,IAAIA,UAAU,CAACK,WAAW,EAAE;UAC1BL,UAAU,CAACK,WAAW,CAACE,OAAO,CAAC,CAACC,GAAQ,EAAEC,KAAa,KAAI;YACzDrF,OAAO,CAACC,GAAG,CAAC,yBAAyBoF,KAAK,GAAG,EAAE;cAC7CR,IAAI,EAAEO,GAAG,CAACP,IAAI;cACdS,GAAG,EAAEF,GAAG,CAACE,GAAG;cACZC,IAAI,EAAEH,GAAG,CAACG,IAAI;cACd9H,IAAI,EAAE2H,GAAG,CAAC3H,IAAI;cACd+H,IAAI,EAAEJ,GAAG,CAACI;aACX,CAAC;UACJ,CAAC,CAAC;;QAGJ;QACA,MAAMC,aAAa,GAAG,IAAI,CAAClL,QAAQ,CAACmL,IAAI,CACrCC,GAAG,IAAKA,GAAG,CAACnI,EAAE,KAAKoH,UAAU,CAACpH,EAAE,CAClC;QACD,IAAI,CAACiI,aAAa,EAAE;UAClB;UACA,IAAI,CAAClL,QAAQ,CAACqL,IAAI,CAAChB,UAAU,CAAC;UAC9B5E,OAAO,CAACC,GAAG,CACT,0CAA0C,EAC1C,IAAI,CAAC1F,QAAQ,CAACyI,MAAM,CACrB;UAED;UACA,IAAI,CAAC3I,GAAG,CAACwL,aAAa,EAAE;UAExB;UACA1G,UAAU,CAAC,MAAK;YACd,IAAI,CAACgF,cAAc,EAAE;UACvB,CAAC,EAAE,EAAE,CAAC;UAEN;UACA,MAAMY,QAAQ,GAAGH,UAAU,CAACE,MAAM,EAAEtH,EAAE,IAAIoH,UAAU,CAACG,QAAQ;UAC7D/E,OAAO,CAACC,GAAG,CAAC,kDAAkD,EAAE;YAC9D8E,QAAQ;YACRvK,aAAa,EAAE,IAAI,CAACA,aAAa;YACjCsL,gBAAgB,EAAEf,QAAQ,KAAK,IAAI,CAACvK;WACrC,CAAC;UAEF,IAAIuK,QAAQ,IAAIA,QAAQ,KAAK,IAAI,CAACvK,aAAa,EAAE;YAC/C,IAAI,CAACuL,iBAAiB,CAACnB,UAAU,CAACpH,EAAE,CAAC;;;MAG3C,CAAC;MACDgE,KAAK,EAAGA,KAAK,IAAI;QACfxB,OAAO,CAACwB,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;QACxD,IAAI,CAACpH,YAAY,CAACqI,SAAS,CAAC,kCAAkC,CAAC;QAE/D;QACAtD,UAAU,CAAC,MAAK;UACd,IAAI,CAAC2D,kBAAkB,EAAE;QAC3B,CAAC,EAAE,IAAI,CAAC;MACV;KACD,CAAC,CACH;IAED;IAEA,IAAI,CAAC3E,aAAa,CAAC+C,GAAG,CACpB,IAAI,CAAChH,cAAc,CAAC8L,0BAA0B,CAC5C,IAAI,CAAC1L,YAAY,CAACkD,EAAE,CACrB,CAAC4D,SAAS,CAAC;MACVC,IAAI,EAAG4E,UAAe,IAAI;QACxB;QACA,IAAIA,UAAU,CAAC7D,MAAM,KAAK,IAAI,CAAC5H,aAAa,EAAE;UAC5C,IAAI,CAACa,iBAAiB,GAAG4K,UAAU,CAACjI,QAAQ;UAC5C,IAAI,CAACC,YAAY,GAAGgI,UAAU,CAACjI,QAAQ,CAAC,CAAC;UACzCgC,OAAO,CAACC,GAAG,CACT,sCAAsC,EACtC,IAAI,CAAC5E,iBAAiB,CACvB;UACD,IAAI,CAAChB,GAAG,CAACwL,aAAa,EAAE;;MAE5B,CAAC;MACDrE,KAAK,EAAGA,KAAU,IAAI;QACpBxB,OAAO,CAACwB,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACzD;KACD,CAAC,CACH;IAED;IACA,IAAI,CAACrD,aAAa,CAAC+C,GAAG,CACpB,IAAI,CAAChH,cAAc,CAACgM,8BAA8B,CAChD,IAAI,CAAC5L,YAAY,CAACkD,EAAE,CACrB,CAAC4D,SAAS,CAAC;MACVC,IAAI,EAAG8E,kBAAuB,IAAI;QAChC;QACA,IAAIA,kBAAkB,CAAC3I,EAAE,KAAK,IAAI,CAAClD,YAAY,CAACkD,EAAE,EAAE;UAClD,IAAI,CAAClD,YAAY,GAAG;YAAE,GAAG,IAAI,CAACA,YAAY;YAAE,GAAG6L;UAAkB,CAAE;UACnE,IAAI,CAAC9L,GAAG,CAACwL,aAAa,EAAE;;MAE5B,CAAC;MACDrE,KAAK,EAAGA,KAAU,IAAI;QACpBxB,OAAO,CAACwB,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;MAC/D;KACD,CAAC,CACH;EACH;EAEQuE,iBAAiBA,CAACK,SAAiB;IACzC,IAAI,CAAClM,cAAc,CAAC6L,iBAAiB,CAACK,SAAS,CAAC,CAAChF,SAAS,CAAC;MACzDC,IAAI,EAAEA,CAAA,KAAK,CAAE,CAAC;MACdG,KAAK,EAAGA,KAAK,IAAI;QACfxB,OAAO,CAACwB,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MAC1D;KACD,CAAC;EACJ;EAEA;EACA6E,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAACjI,WAAW,CAACkI,KAAK,IAAI,CAAC,IAAI,CAAChM,YAAY,EAAEkD,EAAE,EAAE;IAEvD,MAAMc,OAAO,GAAG,IAAI,CAACF,WAAW,CAACQ,GAAG,CAAC,SAAS,CAAC,EAAE2H,KAAK,EAAEC,IAAI,EAAE;IAC9D,IAAI,CAAClI,OAAO,EAAE;IAEd,MAAM0G,UAAU,GAAG,IAAI,CAACtK,gBAAgB,EAAE8C,EAAE,IAAI,IAAI,CAAC9C,gBAAgB,EAAE2H,GAAG;IAE1E,IAAI,CAAC2C,UAAU,EAAE;MACf,IAAI,CAAC5K,YAAY,CAACqI,SAAS,CAAC,0BAA0B,CAAC;MACvD;;IAGF;IACA,IAAI,CAACrH,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACsD,gBAAgB,EAAE;IAEvBsB,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE;MACjC3B,OAAO;MACP0G,UAAU;MACV1C,cAAc,EAAE,IAAI,CAAChI,YAAY,CAACkD;KACnC,CAAC;IAEF,IAAI,CAACtD,cAAc,CAACmM,WAAW,CAC7BrB,UAAU,EACV1G,OAAO,EACPmI,SAAS,EACT,MAAa,EACb,IAAI,CAACnM,YAAY,CAACkD,EAAE,CACrB,CAAC4D,SAAS,CAAC;MACVC,IAAI,EAAGqF,OAAY,IAAI;QACrB;QACA,MAAMjB,aAAa,GAAG,IAAI,CAAClL,QAAQ,CAACmL,IAAI,CACrCC,GAAG,IAAKA,GAAG,CAACnI,EAAE,KAAKkJ,OAAO,CAAClJ,EAAE,CAC/B;QACD,IAAI,CAACiI,aAAa,EAAE;UAClB,IAAI,CAAClL,QAAQ,CAACqL,IAAI,CAACc,OAAO,CAAC;UAC3B1G,OAAO,CAACC,GAAG,CACT,wCAAwC,EACxC,IAAI,CAAC1F,QAAQ,CAACyI,MAAM,CACrB;;QAGH;QACA,IAAI,CAAC5E,WAAW,CAACuI,KAAK,EAAE;QACxB,IAAI,CAACvL,gBAAgB,GAAG,KAAK;QAC7B,IAAI,CAACsD,gBAAgB,EAAE;QAEvB;QACA,IAAI,CAACrE,GAAG,CAACwL,aAAa,EAAE;QACxB1G,UAAU,CAAC,MAAK;UACd,IAAI,CAACgF,cAAc,EAAE;QACvB,CAAC,EAAE,EAAE,CAAC;MACR,CAAC;MACD3C,KAAK,EAAGA,KAAU,IAAI;QACpBxB,OAAO,CAACwB,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;QAC5D,IAAI,CAACpH,YAAY,CAACqI,SAAS,CAAC,mCAAmC,CAAC;QAChE,IAAI,CAACrH,gBAAgB,GAAG,KAAK;QAC7B,IAAI,CAACsD,gBAAgB,EAAE;MACzB;KACD,CAAC;EACJ;EAEAyF,cAAcA,CAAA;IACZhF,UAAU,CAAC,MAAK;MACd,IAAI,IAAI,CAACyH,iBAAiB,EAAE;QAC1B,MAAMC,OAAO,GAAG,IAAI,CAACD,iBAAiB,CAACpH,aAAa;QACpDqH,OAAO,CAACC,SAAS,GAAGD,OAAO,CAACE,YAAY;;IAE5C,CAAC,EAAE,GAAG,CAAC;EACT;EAEA;EACAC,gBAAgBA,CAACC,UAAgC;IAC/C,IAAI,CAACA,UAAU,EAAE,OAAO,YAAY;IAEpC,MAAMC,QAAQ,GAAGC,IAAI,CAACC,KAAK,CACzB,CAACzD,IAAI,CAAC0D,GAAG,EAAE,GAAG,IAAI1D,IAAI,CAACsD,UAAU,CAAC,CAACnD,OAAO,EAAE,IAAI,KAAK,CACtD;IAED,IAAIoD,QAAQ,GAAG,CAAC,EAAE,OAAO,aAAa;IACtC,IAAIA,QAAQ,GAAG,EAAE,EAAE,OAAO,UAAUA,QAAQ,MAAM;IAClD,IAAIA,QAAQ,GAAG,IAAI,EAAE,OAAO,UAAUC,IAAI,CAACC,KAAK,CAACF,QAAQ,GAAG,EAAE,CAAC,GAAG;IAClE,OAAO,UAAUC,IAAI,CAACC,KAAK,CAACF,QAAQ,GAAG,IAAI,CAAC,GAAG;EACjD;EAEA;EACAI,oBAAoBA,CAAClB,SAAiB;IACpC,OACE,IAAI,CAACxJ,aAAa,CAACwJ,SAAS,CAAC,IAAI;MAC/BmB,QAAQ,EAAE,CAAC;MACXC,QAAQ,EAAE,CAAC;MACXC,WAAW,EAAE,CAAC;MACdC,KAAK,EAAE;KACR;EAEL;EAEQC,oBAAoBA,CAC1BvB,SAAiB,EACjBwB,IAAkD;IAElD,IAAI,CAAChL,aAAa,CAACwJ,SAAS,CAAC,GAAG;MAC9B,GAAG,IAAI,CAACkB,oBAAoB,CAAClB,SAAS,CAAC;MACvC,GAAGwB;KACJ;EACH;EAEA;EAEA;EAEAC,cAAcA,CAAA;IACZ,IAAI,CAAC,IAAI,CAACnN,gBAAgB,EAAE8C,EAAE,EAAE;MAC9B,IAAI,CAACpD,YAAY,CAACqI,SAAS,CAAC,gCAAgC,CAAC;MAC7D;;IAGF,IAAI,CAACqF,YAAY,CAAClO,QAAQ,CAACmO,KAAK,CAAC;EACnC;EAEAC,cAAcA,CAAA;IACZ,IAAI,CAAC,IAAI,CAACtN,gBAAgB,EAAE8C,EAAE,EAAE;MAC9B,IAAI,CAACpD,YAAY,CAACqI,SAAS,CAAC,gCAAgC,CAAC;MAC7D;;IAGF;IACA,IAAI,CAACrD,kBAAkB,EAAE;IAEzB,IAAI,CAAC0I,YAAY,CAAClO,QAAQ,CAACqO,KAAK,CAAC;EACnC;EAEA;;;EAGAC,gBAAgBA,CAAA;IACd,IAAI,CAAC/K,OAAO,GAAG,CAAC,IAAI,CAACA,OAAO;IAE5B,IAAI,IAAI,CAAChD,WAAW,CAACgO,WAAW,EAAE;MAChC,IAAI,CAAChO,WAAW,CAACgO,WAAW,EAAE;;IAGhC,IAAI,CAAC/N,YAAY,CAACgO,WAAW,CAC3B,IAAI,CAACjL,OAAO,GAAG,kBAAkB,GAAG,mBAAmB,CACxD;EACH;EAEA;;;EAGAkL,YAAYA,CAAA;IACV,IAAI,CAACjL,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;IAC1C4C,OAAO,CAACC,GAAG,CACT,oBAAoB,EACpB,IAAI,CAAC7C,cAAc,GAAG,SAAS,GAAG,UAAU,CAC7C;IAED,IAAI,IAAI,CAACjD,WAAW,CAACmO,WAAW,EAAE;MAChC,IAAI,CAACnO,WAAW,CAACmO,WAAW,EAAE;;IAGhC,IAAI,CAAClO,YAAY,CAACgO,WAAW,CAC3B,IAAI,CAAChL,cAAc,GAAG,gBAAgB,GAAG,mBAAmB,CAC7D;EACH;EAEA;;;EAGAmL,OAAOA,CAAA;IACL,IAAI,IAAI,CAACtL,UAAU,EAAE;MACnB,IAAI,IAAI,CAAC9C,WAAW,CAACoO,OAAO,EAAE;QAC5B,IAAI,CAACpO,WAAW,CAACoO,OAAO,CAAC,IAAI,CAACtL,UAAU,CAACO,EAAE,CAAC,CAAC4D,SAAS,CAAC;UACrDC,IAAI,EAAEA,CAAA,KAAK;YACT,IAAI,CAACpE,UAAU,GAAG,IAAI;YACtB,IAAI,CAACC,eAAe,GAAG,KAAK;UAC9B,CAAC;UACDsE,KAAK,EAAGA,KAAK,IAAI;YACfxB,OAAO,CAACwB,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;YAC5C,IAAI,CAACpH,YAAY,CAACqI,SAAS,CAAC,kCAAkC,CAAC;UACjE;SACD,CAAC;OACH,MAAM;QACL;QACA,IAAI,CAACxF,UAAU,GAAG,IAAI;QACtB,IAAI,CAACC,eAAe,GAAG,KAAK;QAC5B,IAAI,CAAC9C,YAAY,CAACgO,WAAW,CAAC,eAAe,CAAC;;;EAGpD;EAEA;EACA;EAEA;EAEAI,cAAcA,CAACC,KAAa;IAC1B,IAAIA,KAAK,GAAG,IAAI,EAAE,OAAOA,KAAK,GAAG,IAAI;IACrC,IAAIA,KAAK,GAAG,OAAO,EAAE,OAAOtB,IAAI,CAACuB,KAAK,CAACD,KAAK,GAAG,IAAI,CAAC,GAAG,KAAK;IAC5D,OAAOtB,IAAI,CAACuB,KAAK,CAACD,KAAK,GAAG,OAAO,CAAC,GAAG,KAAK;EAC5C;EAEAE,YAAYA,CAACjC,OAAY;IACvB,MAAMkC,cAAc,GAAGlC,OAAO,CAACzB,WAAW,EAAE/B,IAAI,CAC7CkC,GAAQ,IAAK,CAACA,GAAG,CAACP,IAAI,EAAEgE,UAAU,CAAC,QAAQ,CAAC,CAC9C;IACD,IAAID,cAAc,EAAEtD,GAAG,EAAE;MACvB,MAAMwD,IAAI,GAAGjJ,QAAQ,CAACM,aAAa,CAAC,GAAG,CAAC;MACxC2I,IAAI,CAACC,IAAI,GAAGH,cAAc,CAACtD,GAAG;MAC9BwD,IAAI,CAACE,QAAQ,GAAGJ,cAAc,CAACnL,IAAI,IAAI,MAAM;MAC7CqL,IAAI,CAACG,MAAM,GAAG,QAAQ;MACtBpJ,QAAQ,CAACY,IAAI,CAACC,WAAW,CAACoI,IAAI,CAAC;MAC/BA,IAAI,CAACI,KAAK,EAAE;MACZrJ,QAAQ,CAACY,IAAI,CAAC0I,WAAW,CAACL,IAAI,CAAC;MAC/B,IAAI,CAAC1O,YAAY,CAACgO,WAAW,CAAC,wBAAwB,CAAC;;EAE3D;EAEA;EAEAgB,YAAYA,CAAA;IACV,IAAI,CAACjO,UAAU,GAAG,CAAC,IAAI,CAACA,UAAU;IAClC,IAAI,CAACH,UAAU,GAAG,IAAI,CAACG,UAAU;EACnC;EAEAkO,cAAcA,CAAA;IACZ,IAAI,CAAC/N,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;EACxC;EAEAgO,qBAAqBA,CAAA;IACnB;IACA,IAAI,CAACrP,MAAM,CACRsP,QAAQ,CAAC,CAAC,+BAA+B,CAAC,CAAC,CAC3CC,IAAI,CAAC,MAAK,CAAE,CAAC,CAAC,CACdC,KAAK,CAAEjI,KAAK,IAAI;MACfxB,OAAO,CAACwB,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3C;MACA,IAAI,CAACvH,MAAM,CAACsP,QAAQ,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAACE,KAAK,CAAC,MAAK;QACnD;QACAC,MAAM,CAACC,QAAQ,CAACZ,IAAI,GAAG,+BAA+B;MACxD,CAAC,CAAC;IACJ,CAAC,CAAC;EACN;EAEA;EAEAa,aAAaA,CAAA;IACX,IAAI,CAAC9O,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACC,kBAAkB,GAAG,KAAK;IAC/B,IAAI,CAACO,YAAY,GAAG,KAAK;IACzB,IAAI,CAACC,sBAAsB,GAAG,KAAK;IACnC,IAAI,CAACK,kBAAkB,GAAG,KAAK;EACjC;EAEAiO,oBAAoBA,CAACnD,OAAY,EAAEoD,KAAiB;IAClDA,KAAK,CAACC,cAAc,EAAE;IACtB,IAAI,CAACvO,eAAe,GAAGkL,OAAO;IAC9B,IAAI,CAACjL,mBAAmB,GAAG;MAAEC,CAAC,EAAEoO,KAAK,CAACE,OAAO;MAAErO,CAAC,EAAEmO,KAAK,CAACG;IAAO,CAAE;IACjE,IAAI,CAAC1O,sBAAsB,GAAG,IAAI;EACpC;EAEA2O,kBAAkBA,CAACxD,OAAY,EAAEoD,KAAiB;IAChDA,KAAK,CAACK,eAAe,EAAE;IACvB,IAAI,CAACtO,qBAAqB,GAAG6K,OAAO;IACpC,IAAI,CAACjL,mBAAmB,GAAG;MAAEC,CAAC,EAAEoO,KAAK,CAACE,OAAO;MAAErO,CAAC,EAAEmO,KAAK,CAACG;IAAO,CAAE;IACjE,IAAI,CAACrO,kBAAkB,GAAG,IAAI;EAChC;EAEAwO,UAAUA,CAACxM,KAAa;IACtB,IAAI,IAAI,CAAC/B,qBAAqB,EAAE;MAC9B,IAAI,CAACwO,cAAc,CAAC,IAAI,CAACxO,qBAAqB,CAAC2B,EAAE,EAAEI,KAAK,CAAC;;IAE3D,IAAI,CAAChC,kBAAkB,GAAG,KAAK;EACjC;EAEAyO,cAAcA,CAACjE,SAAiB,EAAExI,KAAa;IAC7C,IAAI,CAACwI,SAAS,IAAI,CAACxI,KAAK,EAAE;MACxBoC,OAAO,CAACwB,KAAK,CAAC,2CAA2C,CAAC;MAC1D;;IAGF;IACA,IAAI,CAACtH,cAAc,CAACoQ,cAAc,CAAClE,SAAS,EAAExI,KAAK,CAAC,CAACwD,SAAS,CAAC;MAC7DC,IAAI,EAAGkJ,MAAM,IAAI;QACf;QACA,MAAMC,YAAY,GAAG,IAAI,CAACjQ,QAAQ,CAACkQ,SAAS,CACzC9E,GAAG,IAAKA,GAAG,CAACnI,EAAE,KAAK4I,SAAS,CAC9B;QACD,IAAIoE,YAAY,KAAK,CAAC,CAAC,EAAE;UACvB,IAAI,CAACjQ,QAAQ,CAACiQ,YAAY,CAAC,GAAGD,MAAM;UACpC,IAAI,CAAClQ,GAAG,CAACwL,aAAa,EAAE;;QAG1B,IAAI,CAACzL,YAAY,CAACgO,WAAW,CAAC,YAAYxK,KAAK,UAAU,CAAC;MAC5D,CAAC;MACD4D,KAAK,EAAGA,KAAK,IAAI;QACfxB,OAAO,CAACwB,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QAClD,IAAI,CAACpH,YAAY,CAACqI,SAAS,CAAC,uCAAuC,CAAC;MACtE;KACD,CAAC;EACJ;EAEAiI,cAAcA,CAACC,QAAa,EAAEvI,MAAc;IAC1C,OAAOuI,QAAQ,CAACvI,MAAM,KAAKA,MAAM;EACnC;EAEAwI,cAAcA,CAAClE,OAAY;IACzB,IAAI,CAACkD,aAAa,EAAE;EACtB;EAEAiB,cAAcA,CAACnE,OAAY;IACzB,IAAI,CAACkD,aAAa,EAAE;EACtB;EAEAkB,aAAaA,CAACpE,OAAY;IACxB,IAAI,CAACA,OAAO,CAAClJ,EAAE,EAAE;MACfwC,OAAO,CAACwB,KAAK,CAAC,uCAAuC,CAAC;MACtD,IAAI,CAACpH,YAAY,CAACqI,SAAS,CAAC,gCAAgC,CAAC;MAC7D;;IAGF;IACA,MAAMsI,SAAS,GACbrE,OAAO,CAAC5B,MAAM,EAAEtH,EAAE,KAAK,IAAI,CAAChD,aAAa,IACzCkM,OAAO,CAAC3B,QAAQ,KAAK,IAAI,CAACvK,aAAa;IAEzC,IAAI,CAACuQ,SAAS,EAAE;MACd,IAAI,CAAC3Q,YAAY,CAACqI,SAAS,CACzB,mDAAmD,CACpD;MACD,IAAI,CAACmH,aAAa,EAAE;MACpB;;IAGF;IACA,IAAI,CAACoB,OAAO,CAAC,iDAAiD,CAAC,EAAE;MAC/D,IAAI,CAACpB,aAAa,EAAE;MACpB;;IAGF;IACA,IAAI,CAAC1P,cAAc,CAAC4Q,aAAa,CAACpE,OAAO,CAAClJ,EAAE,CAAC,CAAC4D,SAAS,CAAC;MACtDC,IAAI,EAAGkJ,MAAM,IAAI;QACf;QACA,IAAI,CAAChQ,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAAC0Q,MAAM,CAAEtF,GAAG,IAAKA,GAAG,CAACnI,EAAE,KAAKkJ,OAAO,CAAClJ,EAAE,CAAC;QAEpE,IAAI,CAACpD,YAAY,CAACgO,WAAW,CAAC,kBAAkB,CAAC;QACjD,IAAI,CAAC/N,GAAG,CAACwL,aAAa,EAAE;MAC1B,CAAC;MACDrE,KAAK,EAAGA,KAAK,IAAI;QACfxB,OAAO,CAACwB,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjD,IAAI,CAACpH,YAAY,CAACqI,SAAS,CAAC,0CAA0C,CAAC;MACzE;KACD,CAAC;IAEF,IAAI,CAACmH,aAAa,EAAE;EACtB;EAEA;EAEAsB,iBAAiBA,CAAA;IACf,IAAI,CAACpQ,eAAe,GAAG,CAAC,IAAI,CAACA,eAAe;EAC9C;EAEAqQ,mBAAmBA,CAACC,QAAa;IAC/B,IAAI,CAACvN,qBAAqB,GAAGuN,QAAQ;EACvC;EAEAC,oBAAoBA,CAACD,QAAa;IAChC,OAAOA,QAAQ,EAAEzN,MAAM,IAAI,EAAE;EAC/B;EAEA2N,WAAWA,CAAC1N,KAAU;IACpB,MAAM2N,cAAc,GAAG,IAAI,CAACnN,WAAW,CAACQ,GAAG,CAAC,SAAS,CAAC,EAAE2H,KAAK,IAAI,EAAE;IACnE,MAAMiF,UAAU,GAAGD,cAAc,GAAG3N,KAAK,CAACA,KAAK;IAC/C,IAAI,CAACQ,WAAW,CAACqN,UAAU,CAAC;MAAEnN,OAAO,EAAEkN;IAAU,CAAE,CAAC;IACpD,IAAI,CAAC1Q,eAAe,GAAG,KAAK;EAC9B;EAEA4Q,oBAAoBA,CAAA;IAClB,IAAI,CAAC3Q,kBAAkB,GAAG,CAAC,IAAI,CAACA,kBAAkB;EACpD;EAEA;EACA;EAEA;EACA;EAEA;EACA;EAEA;EAEA4Q,gBAAgBA,CAACtG,KAAa,EAAEqB,OAAY;IAC1C,OAAOA,OAAO,CAAClJ,EAAE,IAAIkJ,OAAO,CAACrE,GAAG,IAAIgD,KAAK,CAACuG,QAAQ,EAAE;EACtD;EAEA;EAEAC,cAAcA,CAAA;IACZ,MAAMC,WAAW,GAAG;MAClBtO,EAAE,EAAE,QAAQmG,IAAI,CAAC0D,GAAG,EAAE,EAAE;MACxB/I,OAAO,EAAE,mBAAmB,IAAIqF,IAAI,EAAE,CAACoI,kBAAkB,EAAE,EAAE;MAC7DnI,SAAS,EAAE,IAAID,IAAI,EAAE,CAACqI,WAAW,EAAE;MACnClH,MAAM,EAAE;QACNtH,EAAE,EAAE,IAAI,CAAC9C,gBAAgB,EAAE8C,EAAE,IAAI,WAAW;QAC5CoE,QAAQ,EAAE,IAAI,CAAClH,gBAAgB,EAAEkH,QAAQ,IAAI,WAAW;QACxDqK,KAAK,EACH,IAAI,CAACvR,gBAAgB,EAAEuR,KAAK,IAAI;OACnC;MACDpH,IAAI,EAAE,MAAM;MACZqH,MAAM,EAAE;KACT;IACD,IAAI,CAAC3R,QAAQ,CAACqL,IAAI,CAACkG,WAAW,CAAC;IAC/B,IAAI,CAACzR,GAAG,CAACwL,aAAa,EAAE;IACxB1G,UAAU,CAAC,MAAM,IAAI,CAACgF,cAAc,EAAE,EAAE,EAAE,CAAC;EAC7C;EAEAgI,mBAAmBA,CAAA;IACjB,OACE,IAAI,CAAC7R,YAAY,EAAE2I,OAAO,IAC1B,IAAI,CAAC3I,YAAY,EAAEyI,YAAY,EAAEC,MAAM,GAAG,CAAC,IAC3C,KAAK;EAET;EAEAoJ,UAAUA,CAAA;IACR,IAAI,CAACrR,kBAAkB,GAAG,KAAK;IAC/B;EACF;;EAEAsR,SAASA,CAACC,MAAc;IACtB,MAAMC,YAAY,GAAG1M,QAAQ,CAAC2M,aAAa,CACzC,oBAAoB,CACN;IAChB,IAAID,YAAY,EAAE;MAChB,MAAME,gBAAgB,GAAGF,YAAY,CAAChM,KAAK,CAACmM,SAAS,IAAI,UAAU;MACnE,MAAMC,YAAY,GAAGC,UAAU,CAC7BH,gBAAgB,CAACI,KAAK,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,IAAI,GAAG,CACvD;MACD,MAAMC,QAAQ,GAAG3F,IAAI,CAAC4F,GAAG,CAAC,GAAG,EAAE5F,IAAI,CAAC6F,GAAG,CAAC,CAAC,EAAEL,YAAY,GAAGL,MAAM,CAAC,CAAC;MAClEC,YAAY,CAAChM,KAAK,CAACmM,SAAS,GAAG,SAASI,QAAQ,GAAG;MACnD,IAAIA,QAAQ,GAAG,CAAC,EAAE;QAChBP,YAAY,CAACU,SAAS,CAAC/L,GAAG,CAAC,QAAQ,CAAC;OACrC,MAAM;QACLqL,YAAY,CAACU,SAAS,CAACC,MAAM,CAAC,QAAQ,CAAC;;;EAG7C;EAEAC,SAASA,CAAA;IACP,MAAMZ,YAAY,GAAG1M,QAAQ,CAAC2M,aAAa,CACzC,oBAAoB,CACN;IAChB,IAAID,YAAY,EAAE;MAChBA,YAAY,CAAChM,KAAK,CAACmM,SAAS,GAAG,UAAU;MACzCH,YAAY,CAACU,SAAS,CAACC,MAAM,CAAC,QAAQ,CAAC;;EAE3C;EAEA;EACA;EACA;EACA;EAEAE,gBAAgBA,CAACvI,IAAa;IAC5B,MAAMwI,KAAK,GAAG,IAAI,CAACC,SAAS,EAAE9N,aAAa;IAC3C,IAAI,CAAC6N,KAAK,EAAE;MACVrN,OAAO,CAACwB,KAAK,CAAC,8BAA8B,CAAC;MAC7C;;IAGF;IACA,IAAIqD,IAAI,KAAK,OAAO,EAAE;MACpBwI,KAAK,CAACE,MAAM,GAAG,SAAS;KACzB,MAAM,IAAI1I,IAAI,KAAK,OAAO,EAAE;MAC3BwI,KAAK,CAACE,MAAM,GAAG,SAAS;KACzB,MAAM,IAAI1I,IAAI,KAAK,UAAU,EAAE;MAC9BwI,KAAK,CAACE,MAAM,GAAG,iCAAiC;KACjD,MAAM;MACLF,KAAK,CAACE,MAAM,GAAG,KAAK;;IAGtB;IACAF,KAAK,CAAC9G,KAAK,GAAG,EAAE;IAEhB;IACA8G,KAAK,CAACnE,KAAK,EAAE;IACb,IAAI,CAACnO,kBAAkB,GAAG,KAAK;EACjC;EAEAyS,iBAAiBA,CAAC5J,SAAwB;IACxC,IAAI,CAACA,SAAS,EAAE,OAAO,EAAE;IAEzB,MAAM6J,IAAI,GAAG,IAAI9J,IAAI,CAACC,SAAS,CAAC;IAChC,OAAO6J,IAAI,CAAC1B,kBAAkB,CAAC,OAAO,EAAE;MACtC2B,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;KACT,CAAC;EACJ;EAEAC,mBAAmBA,CAAChK,SAAwB;IAC1C,IAAI,CAACA,SAAS,EAAE,OAAO,EAAE;IAEzB,MAAM6J,IAAI,GAAG,IAAI9J,IAAI,CAACC,SAAS,CAAC;IAChC,MAAMiK,KAAK,GAAG,IAAIlK,IAAI,EAAE;IACxB,MAAMmK,SAAS,GAAG,IAAInK,IAAI,CAACkK,KAAK,CAAC;IACjCC,SAAS,CAACC,OAAO,CAACD,SAAS,CAACE,OAAO,EAAE,GAAG,CAAC,CAAC;IAE1C,IAAIP,IAAI,CAACQ,YAAY,EAAE,KAAKJ,KAAK,CAACI,YAAY,EAAE,EAAE;MAChD,OAAO,aAAa;KACrB,MAAM,IAAIR,IAAI,CAACQ,YAAY,EAAE,KAAKH,SAAS,CAACG,YAAY,EAAE,EAAE;MAC3D,OAAO,MAAM;KACd,MAAM;MACL,OAAOR,IAAI,CAACS,kBAAkB,CAAC,OAAO,CAAC;;EAE3C;EAEAC,oBAAoBA,CAAC7P,OAAe;IAClC,IAAI,CAACA,OAAO,EAAE,OAAO,EAAE;IAEvB;IACA,MAAM8P,QAAQ,GAAG,sBAAsB;IACvC,OAAO9P,OAAO,CAAC+P,OAAO,CACpBD,QAAQ,EACR,qEAAqE,CACtE;EACH;EAEAE,uBAAuBA,CAACjJ,KAAa;IACnC,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO,IAAI;IAE5B,MAAMkJ,cAAc,GAAG,IAAI,CAAChU,QAAQ,CAAC8K,KAAK,CAAC;IAC3C,MAAMmJ,eAAe,GAAG,IAAI,CAACjU,QAAQ,CAAC8K,KAAK,GAAG,CAAC,CAAC;IAEhD,IAAI,CAACkJ,cAAc,EAAE3K,SAAS,IAAI,CAAC4K,eAAe,EAAE5K,SAAS,EAAE,OAAO,KAAK;IAE3E,MAAM6K,WAAW,GAAG,IAAI9K,IAAI,CAAC4K,cAAc,CAAC3K,SAAS,CAAC,CAACqK,YAAY,EAAE;IACrE,MAAMS,YAAY,GAAG,IAAI/K,IAAI,CAAC6K,eAAe,CAAC5K,SAAS,CAAC,CAACqK,YAAY,EAAE;IAEvE,OAAOQ,WAAW,KAAKC,YAAY;EACrC;EAEAC,gBAAgBA,CAACtJ,KAAa;IAC5B,MAAMkJ,cAAc,GAAG,IAAI,CAAChU,QAAQ,CAAC8K,KAAK,CAAC;IAC3C,MAAMuJ,WAAW,GAAG,IAAI,CAACrU,QAAQ,CAAC8K,KAAK,GAAG,CAAC,CAAC;IAE5C,IAAI,CAACuJ,WAAW,EAAE,OAAO,IAAI;IAE7B,OAAOL,cAAc,CAACzJ,MAAM,EAAEtH,EAAE,KAAKoR,WAAW,CAAC9J,MAAM,EAAEtH,EAAE;EAC7D;EAEAqR,oBAAoBA,CAACxJ,KAAa;IAChC,MAAMkJ,cAAc,GAAG,IAAI,CAAChU,QAAQ,CAAC8K,KAAK,CAAC;IAC3C,MAAMmJ,eAAe,GAAG,IAAI,CAACjU,QAAQ,CAAC8K,KAAK,GAAG,CAAC,CAAC;IAEhD,IAAI,CAACmJ,eAAe,EAAE,OAAO,IAAI;IAEjC,OAAOD,cAAc,CAACzJ,MAAM,EAAEtH,EAAE,KAAKgR,eAAe,CAAC1J,MAAM,EAAEtH,EAAE;EACjE;EAEA0H,cAAcA,CAACwB,OAAY;IACzB;IACA,IAAIA,OAAO,CAAC7B,IAAI,EAAE;MAChB,IAAI6B,OAAO,CAAC7B,IAAI,KAAK,OAAO,IAAI6B,OAAO,CAAC7B,IAAI,KAAK,OAAO,EAAE,OAAO,OAAO;MACxE,IAAI6B,OAAO,CAAC7B,IAAI,KAAK,OAAO,IAAI6B,OAAO,CAAC7B,IAAI,KAAK,OAAO,EAAE,OAAO,OAAO;MACxE,IAAI6B,OAAO,CAAC7B,IAAI,KAAK,OAAO,IAAI6B,OAAO,CAAC7B,IAAI,KAAK,OAAO,EAAE,OAAO,OAAO;MACxE,IAAI6B,OAAO,CAAC7B,IAAI,KAAK,eAAe,EAAE,OAAO,OAAO;MACpD,IAAI6B,OAAO,CAAC7B,IAAI,KAAK,MAAM,IAAI6B,OAAO,CAAC7B,IAAI,KAAK,MAAM,EAAE,OAAO,MAAM;;IAGvE;IACA,IAAI6B,OAAO,CAACzB,WAAW,IAAIyB,OAAO,CAACzB,WAAW,CAACjC,MAAM,GAAG,CAAC,EAAE;MACzD,MAAM8L,UAAU,GAAGpI,OAAO,CAACzB,WAAW,CAAC,CAAC,CAAC;MACzC,IAAI6J,UAAU,CAACjK,IAAI,EAAEgE,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,OAAO;MACzD,IAAIiG,UAAU,CAACjK,IAAI,EAAEgE,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,OAAO;MACzD,IAAIiG,UAAU,CAACjK,IAAI,EAAEgE,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,OAAO;MACzD,OAAO,MAAM;;IAGf;IACA,IAAInC,OAAO,CAACqI,QAAQ,IAAIrI,OAAO,CAACsI,QAAQ,IAAItI,OAAO,CAACuI,KAAK,EAAE,OAAO,OAAO;IAEzE,OAAO,MAAM;EACf;EAEAC,QAAQA,CAACxI,OAAY;IACnB;IACA,IAAIA,OAAO,CAAC7B,IAAI,KAAK,OAAO,IAAI6B,OAAO,CAAC7B,IAAI,KAAK,OAAO,EAAE;MACxD,OAAO,IAAI;;IAGb;IACA,MAAMsK,kBAAkB,GACtBzI,OAAO,CAACzB,WAAW,EAAES,IAAI,CAAEN,GAAQ,IAAI;MACrC,OAAOA,GAAG,CAACP,IAAI,EAAEgE,UAAU,CAAC,QAAQ,CAAC,IAAIzD,GAAG,CAACP,IAAI,KAAK,OAAO;IAC/D,CAAC,CAAC,IAAI,KAAK;IAEb;IACA,MAAMuK,WAAW,GAAG,CAAC,EAAE1I,OAAO,CAAC2I,QAAQ,IAAI3I,OAAO,CAACuF,KAAK,CAAC;IAEzD,OAAOkD,kBAAkB,IAAIC,WAAW;EAC1C;EAEAE,OAAOA,CAAC5I,OAAY;IAClB;IACA,IAAIA,OAAO,CAAC7B,IAAI,KAAK,MAAM,IAAI6B,OAAO,CAAC7B,IAAI,KAAK,MAAM,EAAE;MACtD,OAAO,IAAI;;IAGb;IACA,MAAM0K,iBAAiB,GACrB7I,OAAO,CAACzB,WAAW,EAAES,IAAI,CAAEN,GAAQ,IAAI;MACrC,OAAO,CAACA,GAAG,CAACP,IAAI,EAAEgE,UAAU,CAAC,QAAQ,CAAC,IAAIzD,GAAG,CAACP,IAAI,KAAK,OAAO;IAChE,CAAC,CAAC,IAAI,KAAK;IAEb,OAAO0K,iBAAiB;EAC1B;EAEAC,WAAWA,CAAC9I,OAAY;IACtB;IACA,IAAIA,OAAO,CAAC2I,QAAQ,EAAE;MACpB,OAAO3I,OAAO,CAAC2I,QAAQ;;IAEzB,IAAI3I,OAAO,CAACuF,KAAK,EAAE;MACjB,OAAOvF,OAAO,CAACuF,KAAK;;IAGtB;IACA,MAAMwD,eAAe,GAAG/I,OAAO,CAACzB,WAAW,EAAE/B,IAAI,CAC9CkC,GAAQ,IAAKA,GAAG,CAACP,IAAI,EAAEgE,UAAU,CAAC,QAAQ,CAAC,IAAIzD,GAAG,CAACP,IAAI,KAAK,OAAO,CACrE;IAED,IAAI4K,eAAe,EAAE;MACnB,OAAOA,eAAe,CAACnK,GAAG,IAAImK,eAAe,CAAClK,IAAI,IAAI,EAAE;;IAG1D,OAAO,EAAE;EACX;EAEAmK,WAAWA,CAAChJ,OAAY;IACtB,MAAMkC,cAAc,GAAGlC,OAAO,CAACzB,WAAW,EAAE/B,IAAI,CAC7CkC,GAAQ,IAAK,CAACA,GAAG,CAACP,IAAI,EAAEgE,UAAU,CAAC,QAAQ,CAAC,CAC9C;IACD,OAAOD,cAAc,EAAEnL,IAAI,IAAI,SAAS;EAC1C;EAEAkS,WAAWA,CAACjJ,OAAY;IACtB,MAAMkC,cAAc,GAAGlC,OAAO,CAACzB,WAAW,EAAE/B,IAAI,CAC7CkC,GAAQ,IAAK,CAACA,GAAG,CAACP,IAAI,EAAEgE,UAAU,CAAC,QAAQ,CAAC,CAC9C;IACD,IAAI,CAACD,cAAc,EAAEpD,IAAI,EAAE,OAAO,EAAE;IAEpC,MAAMiD,KAAK,GAAGG,cAAc,CAACpD,IAAI;IACjC,IAAIiD,KAAK,GAAG,IAAI,EAAE,OAAOA,KAAK,GAAG,IAAI;IACrC,IAAIA,KAAK,GAAG,OAAO,EAAE,OAAOtB,IAAI,CAACuB,KAAK,CAACD,KAAK,GAAG,IAAI,CAAC,GAAG,KAAK;IAC5D,OAAOtB,IAAI,CAACuB,KAAK,CAACD,KAAK,GAAG,OAAO,CAAC,GAAG,KAAK;EAC5C;EAEAmH,WAAWA,CAAClJ,OAAY;IACtB,MAAMkC,cAAc,GAAGlC,OAAO,CAACzB,WAAW,EAAE/B,IAAI,CAC7CkC,GAAQ,IAAK,CAACA,GAAG,CAACP,IAAI,EAAEgE,UAAU,CAAC,QAAQ,CAAC,CAC9C;IACD,IAAI,CAACD,cAAc,EAAE/D,IAAI,EAAE,OAAO,aAAa;IAE/C,IAAI+D,cAAc,CAAC/D,IAAI,CAACgE,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,mBAAmB;IACxE,IAAID,cAAc,CAAC/D,IAAI,CAACgE,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,mBAAmB;IACxE,IAAID,cAAc,CAAC/D,IAAI,CAACgL,QAAQ,CAAC,KAAK,CAAC,EAAE,OAAO,iBAAiB;IACjE,IAAIjH,cAAc,CAAC/D,IAAI,CAACgL,QAAQ,CAAC,MAAM,CAAC,EAAE,OAAO,kBAAkB;IACnE,IAAIjH,cAAc,CAAC/D,IAAI,CAACgL,QAAQ,CAAC,OAAO,CAAC,EAAE,OAAO,mBAAmB;IACrE,OAAO,aAAa;EACtB;EAEAC,YAAYA,CAAC1N,MAAc;IACzB;IACA,MAAM2N,MAAM,GAAG,CACb,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,CACV;IACD,MAAM1K,KAAK,GAAGjD,MAAM,CAAC4N,UAAU,CAAC,CAAC,CAAC,GAAGD,MAAM,CAAC/M,MAAM;IAClD,OAAO+M,MAAM,CAAC1K,KAAK,CAAC;EACtB;EAEA;EACA4K,cAAcA,CAACvJ,OAAY,EAAEoD,KAAU,GAAS;EAEhDoG,aAAaA,CAACpG,KAAU;IACtB;IACA,IAAI,CAACqG,qBAAqB,EAAE;EAC9B;EAEAC,cAAcA,CAACtG,KAAoB;IACjC,IAAIA,KAAK,CAACuG,GAAG,KAAK,OAAO,IAAI,CAACvG,KAAK,CAACwG,QAAQ,EAAE;MAC5CxG,KAAK,CAACC,cAAc,EAAE;MACtB,IAAI,CAAC1D,WAAW,EAAE;;EAEtB;EAEAkK,YAAYA,CAAA;IACV;EAAA;EAGFC,WAAWA,CAAA;IACT;EAAA;EAGFC,QAAQA,CAAC3G,KAAU;IACjB;IACA,MAAMjD,OAAO,GAAGiD,KAAK,CAACb,MAAM;IAC5B,IACEpC,OAAO,CAACC,SAAS,KAAK,CAAC,IACvB,IAAI,CAACjM,eAAe,IACpB,CAAC,IAAI,CAACD,aAAa,EACnB;MACA,IAAI,CAACwJ,gBAAgB,EAAE;;EAE3B;EAEAsM,eAAeA,CAACtO,MAAc,GAAS;EAEvCuO,WAAWA,CAAC7G,KAAU,EAAEpD,OAAY;IAClC1G,OAAO,CAACC,GAAG,CACT,oDAAoD,EACpDyG,OAAO,CAAClJ,EAAE,EACVsM,KAAK,CAACb,MAAM,CAAC2H,GAAG,CACjB;EACH;EAEAC,YAAYA,CAAC/G,KAAU,EAAEpD,OAAY;IACnC1G,OAAO,CAACwB,KAAK,CAAC,+CAA+C,EAAEkF,OAAO,CAAClJ,EAAE,EAAE;MACzEoT,GAAG,EAAE9G,KAAK,CAACb,MAAM,CAAC2H,GAAG;MACrBpP,KAAK,EAAEsI;KACR,CAAC;IACF;IACAA,KAAK,CAACb,MAAM,CAAC2H,GAAG,GACd,4WAA4W;EAChX;EAEAE,eAAeA,CAACpK,OAAY;IAC1B,MAAM+I,eAAe,GAAG/I,OAAO,CAACzB,WAAW,EAAE/B,IAAI,CAAEkC,GAAQ,IACzDA,GAAG,CAACP,IAAI,EAAEgE,UAAU,CAAC,QAAQ,CAAC,CAC/B;IACD,IAAI4G,eAAe,EAAEnK,GAAG,EAAE;MACxB,IAAI,CAACvJ,aAAa,GAAG;QACnBuJ,GAAG,EAAEmK,eAAe,CAACnK,GAAG;QACxB7H,IAAI,EAAEgS,eAAe,CAAChS,IAAI,IAAI,OAAO;QACrC+H,IAAI,EAAE,IAAI,CAACgD,cAAc,CAACiH,eAAe,CAACjK,IAAI,IAAI,CAAC,CAAC;QACpDkB,OAAO,EAAEA;OACV;MACD,IAAI,CAAC5K,eAAe,GAAG,IAAI;;EAE/B;EAEAiV,gBAAgBA,CAAA;IACd,IAAI,CAACjV,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACC,aAAa,GAAG,IAAI;EAC3B;EAEAiV,aAAaA,CAAA;IACX,IAAI,IAAI,CAACjV,aAAa,EAAEuJ,GAAG,EAAE;MAC3B,MAAMwD,IAAI,GAAGjJ,QAAQ,CAACM,aAAa,CAAC,GAAG,CAAC;MACxC2I,IAAI,CAACC,IAAI,GAAG,IAAI,CAAChN,aAAa,CAACuJ,GAAG;MAClCwD,IAAI,CAACE,QAAQ,GAAG,IAAI,CAACjN,aAAa,CAAC0B,IAAI,IAAI,OAAO;MAClDqL,IAAI,CAACG,MAAM,GAAG,QAAQ;MACtBpJ,QAAQ,CAACY,IAAI,CAACC,WAAW,CAACoI,IAAI,CAAC;MAC/BA,IAAI,CAACI,KAAK,EAAE;MACZrJ,QAAQ,CAACY,IAAI,CAAC0I,WAAW,CAACL,IAAI,CAAC;MAC/B,IAAI,CAAC1O,YAAY,CAACgO,WAAW,CAAC,wBAAwB,CAAC;MACvDpI,OAAO,CAACC,GAAG,CACT,qCAAqC,EACrC,IAAI,CAAClE,aAAa,CAAC0B,IAAI,CACxB;;EAEL;EAEA;EACA;EACA;EAEAwT,cAAcA,CAAA;IACZ,IAAI,CAAC,IAAI,CAAChW,WAAW,CAACuL,IAAI,EAAE,EAAE;MAC5B,IAAI,CAACtL,aAAa,GAAG,EAAE;MACvB;;IAGF,IAAI,CAACA,aAAa,GAAG,IAAI,CAACX,QAAQ,CAAC0Q,MAAM,CACtCvE,OAAO,IACNA,OAAO,CAACpI,OAAO,EACX4S,WAAW,EAAE,CACdrB,QAAQ,CAAC,IAAI,CAAC5U,WAAW,CAACiW,WAAW,EAAE,CAAC,IAC3CxK,OAAO,CAAC5B,MAAM,EAAElD,QAAQ,EACpBsP,WAAW,EAAE,CACdrB,QAAQ,CAAC,IAAI,CAAC5U,WAAW,CAACiW,WAAW,EAAE,CAAC,CAC9C;EACH;EAEAC,mBAAmBA,CAAA;IACjB,IAAI,CAACF,cAAc,EAAE;EACvB;EAEAG,WAAWA,CAAA;IACT,IAAI,CAACnW,WAAW,GAAG,EAAE;IACrB,IAAI,CAACC,aAAa,GAAG,EAAE;EACzB;EAEAmW,aAAaA,CAACjL,SAAiB;IAC7B,MAAMkL,cAAc,GAAGzR,QAAQ,CAACC,cAAc,CAAC,WAAWsG,SAAS,EAAE,CAAC;IACtE,IAAIkL,cAAc,EAAE;MAClBA,cAAc,CAACC,cAAc,CAAC;QAAEC,QAAQ,EAAE,QAAQ;QAAEC,KAAK,EAAE;MAAQ,CAAE,CAAC;MACtE;MACAH,cAAc,CAACrE,SAAS,CAAC/L,GAAG,CAAC,WAAW,CAAC;MACzC/B,UAAU,CAAC,MAAK;QACdmS,cAAc,CAACrE,SAAS,CAACC,MAAM,CAAC,WAAW,CAAC;MAC9C,CAAC,EAAE,IAAI,CAAC;;EAEZ;EAEA;EACA;EACA;EAEAwE,gBAAgBA,CAAA;IACd,IAAI,CAACnW,sBAAsB,GAAG,KAAK;IACnC,IAAI,CAACC,eAAe,GAAG,IAAI;EAC7B;EAEA;EACA;EACA;EACA;EAEA;EACA;EAEA;EACA;EACA;EACA;EAEQsM,YAAYA,CAAChL,QAAkB;IACrCkD,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE;MAC5CnD,QAAQ;MACRpC,gBAAgB,EAAE,IAAI,CAACA,gBAAgB;MACvCJ,YAAY,EAAE,IAAI,CAACA,YAAY,EAAEkD,EAAE;MACnChD,aAAa,EAAE,IAAI,CAACA;KACrB,CAAC;IAEF,IAAI,CAAC,IAAI,CAACE,gBAAgB,EAAE;MAC1BsF,OAAO,CAACwB,KAAK,CAAC,uCAAuC,CAAC;MACtD,IAAI,CAACpH,YAAY,CAACqI,SAAS,CAAC,gCAAgC,CAAC;MAC7D;;IAGF,MAAMkP,WAAW,GAAG,IAAI,CAACjX,gBAAgB,CAAC8C,EAAE,IAAI,IAAI,CAAC9C,gBAAgB,CAAC2H,GAAG;IACzE,IAAI,CAACsP,WAAW,EAAE;MAChB3R,OAAO,CAACwB,KAAK,CAAC,wCAAwC,CAAC;MACvD,IAAI,CAACpH,YAAY,CAACqI,SAAS,CAAC,gCAAgC,CAAC;MAC7D;;IAGFzC,OAAO,CAACC,GAAG,CAAC,+BAA+BnD,QAAQ,gBAAgB,EAAE;MACnE6U,WAAW;MACXC,aAAa,EACX,IAAI,CAAClX,gBAAgB,CAACkH,QAAQ,IAAI,IAAI,CAAClH,gBAAgB,CAAC+C,IAAI;MAC9D6E,cAAc,EAAE,IAAI,CAAChI,YAAY,EAAEkD;KACpC,CAAC;IAEF,IAAI,CAACX,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,QAAQ,GAAGA,QAAQ,KAAKlD,QAAQ,CAACmO,KAAK,GAAG,OAAO,GAAG,OAAO;IAC/D,IAAI,CAAChL,YAAY,GAAG,CAAC;IAErB;IACA,IAAI,CAAC8U,cAAc,EAAE;IAErB;IACA,IAAI,CAAC1X,WAAW,CACb2N,YAAY,CAAC6J,WAAW,EAAE7U,QAAQ,EAAE,IAAI,CAACxC,YAAY,EAAEkD,EAAE,CAAC,CAC1D4D,SAAS,CAAC;MACTC,IAAI,EAAGK,IAAU,IAAI;QACnB,IAAI,CAACzE,UAAU,GAAGyE,IAAI;QACtB,IAAI,CAACxE,eAAe,GAAG,KAAK;QAC5B,IAAI,CAAC9C,YAAY,CAACgO,WAAW,CAC3B,SAAStL,QAAQ,KAAKlD,QAAQ,CAACmO,KAAK,GAAG,OAAO,GAAG,OAAO,SAAS,CAClE;QAED/H,OAAO,CAACC,GAAG,CACT,qEAAqE,CACtE;MACH,CAAC;MACDuB,KAAK,EAAGA,KAAU,IAAI;QACpBxB,OAAO,CAACwB,KAAK,CAAC,wCAAwC,EAAE;UACtDA,KAAK,EAAEA,KAAK,CAACkF,OAAO,IAAIlF,KAAK;UAC7BmQ,WAAW;UACX7U,QAAQ;UACRwF,cAAc,EAAE,IAAI,CAAChI,YAAY,EAAEkD;SACpC,CAAC;QAEF,IAAI,CAAC+K,OAAO,EAAE;QACd,IAAI,CAACnO,YAAY,CAACqI,SAAS,CAAC,wCAAwC,CAAC;MACvE;KACD,CAAC;EACN;EAEAqP,UAAUA,CAACxQ,YAA0B;IACnC,IAAI,CAACnH,WAAW,CAAC2X,UAAU,CAACxQ,YAAY,CAAC,CAACF,SAAS,CAAC;MAClDC,IAAI,EAAGK,IAAU,IAAI;QACnB,IAAI,CAACzE,UAAU,GAAGyE,IAAI;QACtB,IAAI,CAAC7E,QAAQ,GAAG,IAAI;QACpB,IAAI,CAACK,eAAe,GAAG,IAAI;QAC3B,IAAI,CAACJ,QAAQ,GAAG4E,IAAI,CAACmD,IAAI,KAAKjL,QAAQ,CAACmO,KAAK,GAAG,OAAO,GAAG,OAAO;QAChE,IAAI,CAAC8J,cAAc,EAAE;QACrB,IAAI,CAACzX,YAAY,CAACgO,WAAW,CAAC,eAAe,CAAC;MAChD,CAAC;MACD5G,KAAK,EAAGA,KAAU,IAAI;QACpBxB,OAAO,CAACwB,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,IAAI,CAACpH,YAAY,CAACqI,SAAS,CAAC,yCAAyC,CAAC;MACxE;KACD,CAAC;EACJ;EAEAsP,UAAUA,CAACzQ,YAA0B;IACnC,IAAI,CAACnH,WAAW,CAAC4X,UAAU,CAACzQ,YAAY,CAAC9D,EAAE,EAAE,eAAe,CAAC,CAAC4D,SAAS,CAAC;MACtEC,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAACjH,YAAY,CAACgO,WAAW,CAAC,cAAc,CAAC;MAC/C,CAAC;MACD5G,KAAK,EAAGA,KAAK,IAAI;QACfxB,OAAO,CAACwB,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,IAAI,CAACpH,YAAY,CAACqI,SAAS,CAAC,iCAAiC,CAAC;MAChE;KACD,CAAC;EACJ;EAEQoP,cAAcA,CAAA;IACpB,IAAI,CAAC9U,YAAY,GAAG,CAAC;IACrB,IAAI,CAACC,SAAS,GAAGgV,WAAW,CAAC,MAAK;MAChC,IAAI,CAACjV,YAAY,EAAE;MACnB,IAAI,CAAC1C,GAAG,CAACwL,aAAa,EAAE;IAC1B,CAAC,EAAE,IAAI,CAAC;EACV;EAEQoM,cAAcA,CAAA;IACpB,IAAI,IAAI,CAACjV,SAAS,EAAE;MAClBkV,aAAa,CAAC,IAAI,CAAClV,SAAS,CAAC;MAC7B,IAAI,CAACA,SAAS,GAAG,IAAI;;IAGvB,IAAI,CAACH,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,YAAY,GAAG,CAAC;IACrB,IAAI,CAACE,UAAU,GAAG,IAAI;IACtB,IAAI,CAACC,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACC,OAAO,GAAG,KAAK;IACpB,IAAI,CAACC,cAAc,GAAG,IAAI;EAC5B;EAEA;EACA+U,UAAUA,CAAA;IACR,IAAI,CAAC,IAAI,CAAClV,UAAU,EAAE;IAEtB,IAAI,CAACE,OAAO,GAAG,CAAC,IAAI,CAACA,OAAO;IAE5B;IACA,IAAI,CAAChD,WAAW,CACbiY,WAAW,CACV,IAAI,CAACnV,UAAU,CAACO,EAAE,EAClBiJ,SAAS;IAAE;IACX,CAAC,IAAI,CAACtJ,OAAO,CAAC;KACf,CACAiE,SAAS,CAAC;MACTC,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAACjH,YAAY,CAACgO,WAAW,CAC3B,IAAI,CAACjL,OAAO,GAAG,aAAa,GAAG,cAAc,CAC9C;MACH,CAAC;MACDqE,KAAK,EAAGA,KAAK,IAAI;QACfxB,OAAO,CAACwB,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C;QACA,IAAI,CAACrE,OAAO,GAAG,CAAC,IAAI,CAACA,OAAO;QAC5B,IAAI,CAAC/C,YAAY,CAACqI,SAAS,CAAC,oCAAoC,CAAC;MACnE;KACD,CAAC;EACN;EAEA6F,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAACrL,UAAU,EAAE;IAEtB,IAAI,CAACG,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;IAE1C;IACA,IAAI,CAACjD,WAAW,CACbiY,WAAW,CACV,IAAI,CAACnV,UAAU,CAACO,EAAE,EAClB,IAAI,CAACJ,cAAc;IAAE;IACrBqJ,SAAS,CAAC;KACX,CACArF,SAAS,CAAC;MACTC,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAACjH,YAAY,CAACgO,WAAW,CAC3B,IAAI,CAAChL,cAAc,GAAG,gBAAgB,GAAG,mBAAmB,CAC7D;MACH,CAAC;MACDoE,KAAK,EAAGA,KAAK,IAAI;QACfxB,OAAO,CAACwB,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C;QACA,IAAI,CAACpE,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;QAC1C,IAAI,CAAChD,YAAY,CAACqI,SAAS,CAAC,wCAAwC,CAAC;MACvE;KACD,CAAC;EACN;EAEA4P,kBAAkBA,CAAC7K,QAAgB;IACjC,MAAM8K,KAAK,GAAGnL,IAAI,CAACC,KAAK,CAACI,QAAQ,GAAG,IAAI,CAAC;IACzC,MAAM+K,OAAO,GAAGpL,IAAI,CAACC,KAAK,CAAEI,QAAQ,GAAG,IAAI,GAAI,EAAE,CAAC;IAClD,MAAMgL,OAAO,GAAGhL,QAAQ,GAAG,EAAE;IAE7B,IAAI8K,KAAK,GAAG,CAAC,EAAE;MACb,OAAO,GAAGA,KAAK,IAAIC,OAAO,CAAC3G,QAAQ,EAAE,CAAC6G,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAID,OAAO,CAC9D5G,QAAQ,EAAE,CACV6G,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;;IAEvB,OAAO,GAAGF,OAAO,IAAIC,OAAO,CAAC5G,QAAQ,EAAE,CAAC6G,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EAC5D;EAEA;EACA;EAEMC,mBAAmBA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACvB,IAAI;QACF;QACA,IAAI,CAACC,SAAS,CAACC,YAAY,IAAI,CAACD,SAAS,CAACC,YAAY,CAACC,YAAY,EAAE;UACnE,MAAM,IAAIC,KAAK,CACb,yDAAyD,CAC1D;;QAGH;QACA,IAAI,CAACtJ,MAAM,CAACuJ,aAAa,EAAE;UACzB,MAAM,IAAID,KAAK,CACb,uDAAuD,CACxD;;QAGH;QACA,MAAME,MAAM,SAASL,SAAS,CAACC,YAAY,CAACC,YAAY,CAAC;UACvDI,KAAK,EAAE;YACLC,gBAAgB,EAAE,IAAI;YACtBC,gBAAgB,EAAE,IAAI;YACtBC,eAAe,EAAE,IAAI;YACrBC,UAAU,EAAE,KAAK;YACjBC,YAAY,EAAE;;SAEjB,CAAC;QAEF;QACA,IAAIC,QAAQ,GAAG,wBAAwB;QACvC,IAAI,CAACR,aAAa,CAACS,eAAe,CAACD,QAAQ,CAAC,EAAE;UAC5CA,QAAQ,GAAG,YAAY;UACvB,IAAI,CAACR,aAAa,CAACS,eAAe,CAACD,QAAQ,CAAC,EAAE;YAC5CA,QAAQ,GAAG,WAAW;YACtB,IAAI,CAACR,aAAa,CAACS,eAAe,CAACD,QAAQ,CAAC,EAAE;cAC5CA,QAAQ,GAAG,EAAE,CAAC,CAAC;;;;QAKrB;QACAd,KAAI,CAACrW,aAAa,GAAG,IAAI2W,aAAa,CAACC,MAAM,EAAE;UAC7CO,QAAQ,EAAEA,QAAQ,IAAIhN;SACvB,CAAC;QAEF;QACAkM,KAAI,CAACpW,WAAW,GAAG,EAAE;QACrBoW,KAAI,CAACxW,gBAAgB,GAAG,IAAI;QAC5BwW,KAAI,CAACvW,sBAAsB,GAAG,CAAC;QAC/BuW,KAAI,CAACtW,mBAAmB,GAAG,WAAW;QAEtC;QACAsW,KAAI,CAACnW,cAAc,GAAGwV,WAAW,CAAC,MAAK;UACrCW,KAAI,CAACvW,sBAAsB,EAAE;UAC7B;UACAuW,KAAI,CAACgB,iBAAiB,EAAE;UACxBhB,KAAI,CAACtY,GAAG,CAACwL,aAAa,EAAE;QAC1B,CAAC,EAAE,IAAI,CAAC;QAER;QACA8M,KAAI,CAACrW,aAAa,CAACsX,eAAe,GAAI9J,KAAK,IAAI;UAC7C,IAAIA,KAAK,CAAClC,IAAI,CAACpC,IAAI,GAAG,CAAC,EAAE;YACvBmN,KAAI,CAACpW,WAAW,CAACqJ,IAAI,CAACkE,KAAK,CAAClC,IAAI,CAAC;;QAErC,CAAC;QAED+K,KAAI,CAACrW,aAAa,CAACuX,MAAM,GAAG,MAAK;UAC/BlB,KAAI,CAACmB,oBAAoB,EAAE;QAC7B,CAAC;QAEDnB,KAAI,CAACrW,aAAa,CAACyX,OAAO,GAAIjK,KAAU,IAAI;UAC1C9J,OAAO,CAACwB,KAAK,CAAC,iCAAiC,EAAEsI,KAAK,CAACtI,KAAK,CAAC;UAC7DmR,KAAI,CAACvY,YAAY,CAACqI,SAAS,CAAC,iCAAiC,CAAC;UAC9DkQ,KAAI,CAACqB,oBAAoB,EAAE;QAC7B,CAAC;QAED;QACArB,KAAI,CAACrW,aAAa,CAAC2X,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;QAE/BtB,KAAI,CAACvY,YAAY,CAACgO,WAAW,CAAC,iCAAiC,CAAC;OACjE,CAAC,OAAO5G,KAAU,EAAE;QACnBxB,OAAO,CAACwB,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;QAE5D,IAAI0S,YAAY,GAAG,+CAA+C;QAElE,IAAI1S,KAAK,CAAC/D,IAAI,KAAK,iBAAiB,EAAE;UACpCyW,YAAY,GACV,iGAAiG;SACpG,MAAM,IAAI1S,KAAK,CAAC/D,IAAI,KAAK,eAAe,EAAE;UACzCyW,YAAY,GACV,6DAA6D;SAChE,MAAM,IAAI1S,KAAK,CAAC/D,IAAI,KAAK,mBAAmB,EAAE;UAC7CyW,YAAY,GACV,0DAA0D;SAC7D,MAAM,IAAI1S,KAAK,CAACkF,OAAO,EAAE;UACxBwN,YAAY,GAAG1S,KAAK,CAACkF,OAAO;;QAG9BiM,KAAI,CAACvY,YAAY,CAACqI,SAAS,CAACyR,YAAY,CAAC;QACzCvB,KAAI,CAACqB,oBAAoB,EAAE;;IAC5B;EACH;EAEAG,kBAAkBA,CAAA;IAChB,IAAI,IAAI,CAAC7X,aAAa,IAAI,IAAI,CAACA,aAAa,CAAC8X,KAAK,KAAK,WAAW,EAAE;MAClE,IAAI,CAAC9X,aAAa,CAAC+X,IAAI,EAAE;MACzB,IAAI,CAAC/X,aAAa,CAAC4W,MAAM,CAACoB,SAAS,EAAE,CAACnP,OAAO,CAAEoP,KAAK,IAAKA,KAAK,CAACF,IAAI,EAAE,CAAC;;IAGxE,IAAI,IAAI,CAAC7X,cAAc,EAAE;MACvB0V,aAAa,CAAC,IAAI,CAAC1V,cAAc,CAAC;MAClC,IAAI,CAACA,cAAc,GAAG,IAAI;;IAG5B,IAAI,CAACL,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACE,mBAAmB,GAAG,YAAY;EACzC;EAEA2X,oBAAoBA,CAAA;IAClB,IAAI,IAAI,CAAC1X,aAAa,EAAE;MACtB,IAAI,IAAI,CAACA,aAAa,CAAC8X,KAAK,KAAK,WAAW,EAAE;QAC5C,IAAI,CAAC9X,aAAa,CAAC+X,IAAI,EAAE;;MAE3B,IAAI,CAAC/X,aAAa,CAAC4W,MAAM,CAACoB,SAAS,EAAE,CAACnP,OAAO,CAAEoP,KAAK,IAAKA,KAAK,CAACF,IAAI,EAAE,CAAC;MACtE,IAAI,CAAC/X,aAAa,GAAG,IAAI;;IAG3B,IAAI,IAAI,CAACE,cAAc,EAAE;MACvB0V,aAAa,CAAC,IAAI,CAAC1V,cAAc,CAAC;MAClC,IAAI,CAACA,cAAc,GAAG,IAAI;;IAG5B,IAAI,CAACL,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACC,sBAAsB,GAAG,CAAC;IAC/B,IAAI,CAACC,mBAAmB,GAAG,MAAM;IACjC,IAAI,CAACE,WAAW,GAAG,EAAE;EACvB;EAEcuX,oBAAoBA,CAAA;IAAA,IAAAU,MAAA;IAAA,OAAA5B,iBAAA;MAChC,IAAI;QACF;QACA,IAAI4B,MAAI,CAACjY,WAAW,CAACyG,MAAM,KAAK,CAAC,EAAE;UACjChD,OAAO,CAACwB,KAAK,CAAC,sCAAsC,CAAC;UACrDgT,MAAI,CAACpa,YAAY,CAACqI,SAAS,CAAC,wBAAwB,CAAC;UACrD+R,MAAI,CAACR,oBAAoB,EAAE;UAC3B;;QAGFhU,OAAO,CAACC,GAAG,CACT,0BAA0B,EAC1BuU,MAAI,CAACjY,WAAW,CAACyG,MAAM,EACvB,WAAW,EACXwR,MAAI,CAACpY,sBAAsB,CAC5B;QAED;QACA,IAAIoY,MAAI,CAACpY,sBAAsB,GAAG,CAAC,EAAE;UACnC4D,OAAO,CAACwB,KAAK,CACX,iCAAiC,EACjCgT,MAAI,CAACpY,sBAAsB,CAC5B;UACDoY,MAAI,CAACpa,YAAY,CAACqI,SAAS,CACzB,+CAA+C,CAChD;UACD+R,MAAI,CAACR,oBAAoB,EAAE;UAC3B;;QAGF;QACA,IAAIP,QAAQ,GAAG,wBAAwB;QACvC,IAAIe,MAAI,CAAClY,aAAa,EAAEmX,QAAQ,EAAE;UAChCA,QAAQ,GAAGe,MAAI,CAAClY,aAAa,CAACmX,QAAQ;;QAGxC;QACA,MAAMgB,SAAS,GAAG,IAAIC,IAAI,CAACF,MAAI,CAACjY,WAAW,EAAE;UAC3CsI,IAAI,EAAE4O;SACP,CAAC;QAEFzT,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE;UAC5CuF,IAAI,EAAEiP,SAAS,CAACjP,IAAI;UACpBX,IAAI,EAAE4P,SAAS,CAAC5P;SACjB,CAAC;QAEF;QACA,IAAI8P,SAAS,GAAG,OAAO;QACvB,IAAIlB,QAAQ,CAAC5D,QAAQ,CAAC,KAAK,CAAC,EAAE;UAC5B8E,SAAS,GAAG,MAAM;SACnB,MAAM,IAAIlB,QAAQ,CAAC5D,QAAQ,CAAC,KAAK,CAAC,EAAE;UACnC8E,SAAS,GAAG,MAAM;SACnB,MAAM,IAAIlB,QAAQ,CAAC5D,QAAQ,CAAC,KAAK,CAAC,EAAE;UACnC8E,SAAS,GAAG,MAAM;;QAGpB;QACA,MAAMC,SAAS,GAAG,IAAIC,IAAI,CACxB,CAACJ,SAAS,CAAC,EACX,SAAS9Q,IAAI,CAAC0D,GAAG,EAAE,GAAGsN,SAAS,EAAE,EACjC;UACE9P,IAAI,EAAE4O;SACP,CACF;QAEDzT,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE;UAC5CxC,IAAI,EAAEmX,SAAS,CAACnX,IAAI;UACpB+H,IAAI,EAAEoP,SAAS,CAACpP,IAAI;UACpBX,IAAI,EAAE+P,SAAS,CAAC/P;SACjB,CAAC;QAEF;QACA2P,MAAI,CAACnY,mBAAmB,GAAG,YAAY;QACvC,MAAMmY,MAAI,CAACM,gBAAgB,CAACF,SAAS,CAAC;QAEtCJ,MAAI,CAACpa,YAAY,CAACgO,WAAW,CAAC,yBAAyB,CAAC;OACzD,CAAC,OAAO5G,KAAU,EAAE;QACnBxB,OAAO,CAACwB,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;QAC1DgT,MAAI,CAACpa,YAAY,CAACqI,SAAS,CACzB,2CAA2C,IACxCjB,KAAK,CAACkF,OAAO,IAAI,iBAAiB,CAAC,CACvC;OACF,SAAS;QACR;QACA8N,MAAI,CAACnY,mBAAmB,GAAG,MAAM;QACjCmY,MAAI,CAACpY,sBAAsB,GAAG,CAAC;QAC/BoY,MAAI,CAACjY,WAAW,GAAG,EAAE;QACrBiY,MAAI,CAACrY,gBAAgB,GAAG,KAAK;;IAC9B;EACH;EAEc2Y,gBAAgBA,CAACF,SAAe;IAAA,IAAAG,MAAA;IAAA,OAAAnC,iBAAA;MAC5C,MAAM5N,UAAU,GAAG+P,MAAI,CAACra,gBAAgB,EAAE8C,EAAE,IAAIuX,MAAI,CAACra,gBAAgB,EAAE2H,GAAG;MAE1E,IAAI,CAAC2C,UAAU,EAAE;QACf,MAAM,IAAIgO,KAAK,CAAC,0BAA0B,CAAC;;MAG7C,OAAO,IAAIgC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;QACrCH,MAAI,CAAC7a,cAAc,CAACmM,WAAW,CAC7BrB,UAAU,EACV,EAAE,EACF4P,SAAS,EACT,OAAc,EACdG,MAAI,CAACza,YAAY,CAACkD,EAAE,CACrB,CAAC4D,SAAS,CAAC;UACVC,IAAI,EAAGqF,OAAY,IAAI;YACrBqO,MAAI,CAACxa,QAAQ,CAACqL,IAAI,CAACc,OAAO,CAAC;YAC3BqO,MAAI,CAAC5Q,cAAc,EAAE;YACrB8Q,OAAO,EAAE;UACX,CAAC;UACDzT,KAAK,EAAGA,KAAU,IAAI;YACpBxB,OAAO,CAACwB,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;YAChE0T,MAAM,CAAC1T,KAAK,CAAC;UACf;SACD,CAAC;MACJ,CAAC,CAAC;IAAC;EACL;EAEA2T,uBAAuBA,CAAC3N,QAAgB;IACtC,MAAM+K,OAAO,GAAGpL,IAAI,CAACC,KAAK,CAACI,QAAQ,GAAG,EAAE,CAAC;IACzC,MAAMgL,OAAO,GAAGhL,QAAQ,GAAG,EAAE;IAC7B,OAAO,GAAG+K,OAAO,IAAIC,OAAO,CAAC5G,QAAQ,EAAE,CAAC6G,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EAC5D;EAEA;EAEA2C,aAAaA,CAACtL,KAAY;IACxBA,KAAK,CAACC,cAAc,EAAE;IAEtB/J,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE;MACvC9D,gBAAgB,EAAE,IAAI,CAACA,gBAAgB;MACvCE,mBAAmB,EAAE,IAAI,CAACA,mBAAmB;MAC7CD,sBAAsB,EAAE,IAAI,CAACA,sBAAsB;MACnDE,aAAa,EAAE,CAAC,CAAC,IAAI,CAACA;KACvB,CAAC;IAEF;IACA,IAAI,IAAI,CAACD,mBAAmB,KAAK,YAAY,EAAE;MAC7C,IAAI,CAACjC,YAAY,CAACib,WAAW,CAAC,wBAAwB,CAAC;MACvD;;IAGF,IAAI,IAAI,CAAClZ,gBAAgB,EAAE;MACzB,IAAI,CAAC/B,YAAY,CAACib,WAAW,CAAC,iCAAiC,CAAC;MAChE;;IAGF;IACA,IAAI,CAACjb,YAAY,CAACkb,QAAQ,CAAC,2CAA2C,CAAC;IAEvE;IACA,IAAI,CAAC5C,mBAAmB,EAAE,CAACjJ,KAAK,CAAEjI,KAAK,IAAI;MACzCxB,OAAO,CAACwB,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;MAC7D,IAAI,CAACpH,YAAY,CAACqI,SAAS,CACzB,iDAAiD,IAC9CjB,KAAK,CAACkF,OAAO,IAAI,iBAAiB,CAAC,CACvC;IACH,CAAC,CAAC;EACJ;EAEA6O,WAAWA,CAACzL,KAAY;IACtBA,KAAK,CAACC,cAAc,EAAE;IAEtB,IAAI,CAAC,IAAI,CAAC5N,gBAAgB,EAAE;MAC1B;;IAGF;IACA,IAAI,CAACgY,kBAAkB,EAAE;EAC3B;EAEAqB,cAAcA,CAAC1L,KAAY;IACzBA,KAAK,CAACC,cAAc,EAAE;IAEtB,IAAI,CAAC,IAAI,CAAC5N,gBAAgB,EAAE;MAC1B;;IAGF;IACA,IAAI,CAAC6X,oBAAoB,EAAE;EAC7B;EAEAyB,kBAAkBA,CAAA;IAChB,IAAI,IAAI,CAACnZ,aAAa,EAAEmX,QAAQ,EAAE;MAChC,IAAI,IAAI,CAACnX,aAAa,CAACmX,QAAQ,CAAC5D,QAAQ,CAAC,MAAM,CAAC,EAAE,OAAO,MAAM;MAC/D,IAAI,IAAI,CAACvT,aAAa,CAACmX,QAAQ,CAAC5D,QAAQ,CAAC,KAAK,CAAC,EAAE,OAAO,KAAK;MAC7D,IAAI,IAAI,CAACvT,aAAa,CAACmX,QAAQ,CAAC5D,QAAQ,CAAC,KAAK,CAAC,EAAE,OAAO,KAAK;MAC7D,IAAI,IAAI,CAACvT,aAAa,CAACmX,QAAQ,CAAC5D,QAAQ,CAAC,KAAK,CAAC,EAAE,OAAO,KAAK;;IAE/D,OAAO,MAAM;EACf;EAEA;EAEQ8D,iBAAiBA,CAAA;IACvB;IACA,IAAI,CAAClX,UAAU,GAAG,IAAI,CAACA,UAAU,CAACiZ,GAAG,CAAC,MAAK;MACzC,OAAOvO,IAAI,CAACC,KAAK,CAACD,IAAI,CAACwO,MAAM,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;IAC7C,CAAC,CAAC;EACJ;;EAEAC,cAAcA,CAAC9L,KAAU;IACvB,MAAM+L,KAAK,GAAG/L,KAAK,CAACb,MAAM,CAAC4M,KAAK;IAEhC,IAAI,CAACA,KAAK,IAAIA,KAAK,CAAC7S,MAAM,KAAK,CAAC,EAAE;MAChC;;IAGF,KAAK,IAAI8S,IAAI,IAAID,KAAK,EAAE;MACtB7V,OAAO,CAACC,GAAG,CACT,gCAAgC6V,IAAI,CAACrY,IAAI,WAAWqY,IAAI,CAACtQ,IAAI,WAAWsQ,IAAI,CAACjR,IAAI,EAAE,CACpF;MACD,IAAI,CAACkR,UAAU,CAACD,IAAI,CAAC;;EAEzB;EAEQC,UAAUA,CAACD,IAAU;IAC3B,MAAM9Q,UAAU,GAAG,IAAI,CAACtK,gBAAgB,EAAE8C,EAAE,IAAI,IAAI,CAAC9C,gBAAgB,EAAE2H,GAAG;IAE1E,IAAI,CAAC2C,UAAU,EAAE;MACfhF,OAAO,CAACwB,KAAK,CAAC,kCAAkC,CAAC;MACjD,IAAI,CAACpH,YAAY,CAACqI,SAAS,CAAC,0BAA0B,CAAC;MACvD;;IAGF;IACA,MAAMuT,OAAO,GAAG,EAAE,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC;IAClC,IAAIF,IAAI,CAACtQ,IAAI,GAAGwQ,OAAO,EAAE;MACvBhW,OAAO,CAACwB,KAAK,CAAC,+BAA+BsU,IAAI,CAACtQ,IAAI,QAAQ,CAAC;MAC/D,IAAI,CAACpL,YAAY,CAACqI,SAAS,CAAC,oCAAoC,CAAC;MACjE;;IAGF;IACA,IAAIqT,IAAI,CAACjR,IAAI,CAACgE,UAAU,CAAC,QAAQ,CAAC,IAAIiN,IAAI,CAACtQ,IAAI,GAAG,IAAI,GAAG,IAAI,EAAE;MAC7D;MACAxF,OAAO,CAACC,GAAG,CACT,sCAAsC,EACtC6V,IAAI,CAACrY,IAAI,EACT,gBAAgB,EAChBqY,IAAI,CAACtQ,IAAI,CACV;MACD,IAAI,CAACyQ,aAAa,CAACH,IAAI,CAAC,CACrBtM,IAAI,CAAE0M,cAAc,IAAI;QACvBlW,OAAO,CAACC,GAAG,CACT,8DAA8D,EAC9DiW,cAAc,CAAC1Q,IAAI,CACpB;QACD,IAAI,CAAC2Q,gBAAgB,CAACD,cAAc,EAAElR,UAAU,CAAC;MACnD,CAAC,CAAC,CACDyE,KAAK,CAAEjI,KAAK,IAAI;QACfxB,OAAO,CAACwB,KAAK,CAAC,8CAA8C,EAAEA,KAAK,CAAC;QACpE;QACA,IAAI,CAAC2U,gBAAgB,CAACL,IAAI,EAAE9Q,UAAU,CAAC;MACzC,CAAC,CAAC;MACJ;;IAGF;IACA,IAAI,CAACmR,gBAAgB,CAACL,IAAI,EAAE9Q,UAAU,CAAC;EACzC;EAEQmR,gBAAgBA,CAACL,IAAU,EAAE9Q,UAAkB;IACrD,MAAMoR,WAAW,GAAG,IAAI,CAACC,kBAAkB,CAACP,IAAI,CAAC;IAEjD,IAAI,CAAC1a,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACa,WAAW,GAAG,IAAI;IACvB,IAAI,CAACD,cAAc,GAAG,CAAC;IAEvB;IACA,MAAMsa,gBAAgB,GAAGtE,WAAW,CAAC,MAAK;MACxC,IAAI,CAAChW,cAAc,IAAImL,IAAI,CAACwO,MAAM,EAAE,GAAG,EAAE;MACzC,IAAI,IAAI,CAAC3Z,cAAc,IAAI,EAAE,EAAE;QAC7BkW,aAAa,CAACoE,gBAAgB,CAAC;;MAEjC,IAAI,CAACjc,GAAG,CAACwL,aAAa,EAAE;IAC1B,CAAC,EAAE,GAAG,CAAC;IAEP,IAAI,CAAC3L,cAAc,CAACmM,WAAW,CAC7BrB,UAAU,EACV,EAAE,EACF8Q,IAAI,EACJM,WAAW,EACX,IAAI,CAAC9b,YAAY,CAACkD,EAAE,CACrB,CAAC4D,SAAS,CAAC;MACVC,IAAI,EAAGqF,OAAY,IAAI;QACrB1G,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAE;UAChDzC,EAAE,EAAEkJ,OAAO,CAAClJ,EAAE;UACdqH,IAAI,EAAE6B,OAAO,CAAC7B,IAAI;UAClBI,WAAW,EAAEyB,OAAO,CAACzB,WAAW;UAChCiK,QAAQ,EAAE,IAAI,CAACA,QAAQ,CAACxI,OAAO,CAAC;UAChC4I,OAAO,EAAE,IAAI,CAACA,OAAO,CAAC5I,OAAO,CAAC;UAC9B2I,QAAQ,EAAE,IAAI,CAACG,WAAW,CAAC9I,OAAO;SACnC,CAAC;QAEFwL,aAAa,CAACoE,gBAAgB,CAAC;QAC/B,IAAI,CAACta,cAAc,GAAG,GAAG;QAEzBmD,UAAU,CAAC,MAAK;UACd,IAAI,CAAC5E,QAAQ,CAACqL,IAAI,CAACc,OAAO,CAAC;UAC3B,IAAI,CAACvC,cAAc,EAAE;UACrB,IAAI,CAAC/J,YAAY,CAACgO,WAAW,CAAC,4BAA4B,CAAC;UAC3D,IAAI,CAACmO,gBAAgB,EAAE;QACzB,CAAC,EAAE,GAAG,CAAC;MACT,CAAC;MACD/U,KAAK,EAAGA,KAAU,IAAI;QACpBxB,OAAO,CAACwB,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;QACzD0Q,aAAa,CAACoE,gBAAgB,CAAC;QAC/B,IAAI,CAAClc,YAAY,CAACqI,SAAS,CAAC,mCAAmC,CAAC;QAChE,IAAI,CAAC8T,gBAAgB,EAAE;MACzB;KACD,CAAC;EACJ;EAEQF,kBAAkBA,CAACP,IAAU;IACnC,IAAIA,IAAI,CAACjR,IAAI,CAACgE,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,OAAc;IACzD,IAAIiN,IAAI,CAACjR,IAAI,CAACgE,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,OAAc;IACzD,IAAIiN,IAAI,CAACjR,IAAI,CAACgE,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,OAAc;IACzD,OAAO,MAAa;EACtB;EAEA2N,kBAAkBA,CAAA;IAChB,OAAO,KAAK;EACd;EAEAD,gBAAgBA,CAAA;IACd,IAAI,CAACnb,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACa,WAAW,GAAG,KAAK;IACxB,IAAI,CAACD,cAAc,GAAG,CAAC;EACzB;EAEA;EAEAya,UAAUA,CAAC3M,KAAgB;IACzBA,KAAK,CAACC,cAAc,EAAE;IACtBD,KAAK,CAACK,eAAe,EAAE;IACvB,IAAI,CAACjO,UAAU,GAAG,IAAI;EACxB;EAEAwa,WAAWA,CAAC5M,KAAgB;IAC1BA,KAAK,CAACC,cAAc,EAAE;IACtBD,KAAK,CAACK,eAAe,EAAE;IACvB;IACA,MAAMwM,IAAI,GAAI7M,KAAK,CAAC8M,aAA6B,CAACC,qBAAqB,EAAE;IACzE,MAAMnb,CAAC,GAAGoO,KAAK,CAACE,OAAO;IACvB,MAAMrO,CAAC,GAAGmO,KAAK,CAACG,OAAO;IAEvB,IAAIvO,CAAC,GAAGib,IAAI,CAACG,IAAI,IAAIpb,CAAC,GAAGib,IAAI,CAACI,KAAK,IAAIpb,CAAC,GAAGgb,IAAI,CAACK,GAAG,IAAIrb,CAAC,GAAGgb,IAAI,CAACM,MAAM,EAAE;MACtE,IAAI,CAAC/a,UAAU,GAAG,KAAK;;EAE3B;EAEAgb,MAAMA,CAACpN,KAAgB;IACrBA,KAAK,CAACC,cAAc,EAAE;IACtBD,KAAK,CAACK,eAAe,EAAE;IACvB,IAAI,CAACjO,UAAU,GAAG,KAAK;IAEvB,MAAM2Z,KAAK,GAAG/L,KAAK,CAACqN,YAAY,EAAEtB,KAAK;IACvC,IAAIA,KAAK,IAAIA,KAAK,CAAC7S,MAAM,GAAG,CAAC,EAAE;MAC7B;MACAoU,KAAK,CAACC,IAAI,CAACxB,KAAK,CAAC,CAAC1Q,OAAO,CAAE2Q,IAAI,IAAI;QACjC9V,OAAO,CAACC,GAAG,CACT,iCAAiC,EACjC6V,IAAI,CAACrY,IAAI,EACTqY,IAAI,CAACjR,IAAI,EACTiR,IAAI,CAACtQ,IAAI,CACV;QACD,IAAI,CAACuQ,UAAU,CAACD,IAAI,CAAC;MACvB,CAAC,CAAC;MAEF,IAAI,CAAC1b,YAAY,CAACgO,WAAW,CAC3B,GAAGyN,KAAK,CAAC7S,MAAM,8BAA8B,CAC9C;;EAEL;EAEA;EAEQiT,aAAaA,CAACH,IAAU,EAAEwB,OAAA,GAAkB,GAAG;IACrD,OAAO,IAAItC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;MACrC,MAAMqC,MAAM,GAAG1X,QAAQ,CAACM,aAAa,CAAC,QAAQ,CAAC;MAC/C,MAAMqX,GAAG,GAAGD,MAAM,CAACE,UAAU,CAAC,IAAI,CAAC;MACnC,MAAMC,GAAG,GAAG,IAAIC,KAAK,EAAE;MAEvBD,GAAG,CAACE,MAAM,GAAG,MAAK;QAChB;QACA,MAAMC,QAAQ,GAAG,IAAI;QACrB,MAAMC,SAAS,GAAG,IAAI;QACtB,IAAI;UAAEC,KAAK;UAAEC;QAAM,CAAE,GAAGN,GAAG;QAE3B,IAAIK,KAAK,GAAGF,QAAQ,IAAIG,MAAM,GAAGF,SAAS,EAAE;UAC1C,MAAMG,KAAK,GAAG9Q,IAAI,CAAC6F,GAAG,CAAC6K,QAAQ,GAAGE,KAAK,EAAED,SAAS,GAAGE,MAAM,CAAC;UAC5DD,KAAK,IAAIE,KAAK;UACdD,MAAM,IAAIC,KAAK;;QAGjBV,MAAM,CAACQ,KAAK,GAAGA,KAAK;QACpBR,MAAM,CAACS,MAAM,GAAGA,MAAM;QAEtB;QACAR,GAAG,EAAEU,SAAS,CAACR,GAAG,EAAE,CAAC,EAAE,CAAC,EAAEK,KAAK,EAAEC,MAAM,CAAC;QAExC;QACAT,MAAM,CAACY,MAAM,CACVC,IAAI,IAAI;UACP,IAAIA,IAAI,EAAE;YACR,MAAMlC,cAAc,GAAG,IAAIrB,IAAI,CAAC,CAACuD,IAAI,CAAC,EAAEtC,IAAI,CAACrY,IAAI,EAAE;cACjDoH,IAAI,EAAEiR,IAAI,CAACjR,IAAI;cACfwT,YAAY,EAAE1U,IAAI,CAAC0D,GAAG;aACvB,CAAC;YACF4N,OAAO,CAACiB,cAAc,CAAC;WACxB,MAAM;YACLhB,MAAM,CAAC,IAAIlC,KAAK,CAAC,0BAA0B,CAAC,CAAC;;QAEjD,CAAC,EACD8C,IAAI,CAACjR,IAAI,EACTyS,OAAO,CACR;MACH,CAAC;MAEDI,GAAG,CAAC3D,OAAO,GAAG,MAAMmB,MAAM,CAAC,IAAIlC,KAAK,CAAC,sBAAsB,CAAC,CAAC;MAC7D0E,GAAG,CAAC9G,GAAG,GAAG0H,GAAG,CAACC,eAAe,CAACzC,IAAI,CAAC;IACrC,CAAC,CAAC;EACJ;EAEA;EAEQ3F,qBAAqBA,CAAA;IAC3B,IAAI,CAAC,IAAI,CAACnS,QAAQ,EAAE;MAClB,IAAI,CAACA,QAAQ,GAAG,IAAI;MACpB;MACA,IAAI,CAACwa,mBAAmB,CAAC,IAAI,CAAC;;IAGhC;IACA,IAAI,IAAI,CAACta,aAAa,EAAE;MACtBua,YAAY,CAAC,IAAI,CAACva,aAAa,CAAC;;IAGlC,IAAI,CAACA,aAAa,GAAGiB,UAAU,CAAC,MAAK;MACnC,IAAI,CAACnB,QAAQ,GAAG,KAAK;MACrB;MACA,IAAI,CAACwa,mBAAmB,CAAC,KAAK,CAAC;IACjC,CAAC,EAAE,IAAI,CAAC;EACV;EAEQA,mBAAmBA,CAACxa,QAAiB;IAC3C;IACA,MAAMgH,UAAU,GAAG,IAAI,CAACtK,gBAAgB,EAAE8C,EAAE,IAAI,IAAI,CAAC9C,gBAAgB,EAAE2H,GAAG;IAC1E,IAAI2C,UAAU,IAAI,IAAI,CAAC1K,YAAY,EAAEkD,EAAE,EAAE;MACvCwC,OAAO,CAACC,GAAG,CACT,gCAAgCjC,QAAQ,YAAYgH,UAAU,EAAE,CACjE;MACD;MACA;;EAEJ;EAEA;EAEA0T,cAAcA,CAAChX,IAAU;IACvB,IAAI,CAACzE,UAAU,GAAGyE,IAAI;IACtB,IAAI,CAAC7E,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACK,eAAe,GAAG,IAAI;IAC3B,IAAI,CAAC2U,cAAc,EAAE;IACrB,IAAI,CAACzX,YAAY,CAACgO,WAAW,CAAC,eAAe,CAAC;EAChD;EAEAuQ,cAAcA,CAAA;IACZ,IAAI,CAACpQ,OAAO,EAAE;IACd,IAAI,CAACnO,YAAY,CAACkb,QAAQ,CAAC,cAAc,CAAC;EAC5C;EAEA;EAEAsD,gBAAgBA,CAAClS,OAAY;IAC3B,IAAI,CAACmS,mBAAmB,CAACnS,OAAO,CAAC;EACnC;EAEAoS,cAAcA,CAAC1S,SAAiB;IAC9B,OAAO,IAAI,CAACzJ,gBAAgB,KAAKyJ,SAAS;EAC5C;EAEAyS,mBAAmBA,CAACnS,OAAY;IAC9B,MAAMN,SAAS,GAAGM,OAAO,CAAClJ,EAAE;IAC5B,MAAMwR,QAAQ,GAAG,IAAI,CAAC+J,WAAW,CAACrS,OAAO,CAAC;IAE1C,IAAI,CAACsI,QAAQ,EAAE;MACbhP,OAAO,CAACwB,KAAK,CAAC,4CAA4C,EAAE4E,SAAS,CAAC;MACtE,IAAI,CAAChM,YAAY,CAACqI,SAAS,CAAC,2BAA2B,CAAC;MACxD;;IAGF;IACA,IAAI,IAAI,CAACqW,cAAc,CAAC1S,SAAS,CAAC,EAAE;MAClC,IAAI,CAAC4S,iBAAiB,EAAE;MACxB;;IAGF;IACA,IAAI,CAACA,iBAAiB,EAAE;IAExB;IACA,IAAI,CAACC,kBAAkB,CAACvS,OAAO,EAAEsI,QAAQ,CAAC;EAC5C;EAEQiK,kBAAkBA,CAACvS,OAAY,EAAEsI,QAAgB;IACvD,MAAM5I,SAAS,GAAGM,OAAO,CAAClJ,EAAE;IAE5B,IAAI;MACFwC,OAAO,CAACC,GAAG,CACT,mCAAmC,EACnCmG,SAAS,EACT,MAAM,EACN4I,QAAQ,CACT;MAED,IAAI,CAACtS,YAAY,GAAG,IAAIwc,KAAK,CAAClK,QAAQ,CAAC;MACvC,IAAI,CAACrS,gBAAgB,GAAGyJ,SAAS;MAEjC;MACA,MAAM+S,WAAW,GAAG,IAAI,CAAC7R,oBAAoB,CAAClB,SAAS,CAAC;MACxD,IAAI,CAACuB,oBAAoB,CAACvB,SAAS,EAAE;QACnCmB,QAAQ,EAAE,CAAC;QACXE,WAAW,EAAE,CAAC;QACdC,KAAK,EAAEyR,WAAW,CAACzR,KAAK,IAAI,CAAC;QAC7BF,QAAQ,EAAE2R,WAAW,CAAC3R,QAAQ,IAAI;OACnC,CAAC;MAEF;MACA,IAAI,CAAC9K,YAAY,CAAC0c,YAAY,GAAGD,WAAW,CAACzR,KAAK,IAAI,CAAC;MAEvD;MACA,IAAI,CAAChL,YAAY,CAACmE,gBAAgB,CAAC,gBAAgB,EAAE,MAAK;QACxD,IAAI,IAAI,CAACnE,YAAY,EAAE;UACrB,IAAI,CAACiL,oBAAoB,CAACvB,SAAS,EAAE;YACnCoB,QAAQ,EAAE,IAAI,CAAC9K,YAAY,CAAC8K;WAC7B,CAAC;UACFxH,OAAO,CAACC,GAAG,CACT,oCAAoC,EACpC,IAAI,CAACvD,YAAY,CAAC8K,QAAQ,CAC3B;;MAEL,CAAC,CAAC;MAEF,IAAI,CAAC9K,YAAY,CAACmE,gBAAgB,CAAC,YAAY,EAAE,MAAK;QACpD,IAAI,IAAI,CAACnE,YAAY,IAAI,IAAI,CAACC,gBAAgB,KAAKyJ,SAAS,EAAE;UAC5D,MAAMqB,WAAW,GAAG,IAAI,CAAC/K,YAAY,CAAC+K,WAAW;UACjD,MAAMF,QAAQ,GAAIE,WAAW,GAAG,IAAI,CAAC/K,YAAY,CAAC8K,QAAQ,GAAI,GAAG;UACjE,IAAI,CAACG,oBAAoB,CAACvB,SAAS,EAAE;YAAEqB,WAAW;YAAEF;UAAQ,CAAE,CAAC;UAC/D,IAAI,CAAClN,GAAG,CAACwL,aAAa,EAAE;;MAE5B,CAAC,CAAC;MAEF,IAAI,CAACnJ,YAAY,CAACmE,gBAAgB,CAAC,OAAO,EAAE,MAAK;QAC/C,IAAI,CAACmY,iBAAiB,EAAE;MAC1B,CAAC,CAAC;MAEF,IAAI,CAACtc,YAAY,CAACmE,gBAAgB,CAAC,OAAO,EAAGW,KAAK,IAAI;QACpDxB,OAAO,CAACwB,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,IAAI,CAACpH,YAAY,CAACqI,SAAS,CAAC,iCAAiC,CAAC;QAC9D,IAAI,CAACuW,iBAAiB,EAAE;MAC1B,CAAC,CAAC;MAEF;MACA,IAAI,CAACtc,YAAY,CACdmF,IAAI,EAAE,CACN2H,IAAI,CAAC,MAAK;QACT,IAAI,CAACpP,YAAY,CAACgO,WAAW,CAAC,6BAA6B,CAAC;MAC9D,CAAC,CAAC,CACDqB,KAAK,CAAEjI,KAAK,IAAI;QACfxB,OAAO,CAACwB,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;QAC3D,IAAI,CAACpH,YAAY,CAACqI,SAAS,CAAC,qCAAqC,CAAC;QAClE,IAAI,CAACuW,iBAAiB,EAAE;MAC1B,CAAC,CAAC;KACL,CAAC,OAAOxX,KAAK,EAAE;MACdxB,OAAO,CAACwB,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxD,IAAI,CAACpH,YAAY,CAACqI,SAAS,CAAC,iCAAiC,CAAC;MAC9D,IAAI,CAACuW,iBAAiB,EAAE;;EAE5B;EAEQA,iBAAiBA,CAAA;IACvB,IAAI,IAAI,CAACtc,YAAY,EAAE;MACrB,IAAI,CAACA,YAAY,CAAC2c,KAAK,EAAE;MACzB,IAAI,CAAC3c,YAAY,CAAC+K,WAAW,GAAG,CAAC;MACjC,IAAI,CAAC/K,YAAY,GAAG,IAAI;;IAE1B,IAAI,CAACC,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACtC,GAAG,CAACwL,aAAa,EAAE;EAC1B;EAEAkT,WAAWA,CAACrS,OAAY;IACtB;IACA,IAAIA,OAAO,CAACqI,QAAQ,EAAE,OAAOrI,OAAO,CAACqI,QAAQ;IAC7C,IAAIrI,OAAO,CAACsI,QAAQ,EAAE,OAAOtI,OAAO,CAACsI,QAAQ;IAC7C,IAAItI,OAAO,CAACuI,KAAK,EAAE,OAAOvI,OAAO,CAACuI,KAAK;IAEvC;IACA,MAAMqK,eAAe,GAAG5S,OAAO,CAACzB,WAAW,EAAE/B,IAAI,CAC9CkC,GAAQ,IAAKA,GAAG,CAACP,IAAI,EAAEgE,UAAU,CAAC,QAAQ,CAAC,IAAIzD,GAAG,CAACP,IAAI,KAAK,OAAO,CACrE;IAED,IAAIyU,eAAe,EAAE;MACnB,OAAOA,eAAe,CAAChU,GAAG,IAAIgU,eAAe,CAAC/T,IAAI,IAAI,EAAE;;IAG1D,OAAO,EAAE;EACX;EAEAgU,aAAaA,CAAC7S,OAAY;IACxB;IACA,MAAMN,SAAS,GAAGM,OAAO,CAAClJ,EAAE,IAAI,EAAE;IAClC,MAAMgc,IAAI,GAAGpT,SAAS,CACnBqT,KAAK,CAAC,EAAE,CAAC,CACTC,MAAM,CAAC,CAACC,GAAW,EAAEC,IAAY,KAAKD,GAAG,GAAGC,IAAI,CAAC5J,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACrE,MAAM6J,KAAK,GAAa,EAAE;IAE1B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;MAC3B,MAAM9B,MAAM,GAAG,CAAC,GAAI,CAACwB,IAAI,GAAGM,CAAC,GAAG,CAAC,IAAI,EAAG;MACxCD,KAAK,CAACjU,IAAI,CAACoS,MAAM,CAAC;;IAGpB,OAAO6B,KAAK;EACd;EAEAE,gBAAgBA,CAACrT,OAAY;IAC3B,MAAMkB,IAAI,GAAG,IAAI,CAACN,oBAAoB,CAACZ,OAAO,CAAClJ,EAAE,CAAC;IAClD,MAAMwc,UAAU,GAAG,EAAE;IACrB,OAAO7S,IAAI,CAACC,KAAK,CAAEQ,IAAI,CAACL,QAAQ,GAAG,GAAG,GAAIyS,UAAU,CAAC;EACvD;EAEAC,mBAAmBA,CAACvT,OAAY;IAC9B,MAAMkB,IAAI,GAAG,IAAI,CAACN,oBAAoB,CAACZ,OAAO,CAAClJ,EAAE,CAAC;IAClD,OAAO,IAAI,CAAC0c,eAAe,CAACtS,IAAI,CAACH,WAAW,CAAC;EAC/C;EAEA0S,gBAAgBA,CAACzT,OAAY;IAC3B,MAAMkB,IAAI,GAAG,IAAI,CAACN,oBAAoB,CAACZ,OAAO,CAAClJ,EAAE,CAAC;IAClD,MAAMgK,QAAQ,GAAGI,IAAI,CAACJ,QAAQ,IAAId,OAAO,CAAC0T,QAAQ,EAAE5S,QAAQ,IAAI,CAAC;IAEjE,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;MAChC,OAAOA,QAAQ,CAAC,CAAC;;;IAGnB,OAAO,IAAI,CAAC0S,eAAe,CAAC1S,QAAQ,CAAC;EACvC;EAEQ0S,eAAeA,CAAC1H,OAAe;IACrC,MAAMD,OAAO,GAAGpL,IAAI,CAACC,KAAK,CAACoL,OAAO,GAAG,EAAE,CAAC;IACxC,MAAM6H,gBAAgB,GAAGlT,IAAI,CAACC,KAAK,CAACoL,OAAO,GAAG,EAAE,CAAC;IACjD,OAAO,GAAGD,OAAO,IAAI8H,gBAAgB,CAACzO,QAAQ,EAAE,CAAC6G,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EACrE;EAEA6H,gBAAgBA,CAAC5T,OAAY,EAAE6T,SAAiB;IAC9C,MAAMnU,SAAS,GAAGM,OAAO,CAAClJ,EAAE;IAE5B,IAAI,CAAC,IAAI,CAACd,YAAY,IAAI,IAAI,CAACC,gBAAgB,KAAKyJ,SAAS,EAAE;MAC7D;;IAGF,MAAM4T,UAAU,GAAG,EAAE;IACrB,MAAMQ,cAAc,GAAID,SAAS,GAAGP,UAAU,GAAI,GAAG;IACrD,MAAMS,QAAQ,GAAID,cAAc,GAAG,GAAG,GAAI,IAAI,CAAC9d,YAAY,CAAC8K,QAAQ;IAEpE,IAAI,CAAC9K,YAAY,CAAC+K,WAAW,GAAGgT,QAAQ;EAC1C;EAEAC,gBAAgBA,CAAChU,OAAY;IAC3B,MAAMN,SAAS,GAAGM,OAAO,CAAClJ,EAAE;IAC5B,MAAMoK,IAAI,GAAG,IAAI,CAACN,oBAAoB,CAAClB,SAAS,CAAC;IAEjD;IACA,MAAMuU,QAAQ,GAAG/S,IAAI,CAACF,KAAK,KAAK,CAAC,GAAG,GAAG,GAAGE,IAAI,CAACF,KAAK,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC;IAEpE,IAAI,CAACC,oBAAoB,CAACvB,SAAS,EAAE;MAAEsB,KAAK,EAAEiT;IAAQ,CAAE,CAAC;IAEzD,IAAI,IAAI,CAACje,YAAY,IAAI,IAAI,CAACC,gBAAgB,KAAKyJ,SAAS,EAAE;MAC5D,IAAI,CAAC1J,YAAY,CAAC0c,YAAY,GAAGuB,QAAQ;;IAG3C,IAAI,CAACvgB,YAAY,CAACgO,WAAW,CAAC,YAAYuS,QAAQ,GAAG,CAAC;EACxD;EAEA;EAEAC,gBAAgBA,CAAClU,OAAY;IAC3B,IAAI,CAACgU,gBAAgB,CAAChU,OAAO,CAAC;EAChC;EAEAmU,aAAaA,CAACnU,OAAY;IACxB,MAAMkB,IAAI,GAAG,IAAI,CAACN,oBAAoB,CAACZ,OAAO,CAAClJ,EAAE,CAAC;IAClD,OAAOoK,IAAI,CAACF,KAAK,IAAI,CAAC;EACxB;EAEAoT,WAAWA,CAAA;IACT,IAAI,CAAC3c,aAAa,CAACsG,WAAW,EAAE;IAEhC;IACA,IAAI,IAAI,CAACzH,SAAS,EAAE;MAClBkV,aAAa,CAAC,IAAI,CAAClV,SAAS,CAAC;;IAE/B,IAAI,IAAI,CAACR,cAAc,EAAE;MACvB0V,aAAa,CAAC,IAAI,CAAC1V,cAAc,CAAC;;IAEpC,IAAI,IAAI,CAAC0B,aAAa,EAAE;MACtBua,YAAY,CAAC,IAAI,CAACva,aAAa,CAAC;;IAGlC;IACA,IAAI,IAAI,CAAC5B,aAAa,EAAE;MACtB,IAAI,IAAI,CAACA,aAAa,CAAC8X,KAAK,KAAK,WAAW,EAAE;QAC5C,IAAI,CAAC9X,aAAa,CAAC+X,IAAI,EAAE;;MAE3B,IAAI,CAAC/X,aAAa,CAAC4W,MAAM,EAAEoB,SAAS,EAAE,CAACnP,OAAO,CAAEoP,KAAK,IAAKA,KAAK,CAACF,IAAI,EAAE,CAAC;;IAGzE;IACA,IAAI,CAAC2E,iBAAiB,EAAE;EAC1B;CACD;AA3iFyC+B,UAAA,EAAvCthB,SAAS,CAAC,mBAAmB,CAAC,C,8DAAwC;AAEvEshB,UAAA,EADCthB,SAAS,CAAC,WAAW,EAAE;EAAEuhB,MAAM,EAAE;AAAK,CAAE,CAAC,C,sDACD;AAEzCD,UAAA,EADCthB,SAAS,CAAC,YAAY,EAAE;EAAEuhB,MAAM,EAAE;AAAK,CAAE,CAAC,C,uDACD;AAE1CD,UAAA,EADCthB,SAAS,CAAC,aAAa,EAAE;EAAEuhB,MAAM,EAAE;AAAK,CAAE,CAAC,C,wDACD;AAE3CD,UAAA,EADCthB,SAAS,CAAC,kBAAkB,EAAE;EAAEuhB,MAAM,EAAE;AAAK,CAAE,CAAC,C,6DACD;AAEhDD,UAAA,EADCthB,SAAS,CAAC,mBAAmB,EAAE;EAAEuhB,MAAM,EAAE;AAAK,CAAE,CAAC,C,8DACD;AAZtCnhB,oBAAoB,GAAAkhB,UAAA,EAJhCvhB,SAAS,CAAC;EACTyhB,QAAQ,EAAE,kBAAkB;EAC5BC,WAAW,EAAE;CACd,CAAC,C,EACWrhB,oBAAoB,CA6iFhC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}