"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[292],{3292:(x,n,i)=>{i.r(n),i.d(n,{ForgotPasswordModule:()=>k});var l=i(177),o=i(4341),f=i(6647),t=i(7705),u=i(4796);function c(r,m){1&r&&(t.j41(0,"div",33),t.nrm(1,"i",34),t.EFF(2," Email is required "),t.k0s())}function b(r,m){if(1&r&&(t.j41(0,"div",35)(1,"div",36)(2,"div",37),t.nrm(3,"i",38)(4,"div",39),t.k0s(),t.j41(5,"div",40)(6,"p",41),t.<PERSON><PERSON>(7),t.k0s()()()()),2&r){const d=t.XpG();t.R7$(7),t.SpI(" ",d.error," ")}}function g(r,m){if(1&r&&(t.j41(0,"div",42)(1,"div",36)(2,"div",43),t.nrm(3,"i",44)(4,"div",45),t.k0s(),t.j41(5,"div",40)(6,"p",46),t.EFF(7),t.k0s()()()()),2&r){const d=t.XpG();t.R7$(7),t.SpI(" ",d.message," ")}}const p=[{path:"",component:(()=>{class r{constructor(d,e,a){this.fb=d,this.authService=e,this.router=a,this.message="",this.error="",this.forgotForm=this.fb.group({email:["",[o.k0.required,o.k0.email]]})}onSubmit(){if(this.forgotForm.invalid)return;const d=this.forgotForm.value.email;this.authService.forgotPassword(d).subscribe({next:e=>{this.message=e.message,this.error="",setTimeout(()=>this.router.navigate(["/reset-password"],{queryParams:{email:d}}),1500)},error:e=>{this.error=e.error.message||"Something went wrong.",this.message=""}})}static{this.\u0275fac=function(e){return new(e||r)(t.rXU(o.ok),t.rXU(u.u),t.rXU(f.Ix))}}static{this.\u0275cmp=t.VBU({type:r,selectors:[["app-forgot-password"]],decls:50,vars:5,consts:[[1,"container-fluid","p-4","md:p-6","bg-[#edf1f4]","dark:bg-[#121212]","min-h-screen","flex","items-center","justify-center","relative","futuristic-layout"],[1,"absolute","inset-0","overflow-hidden","pointer-events-none"],[1,"absolute","top-[15%]","left-[10%]","w-64","h-64","rounded-full","bg-gradient-to-br","from-[#4f5fad]/5","to-transparent","dark:from-[#6d78c9]/3","dark:to-transparent","blur-3xl"],[1,"absolute","bottom-[20%]","right-[10%]","w-80","h-80","rounded-full","bg-gradient-to-tl","from-[#4f5fad]/5","to-transparent","dark:from-[#6d78c9]/3","dark:to-transparent","blur-3xl"],[1,"absolute","inset-0","opacity-5","dark:opacity-[0.03]"],[1,"h-full","grid","grid-cols-12"],[1,"border-r","border-[#4f5fad]","dark:border-[#6d78c9]"],[1,"w-full","max-w-md","relative","z-10"],[1,"bg-white","dark:bg-[#1e1e1e]","rounded-xl","shadow-lg","dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)]","overflow-hidden","backdrop-blur-sm","border","border-[#edf1f4]/50","dark:border-[#2a2a2a]","relative"],[1,"absolute","top-0","left-0","right-0","h-1","bg-gradient-to-r","from-[#3d4a85]","to-[#4f5fad]","dark:from-[#6d78c9]","dark:to-[#4f5fad]"],[1,"absolute","top-0","left-0","right-0","h-1","bg-gradient-to-r","from-[#3d4a85]","to-[#4f5fad]","dark:from-[#6d78c9]","dark:to-[#4f5fad]","blur-md"],[1,"p-6","text-center"],[1,"text-2xl","font-bold","bg-gradient-to-r","from-[#3d4a85]","to-[#4f5fad]","dark:from-[#6d78c9]","dark:to-[#4f5fad]","bg-clip-text","text-transparent"],[1,"text-sm","text-[#6d6870]","dark:text-[#a0a0a0]","mt-2"],[1,"p-6"],[1,"space-y-5",3,"formGroup","ngSubmit"],[1,"group"],["for","email",1,"flex","items-center","text-sm","font-medium","text-[#4f5fad]","dark:text-[#6d78c9]","mb-2"],[1,"fas","fa-envelope","mr-1.5","text-xs"],[1,"relative"],["id","email","type","email","formControlName","email","placeholder","<EMAIL>","required","",1,"w-full","px-4","py-2.5","text-sm","rounded-lg","border","border-[#bdc6cc]","dark:border-[#2a2a2a]","bg-white","dark:bg-[#1e1e1e]","text-[#6d6870]","dark:text-[#e0e0e0]","focus:outline-none","focus:border-[#4f5fad]","dark:focus:border-[#6d78c9]","focus:ring-2","focus:ring-[#4f5fad]/20","dark:focus:ring-[#6d78c9]/20","transition-all"],[1,"absolute","inset-y-0","left-0","pl-3","flex","items-center","pointer-events-none","opacity-0","group-focus-within:opacity-100","transition-opacity"],[1,"w-0.5","h-4","bg-gradient-to-b","from-[#3d4a85]","to-[#4f5fad]","dark:from-[#6d78c9]","dark:to-[#4f5fad]","rounded-full"],["class","text-[#ff6b69] dark:text-[#ff8785] text-xs mt-1.5 flex items-center",4,"ngIf"],["class","bg-[#ff6b69]/10 dark:bg-[#ff6b69]/5 border border-[#ff6b69] dark:border-[#ff6b69]/30 rounded-lg p-3 backdrop-blur-sm",4,"ngIf"],["class","bg-[#4f5fad]/10 dark:bg-[#6d78c9]/5 border border-[#4f5fad] dark:border-[#6d78c9]/30 rounded-lg p-3 backdrop-blur-sm",4,"ngIf"],["type","submit",1,"w-full","relative","overflow-hidden","group","mt-6",3,"disabled"],[1,"absolute","inset-0","bg-gradient-to-r","from-[#3d4a85]","to-[#4f5fad]","dark:from-[#3d4a85]","dark:to-[#6d78c9]","rounded-lg","transition-transform","duration-300","group-hover:scale-105","disabled:opacity-50"],[1,"absolute","inset-0","bg-gradient-to-r","from-[#3d4a85]","to-[#4f5fad]","dark:from-[#3d4a85]","dark:to-[#6d78c9]","rounded-lg","opacity-0","group-hover:opacity-100","blur-xl","transition-opacity","duration-300","disabled:opacity-0"],[1,"relative","flex","items-center","justify-center","text-white","font-medium","py-2.5","px-4","rounded-lg","transition-all","z-10"],[1,"fas","fa-paper-plane","mr-2"],[1,"text-center","text-sm","text-[#6d6870]","dark:text-[#a0a0a0]","space-y-2","pt-4"],["routerLink","/login",1,"text-[#4f5fad]","dark:text-[#6d78c9]","hover:text-[#3d4a85]","dark:hover:text-[#4f5fad]","transition-colors","font-medium"],[1,"text-[#ff6b69]","dark:text-[#ff8785]","text-xs","mt-1.5","flex","items-center"],[1,"fas","fa-exclamation-circle","mr-1"],[1,"bg-[#ff6b69]/10","dark:bg-[#ff6b69]/5","border","border-[#ff6b69]","dark:border-[#ff6b69]/30","rounded-lg","p-3","backdrop-blur-sm"],[1,"flex","items-start"],[1,"text-[#ff6b69]","dark:text-[#ff8785]","mr-2","text-base","relative"],[1,"fas","fa-exclamation-triangle"],[1,"absolute","inset-0","bg-[#ff6b69]/20","dark:bg-[#ff8785]/20","blur-xl","rounded-full","transform","scale-150","-z-10"],[1,"flex-1"],[1,"text-xs","text-[#ff6b69]","dark:text-[#ff8785]"],[1,"bg-[#4f5fad]/10","dark:bg-[#6d78c9]/5","border","border-[#4f5fad]","dark:border-[#6d78c9]/30","rounded-lg","p-3","backdrop-blur-sm"],[1,"text-[#4f5fad]","dark:text-[#6d78c9]","mr-2","text-base","relative"],[1,"fas","fa-check-circle"],[1,"absolute","inset-0","bg-[#4f5fad]/20","dark:bg-[#6d78c9]/20","blur-xl","rounded-full","transform","scale-150","-z-10"],[1,"text-xs","text-[#4f5fad]","dark:text-[#6d78c9]"]],template:function(e,a){if(1&e&&(t.j41(0,"div",0)(1,"div",1),t.nrm(2,"div",2)(3,"div",3),t.j41(4,"div",4)(5,"div",5),t.nrm(6,"div",6)(7,"div",6)(8,"div",6)(9,"div",6)(10,"div",6)(11,"div",6)(12,"div",6)(13,"div",6)(14,"div",6)(15,"div",6)(16,"div",6),t.k0s()()(),t.j41(17,"div",7)(18,"div",8),t.nrm(19,"div",9)(20,"div",10),t.j41(21,"div",11)(22,"h1",12),t.EFF(23," Forgot Password "),t.k0s(),t.j41(24,"p",13),t.EFF(25," Enter your email to receive a reset code "),t.k0s()(),t.j41(26,"div",14)(27,"form",15),t.bIt("ngSubmit",function(){return a.onSubmit()}),t.j41(28,"div",16)(29,"label",17),t.nrm(30,"i",18),t.EFF(31," Email "),t.k0s(),t.j41(32,"div",19),t.nrm(33,"input",20),t.j41(34,"div",21),t.nrm(35,"div",22),t.k0s()(),t.DNE(36,c,3,0,"div",23),t.k0s(),t.DNE(37,b,8,1,"div",24),t.DNE(38,g,8,1,"div",25),t.j41(39,"button",26),t.nrm(40,"div",27)(41,"div",28),t.j41(42,"span",29),t.nrm(43,"i",30),t.EFF(44," Send Reset Code "),t.k0s()(),t.j41(45,"div",31)(46,"div"),t.EFF(47," Remember your password? "),t.j41(48,"a",32),t.EFF(49," Sign in "),t.k0s()()()()()()()()),2&e){let s;t.R7$(27),t.Y8G("formGroup",a.forgotForm),t.R7$(9),t.Y8G("ngIf",(null==(s=a.forgotForm.get("email"))?null:s.invalid)&&(null==(s=a.forgotForm.get("email"))?null:s.touched)),t.R7$(1),t.Y8G("ngIf",a.error),t.R7$(1),t.Y8G("ngIf",a.message),t.R7$(1),t.Y8G("disabled",a.forgotForm.invalid)}},dependencies:[l.bT,o.qT,o.me,o.BC,o.cb,o.YS,o.j4,o.JD,f.Wk]})}}return r})()}];let v=(()=>{class r{static{this.\u0275fac=function(e){return new(e||r)}}static{this.\u0275mod=t.$C({type:r})}static{this.\u0275inj=t.G2t({imports:[f.iI.forChild(p),f.iI]})}}return r})(),k=(()=>{class r{static{this.\u0275fac=function(e){return new(e||r)}}static{this.\u0275mod=t.$C({type:r})}static{this.\u0275inj=t.G2t({imports:[l.MD,o.YN,o.X1,v]})}}return r})()}}]);