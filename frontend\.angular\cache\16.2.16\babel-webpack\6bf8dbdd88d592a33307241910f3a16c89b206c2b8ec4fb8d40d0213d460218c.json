{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/OneDrive/Bureau/Project PI/devBridge/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Validators } from '@angular/forms';\nimport { Subscription } from 'rxjs';\nimport { CallType } from '../../../../models/message.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"../../../../services/message.service\";\nimport * as i4 from \"../../../../services/call.service\";\nimport * as i5 from \"../../../../services/toast.service\";\nimport * as i6 from \"@angular/common\";\nconst _c0 = [\"messagesContainer\"];\nconst _c1 = [\"fileInput\"];\nconst _c2 = [\"localVideo\"];\nconst _c3 = [\"remoteVideo\"];\nconst _c4 = [\"localVideoHidden\"];\nconst _c5 = [\"remoteVideoHidden\"];\nfunction MessageChatComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 55);\n  }\n}\nfunction MessageChatComponent_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 56)(1, \"span\");\n    i0.ɵɵtext(2, \"En train d'\\u00E9crire\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 57);\n    i0.ɵɵelement(4, \"div\", 58)(5, \"div\", 59)(6, \"div\", 60);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction MessageChatComponent_span_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r4.otherParticipant == null ? null : ctx_r4.otherParticipant.isOnline) ? \"En ligne\" : ctx_r4.formatLastActive(ctx_r4.otherParticipant == null ? null : ctx_r4.otherParticipant.lastActive), \" \");\n  }\n}\nfunction MessageChatComponent_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 61)(1, \"div\", 62)(2, \"button\", 63);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_29_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r19 = i0.ɵɵnextContext();\n      ctx_r19.toggleSearch();\n      return i0.ɵɵresetView(ctx_r19.showMainMenu = false);\n    });\n    i0.ɵɵelement(3, \"i\", 64);\n    i0.ɵɵelementStart(4, \"span\", 65);\n    i0.ɵɵtext(5, \"Rechercher\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"button\", 66);\n    i0.ɵɵelement(7, \"i\", 67);\n    i0.ɵɵelementStart(8, \"span\", 65);\n    i0.ɵɵtext(9, \"Voir le profil\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(10, \"hr\", 68);\n    i0.ɵɵelementStart(11, \"button\", 66);\n    i0.ɵɵelement(12, \"i\", 69);\n    i0.ɵɵelementStart(13, \"span\", 65);\n    i0.ɵɵtext(14, \"Param\\u00E8tres\");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nfunction MessageChatComponent_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 70)(1, \"div\", 71);\n    i0.ɵɵelement(2, \"i\", 72);\n    i0.ɵɵelementStart(3, \"p\", 73);\n    i0.ɵɵtext(4, \" D\\u00E9posez vos fichiers ici \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 74);\n    i0.ɵɵtext(6, \" Images, vid\\u00E9os, documents... \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction MessageChatComponent_div_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 75);\n    i0.ɵɵelement(1, \"div\", 76);\n    i0.ɵɵelementStart(2, \"span\", 77);\n    i0.ɵɵtext(3, \"Chargement des messages...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction MessageChatComponent_div_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 78)(1, \"div\", 79);\n    i0.ɵɵelement(2, \"i\", 80);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\", 81);\n    i0.ɵɵtext(4, \" Aucun message \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 82);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" Commencez votre conversation avec \", ctx_r9.otherParticipant == null ? null : ctx_r9.otherParticipant.username, \" \");\n  }\n}\nfunction MessageChatComponent_div_35_ng_container_1_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 96)(1, \"div\", 97)(2, \"span\", 98);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const message_r23 = i0.ɵɵnextContext().$implicit;\n    const ctx_r25 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r25.formatDateSeparator(message_r23.timestamp), \" \");\n  }\n}\nfunction MessageChatComponent_div_35_ng_container_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r35 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 99)(1, \"img\", 100);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_35_ng_container_1_div_3_Template_img_click_1_listener() {\n      i0.ɵɵrestoreView(_r35);\n      const message_r23 = i0.ɵɵnextContext().$implicit;\n      const ctx_r33 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r33.openUserProfile(message_r23.sender == null ? null : message_r23.sender.id));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const message_r23 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", (message_r23.sender == null ? null : message_r23.sender.image) || \"assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", message_r23.sender == null ? null : message_r23.sender.username);\n  }\n}\nfunction MessageChatComponent_div_35_ng_container_1_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 101);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r23 = i0.ɵɵnextContext().$implicit;\n    const ctx_r27 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleProp(\"color\", ctx_r27.getUserColor(message_r23.sender == null ? null : message_r23.sender.id));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", message_r23.sender == null ? null : message_r23.sender.username, \" \");\n  }\n}\nfunction MessageChatComponent_div_35_ng_container_1_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 102);\n    i0.ɵɵelement(1, \"div\", 103);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r23 = i0.ɵɵnextContext().$implicit;\n    const ctx_r28 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"innerHTML\", ctx_r28.formatMessageContent(message_r23.content), i0.ɵɵsanitizeHtml);\n  }\n}\nfunction MessageChatComponent_div_35_ng_container_1_div_7_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 107);\n  }\n  if (rf & 2) {\n    const message_r23 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r39 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleProp(\"color\", (message_r23.sender == null ? null : message_r23.sender.id) === ctx_r39.currentUserId ? \"#ffffff\" : \"#111827\");\n    i0.ɵɵproperty(\"innerHTML\", ctx_r39.formatMessageContent(message_r23.content), i0.ɵɵsanitizeHtml);\n  }\n}\nfunction MessageChatComponent_div_35_ng_container_1_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r43 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 104)(1, \"img\", 105);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_35_ng_container_1_div_7_Template_img_click_1_listener() {\n      i0.ɵɵrestoreView(_r43);\n      const message_r23 = i0.ɵɵnextContext().$implicit;\n      const ctx_r41 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r41.openImageViewer(message_r23));\n    })(\"load\", function MessageChatComponent_div_35_ng_container_1_div_7_Template_img_load_1_listener($event) {\n      i0.ɵɵrestoreView(_r43);\n      const message_r23 = i0.ɵɵnextContext().$implicit;\n      const ctx_r44 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r44.onImageLoad($event, message_r23));\n    })(\"error\", function MessageChatComponent_div_35_ng_container_1_div_7_Template_img_error_1_listener($event) {\n      i0.ɵɵrestoreView(_r43);\n      const message_r23 = i0.ɵɵnextContext().$implicit;\n      const ctx_r46 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r46.onImageError($event, message_r23));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(2, MessageChatComponent_div_35_ng_container_1_div_7_div_2_Template, 1, 3, \"div\", 106);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r23 = i0.ɵɵnextContext().$implicit;\n    const ctx_r29 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", ctx_r29.getImageUrl(message_r23), i0.ɵɵsanitizeUrl)(\"alt\", message_r23.content || \"Image\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r23.content);\n  }\n}\nfunction MessageChatComponent_div_35_ng_container_1_div_8_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 116);\n  }\n  if (rf & 2) {\n    const wave_r51 = ctx.$implicit;\n    const i_r52 = ctx.index;\n    const message_r23 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r49 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleProp(\"height\", ctx_r49.isVoicePlaying(message_r23.id) ? wave_r51 : 8, \"px\")(\"animation\", ctx_r49.isVoicePlaying(message_r23.id) ? \"pulse 1s infinite\" : \"none\")(\"animation-delay\", i_r52 * 0.1 + \"s\");\n  }\n}\nfunction MessageChatComponent_div_35_ng_container_1_div_8_button_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r56 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 117);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_35_ng_container_1_div_8_button_8_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r56);\n      const message_r23 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r54 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r54.changeVoiceSpeed(message_r23));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r23 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r50 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r50.getVoiceSpeed(message_r23), \"x \");\n  }\n}\nfunction MessageChatComponent_div_35_ng_container_1_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r60 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 108)(1, \"button\", 109);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_35_ng_container_1_div_8_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r60);\n      const message_r23 = i0.ɵɵnextContext().$implicit;\n      const ctx_r58 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r58.toggleVoicePlayback(message_r23));\n    });\n    i0.ɵɵelement(2, \"i\", 110);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 111);\n    i0.ɵɵtemplate(4, MessageChatComponent_div_35_ng_container_1_div_8_div_4_Template, 1, 6, \"div\", 112);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 113)(6, \"div\", 114);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, MessageChatComponent_div_35_ng_container_1_div_8_button_8_Template, 2, 1, \"button\", 115);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const message_r23 = i0.ɵɵnextContext().$implicit;\n    const ctx_r30 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(ctx_r30.isVoicePlaying(message_r23.id) ? \"fas fa-pause\" : \"fas fa-play\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r30.voiceWaves);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r30.getVoiceDuration(message_r23), \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r30.isVoicePlaying(message_r23.id));\n  }\n}\nfunction MessageChatComponent_div_35_ng_container_1_div_12_i_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 123);\n  }\n}\nfunction MessageChatComponent_div_35_ng_container_1_div_12_i_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 124);\n  }\n}\nfunction MessageChatComponent_div_35_ng_container_1_div_12_i_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 125);\n  }\n}\nfunction MessageChatComponent_div_35_ng_container_1_div_12_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 126);\n  }\n}\nfunction MessageChatComponent_div_35_ng_container_1_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 118);\n    i0.ɵɵtemplate(1, MessageChatComponent_div_35_ng_container_1_div_12_i_1_Template, 1, 0, \"i\", 119);\n    i0.ɵɵtemplate(2, MessageChatComponent_div_35_ng_container_1_div_12_i_2_Template, 1, 0, \"i\", 120);\n    i0.ɵɵtemplate(3, MessageChatComponent_div_35_ng_container_1_div_12_i_3_Template, 1, 0, \"i\", 121);\n    i0.ɵɵtemplate(4, MessageChatComponent_div_35_ng_container_1_div_12_i_4_Template, 1, 0, \"i\", 122);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r23 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r23.status === \"SENDING\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r23.status === \"SENT\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r23.status === \"DELIVERED\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r23.status === \"READ\");\n  }\n}\nfunction MessageChatComponent_div_35_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r68 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, MessageChatComponent_div_35_ng_container_1_div_1_Template, 4, 1, \"div\", 86);\n    i0.ɵɵelementStart(2, \"div\", 87);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_35_ng_container_1_Template_div_click_2_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r68);\n      const message_r23 = restoredCtx.$implicit;\n      const ctx_r67 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r67.onMessageClick(message_r23, $event));\n    })(\"contextmenu\", function MessageChatComponent_div_35_ng_container_1_Template_div_contextmenu_2_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r68);\n      const message_r23 = restoredCtx.$implicit;\n      const ctx_r69 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r69.onMessageContextMenu(message_r23, $event));\n    });\n    i0.ɵɵtemplate(3, MessageChatComponent_div_35_ng_container_1_div_3_Template, 2, 2, \"div\", 88);\n    i0.ɵɵelementStart(4, \"div\", 89);\n    i0.ɵɵtemplate(5, MessageChatComponent_div_35_ng_container_1_div_5_Template, 2, 3, \"div\", 90);\n    i0.ɵɵtemplate(6, MessageChatComponent_div_35_ng_container_1_div_6_Template, 2, 1, \"div\", 91);\n    i0.ɵɵtemplate(7, MessageChatComponent_div_35_ng_container_1_div_7_Template, 3, 3, \"div\", 92);\n    i0.ɵɵtemplate(8, MessageChatComponent_div_35_ng_container_1_div_8_Template, 9, 5, \"div\", 93);\n    i0.ɵɵelementStart(9, \"div\", 94)(10, \"span\");\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(12, MessageChatComponent_div_35_ng_container_1_div_12_Template, 5, 4, \"div\", 95);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const message_r23 = ctx.$implicit;\n    const i_r24 = ctx.index;\n    const ctx_r21 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r21.shouldShowDateSeparator(i_r24));\n    i0.ɵɵadvance(1);\n    i0.ɵɵstyleProp(\"justify-content\", (message_r23.sender == null ? null : message_r23.sender.id) === ctx_r21.currentUserId ? \"flex-end\" : \"flex-start\");\n    i0.ɵɵproperty(\"id\", \"message-\" + message_r23.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (message_r23.sender == null ? null : message_r23.sender.id) !== ctx_r21.currentUserId && ctx_r21.shouldShowAvatar(i_r24));\n    i0.ɵɵadvance(1);\n    i0.ɵɵstyleProp(\"background-color\", (message_r23.sender == null ? null : message_r23.sender.id) === ctx_r21.currentUserId ? \"#3b82f6\" : \"#ffffff\")(\"color\", (message_r23.sender == null ? null : message_r23.sender.id) === ctx_r21.currentUserId ? \"#ffffff\" : \"#111827\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r21.isGroupConversation() && (message_r23.sender == null ? null : message_r23.sender.id) !== ctx_r21.currentUserId && ctx_r21.shouldShowSenderName(i_r24));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r21.getMessageType(message_r23) === \"text\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r21.hasImage(message_r23));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r21.getMessageType(message_r23) === \"audio\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r21.formatMessageTime(message_r23.timestamp));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (message_r23.sender == null ? null : message_r23.sender.id) === ctx_r21.currentUserId);\n  }\n}\nfunction MessageChatComponent_div_35_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 127);\n    i0.ɵɵelement(1, \"img\", 128);\n    i0.ɵɵelementStart(2, \"div\", 129)(3, \"div\", 130);\n    i0.ɵɵelement(4, \"div\", 131)(5, \"div\", 132)(6, \"div\", 133);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r22 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", (ctx_r22.otherParticipant == null ? null : ctx_r22.otherParticipant.image) || \"assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", ctx_r22.otherParticipant == null ? null : ctx_r22.otherParticipant.username);\n  }\n}\nfunction MessageChatComponent_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 83);\n    i0.ɵɵtemplate(1, MessageChatComponent_div_35_ng_container_1_Template, 13, 15, \"ng-container\", 84);\n    i0.ɵɵtemplate(2, MessageChatComponent_div_35_div_2_Template, 7, 2, \"div\", 85);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r10.messages)(\"ngForTrackBy\", ctx_r10.trackByMessageId);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.otherUserIsTyping);\n  }\n}\nfunction MessageChatComponent_div_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 134);\n  }\n}\nfunction MessageChatComponent_i_49_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 135);\n  }\n}\nfunction MessageChatComponent_div_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 136);\n  }\n}\nfunction MessageChatComponent_div_51_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r73 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 142);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_51_button_5_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r73);\n      const emoji_r71 = restoredCtx.$implicit;\n      const ctx_r72 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r72.insertEmoji(emoji_r71));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const emoji_r71 = ctx.$implicit;\n    i0.ɵɵproperty(\"title\", emoji_r71.name);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", emoji_r71.emoji, \" \");\n  }\n}\nfunction MessageChatComponent_div_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 137)(1, \"div\", 138)(2, \"h4\", 139);\n    i0.ɵɵtext(3, \" \\u00C9mojis \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 140);\n    i0.ɵɵtemplate(5, MessageChatComponent_div_51_button_5_Template, 2, 2, \"button\", 141);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r14.getEmojisForCategory(ctx_r14.selectedEmojiCategory));\n  }\n}\nfunction MessageChatComponent_div_52_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r75 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 143)(1, \"div\", 138)(2, \"h4\", 139);\n    i0.ɵɵtext(3, \" Pi\\u00E8ces jointes \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 144)(5, \"button\", 145);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_52_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r75);\n      const ctx_r74 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r74.triggerFileInput(\"image\"));\n    });\n    i0.ɵɵelementStart(6, \"div\", 146);\n    i0.ɵɵelement(7, \"i\", 147);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"span\", 148);\n    i0.ɵɵtext(9, \"Images\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"button\", 145);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_52_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r75);\n      const ctx_r76 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r76.triggerFileInput(\"document\"));\n    });\n    i0.ɵɵelementStart(11, \"div\", 149);\n    i0.ɵɵelement(12, \"i\", 150);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"span\", 148);\n    i0.ɵɵtext(14, \"Documents\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"button\", 145);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_52_Template_button_click_15_listener() {\n      i0.ɵɵrestoreView(_r75);\n      const ctx_r77 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r77.openCamera());\n    });\n    i0.ɵɵelementStart(16, \"div\", 151);\n    i0.ɵɵelement(17, \"i\", 152);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"span\", 148);\n    i0.ɵɵtext(19, \"Cam\\u00E9ra\");\n    i0.ɵɵelementEnd()()()()();\n  }\n}\nfunction MessageChatComponent_div_55_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r79 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 153);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_55_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r79);\n      const ctx_r78 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r78.closeAllMenus());\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MessageChatComponent_div_56_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 166);\n  }\n  if (rf & 2) {\n    const wave_r81 = ctx.$implicit;\n    const i_r82 = ctx.index;\n    i0.ɵɵstyleProp(\"height\", wave_r81, \"px\")(\"animation\", \"bounce 1s infinite\")(\"animation-delay\", i_r82 * 0.1 + \"s\");\n  }\n}\nfunction MessageChatComponent_div_56_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r84 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 154)(1, \"div\", 155);\n    i0.ɵɵelement(2, \"i\", 156);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 157)(4, \"div\", 158);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 159);\n    i0.ɵɵtemplate(7, MessageChatComponent_div_56_div_7_Template, 1, 6, \"div\", 160);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 161);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 37)(11, \"button\", 162);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_56_Template_button_click_11_listener($event) {\n      i0.ɵɵrestoreView(_r84);\n      const ctx_r83 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r83.onRecordCancel($event));\n    });\n    i0.ɵɵelement(12, \"i\", 163);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"button\", 164);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_56_Template_button_click_13_listener($event) {\n      i0.ɵɵrestoreView(_r84);\n      const ctx_r85 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r85.onRecordEnd($event));\n    });\n    i0.ɵɵelement(14, \"i\", 165);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r18 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r18.formatRecordingDuration(ctx_r18.voiceRecordingDuration), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r18.voiceWaves);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" Format: \", ctx_r18.getRecordingFormat(), \" \");\n  }\n}\nexport let MessageChatComponent = /*#__PURE__*/(() => {\n  class MessageChatComponent {\n    constructor(fb, route, router, MessageService, callService, toastService, cdr) {\n      this.fb = fb;\n      this.route = route;\n      this.router = router;\n      this.MessageService = MessageService;\n      this.callService = callService;\n      this.toastService = toastService;\n      this.cdr = cdr;\n      // === DONNÉES PRINCIPALES ===\n      this.conversation = null;\n      this.messages = [];\n      this.currentUserId = null;\n      this.currentUsername = 'You';\n      this.otherParticipant = null;\n      // === ÉTATS DE L'INTERFACE ===\n      this.isLoading = false;\n      this.isLoadingMore = false;\n      this.hasMoreMessages = true;\n      this.showEmojiPicker = false;\n      this.showAttachmentMenu = false;\n      this.showSearch = false;\n      this.searchQuery = '';\n      this.searchResults = [];\n      this.searchMode = false;\n      this.isSendingMessage = false;\n      this.otherUserIsTyping = false;\n      this.showMainMenu = false;\n      this.showMessageContextMenu = false;\n      this.selectedMessage = null;\n      this.contextMenuPosition = {\n        x: 0,\n        y: 0\n      };\n      this.showReactionPicker = false;\n      this.reactionPickerMessage = null;\n      this.showImageViewer = false;\n      this.selectedImage = null;\n      this.uploadProgress = 0;\n      this.isUploading = false;\n      this.isDragOver = false;\n      // === GESTION VOCALE OPTIMISÉE ===\n      this.isRecordingVoice = false;\n      this.voiceRecordingDuration = 0;\n      this.voiceRecordingState = 'idle';\n      this.mediaRecorder = null;\n      this.audioChunks = [];\n      this.recordingTimer = null;\n      this.voiceWaves = [4, 8, 12, 16, 20, 16, 12, 8, 4, 8, 12, 16, 20, 16, 12, 8];\n      // Lecture des messages vocaux\n      this.currentAudio = null;\n      this.playingMessageId = null;\n      this.voicePlayback = {};\n      // === APPELS WEBRTC ===\n      this.isInCall = false;\n      this.callType = null;\n      this.callDuration = 0;\n      this.callTimer = null;\n      // État de l'appel WebRTC - Géré globalement par les composants d'appel\n      // activeCall, isMuted, isVideoEnabled sont maintenant dans ActiveCallComponent\n      // === ÉMOJIS ===\n      this.emojiCategories = [{\n        id: 'smileys',\n        name: 'Smileys',\n        icon: '😀',\n        emojis: [{\n          emoji: '😀',\n          name: 'grinning face'\n        }, {\n          emoji: '😃',\n          name: 'grinning face with big eyes'\n        }, {\n          emoji: '😄',\n          name: 'grinning face with smiling eyes'\n        }, {\n          emoji: '😁',\n          name: 'beaming face with smiling eyes'\n        }, {\n          emoji: '😆',\n          name: 'grinning squinting face'\n        }, {\n          emoji: '😅',\n          name: 'grinning face with sweat'\n        }, {\n          emoji: '😂',\n          name: 'face with tears of joy'\n        }, {\n          emoji: '🤣',\n          name: 'rolling on the floor laughing'\n        }, {\n          emoji: '😊',\n          name: 'smiling face with smiling eyes'\n        }, {\n          emoji: '😇',\n          name: 'smiling face with halo'\n        }]\n      }, {\n        id: 'people',\n        name: 'People',\n        icon: '👤',\n        emojis: [{\n          emoji: '👶',\n          name: 'baby'\n        }, {\n          emoji: '🧒',\n          name: 'child'\n        }, {\n          emoji: '👦',\n          name: 'boy'\n        }, {\n          emoji: '👧',\n          name: 'girl'\n        }, {\n          emoji: '🧑',\n          name: 'person'\n        }, {\n          emoji: '👨',\n          name: 'man'\n        }, {\n          emoji: '👩',\n          name: 'woman'\n        }, {\n          emoji: '👴',\n          name: 'old man'\n        }, {\n          emoji: '👵',\n          name: 'old woman'\n        }]\n      }, {\n        id: 'nature',\n        name: 'Nature',\n        icon: '🌿',\n        emojis: [{\n          emoji: '🐶',\n          name: 'dog face'\n        }, {\n          emoji: '🐱',\n          name: 'cat face'\n        }, {\n          emoji: '🐭',\n          name: 'mouse face'\n        }, {\n          emoji: '🐹',\n          name: 'hamster'\n        }, {\n          emoji: '🐰',\n          name: 'rabbit face'\n        }, {\n          emoji: '🦊',\n          name: 'fox'\n        }, {\n          emoji: '🐻',\n          name: 'bear'\n        }, {\n          emoji: '🐼',\n          name: 'panda'\n        }]\n      }];\n      this.selectedEmojiCategory = this.emojiCategories[0];\n      // === PAGINATION ===\n      this.MAX_MESSAGES_TO_LOAD = 10;\n      this.currentPage = 1;\n      // === AUTRES ÉTATS ===\n      this.isTyping = false;\n      this.isUserTyping = false;\n      this.typingTimeout = null;\n      this.subscriptions = new Subscription();\n      this.messageForm = this.fb.group({\n        content: ['', [Validators.required, Validators.minLength(1)]]\n      });\n    }\n    // Méthode pour vérifier si le champ de saisie doit être désactivé\n    isInputDisabled() {\n      return !this.otherParticipant || this.isRecordingVoice || this.isSendingMessage;\n    }\n    // Méthode pour gérer l'état du contrôle de saisie\n    updateInputState() {\n      const contentControl = this.messageForm.get('content');\n      if (this.isInputDisabled()) {\n        contentControl?.disable();\n      } else {\n        contentControl?.enable();\n      }\n    }\n    ngOnInit() {\n      this.initializeComponent();\n      // Activer les sons après interaction utilisateur\n      this.enableSoundsOnFirstInteraction();\n    }\n    ngAfterViewInit() {\n      // Configurer les éléments vidéo pour WebRTC après que la vue soit initialisée\n      setTimeout(() => {\n        this.setupVideoElements();\n      }, 100);\n      // Réessayer plusieurs fois pour s'assurer que les éléments sont configurés\n      setTimeout(() => {\n        this.setupVideoElements();\n      }, 500);\n      setTimeout(() => {\n        this.setupVideoElements();\n      }, 1000);\n      setTimeout(() => {\n        this.setupVideoElements();\n      }, 2000);\n    }\n    /**\n     * Configure les éléments vidéo pour WebRTC\n     */\n    setupVideoElements() {\n      // Essayer d'abord les éléments visibles (pour appels vidéo)\n      if (this.localVideo && this.remoteVideo) {\n        this.callService.setVideoElements(this.localVideo.nativeElement, this.remoteVideo.nativeElement);\n      }\n      // Sinon utiliser les éléments cachés (pour appels audio)\n      else if (this.localVideoHidden && this.remoteVideoHidden) {\n        this.callService.setVideoElements(this.localVideoHidden.nativeElement, this.remoteVideoHidden.nativeElement);\n      } else {\n        this.createVideoElementsManually();\n        // Réessayer après un délai\n        setTimeout(() => {\n          this.setupVideoElements();\n        }, 500);\n        // Réessayer encore plus tard\n        setTimeout(() => {\n          this.setupVideoElements();\n        }, 1500);\n      }\n    }\n    /**\n     * Crée les éléments vidéo manuellement si les ViewChild ne fonctionnent pas\n     */\n    createVideoElementsManually() {\n      // Chercher les éléments dans le DOM\n      const localVideoEl = document.getElementById('localVideo');\n      const remoteVideoEl = document.getElementById('remoteVideo');\n      if (localVideoEl && remoteVideoEl) {\n        console.log('✅ [MessageChat] Found video elements in DOM, configuring...');\n        this.callService.setVideoElements(localVideoEl, remoteVideoEl);\n      } else {\n        console.warn('⚠️ [MessageChat] Video elements not found in DOM either');\n        // Créer les éléments dynamiquement\n        const localVideo = document.createElement('video');\n        localVideo.id = 'localVideo';\n        localVideo.autoplay = true;\n        localVideo.muted = true;\n        localVideo.playsInline = true;\n        localVideo.style.cssText = 'position: absolute; top: -9999px; left: -9999px; width: 1px; height: 1px;';\n        const remoteVideo = document.createElement('video');\n        remoteVideo.id = 'remoteVideo';\n        remoteVideo.autoplay = true;\n        remoteVideo.playsInline = true;\n        remoteVideo.style.cssText = 'position: absolute; top: -9999px; left: -9999px; width: 1px; height: 1px;';\n        document.body.appendChild(localVideo);\n        document.body.appendChild(remoteVideo);\n        this.callService.setVideoElements(localVideo, remoteVideo);\n      }\n    }\n    enableSoundsOnFirstInteraction() {\n      const enableSounds = () => {\n        this.callService.enableSounds();\n        document.removeEventListener('click', enableSounds);\n        document.removeEventListener('keydown', enableSounds);\n        document.removeEventListener('touchstart', enableSounds);\n      };\n      document.addEventListener('click', enableSounds, {\n        once: true\n      });\n      document.addEventListener('keydown', enableSounds, {\n        once: true\n      });\n      document.addEventListener('touchstart', enableSounds, {\n        once: true\n      });\n    }\n    initializeComponent() {\n      this.loadCurrentUser();\n      this.loadConversation();\n      this.setupCallSubscriptions();\n    }\n    setupCallSubscriptions() {\n      // Les appels sont maintenant gérés globalement par app-incoming-call et app-active-call\n      // Plus besoin de subscriptions locales ici\n      console.log('📞 [MessageChat] Call subscriptions handled globally');\n    }\n    handleIncomingCall(incomingCall) {\n      // Afficher une notification ou modal d'appel entrant\n      // Pour l'instant, on log juste\n      console.log('🔔 Handling incoming call from:', incomingCall.caller.username);\n      // Jouer la sonnerie\n      this.MessageService.play('ringtone');\n      // Ici on pourrait afficher une modal ou notification\n      // Pour l'instant, on accepte automatiquement pour tester\n      // this.acceptCall(incomingCall);\n    }\n\n    loadCurrentUser() {\n      try {\n        const userString = localStorage.getItem('user');\n        if (!userString || userString === 'null' || userString === 'undefined') {\n          console.error('❌ No user data in localStorage');\n          this.currentUserId = null;\n          this.currentUsername = 'You';\n          return;\n        }\n        const user = JSON.parse(userString);\n        // Essayer différentes propriétés pour l'ID utilisateur\n        const userId = user._id || user.id || user.userId;\n        if (userId) {\n          this.currentUserId = userId;\n          this.currentUsername = user.username || user.name || 'You';\n        } else {\n          console.error('❌ No valid user ID found in user object:', user);\n          this.currentUserId = null;\n          this.currentUsername = 'You';\n        }\n      } catch (error) {\n        console.error('❌ Error parsing user from localStorage:', error);\n        this.currentUserId = null;\n        this.currentUsername = 'You';\n      }\n    }\n    loadConversation() {\n      const conversationId = this.route.snapshot.paramMap.get('id');\n      if (!conversationId) {\n        this.toastService.showError('ID de conversation manquant');\n        return;\n      }\n      this.isLoading = true;\n      // Nettoyer les subscriptions existantes avant de recharger\n      this.cleanupSubscriptions();\n      this.MessageService.getConversation(conversationId).subscribe({\n        next: conversation => {\n          this.conversation = conversation;\n          this.setOtherParticipant();\n          this.loadMessages();\n          // Configurer les subscriptions temps réel après le chargement de la conversation\n          this.setupSubscriptions();\n          this.isLoading = false;\n        },\n        error: error => {\n          console.error('Erreur lors du chargement de la conversation:', error);\n          this.toastService.showError('Erreur lors du chargement de la conversation');\n          this.isLoading = false;\n          // Réessayer après 2 secondes en cas d'erreur\n          setTimeout(() => {\n            this.loadConversation();\n          }, 2000);\n        }\n      });\n    }\n    setOtherParticipant() {\n      if (!this.conversation?.participants || this.conversation.participants.length === 0) {\n        console.warn('No participants found in conversation');\n        this.otherParticipant = null;\n        return;\n      }\n      // Dans une conversation 1-à-1, on veut afficher l'autre personne (pas l'utilisateur actuel)\n      // Dans une conversation de groupe, on peut afficher le nom du groupe ou le premier autre participant\n      if (this.conversation.isGroup) {\n        // Pour les groupes, on pourrait afficher le nom du groupe\n        // Mais pour l'instant, on prend le premier participant qui n'est pas l'utilisateur actuel\n        this.otherParticipant = this.conversation.participants.find(p => {\n          const participantId = p.id || p._id;\n          return String(participantId) !== String(this.currentUserId);\n        });\n      } else {\n        // Pour les conversations 1-à-1, on prend l'autre participant\n        this.otherParticipant = this.conversation.participants.find(p => {\n          const participantId = p.id || p._id;\n          console.log('Comparing participant ID:', participantId, 'with current user ID:', this.currentUserId);\n          return String(participantId) !== String(this.currentUserId);\n        });\n      }\n      // Fallback si aucun autre participant n'est trouvé\n      if (!this.otherParticipant && this.conversation.participants.length > 0) {\n        this.otherParticipant = this.conversation.participants[0];\n        // Si le premier participant est l'utilisateur actuel et qu'il y en a d'autres\n        if (this.conversation.participants.length > 1) {\n          const firstParticipantId = this.otherParticipant.id || this.otherParticipant._id;\n          if (String(firstParticipantId) === String(this.currentUserId)) {\n            console.log('First participant is current user, using second participant');\n            this.otherParticipant = this.conversation.participants[1];\n          }\n        }\n      }\n      // Vérification finale et logs\n      if (this.otherParticipant) {\n        // Log très visible pour debug\n        console.log('🎯 FINAL RESULT: otherParticipant =', this.otherParticipant.username);\n        console.log('🎯 Should display in sidebar:', this.otherParticipant.username);\n      } else {\n        console.error('❌ No other participant found! This should not happen.');\n        // Log très visible pour debug\n      }\n      // Mettre à jour l'état du champ de saisie\n      this.updateInputState();\n    }\n    loadMessages() {\n      if (!this.conversation?.id) return;\n      // Les messages sont déjà chargés avec la conversation\n      let messages = this.conversation.messages || [];\n      // Trier les messages par timestamp (plus anciens en premier)\n      this.messages = messages.sort((a, b) => {\n        const dateA = new Date(a.timestamp || a.createdAt).getTime();\n        const dateB = new Date(b.timestamp || b.createdAt).getTime();\n        return dateA - dateB; // Ordre croissant (plus anciens en premier)\n      });\n\n      console.log('📋 Messages loaded and sorted:', {\n        total: this.messages.length,\n        first: this.messages[0]?.content,\n        last: this.messages[this.messages.length - 1]?.content\n      });\n      this.hasMoreMessages = this.messages.length === this.MAX_MESSAGES_TO_LOAD;\n      this.isLoading = false;\n      this.scrollToBottom();\n    }\n    loadMoreMessages() {\n      if (this.isLoadingMore || !this.hasMoreMessages || !this.conversation?.id) return;\n      this.isLoadingMore = true;\n      this.currentPage++;\n      // Calculer l'offset basé sur les messages déjà chargés\n      const offset = this.messages.length;\n      this.MessageService.getMessages(this.currentUserId,\n      // senderId\n      this.otherParticipant?.id || this.otherParticipant?._id,\n      // receiverId\n      this.conversation.id, this.currentPage, this.MAX_MESSAGES_TO_LOAD).subscribe({\n        next: newMessages => {\n          if (newMessages && newMessages.length > 0) {\n            // Ajouter les nouveaux messages au début de la liste\n            this.messages = [...newMessages.reverse(), ...this.messages];\n            this.hasMoreMessages = newMessages.length === this.MAX_MESSAGES_TO_LOAD;\n          } else {\n            this.hasMoreMessages = false;\n          }\n          this.isLoadingMore = false;\n        },\n        error: error => {\n          console.error('Erreur lors du chargement des messages:', error);\n          this.toastService.showError('Erreur lors du chargement des messages');\n          this.isLoadingMore = false;\n          this.currentPage--; // Revenir à la page précédente en cas d'erreur\n        }\n      });\n    }\n    /**\n     * Nettoie les subscriptions existantes\n     */\n    cleanupSubscriptions() {\n      this.subscriptions.unsubscribe();\n      this.subscriptions = new Subscription();\n    }\n    /**\n     * Recharge la conversation actuelle\n     */\n    reloadConversation() {\n      if (this.conversation?.id) {\n        // Réinitialiser l'état\n        this.messages = [];\n        this.currentPage = 1;\n        this.hasMoreMessages = true;\n        // Recharger\n        this.loadConversation();\n      }\n    }\n    setupSubscriptions() {\n      if (!this.conversation?.id) {\n        console.warn('❌ Cannot setup subscriptions: no conversation ID');\n        return;\n      }\n      console.log('🔄 Setting up real-time subscriptions for conversation:', this.conversation.id);\n      // Subscription pour les nouveaux messages\n      this.subscriptions.add(this.MessageService.subscribeToNewMessages(this.conversation.id).subscribe({\n        next: newMessage => {\n          console.log('📨 Message structure:', {\n            id: newMessage.id,\n            type: newMessage.type,\n            content: newMessage.content,\n            sender: newMessage.sender,\n            senderId: newMessage.senderId,\n            receiverId: newMessage.receiverId,\n            attachments: newMessage.attachments\n          });\n          // Debug des attachments\n          console.log('📨 [Debug] Message type detected:', this.getMessageType(newMessage));\n          if (newMessage.attachments) {\n            newMessage.attachments.forEach((att, index) => {\n              console.log(`📨 [Debug] Attachment ${index}:`, {\n                type: att.type,\n                url: att.url,\n                path: att.path,\n                name: att.name,\n                size: att.size\n              });\n            });\n          }\n          // Ajouter le message à la liste s'il n'existe pas déjà\n          const messageExists = this.messages.some(msg => msg.id === newMessage.id);\n          if (!messageExists) {\n            // Ajouter le nouveau message à la fin (en bas)\n            this.messages.push(newMessage);\n            console.log('✅ Message added to list, total messages:', this.messages.length);\n            // Forcer la détection de changements\n            this.cdr.detectChanges();\n            // Scroll vers le bas après un court délai\n            setTimeout(() => {\n              this.scrollToBottom();\n            }, 50);\n            // Marquer comme lu si ce n'est pas notre message\n            const senderId = newMessage.sender?.id || newMessage.senderId;\n            console.log('📨 Checking if message should be marked as read:', {\n              senderId,\n              currentUserId: this.currentUserId,\n              shouldMarkAsRead: senderId !== this.currentUserId\n            });\n            if (senderId && senderId !== this.currentUserId) {\n              this.markMessageAsRead(newMessage.id);\n            }\n          }\n        },\n        error: error => {\n          console.error('❌ Error in message subscription:', error);\n          this.toastService.showError('Connexion temps réel interrompue');\n          // Réessayer la connexion après 5 secondes\n          setTimeout(() => {\n            this.setupSubscriptions();\n          }, 5000);\n        }\n      }));\n      // Subscription pour les indicateurs de frappe\n      this.subscriptions.add(this.MessageService.subscribeToTypingIndicator(this.conversation.id).subscribe({\n        next: typingData => {\n          // Afficher l'indicateur seulement si c'est l'autre utilisateur qui tape\n          if (typingData.userId !== this.currentUserId) {\n            this.otherUserIsTyping = typingData.isTyping;\n            this.isUserTyping = typingData.isTyping; // Pour compatibilité avec le template\n            console.log('📝 Other user typing status updated:', this.otherUserIsTyping);\n            this.cdr.detectChanges();\n          }\n        },\n        error: error => {\n          console.error('❌ Error in typing subscription:', error);\n        }\n      }));\n      // Subscription pour les mises à jour de conversation\n      this.subscriptions.add(this.MessageService.subscribeToConversationUpdates(this.conversation.id).subscribe({\n        next: conversationUpdate => {\n          // Mettre à jour la conversation si nécessaire\n          if (conversationUpdate.id === this.conversation.id) {\n            this.conversation = {\n              ...this.conversation,\n              ...conversationUpdate\n            };\n            this.cdr.detectChanges();\n          }\n        },\n        error: error => {\n          console.error('❌ Error in conversation subscription:', error);\n        }\n      }));\n    }\n    markMessageAsRead(messageId) {\n      this.MessageService.markMessageAsRead(messageId).subscribe({\n        next: () => {},\n        error: error => {\n          console.error('❌ Error marking message as read:', error);\n        }\n      });\n    }\n    // === ENVOI DE MESSAGES ===\n    sendMessage() {\n      if (!this.messageForm.valid || !this.conversation?.id) return;\n      const content = this.messageForm.get('content')?.value?.trim();\n      if (!content) return;\n      const receiverId = this.otherParticipant?.id || this.otherParticipant?._id;\n      if (!receiverId) {\n        this.toastService.showError('Destinataire introuvable');\n        return;\n      }\n      // Désactiver le bouton d'envoi\n      this.isSendingMessage = true;\n      this.updateInputState();\n      console.log('📤 Sending message:', {\n        content,\n        receiverId,\n        conversationId: this.conversation.id\n      });\n      this.MessageService.sendMessage(receiverId, content, undefined, 'TEXT', this.conversation.id).subscribe({\n        next: message => {\n          // Ajouter le message à la liste s'il n'y est pas déjà\n          const messageExists = this.messages.some(msg => msg.id === message.id);\n          if (!messageExists) {\n            this.messages.push(message);\n            console.log('📋 Message added to local list, total:', this.messages.length);\n          }\n          // Réinitialiser le formulaire\n          this.messageForm.reset();\n          this.isSendingMessage = false;\n          this.updateInputState();\n          // Forcer la détection de changements et scroll\n          this.cdr.detectChanges();\n          setTimeout(() => {\n            this.scrollToBottom();\n          }, 50);\n        },\n        error: error => {\n          console.error(\"❌ Erreur lors de l'envoi du message:\", error);\n          this.toastService.showError(\"Erreur lors de l'envoi du message\");\n          this.isSendingMessage = false;\n          this.updateInputState();\n        }\n      });\n    }\n    scrollToBottom() {\n      setTimeout(() => {\n        if (this.messagesContainer) {\n          const element = this.messagesContainer.nativeElement;\n          element.scrollTop = element.scrollHeight;\n        }\n      }, 100);\n    }\n    // === MÉTHODES UTILITAIRES OPTIMISÉES ===\n    formatLastActive(lastActive) {\n      if (!lastActive) return 'Hors ligne';\n      const diffMins = Math.floor((Date.now() - new Date(lastActive).getTime()) / 60000);\n      if (diffMins < 1) return \"À l'instant\";\n      if (diffMins < 60) return `Il y a ${diffMins} min`;\n      if (diffMins < 1440) return `Il y a ${Math.floor(diffMins / 60)}h`;\n      return `Il y a ${Math.floor(diffMins / 1440)}j`;\n    }\n    // Méthodes utilitaires pour les messages vocaux\n    getVoicePlaybackData(messageId) {\n      return this.voicePlayback[messageId] || {\n        progress: 0,\n        duration: 0,\n        currentTime: 0,\n        speed: 1\n      };\n    }\n    setVoicePlaybackData(messageId, data) {\n      this.voicePlayback[messageId] = {\n        ...this.getVoicePlaybackData(messageId),\n        ...data\n      };\n    }\n    // === MÉTHODES POUR LES MESSAGES VOCAUX (TEMPLATE) ===\n    // === MÉTHODES POUR LES APPELS (TEMPLATE) ===\n    startVideoCall() {\n      if (!this.otherParticipant?.id) {\n        this.toastService.showError(\"Impossible de démarrer l'appel\");\n        return;\n      }\n      this.initiateCall(CallType.VIDEO);\n    }\n    startVoiceCall() {\n      if (!this.otherParticipant?.id) {\n        this.toastService.showError(\"Impossible de démarrer l'appel\");\n        return;\n      }\n      // Forcer la configuration des éléments vidéo avant l'appel\n      this.setupVideoElements();\n      this.initiateCall(CallType.AUDIO);\n    }\n    // Méthodes d'appel supprimées - Gérées par ActiveCallComponent et IncomingCallComponent\n    // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n    // onCallAccepted, onCallRejected - définies plus loin\n    // === MÉTHODES POUR LES FICHIERS (TEMPLATE) ===\n    formatFileSize(bytes) {\n      if (bytes < 1024) return bytes + ' B';\n      if (bytes < 1048576) return Math.round(bytes / 1024) + ' KB';\n      return Math.round(bytes / 1048576) + ' MB';\n    }\n    downloadFile(message) {\n      const fileAttachment = message.attachments?.find(att => !att.type?.startsWith('image/'));\n      if (fileAttachment?.url) {\n        const link = document.createElement('a');\n        link.href = fileAttachment.url;\n        link.download = fileAttachment.name || 'file';\n        link.target = '_blank';\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        this.toastService.showSuccess('Téléchargement démarré');\n      }\n    }\n    // === MÉTHODES POUR L'INTERFACE UTILISATEUR (TEMPLATE) ===\n    toggleSearch() {\n      this.searchMode = !this.searchMode;\n      this.showSearch = this.searchMode;\n    }\n    toggleMainMenu() {\n      this.showMainMenu = !this.showMainMenu;\n    }\n    goBackToConversations() {\n      // Naviguer vers la liste des conversations\n      this.router.navigate(['/front/messages/conversations']).then(() => {}).catch(error => {\n        console.error('❌ Navigation error:', error);\n        // Fallback: essayer la route parent\n        this.router.navigate(['/front/messages']).catch(() => {\n          // Dernier recours: recharger la page\n          window.location.href = '/front/messages/conversations';\n        });\n      });\n    }\n    // === MÉTHODES POUR LES MENUS ET INTERACTIONS ===\n    closeAllMenus() {\n      this.showEmojiPicker = false;\n      this.showAttachmentMenu = false;\n      this.showMainMenu = false;\n      this.showMessageContextMenu = false;\n      this.showReactionPicker = false;\n    }\n    onMessageContextMenu(message, event) {\n      event.preventDefault();\n      this.selectedMessage = message;\n      this.contextMenuPosition = {\n        x: event.clientX,\n        y: event.clientY\n      };\n      this.showMessageContextMenu = true;\n    }\n    showQuickReactions(message, event) {\n      event.stopPropagation();\n      this.reactionPickerMessage = message;\n      this.contextMenuPosition = {\n        x: event.clientX,\n        y: event.clientY\n      };\n      this.showReactionPicker = true;\n    }\n    quickReact(emoji) {\n      if (this.reactionPickerMessage) {\n        this.toggleReaction(this.reactionPickerMessage.id, emoji);\n      }\n      this.showReactionPicker = false;\n    }\n    toggleReaction(messageId, emoji) {\n      if (!messageId || !emoji) {\n        console.error('❌ Missing messageId or emoji for reaction');\n        return;\n      }\n      // Appeler le service pour ajouter/supprimer la réaction\n      this.MessageService.reactToMessage(messageId, emoji).subscribe({\n        next: result => {\n          // Mettre à jour le message local avec les nouvelles réactions\n          const messageIndex = this.messages.findIndex(msg => msg.id === messageId);\n          if (messageIndex !== -1) {\n            this.messages[messageIndex] = result;\n            this.cdr.detectChanges();\n          }\n          this.toastService.showSuccess(`Réaction ${emoji} ajoutée`);\n        },\n        error: error => {\n          console.error('❌ Error toggling reaction:', error);\n          this.toastService.showError(\"Erreur lors de l'ajout de la réaction\");\n        }\n      });\n    }\n    hasUserReacted(reaction, userId) {\n      return reaction.userId === userId;\n    }\n    replyToMessage(message) {\n      this.closeAllMenus();\n    }\n    forwardMessage(message) {\n      this.closeAllMenus();\n    }\n    deleteMessage(message) {\n      if (!message.id) {\n        console.error('❌ No message ID provided for deletion');\n        this.toastService.showError('Erreur: ID du message manquant');\n        return;\n      }\n      // Vérifier si l'utilisateur peut supprimer ce message\n      const canDelete = message.sender?.id === this.currentUserId || message.senderId === this.currentUserId;\n      if (!canDelete) {\n        this.toastService.showError('Vous ne pouvez supprimer que vos propres messages');\n        this.closeAllMenus();\n        return;\n      }\n      // Demander confirmation\n      if (!confirm('Êtes-vous sûr de vouloir supprimer ce message ?')) {\n        this.closeAllMenus();\n        return;\n      }\n      // Appeler le service pour supprimer le message\n      this.MessageService.deleteMessage(message.id).subscribe({\n        next: result => {\n          // Supprimer le message de la liste locale\n          this.messages = this.messages.filter(msg => msg.id !== message.id);\n          this.toastService.showSuccess('Message supprimé');\n          this.cdr.detectChanges();\n        },\n        error: error => {\n          console.error('❌ Error deleting message:', error);\n          this.toastService.showError('Erreur lors de la suppression du message');\n        }\n      });\n      this.closeAllMenus();\n    }\n    // === MÉTHODES POUR LES ÉMOJIS ET PIÈCES JOINTES ===\n    toggleEmojiPicker() {\n      this.showEmojiPicker = !this.showEmojiPicker;\n    }\n    selectEmojiCategory(category) {\n      this.selectedEmojiCategory = category;\n    }\n    getEmojisForCategory(category) {\n      return category?.emojis || [];\n    }\n    insertEmoji(emoji) {\n      const currentContent = this.messageForm.get('content')?.value || '';\n      const newContent = currentContent + emoji.emoji;\n      this.messageForm.patchValue({\n        content: newContent\n      });\n      this.showEmojiPicker = false;\n    }\n    toggleAttachmentMenu() {\n      this.showAttachmentMenu = !this.showAttachmentMenu;\n    }\n    // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n    // triggerFileInput, getFileAcceptTypes, onFileSelected - définies plus loin\n    // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n    // Ces méthodes sont déjà définies plus loin dans le fichier\n    // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n    // handleTypingIndicator - définie plus loin\n    // === MÉTHODES UTILITAIRES POUR LE TEMPLATE ===\n    trackByMessageId(index, message) {\n      return message.id || message._id || index.toString();\n    }\n    // === MÉTHODES MANQUANTES POUR LE TEMPLATE ===\n    testAddMessage() {\n      const testMessage = {\n        id: `test-${Date.now()}`,\n        content: `Message de test ${new Date().toLocaleTimeString()}`,\n        timestamp: new Date().toISOString(),\n        sender: {\n          id: this.otherParticipant?.id || 'test-user',\n          username: this.otherParticipant?.username || 'Test User',\n          image: this.otherParticipant?.image || 'assets/images/default-avatar.png'\n        },\n        type: 'TEXT',\n        isRead: false\n      };\n      this.messages.push(testMessage);\n      this.cdr.detectChanges();\n      setTimeout(() => this.scrollToBottom(), 50);\n    }\n    isGroupConversation() {\n      return this.conversation?.isGroup || this.conversation?.participants?.length > 2 || false;\n    }\n    openCamera() {\n      this.showAttachmentMenu = false;\n      // TODO: Implémenter l'ouverture de la caméra\n    }\n\n    zoomImage(factor) {\n      const imageElement = document.querySelector('.image-viewer-zoom');\n      if (imageElement) {\n        const currentTransform = imageElement.style.transform || 'scale(1)';\n        const currentScale = parseFloat(currentTransform.match(/scale\\(([^)]+)\\)/)?.[1] || '1');\n        const newScale = Math.max(0.5, Math.min(3, currentScale * factor));\n        imageElement.style.transform = `scale(${newScale})`;\n        if (newScale > 1) {\n          imageElement.classList.add('zoomed');\n        } else {\n          imageElement.classList.remove('zoomed');\n        }\n      }\n    }\n    resetZoom() {\n      const imageElement = document.querySelector('.image-viewer-zoom');\n      if (imageElement) {\n        imageElement.style.transform = 'scale(1)';\n        imageElement.classList.remove('zoomed');\n      }\n    }\n    // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n    // formatFileSize, downloadFile, toggleReaction, hasUserReacted, showQuickReactions\n    // toggleEmojiPicker, toggleAttachmentMenu, selectEmojiCategory, getEmojisForCategory, insertEmoji\n    // Ces méthodes sont déjà définies plus loin dans le fichier\n    triggerFileInput(type) {\n      const input = this.fileInput?.nativeElement;\n      if (!input) {\n        console.error('File input element not found');\n        return;\n      }\n      // Configurer le type de fichier accepté\n      if (type === 'image') {\n        input.accept = 'image/*';\n      } else if (type === 'video') {\n        input.accept = 'video/*';\n      } else if (type === 'document') {\n        input.accept = '.pdf,.doc,.docx,.xls,.xlsx,.txt';\n      } else {\n        input.accept = '*/*';\n      }\n      // Réinitialiser la valeur pour permettre la sélection du même fichier\n      input.value = '';\n      // Déclencher la sélection de fichier\n      input.click();\n      this.showAttachmentMenu = false;\n    }\n    formatMessageTime(timestamp) {\n      if (!timestamp) return '';\n      const date = new Date(timestamp);\n      return date.toLocaleTimeString('fr-FR', {\n        hour: '2-digit',\n        minute: '2-digit'\n      });\n    }\n    formatDateSeparator(timestamp) {\n      if (!timestamp) return '';\n      const date = new Date(timestamp);\n      const today = new Date();\n      const yesterday = new Date(today);\n      yesterday.setDate(yesterday.getDate() - 1);\n      if (date.toDateString() === today.toDateString()) {\n        return \"Aujourd'hui\";\n      } else if (date.toDateString() === yesterday.toDateString()) {\n        return 'Hier';\n      } else {\n        return date.toLocaleDateString('fr-FR');\n      }\n    }\n    formatMessageContent(content) {\n      if (!content) return '';\n      // Remplacer les URLs par des liens\n      const urlRegex = /(https?:\\/\\/[^\\s]+)/g;\n      return content.replace(urlRegex, '<a href=\"$1\" target=\"_blank\" class=\"text-blue-500 underline\">$1</a>');\n    }\n    shouldShowDateSeparator(index) {\n      if (index === 0) return true;\n      const currentMessage = this.messages[index];\n      const previousMessage = this.messages[index - 1];\n      if (!currentMessage?.timestamp || !previousMessage?.timestamp) return false;\n      const currentDate = new Date(currentMessage.timestamp).toDateString();\n      const previousDate = new Date(previousMessage.timestamp).toDateString();\n      return currentDate !== previousDate;\n    }\n    shouldShowAvatar(index) {\n      const currentMessage = this.messages[index];\n      const nextMessage = this.messages[index + 1];\n      if (!nextMessage) return true;\n      return currentMessage.sender?.id !== nextMessage.sender?.id;\n    }\n    shouldShowSenderName(index) {\n      const currentMessage = this.messages[index];\n      const previousMessage = this.messages[index - 1];\n      if (!previousMessage) return true;\n      return currentMessage.sender?.id !== previousMessage.sender?.id;\n    }\n    getMessageType(message) {\n      // Vérifier d'abord le type de message explicite\n      if (message.type) {\n        if (message.type === 'IMAGE' || message.type === 'image') return 'image';\n        if (message.type === 'VIDEO' || message.type === 'video') return 'video';\n        if (message.type === 'AUDIO' || message.type === 'audio') return 'audio';\n        if (message.type === 'VOICE_MESSAGE') return 'audio';\n        if (message.type === 'FILE' || message.type === 'file') return 'file';\n      }\n      // Ensuite vérifier les attachments\n      if (message.attachments && message.attachments.length > 0) {\n        const attachment = message.attachments[0];\n        if (attachment.type?.startsWith('image/')) return 'image';\n        if (attachment.type?.startsWith('video/')) return 'video';\n        if (attachment.type?.startsWith('audio/')) return 'audio';\n        return 'file';\n      }\n      // Vérifier si c'est un message vocal basé sur les propriétés\n      if (message.voiceUrl || message.audioUrl || message.voice) return 'audio';\n      return 'text';\n    }\n    hasImage(message) {\n      // Vérifier le type de message\n      if (message.type === 'IMAGE' || message.type === 'image') {\n        return true;\n      }\n      // Vérifier les attachments\n      const hasImageAttachment = message.attachments?.some(att => {\n        return att.type?.startsWith('image/') || att.type === 'IMAGE';\n      }) || false;\n      // Vérifier les propriétés directes d'image\n      const hasImageUrl = !!(message.imageUrl || message.image);\n      return hasImageAttachment || hasImageUrl;\n    }\n    hasFile(message) {\n      // Vérifier le type de message\n      if (message.type === 'FILE' || message.type === 'file') {\n        return true;\n      }\n      // Vérifier les attachments non-image\n      const hasFileAttachment = message.attachments?.some(att => {\n        return !att.type?.startsWith('image/') && att.type !== 'IMAGE';\n      }) || false;\n      return hasFileAttachment;\n    }\n    getImageUrl(message) {\n      // Vérifier les propriétés directes d'image\n      if (message.imageUrl) {\n        return message.imageUrl;\n      }\n      if (message.image) {\n        return message.image;\n      }\n      // Vérifier les attachments\n      const imageAttachment = message.attachments?.find(att => att.type?.startsWith('image/') || att.type === 'IMAGE');\n      if (imageAttachment) {\n        return imageAttachment.url || imageAttachment.path || '';\n      }\n      return '';\n    }\n    getFileName(message) {\n      const fileAttachment = message.attachments?.find(att => !att.type?.startsWith('image/'));\n      return fileAttachment?.name || 'Fichier';\n    }\n    getFileSize(message) {\n      const fileAttachment = message.attachments?.find(att => !att.type?.startsWith('image/'));\n      if (!fileAttachment?.size) return '';\n      const bytes = fileAttachment.size;\n      if (bytes < 1024) return bytes + ' B';\n      if (bytes < 1048576) return Math.round(bytes / 1024) + ' KB';\n      return Math.round(bytes / 1048576) + ' MB';\n    }\n    getFileIcon(message) {\n      const fileAttachment = message.attachments?.find(att => !att.type?.startsWith('image/'));\n      if (!fileAttachment?.type) return 'fas fa-file';\n      if (fileAttachment.type.startsWith('audio/')) return 'fas fa-file-audio';\n      if (fileAttachment.type.startsWith('video/')) return 'fas fa-file-video';\n      if (fileAttachment.type.includes('pdf')) return 'fas fa-file-pdf';\n      if (fileAttachment.type.includes('word')) return 'fas fa-file-word';\n      if (fileAttachment.type.includes('excel')) return 'fas fa-file-excel';\n      return 'fas fa-file';\n    }\n    getUserColor(userId) {\n      // Générer une couleur basée sur l'ID utilisateur\n      const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8'];\n      const index = userId.charCodeAt(0) % colors.length;\n      return colors[index];\n    }\n    // === MÉTHODES D'INTERACTION ===\n    onMessageClick(message, event) {}\n    onInputChange(event) {\n      // Gérer les changements dans le champ de saisie\n      this.handleTypingIndicator();\n    }\n    onInputKeyDown(event) {\n      if (event.key === 'Enter' && !event.shiftKey) {\n        event.preventDefault();\n        this.sendMessage();\n      }\n    }\n    onInputFocus() {\n      // Gérer le focus sur le champ de saisie\n    }\n    onInputBlur() {\n      // Gérer la perte de focus sur le champ de saisie\n    }\n    onScroll(event) {\n      // Gérer le scroll pour charger plus de messages\n      const element = event.target;\n      if (element.scrollTop === 0 && this.hasMoreMessages && !this.isLoadingMore) {\n        this.loadMoreMessages();\n      }\n    }\n    openUserProfile(userId) {}\n    onImageLoad(event, message) {\n      console.log('🖼️ [Debug] Image loaded successfully for message:', message.id, event.target.src);\n    }\n    onImageError(event, message) {\n      console.error('🖼️ [Debug] Image failed to load for message:', message.id, {\n        src: event.target.src,\n        error: event\n      });\n      // Optionnel : afficher une image de remplacement\n      event.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjNmNGY2Ii8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzZiNzI4MCIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkltYWdlIG5vbiBkaXNwb25pYmxlPC90ZXh0Pjwvc3ZnPg==';\n    }\n    openImageViewer(message) {\n      const imageAttachment = message.attachments?.find(att => att.type?.startsWith('image/'));\n      if (imageAttachment?.url) {\n        this.selectedImage = {\n          url: imageAttachment.url,\n          name: imageAttachment.name || 'Image',\n          size: this.formatFileSize(imageAttachment.size || 0),\n          message: message\n        };\n        this.showImageViewer = true;\n      }\n    }\n    closeImageViewer() {\n      this.showImageViewer = false;\n      this.selectedImage = null;\n    }\n    downloadImage() {\n      if (this.selectedImage?.url) {\n        const link = document.createElement('a');\n        link.href = this.selectedImage.url;\n        link.download = this.selectedImage.name || 'image';\n        link.target = '_blank';\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        this.toastService.showSuccess('Téléchargement démarré');\n        console.log('🖼️ [ImageViewer] Download started:', this.selectedImage.name);\n      }\n    }\n    // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n    // zoomImage, resetZoom, formatFileSize, downloadFile, toggleReaction, hasUserReacted, toggleSearch\n    // Ces méthodes sont déjà définies plus loin dans le fichier\n    searchMessages() {\n      if (!this.searchQuery.trim()) {\n        this.searchResults = [];\n        return;\n      }\n      this.searchResults = this.messages.filter(message => message.content?.toLowerCase().includes(this.searchQuery.toLowerCase()) || message.sender?.username?.toLowerCase().includes(this.searchQuery.toLowerCase()));\n    }\n    onSearchQueryChange() {\n      this.searchMessages();\n    }\n    clearSearch() {\n      this.searchQuery = '';\n      this.searchResults = [];\n    }\n    jumpToMessage(messageId) {\n      const messageElement = document.getElementById(`message-${messageId}`);\n      if (messageElement) {\n        messageElement.scrollIntoView({\n          behavior: 'smooth',\n          block: 'center'\n        });\n        // Highlight temporairement le message\n        messageElement.classList.add('highlight');\n        setTimeout(() => {\n          messageElement.classList.remove('highlight');\n        }, 2000);\n      }\n    }\n    // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n    // toggleMainMenu, toggleTheme, onMessageContextMenu\n    // Ces méthodes sont déjà définies plus loin dans le fichier\n    closeContextMenu() {\n      this.showMessageContextMenu = false;\n      this.selectedMessage = null;\n    }\n    // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n    // replyToMessage, forwardMessage, deleteMessage, showQuickReactions, closeReactionPicker\n    // quickReact, closeAllMenus, testAddMessage, toggleAttachmentMenu, toggleEmojiPicker\n    // Ces méthodes sont déjà définies plus loin dans le fichier\n    // === MÉTHODE SUPPRIMÉE (DUPLICATION) ===\n    // triggerFileInput - définie plus loin\n    // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n    // openCamera, getEmojisForCategory, selectEmojiCategory, insertEmoji\n    // goBackToConversations, startVideoCall, startVoiceCall\n    // Ces méthodes sont déjà définies plus loin dans le fichier\n    initiateCall(callType) {\n      console.log('📋 [MessageChat] Call details:', {\n        callType,\n        otherParticipant: this.otherParticipant,\n        conversation: this.conversation?.id,\n        currentUserId: this.currentUserId\n      });\n      if (!this.otherParticipant) {\n        console.error('❌ [MessageChat] No recipient selected');\n        this.toastService.showError('Aucun destinataire sélectionné');\n        return;\n      }\n      const recipientId = this.otherParticipant.id || this.otherParticipant._id;\n      if (!recipientId) {\n        console.error('❌ [MessageChat] Recipient ID not found');\n        this.toastService.showError('ID du destinataire introuvable');\n        return;\n      }\n      console.log(`📞 [MessageChat] Initiating ${callType} call to user:`, {\n        recipientId,\n        recipientName: this.otherParticipant.username || this.otherParticipant.name,\n        conversationId: this.conversation?.id\n      });\n      this.isInCall = true;\n      this.callType = callType === CallType.VIDEO ? 'VIDEO' : 'AUDIO';\n      this.callDuration = 0;\n      // Démarrer le timer d'appel\n      this.startCallTimer();\n      // Utiliser le CallService\n      this.callService.initiateCall(recipientId, callType, this.conversation?.id).subscribe({\n        next: call => {\n          // L'appel est maintenant géré globalement par ActiveCallComponent\n          this.toastService.showSuccess(`Appel ${callType === CallType.VIDEO ? 'vidéo' : 'audio'} initié`);\n          console.log('📡 [MessageChat] Call should now be sent to recipient via WebSocket');\n        },\n        error: error => {\n          console.error('❌ [MessageChat] Error initiating call:', {\n            error: error.message || error,\n            recipientId,\n            callType,\n            conversationId: this.conversation?.id\n          });\n          this.endCall();\n          this.toastService.showError(\"Erreur lors de l'initiation de l'appel\");\n        }\n      });\n    }\n    acceptCall(incomingCall) {\n      // L'acceptation d'appel est maintenant gérée par IncomingCallComponent\n      console.log('📞 [MessageChat] Call acceptance handled by IncomingCallComponent');\n    }\n    rejectCall(incomingCall) {\n      this.callService.rejectCall(incomingCall.id, 'User rejected').subscribe({\n        next: () => {\n          this.toastService.showSuccess('Appel rejeté');\n        },\n        error: error => {\n          console.error('❌ Error rejecting call:', error);\n          this.toastService.showError(\"Erreur lors du rejet de l'appel\");\n        }\n      });\n    }\n    startCallTimer() {\n      this.callDuration = 0;\n      this.callTimer = setInterval(() => {\n        this.callDuration++;\n        this.cdr.detectChanges();\n      }, 1000);\n    }\n    resetCallState() {\n      if (this.callTimer) {\n        clearInterval(this.callTimer);\n        this.callTimer = null;\n      }\n      this.isInCall = false;\n      this.callType = null;\n      this.callDuration = 0;\n      // activeCall, isCallConnected, isMuted, isVideoEnabled gérés par ActiveCallComponent\n    }\n    // === CONTRÔLES D'APPEL ===\n    // Contrôles d'appel maintenant gérés par ActiveCallComponent\n    toggleMute() {\n      console.log('📞 [MessageChat] Mute toggle handled by ActiveCallComponent');\n    }\n    toggleVideo() {\n      console.log('📞 [MessageChat] Video toggle handled by ActiveCallComponent');\n    }\n    formatCallDuration(duration) {\n      const hours = Math.floor(duration / 3600);\n      const minutes = Math.floor(duration % 3600 / 60);\n      const seconds = duration % 60;\n      if (hours > 0) {\n        return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;\n      }\n      return `${minutes}:${seconds.toString().padStart(2, '0')}`;\n    }\n    // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n    // trackByMessageId, isGroupConversation - Ces méthodes sont déjà définies plus loin dans le fichier\n    startVoiceRecording() {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        try {\n          // Vérifier le support du navigateur\n          if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {\n            throw new Error(\"Votre navigateur ne supporte pas l'enregistrement audio\");\n          }\n          // Vérifier si MediaRecorder est supporté\n          if (!window.MediaRecorder) {\n            throw new Error(\"MediaRecorder n'est pas supporté par votre navigateur\");\n          }\n          // Demander l'accès au microphone avec des contraintes optimisées\n          const stream = yield navigator.mediaDevices.getUserMedia({\n            audio: {\n              echoCancellation: true,\n              noiseSuppression: true,\n              autoGainControl: true,\n              sampleRate: 44100,\n              channelCount: 1\n            }\n          });\n          // Vérifier les types MIME supportés\n          let mimeType = 'audio/webm;codecs=opus';\n          if (!MediaRecorder.isTypeSupported(mimeType)) {\n            mimeType = 'audio/webm';\n            if (!MediaRecorder.isTypeSupported(mimeType)) {\n              mimeType = 'audio/mp4';\n              if (!MediaRecorder.isTypeSupported(mimeType)) {\n                mimeType = ''; // Laisser le navigateur choisir\n              }\n            }\n          }\n          // Créer le MediaRecorder\n          _this.mediaRecorder = new MediaRecorder(stream, {\n            mimeType: mimeType || undefined\n          });\n          // Initialiser les variables\n          _this.audioChunks = [];\n          _this.isRecordingVoice = true;\n          _this.voiceRecordingDuration = 0;\n          _this.voiceRecordingState = 'recording';\n          // Démarrer le timer\n          _this.recordingTimer = setInterval(() => {\n            _this.voiceRecordingDuration++;\n            // Animer les waves\n            _this.animateVoiceWaves();\n            _this.cdr.detectChanges();\n          }, 1000);\n          // Gérer les événements du MediaRecorder\n          _this.mediaRecorder.ondataavailable = event => {\n            if (event.data.size > 0) {\n              _this.audioChunks.push(event.data);\n            }\n          };\n          _this.mediaRecorder.onstop = () => {\n            _this.processRecordedAudio();\n          };\n          _this.mediaRecorder.onerror = event => {\n            console.error('🎤 [Voice] MediaRecorder error:', event.error);\n            _this.toastService.showError(\"Erreur lors de l'enregistrement\");\n            _this.cancelVoiceRecording();\n          };\n          // Démarrer l'enregistrement\n          _this.mediaRecorder.start(100); // Collecter les données toutes les 100ms\n          _this.toastService.showSuccess('🎤 Enregistrement vocal démarré');\n        } catch (error) {\n          console.error('🎤 [Voice] Error starting recording:', error);\n          let errorMessage = \"Impossible de démarrer l'enregistrement vocal\";\n          if (error.name === 'NotAllowedError') {\n            errorMessage = \"Accès au microphone refusé. Veuillez autoriser l'accès dans les paramètres de votre navigateur.\";\n          } else if (error.name === 'NotFoundError') {\n            errorMessage = 'Aucun microphone détecté. Veuillez connecter un microphone.';\n          } else if (error.name === 'NotSupportedError') {\n            errorMessage = \"Votre navigateur ne supporte pas l'enregistrement audio.\";\n          } else if (error.message) {\n            errorMessage = error.message;\n          }\n          _this.toastService.showError(errorMessage);\n          _this.cancelVoiceRecording();\n        }\n      })();\n    }\n    stopVoiceRecording() {\n      if (this.mediaRecorder && this.mediaRecorder.state === 'recording') {\n        this.mediaRecorder.stop();\n        this.mediaRecorder.stream.getTracks().forEach(track => track.stop());\n      }\n      if (this.recordingTimer) {\n        clearInterval(this.recordingTimer);\n        this.recordingTimer = null;\n      }\n      this.isRecordingVoice = false;\n      this.voiceRecordingState = 'processing';\n    }\n    cancelVoiceRecording() {\n      if (this.mediaRecorder) {\n        if (this.mediaRecorder.state === 'recording') {\n          this.mediaRecorder.stop();\n        }\n        this.mediaRecorder.stream.getTracks().forEach(track => track.stop());\n        this.mediaRecorder = null;\n      }\n      if (this.recordingTimer) {\n        clearInterval(this.recordingTimer);\n        this.recordingTimer = null;\n      }\n      this.isRecordingVoice = false;\n      this.voiceRecordingDuration = 0;\n      this.voiceRecordingState = 'idle';\n      this.audioChunks = [];\n    }\n    processRecordedAudio() {\n      var _this2 = this;\n      return _asyncToGenerator(function* () {\n        try {\n          // Vérifier qu'on a des données audio\n          if (_this2.audioChunks.length === 0) {\n            console.error('🎤 [Voice] No audio chunks available');\n            _this2.toastService.showError('Aucun audio enregistré');\n            _this2.cancelVoiceRecording();\n            return;\n          }\n          console.log('🎤 [Voice] Audio chunks:', _this2.audioChunks.length, 'Duration:', _this2.voiceRecordingDuration);\n          // Vérifier la durée minimale\n          if (_this2.voiceRecordingDuration < 1) {\n            console.error('🎤 [Voice] Recording too short:', _this2.voiceRecordingDuration);\n            _this2.toastService.showError('Enregistrement trop court (minimum 1 seconde)');\n            _this2.cancelVoiceRecording();\n            return;\n          }\n          // Déterminer le type MIME du blob\n          let mimeType = 'audio/webm;codecs=opus';\n          if (_this2.mediaRecorder?.mimeType) {\n            mimeType = _this2.mediaRecorder.mimeType;\n          }\n          // Créer le blob audio\n          const audioBlob = new Blob(_this2.audioChunks, {\n            type: mimeType\n          });\n          console.log('🎤 [Voice] Audio blob created:', {\n            size: audioBlob.size,\n            type: audioBlob.type\n          });\n          // Déterminer l'extension du fichier\n          let extension = '.webm';\n          if (mimeType.includes('mp4')) {\n            extension = '.mp4';\n          } else if (mimeType.includes('wav')) {\n            extension = '.wav';\n          } else if (mimeType.includes('ogg')) {\n            extension = '.ogg';\n          }\n          // Créer le fichier\n          const audioFile = new File([audioBlob], `voice_${Date.now()}${extension}`, {\n            type: mimeType\n          });\n          console.log('🎤 [Voice] Audio file created:', {\n            name: audioFile.name,\n            size: audioFile.size,\n            type: audioFile.type\n          });\n          // Envoyer le message vocal\n          _this2.voiceRecordingState = 'processing';\n          yield _this2.sendVoiceMessage(audioFile);\n          _this2.toastService.showSuccess('🎤 Message vocal envoyé');\n        } catch (error) {\n          console.error('🎤 [Voice] Error processing audio:', error);\n          _this2.toastService.showError(\"Erreur lors de l'envoi du message vocal: \" + (error.message || 'Erreur inconnue'));\n        } finally {\n          // Nettoyer l'état\n          _this2.voiceRecordingState = 'idle';\n          _this2.voiceRecordingDuration = 0;\n          _this2.audioChunks = [];\n          _this2.isRecordingVoice = false;\n        }\n      })();\n    }\n    sendVoiceMessage(audioFile) {\n      var _this3 = this;\n      return _asyncToGenerator(function* () {\n        const receiverId = _this3.otherParticipant?.id || _this3.otherParticipant?._id;\n        if (!receiverId) {\n          throw new Error('Destinataire introuvable');\n        }\n        return new Promise((resolve, reject) => {\n          _this3.MessageService.sendMessage(receiverId, '', audioFile, 'AUDIO', _this3.conversation.id).subscribe({\n            next: message => {\n              _this3.messages.push(message);\n              _this3.scrollToBottom();\n              resolve();\n            },\n            error: error => {\n              console.error(\"Erreur lors de l'envoi du message vocal:\", error);\n              reject(error);\n            }\n          });\n        });\n      })();\n    }\n    formatRecordingDuration(duration) {\n      const minutes = Math.floor(duration / 60);\n      const seconds = duration % 60;\n      return `${minutes}:${seconds.toString().padStart(2, '0')}`;\n    }\n    // === MÉTHODES D'ENREGISTREMENT VOCAL AMÉLIORÉES ===\n    onRecordStart(event) {\n      event.preventDefault();\n      console.log('🎤 [Voice] Current state:', {\n        isRecordingVoice: this.isRecordingVoice,\n        voiceRecordingState: this.voiceRecordingState,\n        voiceRecordingDuration: this.voiceRecordingDuration,\n        mediaRecorder: !!this.mediaRecorder\n      });\n      // Vérifier si on peut enregistrer\n      if (this.voiceRecordingState === 'processing') {\n        this.toastService.showWarning('Traitement en cours...');\n        return;\n      }\n      if (this.isRecordingVoice) {\n        this.toastService.showWarning('Enregistrement déjà en cours...');\n        return;\n      }\n      // Afficher un message de début\n      this.toastService.showInfo(\"🎤 Démarrage de l'enregistrement vocal...\");\n      // Démarrer l'enregistrement\n      this.startVoiceRecording().catch(error => {\n        console.error('🎤 [Voice] Failed to start recording:', error);\n        this.toastService.showError(\"Impossible de démarrer l'enregistrement vocal: \" + (error.message || 'Erreur inconnue'));\n      });\n    }\n    onRecordEnd(event) {\n      event.preventDefault();\n      if (!this.isRecordingVoice) {\n        return;\n      }\n      // Arrêter l'enregistrement et envoyer\n      this.stopVoiceRecording();\n    }\n    onRecordCancel(event) {\n      event.preventDefault();\n      if (!this.isRecordingVoice) {\n        return;\n      }\n      // Annuler l'enregistrement\n      this.cancelVoiceRecording();\n    }\n    getRecordingFormat() {\n      if (this.mediaRecorder?.mimeType) {\n        if (this.mediaRecorder.mimeType.includes('webm')) return 'WebM';\n        if (this.mediaRecorder.mimeType.includes('mp4')) return 'MP4';\n        if (this.mediaRecorder.mimeType.includes('wav')) return 'WAV';\n        if (this.mediaRecorder.mimeType.includes('ogg')) return 'OGG';\n      }\n      return 'Auto';\n    }\n    // === ANIMATION DES WAVES VOCALES ===\n    animateVoiceWaves() {\n      // Animer les waves pendant l'enregistrement\n      this.voiceWaves = this.voiceWaves.map(() => {\n        return Math.floor(Math.random() * 20) + 4; // Hauteur entre 4 et 24px\n      });\n    }\n\n    onFileSelected(event) {\n      const files = event.target.files;\n      if (!files || files.length === 0) {\n        return;\n      }\n      for (let file of files) {\n        console.log(`📁 [Upload] Processing file: ${file.name}, size: ${file.size}, type: ${file.type}`);\n        this.uploadFile(file);\n      }\n    }\n    uploadFile(file) {\n      const receiverId = this.otherParticipant?.id || this.otherParticipant?._id;\n      if (!receiverId) {\n        console.error('📁 [Upload] No receiver ID found');\n        this.toastService.showError('Destinataire introuvable');\n        return;\n      }\n      // Vérifier la taille du fichier (max 50MB)\n      const maxSize = 50 * 1024 * 1024; // 50MB\n      if (file.size > maxSize) {\n        console.error(`📁 [Upload] File too large: ${file.size} bytes`);\n        this.toastService.showError('Fichier trop volumineux (max 50MB)');\n        return;\n      }\n      // 🖼️ Compression d'image si nécessaire\n      if (file.type.startsWith('image/') && file.size > 1024 * 1024) {\n        // > 1MB\n        console.log('🖼️ [Compression] Compressing image:', file.name, 'Original size:', file.size);\n        this.compressImage(file).then(compressedFile => {\n          console.log('🖼️ [Compression] ✅ Image compressed successfully. New size:', compressedFile.size);\n          this.sendFileToServer(compressedFile, receiverId);\n        }).catch(error => {\n          console.error('🖼️ [Compression] ❌ Error compressing image:', error);\n          // Envoyer le fichier original en cas d'erreur\n          this.sendFileToServer(file, receiverId);\n        });\n        return;\n      }\n      // Envoyer le fichier sans compression\n      this.sendFileToServer(file, receiverId);\n    }\n    sendFileToServer(file, receiverId) {\n      const messageType = this.getFileMessageType(file);\n      this.isSendingMessage = true;\n      this.isUploading = true;\n      this.uploadProgress = 0;\n      // Simuler la progression d'upload\n      const progressInterval = setInterval(() => {\n        this.uploadProgress += Math.random() * 15;\n        if (this.uploadProgress >= 90) {\n          clearInterval(progressInterval);\n        }\n        this.cdr.detectChanges();\n      }, 300);\n      this.MessageService.sendMessage(receiverId, '', file, messageType, this.conversation.id).subscribe({\n        next: message => {\n          console.log('📁 [Debug] Sent message structure:', {\n            id: message.id,\n            type: message.type,\n            attachments: message.attachments,\n            hasImage: this.hasImage(message),\n            hasFile: this.hasFile(message),\n            imageUrl: this.getImageUrl(message)\n          });\n          clearInterval(progressInterval);\n          this.uploadProgress = 100;\n          setTimeout(() => {\n            this.messages.push(message);\n            this.scrollToBottom();\n            this.toastService.showSuccess('Fichier envoyé avec succès');\n            this.resetUploadState();\n          }, 500);\n        },\n        error: error => {\n          console.error('📁 [Upload] ❌ Error sending file:', error);\n          clearInterval(progressInterval);\n          this.toastService.showError(\"Erreur lors de l'envoi du fichier\");\n          this.resetUploadState();\n        }\n      });\n    }\n    getFileMessageType(file) {\n      if (file.type.startsWith('image/')) return 'IMAGE';\n      if (file.type.startsWith('video/')) return 'VIDEO';\n      if (file.type.startsWith('audio/')) return 'AUDIO';\n      return 'FILE';\n    }\n    getFileAcceptTypes() {\n      return '*/*';\n    }\n    resetUploadState() {\n      this.isSendingMessage = false;\n      this.isUploading = false;\n      this.uploadProgress = 0;\n    }\n    // === DRAG & DROP ===\n    onDragOver(event) {\n      event.preventDefault();\n      event.stopPropagation();\n      this.isDragOver = true;\n    }\n    onDragLeave(event) {\n      event.preventDefault();\n      event.stopPropagation();\n      // Vérifier si on quitte vraiment la zone (pas un enfant)\n      const rect = event.currentTarget.getBoundingClientRect();\n      const x = event.clientX;\n      const y = event.clientY;\n      if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {\n        this.isDragOver = false;\n      }\n    }\n    onDrop(event) {\n      event.preventDefault();\n      event.stopPropagation();\n      this.isDragOver = false;\n      const files = event.dataTransfer?.files;\n      if (files && files.length > 0) {\n        // Traiter chaque fichier\n        Array.from(files).forEach(file => {\n          console.log('📁 [Drag&Drop] Processing file:', file.name, file.type, file.size);\n          this.uploadFile(file);\n        });\n        this.toastService.showSuccess(`${files.length} fichier(s) en cours d'envoi`);\n      }\n    }\n    // === COMPRESSION D'IMAGES ===\n    compressImage(file, quality = 0.8) {\n      return new Promise((resolve, reject) => {\n        const canvas = document.createElement('canvas');\n        const ctx = canvas.getContext('2d');\n        const img = new Image();\n        img.onload = () => {\n          // Calculer les nouvelles dimensions (max 1920x1080)\n          const maxWidth = 1920;\n          const maxHeight = 1080;\n          let {\n            width,\n            height\n          } = img;\n          if (width > maxWidth || height > maxHeight) {\n            const ratio = Math.min(maxWidth / width, maxHeight / height);\n            width *= ratio;\n            height *= ratio;\n          }\n          canvas.width = width;\n          canvas.height = height;\n          // Dessiner l'image redimensionnée\n          ctx?.drawImage(img, 0, 0, width, height);\n          // Convertir en blob avec compression\n          canvas.toBlob(blob => {\n            if (blob) {\n              const compressedFile = new File([blob], file.name, {\n                type: file.type,\n                lastModified: Date.now()\n              });\n              resolve(compressedFile);\n            } else {\n              reject(new Error('Failed to compress image'));\n            }\n          }, file.type, quality);\n        };\n        img.onerror = () => reject(new Error('Failed to load image'));\n        img.src = URL.createObjectURL(file);\n      });\n    }\n    // === MÉTHODES DE FORMATAGE SUPPLÉMENTAIRES ===\n    handleTypingIndicator() {\n      if (!this.isTyping) {\n        this.isTyping = true;\n        // Envoyer l'indicateur de frappe à l'autre utilisateur\n        this.sendTypingIndicator(true);\n      }\n      // Reset le timer\n      if (this.typingTimeout) {\n        clearTimeout(this.typingTimeout);\n      }\n      this.typingTimeout = setTimeout(() => {\n        this.isTyping = false;\n        // Arrêter l'indicateur de frappe\n        this.sendTypingIndicator(false);\n      }, 2000);\n    }\n    sendTypingIndicator(isTyping) {\n      // Envoyer l'indicateur de frappe via WebSocket/GraphQL\n      const receiverId = this.otherParticipant?.id || this.otherParticipant?._id;\n      if (receiverId && this.conversation?.id) {\n        console.log(`📝 Sending typing indicator: ${isTyping} to user ${receiverId}`);\n        // TODO: Implémenter l'envoi via WebSocket/GraphQL subscription\n        // this.MessageService.sendTypingIndicator(this.conversation.id, receiverId, isTyping);\n      }\n    }\n    // === MÉTHODES POUR L'INTERFACE D'APPEL ===\n    onCallAccepted(call) {\n      // Gestion d'appel accepté maintenant dans ActiveCallComponent\n      this.isInCall = true;\n      this.startCallTimer();\n      this.toastService.showSuccess('Appel accepté');\n    }\n    onCallRejected() {\n      this.endCall();\n      this.toastService.showInfo('Appel rejeté');\n    }\n    // === MÉTHODES POUR LA LECTURE DES MESSAGES VOCAUX ===\n    playVoiceMessage(message) {\n      this.toggleVoicePlayback(message);\n    }\n    isVoicePlaying(messageId) {\n      return this.playingMessageId === messageId;\n    }\n    toggleVoicePlayback(message) {\n      const messageId = message.id;\n      const audioUrl = this.getVoiceUrl(message);\n      if (!audioUrl) {\n        console.error('🎵 [Voice] No audio URL found for message:', messageId);\n        this.toastService.showError('Fichier audio introuvable');\n        return;\n      }\n      // Si c'est déjà en cours de lecture, arrêter\n      if (this.isVoicePlaying(messageId)) {\n        this.stopVoicePlayback();\n        return;\n      }\n      // Arrêter toute autre lecture en cours\n      this.stopVoicePlayback();\n      // Démarrer la nouvelle lecture\n      this.startVoicePlayback(message, audioUrl);\n    }\n    startVoicePlayback(message, audioUrl) {\n      const messageId = message.id;\n      try {\n        console.log('🎵 [Voice] Starting playback for:', messageId, 'URL:', audioUrl);\n        this.currentAudio = new Audio(audioUrl);\n        this.playingMessageId = messageId;\n        // Initialiser les valeurs par défaut avec la nouvelle structure\n        const currentData = this.getVoicePlaybackData(messageId);\n        this.setVoicePlaybackData(messageId, {\n          progress: 0,\n          currentTime: 0,\n          speed: currentData.speed || 1,\n          duration: currentData.duration || 0\n        });\n        // Configurer la vitesse de lecture\n        this.currentAudio.playbackRate = currentData.speed || 1;\n        // Événements audio\n        this.currentAudio.addEventListener('loadedmetadata', () => {\n          if (this.currentAudio) {\n            this.setVoicePlaybackData(messageId, {\n              duration: this.currentAudio.duration\n            });\n            console.log('🎵 [Voice] Audio loaded, duration:', this.currentAudio.duration);\n          }\n        });\n        this.currentAudio.addEventListener('timeupdate', () => {\n          if (this.currentAudio && this.playingMessageId === messageId) {\n            const currentTime = this.currentAudio.currentTime;\n            const progress = currentTime / this.currentAudio.duration * 100;\n            this.setVoicePlaybackData(messageId, {\n              currentTime,\n              progress\n            });\n            this.cdr.detectChanges();\n          }\n        });\n        this.currentAudio.addEventListener('ended', () => {\n          this.stopVoicePlayback();\n        });\n        this.currentAudio.addEventListener('error', error => {\n          console.error('🎵 [Voice] Audio error:', error);\n          this.toastService.showError('Erreur lors de la lecture audio');\n          this.stopVoicePlayback();\n        });\n        // Démarrer la lecture\n        this.currentAudio.play().then(() => {\n          this.toastService.showSuccess('🎵 Lecture du message vocal');\n        }).catch(error => {\n          console.error('🎵 [Voice] Error starting playback:', error);\n          this.toastService.showError('Impossible de lire le message vocal');\n          this.stopVoicePlayback();\n        });\n      } catch (error) {\n        console.error('🎵 [Voice] Error creating audio:', error);\n        this.toastService.showError('Erreur lors de la lecture audio');\n        this.stopVoicePlayback();\n      }\n    }\n    stopVoicePlayback() {\n      if (this.currentAudio) {\n        this.currentAudio.pause();\n        this.currentAudio.currentTime = 0;\n        this.currentAudio = null;\n      }\n      this.playingMessageId = null;\n      this.cdr.detectChanges();\n    }\n    getVoiceUrl(message) {\n      // Vérifier les propriétés directes d'audio\n      if (message.voiceUrl) return message.voiceUrl;\n      if (message.audioUrl) return message.audioUrl;\n      if (message.voice) return message.voice;\n      // Vérifier les attachments audio\n      const audioAttachment = message.attachments?.find(att => att.type?.startsWith('audio/') || att.type === 'AUDIO');\n      if (audioAttachment) {\n        return audioAttachment.url || audioAttachment.path || '';\n      }\n      return '';\n    }\n    getVoiceWaves(message) {\n      // Générer des waves basées sur l'ID du message pour la cohérence\n      const messageId = message.id || '';\n      const seed = messageId.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);\n      const waves = [];\n      for (let i = 0; i < 16; i++) {\n        const height = 4 + (seed + i * 7) % 20;\n        waves.push(height);\n      }\n      return waves;\n    }\n    getVoiceProgress(message) {\n      const data = this.getVoicePlaybackData(message.id);\n      const totalWaves = 16;\n      return Math.floor(data.progress / 100 * totalWaves);\n    }\n    getVoiceCurrentTime(message) {\n      const data = this.getVoicePlaybackData(message.id);\n      return this.formatAudioTime(data.currentTime);\n    }\n    getVoiceDuration(message) {\n      const data = this.getVoicePlaybackData(message.id);\n      const duration = data.duration || message.metadata?.duration || 0;\n      if (typeof duration === 'string') {\n        return duration; // Déjà formaté\n      }\n\n      return this.formatAudioTime(duration);\n    }\n    formatAudioTime(seconds) {\n      const minutes = Math.floor(seconds / 60);\n      const remainingSeconds = Math.floor(seconds % 60);\n      return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n    }\n    seekVoiceMessage(message, waveIndex) {\n      const messageId = message.id;\n      if (!this.currentAudio || this.playingMessageId !== messageId) {\n        return;\n      }\n      const totalWaves = 16;\n      const seekPercentage = waveIndex / totalWaves * 100;\n      const seekTime = seekPercentage / 100 * this.currentAudio.duration;\n      this.currentAudio.currentTime = seekTime;\n    }\n    toggleVoiceSpeed(message) {\n      const messageId = message.id;\n      const data = this.getVoicePlaybackData(messageId);\n      // Cycle entre 1x, 1.5x, 2x\n      const newSpeed = data.speed === 1 ? 1.5 : data.speed === 1.5 ? 2 : 1;\n      this.setVoicePlaybackData(messageId, {\n        speed: newSpeed\n      });\n      if (this.currentAudio && this.playingMessageId === messageId) {\n        this.currentAudio.playbackRate = newSpeed;\n      }\n      this.toastService.showSuccess(`Vitesse: ${newSpeed}x`);\n    }\n    // === MÉTHODES MANQUANTES POUR LE TEMPLATE ===\n    changeVoiceSpeed(message) {\n      this.toggleVoiceSpeed(message);\n    }\n    getVoiceSpeed(message) {\n      const data = this.getVoicePlaybackData(message.id);\n      return data.speed || 1;\n    }\n    ngOnDestroy() {\n      this.subscriptions.unsubscribe();\n      // Nettoyer les timers\n      if (this.callTimer) {\n        clearInterval(this.callTimer);\n      }\n      if (this.recordingTimer) {\n        clearInterval(this.recordingTimer);\n      }\n      if (this.typingTimeout) {\n        clearTimeout(this.typingTimeout);\n      }\n      // Nettoyer les ressources audio\n      if (this.mediaRecorder) {\n        if (this.mediaRecorder.state === 'recording') {\n          this.mediaRecorder.stop();\n        }\n        this.mediaRecorder.stream?.getTracks().forEach(track => track.stop());\n      }\n      // Nettoyer la lecture audio\n      this.stopVoicePlayback();\n    }\n    static {\n      this.ɵfac = function MessageChatComponent_Factory(t) {\n        return new (t || MessageChatComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i4.CallService), i0.ɵɵdirectiveInject(i5.ToastService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: MessageChatComponent,\n        selectors: [[\"app-message-chat\"]],\n        viewQuery: function MessageChatComponent_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(_c0, 5);\n            i0.ɵɵviewQuery(_c1, 5);\n            i0.ɵɵviewQuery(_c2, 5);\n            i0.ɵɵviewQuery(_c3, 5);\n            i0.ɵɵviewQuery(_c4, 5);\n            i0.ɵɵviewQuery(_c5, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.messagesContainer = _t.first);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.fileInput = _t.first);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.localVideo = _t.first);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.remoteVideo = _t.first);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.localVideoHidden = _t.first);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.remoteVideoHidden = _t.first);\n          }\n        },\n        decls: 57,\n        vars: 59,\n        consts: [[\"autoplay\", \"\", \"muted\", \"\", \"playsinline\", \"\", 2, \"position\", \"absolute\", \"top\", \"-9999px\", \"left\", \"-9999px\", \"width\", \"1px\", \"height\", \"1px\"], [\"localVideo\", \"\"], [\"autoplay\", \"\", \"playsinline\", \"\", 2, \"position\", \"absolute\", \"top\", \"-9999px\", \"left\", \"-9999px\", \"width\", \"1px\", \"height\", \"1px\"], [\"remoteVideo\", \"\"], [2, \"display\", \"flex\", \"flex-direction\", \"column\", \"height\", \"100vh\", \"background\", \"linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)\", \"color\", \"#1f2937\", \"font-family\", \"'Inter', -apple-system, BlinkMacSystemFont, sans-serif\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"padding\", \"12px 16px\", \"background\", \"#ffffff\", \"border-bottom\", \"1px solid #e5e7eb\", \"box-shadow\", \"0 1px 3px rgba(0, 0, 0, 0.1)\", \"position\", \"relative\", \"z-index\", \"10\"], [\"onmouseover\", \"this.style.background='#f3f4f6'; this.style.transform='scale(1.05)'\", \"onmouseout\", \"this.style.background='transparent'; this.style.transform='scale(1)'\", \"title\", \"Retour aux conversations\", 2, \"padding\", \"10px\", \"margin-right\", \"12px\", \"border-radius\", \"50%\", \"border\", \"none\", \"background\", \"transparent\", \"cursor\", \"pointer\", \"transition\", \"all 0.2s ease\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"center\", \"min-width\", \"40px\", \"min-height\", \"40px\", 3, \"click\"], [1, \"fas\", \"fa-arrow-left\", 2, \"color\", \"#374151\", \"font-size\", \"18px\", \"font-weight\", \"bold\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"flex\", \"1\", \"min-width\", \"0\"], [2, \"position\", \"relative\", \"margin-right\", \"12px\"], [\"onmouseover\", \"this.style.transform='scale(1.05)'\", \"onmouseout\", \"this.style.transform='scale(1)'\", \"title\", \"Voir le profil\", 2, \"width\", \"40px\", \"height\", \"40px\", \"border-radius\", \"50%\", \"object-fit\", \"cover\", \"border\", \"2px solid transparent\", \"cursor\", \"pointer\", \"transition\", \"transform 0.2s ease\", 3, \"src\", \"alt\", \"click\"], [\"style\", \"\\n            position: absolute;\\n            bottom: 0;\\n            right: 0;\\n            width: 12px;\\n            height: 12px;\\n            background: #10b981;\\n            border: 2px solid transparent;\\n            border-radius: 50%;\\n            animation: pulse 2s infinite;\\n          \", 4, \"ngIf\"], [2, \"flex\", \"1\", \"min-width\", \"0\"], [2, \"font-weight\", \"600\", \"color\", \"#111827\", \"margin\", \"0\", \"font-size\", \"16px\", \"white-space\", \"nowrap\", \"overflow\", \"hidden\", \"text-overflow\", \"ellipsis\"], [2, \"font-size\", \"14px\", \"color\", \"#6b7280\", \"margin-top\", \"2px\"], [\"style\", \"display: flex; align-items: center; gap: 4px; color: #10b981\", 4, \"ngIf\"], [4, \"ngIf\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"8px\"], [\"title\", \"Appel vid\\u00E9o\", \"onmouseover\", \"this.style.transform='scale(1.1)'; this.style.boxShadow='0 6px 20px rgba(59, 130, 246, 0.4)'\", \"onmouseout\", \"this.style.transform='scale(1)'; this.style.boxShadow='0 4px 12px rgba(59, 130, 246, 0.3)'\", 2, \"padding\", \"10px\", \"border-radius\", \"50%\", \"border\", \"none\", \"background\", \"linear-gradient(135deg, #3b82f6, #1d4ed8)\", \"color\", \"white\", \"cursor\", \"pointer\", \"transition\", \"all 0.3s\", \"box-shadow\", \"0 4px 12px rgba(59, 130, 246, 0.3)\", \"width\", \"40px\", \"height\", \"40px\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"center\", 3, \"click\"], [1, \"fas\", \"fa-video\", 2, \"font-size\", \"14px\"], [\"title\", \"Appel vocal\", \"onmouseover\", \"this.style.transform='scale(1.1)'; this.style.boxShadow='0 6px 20px rgba(16, 185, 129, 0.4)'\", \"onmouseout\", \"this.style.transform='scale(1)'; this.style.boxShadow='0 4px 12px rgba(16, 185, 129, 0.3)'\", 2, \"padding\", \"10px\", \"border-radius\", \"50%\", \"border\", \"none\", \"background\", \"linear-gradient(135deg, #10b981, #047857)\", \"color\", \"white\", \"cursor\", \"pointer\", \"transition\", \"all 0.3s\", \"box-shadow\", \"0 4px 12px rgba(16, 185, 129, 0.3)\", \"width\", \"40px\", \"height\", \"40px\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"center\", 3, \"click\"], [1, \"fas\", \"fa-phone\", 2, \"font-size\", \"14px\"], [\"title\", \"Rechercher\", 2, \"padding\", \"8px\", \"border-radius\", \"50%\", \"border\", \"none\", \"background\", \"transparent\", \"color\", \"#6b7280\", \"cursor\", \"pointer\", \"transition\", \"all 0.2s\", 3, \"click\"], [1, \"fas\", \"fa-search\"], [\"title\", \"Recharger la conversation\", \"onmouseover\", \"this.style.background='#f3f4f6'; this.style.color='#374151'\", \"onmouseout\", \"this.style.background='transparent'; this.style.color='#6b7280'\", 2, \"padding\", \"8px\", \"border-radius\", \"50%\", \"border\", \"none\", \"background\", \"transparent\", \"color\", \"#6b7280\", \"cursor\", \"pointer\", \"transition\", \"all 0.2s\", 3, \"disabled\", \"click\"], [1, \"fas\", \"fa-sync-alt\"], [\"title\", \"Menu\", 2, \"padding\", \"8px\", \"border-radius\", \"50%\", \"border\", \"none\", \"background\", \"transparent\", \"color\", \"#6b7280\", \"cursor\", \"pointer\", \"transition\", \"all 0.2s\", \"position\", \"relative\", 3, \"click\"], [1, \"fas\", \"fa-ellipsis-v\"], [\"style\", \"\\n        position: absolute;\\n        top: 64px;\\n        right: 16px;\\n        background: #ffffff;\\n        border-radius: 16px;\\n        box-shadow: 0 20px 25px rgba(0, 0, 0, 0.1);\\n        border: 1px solid #e5e7eb;\\n        z-index: 50;\\n        min-width: 192px;\\n      \", 4, \"ngIf\"], [2, \"flex\", \"1\", \"overflow-y\", \"auto\", \"padding\", \"16px\", \"position\", \"relative\", 3, \"scroll\", \"dragover\", \"dragleave\", \"drop\"], [\"messagesContainer\", \"\"], [\"style\", \"\\n        position: absolute;\\n        top: 0;\\n        left: 0;\\n        right: 0;\\n        bottom: 0;\\n        background: rgba(34, 197, 94, 0.2);\\n        border: 2px dashed transparent;\\n        border-radius: 8px;\\n        display: flex;\\n        align-items: center;\\n        justify-content: center;\\n        z-index: 50;\\n        backdrop-filter: blur(2px);\\n        animation: pulse 2s infinite;\\n      \", 4, \"ngIf\"], [\"style\", \"\\n        display: flex;\\n        flex-direction: column;\\n        align-items: center;\\n        justify-content: center;\\n        padding: 32px 0;\\n      \", 4, \"ngIf\"], [\"style\", \"\\n        display: flex;\\n        flex-direction: column;\\n        align-items: center;\\n        justify-content: center;\\n        padding: 64px 0;\\n      \", 4, \"ngIf\"], [\"style\", \"display: flex; flex-direction: column; gap: 8px\", 4, \"ngIf\"], [2, \"background\", \"#ffffff\", \"border-top\", \"1px solid #e5e7eb\", \"padding\", \"16px\"], [2, \"display\", \"flex\", \"align-items\", \"end\", \"gap\", \"12px\", 3, \"formGroup\", \"ngSubmit\"], [2, \"display\", \"flex\", \"gap\", \"8px\"], [\"type\", \"button\", \"title\", \"\\u00C9mojis\", \"onmouseover\", \"this.style.background=this.style.background === 'transparent' ? '#f3f4f6' : this.style.background\", \"onmouseout\", \"this.style.background=this.style.background === '#f3f4f6' ? 'transparent' : this.style.background\", 2, \"padding\", \"8px\", \"border-radius\", \"50%\", \"border\", \"none\", \"background\", \"transparent\", \"color\", \"#6b7280\", \"cursor\", \"pointer\", \"transition\", \"all 0.2s\", 3, \"click\"], [1, \"fas\", \"fa-smile\"], [\"type\", \"button\", \"title\", \"Pi\\u00E8ces jointes\", \"onmouseover\", \"this.style.background=this.style.background === 'transparent' ? '#f3f4f6' : this.style.background\", \"onmouseout\", \"this.style.background=this.style.background === '#f3f4f6' ? 'transparent' : this.style.background\", 2, \"padding\", \"8px\", \"border-radius\", \"50%\", \"border\", \"none\", \"background\", \"transparent\", \"color\", \"#6b7280\", \"cursor\", \"pointer\", \"transition\", \"all 0.2s\", 3, \"click\"], [1, \"fas\", \"fa-paperclip\"], [\"type\", \"button\", \"title\", \"Maintenir pour enregistrer un message vocal\", \"onmouseover\", \"if(!this.style.background || this.style.background === 'transparent') this.style.background='#f3f4f6'\", \"onmouseout\", \"if(this.style.background === '#f3f4f6') this.style.background='transparent'\", 2, \"padding\", \"8px\", \"border-radius\", \"50%\", \"border\", \"none\", \"background\", \"transparent\", \"color\", \"#6b7280\", \"cursor\", \"pointer\", \"transition\", \"all 0.2s\", \"position\", \"relative\", 3, \"mousedown\", \"mouseup\", \"mouseleave\", \"touchstart\", \"touchend\", \"touchcancel\"], [\"style\", \"\\n              position: absolute;\\n              top: -2px;\\n              right: -2px;\\n              width: 8px;\\n              height: 8px;\\n              background: #ef4444;\\n              border-radius: 50%;\\n              animation: ping 1s infinite;\\n            \", 4, \"ngIf\"], [2, \"flex\", \"1\", \"position\", \"relative\"], [\"formControlName\", \"content\", \"placeholder\", \"Tapez votre message...\", 2, \"width\", \"100%\", \"min-height\", \"44px\", \"max-height\", \"120px\", \"padding\", \"12px 16px\", \"border\", \"1px solid #e5e7eb\", \"border-radius\", \"22px\", \"resize\", \"none\", \"outline\", \"none\", \"font-family\", \"inherit\", \"font-size\", \"14px\", \"line-height\", \"1.4\", \"background\", \"#ffffff\", \"color\", \"#111827\", \"transition\", \"all 0.2s\", 3, \"disabled\", \"keydown\", \"input\", \"focus\"], [\"type\", \"submit\", \"title\", \"Envoyer\", \"onmouseover\", \"if(!this.disabled) this.style.background='#2563eb'\", \"onmouseout\", \"if(!this.disabled) this.style.background='#3b82f6'\", 2, \"padding\", \"12px\", \"border-radius\", \"50%\", \"border\", \"none\", \"background\", \"#3b82f6\", \"color\", \"#ffffff\", \"cursor\", \"pointer\", \"transition\", \"all 0.2s\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"center\", \"min-width\", \"44px\", \"min-height\", \"44px\", 3, \"disabled\"], [\"class\", \"fas fa-paper-plane\", 4, \"ngIf\"], [\"style\", \"\\n            width: 16px;\\n            height: 16px;\\n            border: 2px solid #ffffff;\\n            border-top-color: transparent;\\n            border-radius: 50%;\\n            animation: spin 1s linear infinite;\\n          \", 4, \"ngIf\"], [\"style\", \"\\n        position: absolute;\\n        bottom: 80px;\\n        left: 16px;\\n        background: #ffffff;\\n        border-radius: 16px;\\n        box-shadow: 0 20px 25px rgba(0, 0, 0, 0.1);\\n        border: 1px solid #e5e7eb;\\n        z-index: 50;\\n        width: 320px;\\n        max-height: 300px;\\n        overflow-y: auto;\\n      \", 4, \"ngIf\"], [\"style\", \"\\n        position: absolute;\\n        bottom: 80px;\\n        left: 60px;\\n        background: #ffffff;\\n        border-radius: 16px;\\n        box-shadow: 0 20px 25px rgba(0, 0, 0, 0.1);\\n        border: 1px solid #e5e7eb;\\n        z-index: 50;\\n        min-width: 200px;\\n      \", 4, \"ngIf\"], [\"type\", \"file\", \"multiple\", \"\", 2, \"display\", \"none\", 3, \"accept\", \"change\"], [\"fileInput\", \"\"], [\"style\", \"\\n      position: fixed;\\n      top: 0;\\n      left: 0;\\n      right: 0;\\n      bottom: 0;\\n      background: rgba(0, 0, 0, 0.25);\\n      z-index: 40;\\n    \", 3, \"click\", 4, \"ngIf\"], [\"style\", \"\\n      position: fixed;\\n      bottom: 100px;\\n      left: 50%;\\n      transform: translateX(-50%);\\n      background: linear-gradient(135deg, #f59e0b, #d97706);\\n      color: white;\\n      padding: 20px 24px;\\n      border-radius: 20px;\\n      box-shadow: 0 20px 25px rgba(0, 0, 0, 0.2);\\n      z-index: 60;\\n      display: flex;\\n      align-items: center;\\n      gap: 16px;\\n      min-width: 280px;\\n      animation: slideInUp 0.3s ease-out;\\n    \", 4, \"ngIf\"], [2, \"position\", \"absolute\", \"bottom\", \"0\", \"right\", \"0\", \"width\", \"12px\", \"height\", \"12px\", \"background\", \"#10b981\", \"border\", \"2px solid transparent\", \"border-radius\", \"50%\", \"animation\", \"pulse 2s infinite\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"4px\", \"color\", \"#10b981\"], [2, \"display\", \"flex\", \"gap\", \"2px\"], [2, \"width\", \"4px\", \"height\", \"4px\", \"background\", \"#10b981\", \"border-radius\", \"50%\", \"animation\", \"bounce 1s infinite\"], [2, \"width\", \"4px\", \"height\", \"4px\", \"background\", \"#10b981\", \"border-radius\", \"50%\", \"animation\", \"bounce 1s infinite 0.1s\"], [2, \"width\", \"4px\", \"height\", \"4px\", \"background\", \"#10b981\", \"border-radius\", \"50%\", \"animation\", \"bounce 1s infinite 0.2s\"], [2, \"position\", \"absolute\", \"top\", \"64px\", \"right\", \"16px\", \"background\", \"#ffffff\", \"border-radius\", \"16px\", \"box-shadow\", \"0 20px 25px rgba(0, 0, 0, 0.1)\", \"border\", \"1px solid #e5e7eb\", \"z-index\", \"50\", \"min-width\", \"192px\"], [2, \"padding\", \"8px\"], [\"onmouseover\", \"this.style.background='#f3f4f6'\", \"onmouseout\", \"this.style.background='transparent'\", 2, \"width\", \"100%\", \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"12px\", \"padding\", \"8px 12px\", \"border-radius\", \"8px\", \"border\", \"none\", \"background\", \"transparent\", \"cursor\", \"pointer\", \"transition\", \"all 0.2s\", \"text-align\", \"left\", 3, \"click\"], [1, \"fas\", \"fa-search\", 2, \"color\", \"#3b82f6\"], [2, \"color\", \"#374151\"], [\"onmouseover\", \"this.style.background='#f3f4f6'\", \"onmouseout\", \"this.style.background='transparent'\", 2, \"width\", \"100%\", \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"12px\", \"padding\", \"8px 12px\", \"border-radius\", \"8px\", \"border\", \"none\", \"background\", \"transparent\", \"cursor\", \"pointer\", \"transition\", \"all 0.2s\", \"text-align\", \"left\"], [1, \"fas\", \"fa-user\", 2, \"color\", \"#10b981\"], [2, \"margin\", \"8px 0\", \"border-color\", \"#e5e7eb\"], [1, \"fas\", \"fa-cog\", 2, \"color\", \"#6b7280\"], [2, \"position\", \"absolute\", \"top\", \"0\", \"left\", \"0\", \"right\", \"0\", \"bottom\", \"0\", \"background\", \"rgba(34, 197, 94, 0.2)\", \"border\", \"2px dashed transparent\", \"border-radius\", \"8px\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"center\", \"z-index\", \"50\", \"backdrop-filter\", \"blur(2px)\", \"animation\", \"pulse 2s infinite\"], [2, \"text-align\", \"center\", \"background\", \"#ffffff\", \"padding\", \"24px\", \"border-radius\", \"12px\", \"box-shadow\", \"0 10px 15px rgba(0, 0, 0, 0.1)\", \"border\", \"1px solid transparent\"], [1, \"fas\", \"fa-cloud-upload-alt\", 2, \"font-size\", \"48px\", \"color\", \"#10b981\", \"margin-bottom\", \"12px\", \"animation\", \"bounce 1s infinite\"], [2, \"font-size\", \"20px\", \"font-weight\", \"bold\", \"color\", \"#047857\", \"margin-bottom\", \"8px\"], [2, \"font-size\", \"14px\", \"color\", \"#10b981\"], [2, \"display\", \"flex\", \"flex-direction\", \"column\", \"align-items\", \"center\", \"justify-content\", \"center\", \"padding\", \"32px 0\"], [2, \"width\", \"32px\", \"height\", \"32px\", \"border\", \"2px solid #e5e7eb\", \"border-bottom-color\", \"#10b981\", \"border-radius\", \"50%\", \"animation\", \"spin 1s linear infinite\", \"margin-bottom\", \"16px\"], [2, \"color\", \"#6b7280\"], [2, \"display\", \"flex\", \"flex-direction\", \"column\", \"align-items\", \"center\", \"justify-content\", \"center\", \"padding\", \"64px 0\"], [2, \"font-size\", \"64px\", \"color\", \"#d1d5db\", \"margin-bottom\", \"16px\"], [1, \"fas\", \"fa-comments\"], [2, \"font-size\", \"20px\", \"font-weight\", \"600\", \"color\", \"#374151\", \"margin-bottom\", \"8px\"], [2, \"color\", \"#6b7280\", \"text-align\", \"center\"], [2, \"display\", \"flex\", \"flex-direction\", \"column\", \"gap\", \"8px\"], [4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"style\", \"display: flex; align-items: start; gap: 8px\", 4, \"ngIf\"], [\"style\", \"display: flex; justify-content: center; margin: 16px 0\", 4, \"ngIf\"], [2, \"display\", \"flex\", 3, \"id\", \"click\", \"contextmenu\"], [\"style\", \"margin-right: 8px; flex-shrink: 0\", 4, \"ngIf\"], [2, \"max-width\", \"320px\", \"padding\", \"12px 16px\", \"border-radius\", \"18px\", \"box-shadow\", \"0 1px 3px rgba(0, 0, 0, 0.1)\", \"position\", \"relative\", \"word-wrap\", \"break-word\", \"overflow-wrap\", \"break-word\", \"border\", \"none\"], [\"style\", \"\\n                font-size: 12px;\\n                font-weight: 600;\\n                margin-bottom: 4px;\\n                opacity: 0.75;\\n              \", 3, \"color\", 4, \"ngIf\"], [\"style\", \"word-wrap: break-word; overflow-wrap: break-word\", 4, \"ngIf\"], [\"style\", \"margin: 8px 0\", 4, \"ngIf\"], [\"style\", \"\\n                display: flex;\\n                align-items: center;\\n                gap: 12px;\\n                padding: 12px;\\n                background: rgba(255, 255, 255, 0.1);\\n                border-radius: 12px;\\n                margin: 8px 0;\\n                min-width: 200px;\\n                max-width: 280px;\\n              \", 4, \"ngIf\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"flex-end\", \"gap\", \"4px\", \"margin-top\", \"4px\", \"font-size\", \"12px\", \"opacity\", \"0.75\"], [\"style\", \"display: flex; align-items: center\", 4, \"ngIf\"], [2, \"display\", \"flex\", \"justify-content\", \"center\", \"margin\", \"16px 0\"], [2, \"background\", \"#ffffff\", \"padding\", \"4px 12px\", \"border-radius\", \"20px\", \"box-shadow\", \"0 1px 3px rgba(0, 0, 0, 0.1)\"], [2, \"font-size\", \"12px\", \"color\", \"#6b7280\"], [2, \"margin-right\", \"8px\", \"flex-shrink\", \"0\"], [\"onmouseover\", \"this.style.transform='scale(1.05)'\", \"onmouseout\", \"this.style.transform='scale(1)'\", 2, \"width\", \"32px\", \"height\", \"32px\", \"border-radius\", \"50%\", \"object-fit\", \"cover\", \"cursor\", \"pointer\", \"transition\", \"transform 0.2s\", 3, \"src\", \"alt\", \"click\"], [2, \"font-size\", \"12px\", \"font-weight\", \"600\", \"margin-bottom\", \"4px\", \"opacity\", \"0.75\"], [2, \"word-wrap\", \"break-word\", \"overflow-wrap\", \"break-word\"], [3, \"innerHTML\"], [2, \"margin\", \"8px 0\"], [\"onmouseover\", \"this.style.transform='scale(1.02)'\", \"onmouseout\", \"this.style.transform='scale(1)'\", 2, \"max-width\", \"280px\", \"height\", \"auto\", \"border-radius\", \"12px\", \"cursor\", \"pointer\", \"transition\", \"transform 0.2s\", 3, \"src\", \"alt\", \"click\", \"load\", \"error\"], [\"style\", \"font-size: 14px; margin-top: 8px; line-height: 1.4\", 3, \"color\", \"innerHTML\", 4, \"ngIf\"], [2, \"font-size\", \"14px\", \"margin-top\", \"8px\", \"line-height\", \"1.4\", 3, \"innerHTML\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"12px\", \"padding\", \"12px\", \"background\", \"rgba(255, 255, 255, 0.1)\", \"border-radius\", \"12px\", \"margin\", \"8px 0\", \"min-width\", \"200px\", \"max-width\", \"280px\"], [\"onmouseover\", \"this.style.background='rgba(255, 255, 255, 0.3)'\", \"onmouseout\", \"this.style.background='rgba(255, 255, 255, 0.2)'\", \"title\", \"Lire/Pause\", 2, \"width\", \"40px\", \"height\", \"40px\", \"border-radius\", \"50%\", \"border\", \"none\", \"background\", \"rgba(255, 255, 255, 0.2)\", \"color\", \"inherit\", \"cursor\", \"pointer\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"center\", \"transition\", \"all 0.2s\", \"flex-shrink\", \"0\", 3, \"click\"], [2, \"font-size\", \"14px\"], [2, \"flex\", \"1\", \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"2px\", \"height\", \"24px\", \"overflow\", \"hidden\"], [\"style\", \"\\n                    width: 3px;\\n                    background: currentColor;\\n                    border-radius: 2px;\\n                    opacity: 0.7;\\n                    transition: height 0.3s ease;\\n                  \", 3, \"height\", \"animation\", \"animation-delay\", 4, \"ngFor\", \"ngForOf\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"8px\", \"flex-shrink\", \"0\"], [2, \"font-size\", \"12px\", \"opacity\", \"0.8\", \"min-width\", \"40px\", \"text-align\", \"right\"], [\"style\", \"\\n                    padding: 4px 8px;\\n                    border-radius: 12px;\\n                    border: none;\\n                    background: rgba(255, 255, 255, 0.2);\\n                    color: inherit;\\n                    cursor: pointer;\\n                    font-size: 11px;\\n                    transition: all 0.2s;\\n                  \", \"onmouseover\", \"this.style.background='rgba(255, 255, 255, 0.3)'\", \"onmouseout\", \"this.style.background='rgba(255, 255, 255, 0.2)'\", \"title\", \"Changer la vitesse\", 3, \"click\", 4, \"ngIf\"], [2, \"width\", \"3px\", \"background\", \"currentColor\", \"border-radius\", \"2px\", \"opacity\", \"0.7\", \"transition\", \"height 0.3s ease\"], [\"onmouseover\", \"this.style.background='rgba(255, 255, 255, 0.3)'\", \"onmouseout\", \"this.style.background='rgba(255, 255, 255, 0.2)'\", \"title\", \"Changer la vitesse\", 2, \"padding\", \"4px 8px\", \"border-radius\", \"12px\", \"border\", \"none\", \"background\", \"rgba(255, 255, 255, 0.2)\", \"color\", \"inherit\", \"cursor\", \"pointer\", \"font-size\", \"11px\", \"transition\", \"all 0.2s\", 3, \"click\"], [2, \"display\", \"flex\", \"align-items\", \"center\"], [\"class\", \"fas fa-clock\", \"title\", \"Envoi en cours\", 4, \"ngIf\"], [\"class\", \"fas fa-check\", \"title\", \"Envoy\\u00E9\", 4, \"ngIf\"], [\"class\", \"fas fa-check-double\", \"title\", \"Livr\\u00E9\", 4, \"ngIf\"], [\"class\", \"fas fa-check-double\", \"style\", \"color: #3b82f6\", \"title\", \"Lu\", 4, \"ngIf\"], [\"title\", \"Envoi en cours\", 1, \"fas\", \"fa-clock\"], [\"title\", \"Envoy\\u00E9\", 1, \"fas\", \"fa-check\"], [\"title\", \"Livr\\u00E9\", 1, \"fas\", \"fa-check-double\"], [\"title\", \"Lu\", 1, \"fas\", \"fa-check-double\", 2, \"color\", \"#3b82f6\"], [2, \"display\", \"flex\", \"align-items\", \"start\", \"gap\", \"8px\"], [2, \"width\", \"32px\", \"height\", \"32px\", \"border-radius\", \"50%\", \"object-fit\", \"cover\", 3, \"src\", \"alt\"], [2, \"background\", \"#ffffff\", \"padding\", \"12px 16px\", \"border-radius\", \"18px\", \"box-shadow\", \"0 1px 3px rgba(0, 0, 0, 0.1)\"], [2, \"display\", \"flex\", \"gap\", \"4px\"], [2, \"width\", \"8px\", \"height\", \"8px\", \"background\", \"#6b7280\", \"border-radius\", \"50%\", \"animation\", \"bounce 1s infinite\"], [2, \"width\", \"8px\", \"height\", \"8px\", \"background\", \"#6b7280\", \"border-radius\", \"50%\", \"animation\", \"bounce 1s infinite 0.1s\"], [2, \"width\", \"8px\", \"height\", \"8px\", \"background\", \"#6b7280\", \"border-radius\", \"50%\", \"animation\", \"bounce 1s infinite 0.2s\"], [2, \"position\", \"absolute\", \"top\", \"-2px\", \"right\", \"-2px\", \"width\", \"8px\", \"height\", \"8px\", \"background\", \"#ef4444\", \"border-radius\", \"50%\", \"animation\", \"ping 1s infinite\"], [1, \"fas\", \"fa-paper-plane\"], [2, \"width\", \"16px\", \"height\", \"16px\", \"border\", \"2px solid #ffffff\", \"border-top-color\", \"transparent\", \"border-radius\", \"50%\", \"animation\", \"spin 1s linear infinite\"], [2, \"position\", \"absolute\", \"bottom\", \"80px\", \"left\", \"16px\", \"background\", \"#ffffff\", \"border-radius\", \"16px\", \"box-shadow\", \"0 20px 25px rgba(0, 0, 0, 0.1)\", \"border\", \"1px solid #e5e7eb\", \"z-index\", \"50\", \"width\", \"320px\", \"max-height\", \"300px\", \"overflow-y\", \"auto\"], [2, \"padding\", \"16px\"], [2, \"margin\", \"0 0 12px 0\", \"font-size\", \"14px\", \"font-weight\", \"600\", \"color\", \"#374151\"], [2, \"display\", \"grid\", \"grid-template-columns\", \"repeat(8, 1fr)\", \"gap\", \"8px\"], [\"style\", \"\\n              padding: 8px;\\n              border: none;\\n              background: transparent;\\n              border-radius: 8px;\\n              cursor: pointer;\\n              font-size: 20px;\\n              transition: all 0.2s;\\n            \", \"onmouseover\", \"this.style.background='#f3f4f6'\", \"onmouseout\", \"this.style.background='transparent'\", 3, \"title\", \"click\", 4, \"ngFor\", \"ngForOf\"], [\"onmouseover\", \"this.style.background='#f3f4f6'\", \"onmouseout\", \"this.style.background='transparent'\", 2, \"padding\", \"8px\", \"border\", \"none\", \"background\", \"transparent\", \"border-radius\", \"8px\", \"cursor\", \"pointer\", \"font-size\", \"20px\", \"transition\", \"all 0.2s\", 3, \"title\", \"click\"], [2, \"position\", \"absolute\", \"bottom\", \"80px\", \"left\", \"60px\", \"background\", \"#ffffff\", \"border-radius\", \"16px\", \"box-shadow\", \"0 20px 25px rgba(0, 0, 0, 0.1)\", \"border\", \"1px solid #e5e7eb\", \"z-index\", \"50\", \"min-width\", \"200px\"], [2, \"display\", \"grid\", \"grid-template-columns\", \"repeat(2, 1fr)\", \"gap\", \"12px\"], [\"onmouseover\", \"this.style.background='#f3f4f6'\", \"onmouseout\", \"this.style.background='transparent'\", 2, \"display\", \"flex\", \"flex-direction\", \"column\", \"align-items\", \"center\", \"gap\", \"8px\", \"padding\", \"16px\", \"border\", \"none\", \"background\", \"transparent\", \"border-radius\", \"12px\", \"cursor\", \"pointer\", \"transition\", \"all 0.2s\", 3, \"click\"], [2, \"width\", \"48px\", \"height\", \"48px\", \"background\", \"#dbeafe\", \"border-radius\", \"50%\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"center\"], [1, \"fas\", \"fa-image\", 2, \"color\", \"#3b82f6\", \"font-size\", \"20px\"], [2, \"font-size\", \"14px\", \"font-weight\", \"500\", \"color\", \"#374151\"], [2, \"width\", \"48px\", \"height\", \"48px\", \"background\", \"#fef3c7\", \"border-radius\", \"50%\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"center\"], [1, \"fas\", \"fa-file-alt\", 2, \"color\", \"#f59e0b\", \"font-size\", \"20px\"], [2, \"width\", \"48px\", \"height\", \"48px\", \"background\", \"#dcfce7\", \"border-radius\", \"50%\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"center\"], [1, \"fas\", \"fa-camera\", 2, \"color\", \"#10b981\", \"font-size\", \"20px\"], [2, \"position\", \"fixed\", \"top\", \"0\", \"left\", \"0\", \"right\", \"0\", \"bottom\", \"0\", \"background\", \"rgba(0, 0, 0, 0.25)\", \"z-index\", \"40\", 3, \"click\"], [2, \"position\", \"fixed\", \"bottom\", \"100px\", \"left\", \"50%\", \"transform\", \"translateX(-50%)\", \"background\", \"linear-gradient(135deg, #f59e0b, #d97706)\", \"color\", \"white\", \"padding\", \"20px 24px\", \"border-radius\", \"20px\", \"box-shadow\", \"0 20px 25px rgba(0, 0, 0, 0.2)\", \"z-index\", \"60\", \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"16px\", \"min-width\", \"280px\", \"animation\", \"slideInUp 0.3s ease-out\"], [2, \"width\", \"48px\", \"height\", \"48px\", \"background\", \"rgba(255, 255, 255, 0.2)\", \"border-radius\", \"50%\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"center\", \"animation\", \"pulse 1s infinite\"], [1, \"fas\", \"fa-microphone\", 2, \"font-size\", \"20px\"], [2, \"flex\", \"1\"], [2, \"font-size\", \"18px\", \"font-weight\", \"bold\", \"margin-bottom\", \"4px\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"2px\", \"height\", \"20px\"], [\"style\", \"\\n            width: 3px;\\n            background: rgba(255, 255, 255, 0.8);\\n            border-radius: 2px;\\n            transition: height 0.3s ease;\\n          \", 3, \"height\", \"animation\", \"animation-delay\", 4, \"ngFor\", \"ngForOf\"], [2, \"font-size\", \"12px\", \"opacity\", \"0.8\", \"margin-top\", \"4px\"], [\"onmouseover\", \"this.style.background='rgba(239, 68, 68, 1)'\", \"onmouseout\", \"this.style.background='rgba(239, 68, 68, 0.8)'\", \"title\", \"Annuler l'enregistrement\", 2, \"width\", \"40px\", \"height\", \"40px\", \"border-radius\", \"50%\", \"border\", \"none\", \"background\", \"rgba(239, 68, 68, 0.8)\", \"color\", \"white\", \"cursor\", \"pointer\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"center\", \"transition\", \"all 0.2s\", 3, \"click\"], [1, \"fas\", \"fa-times\", 2, \"font-size\", \"16px\"], [\"onmouseover\", \"this.style.background='rgba(34, 197, 94, 1)'\", \"onmouseout\", \"this.style.background='rgba(34, 197, 94, 0.8)'\", \"title\", \"Envoyer le message vocal\", 2, \"width\", \"40px\", \"height\", \"40px\", \"border-radius\", \"50%\", \"border\", \"none\", \"background\", \"rgba(34, 197, 94, 0.8)\", \"color\", \"white\", \"cursor\", \"pointer\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"center\", \"transition\", \"all 0.2s\", 3, \"click\"], [1, \"fas\", \"fa-paper-plane\", 2, \"font-size\", \"16px\"], [2, \"width\", \"3px\", \"background\", \"rgba(255, 255, 255, 0.8)\", \"border-radius\", \"2px\", \"transition\", \"height 0.3s ease\"]],\n        template: function MessageChatComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelement(0, \"video\", 0, 1)(2, \"video\", 2, 3);\n            i0.ɵɵelementStart(4, \"div\", 4)(5, \"header\", 5)(6, \"button\", 6);\n            i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_6_listener() {\n              return ctx.goBackToConversations();\n            });\n            i0.ɵɵelement(7, \"i\", 7);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(8, \"div\", 8)(9, \"div\", 9)(10, \"img\", 10);\n            i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_img_click_10_listener() {\n              return ctx.openUserProfile(ctx.otherParticipant == null ? null : ctx.otherParticipant.id);\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(11, MessageChatComponent_div_11_Template, 1, 0, \"div\", 11);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(12, \"div\", 12)(13, \"h3\", 13);\n            i0.ɵɵtext(14);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(15, \"div\", 14);\n            i0.ɵɵtemplate(16, MessageChatComponent_div_16_Template, 7, 0, \"div\", 15);\n            i0.ɵɵtemplate(17, MessageChatComponent_span_17_Template, 2, 1, \"span\", 16);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(18, \"div\", 17)(19, \"button\", 18);\n            i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_19_listener() {\n              return ctx.startVideoCall();\n            });\n            i0.ɵɵelement(20, \"i\", 19);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(21, \"button\", 20);\n            i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_21_listener() {\n              return ctx.startVoiceCall();\n            });\n            i0.ɵɵelement(22, \"i\", 21);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(23, \"button\", 22);\n            i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_23_listener() {\n              return ctx.toggleSearch();\n            });\n            i0.ɵɵelement(24, \"i\", 23);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(25, \"button\", 24);\n            i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_25_listener() {\n              return ctx.reloadConversation();\n            });\n            i0.ɵɵelement(26, \"i\", 25);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(27, \"button\", 26);\n            i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_27_listener() {\n              return ctx.toggleMainMenu();\n            });\n            i0.ɵɵelement(28, \"i\", 27);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(29, MessageChatComponent_div_29_Template, 15, 0, \"div\", 28);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(30, \"main\", 29, 30);\n            i0.ɵɵlistener(\"scroll\", function MessageChatComponent_Template_main_scroll_30_listener($event) {\n              return ctx.onScroll($event);\n            })(\"dragover\", function MessageChatComponent_Template_main_dragover_30_listener($event) {\n              return ctx.onDragOver($event);\n            })(\"dragleave\", function MessageChatComponent_Template_main_dragleave_30_listener($event) {\n              return ctx.onDragLeave($event);\n            })(\"drop\", function MessageChatComponent_Template_main_drop_30_listener($event) {\n              return ctx.onDrop($event);\n            });\n            i0.ɵɵtemplate(32, MessageChatComponent_div_32_Template, 7, 0, \"div\", 31);\n            i0.ɵɵtemplate(33, MessageChatComponent_div_33_Template, 4, 0, \"div\", 32);\n            i0.ɵɵtemplate(34, MessageChatComponent_div_34_Template, 7, 1, \"div\", 33);\n            i0.ɵɵtemplate(35, MessageChatComponent_div_35_Template, 3, 3, \"div\", 34);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(36, \"footer\", 35)(37, \"form\", 36);\n            i0.ɵɵlistener(\"ngSubmit\", function MessageChatComponent_Template_form_ngSubmit_37_listener() {\n              return ctx.sendMessage();\n            });\n            i0.ɵɵelementStart(38, \"div\", 37)(39, \"button\", 38);\n            i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_39_listener() {\n              return ctx.toggleEmojiPicker();\n            });\n            i0.ɵɵelement(40, \"i\", 39);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(41, \"button\", 40);\n            i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_41_listener() {\n              return ctx.toggleAttachmentMenu();\n            });\n            i0.ɵɵelement(42, \"i\", 41);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(43, \"button\", 42);\n            i0.ɵɵlistener(\"mousedown\", function MessageChatComponent_Template_button_mousedown_43_listener($event) {\n              return ctx.onRecordStart($event);\n            })(\"mouseup\", function MessageChatComponent_Template_button_mouseup_43_listener($event) {\n              return ctx.onRecordEnd($event);\n            })(\"mouseleave\", function MessageChatComponent_Template_button_mouseleave_43_listener($event) {\n              return ctx.onRecordCancel($event);\n            })(\"touchstart\", function MessageChatComponent_Template_button_touchstart_43_listener($event) {\n              return ctx.onRecordStart($event);\n            })(\"touchend\", function MessageChatComponent_Template_button_touchend_43_listener($event) {\n              return ctx.onRecordEnd($event);\n            })(\"touchcancel\", function MessageChatComponent_Template_button_touchcancel_43_listener($event) {\n              return ctx.onRecordCancel($event);\n            });\n            i0.ɵɵelement(44, \"i\");\n            i0.ɵɵtemplate(45, MessageChatComponent_div_45_Template, 1, 0, \"div\", 43);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(46, \"div\", 44)(47, \"textarea\", 45);\n            i0.ɵɵlistener(\"keydown\", function MessageChatComponent_Template_textarea_keydown_47_listener($event) {\n              return ctx.onInputKeyDown($event);\n            })(\"input\", function MessageChatComponent_Template_textarea_input_47_listener($event) {\n              return ctx.onInputChange($event);\n            })(\"focus\", function MessageChatComponent_Template_textarea_focus_47_listener() {\n              return ctx.onInputFocus();\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(48, \"button\", 46);\n            i0.ɵɵtemplate(49, MessageChatComponent_i_49_Template, 1, 0, \"i\", 47);\n            i0.ɵɵtemplate(50, MessageChatComponent_div_50_Template, 1, 0, \"div\", 48);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(51, MessageChatComponent_div_51_Template, 6, 1, \"div\", 49);\n            i0.ɵɵtemplate(52, MessageChatComponent_div_52_Template, 20, 0, \"div\", 50);\n            i0.ɵɵelementStart(53, \"input\", 51, 52);\n            i0.ɵɵlistener(\"change\", function MessageChatComponent_Template_input_change_53_listener($event) {\n              return ctx.onFileSelected($event);\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(55, MessageChatComponent_div_55_Template, 1, 0, \"div\", 53);\n            i0.ɵɵtemplate(56, MessageChatComponent_div_56_Template, 15, 3, \"div\", 54);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(10);\n            i0.ɵɵproperty(\"src\", (ctx.otherParticipant == null ? null : ctx.otherParticipant.image) || \"assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", ctx.otherParticipant == null ? null : ctx.otherParticipant.username);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.otherParticipant == null ? null : ctx.otherParticipant.isOnline);\n            i0.ɵɵadvance(3);\n            i0.ɵɵtextInterpolate1(\" \", (ctx.otherParticipant == null ? null : ctx.otherParticipant.username) || \"Utilisateur\", \" \");\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", ctx.isUserTyping);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.isUserTyping);\n            i0.ɵɵadvance(6);\n            i0.ɵɵstyleProp(\"background\", ctx.searchMode ? \"#dcfce7\" : \"transparent\")(\"color\", ctx.searchMode ? \"#16a34a\" : \"#6b7280\");\n            i0.ɵɵadvance(2);\n            i0.ɵɵstyleProp(\"opacity\", ctx.isLoading ? \"0.5\" : \"1\");\n            i0.ɵɵproperty(\"disabled\", ctx.isLoading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵstyleProp(\"animation\", ctx.isLoading ? \"spin 1s linear infinite\" : \"none\");\n            i0.ɵɵadvance(1);\n            i0.ɵɵstyleProp(\"background\", ctx.showMainMenu ? \"#dcfce7\" : \"transparent\")(\"color\", ctx.showMainMenu ? \"#16a34a\" : \"#6b7280\");\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", ctx.showMainMenu);\n            i0.ɵɵadvance(1);\n            i0.ɵɵstyleProp(\"background\", ctx.isDragOver ? \"rgba(34, 197, 94, 0.1)\" : \"transparent\");\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", ctx.isDragOver);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.messages.length === 0);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.messages.length > 0);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"formGroup\", ctx.messageForm);\n            i0.ɵɵadvance(2);\n            i0.ɵɵstyleProp(\"background\", ctx.showEmojiPicker ? \"#dcfce7\" : \"transparent\")(\"color\", ctx.showEmojiPicker ? \"#16a34a\" : \"#6b7280\");\n            i0.ɵɵadvance(2);\n            i0.ɵɵstyleProp(\"background\", ctx.showAttachmentMenu ? \"#dcfce7\" : \"transparent\")(\"color\", ctx.showAttachmentMenu ? \"#16a34a\" : \"#6b7280\");\n            i0.ɵɵadvance(2);\n            i0.ɵɵstyleProp(\"background\", ctx.isRecordingVoice ? \"#fef3c7\" : \"transparent\")(\"color\", ctx.isRecordingVoice ? \"#f59e0b\" : \"#6b7280\")(\"transform\", ctx.isRecordingVoice ? \"scale(1.1)\" : \"scale(1)\");\n            i0.ɵɵadvance(1);\n            i0.ɵɵclassMap(ctx.isRecordingVoice ? \"fas fa-stop\" : \"fas fa-microphone\");\n            i0.ɵɵstyleProp(\"animation\", ctx.isRecordingVoice ? \"pulse 1s infinite\" : \"none\");\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.isRecordingVoice);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"disabled\", ctx.isInputDisabled());\n            i0.ɵɵadvance(1);\n            i0.ɵɵstyleProp(\"background\", !ctx.messageForm.valid || ctx.isSendingMessage ? \"#9ca3af\" : \"#3b82f6\")(\"cursor\", !ctx.messageForm.valid || ctx.isSendingMessage ? \"not-allowed\" : \"pointer\");\n            i0.ɵɵproperty(\"disabled\", !ctx.messageForm.valid || ctx.isSendingMessage);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.isSendingMessage);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.isSendingMessage);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.showEmojiPicker);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.showAttachmentMenu);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"accept\", ctx.getFileAcceptTypes());\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", ctx.showEmojiPicker || ctx.showAttachmentMenu || ctx.showMainMenu);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.isRecordingVoice);\n          }\n        },\n        dependencies: [i6.NgForOf, i6.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName],\n        styles: [\"@keyframes _ngcontent-%COMP%_pulse {\\n      0%,\\n      100% {\\n        opacity: 1;\\n      }\\n      50% {\\n        opacity: 0.5;\\n      }\\n    }\\n    @keyframes _ngcontent-%COMP%_bounce {\\n      0%,\\n      20%,\\n      53%,\\n      80%,\\n      100% {\\n        transform: translateY(0);\\n      }\\n      40%,\\n      43% {\\n        transform: translateY(-8px);\\n      }\\n      70% {\\n        transform: translateY(-4px);\\n      }\\n    }\\n    @keyframes _ngcontent-%COMP%_spin {\\n      from {\\n        transform: rotate(0deg);\\n      }\\n      to {\\n        transform: rotate(360deg);\\n      }\\n    }\\n    @keyframes _ngcontent-%COMP%_ping {\\n      75%,\\n      100% {\\n        transform: scale(2);\\n        opacity: 0;\\n      }\\n    }\\n    @keyframes _ngcontent-%COMP%_slideInUp {\\n      from {\\n        transform: translateX(-50%) translateY(20px);\\n        opacity: 0;\\n      }\\n      to {\\n        transform: translateX(-50%) translateY(0);\\n        opacity: 1;\\n      }\\n    }\"]\n      });\n    }\n  }\n  return MessageChatComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}