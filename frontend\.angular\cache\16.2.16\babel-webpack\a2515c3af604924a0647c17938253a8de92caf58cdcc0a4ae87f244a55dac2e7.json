{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/OneDrive/Bureau/Project PI/devBridge/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { BehaviorSubject, throwError, from } from 'rxjs';\nimport { map, catchError, switchMap } from 'rxjs/operators';\nimport { CallStatus } from '../models/message.model';\nimport { INITIATE_CALL_MUTATION, ACCEPT_CALL_MUTATION, REJECT_CALL_MUTATION, END_CALL_MUTATION, TOGGLE_CALL_MEDIA_MUTATION, INCOMING_CALL_SUBSCRIPTION, CALL_STATUS_CHANGED_SUBSCRIPTION } from '../graphql/message.graphql';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"apollo-angular\";\nimport * as i2 from \"./logger.service\";\nexport class CallService {\n  constructor(apollo, logger) {\n    this.apollo = apollo;\n    this.logger = logger;\n    // État des appels\n    this.activeCall = new BehaviorSubject(null);\n    this.incomingCall = new BehaviorSubject(null);\n    // Observables publics\n    this.activeCall$ = this.activeCall.asObservable();\n    this.incomingCall$ = this.incomingCall.asObservable();\n    // Propriétés pour la gestion des sons\n    this.sounds = {};\n    this.isPlaying = {};\n    this.muted = false;\n    // États simples pour les médias\n    this.isVideoEnabled = true;\n    this.isAudioEnabled = true;\n    this.preloadSounds();\n    this.initializeSubscriptions();\n  }\n  /**\n   * Initialise les subscriptions avec un délai pour s'assurer que l'authentification est prête\n   */\n  initializeSubscriptions() {\n    // Attendre un peu pour s'assurer que l'authentification est prête\n    setTimeout(() => {\n      this.subscribeToIncomingCalls();\n      this.subscribeToCallStatusChanges();\n    }, 1000);\n    // Réessayer après 5 secondes si la première tentative échoue\n    setTimeout(() => {\n      if (!this.incomingCall.value) {\n        console.log('🔄 [CallService] Retrying subscription initialization...');\n        this.subscribeToIncomingCalls();\n        this.subscribeToCallStatusChanges();\n      }\n    }, 5000);\n  }\n  /**\n   * Précharge les sons utilisés dans l'application\n   */\n  preloadSounds() {\n    // Créer des sons synthétiques si les fichiers ne sont pas disponibles\n    this.createSyntheticSounds();\n    // Essayer de charger les vrais fichiers audio\n    this.loadSound('ringtone', 'assets/sounds/ringtone.mp3');\n    this.loadSound('call-end', 'assets/sounds/call-end.mp3');\n    this.loadSound('call-connected', 'assets/sounds/call-connected.mp3');\n  }\n  /**\n   * Crée des sons synthétiques comme fallback\n   */\n  createSyntheticSounds() {\n    try {\n      // Créer un contexte audio pour les sons synthétiques\n      const audioContext = new (window.AudioContext || window.webkitAudioContext)();\n      // Son de sonnerie (mélodie agréable)\n      this.sounds['ringtone-synthetic'] = this.createRingtoneSound(audioContext);\n      // Son de connexion (accord agréable)\n      this.sounds['call-connected-synthetic'] = this.createConnectedSound(audioContext);\n      // Son de fin d'appel (ton descendant)\n      this.sounds['call-end-synthetic'] = this.createEndSound(audioContext);\n      console.log('🔊 [CallService] Synthetic sounds created as fallback');\n    } catch (error) {\n      console.warn('⚠️ [CallService] Could not create synthetic sounds:', error);\n    }\n  }\n  /**\n   * Crée une sonnerie agréable (mélodie)\n   */\n  createRingtoneSound(audioContext) {\n    const audio = new Audio();\n    audio.volume = 0.4;\n    let isPlaying = false;\n    let intervalId = null;\n    audio.playSynthetic = () => {\n      if (isPlaying) return Promise.resolve();\n      isPlaying = true;\n      const playMelody = () => {\n        if (!isPlaying) return;\n        // Mélodie agréable : Do-Mi-Sol-Do (523, 659, 784, 1047 Hz)\n        const notes = [523, 659, 784, 1047];\n        let noteIndex = 0;\n        const playNote = () => {\n          if (!isPlaying || noteIndex >= notes.length) {\n            noteIndex = 0;\n            setTimeout(playMelody, 500); // Pause entre les répétitions\n            return;\n          }\n          const oscillator = audioContext.createOscillator();\n          const gainNode = audioContext.createGain();\n          oscillator.connect(gainNode);\n          gainNode.connect(audioContext.destination);\n          oscillator.frequency.value = notes[noteIndex];\n          oscillator.type = 'sine';\n          gainNode.gain.setValueAtTime(0, audioContext.currentTime);\n          gainNode.gain.linearRampToValueAtTime(0.3, audioContext.currentTime + 0.05);\n          gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.4);\n          oscillator.start(audioContext.currentTime);\n          oscillator.stop(audioContext.currentTime + 0.4);\n          noteIndex++;\n          setTimeout(playNote, 200);\n        };\n        playNote();\n      };\n      playMelody();\n      return Promise.resolve();\n    };\n    audio.stopSynthetic = () => {\n      isPlaying = false;\n      if (intervalId) {\n        clearInterval(intervalId);\n        intervalId = null;\n      }\n    };\n    return audio;\n  }\n  /**\n   * Crée un son de connexion agréable (accord)\n   */\n  createConnectedSound(audioContext) {\n    const audio = new Audio();\n    audio.volume = 0.4;\n    audio.playSynthetic = () => {\n      // Accord majeur : Do-Mi-Sol (523, 659, 784 Hz)\n      const frequencies = [523, 659, 784];\n      frequencies.forEach((freq, index) => {\n        const oscillator = audioContext.createOscillator();\n        const gainNode = audioContext.createGain();\n        oscillator.connect(gainNode);\n        gainNode.connect(audioContext.destination);\n        oscillator.frequency.value = freq;\n        oscillator.type = 'sine';\n        const startTime = audioContext.currentTime + index * 0.1;\n        gainNode.gain.setValueAtTime(0, startTime);\n        gainNode.gain.linearRampToValueAtTime(0.2, startTime + 0.1);\n        gainNode.gain.exponentialRampToValueAtTime(0.01, startTime + 1.0);\n        oscillator.start(startTime);\n        oscillator.stop(startTime + 1.0);\n      });\n      return Promise.resolve();\n    };\n    return audio;\n  }\n  /**\n   * Crée un son de fin d'appel (ton descendant)\n   */\n  createEndSound(audioContext) {\n    const audio = new Audio();\n    audio.volume = 0.4;\n    audio.playSynthetic = () => {\n      const oscillator = audioContext.createOscillator();\n      const gainNode = audioContext.createGain();\n      oscillator.connect(gainNode);\n      gainNode.connect(audioContext.destination);\n      // Ton descendant de 800Hz à 200Hz\n      oscillator.frequency.setValueAtTime(800, audioContext.currentTime);\n      oscillator.frequency.exponentialRampToValueAtTime(200, audioContext.currentTime + 1.0);\n      oscillator.type = 'sine';\n      gainNode.gain.setValueAtTime(0, audioContext.currentTime);\n      gainNode.gain.linearRampToValueAtTime(0.3, audioContext.currentTime + 0.1);\n      gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 1.0);\n      oscillator.start(audioContext.currentTime);\n      oscillator.stop(audioContext.currentTime + 1.0);\n      return Promise.resolve();\n    };\n    return audio;\n  }\n  /**\n   * Charge un fichier audio\n   */\n  loadSound(name, path) {\n    try {\n      const audio = new Audio();\n      // Configuration de l'audio\n      audio.preload = 'auto';\n      audio.volume = 0.7;\n      // Gérer les événements de chargement\n      audio.addEventListener('canplaythrough', () => {\n        console.log(`✅ [CallService] Sound ${name} loaded successfully from ${path}`);\n      });\n      audio.addEventListener('error', e => {\n        console.error(`❌ [CallService] Error loading sound ${name} from ${path}:`, e);\n        console.log(`🔄 [CallService] Trying to load ${name} with different approach...`);\n        // Essayer de charger avec un chemin relatif\n        const altPath = path.startsWith('/') ? path.substring(1) : path;\n        if (altPath !== path) {\n          setTimeout(() => {\n            audio.src = altPath;\n            audio.load();\n          }, 100);\n        }\n      });\n      audio.addEventListener('ended', () => {\n        this.isPlaying[name] = false;\n        console.log(`🔇 [CallService] Sound ${name} ended`);\n      });\n      // Définir la source et charger\n      audio.src = path;\n      audio.load();\n      this.sounds[name] = audio;\n      this.isPlaying[name] = false;\n      console.log(`🔊 [CallService] Loading sound ${name} from ${path}`);\n    } catch (error) {\n      console.error(`❌ [CallService] Error creating audio element for ${name}:`, error);\n    }\n  }\n  /**\n   * Joue un son\n   */\n  play(name, loop = false) {\n    if (this.muted) {\n      console.log(`🔇 [CallService] Sound ${name} muted`);\n      return;\n    }\n    try {\n      let sound = this.sounds[name];\n      // Si le son principal n'est pas disponible, essayer la version synthétique\n      if (!sound || sound.error) {\n        const syntheticName = `${name}-synthetic`;\n        sound = this.sounds[syntheticName];\n        if (sound) {\n          console.log(`🔊 [CallService] Using synthetic sound for ${name}`);\n        }\n      }\n      if (!sound) {\n        console.warn(`🔊 [CallService] Sound ${name} not found (neither original nor synthetic)`);\n        // Créer un bip simple comme dernier recours\n        this.playSimpleBeep(name === 'ringtone' ? 800 : name === 'call-connected' ? 1000 : 400);\n        return;\n      }\n      sound.loop = loop;\n      sound.volume = 0.7;\n      if (!this.isPlaying[name]) {\n        console.log(`🔊 [CallService] Playing sound: ${name} (loop: ${loop})`);\n        // Vérifier si c'est un son synthétique\n        if (sound.playSynthetic) {\n          sound.playSynthetic().then(() => {\n            console.log(`✅ [CallService] Synthetic sound ${name} started successfully`);\n            this.isPlaying[name] = true;\n            // Pour la sonnerie synthétique, elle gère sa propre boucle\n            if (name === 'ringtone' && !loop) {\n              // Si ce n'est pas en boucle, arrêter après un certain temps\n              setTimeout(() => {\n                this.isPlaying[name] = false;\n              }, 3000);\n            } else if (name !== 'ringtone') {\n              // Pour les autres sons, arrêter après leur durée\n              setTimeout(() => {\n                this.isPlaying[name] = false;\n              }, name === 'call-connected' ? 1200 : 1000);\n            }\n          }).catch(error => {\n            console.error(`❌ [CallService] Error playing synthetic sound ${name}:`, error);\n          });\n        } else {\n          // Son normal\n          sound.currentTime = 0;\n          sound.play().then(() => {\n            console.log(`✅ [CallService] Sound ${name} started successfully`);\n            this.isPlaying[name] = true;\n          }).catch(error => {\n            console.error(`❌ [CallService] Error playing sound ${name}:`, error);\n            // Essayer le son synthétique en cas d'échec\n            const syntheticName = `${name}-synthetic`;\n            const syntheticSound = this.sounds[syntheticName];\n            if (syntheticSound && syntheticSound.playSynthetic) {\n              console.log(`🔄 [CallService] Falling back to synthetic sound for ${name}`);\n              this.play(name, loop);\n            } else {\n              // Dernier recours : bip simple\n              this.playSimpleBeep(name === 'ringtone' ? 800 : name === 'call-connected' ? 1000 : 400);\n            }\n          });\n        }\n      } else {\n        console.log(`🔊 [CallService] Sound ${name} already playing`);\n      }\n    } catch (error) {\n      console.error(`❌ [CallService] Error in play method for ${name}:`, error);\n      // Dernier recours\n      this.playSimpleBeep(name === 'ringtone' ? 800 : name === 'call-connected' ? 1000 : 400);\n    }\n  }\n  /**\n   * Joue un bip simple comme dernier recours\n   */\n  playSimpleBeep(frequency) {\n    try {\n      const audioContext = new (window.AudioContext || window.webkitAudioContext)();\n      const oscillator = audioContext.createOscillator();\n      const gainNode = audioContext.createGain();\n      oscillator.connect(gainNode);\n      gainNode.connect(audioContext.destination);\n      oscillator.frequency.value = frequency;\n      oscillator.type = 'sine';\n      gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);\n      gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);\n      oscillator.start(audioContext.currentTime);\n      oscillator.stop(audioContext.currentTime + 0.3);\n      console.log(`🔊 [CallService] Playing simple beep at ${frequency}Hz`);\n    } catch (error) {\n      console.error('❌ [CallService] Could not play simple beep:', error);\n    }\n  }\n  /**\n   * Arrête un son\n   */\n  stop(name) {\n    try {\n      let sound = this.sounds[name];\n      // Essayer aussi la version synthétique\n      if (!sound) {\n        sound = this.sounds[`${name}-synthetic`];\n      }\n      if (!sound) {\n        console.warn(`🔊 [CallService] Sound ${name} not found for stopping`);\n        return;\n      }\n      if (this.isPlaying[name]) {\n        console.log(`🔇 [CallService] Stopping sound: ${name}`);\n        // Arrêter le son synthétique si c'est le cas\n        if (sound.stopSynthetic) {\n          sound.stopSynthetic();\n        } else {\n          sound.pause();\n          sound.currentTime = 0;\n        }\n        this.isPlaying[name] = false;\n      } else {\n        console.log(`🔇 [CallService] Sound ${name} was not playing`);\n      }\n    } catch (error) {\n      console.error(`❌ [CallService] Error stopping sound ${name}:`, error);\n    }\n  }\n  /**\n   * Arrête tous les sons\n   */\n  stopAllSounds() {\n    console.log('🔇 [CallService] Stopping all sounds');\n    this.stop('ringtone');\n    this.stop('call-connected');\n    this.stop('call-end');\n  }\n  /**\n   * S'abonne aux appels entrants\n   */\n  subscribeToIncomingCalls() {\n    console.log('🔔 [CallService] Setting up incoming call subscription...');\n    try {\n      this.apollo.subscribe({\n        query: INCOMING_CALL_SUBSCRIPTION,\n        errorPolicy: 'all' // Continuer même en cas d'erreur\n      }).subscribe({\n        next: ({\n          data,\n          errors\n        }) => {\n          if (errors) {\n            console.warn('⚠️ [CallService] GraphQL errors in subscription:', errors);\n          }\n          if (data?.incomingCall) {\n            console.log('📞 [CallService] Incoming call received:', {\n              callId: data.incomingCall.id,\n              callType: data.incomingCall.type,\n              caller: data.incomingCall.caller?.username,\n              conversationId: data.incomingCall.conversationId\n            });\n            this.handleIncomingCall(data.incomingCall);\n          }\n        },\n        error: error => {\n          console.error('❌ [CallService] Error in incoming call subscription:', error);\n          // Réessayer après 5 secondes en cas d'erreur\n          setTimeout(() => {\n            console.log('🔄 [CallService] Retrying incoming call subscription...');\n            this.subscribeToIncomingCalls();\n          }, 5000);\n        },\n        complete: () => {\n          console.log('🔚 [CallService] Incoming call subscription completed');\n          // Réessayer si la subscription se ferme de manière inattendue\n          setTimeout(() => {\n            console.log('🔄 [CallService] Restarting subscription after completion...');\n            this.subscribeToIncomingCalls();\n          }, 2000);\n        }\n      });\n    } catch (error) {\n      console.error('❌ [CallService] Failed to create subscription:', error);\n      // Réessayer après 3 secondes\n      setTimeout(() => {\n        this.subscribeToIncomingCalls();\n      }, 3000);\n    }\n  }\n  /**\n   * Méthode publique pour forcer la réinitialisation de la subscription\n   */\n  reinitializeSubscription() {\n    console.log('🔄 [CallService] Manually reinitializing subscription...');\n    this.subscribeToIncomingCalls();\n    this.subscribeToCallStatusChanges();\n  }\n  /**\n   * Méthode de test pour vérifier les sons\n   */\n  testSounds() {\n    console.log('🧪 [CallService] Testing sounds...');\n    // Test de la sonnerie\n    console.log('🔊 Testing ringtone...');\n    this.play('ringtone', true);\n    setTimeout(() => {\n      this.stop('ringtone');\n      console.log('🔊 Testing call-connected...');\n      this.play('call-connected');\n    }, 3000);\n    setTimeout(() => {\n      console.log('🔊 Testing call-end...');\n      this.play('call-end');\n    }, 5000);\n  }\n  /**\n   * Test spécifique pour la sonnerie\n   */\n  testRingtone() {\n    console.log('🧪 [CallService] Testing ringtone specifically...');\n    console.log('🔊 [CallService] Available sounds:', Object.keys(this.sounds));\n    // Vérifier si le son est chargé\n    const ringtone = this.sounds['ringtone'];\n    if (ringtone) {\n      console.log('✅ [CallService] Ringtone found:', {\n        src: ringtone.src,\n        readyState: ringtone.readyState,\n        error: ringtone.error,\n        duration: ringtone.duration\n      });\n    } else {\n      console.log('❌ [CallService] Ringtone not found in sounds');\n    }\n    // Jouer la sonnerie\n    this.play('ringtone', true);\n    // Arrêter après 5 secondes\n    setTimeout(() => {\n      this.stop('ringtone');\n      console.log('🔇 [CallService] Ringtone test stopped');\n    }, 5000);\n  }\n  /**\n   * S'abonne aux changements de statut d'appel\n   */\n  subscribeToCallStatusChanges() {\n    console.log('📞 [CallService] Setting up call status change subscription...');\n    try {\n      this.apollo.subscribe({\n        query: CALL_STATUS_CHANGED_SUBSCRIPTION,\n        errorPolicy: 'all'\n      }).subscribe({\n        next: ({\n          data,\n          errors\n        }) => {\n          if (errors) {\n            console.warn('⚠️ [CallService] GraphQL errors in call status subscription:', errors);\n          }\n          if (data?.callStatusChanged) {\n            console.log('📞 [CallService] Call status changed:', data.callStatusChanged);\n            this.handleCallStatusChange(data.callStatusChanged);\n          }\n        },\n        error: error => {\n          console.error('❌ [CallService] Error in call status subscription:', error);\n          // Réessayer après 5 secondes\n          setTimeout(() => {\n            this.subscribeToCallStatusChanges();\n          }, 5000);\n        },\n        complete: () => {\n          console.log('🔚 [CallService] Call status subscription completed');\n          // Réessayer si la subscription se ferme\n          setTimeout(() => {\n            this.subscribeToCallStatusChanges();\n          }, 2000);\n        }\n      });\n    } catch (error) {\n      console.error('❌ [CallService] Failed to create call status subscription:', error);\n      setTimeout(() => {\n        this.subscribeToCallStatusChanges();\n      }, 3000);\n    }\n  }\n  /**\n   * Gère un appel entrant\n   */\n  handleIncomingCall(call) {\n    console.log('🔔 [CallService] Handling incoming call:', {\n      callId: call.id,\n      callType: call.type,\n      caller: call.caller?.username,\n      conversationId: call.conversationId\n    });\n    this.incomingCall.next(call);\n    this.play('ringtone', true);\n    console.log('🔊 [CallService] Ringtone started, call notification sent to UI');\n  }\n  /**\n   * Gère les changements de statut d'appel\n   */\n  handleCallStatusChange(call) {\n    console.log('📞 [CallService] Call status changed:', call.status);\n    switch (call.status) {\n      case CallStatus.REJECTED:\n        console.log('❌ [CallService] Call was rejected');\n        this.stop('ringtone');\n        this.play('call-end');\n        this.activeCall.next(null);\n        this.incomingCall.next(null);\n        break;\n      case CallStatus.ENDED:\n        console.log('📞 [CallService] Call ended');\n        this.stopAllSounds();\n        this.play('call-end');\n        this.activeCall.next(null);\n        this.incomingCall.next(null);\n        break;\n      case CallStatus.CONNECTED:\n        console.log('✅ [CallService] Call connected');\n        this.stop('ringtone');\n        this.play('call-connected');\n        this.activeCall.next(call);\n        this.incomingCall.next(null);\n        break;\n      case CallStatus.RINGING:\n        console.log('📞 [CallService] Call is ringing');\n        this.play('ringtone', true);\n        break;\n      default:\n        console.log('📞 [CallService] Unknown call status:', call.status);\n        break;\n    }\n  }\n  /**\n   * Initie un appel WebRTC\n   */\n  initiateCall(recipientId, callType, conversationId) {\n    console.log('🔄 [CallService] Initiating call:', {\n      recipientId,\n      callType,\n      conversationId\n    });\n    if (!recipientId) {\n      const error = new Error('Recipient ID is required');\n      console.error('❌ [CallService] initiateCall error:', error);\n      return throwError(() => error);\n    }\n    // Générer un ID unique pour l'appel\n    const callId = `call_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n    // Pour l'instant, utiliser une offre WebRTC factice\n    const offer = JSON.stringify({\n      type: 'offer',\n      sdp: 'v=0\\r\\no=- 0 0 IN IP4 127.0.0.1\\r\\ns=-\\r\\nt=0 0\\r\\n'\n    });\n    const variables = {\n      recipientId,\n      callType: callType,\n      callId,\n      offer,\n      conversationId\n    };\n    console.log('📤 [CallService] Sending initiate call mutation:', variables);\n    return this.apollo.mutate({\n      mutation: INITIATE_CALL_MUTATION,\n      variables\n    }).pipe(map(result => {\n      console.log('✅ [CallService] Call initiated successfully:', result);\n      if (!result.data?.initiateCall) {\n        throw new Error('No call data received from server');\n      }\n      const call = result.data.initiateCall;\n      console.log('📞 [CallService] Call details:', {\n        id: call.id,\n        type: call.type,\n        status: call.status,\n        caller: call.caller?.username,\n        recipient: call.recipient?.username\n      });\n      // Mettre à jour l'état local\n      this.activeCall.next(call);\n      return call;\n    }), catchError(error => {\n      console.error('❌ [CallService] initiateCall error:', error);\n      this.logger.error('Error initiating call:', error);\n      let errorMessage = \"Erreur lors de l'initiation de l'appel\";\n      if (error.networkError) {\n        errorMessage = 'Erreur de connexion réseau';\n      } else if (error.graphQLErrors?.length > 0) {\n        errorMessage = error.graphQLErrors[0].message || errorMessage;\n      }\n      return throwError(() => new Error(errorMessage));\n    }));\n  }\n  /**\n   * Accepte un appel entrant\n   */\n  acceptCall(incomingCall) {\n    console.log('🔄 [CallService] Accepting call:', incomingCall.id);\n    // Générer une réponse WebRTC factice\n    const answer = JSON.stringify({\n      type: 'answer',\n      sdp: 'v=0\\r\\no=- 0 0 IN IP4 127.0.0.1\\r\\ns=-\\r\\nt=0 0\\r\\n'\n    });\n    return this.apollo.mutate({\n      mutation: ACCEPT_CALL_MUTATION,\n      variables: {\n        callId: incomingCall.id,\n        answer\n      }\n    }).pipe(switchMap(result => {\n      console.log('✅ [CallService] Call accepted successfully:', result);\n      if (!result.data?.acceptCall) {\n        throw new Error('No call data received from server');\n      }\n      const call = result.data.acceptCall;\n      // Arrêter la sonnerie\n      this.stop('ringtone');\n      this.play('call-connected');\n      // Démarrer les médias pour l'appel de manière asynchrone\n      return from(this.startMediaForCall(incomingCall, call));\n    }), catchError(error => {\n      console.error('❌ [CallService] acceptCall error:', error);\n      this.logger.error('Error accepting call:', error);\n      return throwError(() => new Error(\"Erreur lors de l'acceptation de l'appel\"));\n    }));\n  }\n  /**\n   * Rejette un appel entrant\n   */\n  rejectCall(callId, reason) {\n    console.log('🔄 [CallService] Rejecting call:', callId, reason);\n    return this.apollo.mutate({\n      mutation: REJECT_CALL_MUTATION,\n      variables: {\n        callId,\n        reason: reason || 'User rejected'\n      }\n    }).pipe(map(result => {\n      console.log('✅ [CallService] Call rejected successfully:', result);\n      if (!result.data?.rejectCall) {\n        throw new Error('No response received from server');\n      }\n      // Mettre à jour l'état local\n      this.incomingCall.next(null);\n      this.activeCall.next(null);\n      // Arrêter la sonnerie\n      this.stop('ringtone');\n      return result.data.rejectCall;\n    }), catchError(error => {\n      console.error('❌ [CallService] rejectCall error:', error);\n      this.logger.error('Error rejecting call:', error);\n      return throwError(() => new Error(\"Erreur lors du rejet de l'appel\"));\n    }));\n  }\n  /**\n   * Termine un appel en cours\n   */\n  endCall(callId) {\n    console.log('🔄 [CallService] Ending call:', callId);\n    return this.apollo.mutate({\n      mutation: END_CALL_MUTATION,\n      variables: {\n        callId,\n        feedback: null // Pas de feedback pour l'instant\n      }\n    }).pipe(map(result => {\n      console.log('✅ [CallService] Call ended successfully:', result);\n      if (!result.data?.endCall) {\n        throw new Error('No response received from server');\n      }\n      // Mettre à jour l'état local\n      this.activeCall.next(null);\n      this.incomingCall.next(null);\n      // Arrêter tous les sons\n      this.stopAllSounds();\n      this.play('call-end');\n      return result.data.endCall;\n    }), catchError(error => {\n      console.error('❌ [CallService] endCall error:', error);\n      this.logger.error('Error ending call:', error);\n      return throwError(() => new Error(\"Erreur lors de la fin de l'appel\"));\n    }));\n  }\n  /**\n   * Bascule l'état des médias (audio/vidéo) pendant un appel\n   */\n  toggleMedia(callId, enableVideo, enableAudio) {\n    console.log('🔄 [CallService] Toggling media:', {\n      callId,\n      enableVideo,\n      enableAudio\n    });\n    return this.apollo.mutate({\n      mutation: TOGGLE_CALL_MEDIA_MUTATION,\n      variables: {\n        callId,\n        video: enableVideo,\n        audio: enableAudio\n      }\n    }).pipe(map(result => {\n      console.log('✅ [CallService] Media toggled successfully:', result);\n      if (!result.data?.toggleCallMedia) {\n        throw new Error('No response received from server');\n      }\n      return result.data.toggleCallMedia;\n    }), catchError(error => {\n      console.error('❌ [CallService] toggleMedia error:', error);\n      this.logger.error('Error toggling media:', error);\n      return throwError(() => new Error('Erreur lors du changement des médias'));\n    }));\n  }\n  /**\n   * Démarre les médias pour un appel accepté\n   */\n  startMediaForCall(incomingCall, call) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      console.log('🎥 [CallService] Call connected - playing connection sound');\n      // Jouer le son de connexion\n      _this.play('call-connected');\n      // Mettre à jour l'état local\n      _this.activeCall.next(call);\n      _this.incomingCall.next(null); // Supprimer l'appel entrant\n      return call;\n    })();\n  }\n  /**\n   * Active/désactive la vidéo\n   */\n  toggleVideo() {\n    this.isVideoEnabled = !this.isVideoEnabled;\n    console.log('📹 [CallService] Video toggled:', this.isVideoEnabled);\n    return this.isVideoEnabled;\n  }\n  /**\n   * Active/désactive l'audio\n   */\n  toggleAudio() {\n    this.isAudioEnabled = !this.isAudioEnabled;\n    console.log('🎤 [CallService] Audio toggled:', this.isAudioEnabled);\n    return this.isAudioEnabled;\n  }\n  /**\n   * Obtient l'état de la vidéo\n   */\n  getVideoEnabled() {\n    return this.isVideoEnabled;\n  }\n  /**\n   * Obtient l'état de l'audio\n   */\n  getAudioEnabled() {\n    return this.isAudioEnabled;\n  }\n  /**\n   * Arrête tous les sons\n   */\n  stopAllSounds() {\n    console.log('🔇 [CallService] Stopping all sounds');\n    Object.keys(this.sounds).forEach(name => {\n      this.stop(name);\n    });\n  }\n  /**\n   * Active les sons après interaction utilisateur (pour contourner les restrictions du navigateur)\n   */\n  enableSounds() {\n    console.log('🔊 [CallService] Enabling sounds after user interaction');\n    Object.values(this.sounds).forEach(sound => {\n      if (sound) {\n        sound.muted = false;\n        sound.volume = 0.7;\n        // Jouer et arrêter immédiatement pour \"débloquer\" l'audio\n        sound.play().then(() => {\n          sound.pause();\n          sound.currentTime = 0;\n        }).catch(() => {\n          // Ignorer les erreurs ici\n        });\n      }\n    });\n  }\n  ngOnDestroy() {\n    this.stopAllSounds();\n  }\n  static {\n    this.ɵfac = function CallService_Factory(t) {\n      return new (t || CallService)(i0.ɵɵinject(i1.Apollo), i0.ɵɵinject(i2.LoggerService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: CallService,\n      factory: CallService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["BehaviorSubject", "throwError", "from", "map", "catchError", "switchMap", "CallStatus", "INITIATE_CALL_MUTATION", "ACCEPT_CALL_MUTATION", "REJECT_CALL_MUTATION", "END_CALL_MUTATION", "TOGGLE_CALL_MEDIA_MUTATION", "INCOMING_CALL_SUBSCRIPTION", "CALL_STATUS_CHANGED_SUBSCRIPTION", "CallService", "constructor", "apollo", "logger", "activeCall", "incomingCall", "activeCall$", "asObservable", "incomingCall$", "sounds", "isPlaying", "muted", "isVideoEnabled", "isAudioEnabled", "preloadSounds", "initializeSubscriptions", "setTimeout", "subscribeToIncomingCalls", "subscribeToCallStatusChanges", "value", "console", "log", "createSyntheticSounds", "loadSound", "audioContext", "window", "AudioContext", "webkitAudioContext", "createRingtoneSound", "createConnectedSound", "createEndSound", "error", "warn", "audio", "Audio", "volume", "intervalId", "playSynthetic", "Promise", "resolve", "playMelody", "notes", "noteIndex", "playNote", "length", "oscillator", "createOscillator", "gainNode", "createGain", "connect", "destination", "frequency", "type", "gain", "setValueAtTime", "currentTime", "linearRampToValueAtTime", "exponentialRampToValueAtTime", "start", "stop", "stopSynthetic", "clearInterval", "frequencies", "for<PERSON>ach", "freq", "index", "startTime", "name", "path", "preload", "addEventListener", "e", "altPath", "startsWith", "substring", "src", "load", "play", "loop", "sound", "syntheticName", "playSimpleBeep", "then", "catch", "syntheticSound", "pause", "stopAllSounds", "subscribe", "query", "errorPolicy", "next", "data", "errors", "callId", "id", "callType", "caller", "username", "conversationId", "handleIncomingCall", "complete", "reinitializeSubscription", "testSounds", "testRingtone", "Object", "keys", "ringtone", "readyState", "duration", "callStatusChanged", "handleCallStatusChange", "call", "status", "REJECTED", "ENDED", "CONNECTED", "RINGING", "initiateCall", "recipientId", "Error", "Date", "now", "Math", "random", "toString", "substr", "offer", "JSON", "stringify", "sdp", "variables", "mutate", "mutation", "pipe", "result", "recipient", "errorMessage", "networkError", "graphQLErrors", "message", "acceptCall", "answer", "startMediaForCall", "rejectCall", "reason", "endCall", "feedback", "toggleMedia", "enableVideo", "enableAudio", "video", "toggleCallMedia", "_this", "_asyncToGenerator", "toggleVideo", "toggleAudio", "getVideoEnabled", "getAudioEnabled", "enableSounds", "values", "ngOnDestroy", "i0", "ɵɵinject", "i1", "Apollo", "i2", "LoggerService", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\services\\call.service.ts"], "sourcesContent": ["import { Injectable, <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';\nimport { Apollo } from 'apollo-angular';\nimport { BehaviorSubject, Observable, throwError, of, from } from 'rxjs';\nimport { map, catchError, switchMap } from 'rxjs/operators';\nimport {\n  Call,\n  CallType,\n  CallStatus,\n  IncomingCall,\n  CallSuccess,\n} from '../models/message.model';\nimport {\n  INITIATE_CALL_MUTATION,\n  ACCEPT_CALL_MUTATION,\n  REJECT_CALL_MUTATION,\n  END_CALL_MUTATION,\n  TOGGLE_CALL_MEDIA_MUTATION,\n  INCOMING_CALL_SUBSCRIPTION,\n  CALL_STATUS_CHANGED_SUBSCRIPTION,\n} from '../graphql/message.graphql';\nimport { LoggerService } from './logger.service';\n\n@Injectable({\n  providedIn: 'root',\n})\nexport class CallService implements OnDestroy {\n  // État des appels\n  private activeCall = new BehaviorSubject<Call | null>(null);\n  private incomingCall = new BehaviorSubject<IncomingCall | null>(null);\n\n  // Observables publics\n  public activeCall$ = this.activeCall.asObservable();\n  public incomingCall$ = this.incomingCall.asObservable();\n\n  // Propriétés pour la gestion des sons\n  private sounds: { [key: string]: HTMLAudioElement } = {};\n  private isPlaying: { [key: string]: boolean } = {};\n  private muted = false;\n\n  // États simples pour les médias\n  private isVideoEnabled = true;\n  private isAudioEnabled = true;\n\n  constructor(private apollo: Apollo, private logger: LoggerService) {\n    this.preloadSounds();\n    this.initializeSubscriptions();\n  }\n\n  /**\n   * Initialise les subscriptions avec un délai pour s'assurer que l'authentification est prête\n   */\n  private initializeSubscriptions(): void {\n    // Attendre un peu pour s'assurer que l'authentification est prête\n    setTimeout(() => {\n      this.subscribeToIncomingCalls();\n      this.subscribeToCallStatusChanges();\n    }, 1000);\n\n    // Réessayer après 5 secondes si la première tentative échoue\n    setTimeout(() => {\n      if (!this.incomingCall.value) {\n        console.log('🔄 [CallService] Retrying subscription initialization...');\n        this.subscribeToIncomingCalls();\n        this.subscribeToCallStatusChanges();\n      }\n    }, 5000);\n  }\n\n  /**\n   * Précharge les sons utilisés dans l'application\n   */\n  private preloadSounds(): void {\n    // Créer des sons synthétiques si les fichiers ne sont pas disponibles\n    this.createSyntheticSounds();\n\n    // Essayer de charger les vrais fichiers audio\n    this.loadSound('ringtone', 'assets/sounds/ringtone.mp3');\n    this.loadSound('call-end', 'assets/sounds/call-end.mp3');\n    this.loadSound('call-connected', 'assets/sounds/call-connected.mp3');\n  }\n\n  /**\n   * Crée des sons synthétiques comme fallback\n   */\n  private createSyntheticSounds(): void {\n    try {\n      // Créer un contexte audio pour les sons synthétiques\n      const audioContext = new (window.AudioContext ||\n        (window as any).webkitAudioContext)();\n\n      // Son de sonnerie (mélodie agréable)\n      this.sounds['ringtone-synthetic'] =\n        this.createRingtoneSound(audioContext);\n\n      // Son de connexion (accord agréable)\n      this.sounds['call-connected-synthetic'] =\n        this.createConnectedSound(audioContext);\n\n      // Son de fin d'appel (ton descendant)\n      this.sounds['call-end-synthetic'] = this.createEndSound(audioContext);\n\n      console.log('🔊 [CallService] Synthetic sounds created as fallback');\n    } catch (error) {\n      console.warn(\n        '⚠️ [CallService] Could not create synthetic sounds:',\n        error\n      );\n    }\n  }\n\n  /**\n   * Crée une sonnerie agréable (mélodie)\n   */\n  private createRingtoneSound(audioContext: AudioContext): HTMLAudioElement {\n    const audio = new Audio();\n    audio.volume = 0.4;\n\n    let isPlaying = false;\n    let intervalId: any = null;\n\n    (audio as any).playSynthetic = () => {\n      if (isPlaying) return Promise.resolve();\n\n      isPlaying = true;\n      const playMelody = () => {\n        if (!isPlaying) return;\n\n        // Mélodie agréable : Do-Mi-Sol-Do (523, 659, 784, 1047 Hz)\n        const notes = [523, 659, 784, 1047];\n        let noteIndex = 0;\n\n        const playNote = () => {\n          if (!isPlaying || noteIndex >= notes.length) {\n            noteIndex = 0;\n            setTimeout(playMelody, 500); // Pause entre les répétitions\n            return;\n          }\n\n          const oscillator = audioContext.createOscillator();\n          const gainNode = audioContext.createGain();\n\n          oscillator.connect(gainNode);\n          gainNode.connect(audioContext.destination);\n\n          oscillator.frequency.value = notes[noteIndex];\n          oscillator.type = 'sine';\n\n          gainNode.gain.setValueAtTime(0, audioContext.currentTime);\n          gainNode.gain.linearRampToValueAtTime(\n            0.3,\n            audioContext.currentTime + 0.05\n          );\n          gainNode.gain.exponentialRampToValueAtTime(\n            0.01,\n            audioContext.currentTime + 0.4\n          );\n\n          oscillator.start(audioContext.currentTime);\n          oscillator.stop(audioContext.currentTime + 0.4);\n\n          noteIndex++;\n          setTimeout(playNote, 200);\n        };\n\n        playNote();\n      };\n\n      playMelody();\n      return Promise.resolve();\n    };\n\n    (audio as any).stopSynthetic = () => {\n      isPlaying = false;\n      if (intervalId) {\n        clearInterval(intervalId);\n        intervalId = null;\n      }\n    };\n\n    return audio;\n  }\n\n  /**\n   * Crée un son de connexion agréable (accord)\n   */\n  private createConnectedSound(audioContext: AudioContext): HTMLAudioElement {\n    const audio = new Audio();\n    audio.volume = 0.4;\n\n    (audio as any).playSynthetic = () => {\n      // Accord majeur : Do-Mi-Sol (523, 659, 784 Hz)\n      const frequencies = [523, 659, 784];\n\n      frequencies.forEach((freq, index) => {\n        const oscillator = audioContext.createOscillator();\n        const gainNode = audioContext.createGain();\n\n        oscillator.connect(gainNode);\n        gainNode.connect(audioContext.destination);\n\n        oscillator.frequency.value = freq;\n        oscillator.type = 'sine';\n\n        const startTime = audioContext.currentTime + index * 0.1;\n        gainNode.gain.setValueAtTime(0, startTime);\n        gainNode.gain.linearRampToValueAtTime(0.2, startTime + 0.1);\n        gainNode.gain.exponentialRampToValueAtTime(0.01, startTime + 1.0);\n\n        oscillator.start(startTime);\n        oscillator.stop(startTime + 1.0);\n      });\n\n      return Promise.resolve();\n    };\n\n    return audio;\n  }\n\n  /**\n   * Crée un son de fin d'appel (ton descendant)\n   */\n  private createEndSound(audioContext: AudioContext): HTMLAudioElement {\n    const audio = new Audio();\n    audio.volume = 0.4;\n\n    (audio as any).playSynthetic = () => {\n      const oscillator = audioContext.createOscillator();\n      const gainNode = audioContext.createGain();\n\n      oscillator.connect(gainNode);\n      gainNode.connect(audioContext.destination);\n\n      // Ton descendant de 800Hz à 200Hz\n      oscillator.frequency.setValueAtTime(800, audioContext.currentTime);\n      oscillator.frequency.exponentialRampToValueAtTime(\n        200,\n        audioContext.currentTime + 1.0\n      );\n      oscillator.type = 'sine';\n\n      gainNode.gain.setValueAtTime(0, audioContext.currentTime);\n      gainNode.gain.linearRampToValueAtTime(\n        0.3,\n        audioContext.currentTime + 0.1\n      );\n      gainNode.gain.exponentialRampToValueAtTime(\n        0.01,\n        audioContext.currentTime + 1.0\n      );\n\n      oscillator.start(audioContext.currentTime);\n      oscillator.stop(audioContext.currentTime + 1.0);\n\n      return Promise.resolve();\n    };\n\n    return audio;\n  }\n\n  /**\n   * Charge un fichier audio\n   */\n  private loadSound(name: string, path: string): void {\n    try {\n      const audio = new Audio();\n\n      // Configuration de l'audio\n      audio.preload = 'auto';\n      audio.volume = 0.7;\n\n      // Gérer les événements de chargement\n      audio.addEventListener('canplaythrough', () => {\n        console.log(\n          `✅ [CallService] Sound ${name} loaded successfully from ${path}`\n        );\n      });\n\n      audio.addEventListener('error', (e) => {\n        console.error(\n          `❌ [CallService] Error loading sound ${name} from ${path}:`,\n          e\n        );\n        console.log(\n          `🔄 [CallService] Trying to load ${name} with different approach...`\n        );\n\n        // Essayer de charger avec un chemin relatif\n        const altPath = path.startsWith('/') ? path.substring(1) : path;\n        if (altPath !== path) {\n          setTimeout(() => {\n            audio.src = altPath;\n            audio.load();\n          }, 100);\n        }\n      });\n\n      audio.addEventListener('ended', () => {\n        this.isPlaying[name] = false;\n        console.log(`🔇 [CallService] Sound ${name} ended`);\n      });\n\n      // Définir la source et charger\n      audio.src = path;\n      audio.load();\n\n      this.sounds[name] = audio;\n      this.isPlaying[name] = false;\n\n      console.log(`🔊 [CallService] Loading sound ${name} from ${path}`);\n    } catch (error) {\n      console.error(\n        `❌ [CallService] Error creating audio element for ${name}:`,\n        error\n      );\n    }\n  }\n\n  /**\n   * Joue un son\n   */\n  private play(name: string, loop: boolean = false): void {\n    if (this.muted) {\n      console.log(`🔇 [CallService] Sound ${name} muted`);\n      return;\n    }\n\n    try {\n      let sound = this.sounds[name];\n\n      // Si le son principal n'est pas disponible, essayer la version synthétique\n      if (!sound || sound.error) {\n        const syntheticName = `${name}-synthetic`;\n        sound = this.sounds[syntheticName];\n        if (sound) {\n          console.log(`🔊 [CallService] Using synthetic sound for ${name}`);\n        }\n      }\n\n      if (!sound) {\n        console.warn(\n          `🔊 [CallService] Sound ${name} not found (neither original nor synthetic)`\n        );\n        // Créer un bip simple comme dernier recours\n        this.playSimpleBeep(\n          name === 'ringtone' ? 800 : name === 'call-connected' ? 1000 : 400\n        );\n        return;\n      }\n\n      sound.loop = loop;\n      sound.volume = 0.7;\n\n      if (!this.isPlaying[name]) {\n        console.log(`🔊 [CallService] Playing sound: ${name} (loop: ${loop})`);\n\n        // Vérifier si c'est un son synthétique\n        if ((sound as any).playSynthetic) {\n          (sound as any)\n            .playSynthetic()\n            .then(() => {\n              console.log(\n                `✅ [CallService] Synthetic sound ${name} started successfully`\n              );\n              this.isPlaying[name] = true;\n\n              // Pour la sonnerie synthétique, elle gère sa propre boucle\n              if (name === 'ringtone' && !loop) {\n                // Si ce n'est pas en boucle, arrêter après un certain temps\n                setTimeout(() => {\n                  this.isPlaying[name] = false;\n                }, 3000);\n              } else if (name !== 'ringtone') {\n                // Pour les autres sons, arrêter après leur durée\n                setTimeout(\n                  () => {\n                    this.isPlaying[name] = false;\n                  },\n                  name === 'call-connected' ? 1200 : 1000\n                );\n              }\n            })\n            .catch((error: any) => {\n              console.error(\n                `❌ [CallService] Error playing synthetic sound ${name}:`,\n                error\n              );\n            });\n        } else {\n          // Son normal\n          sound.currentTime = 0;\n          sound\n            .play()\n            .then(() => {\n              console.log(\n                `✅ [CallService] Sound ${name} started successfully`\n              );\n              this.isPlaying[name] = true;\n            })\n            .catch((error) => {\n              console.error(\n                `❌ [CallService] Error playing sound ${name}:`,\n                error\n              );\n\n              // Essayer le son synthétique en cas d'échec\n              const syntheticName = `${name}-synthetic`;\n              const syntheticSound = this.sounds[syntheticName];\n              if (syntheticSound && (syntheticSound as any).playSynthetic) {\n                console.log(\n                  `🔄 [CallService] Falling back to synthetic sound for ${name}`\n                );\n                this.play(name, loop);\n              } else {\n                // Dernier recours : bip simple\n                this.playSimpleBeep(\n                  name === 'ringtone'\n                    ? 800\n                    : name === 'call-connected'\n                    ? 1000\n                    : 400\n                );\n              }\n            });\n        }\n      } else {\n        console.log(`🔊 [CallService] Sound ${name} already playing`);\n      }\n    } catch (error) {\n      console.error(\n        `❌ [CallService] Error in play method for ${name}:`,\n        error\n      );\n      // Dernier recours\n      this.playSimpleBeep(\n        name === 'ringtone' ? 800 : name === 'call-connected' ? 1000 : 400\n      );\n    }\n  }\n\n  /**\n   * Joue un bip simple comme dernier recours\n   */\n  private playSimpleBeep(frequency: number): void {\n    try {\n      const audioContext = new (window.AudioContext ||\n        (window as any).webkitAudioContext)();\n      const oscillator = audioContext.createOscillator();\n      const gainNode = audioContext.createGain();\n\n      oscillator.connect(gainNode);\n      gainNode.connect(audioContext.destination);\n\n      oscillator.frequency.value = frequency;\n      oscillator.type = 'sine';\n\n      gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);\n      gainNode.gain.exponentialRampToValueAtTime(\n        0.01,\n        audioContext.currentTime + 0.3\n      );\n\n      oscillator.start(audioContext.currentTime);\n      oscillator.stop(audioContext.currentTime + 0.3);\n\n      console.log(`🔊 [CallService] Playing simple beep at ${frequency}Hz`);\n    } catch (error) {\n      console.error('❌ [CallService] Could not play simple beep:', error);\n    }\n  }\n\n  /**\n   * Arrête un son\n   */\n  private stop(name: string): void {\n    try {\n      let sound = this.sounds[name];\n\n      // Essayer aussi la version synthétique\n      if (!sound) {\n        sound = this.sounds[`${name}-synthetic`];\n      }\n\n      if (!sound) {\n        console.warn(`🔊 [CallService] Sound ${name} not found for stopping`);\n        return;\n      }\n\n      if (this.isPlaying[name]) {\n        console.log(`🔇 [CallService] Stopping sound: ${name}`);\n\n        // Arrêter le son synthétique si c'est le cas\n        if ((sound as any).stopSynthetic) {\n          (sound as any).stopSynthetic();\n        } else {\n          sound.pause();\n          sound.currentTime = 0;\n        }\n\n        this.isPlaying[name] = false;\n      } else {\n        console.log(`🔇 [CallService] Sound ${name} was not playing`);\n      }\n    } catch (error) {\n      console.error(`❌ [CallService] Error stopping sound ${name}:`, error);\n    }\n  }\n\n  /**\n   * Arrête tous les sons\n   */\n  private stopAllSounds(): void {\n    console.log('🔇 [CallService] Stopping all sounds');\n    this.stop('ringtone');\n    this.stop('call-connected');\n    this.stop('call-end');\n  }\n\n  /**\n   * S'abonne aux appels entrants\n   */\n  private subscribeToIncomingCalls(): void {\n    console.log('🔔 [CallService] Setting up incoming call subscription...');\n\n    try {\n      this.apollo\n        .subscribe<{ incomingCall: IncomingCall }>({\n          query: INCOMING_CALL_SUBSCRIPTION,\n          errorPolicy: 'all', // Continuer même en cas d'erreur\n        })\n        .subscribe({\n          next: ({ data, errors }) => {\n            if (errors) {\n              console.warn(\n                '⚠️ [CallService] GraphQL errors in subscription:',\n                errors\n              );\n            }\n\n            if (data?.incomingCall) {\n              console.log('📞 [CallService] Incoming call received:', {\n                callId: data.incomingCall.id,\n                callType: data.incomingCall.type,\n                caller: data.incomingCall.caller?.username,\n                conversationId: data.incomingCall.conversationId,\n              });\n              this.handleIncomingCall(data.incomingCall);\n            }\n          },\n          error: (error) => {\n            console.error(\n              '❌ [CallService] Error in incoming call subscription:',\n              error\n            );\n\n            // Réessayer après 5 secondes en cas d'erreur\n            setTimeout(() => {\n              console.log(\n                '🔄 [CallService] Retrying incoming call subscription...'\n              );\n              this.subscribeToIncomingCalls();\n            }, 5000);\n          },\n          complete: () => {\n            console.log(\n              '🔚 [CallService] Incoming call subscription completed'\n            );\n            // Réessayer si la subscription se ferme de manière inattendue\n            setTimeout(() => {\n              console.log(\n                '🔄 [CallService] Restarting subscription after completion...'\n              );\n              this.subscribeToIncomingCalls();\n            }, 2000);\n          },\n        });\n    } catch (error) {\n      console.error('❌ [CallService] Failed to create subscription:', error);\n      // Réessayer après 3 secondes\n      setTimeout(() => {\n        this.subscribeToIncomingCalls();\n      }, 3000);\n    }\n  }\n\n  /**\n   * Méthode publique pour forcer la réinitialisation de la subscription\n   */\n  public reinitializeSubscription(): void {\n    console.log('🔄 [CallService] Manually reinitializing subscription...');\n    this.subscribeToIncomingCalls();\n    this.subscribeToCallStatusChanges();\n  }\n\n  /**\n   * Méthode de test pour vérifier les sons\n   */\n  public testSounds(): void {\n    console.log('🧪 [CallService] Testing sounds...');\n\n    // Test de la sonnerie\n    console.log('🔊 Testing ringtone...');\n    this.play('ringtone', true);\n\n    setTimeout(() => {\n      this.stop('ringtone');\n      console.log('🔊 Testing call-connected...');\n      this.play('call-connected');\n    }, 3000);\n\n    setTimeout(() => {\n      console.log('🔊 Testing call-end...');\n      this.play('call-end');\n    }, 5000);\n  }\n\n  /**\n   * Test spécifique pour la sonnerie\n   */\n  public testRingtone(): void {\n    console.log('🧪 [CallService] Testing ringtone specifically...');\n    console.log('🔊 [CallService] Available sounds:', Object.keys(this.sounds));\n\n    // Vérifier si le son est chargé\n    const ringtone = this.sounds['ringtone'];\n    if (ringtone) {\n      console.log('✅ [CallService] Ringtone found:', {\n        src: ringtone.src,\n        readyState: ringtone.readyState,\n        error: ringtone.error,\n        duration: ringtone.duration,\n      });\n    } else {\n      console.log('❌ [CallService] Ringtone not found in sounds');\n    }\n\n    // Jouer la sonnerie\n    this.play('ringtone', true);\n\n    // Arrêter après 5 secondes\n    setTimeout(() => {\n      this.stop('ringtone');\n      console.log('🔇 [CallService] Ringtone test stopped');\n    }, 5000);\n  }\n\n  /**\n   * S'abonne aux changements de statut d'appel\n   */\n  private subscribeToCallStatusChanges(): void {\n    console.log(\n      '📞 [CallService] Setting up call status change subscription...'\n    );\n\n    try {\n      this.apollo\n        .subscribe<{ callStatusChanged: Call }>({\n          query: CALL_STATUS_CHANGED_SUBSCRIPTION,\n          errorPolicy: 'all',\n        })\n        .subscribe({\n          next: ({ data, errors }) => {\n            if (errors) {\n              console.warn(\n                '⚠️ [CallService] GraphQL errors in call status subscription:',\n                errors\n              );\n            }\n\n            if (data?.callStatusChanged) {\n              console.log(\n                '📞 [CallService] Call status changed:',\n                data.callStatusChanged\n              );\n              this.handleCallStatusChange(data.callStatusChanged);\n            }\n          },\n          error: (error) => {\n            console.error(\n              '❌ [CallService] Error in call status subscription:',\n              error\n            );\n            // Réessayer après 5 secondes\n            setTimeout(() => {\n              this.subscribeToCallStatusChanges();\n            }, 5000);\n          },\n          complete: () => {\n            console.log('🔚 [CallService] Call status subscription completed');\n            // Réessayer si la subscription se ferme\n            setTimeout(() => {\n              this.subscribeToCallStatusChanges();\n            }, 2000);\n          },\n        });\n    } catch (error) {\n      console.error(\n        '❌ [CallService] Failed to create call status subscription:',\n        error\n      );\n      setTimeout(() => {\n        this.subscribeToCallStatusChanges();\n      }, 3000);\n    }\n  }\n\n  /**\n   * Gère un appel entrant\n   */\n  private handleIncomingCall(call: IncomingCall): void {\n    console.log('🔔 [CallService] Handling incoming call:', {\n      callId: call.id,\n      callType: call.type,\n      caller: call.caller?.username,\n      conversationId: call.conversationId,\n    });\n\n    this.incomingCall.next(call);\n    this.play('ringtone', true);\n\n    console.log(\n      '🔊 [CallService] Ringtone started, call notification sent to UI'\n    );\n  }\n\n  /**\n   * Gère les changements de statut d'appel\n   */\n  private handleCallStatusChange(call: Call): void {\n    console.log('📞 [CallService] Call status changed:', call.status);\n\n    switch (call.status) {\n      case CallStatus.REJECTED:\n        console.log('❌ [CallService] Call was rejected');\n        this.stop('ringtone');\n        this.play('call-end');\n        this.activeCall.next(null);\n        this.incomingCall.next(null);\n        break;\n\n      case CallStatus.ENDED:\n        console.log('📞 [CallService] Call ended');\n        this.stopAllSounds();\n        this.play('call-end');\n        this.activeCall.next(null);\n        this.incomingCall.next(null);\n        break;\n\n      case CallStatus.CONNECTED:\n        console.log('✅ [CallService] Call connected');\n        this.stop('ringtone');\n        this.play('call-connected');\n        this.activeCall.next(call);\n        this.incomingCall.next(null);\n        break;\n\n      case CallStatus.RINGING:\n        console.log('📞 [CallService] Call is ringing');\n        this.play('ringtone', true);\n        break;\n\n      default:\n        console.log('📞 [CallService] Unknown call status:', call.status);\n        break;\n    }\n  }\n\n  /**\n   * Initie un appel WebRTC\n   */\n  initiateCall(\n    recipientId: string,\n    callType: CallType,\n    conversationId?: string\n  ): Observable<Call> {\n    console.log('🔄 [CallService] Initiating call:', {\n      recipientId,\n      callType,\n      conversationId,\n    });\n\n    if (!recipientId) {\n      const error = new Error('Recipient ID is required');\n      console.error('❌ [CallService] initiateCall error:', error);\n      return throwError(() => error);\n    }\n\n    // Générer un ID unique pour l'appel\n    const callId = `call_${Date.now()}_${Math.random()\n      .toString(36)\n      .substr(2, 9)}`;\n\n    // Pour l'instant, utiliser une offre WebRTC factice\n    const offer = JSON.stringify({\n      type: 'offer',\n      sdp: 'v=0\\r\\no=- 0 0 IN IP4 127.0.0.1\\r\\ns=-\\r\\nt=0 0\\r\\n',\n    });\n\n    const variables = {\n      recipientId,\n      callType: callType,\n      callId,\n      offer,\n      conversationId,\n    };\n\n    console.log('📤 [CallService] Sending initiate call mutation:', variables);\n\n    return this.apollo\n      .mutate<{ initiateCall: Call }>({\n        mutation: INITIATE_CALL_MUTATION,\n        variables,\n      })\n      .pipe(\n        map((result) => {\n          console.log('✅ [CallService] Call initiated successfully:', result);\n\n          if (!result.data?.initiateCall) {\n            throw new Error('No call data received from server');\n          }\n\n          const call = result.data.initiateCall;\n          console.log('📞 [CallService] Call details:', {\n            id: call.id,\n            type: call.type,\n            status: call.status,\n            caller: call.caller?.username,\n            recipient: call.recipient?.username,\n          });\n\n          // Mettre à jour l'état local\n          this.activeCall.next(call);\n\n          return call;\n        }),\n        catchError((error) => {\n          console.error('❌ [CallService] initiateCall error:', error);\n          this.logger.error('Error initiating call:', error);\n\n          let errorMessage = \"Erreur lors de l'initiation de l'appel\";\n          if (error.networkError) {\n            errorMessage = 'Erreur de connexion réseau';\n          } else if (error.graphQLErrors?.length > 0) {\n            errorMessage = error.graphQLErrors[0].message || errorMessage;\n          }\n\n          return throwError(() => new Error(errorMessage));\n        })\n      );\n  }\n\n  /**\n   * Accepte un appel entrant\n   */\n  acceptCall(incomingCall: IncomingCall): Observable<Call> {\n    console.log('🔄 [CallService] Accepting call:', incomingCall.id);\n\n    // Générer une réponse WebRTC factice\n    const answer = JSON.stringify({\n      type: 'answer',\n      sdp: 'v=0\\r\\no=- 0 0 IN IP4 127.0.0.1\\r\\ns=-\\r\\nt=0 0\\r\\n',\n    });\n\n    return this.apollo\n      .mutate<{ acceptCall: Call }>({\n        mutation: ACCEPT_CALL_MUTATION,\n        variables: {\n          callId: incomingCall.id,\n          answer,\n        },\n      })\n      .pipe(\n        switchMap((result) => {\n          console.log('✅ [CallService] Call accepted successfully:', result);\n\n          if (!result.data?.acceptCall) {\n            throw new Error('No call data received from server');\n          }\n\n          const call = result.data.acceptCall;\n\n          // Arrêter la sonnerie\n          this.stop('ringtone');\n          this.play('call-connected');\n\n          // Démarrer les médias pour l'appel de manière asynchrone\n          return from(this.startMediaForCall(incomingCall, call));\n        }),\n        catchError((error) => {\n          console.error('❌ [CallService] acceptCall error:', error);\n          this.logger.error('Error accepting call:', error);\n          return throwError(\n            () => new Error(\"Erreur lors de l'acceptation de l'appel\")\n          );\n        })\n      );\n  }\n\n  /**\n   * Rejette un appel entrant\n   */\n  rejectCall(callId: string, reason?: string): Observable<CallSuccess> {\n    console.log('🔄 [CallService] Rejecting call:', callId, reason);\n\n    return this.apollo\n      .mutate<{ rejectCall: CallSuccess }>({\n        mutation: REJECT_CALL_MUTATION,\n        variables: {\n          callId,\n          reason: reason || 'User rejected',\n        },\n      })\n      .pipe(\n        map((result) => {\n          console.log('✅ [CallService] Call rejected successfully:', result);\n\n          if (!result.data?.rejectCall) {\n            throw new Error('No response received from server');\n          }\n\n          // Mettre à jour l'état local\n          this.incomingCall.next(null);\n          this.activeCall.next(null);\n\n          // Arrêter la sonnerie\n          this.stop('ringtone');\n\n          return result.data.rejectCall;\n        }),\n        catchError((error) => {\n          console.error('❌ [CallService] rejectCall error:', error);\n          this.logger.error('Error rejecting call:', error);\n          return throwError(() => new Error(\"Erreur lors du rejet de l'appel\"));\n        })\n      );\n  }\n\n  /**\n   * Termine un appel en cours\n   */\n  endCall(callId: string): Observable<CallSuccess> {\n    console.log('🔄 [CallService] Ending call:', callId);\n\n    return this.apollo\n      .mutate<{ endCall: CallSuccess }>({\n        mutation: END_CALL_MUTATION,\n        variables: {\n          callId,\n          feedback: null, // Pas de feedback pour l'instant\n        },\n      })\n      .pipe(\n        map((result) => {\n          console.log('✅ [CallService] Call ended successfully:', result);\n\n          if (!result.data?.endCall) {\n            throw new Error('No response received from server');\n          }\n\n          // Mettre à jour l'état local\n          this.activeCall.next(null);\n          this.incomingCall.next(null);\n\n          // Arrêter tous les sons\n          this.stopAllSounds();\n          this.play('call-end');\n\n          return result.data.endCall;\n        }),\n        catchError((error) => {\n          console.error('❌ [CallService] endCall error:', error);\n          this.logger.error('Error ending call:', error);\n          return throwError(\n            () => new Error(\"Erreur lors de la fin de l'appel\")\n          );\n        })\n      );\n  }\n\n  /**\n   * Bascule l'état des médias (audio/vidéo) pendant un appel\n   */\n  toggleMedia(\n    callId: string,\n    enableVideo?: boolean,\n    enableAudio?: boolean\n  ): Observable<CallSuccess> {\n    console.log('🔄 [CallService] Toggling media:', {\n      callId,\n      enableVideo,\n      enableAudio,\n    });\n\n    return this.apollo\n      .mutate<{ toggleCallMedia: CallSuccess }>({\n        mutation: TOGGLE_CALL_MEDIA_MUTATION,\n        variables: {\n          callId,\n          video: enableVideo,\n          audio: enableAudio,\n        },\n      })\n      .pipe(\n        map((result) => {\n          console.log('✅ [CallService] Media toggled successfully:', result);\n\n          if (!result.data?.toggleCallMedia) {\n            throw new Error('No response received from server');\n          }\n\n          return result.data.toggleCallMedia;\n        }),\n        catchError((error) => {\n          console.error('❌ [CallService] toggleMedia error:', error);\n          this.logger.error('Error toggling media:', error);\n          return throwError(\n            () => new Error('Erreur lors du changement des médias')\n          );\n        })\n      );\n  }\n\n  /**\n   * Démarre les médias pour un appel accepté\n   */\n  private async startMediaForCall(\n    incomingCall: IncomingCall,\n    call: Call\n  ): Promise<Call> {\n    console.log('🎥 [CallService] Call connected - playing connection sound');\n\n    // Jouer le son de connexion\n    this.play('call-connected');\n\n    // Mettre à jour l'état local\n    this.activeCall.next(call);\n    this.incomingCall.next(null); // Supprimer l'appel entrant\n\n    return call;\n  }\n\n  /**\n   * Active/désactive la vidéo\n   */\n  toggleVideo(): boolean {\n    this.isVideoEnabled = !this.isVideoEnabled;\n    console.log('📹 [CallService] Video toggled:', this.isVideoEnabled);\n    return this.isVideoEnabled;\n  }\n\n  /**\n   * Active/désactive l'audio\n   */\n  toggleAudio(): boolean {\n    this.isAudioEnabled = !this.isAudioEnabled;\n    console.log('🎤 [CallService] Audio toggled:', this.isAudioEnabled);\n    return this.isAudioEnabled;\n  }\n\n  /**\n   * Obtient l'état de la vidéo\n   */\n  getVideoEnabled(): boolean {\n    return this.isVideoEnabled;\n  }\n\n  /**\n   * Obtient l'état de l'audio\n   */\n  getAudioEnabled(): boolean {\n    return this.isAudioEnabled;\n  }\n\n  /**\n   * Arrête tous les sons\n   */\n  private stopAllSounds(): void {\n    console.log('🔇 [CallService] Stopping all sounds');\n    Object.keys(this.sounds).forEach((name) => {\n      this.stop(name);\n    });\n  }\n\n  /**\n   * Active les sons après interaction utilisateur (pour contourner les restrictions du navigateur)\n   */\n  public enableSounds(): void {\n    console.log('🔊 [CallService] Enabling sounds after user interaction');\n    Object.values(this.sounds).forEach((sound) => {\n      if (sound) {\n        sound.muted = false;\n        sound.volume = 0.7;\n        // Jouer et arrêter immédiatement pour \"débloquer\" l'audio\n        sound\n          .play()\n          .then(() => {\n            sound.pause();\n            sound.currentTime = 0;\n          })\n          .catch(() => {\n            // Ignorer les erreurs ici\n          });\n      }\n    });\n  }\n\n  ngOnDestroy(): void {\n    this.stopAllSounds();\n  }\n}\n"], "mappings": ";AAEA,SAASA,eAAe,EAAcC,UAAU,EAAMC,IAAI,QAAQ,MAAM;AACxE,SAASC,GAAG,EAAEC,UAAU,EAAEC,SAAS,QAAQ,gBAAgB;AAC3D,SAGEC,UAAU,QAGL,yBAAyB;AAChC,SACEC,sBAAsB,EACtBC,oBAAoB,EACpBC,oBAAoB,EACpBC,iBAAiB,EACjBC,0BAA0B,EAC1BC,0BAA0B,EAC1BC,gCAAgC,QAC3B,4BAA4B;;;;AAMnC,OAAM,MAAOC,WAAW;EAkBtBC,YAAoBC,MAAc,EAAUC,MAAqB;IAA7C,KAAAD,MAAM,GAANA,MAAM;IAAkB,KAAAC,MAAM,GAANA,MAAM;IAjBlD;IACQ,KAAAC,UAAU,GAAG,IAAIlB,eAAe,CAAc,IAAI,CAAC;IACnD,KAAAmB,YAAY,GAAG,IAAInB,eAAe,CAAsB,IAAI,CAAC;IAErE;IACO,KAAAoB,WAAW,GAAG,IAAI,CAACF,UAAU,CAACG,YAAY,EAAE;IAC5C,KAAAC,aAAa,GAAG,IAAI,CAACH,YAAY,CAACE,YAAY,EAAE;IAEvD;IACQ,KAAAE,MAAM,GAAwC,EAAE;IAChD,KAAAC,SAAS,GAA+B,EAAE;IAC1C,KAAAC,KAAK,GAAG,KAAK;IAErB;IACQ,KAAAC,cAAc,GAAG,IAAI;IACrB,KAAAC,cAAc,GAAG,IAAI;IAG3B,IAAI,CAACC,aAAa,EAAE;IACpB,IAAI,CAACC,uBAAuB,EAAE;EAChC;EAEA;;;EAGQA,uBAAuBA,CAAA;IAC7B;IACAC,UAAU,CAAC,MAAK;MACd,IAAI,CAACC,wBAAwB,EAAE;MAC/B,IAAI,CAACC,4BAA4B,EAAE;IACrC,CAAC,EAAE,IAAI,CAAC;IAER;IACAF,UAAU,CAAC,MAAK;MACd,IAAI,CAAC,IAAI,CAACX,YAAY,CAACc,KAAK,EAAE;QAC5BC,OAAO,CAACC,GAAG,CAAC,0DAA0D,CAAC;QACvE,IAAI,CAACJ,wBAAwB,EAAE;QAC/B,IAAI,CAACC,4BAA4B,EAAE;;IAEvC,CAAC,EAAE,IAAI,CAAC;EACV;EAEA;;;EAGQJ,aAAaA,CAAA;IACnB;IACA,IAAI,CAACQ,qBAAqB,EAAE;IAE5B;IACA,IAAI,CAACC,SAAS,CAAC,UAAU,EAAE,4BAA4B,CAAC;IACxD,IAAI,CAACA,SAAS,CAAC,UAAU,EAAE,4BAA4B,CAAC;IACxD,IAAI,CAACA,SAAS,CAAC,gBAAgB,EAAE,kCAAkC,CAAC;EACtE;EAEA;;;EAGQD,qBAAqBA,CAAA;IAC3B,IAAI;MACF;MACA,MAAME,YAAY,GAAG,KAAKC,MAAM,CAACC,YAAY,IAC1CD,MAAc,CAACE,kBAAkB,EAAC,CAAE;MAEvC;MACA,IAAI,CAAClB,MAAM,CAAC,oBAAoB,CAAC,GAC/B,IAAI,CAACmB,mBAAmB,CAACJ,YAAY,CAAC;MAExC;MACA,IAAI,CAACf,MAAM,CAAC,0BAA0B,CAAC,GACrC,IAAI,CAACoB,oBAAoB,CAACL,YAAY,CAAC;MAEzC;MACA,IAAI,CAACf,MAAM,CAAC,oBAAoB,CAAC,GAAG,IAAI,CAACqB,cAAc,CAACN,YAAY,CAAC;MAErEJ,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;KACrE,CAAC,OAAOU,KAAK,EAAE;MACdX,OAAO,CAACY,IAAI,CACV,qDAAqD,EACrDD,KAAK,CACN;;EAEL;EAEA;;;EAGQH,mBAAmBA,CAACJ,YAA0B;IACpD,MAAMS,KAAK,GAAG,IAAIC,KAAK,EAAE;IACzBD,KAAK,CAACE,MAAM,GAAG,GAAG;IAElB,IAAIzB,SAAS,GAAG,KAAK;IACrB,IAAI0B,UAAU,GAAQ,IAAI;IAEzBH,KAAa,CAACI,aAAa,GAAG,MAAK;MAClC,IAAI3B,SAAS,EAAE,OAAO4B,OAAO,CAACC,OAAO,EAAE;MAEvC7B,SAAS,GAAG,IAAI;MAChB,MAAM8B,UAAU,GAAGA,CAAA,KAAK;QACtB,IAAI,CAAC9B,SAAS,EAAE;QAEhB;QACA,MAAM+B,KAAK,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC;QACnC,IAAIC,SAAS,GAAG,CAAC;QAEjB,MAAMC,QAAQ,GAAGA,CAAA,KAAK;UACpB,IAAI,CAACjC,SAAS,IAAIgC,SAAS,IAAID,KAAK,CAACG,MAAM,EAAE;YAC3CF,SAAS,GAAG,CAAC;YACb1B,UAAU,CAACwB,UAAU,EAAE,GAAG,CAAC,CAAC,CAAC;YAC7B;;UAGF,MAAMK,UAAU,GAAGrB,YAAY,CAACsB,gBAAgB,EAAE;UAClD,MAAMC,QAAQ,GAAGvB,YAAY,CAACwB,UAAU,EAAE;UAE1CH,UAAU,CAACI,OAAO,CAACF,QAAQ,CAAC;UAC5BA,QAAQ,CAACE,OAAO,CAACzB,YAAY,CAAC0B,WAAW,CAAC;UAE1CL,UAAU,CAACM,SAAS,CAAChC,KAAK,GAAGsB,KAAK,CAACC,SAAS,CAAC;UAC7CG,UAAU,CAACO,IAAI,GAAG,MAAM;UAExBL,QAAQ,CAACM,IAAI,CAACC,cAAc,CAAC,CAAC,EAAE9B,YAAY,CAAC+B,WAAW,CAAC;UACzDR,QAAQ,CAACM,IAAI,CAACG,uBAAuB,CACnC,GAAG,EACHhC,YAAY,CAAC+B,WAAW,GAAG,IAAI,CAChC;UACDR,QAAQ,CAACM,IAAI,CAACI,4BAA4B,CACxC,IAAI,EACJjC,YAAY,CAAC+B,WAAW,GAAG,GAAG,CAC/B;UAEDV,UAAU,CAACa,KAAK,CAAClC,YAAY,CAAC+B,WAAW,CAAC;UAC1CV,UAAU,CAACc,IAAI,CAACnC,YAAY,CAAC+B,WAAW,GAAG,GAAG,CAAC;UAE/Cb,SAAS,EAAE;UACX1B,UAAU,CAAC2B,QAAQ,EAAE,GAAG,CAAC;QAC3B,CAAC;QAEDA,QAAQ,EAAE;MACZ,CAAC;MAEDH,UAAU,EAAE;MACZ,OAAOF,OAAO,CAACC,OAAO,EAAE;IAC1B,CAAC;IAEAN,KAAa,CAAC2B,aAAa,GAAG,MAAK;MAClClD,SAAS,GAAG,KAAK;MACjB,IAAI0B,UAAU,EAAE;QACdyB,aAAa,CAACzB,UAAU,CAAC;QACzBA,UAAU,GAAG,IAAI;;IAErB,CAAC;IAED,OAAOH,KAAK;EACd;EAEA;;;EAGQJ,oBAAoBA,CAACL,YAA0B;IACrD,MAAMS,KAAK,GAAG,IAAIC,KAAK,EAAE;IACzBD,KAAK,CAACE,MAAM,GAAG,GAAG;IAEjBF,KAAa,CAACI,aAAa,GAAG,MAAK;MAClC;MACA,MAAMyB,WAAW,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;MAEnCA,WAAW,CAACC,OAAO,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAI;QAClC,MAAMpB,UAAU,GAAGrB,YAAY,CAACsB,gBAAgB,EAAE;QAClD,MAAMC,QAAQ,GAAGvB,YAAY,CAACwB,UAAU,EAAE;QAE1CH,UAAU,CAACI,OAAO,CAACF,QAAQ,CAAC;QAC5BA,QAAQ,CAACE,OAAO,CAACzB,YAAY,CAAC0B,WAAW,CAAC;QAE1CL,UAAU,CAACM,SAAS,CAAChC,KAAK,GAAG6C,IAAI;QACjCnB,UAAU,CAACO,IAAI,GAAG,MAAM;QAExB,MAAMc,SAAS,GAAG1C,YAAY,CAAC+B,WAAW,GAAGU,KAAK,GAAG,GAAG;QACxDlB,QAAQ,CAACM,IAAI,CAACC,cAAc,CAAC,CAAC,EAAEY,SAAS,CAAC;QAC1CnB,QAAQ,CAACM,IAAI,CAACG,uBAAuB,CAAC,GAAG,EAAEU,SAAS,GAAG,GAAG,CAAC;QAC3DnB,QAAQ,CAACM,IAAI,CAACI,4BAA4B,CAAC,IAAI,EAAES,SAAS,GAAG,GAAG,CAAC;QAEjErB,UAAU,CAACa,KAAK,CAACQ,SAAS,CAAC;QAC3BrB,UAAU,CAACc,IAAI,CAACO,SAAS,GAAG,GAAG,CAAC;MAClC,CAAC,CAAC;MAEF,OAAO5B,OAAO,CAACC,OAAO,EAAE;IAC1B,CAAC;IAED,OAAON,KAAK;EACd;EAEA;;;EAGQH,cAAcA,CAACN,YAA0B;IAC/C,MAAMS,KAAK,GAAG,IAAIC,KAAK,EAAE;IACzBD,KAAK,CAACE,MAAM,GAAG,GAAG;IAEjBF,KAAa,CAACI,aAAa,GAAG,MAAK;MAClC,MAAMQ,UAAU,GAAGrB,YAAY,CAACsB,gBAAgB,EAAE;MAClD,MAAMC,QAAQ,GAAGvB,YAAY,CAACwB,UAAU,EAAE;MAE1CH,UAAU,CAACI,OAAO,CAACF,QAAQ,CAAC;MAC5BA,QAAQ,CAACE,OAAO,CAACzB,YAAY,CAAC0B,WAAW,CAAC;MAE1C;MACAL,UAAU,CAACM,SAAS,CAACG,cAAc,CAAC,GAAG,EAAE9B,YAAY,CAAC+B,WAAW,CAAC;MAClEV,UAAU,CAACM,SAAS,CAACM,4BAA4B,CAC/C,GAAG,EACHjC,YAAY,CAAC+B,WAAW,GAAG,GAAG,CAC/B;MACDV,UAAU,CAACO,IAAI,GAAG,MAAM;MAExBL,QAAQ,CAACM,IAAI,CAACC,cAAc,CAAC,CAAC,EAAE9B,YAAY,CAAC+B,WAAW,CAAC;MACzDR,QAAQ,CAACM,IAAI,CAACG,uBAAuB,CACnC,GAAG,EACHhC,YAAY,CAAC+B,WAAW,GAAG,GAAG,CAC/B;MACDR,QAAQ,CAACM,IAAI,CAACI,4BAA4B,CACxC,IAAI,EACJjC,YAAY,CAAC+B,WAAW,GAAG,GAAG,CAC/B;MAEDV,UAAU,CAACa,KAAK,CAAClC,YAAY,CAAC+B,WAAW,CAAC;MAC1CV,UAAU,CAACc,IAAI,CAACnC,YAAY,CAAC+B,WAAW,GAAG,GAAG,CAAC;MAE/C,OAAOjB,OAAO,CAACC,OAAO,EAAE;IAC1B,CAAC;IAED,OAAON,KAAK;EACd;EAEA;;;EAGQV,SAASA,CAAC4C,IAAY,EAAEC,IAAY;IAC1C,IAAI;MACF,MAAMnC,KAAK,GAAG,IAAIC,KAAK,EAAE;MAEzB;MACAD,KAAK,CAACoC,OAAO,GAAG,MAAM;MACtBpC,KAAK,CAACE,MAAM,GAAG,GAAG;MAElB;MACAF,KAAK,CAACqC,gBAAgB,CAAC,gBAAgB,EAAE,MAAK;QAC5ClD,OAAO,CAACC,GAAG,CACT,yBAAyB8C,IAAI,6BAA6BC,IAAI,EAAE,CACjE;MACH,CAAC,CAAC;MAEFnC,KAAK,CAACqC,gBAAgB,CAAC,OAAO,EAAGC,CAAC,IAAI;QACpCnD,OAAO,CAACW,KAAK,CACX,uCAAuCoC,IAAI,SAASC,IAAI,GAAG,EAC3DG,CAAC,CACF;QACDnD,OAAO,CAACC,GAAG,CACT,mCAAmC8C,IAAI,6BAA6B,CACrE;QAED;QACA,MAAMK,OAAO,GAAGJ,IAAI,CAACK,UAAU,CAAC,GAAG,CAAC,GAAGL,IAAI,CAACM,SAAS,CAAC,CAAC,CAAC,GAAGN,IAAI;QAC/D,IAAII,OAAO,KAAKJ,IAAI,EAAE;UACpBpD,UAAU,CAAC,MAAK;YACdiB,KAAK,CAAC0C,GAAG,GAAGH,OAAO;YACnBvC,KAAK,CAAC2C,IAAI,EAAE;UACd,CAAC,EAAE,GAAG,CAAC;;MAEX,CAAC,CAAC;MAEF3C,KAAK,CAACqC,gBAAgB,CAAC,OAAO,EAAE,MAAK;QACnC,IAAI,CAAC5D,SAAS,CAACyD,IAAI,CAAC,GAAG,KAAK;QAC5B/C,OAAO,CAACC,GAAG,CAAC,0BAA0B8C,IAAI,QAAQ,CAAC;MACrD,CAAC,CAAC;MAEF;MACAlC,KAAK,CAAC0C,GAAG,GAAGP,IAAI;MAChBnC,KAAK,CAAC2C,IAAI,EAAE;MAEZ,IAAI,CAACnE,MAAM,CAAC0D,IAAI,CAAC,GAAGlC,KAAK;MACzB,IAAI,CAACvB,SAAS,CAACyD,IAAI,CAAC,GAAG,KAAK;MAE5B/C,OAAO,CAACC,GAAG,CAAC,kCAAkC8C,IAAI,SAASC,IAAI,EAAE,CAAC;KACnE,CAAC,OAAOrC,KAAK,EAAE;MACdX,OAAO,CAACW,KAAK,CACX,oDAAoDoC,IAAI,GAAG,EAC3DpC,KAAK,CACN;;EAEL;EAEA;;;EAGQ8C,IAAIA,CAACV,IAAY,EAAEW,IAAA,GAAgB,KAAK;IAC9C,IAAI,IAAI,CAACnE,KAAK,EAAE;MACdS,OAAO,CAACC,GAAG,CAAC,0BAA0B8C,IAAI,QAAQ,CAAC;MACnD;;IAGF,IAAI;MACF,IAAIY,KAAK,GAAG,IAAI,CAACtE,MAAM,CAAC0D,IAAI,CAAC;MAE7B;MACA,IAAI,CAACY,KAAK,IAAIA,KAAK,CAAChD,KAAK,EAAE;QACzB,MAAMiD,aAAa,GAAG,GAAGb,IAAI,YAAY;QACzCY,KAAK,GAAG,IAAI,CAACtE,MAAM,CAACuE,aAAa,CAAC;QAClC,IAAID,KAAK,EAAE;UACT3D,OAAO,CAACC,GAAG,CAAC,8CAA8C8C,IAAI,EAAE,CAAC;;;MAIrE,IAAI,CAACY,KAAK,EAAE;QACV3D,OAAO,CAACY,IAAI,CACV,0BAA0BmC,IAAI,6CAA6C,CAC5E;QACD;QACA,IAAI,CAACc,cAAc,CACjBd,IAAI,KAAK,UAAU,GAAG,GAAG,GAAGA,IAAI,KAAK,gBAAgB,GAAG,IAAI,GAAG,GAAG,CACnE;QACD;;MAGFY,KAAK,CAACD,IAAI,GAAGA,IAAI;MACjBC,KAAK,CAAC5C,MAAM,GAAG,GAAG;MAElB,IAAI,CAAC,IAAI,CAACzB,SAAS,CAACyD,IAAI,CAAC,EAAE;QACzB/C,OAAO,CAACC,GAAG,CAAC,mCAAmC8C,IAAI,WAAWW,IAAI,GAAG,CAAC;QAEtE;QACA,IAAKC,KAAa,CAAC1C,aAAa,EAAE;UAC/B0C,KAAa,CACX1C,aAAa,EAAE,CACf6C,IAAI,CAAC,MAAK;YACT9D,OAAO,CAACC,GAAG,CACT,mCAAmC8C,IAAI,uBAAuB,CAC/D;YACD,IAAI,CAACzD,SAAS,CAACyD,IAAI,CAAC,GAAG,IAAI;YAE3B;YACA,IAAIA,IAAI,KAAK,UAAU,IAAI,CAACW,IAAI,EAAE;cAChC;cACA9D,UAAU,CAAC,MAAK;gBACd,IAAI,CAACN,SAAS,CAACyD,IAAI,CAAC,GAAG,KAAK;cAC9B,CAAC,EAAE,IAAI,CAAC;aACT,MAAM,IAAIA,IAAI,KAAK,UAAU,EAAE;cAC9B;cACAnD,UAAU,CACR,MAAK;gBACH,IAAI,CAACN,SAAS,CAACyD,IAAI,CAAC,GAAG,KAAK;cAC9B,CAAC,EACDA,IAAI,KAAK,gBAAgB,GAAG,IAAI,GAAG,IAAI,CACxC;;UAEL,CAAC,CAAC,CACDgB,KAAK,CAAEpD,KAAU,IAAI;YACpBX,OAAO,CAACW,KAAK,CACX,iDAAiDoC,IAAI,GAAG,EACxDpC,KAAK,CACN;UACH,CAAC,CAAC;SACL,MAAM;UACL;UACAgD,KAAK,CAACxB,WAAW,GAAG,CAAC;UACrBwB,KAAK,CACFF,IAAI,EAAE,CACNK,IAAI,CAAC,MAAK;YACT9D,OAAO,CAACC,GAAG,CACT,yBAAyB8C,IAAI,uBAAuB,CACrD;YACD,IAAI,CAACzD,SAAS,CAACyD,IAAI,CAAC,GAAG,IAAI;UAC7B,CAAC,CAAC,CACDgB,KAAK,CAAEpD,KAAK,IAAI;YACfX,OAAO,CAACW,KAAK,CACX,uCAAuCoC,IAAI,GAAG,EAC9CpC,KAAK,CACN;YAED;YACA,MAAMiD,aAAa,GAAG,GAAGb,IAAI,YAAY;YACzC,MAAMiB,cAAc,GAAG,IAAI,CAAC3E,MAAM,CAACuE,aAAa,CAAC;YACjD,IAAII,cAAc,IAAKA,cAAsB,CAAC/C,aAAa,EAAE;cAC3DjB,OAAO,CAACC,GAAG,CACT,wDAAwD8C,IAAI,EAAE,CAC/D;cACD,IAAI,CAACU,IAAI,CAACV,IAAI,EAAEW,IAAI,CAAC;aACtB,MAAM;cACL;cACA,IAAI,CAACG,cAAc,CACjBd,IAAI,KAAK,UAAU,GACf,GAAG,GACHA,IAAI,KAAK,gBAAgB,GACzB,IAAI,GACJ,GAAG,CACR;;UAEL,CAAC,CAAC;;OAEP,MAAM;QACL/C,OAAO,CAACC,GAAG,CAAC,0BAA0B8C,IAAI,kBAAkB,CAAC;;KAEhE,CAAC,OAAOpC,KAAK,EAAE;MACdX,OAAO,CAACW,KAAK,CACX,4CAA4CoC,IAAI,GAAG,EACnDpC,KAAK,CACN;MACD;MACA,IAAI,CAACkD,cAAc,CACjBd,IAAI,KAAK,UAAU,GAAG,GAAG,GAAGA,IAAI,KAAK,gBAAgB,GAAG,IAAI,GAAG,GAAG,CACnE;;EAEL;EAEA;;;EAGQc,cAAcA,CAAC9B,SAAiB;IACtC,IAAI;MACF,MAAM3B,YAAY,GAAG,KAAKC,MAAM,CAACC,YAAY,IAC1CD,MAAc,CAACE,kBAAkB,EAAC,CAAE;MACvC,MAAMkB,UAAU,GAAGrB,YAAY,CAACsB,gBAAgB,EAAE;MAClD,MAAMC,QAAQ,GAAGvB,YAAY,CAACwB,UAAU,EAAE;MAE1CH,UAAU,CAACI,OAAO,CAACF,QAAQ,CAAC;MAC5BA,QAAQ,CAACE,OAAO,CAACzB,YAAY,CAAC0B,WAAW,CAAC;MAE1CL,UAAU,CAACM,SAAS,CAAChC,KAAK,GAAGgC,SAAS;MACtCN,UAAU,CAACO,IAAI,GAAG,MAAM;MAExBL,QAAQ,CAACM,IAAI,CAACC,cAAc,CAAC,GAAG,EAAE9B,YAAY,CAAC+B,WAAW,CAAC;MAC3DR,QAAQ,CAACM,IAAI,CAACI,4BAA4B,CACxC,IAAI,EACJjC,YAAY,CAAC+B,WAAW,GAAG,GAAG,CAC/B;MAEDV,UAAU,CAACa,KAAK,CAAClC,YAAY,CAAC+B,WAAW,CAAC;MAC1CV,UAAU,CAACc,IAAI,CAACnC,YAAY,CAAC+B,WAAW,GAAG,GAAG,CAAC;MAE/CnC,OAAO,CAACC,GAAG,CAAC,2CAA2C8B,SAAS,IAAI,CAAC;KACtE,CAAC,OAAOpB,KAAK,EAAE;MACdX,OAAO,CAACW,KAAK,CAAC,6CAA6C,EAAEA,KAAK,CAAC;;EAEvE;EAEA;;;EAGQ4B,IAAIA,CAACQ,IAAY;IACvB,IAAI;MACF,IAAIY,KAAK,GAAG,IAAI,CAACtE,MAAM,CAAC0D,IAAI,CAAC;MAE7B;MACA,IAAI,CAACY,KAAK,EAAE;QACVA,KAAK,GAAG,IAAI,CAACtE,MAAM,CAAC,GAAG0D,IAAI,YAAY,CAAC;;MAG1C,IAAI,CAACY,KAAK,EAAE;QACV3D,OAAO,CAACY,IAAI,CAAC,0BAA0BmC,IAAI,yBAAyB,CAAC;QACrE;;MAGF,IAAI,IAAI,CAACzD,SAAS,CAACyD,IAAI,CAAC,EAAE;QACxB/C,OAAO,CAACC,GAAG,CAAC,oCAAoC8C,IAAI,EAAE,CAAC;QAEvD;QACA,IAAKY,KAAa,CAACnB,aAAa,EAAE;UAC/BmB,KAAa,CAACnB,aAAa,EAAE;SAC/B,MAAM;UACLmB,KAAK,CAACM,KAAK,EAAE;UACbN,KAAK,CAACxB,WAAW,GAAG,CAAC;;QAGvB,IAAI,CAAC7C,SAAS,CAACyD,IAAI,CAAC,GAAG,KAAK;OAC7B,MAAM;QACL/C,OAAO,CAACC,GAAG,CAAC,0BAA0B8C,IAAI,kBAAkB,CAAC;;KAEhE,CAAC,OAAOpC,KAAK,EAAE;MACdX,OAAO,CAACW,KAAK,CAAC,wCAAwCoC,IAAI,GAAG,EAAEpC,KAAK,CAAC;;EAEzE;EAEA;;;EAGQuD,aAAaA,CAAA;IACnBlE,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;IACnD,IAAI,CAACsC,IAAI,CAAC,UAAU,CAAC;IACrB,IAAI,CAACA,IAAI,CAAC,gBAAgB,CAAC;IAC3B,IAAI,CAACA,IAAI,CAAC,UAAU,CAAC;EACvB;EAEA;;;EAGQ1C,wBAAwBA,CAAA;IAC9BG,OAAO,CAACC,GAAG,CAAC,2DAA2D,CAAC;IAExE,IAAI;MACF,IAAI,CAACnB,MAAM,CACRqF,SAAS,CAAiC;QACzCC,KAAK,EAAE1F,0BAA0B;QACjC2F,WAAW,EAAE,KAAK,CAAE;OACrB,CAAC,CACDF,SAAS,CAAC;QACTG,IAAI,EAAEA,CAAC;UAAEC,IAAI;UAAEC;QAAM,CAAE,KAAI;UACzB,IAAIA,MAAM,EAAE;YACVxE,OAAO,CAACY,IAAI,CACV,kDAAkD,EAClD4D,MAAM,CACP;;UAGH,IAAID,IAAI,EAAEtF,YAAY,EAAE;YACtBe,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAE;cACtDwE,MAAM,EAAEF,IAAI,CAACtF,YAAY,CAACyF,EAAE;cAC5BC,QAAQ,EAAEJ,IAAI,CAACtF,YAAY,CAAC+C,IAAI;cAChC4C,MAAM,EAAEL,IAAI,CAACtF,YAAY,CAAC2F,MAAM,EAAEC,QAAQ;cAC1CC,cAAc,EAAEP,IAAI,CAACtF,YAAY,CAAC6F;aACnC,CAAC;YACF,IAAI,CAACC,kBAAkB,CAACR,IAAI,CAACtF,YAAY,CAAC;;QAE9C,CAAC;QACD0B,KAAK,EAAGA,KAAK,IAAI;UACfX,OAAO,CAACW,KAAK,CACX,sDAAsD,EACtDA,KAAK,CACN;UAED;UACAf,UAAU,CAAC,MAAK;YACdI,OAAO,CAACC,GAAG,CACT,yDAAyD,CAC1D;YACD,IAAI,CAACJ,wBAAwB,EAAE;UACjC,CAAC,EAAE,IAAI,CAAC;QACV,CAAC;QACDmF,QAAQ,EAAEA,CAAA,KAAK;UACbhF,OAAO,CAACC,GAAG,CACT,uDAAuD,CACxD;UACD;UACAL,UAAU,CAAC,MAAK;YACdI,OAAO,CAACC,GAAG,CACT,8DAA8D,CAC/D;YACD,IAAI,CAACJ,wBAAwB,EAAE;UACjC,CAAC,EAAE,IAAI,CAAC;QACV;OACD,CAAC;KACL,CAAC,OAAOc,KAAK,EAAE;MACdX,OAAO,CAACW,KAAK,CAAC,gDAAgD,EAAEA,KAAK,CAAC;MACtE;MACAf,UAAU,CAAC,MAAK;QACd,IAAI,CAACC,wBAAwB,EAAE;MACjC,CAAC,EAAE,IAAI,CAAC;;EAEZ;EAEA;;;EAGOoF,wBAAwBA,CAAA;IAC7BjF,OAAO,CAACC,GAAG,CAAC,0DAA0D,CAAC;IACvE,IAAI,CAACJ,wBAAwB,EAAE;IAC/B,IAAI,CAACC,4BAA4B,EAAE;EACrC;EAEA;;;EAGOoF,UAAUA,CAAA;IACflF,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;IAEjD;IACAD,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;IACrC,IAAI,CAACwD,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC;IAE3B7D,UAAU,CAAC,MAAK;MACd,IAAI,CAAC2C,IAAI,CAAC,UAAU,CAAC;MACrBvC,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;MAC3C,IAAI,CAACwD,IAAI,CAAC,gBAAgB,CAAC;IAC7B,CAAC,EAAE,IAAI,CAAC;IAER7D,UAAU,CAAC,MAAK;MACdI,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;MACrC,IAAI,CAACwD,IAAI,CAAC,UAAU,CAAC;IACvB,CAAC,EAAE,IAAI,CAAC;EACV;EAEA;;;EAGO0B,YAAYA,CAAA;IACjBnF,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC;IAChED,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEmF,MAAM,CAACC,IAAI,CAAC,IAAI,CAAChG,MAAM,CAAC,CAAC;IAE3E;IACA,MAAMiG,QAAQ,GAAG,IAAI,CAACjG,MAAM,CAAC,UAAU,CAAC;IACxC,IAAIiG,QAAQ,EAAE;MACZtF,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE;QAC7CsD,GAAG,EAAE+B,QAAQ,CAAC/B,GAAG;QACjBgC,UAAU,EAAED,QAAQ,CAACC,UAAU;QAC/B5E,KAAK,EAAE2E,QAAQ,CAAC3E,KAAK;QACrB6E,QAAQ,EAAEF,QAAQ,CAACE;OACpB,CAAC;KACH,MAAM;MACLxF,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;;IAG7D;IACA,IAAI,CAACwD,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC;IAE3B;IACA7D,UAAU,CAAC,MAAK;MACd,IAAI,CAAC2C,IAAI,CAAC,UAAU,CAAC;MACrBvC,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;IACvD,CAAC,EAAE,IAAI,CAAC;EACV;EAEA;;;EAGQH,4BAA4BA,CAAA;IAClCE,OAAO,CAACC,GAAG,CACT,gEAAgE,CACjE;IAED,IAAI;MACF,IAAI,CAACnB,MAAM,CACRqF,SAAS,CAA8B;QACtCC,KAAK,EAAEzF,gCAAgC;QACvC0F,WAAW,EAAE;OACd,CAAC,CACDF,SAAS,CAAC;QACTG,IAAI,EAAEA,CAAC;UAAEC,IAAI;UAAEC;QAAM,CAAE,KAAI;UACzB,IAAIA,MAAM,EAAE;YACVxE,OAAO,CAACY,IAAI,CACV,8DAA8D,EAC9D4D,MAAM,CACP;;UAGH,IAAID,IAAI,EAAEkB,iBAAiB,EAAE;YAC3BzF,OAAO,CAACC,GAAG,CACT,uCAAuC,EACvCsE,IAAI,CAACkB,iBAAiB,CACvB;YACD,IAAI,CAACC,sBAAsB,CAACnB,IAAI,CAACkB,iBAAiB,CAAC;;QAEvD,CAAC;QACD9E,KAAK,EAAGA,KAAK,IAAI;UACfX,OAAO,CAACW,KAAK,CACX,oDAAoD,EACpDA,KAAK,CACN;UACD;UACAf,UAAU,CAAC,MAAK;YACd,IAAI,CAACE,4BAA4B,EAAE;UACrC,CAAC,EAAE,IAAI,CAAC;QACV,CAAC;QACDkF,QAAQ,EAAEA,CAAA,KAAK;UACbhF,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;UAClE;UACAL,UAAU,CAAC,MAAK;YACd,IAAI,CAACE,4BAA4B,EAAE;UACrC,CAAC,EAAE,IAAI,CAAC;QACV;OACD,CAAC;KACL,CAAC,OAAOa,KAAK,EAAE;MACdX,OAAO,CAACW,KAAK,CACX,4DAA4D,EAC5DA,KAAK,CACN;MACDf,UAAU,CAAC,MAAK;QACd,IAAI,CAACE,4BAA4B,EAAE;MACrC,CAAC,EAAE,IAAI,CAAC;;EAEZ;EAEA;;;EAGQiF,kBAAkBA,CAACY,IAAkB;IAC3C3F,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAE;MACtDwE,MAAM,EAAEkB,IAAI,CAACjB,EAAE;MACfC,QAAQ,EAAEgB,IAAI,CAAC3D,IAAI;MACnB4C,MAAM,EAAEe,IAAI,CAACf,MAAM,EAAEC,QAAQ;MAC7BC,cAAc,EAAEa,IAAI,CAACb;KACtB,CAAC;IAEF,IAAI,CAAC7F,YAAY,CAACqF,IAAI,CAACqB,IAAI,CAAC;IAC5B,IAAI,CAAClC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC;IAE3BzD,OAAO,CAACC,GAAG,CACT,iEAAiE,CAClE;EACH;EAEA;;;EAGQyF,sBAAsBA,CAACC,IAAU;IACvC3F,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE0F,IAAI,CAACC,MAAM,CAAC;IAEjE,QAAQD,IAAI,CAACC,MAAM;MACjB,KAAKxH,UAAU,CAACyH,QAAQ;QACtB7F,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;QAChD,IAAI,CAACsC,IAAI,CAAC,UAAU,CAAC;QACrB,IAAI,CAACkB,IAAI,CAAC,UAAU,CAAC;QACrB,IAAI,CAACzE,UAAU,CAACsF,IAAI,CAAC,IAAI,CAAC;QAC1B,IAAI,CAACrF,YAAY,CAACqF,IAAI,CAAC,IAAI,CAAC;QAC5B;MAEF,KAAKlG,UAAU,CAAC0H,KAAK;QACnB9F,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;QAC1C,IAAI,CAACiE,aAAa,EAAE;QACpB,IAAI,CAACT,IAAI,CAAC,UAAU,CAAC;QACrB,IAAI,CAACzE,UAAU,CAACsF,IAAI,CAAC,IAAI,CAAC;QAC1B,IAAI,CAACrF,YAAY,CAACqF,IAAI,CAAC,IAAI,CAAC;QAC5B;MAEF,KAAKlG,UAAU,CAAC2H,SAAS;QACvB/F,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;QAC7C,IAAI,CAACsC,IAAI,CAAC,UAAU,CAAC;QACrB,IAAI,CAACkB,IAAI,CAAC,gBAAgB,CAAC;QAC3B,IAAI,CAACzE,UAAU,CAACsF,IAAI,CAACqB,IAAI,CAAC;QAC1B,IAAI,CAAC1G,YAAY,CAACqF,IAAI,CAAC,IAAI,CAAC;QAC5B;MAEF,KAAKlG,UAAU,CAAC4H,OAAO;QACrBhG,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;QAC/C,IAAI,CAACwD,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC;QAC3B;MAEF;QACEzD,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE0F,IAAI,CAACC,MAAM,CAAC;QACjE;;EAEN;EAEA;;;EAGAK,YAAYA,CACVC,WAAmB,EACnBvB,QAAkB,EAClBG,cAAuB;IAEvB9E,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAE;MAC/CiG,WAAW;MACXvB,QAAQ;MACRG;KACD,CAAC;IAEF,IAAI,CAACoB,WAAW,EAAE;MAChB,MAAMvF,KAAK,GAAG,IAAIwF,KAAK,CAAC,0BAA0B,CAAC;MACnDnG,OAAO,CAACW,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3D,OAAO5C,UAAU,CAAC,MAAM4C,KAAK,CAAC;;IAGhC;IACA,MAAM8D,MAAM,GAAG,QAAQ2B,IAAI,CAACC,GAAG,EAAE,IAAIC,IAAI,CAACC,MAAM,EAAE,CAC/CC,QAAQ,CAAC,EAAE,CAAC,CACZC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;IAEjB;IACA,MAAMC,KAAK,GAAGC,IAAI,CAACC,SAAS,CAAC;MAC3B5E,IAAI,EAAE,OAAO;MACb6E,GAAG,EAAE;KACN,CAAC;IAEF,MAAMC,SAAS,GAAG;MAChBZ,WAAW;MACXvB,QAAQ,EAAEA,QAAQ;MAClBF,MAAM;MACNiC,KAAK;MACL5B;KACD;IAED9E,OAAO,CAACC,GAAG,CAAC,kDAAkD,EAAE6G,SAAS,CAAC;IAE1E,OAAO,IAAI,CAAChI,MAAM,CACfiI,MAAM,CAAyB;MAC9BC,QAAQ,EAAE3I,sBAAsB;MAChCyI;KACD,CAAC,CACDG,IAAI,CACHhJ,GAAG,CAAEiJ,MAAM,IAAI;MACblH,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAEiH,MAAM,CAAC;MAEnE,IAAI,CAACA,MAAM,CAAC3C,IAAI,EAAE0B,YAAY,EAAE;QAC9B,MAAM,IAAIE,KAAK,CAAC,mCAAmC,CAAC;;MAGtD,MAAMR,IAAI,GAAGuB,MAAM,CAAC3C,IAAI,CAAC0B,YAAY;MACrCjG,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE;QAC5CyE,EAAE,EAAEiB,IAAI,CAACjB,EAAE;QACX1C,IAAI,EAAE2D,IAAI,CAAC3D,IAAI;QACf4D,MAAM,EAAED,IAAI,CAACC,MAAM;QACnBhB,MAAM,EAAEe,IAAI,CAACf,MAAM,EAAEC,QAAQ;QAC7BsC,SAAS,EAAExB,IAAI,CAACwB,SAAS,EAAEtC;OAC5B,CAAC;MAEF;MACA,IAAI,CAAC7F,UAAU,CAACsF,IAAI,CAACqB,IAAI,CAAC;MAE1B,OAAOA,IAAI;IACb,CAAC,CAAC,EACFzH,UAAU,CAAEyC,KAAK,IAAI;MACnBX,OAAO,CAACW,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3D,IAAI,CAAC5B,MAAM,CAAC4B,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAElD,IAAIyG,YAAY,GAAG,wCAAwC;MAC3D,IAAIzG,KAAK,CAAC0G,YAAY,EAAE;QACtBD,YAAY,GAAG,4BAA4B;OAC5C,MAAM,IAAIzG,KAAK,CAAC2G,aAAa,EAAE9F,MAAM,GAAG,CAAC,EAAE;QAC1C4F,YAAY,GAAGzG,KAAK,CAAC2G,aAAa,CAAC,CAAC,CAAC,CAACC,OAAO,IAAIH,YAAY;;MAG/D,OAAOrJ,UAAU,CAAC,MAAM,IAAIoI,KAAK,CAACiB,YAAY,CAAC,CAAC;IAClD,CAAC,CAAC,CACH;EACL;EAEA;;;EAGAI,UAAUA,CAACvI,YAA0B;IACnCe,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEhB,YAAY,CAACyF,EAAE,CAAC;IAEhE;IACA,MAAM+C,MAAM,GAAGd,IAAI,CAACC,SAAS,CAAC;MAC5B5E,IAAI,EAAE,QAAQ;MACd6E,GAAG,EAAE;KACN,CAAC;IAEF,OAAO,IAAI,CAAC/H,MAAM,CACfiI,MAAM,CAAuB;MAC5BC,QAAQ,EAAE1I,oBAAoB;MAC9BwI,SAAS,EAAE;QACTrC,MAAM,EAAExF,YAAY,CAACyF,EAAE;QACvB+C;;KAEH,CAAC,CACDR,IAAI,CACH9I,SAAS,CAAE+I,MAAM,IAAI;MACnBlH,OAAO,CAACC,GAAG,CAAC,6CAA6C,EAAEiH,MAAM,CAAC;MAElE,IAAI,CAACA,MAAM,CAAC3C,IAAI,EAAEiD,UAAU,EAAE;QAC5B,MAAM,IAAIrB,KAAK,CAAC,mCAAmC,CAAC;;MAGtD,MAAMR,IAAI,GAAGuB,MAAM,CAAC3C,IAAI,CAACiD,UAAU;MAEnC;MACA,IAAI,CAACjF,IAAI,CAAC,UAAU,CAAC;MACrB,IAAI,CAACkB,IAAI,CAAC,gBAAgB,CAAC;MAE3B;MACA,OAAOzF,IAAI,CAAC,IAAI,CAAC0J,iBAAiB,CAACzI,YAAY,EAAE0G,IAAI,CAAC,CAAC;IACzD,CAAC,CAAC,EACFzH,UAAU,CAAEyC,KAAK,IAAI;MACnBX,OAAO,CAACW,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzD,IAAI,CAAC5B,MAAM,CAAC4B,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MACjD,OAAO5C,UAAU,CACf,MAAM,IAAIoI,KAAK,CAAC,yCAAyC,CAAC,CAC3D;IACH,CAAC,CAAC,CACH;EACL;EAEA;;;EAGAwB,UAAUA,CAAClD,MAAc,EAAEmD,MAAe;IACxC5H,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEwE,MAAM,EAAEmD,MAAM,CAAC;IAE/D,OAAO,IAAI,CAAC9I,MAAM,CACfiI,MAAM,CAA8B;MACnCC,QAAQ,EAAEzI,oBAAoB;MAC9BuI,SAAS,EAAE;QACTrC,MAAM;QACNmD,MAAM,EAAEA,MAAM,IAAI;;KAErB,CAAC,CACDX,IAAI,CACHhJ,GAAG,CAAEiJ,MAAM,IAAI;MACblH,OAAO,CAACC,GAAG,CAAC,6CAA6C,EAAEiH,MAAM,CAAC;MAElE,IAAI,CAACA,MAAM,CAAC3C,IAAI,EAAEoD,UAAU,EAAE;QAC5B,MAAM,IAAIxB,KAAK,CAAC,kCAAkC,CAAC;;MAGrD;MACA,IAAI,CAAClH,YAAY,CAACqF,IAAI,CAAC,IAAI,CAAC;MAC5B,IAAI,CAACtF,UAAU,CAACsF,IAAI,CAAC,IAAI,CAAC;MAE1B;MACA,IAAI,CAAC/B,IAAI,CAAC,UAAU,CAAC;MAErB,OAAO2E,MAAM,CAAC3C,IAAI,CAACoD,UAAU;IAC/B,CAAC,CAAC,EACFzJ,UAAU,CAAEyC,KAAK,IAAI;MACnBX,OAAO,CAACW,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzD,IAAI,CAAC5B,MAAM,CAAC4B,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MACjD,OAAO5C,UAAU,CAAC,MAAM,IAAIoI,KAAK,CAAC,iCAAiC,CAAC,CAAC;IACvE,CAAC,CAAC,CACH;EACL;EAEA;;;EAGA0B,OAAOA,CAACpD,MAAc;IACpBzE,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEwE,MAAM,CAAC;IAEpD,OAAO,IAAI,CAAC3F,MAAM,CACfiI,MAAM,CAA2B;MAChCC,QAAQ,EAAExI,iBAAiB;MAC3BsI,SAAS,EAAE;QACTrC,MAAM;QACNqD,QAAQ,EAAE,IAAI,CAAE;;KAEnB,CAAC,CACDb,IAAI,CACHhJ,GAAG,CAAEiJ,MAAM,IAAI;MACblH,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAEiH,MAAM,CAAC;MAE/D,IAAI,CAACA,MAAM,CAAC3C,IAAI,EAAEsD,OAAO,EAAE;QACzB,MAAM,IAAI1B,KAAK,CAAC,kCAAkC,CAAC;;MAGrD;MACA,IAAI,CAACnH,UAAU,CAACsF,IAAI,CAAC,IAAI,CAAC;MAC1B,IAAI,CAACrF,YAAY,CAACqF,IAAI,CAAC,IAAI,CAAC;MAE5B;MACA,IAAI,CAACJ,aAAa,EAAE;MACpB,IAAI,CAACT,IAAI,CAAC,UAAU,CAAC;MAErB,OAAOyD,MAAM,CAAC3C,IAAI,CAACsD,OAAO;IAC5B,CAAC,CAAC,EACF3J,UAAU,CAAEyC,KAAK,IAAI;MACnBX,OAAO,CAACW,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD,IAAI,CAAC5B,MAAM,CAAC4B,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC9C,OAAO5C,UAAU,CACf,MAAM,IAAIoI,KAAK,CAAC,kCAAkC,CAAC,CACpD;IACH,CAAC,CAAC,CACH;EACL;EAEA;;;EAGA4B,WAAWA,CACTtD,MAAc,EACduD,WAAqB,EACrBC,WAAqB;IAErBjI,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE;MAC9CwE,MAAM;MACNuD,WAAW;MACXC;KACD,CAAC;IAEF,OAAO,IAAI,CAACnJ,MAAM,CACfiI,MAAM,CAAmC;MACxCC,QAAQ,EAAEvI,0BAA0B;MACpCqI,SAAS,EAAE;QACTrC,MAAM;QACNyD,KAAK,EAAEF,WAAW;QAClBnH,KAAK,EAAEoH;;KAEV,CAAC,CACDhB,IAAI,CACHhJ,GAAG,CAAEiJ,MAAM,IAAI;MACblH,OAAO,CAACC,GAAG,CAAC,6CAA6C,EAAEiH,MAAM,CAAC;MAElE,IAAI,CAACA,MAAM,CAAC3C,IAAI,EAAE4D,eAAe,EAAE;QACjC,MAAM,IAAIhC,KAAK,CAAC,kCAAkC,CAAC;;MAGrD,OAAOe,MAAM,CAAC3C,IAAI,CAAC4D,eAAe;IACpC,CAAC,CAAC,EACFjK,UAAU,CAAEyC,KAAK,IAAI;MACnBX,OAAO,CAACW,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;MAC1D,IAAI,CAAC5B,MAAM,CAAC4B,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MACjD,OAAO5C,UAAU,CACf,MAAM,IAAIoI,KAAK,CAAC,sCAAsC,CAAC,CACxD;IACH,CAAC,CAAC,CACH;EACL;EAEA;;;EAGcuB,iBAAiBA,CAC7BzI,YAA0B,EAC1B0G,IAAU;IAAA,IAAAyC,KAAA;IAAA,OAAAC,iBAAA;MAEVrI,OAAO,CAACC,GAAG,CAAC,4DAA4D,CAAC;MAEzE;MACAmI,KAAI,CAAC3E,IAAI,CAAC,gBAAgB,CAAC;MAE3B;MACA2E,KAAI,CAACpJ,UAAU,CAACsF,IAAI,CAACqB,IAAI,CAAC;MAC1ByC,KAAI,CAACnJ,YAAY,CAACqF,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;MAE9B,OAAOqB,IAAI;IAAC;EACd;EAEA;;;EAGA2C,WAAWA,CAAA;IACT,IAAI,CAAC9I,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;IAC1CQ,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE,IAAI,CAACT,cAAc,CAAC;IACnE,OAAO,IAAI,CAACA,cAAc;EAC5B;EAEA;;;EAGA+I,WAAWA,CAAA;IACT,IAAI,CAAC9I,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;IAC1CO,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE,IAAI,CAACR,cAAc,CAAC;IACnE,OAAO,IAAI,CAACA,cAAc;EAC5B;EAEA;;;EAGA+I,eAAeA,CAAA;IACb,OAAO,IAAI,CAAChJ,cAAc;EAC5B;EAEA;;;EAGAiJ,eAAeA,CAAA;IACb,OAAO,IAAI,CAAChJ,cAAc;EAC5B;EAEA;;;EAGQyE,aAAaA,CAAA;IACnBlE,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;IACnDmF,MAAM,CAACC,IAAI,CAAC,IAAI,CAAChG,MAAM,CAAC,CAACsD,OAAO,CAAEI,IAAI,IAAI;MACxC,IAAI,CAACR,IAAI,CAACQ,IAAI,CAAC;IACjB,CAAC,CAAC;EACJ;EAEA;;;EAGO2F,YAAYA,CAAA;IACjB1I,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC;IACtEmF,MAAM,CAACuD,MAAM,CAAC,IAAI,CAACtJ,MAAM,CAAC,CAACsD,OAAO,CAAEgB,KAAK,IAAI;MAC3C,IAAIA,KAAK,EAAE;QACTA,KAAK,CAACpE,KAAK,GAAG,KAAK;QACnBoE,KAAK,CAAC5C,MAAM,GAAG,GAAG;QAClB;QACA4C,KAAK,CACFF,IAAI,EAAE,CACNK,IAAI,CAAC,MAAK;UACTH,KAAK,CAACM,KAAK,EAAE;UACbN,KAAK,CAACxB,WAAW,GAAG,CAAC;QACvB,CAAC,CAAC,CACD4B,KAAK,CAAC,MAAK;UACV;QAAA,CACD,CAAC;;IAER,CAAC,CAAC;EACJ;EAEA6E,WAAWA,CAAA;IACT,IAAI,CAAC1E,aAAa,EAAE;EACtB;;;uBA1jCWtF,WAAW,EAAAiK,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,aAAA;IAAA;EAAA;;;aAAXtK,WAAW;MAAAuK,OAAA,EAAXvK,WAAW,CAAAwK,IAAA;MAAAC,UAAA,EAFV;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}